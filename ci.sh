          ls -la
          export SDK_APP_NAME=$(grep '"name":' package.json | sed 's/^.*\"name\": \"\([a-zA-Z0-9_\.\-]*\)\".*$/\1/')
          export SDK_APP_VERSION=$(grep '"version":' package.json | sed 's/^.*\"version\": \"\([a-zA-Z0-9_\.\-]*\)\".*$/\1/')
          export ENVIRONMENT=$ENVIRONMENT
          nohup node dist/main.js >> $APP_NAME.log 2>&1 &
          sleep 1 && cat $APP_NAME.log
          echo && echo "********************"
          echo "Waiting for service to be ready..."
          RETRIES=10
          until curl -s "http://127.0.0.1:3000/health" | jq -e '.status' >/dev/null; do
            echo "Service not ready yet, waiting 10s... RETRIES $RETRIES"
            sleep 1
            ((RETRIES--))
            if [ "$RETRIES" -le 0 ]; then
              echo "Service did not start in time, continuing without api-docs..."
              cat $APP_NAME.log
              break
            fi
          done
          curl -sS "http://127.0.0.1:3000/health" | jq '.status'
          echo "********************" && echo
          curl -sS "http://127.0.0.1:3000/docs-yaml" > $SDK_APP_NAME.yaml

          if [ ! -e openapitools.json ]; then
              echo "{\"\$schema\": \"./node_modules/@openapitools/openapi-generator-cli/config.schema.json\", \"spaces\": 2, \"generator-cli\": {\"version\": \"${OPENAPI_GENERATOR_VERSION}\"}}" > openapitools.json
          fi

          if [[ "$AWS_CLI_IAM_ROLE_ARN" != "" && "$AWS_S3_BUCKET" != "" ]]; then
              export AWS_ACCOUNT_ID=$(echo $AWS_CLI_IAM_ROLE_ARN | cut -d":" -f5)
              export AWS_PROFILE="$AWS_ACCOUNT_ID"

              if [[ "$CI_COMMIT_BRANCH" =~ ^release-[[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                  AWS_S3_BUCKET_PATH="release"
              else
                  AWS_S3_BUCKET_PATH="snapshot"
              fi
              echo "Uploading api-docs to AWS S3 Bucket..."
              aws s3 cp "${SDK_APP_NAME}.yaml" "s3://${AWS_S3_BUCKET}/${AWS_S3_BUCKET_PATH}/" || exit $?
          fi

          openapi-generator-cli generate -i $SDK_APP_NAME.yaml \
                                          -g typescript-nestjs \
                                          -o $OAS_OUT_DIR \
                                          -c $SDK_TEMPLATE_REPO_NAME/config/config.yaml \
                                          -t $SDK_TEMPLATE_REPO_NAME/src \
                                          --additional-properties=npmName=@vidmob/$SDK_APP_NAME-sdk \
                                          --additional-properties=npmVersion=$SDK_APP_VERSION \
                                          --additional-properties=nestVersion=$NESTJS_VERSION \
                                          --additional-properties=basePath=http://${BUILD_PACKAGE_NAME}.${AWS_ECS_CLUSTER_NAMESPACE}:${AWS_ECS_SERVICE_PORT} \
                                          --skip-validate-spec
          cd $OAS_OUT_DIR && ls -la
          cp ../.npmrc .
          npm i || exit $?
          npm run build || exit $?
          npm publish || exit $?
          echo "OAS SDK successfully published"
