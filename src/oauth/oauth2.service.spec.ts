import { Test, TestingModule } from '@nestjs/testing';
import { OAuth2Service } from './oauth2.service';
import { OAuth2Service as AuthorizationServiceSdk } from '@vidmob/vidmob-authorization-service-sdk';

describe('OAuth2Service', () => {
  let service: OAuth2Service;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OAuth2Service,
        {
          provide: AuthorizationServiceSdk,
          useValue: {
            createAccessTokenAsPromise: jest.fn().mockResolvedValue({
              tokenType: 'Bearer',
              accessToken: 'access token',
              refreshToken: 'refresh token',
              expiresIn: 3000,
            }),
            revokeTokensAsPromise: jest
              .fn()
              .mockResolvedValue('Successfully revoked token.'),
            defaultHeaders: {},
          },
        },
      ],
    }).compile();

    service = module.get<OAuth2Service>(OAuth2Service);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAccessToken', () => {
    it('should generate a new access token', async () => {
      await expect(
        service.createAccessToken({
          clientId: 'client id',
          clientSecret: 'client secret',
          grantType: 'refresh_token',
          code: 'refresh token',
        }),
      ).resolves.toStrictEqual({
        tokenType: 'Bearer',
        accessToken: 'access token',
        refreshToken: 'refresh token',
        expiresIn: 3000,
      });
    });
  });

  describe('revokeToken', () => {
    it('should revoke token', async () => {
      await expect(
        service.revokeToken('authorization', {
          clientId: 'client id',
          clientSecret: 'client secret',
          token: 'refresh token',
        }),
      ).resolves.toBe('Successfully revoked token.');
    });
  });
});
