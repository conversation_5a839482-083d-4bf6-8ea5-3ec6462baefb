import { Test, TestingModule } from '@nestjs/testing';
import { OAuth2Controller } from './oauth2.controller';
import { OAuth2Service } from './oauth2.service';

describe('OauthController', () => {
  let controller: OAuth2Controller;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OAuth2Controller],
      providers: [
        {
          provide: OAuth2Service,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<OAuth2Controller>(OAuth2Controller);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
