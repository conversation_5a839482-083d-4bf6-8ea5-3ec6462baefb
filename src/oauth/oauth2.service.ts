import { Injectable } from '@nestjs/common';
import {
  AccessTokenRequestDto,
  OAuth2Service as AuthorizationServiceSdk,
  RevokeTokenRequestDto,
} from '@vidmob/vidmob-authorization-service-sdk';

@Injectable()
export class OAuth2Service {
  constructor(
    private readonly authorizationServiceSdk: AuthorizationServiceSdk,
  ) {}

  async createAccessToken(accessTokenRequestDto: AccessTokenRequestDto) {
    return await this.authorizationServiceSdk.createAccessTokenAsPromise(
      accessTokenRequestDto,
    );
  }

  async revokeToken(
    authorization: string,
    revokeTokenRequestDto: RevokeTokenRequestDto,
  ) {
    this.authorizationServiceSdk.defaultHeaders.authorization = authorization;

    return await this.authorizationServiceSdk.revokeTokensAsPromise(
      revokeTokenRequestDto,
    );
  }
}
