import { Controller, Get, Param, Query, ValidationPipe } from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { RealEyesService } from './real-eyes.service';
import { FilterReactionsDto } from './dto/filter-reactions.dto';
import { readWorkspaceDetails } from 'src/account-management/account-management.permissions';
import { Permissions } from 'src/auth/decorators/permission.decorator';

@ApiTags('real-eyes')
@ApiSecurity('Bearer Token')
@Controller()
export class RealEyesController {
  constructor(private readonly realEyesService: RealEyesService) {}

  @ApiParam({
    name: 'workspaceId',
    type: 'number',
  })
  @Permissions(readWorkspaceDetails)
  @Get('workspace/:workspaceId/real-eyes')
  async workspaceHasRealEyesAccess(@Param('workspaceId') workspaceId: number) {
    return this.realEyesService.validateWorkspaceAccess(workspaceId);
  }

  @ApiParam({
    name: 'workspaceId',
    type: 'number',
  })
  @ApiParam({
    name: 'mediaId',
    type: 'number',
    description: 'Target analytics media to fetch the real eyes scores',
  })
  @Get('workspace/:workspaceId/real-eyes/:mediaId')
  @Permissions(readWorkspaceDetails)
  async getRealEyesScores(
    @Param('workspaceId') workspaceId: number,
    @Param('mediaId') mediaId: number,
    @Query(new ValidationPipe({ transform: true }))
    filterDto: FilterReactionsDto,
  ) {
    return await this.realEyesService.getRealEyesScores(
      workspaceId,
      mediaId,
      filterDto,
    );
  }
}
