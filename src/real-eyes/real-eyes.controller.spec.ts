import { Test, TestingModule } from '@nestjs/testing';
import { RealEyesController } from './real-eyes.controller';
import { RealEyesService } from './real-eyes.service';

describe('RealEyesController', () => {
  let controller: RealEyesController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RealEyesController],
      providers: [
        {
          provide: RealEyesService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<RealEyesController>(RealEyesController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
