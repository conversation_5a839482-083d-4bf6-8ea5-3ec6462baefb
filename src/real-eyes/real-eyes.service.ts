import { Injectable } from '@nestjs/common';
import { RealEyesService as RealEyesServiceSdk } from '@vidmob/vidmob-soa-media-annotation-service-sdk';
import { FilterReactionsDto } from './dto/filter-reactions.dto';

@Injectable()
export class RealEyesService {
  constructor(private readonly realEyesServiceSdk: RealEyesServiceSdk) {}

  async validateWorkspaceAccess(workspaceId: number) {
    return this.realEyesServiceSdk.workspaceHasRealEyesAccessAsPromise(
      workspaceId,
    );
  }

  async getRealEyesScores(
    workspaceId: number,
    mediaId: number,
    filterDto: FilterReactionsDto,
  ) {
    return this.realEyesServiceSdk.getRealEyesScoresAsPromise(
      workspaceId,
      mediaId,
      filterDto.attention,
      filterDto.confusion,
      filterDto.contempt,
      filterDto.disgust,
      filterDto.distraction,
      filterDto.happiness,
      filterDto.surprise,
    );
  }
}
