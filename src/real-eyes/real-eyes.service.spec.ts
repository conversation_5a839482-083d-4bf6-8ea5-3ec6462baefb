import { Test, TestingModule } from '@nestjs/testing';
import { RealEyesService } from './real-eyes.service';
import { RealEyesService as RealEyesServiceSdk } from '@vidmob/vidmob-soa-media-annotation-service-sdk';

describe('RealEyesService', () => {
  let service: RealEyesService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RealEyesService,
        {
          provide: RealEyesServiceSdk,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<RealEyesService>(RealEyesService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
