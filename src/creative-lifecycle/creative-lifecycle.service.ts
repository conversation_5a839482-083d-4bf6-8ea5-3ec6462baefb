import { Injectable } from '@nestjs/common';
import {
  GetCreativeLifecycleDto,
  KPIMetric,
} from './dto/get-creative-lifecycle.dto';
import { CreativeLifecycleService as CreativeLifecycleServiceSDK } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/api/creativeLifecycle.service';
import {
  BRAND_MOCK_REPORT_COKE_DATA,
  BRAND_MOCK_REPORT_DATA,
} from './mock-data/brand.mock';
import { MARKET_MOCK_REPORT_DATA } from './mock-data/market.mock';
import {
  BRAND_AND_MARKET_MOCK_REPORT_COKE_DATA,
  BRAND_AND_MARKET_MOCK_REPORT_DATA,
} from './mock-data/brand-and-market.mock';

const MOCK_KPIS_DATA: KPIMetric[] = [
  {
    title: 'Total Master Assets',
    value: 623,
    type: 'NUMBER',
  },
  {
    title: 'Total Adaptations',
    value: 1879240,
    type: 'NUMBER',
  },
  {
    title: 'Activation Rate',
    value: 92.0,
    type: 'PERCENTAGE',
  },
  {
    title: 'Adaptation Multiplier',
    value: 3016,
    type: 'MULTIPLIER',
  },
  {
    title: 'Reuse Rate',
    value: 93.0,
    type: 'PERCENTAGE',
  },
  {
    title: 'Overall Adherence',
    value: 82.0,
    type: 'PERCENTAGE',
  },
  {
    title: 'Media Performance',
    subtitle: 'NTB Sales',
    value: 1032383,
    type: 'NUMBER',
  },
  {
    title: 'Media Spend',
    value: 8027015,
    type: 'CURRENCY',
  },
];

const MOCK_REPORT_DATA: Record<string, any> = {
  brand: BRAND_MOCK_REPORT_DATA,
  market: MARKET_MOCK_REPORT_DATA,
  brandAndMarket: BRAND_AND_MARKET_MOCK_REPORT_DATA,
};

const MOCK_REPORT_COKE_DATA: Record<string, any> = {
  brand: BRAND_MOCK_REPORT_COKE_DATA,
  market: MARKET_MOCK_REPORT_DATA,
  brandAndMarket: BRAND_AND_MARKET_MOCK_REPORT_COKE_DATA,
};

@Injectable()
export class CreativeLifecycleService {
  constructor(
    private readonly creativeLifecycleServiceSdk: CreativeLifecycleServiceSDK,
  ) {}

  getReportData(
    getCreativeLifecycleDto: GetCreativeLifecycleDto,
    useDB: boolean,
    isCoke: boolean,
  ) {
    if (!useDB) {
      if (isCoke) {
        return MOCK_REPORT_COKE_DATA[getCreativeLifecycleDto.groupBy];
      }
      return MOCK_REPORT_DATA[getCreativeLifecycleDto.groupBy];
    }
    const payload = {
      organizationId: getCreativeLifecycleDto.organizationId,
      startDate: getCreativeLifecycleDto.startDate,
      endDate: getCreativeLifecycleDto.endDate,
      breakdown: getCreativeLifecycleDto.groupBy,
    };
    return this.creativeLifecycleServiceSdk.getReportDataAsPromise(payload);
  }

  getKpiData(getCreativeLifecycleDto: GetCreativeLifecycleDto, useDB: boolean) {
    if (!useDB) {
      return MOCK_KPIS_DATA;
    }
    const payload = {
      organizationId: getCreativeLifecycleDto.organizationId,
      startDate: getCreativeLifecycleDto.startDate,
      endDate: getCreativeLifecycleDto.endDate,
      breakdown: getCreativeLifecycleDto.groupBy,
    };
    return this.creativeLifecycleServiceSdk.getKpiDataAsPromise(payload);
  }

  // findAll() {
  //   return `This action returns all creativeLifecycle`;
  // }

  // findOne(id: number) {
  //   return `This action returns a #${id} creativeLifecycle`;
  // }

  // remove(id: number) {
  //   return `This action removes a #${id} creativeLifecycle`;
  // }
}
