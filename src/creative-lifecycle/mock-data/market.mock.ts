export const MARKET_MOCK_REPORT_DATA = {
  columns: [
    {
      id: 'column-1',
      displayName: 'Market',
    },
    {
      id: 'column-2',
      displayName: 'Total Master Assets',
    },
    {
      id: 'column-3',
      displayName: 'Total Adaptations',
    },
    {
      id: 'column-4',
      displayName: 'Activation Rate',
    },
    {
      id: 'column-5',
      displayName: 'Adaptation Multiplier',
    },
    {
      id: 'column-6',
      displayName: 'Reuse Rate',
    },
    {
      id: 'column-7',
      displayName: 'Overall Adherence',
    },
    {
      id: 'column-8',
      displayName: 'NTB Sales',
    },
    {
      id: 'column-9',
      displayName: 'Media Spend',
    },
  ],
  items: [
    {
      id: 'row-1',
      displayName: 'Overall', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Overall',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 623,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_879_240,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 92.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 3016,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 82.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 1_032_383,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 8_027_015,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-2',
      displayName: 'France', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 175,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 434_104,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 61.0,
              type: 'PERCENTAGE',
            },
            {
              value: -31.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 1093,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 91.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -7.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 238_608,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_854_241,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-3',
      displayName: 'Spain', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 125,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 310_074,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 67.0,
              type: 'PERCENTAGE',
            },
            {
              value: -25.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 926,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 71.0,
              type: 'PERCENTAGE',
            },
            {
              value: -22.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 70.0,
              type: 'PERCENTAGE',
            },
            {
              value: -12.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 170_434,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_324_458,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-4',
      displayName: 'UK', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 200,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 496_119,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 94.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 450,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 80.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 272_695,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 2_119_132,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-5',
      displayName: 'US', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 123,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 638_942,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 1423,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 98.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 85.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 350_646,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 2_729_185,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
  ],
};
