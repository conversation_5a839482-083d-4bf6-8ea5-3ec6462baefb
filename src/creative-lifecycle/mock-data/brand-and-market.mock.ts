export const BRAND_AND_MARKET_MOCK_REPORT_DATA = {
  columns: [
    {
      id: 'column-1',
      displayName: 'Brand and Market',
    },
    {
      id: 'column-2',
      displayName: 'Total Master Assets',
    },
    {
      id: 'column-3',
      displayName: 'Total Adaptations',
    },
    {
      id: 'column-4',
      displayName: 'Activation Rate',
    },
    {
      id: 'column-5',
      displayName: 'Adaptation Multiplier',
    },
    {
      id: 'column-6',
      displayName: 'Reuse Rate',
    },
    {
      id: 'column-7',
      displayName: 'Overall Adherence',
    },
    {
      id: 'column-8',
      displayName: 'NTB Sales',
    },
    {
      id: 'column-9',
      displayName: 'Media Spend',
    },
  ],
  items: [
    {
      id: 'row-1',
      displayName: 'Overall', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Overall',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 623,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_879_240,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 92.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 3016,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 82.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 1_032_383,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 8_027_015,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-2',
      displayName: 'Armani', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Armani',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 362,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_109_494,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -17.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 1350,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 417_801,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_248_499,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-3',
      parentId: 'row-2',
      displayName: 'France', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 102,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 262_565,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 60.0,
              type: 'PERCENTAGE',
            },
            {
              value: -32.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 379,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 90.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 80.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 107_360,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 882_500,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-4',
      displayName: 'Spain', // maybe don't need
      parentId: 'row-2',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 73,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 182_611,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 62.0,
              type: 'PERCENTAGE',
            },
            {
              value: -30.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 271,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 81.0,
              type: 'PERCENTAGE',
            },
            {
              value: -12.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 83.0,
              type: 'PERCENTAGE',
            },
            {
              value: 1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 79_828,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 651_785,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-5',
      displayName: 'UK', // maybe don't need
      parentId: 'row-2',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 116,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 332_178,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
            {
              value: 1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 167,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 79.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 124_126,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_042_857,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-6',
      displayName: 'US', // maybe don't need
      parentId: 'row-2',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 71,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 332_140,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 97.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 667,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 98.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 74.0,
              type: 'PERCENTAGE',
            },
            {
              value: -8.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 106_487,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 671_357,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-7',
      displayName: 'Viktor&Rolf', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Viktor&Rolf',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 217,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 452_678,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 89.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 981,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 84.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 449_993,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_498_799,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-8',
      displayName: 'France', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 61,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 103_157,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 63.0,
              type: 'PERCENTAGE',
            },
            {
              value: -29.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 276,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 85.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 106_403,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 952_809,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-9',
      displayName: 'Spain', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 44,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 84_826,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 69.0,
              type: 'PERCENTAGE',
            },
            {
              value: -23.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 197,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 73.0,
              type: 'PERCENTAGE',
            },
            {
              value: -20.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 81.0,
              type: 'PERCENTAGE',
            },
            {
              value: -1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 70_288,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 702_006,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-10',
      displayName: 'UK', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 70,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 127_322,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 98.0,
              type: 'PERCENTAGE',
            },
            {
              value: 6.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 254,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 91.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 79.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 104_460,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_123_210,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-11',
      displayName: 'US', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 42,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 137_373,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 94.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 193,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 86.0,
              type: 'PERCENTAGE',
            },
            {
              value: 4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 168_842,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 690_774,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-12',
      displayName: 'Maybelline', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Maybelline',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 44,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 317_068,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 90.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 298,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 51.0,
              type: 'PERCENTAGE',
            },
            {
              value: -42.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 73.0,
              type: 'PERCENTAGE',
            },
            {
              value: -9.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 164_589,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_279_717,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-13',
      displayName: 'France', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 12,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 68_382,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 60.0,
              type: 'PERCENTAGE',
            },
            {
              value: -32.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 84,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 68.0,
              type: 'PERCENTAGE',
            },
            {
              value: -25.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 60.0,
              type: 'PERCENTAGE',
            },
            {
              value: -22.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 24_845,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 193_176,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-14',
      displayName: 'Spain', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 8,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 42_637,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 70.0,
              type: 'PERCENTAGE',
            },
            {
              value: -22.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 60,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 50.0,
              type: 'PERCENTAGE',
            },
            {
              value: -43.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 59.0,
              type: 'PERCENTAGE',
            },
            {
              value: -23.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 20_318,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 157_977,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-15',
      displayName: 'UK', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 14,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 36_619,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 91.0,
              type: 'PERCENTAGE',
            },
            {
              value: -1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 96,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -25.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -7.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 44_109,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 342_958,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-16',
      displayName: 'US', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 10,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 169_429,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 97.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 59,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 80.0,
              type: 'PERCENTAGE',
            },
            {
              value: -13.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 75_317,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 585_607,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
  ],
};

export const BRAND_AND_MARKET_MOCK_REPORT_COKE_DATA = {
  columns: [
    {
      id: 'column-1',
      displayName: 'Brand and Market',
    },
    {
      id: 'column-2',
      displayName: 'Total Master Assets',
    },
    {
      id: 'column-3',
      displayName: 'Total Adaptations',
    },
    {
      id: 'column-4',
      displayName: 'Activation Rate',
    },
    {
      id: 'column-5',
      displayName: 'Adaptation Multiplier',
    },
    {
      id: 'column-6',
      displayName: 'Reuse Rate',
    },
    {
      id: 'column-7',
      displayName: 'Overall Adherence',
    },
    {
      id: 'column-8',
      displayName: 'NTB Sales',
    },
    {
      id: 'column-9',
      displayName: 'Media Spend',
    },
  ],
  items: [
    {
      id: 'row-1',
      displayName: 'Overall', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Overall',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 623,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_879_240,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 92.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 3016,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 82.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 1_032_383,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 8_027_015,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-2',
      displayName: 'Coca-Cola', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Coca-Cola',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 362,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_109_494,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -17.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 1350,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 417_801,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_248_499,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-3',
      parentId: 'row-2',
      displayName: 'France', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 102,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 262_565,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 60.0,
              type: 'PERCENTAGE',
            },
            {
              value: -32.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 379,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 90.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 80.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 107_360,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 882_500,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-4',
      displayName: 'Spain', // maybe don't need
      parentId: 'row-2',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 73,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 182_611,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 62.0,
              type: 'PERCENTAGE',
            },
            {
              value: -30.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 271,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 81.0,
              type: 'PERCENTAGE',
            },
            {
              value: -12.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 83.0,
              type: 'PERCENTAGE',
            },
            {
              value: 1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 79_828,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 651_785,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-5',
      displayName: 'UK', // maybe don't need
      parentId: 'row-2',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 116,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 332_178,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
            {
              value: 1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 167,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 79.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 124_126,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_042_857,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-6',
      displayName: 'US', // maybe don't need
      parentId: 'row-2',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 71,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 332_140,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 97.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 667,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 98.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 74.0,
              type: 'PERCENTAGE',
            },
            {
              value: -8.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 106_487,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 671_357,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-7',
      displayName: 'Fanta', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Fanta',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 217,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 452_678,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 89.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 981,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 84.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 449_993,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_498_799,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-8',
      displayName: 'France', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 61,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 103_157,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 63.0,
              type: 'PERCENTAGE',
            },
            {
              value: -29.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 276,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 85.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 106_403,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 952_809,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-9',
      displayName: 'Spain', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 44,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 84_826,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 69.0,
              type: 'PERCENTAGE',
            },
            {
              value: -23.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 197,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 73.0,
              type: 'PERCENTAGE',
            },
            {
              value: -20.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 81.0,
              type: 'PERCENTAGE',
            },
            {
              value: -1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 70_288,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 702_006,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-10',
      displayName: 'UK', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 70,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 127_322,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 98.0,
              type: 'PERCENTAGE',
            },
            {
              value: 6.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 254,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 91.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 79.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 104_460,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_123_210,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-11',
      displayName: 'US', // maybe don't need
      parentId: 'row-7',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 42,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 137_373,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 94.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 193,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 86.0,
              type: 'PERCENTAGE',
            },
            {
              value: 4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 168_842,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 690_774,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-12',
      displayName: 'Sprite', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Sprite',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 44,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 317_068,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 90.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 298,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 51.0,
              type: 'PERCENTAGE',
            },
            {
              value: -42.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 73.0,
              type: 'PERCENTAGE',
            },
            {
              value: -9.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 164_589,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_279_717,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-13',
      displayName: 'France', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'France',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 12,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 68_382,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 60.0,
              type: 'PERCENTAGE',
            },
            {
              value: -32.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 84,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 68.0,
              type: 'PERCENTAGE',
            },
            {
              value: -25.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 60.0,
              type: 'PERCENTAGE',
            },
            {
              value: -22.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 24_845,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 193_176,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-14',
      displayName: 'Spain', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Spain',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 8,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 42_637,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 70.0,
              type: 'PERCENTAGE',
            },
            {
              value: -22.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 60,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 50.0,
              type: 'PERCENTAGE',
            },
            {
              value: -43.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 59.0,
              type: 'PERCENTAGE',
            },
            {
              value: -23.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 20_318,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 157_977,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-15',
      displayName: 'UK', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'UK',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 14,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 36_619,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 91.0,
              type: 'PERCENTAGE',
            },
            {
              value: -1.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 96,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -25.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -7.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 44_109,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 342_958,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-16',
      displayName: 'US', // maybe don't need
      parentId: 'row-12',
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'US',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 10,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 169_429,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 97.0,
              type: 'PERCENTAGE',
            },
            {
              value: 5.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 59,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 80.0,
              type: 'PERCENTAGE',
            },
            {
              value: -13.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 75_317,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 585_607,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
  ],
};
