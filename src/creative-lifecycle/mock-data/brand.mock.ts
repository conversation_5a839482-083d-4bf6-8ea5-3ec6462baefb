export const BRAND_MOCK_REPORT_DATA = {
  columns: [
    {
      id: 'column-1',
      displayName: 'Brand',
    },
    {
      id: 'column-2',
      displayName: 'Total Master Assets',
    },
    {
      id: 'column-3',
      displayName: 'Total Adaptations',
    },
    {
      id: 'column-4',
      displayName: 'Activation Rate',
    },
    {
      id: 'column-5',
      displayName: 'Adaptation Multiplier',
    },
    {
      id: 'column-6',
      displayName: 'Reuse Rate',
    },
    {
      id: 'column-7',
      displayName: 'Overall Adherence',
    },
    {
      id: 'column-8',
      displayName: 'NTB Sales',
    },
    {
      id: 'column-9',
      displayName: 'Media Spend',
    },
  ],
  items: [
    {
      id: 'row-1',
      displayName: 'Overall', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Overall',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 623,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_879_240,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 92.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 3016,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 82.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 1_032_383,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 8_027_015,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-2',
      displayName: 'Armani', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Armani',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 362,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_109_494,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -17.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 1350,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 417_801,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_248_499,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-3',
      displayName: 'Viktor&Rolf', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Viktor&Rolf',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 217,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 452_678,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 89.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 981,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 84.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 449_993,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_498_799,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-4',
      displayName: 'Maybelline', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Maybelline',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 44,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 317_068,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 90.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 298,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 51.0,
              type: 'PERCENTAGE',
            },
            {
              value: -42.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 73.0,
              type: 'PERCENTAGE',
            },
            {
              value: -9.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 164_589,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_279_717,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
  ],
};

export const BRAND_MOCK_REPORT_COKE_DATA = {
  columns: [
    {
      id: 'column-1',
      displayName: 'Brand',
    },
    {
      id: 'column-2',
      displayName: 'Total Master Assets',
    },
    {
      id: 'column-3',
      displayName: 'Total Adaptations',
    },
    {
      id: 'column-4',
      displayName: 'Activation Rate',
    },
    {
      id: 'column-5',
      displayName: 'Adaptation Multiplier',
    },
    {
      id: 'column-6',
      displayName: 'Reuse Rate',
    },
    {
      id: 'column-7',
      displayName: 'Overall Adherence',
    },
    {
      id: 'column-8',
      displayName: 'NTB Sales',
    },
    {
      id: 'column-9',
      displayName: 'Media Spend',
    },
  ],
  items: [
    {
      id: 'row-1',
      displayName: 'Overall', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Overall',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 623,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_879_240,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 92.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 3016,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 93.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 82.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 1_032_383,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 8_027_015,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-2',
      displayName: 'Coca-Cola', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Coca-Cola',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 362,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 1_109_494,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 75.0,
              type: 'PERCENTAGE',
            },
            {
              value: -17.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 1350,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 96.0,
              type: 'PERCENTAGE',
            },
            {
              value: 3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 78.0,
              type: 'PERCENTAGE',
            },
            {
              value: -4.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 417_801,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_248_499,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-3',
      displayName: 'Fanta', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Fanta',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 217,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 452_678,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 89.0,
              type: 'PERCENTAGE',
            },
            {
              value: -3.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 981,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 95.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 84.0,
              type: 'PERCENTAGE',
            },
            {
              value: 2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 449_993,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 3_498_799,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
    {
      id: 'row-4',
      displayName: 'Sprite', // maybe don't need
      columns: [
        {
          columnId: 'column-1',
          values: [
            {
              value: 'Sprite',
              type: 'STRING',
            },
          ],
        },
        {
          columnId: 'column-2',
          values: [
            {
              value: 44,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-3',
          values: [
            {
              value: 317_068,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-4',
          values: [
            {
              value: 90.0,
              type: 'PERCENTAGE',
            },
            {
              value: -2.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-5',
          values: [
            {
              value: 298,
              type: 'MULTIPLIER',
            },
          ],
        },
        {
          columnId: 'column-6',
          values: [
            {
              value: 51.0,
              type: 'PERCENTAGE',
            },
            {
              value: -42.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-7',
          values: [
            {
              value: 73.0,
              type: 'PERCENTAGE',
            },
            {
              value: -9.0,
              type: 'PERCENTAGE',
            },
          ],
        },
        {
          columnId: 'column-8',
          values: [
            {
              value: 164_589,
              type: 'NUMBER',
            },
          ],
        },
        {
          columnId: 'column-9',
          values: [
            {
              value: 1_279_717,
              type: 'CURRENCY',
            },
          ],
        },
      ],
    },
  ],
};
