import {
  Body,
  Controller,
  Headers,
  Post,
  VERSION_NEUTRAL,
  Version,
} from '@nestjs/common';
import { MailchimpWebhookBodyDto } from '@vidmob/vidmob-soa-notification-service-sdk';
import { NotificationService } from './notification.service';
import { Public } from '../auth/decorators/public.decorator';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Notifications')
@ApiSecurity('Bearer Token')
@Controller('notifications')
export class NotificationsController {
  constructor(private notificationService: NotificationService) {}

  /**
   * Proxy Mailchimp webhook to Notification-Service, as-is.
   *
   * Note: it has no auth at all.
   * Mailchimp payload validity is checked by the Notification-Service by X-Mandrill-Signature.
   *
   * Note: no versioning.
   * Endpoint formats are up to Mailchimp, not BFF.
   *
   * @param signature - The X-Mandrill-Signature, provided by Mailchimp.
   * @param body - The Mailchimp-submitted body, parsed into DTO.
   */
  @Public()
  @Version(VERSION_NEUTRAL)
  @Post('mailchimp/webhook') // Note: keep in sync with vidmob-soa-notification-service config, full (with hostname) request path is involved in signature
  proxyMailchimpWebhook(
    @Headers('X-Mandrill-Signature') signature: string,
    @Body() body: MailchimpWebhookBodyDto,
  ): Promise<object> {
    return this.notificationService.handleMailchimpWebhook(signature, body);
  }
}
