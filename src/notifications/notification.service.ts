import { Injectable } from '@nestjs/common';

import {
  MailchimpWebhookBodyDto,
  DefaultService,
} from '@vidmob/vidmob-soa-notification-service-sdk';

@Injectable()
export class NotificationService {
  constructor(private notificationService: DefaultService) {}

  handleMailchimpWebhook(
    signature: string,
    body: MailchimpWebhookBodyDto,
  ): Promise<object> {
    return this.notificationService.handleMailchimpWebhookAsPromise(
      signature,
      body,
    );
  }
}
