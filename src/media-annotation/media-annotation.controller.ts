import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Request, Param, Post } from '@nestjs/common';
import { MediaAnnotationService } from './media-annotation.service';
import {
  MediaAnnotationFilterDto,
  MediaAnnotationTagDto,
} from './media-annotation.dto';

@ApiTags('MediaAnnotation')
@ApiSecurity('Bearer Token')
@Controller({
  path: 'media-annotations',
})
export class MediaAnnotationController {
  constructor(
    private readonly mediaAnnotationService: MediaAnnotationService,
  ) {}

  @Post('/media/:mediaId/tags')
  async getTags(
    @Request() req: any,
    @Param('mediaId') mediaId: number,
    @Body() body: { filter?: MediaAnnotationFilterDto },
  ): Promise<MediaAnnotationTagDto[]> {
    const { filter } = body;
    const { userId } = req;
    const response = await this.mediaAnnotationService.getTags(
      mediaId,
      userId,
      filter,
    );
    return response;
  }
}
