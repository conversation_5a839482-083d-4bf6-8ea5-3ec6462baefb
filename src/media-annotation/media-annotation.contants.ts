export const MEDIA_MIME_TYPE_TO_EXTENSION_MAP: Record<string, string> = {
  'application/gzip': '.gz',
  'application/illustrator': '.ai',
  'application/javascript': '.js',
  'application/json': '.json',
  'application/mathematica': '.nb',
  'application/msword': '.doc',
  'application/mxf': '.mxf',
  'application/octet-stream': '.octet-stream',
  'application/pdf': '.pdf',
  'application/pgp-signature': '.sig',
  'application/pkcs7-signature': '.p7s',
  'application/postscript': '.ps',
  'application/rdf+xml': '.rdf',
  'application/rtf': '.rtf',
  'application/vnd.3gpp.pic-bw-small': '.psb',
  'application/vnd.adobe.aftereffects.project': '.aep',
  'application/vnd.adobe.aftereffects.template': '.aet',
  'application/vnd.android.package-archive': '.apk',
  'application/vnd.apple.keynote': '.key',
  'application/vnd.apple.numbers': '.numbers',
  'application/vnd.apple.pages': '.pages',
  'application/vnd.clonk.c4group': '.c4g',
  'application/vnd.mif': '.mif',
  'application/vnd.ms-excel': '.xls',
  'application/vnd.ms-excel.sheet.binary.macroenabled.12': '.xlsb',
  'application/vnd.ms-excel.sheet.macroenabled.12': '.xlsm',
  'application/vnd.ms-fontobject': '.eot',
  'application/vnd.ms-outlook': '.msg',
  'application/vnd.ms-pki.stl': '.stl',
  'application/vnd.ms-powerpoint': '.ppt',
  'application/vnd.ms-powerpoint.presentation.macroenabled.12': '.pptm',
  'application/vnd.ms-tnef': '.tnef',
  'application/vnd.oasis.opendocument.text': '.odt',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation':
    '.pptx',
  'application/vnd.openxmlformats-officedocument.presentationml.slideshow':
    '.ppsx',
  'application/vnd.openxmlformats-officedocument.presentationml.template':
    '.potx',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '.xlsx',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    '.docx',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.template':
    '.dotx',
  'application/vnd.syncml.dm+wbxml': '.wbxml',
  'application/x-123': '.wk1',
  'application/x-7z-compressed': '.7z',
  'application/x-adobe-indesign': '.indd',
  'application/x-bplist': '.bplist',
  'application/x-bzip': '.bz',
  'application/x-cpio': '.cpio',
  'application/x-director': '.dir',
  'application/x-dosexec': '.exe',
  'application/x-emf': '.emf',
  'application/x-executable': '.exe',
  'application/x-font-adobe-metric': '.amfm',
  'application/x-font-otf': '.otf',
  'application/x-font-printer-metric': '.afm',
  'application/x-font-ttf': '.ttf',
  'application/x-font-type1': '.pfb',
  'application/x-gtar': '.gtar',
  'application/x-matroska': '.mkv',
  'application/x-ms-installer': '.msi',
  'application/x-msdownload': '.exe',
  'application/x-msdownload; format=pe32': '.exe',
  'application/x-msmetafile': '.wmf',
  'application/x-rar-compressed': '.rar',
  'application/x-sh': '.sh',
  'application/x-shockwave-flash': '.swf',
  'application/x-sqlite3': '.sqlite',
  'application/x-stuffit': '.sit',
  'application/x-tar': '.tar',
  'application/x-tika-msoffice': '.doc',
  'application/x-tika-ooxml': '.xlsx',
  'application/x-webarchive': '.webarchive',
  'application/x-xfig': '.fig',
  'application/xhtml+xml': '.xhtml',
  'application/xml': '.xml',
  'application/zip': '.zip',
  'application/zlib': '.zlib',
  'audio/amr': '.amr',
  'audio/mp4': '.mp4',
  'audio/mpeg': '.mp3',
  'audio/vorbis': '.ogg',
  'audio/x-aac': '.aac',
  'audio/x-aiff': '.aiff',
  'audio/x-flac': '.flac',
  'audio/x-mpegurl': '.m3u',
  'audio/x-wav': '.wav',
  'image/gif': '.gif',
  'image/jp2': '.jp2',
  'image/jpeg': '.jpeg',
  'image/jpx': '.jpf',
  'image/png': '.png',
  'image/svg+xml': '.svg',
  'image/tiff': '.tiff',
  'image/vnd.adobe.photoshop': '.psd',
  'image/vnd.adobe.premiere': '.prel',
  'image/vnd.dxf; format=ascii': '.dxf',
  'image/vnd.microsoft.icon': '.ico',
  'image/webp': '.webp',
  'image/x-ms-bmp': '.bmp',
  'image/x-raw-canon': '.cr2',
  'image/x-raw-fuji': '.raf',
  'image/x-raw-pentax': '.pef',
  'image/x-raw-red': '.r3d',
  'message/rfc822': '.eml',
  'text/css': '.css',
  'text/csv': '.csv',
  'text/html': '.html',
  'text/plain': '.txt',
  'text/vtt': '.vtt',
  'text/x-chdr': '.h',
  'text/x-ini': '.ini',
  'text/x-log': '.log',
  'text/x-matlab': '.m',
  'text/x-pascal': '.pas',
  'text/x-python': '.py',
  'text/x-vcard': '.vcf',
  'text/x-web-markdown': '.md',
  'video/3gpp': '.3gp',
  'video/mp4': '.mp4',
  'video/mpeg': '.mpeg',
  'video/mts': '.mts',
  'video/quicktime': '.mov',
  'video/webm': '.webm',
  'video/x-dirac': '.drc',
  'video/x-flv': '.flv',
  'video/x-m4v': '.m4v',
  'video/x-matroska': '.mkv',
  'video/x-ms-wmv': '.wmv',
  'video/x-msvideo': '.avi',
};

export const MEDIA_DOWNLOAD_URL_EXPIRATION_TIME_SECONDS = 60 * 60; // 1 hour
