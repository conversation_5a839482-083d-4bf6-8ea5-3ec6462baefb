import { MediaAnnotationController } from './media-annotation.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { MediaAnnotationService } from './media-annotation.service';

describe('MediaAnnotationController', () => {
  const mockGetTags = jest.fn();
  const mockGetTagsForPlatformMedia = jest.fn();
  let mediaAnnotationController: MediaAnnotationController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MediaAnnotationController],
      providers: [
        {
          provide: MediaAnnotationService,
          useValue: {
            getTags: mockGetTags,
            getTagsForPlatformMedia: mockGetTagsForPlatformMedia,
          },
        },
      ],
    }).compile();

    mediaAnnotationController = module.get<MediaAnnotationController>(
      MediaAnnotationController,
    );
  });

  it('should be defined', () => {
    expect(mediaAnnotationController).toBeDefined();
  });
});
