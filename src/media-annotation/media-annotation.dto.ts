import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class MediaAnnotationBoundingBoxDto {
  @IsNotEmpty()
  @IsNumber()
  Height: number;

  @IsNotEmpty()
  @IsNumber()
  Width: number;

  @IsNotEmpty()
  @IsNumber()
  Left: number;

  @IsNotEmpty()
  @IsNumber()
  Top: number;

  @IsOptional()
  @IsNumber()
  height?: number;

  @IsOptional()
  @IsNumber()
  width?: number;

  @IsOptional()
  @IsNumber()
  left?: number;

  @IsOptional()
  @IsNumber()
  top?: number;
}

export class MediaAnnotationTagDto {
  @IsNotEmpty()
  @IsString()
  value: string;

  @IsNotEmpty()
  @IsString()
  type: string;

  @IsOptional()
  @IsNumber()
  confidence?: number;

  @IsOptional()
  @IsNumber()
  startTime?: number;

  @IsOptional()
  @IsNumber()
  duration?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MediaAnnotationBoundingBoxDto)
  boundingBox?: MediaAnnotationBoundingBoxDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MediaAnnotationTagDto)
  parents?: MediaAnnotationTagDto[];
}

export class MediaAnnotationFilterDto {
  @IsOptional()
  @IsString()
  apiType?: string;

  @IsOptional()
  @IsArray()
  tagTypes?: string[];

  @IsOptional()
  @IsArray()
  tagSources?: string[];

  @IsOptional()
  @IsArray()
  tagValues?: string[];
}
