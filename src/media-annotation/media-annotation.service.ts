import {
  HttpException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { DefaultService as MediaAnnotationServiceSDK } from '@vidmob/vidmob-soa-media-annotation-service-sdk';
import {
  MediaAnnotationFilterDto,
  MediaAnnotationTagDto,
} from './media-annotation.dto';
import { MediaService } from '@vidmob/vidmob-organization-service-sdk';
import { AxiosError } from 'axios';

const INITIAL_APERTURE_VERSION = '20241206';

@Injectable()
export class MediaAnnotationService {
  private readonly logger = new Logger(MediaAnnotationService.name);

  constructor(
    private readonly mediaAnnotationService: MediaAnnotationServiceSDK,
    private readonly mediaService: MediaService,
  ) {}

  async getTags(
    mediaId: number,
    userId: number,
    filter?: MediaAnnotationFilterDto,
  ): Promise<MediaAnnotationTagDto[]> {
    try {
      const { result } =
        await this.mediaService.validatePersonCanViewMediaAsPromise(
          mediaId,
          userId,
        );
      if (!result?.canView) {
        throw new UnauthorizedException(
          `User is not authorized to view media-id - ${mediaId}`,
        );
      }

      const useRecommendedConfidences = true;
      const response = (await this.mediaAnnotationService.getTagsV2AsPromise(
        mediaId,
        useRecommendedConfidences,
        filter ? JSON.stringify(filter) : '',
        INITIAL_APERTURE_VERSION, //this should be optional but the SDK requires it?
      )) as unknown as { status: string; result: MediaAnnotationTagDto[] };

      return response?.result || [];
    } catch (e) {
      this.logger.error(
        `Error while trying to get media annotation tags ${
          e.response.error.message || e.message || e
        }`,
      );

      this.rethrowAxiosError(e, mediaId);
    }
  }

  rethrowAxiosError(err: any, mediaId: number): never {
    if (err.isAxiosError && err.response) {
      const axiosError: AxiosError = err;
      const { data, status } = axiosError.response!;
      if (data && typeof data === 'object' && 'status' in data) {
        throw new HttpException(data, status);
      }
    }
    throw err.response?.error || err;
  }
}
