import { Test, TestingModule } from '@nestjs/testing';
import { ReportsService } from './reports.service';
import { Brackets, Repository, SelectQueryBuilder } from 'typeorm';
import { Report } from '../entities/report.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DefaultFilterGenerator } from './util/default-filter-generator';
import { ReportConverter } from './util/report-converter';
import { ReportFactory } from './util/report-factory';
import { ScoringReportType } from './model/report';

import { ReportCreationResponseDto } from './model/report-creation-response.dto';
import { ScoringReportsService } from '../scoring/reports/scoring-reports.service';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import {
  InflightUpdateRequestDto,
  ReportUpdateRequestDto,
} from './model/report-update-request.dto';
import {
  ReportFilterDto,
  ReportFilterOperator,
} from './model/report-filters.dto';
import { ScoringAuthService } from '../scoring/scoring-auth/scoring-auth.service';
import { PlatformAdAccount } from '../entities/platform-ad-account.entity';
import { Partner } from '../entities/partner.entity';
import { Person } from '../entities/person.entity';
import { Market } from '../entities/market.entity';
import { WorkspaceService } from '../account-management/organization/workspace/services/workspace.service';
import {
  RollupReportsService,
  ScoringCriteriaService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { BrandService as BrandServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/brand.service';
import { CriteriaGroupService } from '../scoring/criteria/criteria-group.service';
import { AnalyticsReportsService } from '../analytics/saved-report/analytics-reports.service';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';

const mockReportOptionsReturnValue = {
  brands: [
    { value: '1', label: 'Brand X' },
    { value: '2', label: 'Brand Y' },
  ],
  markets: [
    { value: 'usa', label: 'United States' },
    { value: 'mex', label: 'Mexico' },
  ],
  workspaces: [
    { value: 'a', label: 'USA' },
    { value: 'b', label: 'Team Mex' },
    { value: 'c', label: 'Master' },
  ],
  criteriaSets: [
    { label: 'DTC', value: 'f47ac10b-58cc-4372-a567-0e02b2c3d479' },
    { label: 'Equity', value: 'e21b805d-5e49-4bb0-8b1f-916633d03864' },
    { label: 'BEES', value: '7e57d004-2b97-44e7-8f03-66dd730db069' },
  ],
  creativeType: [
    { label: 'Video', value: 'VIDEO' },
    { label: 'Image', value: 'IMAGE' },
    { label: 'Display', value: 'HTML' },
    { label: 'GIF', value: 'ANIMATED_IMAGE' },
  ],
};

describe('ReportsService', () => {
  let service: ReportsService;
  let reportRepository: jest.Mocked<Repository<Report>>;

  const getRelatedAccessiblePartnersMock: jest.Mock = jest.fn();

  const mockTransactionalEntityManager = {
    save: jest.fn(),
    delete: jest.fn(),
    insert: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportsService,
        {
          provide: AnalyticsReportsService,
          useValue: {},
        },
        {
          provide: RollupReportsService,
          useValue: {},
        },
        {
          provide: WorkspaceService,
          useValue: {},
        },
        {
          provide: ScoringCriteriaService,
          useValue: {},
        },
        {
          provide: BrandServiceSDK,
          useValue: {},
        },
        {
          provide: CriteriaGroupService,
          useValue: {},
        },
        {
          provide: getRepositoryToken(Report),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            createQueryBuilder: jest.fn(),
            manager: {
              connection: {
                transaction: jest.fn().mockImplementation(async (callback) => {
                  return callback(mockTransactionalEntityManager);
                }),
              },
            },
            // Mock other methods used by the service
          },
        },
        {
          provide: getRepositoryToken(Person),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            // Mock other methods used by the service
          },
        },
        {
          provide: getRepositoryToken(Partner),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            // Mock other methods used by the service
          },
        },
        {
          provide: getRepositoryToken(Market),
          useValue: {},
        },
        {
          provide: DefaultFilterGenerator,
          useClass: DefaultFilterGenerator,
        },
        {
          provide: ReportConverter,
          useClass: ReportConverter,
        },
        {
          provide: ReportFactory,
          useClass: ReportFactory,
        },
        {
          provide: ScoringReportsService,
          useValue: {
            getReportOptions: jest
              .fn()
              .mockReturnValue(mockReportOptionsReturnValue),
            getChannelIdentifiers: jest.fn(),
            getRelatedAccessiblePartners: getRelatedAccessiblePartnersMock,
          },
        },
        {
          provide: ScoringAuthService,
          useValue: {},
        },
        {
          provide: getRepositoryToken(PlatformAdAccount),
          useValue: {
            createQueryBuilder: jest.fn().mockReturnThis(),
            select: jest.fn().mockReturnThis(),
            where: jest.fn().mockReturnThis(),
            setParameters: jest.fn().mockReturnThis(),
            getMany: jest.fn().mockResolvedValue([]),
            getRawMany: jest.fn().mockResolvedValue([]),
            query: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ReportsService>(ReportsService);
    reportRepository = module.get(getRepositoryToken(Report));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createNewReport', () => {
    it('should create a new report with the provided type', async () => {
      getRelatedAccessiblePartnersMock.mockReturnValue([
        { id: 1, name: 'Partner 1' },
        { id: 2, name: 'Partner 2' },
      ]);
      const result = (await service.createNewReport(
        ScoringReportType.ADOPTION,
        {
          userId: 1111,
          workspaceId: 2222,
        },
      )) as ReportCreationResponseDto;

      // expect(result).toBeDefined();
      expect(result.name).toBe('');
      expect(result.description).toBe('');
      expect(result.reportType).toBe(ScoringReportType.ADOPTION);
      expect(result.filters).toBeDefined();
      const brandFilter = (result.filters as ReportFilterDto[]).filter(
        (f) => f.fieldName === 'brandId',
      );
      expect(brandFilter).toBeDefined();
      expect(brandFilter.length).toBe(1);
      expect(brandFilter[0].value).toEqual(
        mockReportOptionsReturnValue.brands.map((x) => x.value),
      );

      const marketFilter = (result.filters as ReportFilterDto[]).filter(
        (f) => f.fieldName === 'market',
      );
      expect(marketFilter).toBeDefined();
      expect(marketFilter.length).toBe(1);
      expect(marketFilter[0].value).toEqual(
        mockReportOptionsReturnValue.markets.map((x) => x.value),
      );

      const workspaceFilter = (result.filters as ReportFilterDto[]).filter(
        (f) => f.fieldName === 'workspaceId',
      );
      expect(workspaceFilter).toBeDefined();
      expect(workspaceFilter.length).toBe(1);
      expect(workspaceFilter[0].value).toEqual(
        mockReportOptionsReturnValue.workspaces.map((x) => x.value),
      );
    });

    it('should throw an error if the user does not have access to any partners', async () => {
      getRelatedAccessiblePartnersMock.mockReturnValue([]);

      await expect(
        service.createNewReport(ScoringReportType.ADOPTION, {
          userId: 1111,
          workspaceId: 2222,
        }),
      ).rejects.toThrow(
        "User 1111 doesn't have permissions for workspaces in this organization.",
      );
    });

    describe('delete a report', () => {
      it('can only delete your own report', async () => {
        const reportOwnerId = 99;
        const callerId = 1;
        reportRepository.findOne = jest
          .fn()
          .mockResolvedValue({ owner: { id: reportOwnerId } } as Report);
        try {
          await service.deleteReport(callerId, 'report-id');
          expect(true).toBe(false);
        } catch (e) {
          expect(e).toBeInstanceOf(ForbiddenException);
        }
      });

      it('deleting non-existent report throws', async () => {
        reportRepository.findOne = jest.fn().mockResolvedValue(null);
        try {
          await service.deleteReport(1, 'report-id');
          expect(true).toBe(false);
        } catch (e) {
          expect(e).toBeInstanceOf(NotFoundException);
        }
      });
    });

    describe('update a report', () => {
      const reportUpdate: Partial<ReportUpdateRequestDto> = {
        name: 'New Name',
        filtersVersion: 2,
        filters: [
          {
            key: 'workspaceId',
            operator: ReportFilterOperator.In,
            value: [
              { id: 123, name: 'abc' },
              { id: 456, name: 'def' },
            ],
          },
          {
            key: 'mediaCreateDate',
            operator: ReportFilterOperator.Between,
            value: ['2022-01-01', '2022-02-01'],
          },
          {
            key: 'channel',
            operator: ReportFilterOperator.In,
            value: [
              { id: 'LINKEDIN', name: 'LinkedIn' },
              { id: 'FACEBOOK', name: 'Meta' },
            ],
          },
        ],
        groupBy: {
          columns: ['criteria', 'batchType'],
          rows: ['market'],
        },
      };
      const inflightReportUpdate: Partial<InflightUpdateRequestDto> = {
        name: 'New Inflight Name',
        filters: {
          platformAccountIds: ['0qiuw020'],
          workspaceIds: [1111],
          channel: InflightAggregateRequestDto.ChannelEnum.Adwords,
          startDate: '2022-01-01',
          endDate: '2024-02-01',
          analyticsFilters: {},
        },
      };

      const reportDateCreated = new Date('2025-01-01');
      const reportToUpdateFromDb: Report = {
        name: 'Old Name',
        owner: { id: 1 },
        filter: {
          filtersVersion: 2,
          dateCreated: reportDateCreated,
          lastUpdated: reportDateCreated,
          filterWorkspaces: [123, 456],
          filters: JSON.stringify(reportUpdate.filters),
          groupBy: JSON.stringify(reportUpdate.groupBy),
        },
        reportType: ScoringReportType.ADOPTION,
        description: 'Old Description',
      } as Report;

      it('can only update your own report', async () => {
        const reportOwnerId = 99;
        const callerId = 1;
        reportRepository.findOne = jest
          .fn()
          .mockResolvedValue({ owner: { id: reportOwnerId } } as Report);
        try {
          await service.fetchReportToUpdateForUser('report-id', callerId);
          expect(true).toBe(false);
        } catch (e) {
          expect(e).toBeInstanceOf(ForbiddenException);
        }
      });

      it('updating non-existent report throws', async () => {
        reportRepository.findOne = jest.fn().mockResolvedValue(null);
        try {
          await service.fetchReportToUpdateForUser('report-id', 1);
          expect(true).toBe(false);
        } catch (e) {
          expect(e).toBeInstanceOf(NotFoundException);
        }
      });

      it('updating report stringifies filter objects', async () => {
        const callerId = 1;
        reportRepository.findOne = jest.fn().mockResolvedValue({
          owner: { id: callerId },
          filter: {},
        } as Report);
        await service.updateNonInflightReport(
          reportToUpdateFromDb,
          reportUpdate,
        );
        const stringifiedFilters = {} as Record<string, string>;
        stringifiedFilters.filters = JSON.stringify(reportUpdate.filters);
        stringifiedFilters.groupBy = JSON.stringify(reportUpdate.groupBy);
        stringifiedFilters.lastUpdated = expect.any(Date);
        expect(mockTransactionalEntityManager.save).toHaveBeenCalledWith(
          expect.objectContaining({
            filter: expect.objectContaining(stringifiedFilters),
          }),
        );
      });

      it('non-filter update is only a partial update', async () => {
        await service.updateNonInflightReport(
          reportToUpdateFromDb,
          reportUpdate,
        );
        expect(mockTransactionalEntityManager.save).toHaveBeenCalledWith(
          expect.objectContaining({
            filter: expect.objectContaining({
              dateCreated: reportDateCreated,
              lastUpdated: reportDateCreated,
            }),
          }),
        );
      });

      it('updating inflight report stringifies filter objects', async () => {
        const callerId = 1;
        reportRepository.findOne = jest.fn().mockResolvedValue({
          owner: { id: callerId },
          filter: {
            filters:
              '{"workspaceIds": [2222], "platformAccountIds": ["0qiuw020"], "startDate": "2022-01-01", "endDate": "2024-02-01"}',
          },
        } as Report);
        await service.updateInflightReport(
          reportToUpdateFromDb,
          inflightReportUpdate,
        );
        const stringifiedFilters = {} as Record<string, string>;
        stringifiedFilters.filters = JSON.stringify(
          inflightReportUpdate.filters,
        );
        stringifiedFilters.groupBy = JSON.stringify(reportUpdate.groupBy);
        stringifiedFilters.lastUpdated = expect.any(Date);
        expect(mockTransactionalEntityManager.save).toHaveBeenCalledWith(
          expect.objectContaining({
            filter: expect.objectContaining(stringifiedFilters),
          }),
        );
      });
    });
  });

  describe('getReports', () => {
    const createQueryBuilderMock = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getManyAndCount: jest
        .fn()
        .mockImplementation(
          (): Promise<[Report[], number]> => Promise.resolve([[], 0]),
        ),
      getQueryAndParameters: jest.fn().mockReturnThis(),
    } as unknown as SelectQueryBuilder<Report>;

    beforeEach(() => {
      reportRepository.createQueryBuilder.mockReturnValue(
        createQueryBuilderMock,
      );
      (createQueryBuilderMock.getManyAndCount as jest.Mock).mockImplementation(
        (): Promise<[Report[], number]> => Promise.resolve([[], 0]),
      );
    });

    it('should filter by new filters', async () => {
      (createQueryBuilderMock.getManyAndCount as jest.Mock).mockImplementation(
        (): Promise<[Report[], number]> => Promise.resolve([[], 1]),
      );

      await service.getReports({
        workspaceId: 100,
        paginationOptions: { offset: 0, perPage: 10 },
        sortOrder: 'DESC',
        sortBy: 'name',
        searchTerm: 'Report 1',
        filtersVersions: [1],
        accessibleWorkspaceIds: [],
        customFilters: {
          types: [ScoringReportType.ADOPTION],
          createdBy: [1234],
          dateCreated: {
            startDate: '2022-01-01',
            endDate: '2022-02-01',
          },
          lastUpdated: {
            startDate: '2022-01-01',
            endDate: '2022-02-01',
          },
        },
      });

      expect(createQueryBuilderMock.where).toHaveBeenCalledWith(
        'reports.reportType IN (:...types)',
        { types: [ScoringReportType.ADOPTION] },
      );

      expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
        'partner.id = :workspaceId',
        { workspaceId: 100 },
      );

      const bracketsCall = (
        createQueryBuilderMock.andWhere as jest.Mock
      ).mock.calls.find(([firstArg]) => firstArg instanceof Brackets);

      expect(bracketsCall).toBeDefined();
      const brackets = bracketsCall[0] as Brackets;

      // Create a mock query builder to verify the Brackets function
      const qbMock = {
        where: jest.fn().mockReturnThis(),
        orWhere: jest.fn().mockReturnThis(),
      };

      // Execute the Brackets function
      brackets.whereFactory(qbMock as any);

      // Verify the where conditions inside Brackets
      expect(qbMock.where).toHaveBeenCalledWith(
        'reports.name LIKE :searchTerm',
        { searchTerm: `%Report 1%` },
      );

      expect(qbMock.orWhere).toHaveBeenCalledWith(
        'reports.description LIKE :searchTerm',
        { searchTerm: `%Report 1%` },
      );

      expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
        'reports.dateCreated BETWEEN :dateCreatedStart AND :dateCreatedEnd',
        {
          dateCreatedStart: '2022-01-01',
          dateCreatedEnd: '2022-02-01',
        },
      );

      expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
        'reports.lastUpdated BETWEEN :lastUpdatedStart AND :lastUpdatedEnd',
        {
          lastUpdatedStart: '2022-01-01',
          lastUpdatedEnd: '2022-02-01',
        },
      );
    });
  });
});
