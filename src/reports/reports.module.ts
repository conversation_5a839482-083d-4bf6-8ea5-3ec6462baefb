import { Module } from '@nestjs/common';
import { ReportsService } from './reports.service';
import { ReportsController } from './reports.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Report } from '../entities/report.entity';
import { ReportFilter } from '../entities/report-filter.entity';
import { Person } from '../entities/person.entity';
import { Partner } from '../entities/partner.entity';
import { ReportConverter } from './util/report-converter';
import { DefaultFilterGenerator } from './util/default-filter-generator';
import { ReportFactory } from './util/report-factory';
import { ScoringModule } from '../scoring/scoring.module';
import { PersonOrganizationMap } from 'src/entities/person-organization-map.entity';
import { ScoringAuthService } from '../scoring/scoring-auth/scoring-auth.service';
import { HttpModule } from '@nestjs/axios';
import { ReportWorkspaceMap } from 'src/entities/report-workspace-map.entity';
import { ReportChannelMap } from 'src/entities/report-channel.map.entity';
import { ReportPlatformAccountMap } from 'src/entities/report-platform-account-map.entity';
import { ReportPlatformAdAccountMap } from '../entities/report-platform-ad-account-map.entity';
import { Market } from '../entities/market.entity';
import { PlatformAdAccount } from '../entities/platform-ad-account.entity';
import { Organization } from '../entities/organization.entity';
import { Workspace } from '../entities/workspace.entity';
import { AccountManagementModule } from '../account-management/account-management.module';
import { AnalyticsReportsModule } from '../analytics/saved-report/analytics-reports.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      ReportFilter,
      Report,
      Person,
      Partner,
      Market,
      PersonOrganizationMap,
      ReportChannelMap,
      ReportWorkspaceMap,
      ReportPlatformAccountMap,
      ReportPlatformAdAccountMap,
      PlatformAdAccount,
      Organization,
      Workspace,
    ]),
    ScoringModule,
    HttpModule,
    AccountManagementModule,
    AnalyticsReportsModule,
  ],
  providers: [
    ReportsService,
    ReportConverter,
    DefaultFilterGenerator,
    ReportFactory,
    ScoringAuthService,
  ],
  controllers: [ReportsController],
})
export class ReportsModule {}
