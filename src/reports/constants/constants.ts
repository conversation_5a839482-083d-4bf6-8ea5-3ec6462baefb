import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { GetScoringReportBodyDto } from '../model/report-list.dto';

export const MEDIA_TYPES_ID_TO_NAME_MAP = {
  VIDEO: 'Video',
  IMAGE: 'Image',
  ANIMATED_IMAGE: 'GIF',
  HTML: 'Display',
};

export const SCORE_RESULT_ID_NAME_MAP: Record<
  InflightAggregateRequestDto.ScoreResultsEnum,
  string
> = {
  EMPTY: 'All Evaluated Creative',
  PASS: 'Met Criteria',
  FAIL: 'Did Not Meet Criteria',
  NO_DATA: 'Not Available',
};

export const CHANNEL_ID_TO_NAME_MAP: Record<
  InflightAggregateRequestDto.ChannelEnum,
  string
> = {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  AMAZONADVERTISING: 'Amazon Advertising',
  AMAZONADVERTISINGDSP: 'Amazon DSP',
  DV360: 'DV360',
  ADWORDS: 'Google Ads',
  LINKEDIN: 'LinkedIn',
  FACEBOOK: 'Meta',
  PINTEREST: 'Pinterest',
  REDDIT: 'Reddit',
  SNAPCHAT: 'Snapchat',
  TIKTOK: 'TikTok',
  TWITTER: 'X',
};

export interface GetReportOptions {
  workspaceId: number;
  paginationOptions: PaginationOptions;
  sortOrder: 'ASC' | 'DESC';
  sortBy: string;
  searchTerm: string;
  filtersVersions: number[];
  accessibleWorkspaceIds?: number[];
  customFilters?: GetScoringReportBodyDto;
}

export const DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS = ['channel', 'criteria'];
