import { PermissionSubResource } from '../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../auth/enums/permission.domain.enum';
import { PermissionAction } from '../auth/enums/permission.action.enum';
import {
  organization<PERSON>romParams<PERSON>and<PERSON>,
  partner<PERSON>rom<PERSON>ody<PERSON>and<PERSON>,
  partnerFromParamsHandler,
} from '../auth/decorators/permission.decorator';

export const readReports = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DETAILS,
    },
  ],
};

export const readOrgReports = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const createReports = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partner<PERSON>romBody<PERSON>and<PERSON>,
  required: [
    {
      //currently the same as readReports but it may diverge in the future
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DETAILS,
    },
  ],
};
