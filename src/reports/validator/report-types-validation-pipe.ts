import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { ScoringReportType } from '../model/report';

@Injectable()
export class ReportTypesValidationPipe implements PipeTransform {
  transform(value: string): ScoringReportType[] {
    const values = value.split(',');
    const uppercaseValues: string[] = values.map((str) => str.toUpperCase());
    if (
      !uppercaseValues.every((item) =>
        Object.values(ScoringReportType).includes(item as ScoringReportType),
      )
    ) {
      throw new BadRequestException(
        'Invalid value. Expected report type to be one of: ' +
          Object.values(ScoringReportType),
      );
    }

    return values.map(
      (item) => ScoringReportType[item.toUpperCase() as keyof typeof ScoringReportType],
    );
  }
}
