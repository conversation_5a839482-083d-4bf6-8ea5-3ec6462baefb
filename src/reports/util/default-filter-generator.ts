import { BadRequestException, Injectable } from '@nestjs/common';
import { Report, ScoringReportType } from '../model/report';
import { Adoption } from '../model/scoring/adoption-report-type';
import { Adherence } from '../model/scoring/adherence-report-type';
import { ScoringReportsService } from '../../scoring/reports/scoring-reports.service';
import { UserInfoDto } from '../../scoring/reports/dtos/report-options.dto';
import { ImpressionAdherence } from '../model/scoring/impression-adherence-report-type';
import { Diversity } from '../model/scoring/diversity-report-type';
import { Inflight } from '../model/scoring/inflight-report-type';

@Injectable()
export class DefaultFilterGenerator {
  constructor(private readonly scoringReportsService: ScoringReportsService) {}

  async createReportType(
    reportType: ScoringReportType,
    userInfo: UserInfoDto,
  ): Promise<Report> {
    if (reportType === ScoringReportType.ADOPTION) {
      const report = new Adoption(this.scoringReportsService);
      await report.setUserInfo(userInfo);
      return report;
    } else if (reportType === ScoringReportType.ADHERENCE) {
      const report = new Adherence(this.scoringReportsService);
      await report.setUserInfo(userInfo);
      return report;
    } else if (reportType === ScoringReportType.IMPRESSION_ADHERENCE) {
      const report = new ImpressionAdherence(this.scoringReportsService);
      await report.setUserInfo(userInfo);
      return report;
    } else if (reportType === ScoringReportType.DIVERSITY) {
      const report = new Diversity(this.scoringReportsService);
      await report.setUserInfo(userInfo);
      return report;
    } else if (reportType === ScoringReportType.IN_FLIGHT) {
      const report = new Inflight(this.scoringReportsService);
      await report.setUserInfo(userInfo);
      return report;
    } else {
      throw new BadRequestException(`Invalid report type '${reportType}'.`);
    }
  }
}
