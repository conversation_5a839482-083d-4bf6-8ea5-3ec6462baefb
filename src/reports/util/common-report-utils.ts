import { Report } from '../../entities/report.entity';
import { ReportPlatformAdAccountMap } from '../../entities/report-platform-ad-account-map.entity';
import { ReportPlatformAccountMap } from '../../entities/report-platform-account-map.entity';
import { ReportWorkspaceMap } from '../../entities/report-workspace-map.entity';
import { ReportChannelMap } from '../../entities/report-channel.map.entity';
import { DataSource, Repository } from 'typeorm';
import { PlatformAdAccount } from '../../entities/platform-ad-account.entity';

export type ReportAttributes = Partial<InflightReportAttributes> &
  Pick<InflightReportAttributes, 'workspaceIds'>;

export type InflightReportAttributes = {
  workspaceIds: number[];
  platformAdAccountIds?: number[];
  channels: string[];
  platformAccountIds: string[];
};

export const saveReport = async (
  report: Report,
  {
    platformAdAccountIds,
    workspaceIds,
    platformAccountIds,
    channels,
  }: ReportAttributes,
  dataSource: DataSource,
) => {
  return await dataSource.transaction(async (transactionalEntityManager) => {
    report.lastUpdated = new Date();
    const savedReport = await transactionalEntityManager.save(report);

    if (platformAdAccountIds) {
      await transactionalEntityManager.delete(ReportPlatformAdAccountMap, {
        reportId: report.id,
      });
      const mappings = platformAdAccountIds.map((platformAdAccountId) => ({
        reportId: report.id,
        platformAdAccountId,
      }));
      await transactionalEntityManager.insert(
        ReportPlatformAdAccountMap,
        mappings,
      );
    }

    if (platformAccountIds) {
      //TODO delete this once transition to platformAdAccountMap is complete
      await transactionalEntityManager.delete(ReportPlatformAccountMap, {
        reportId: report.id,
      });
      const mappings = platformAccountIds.map((platformAccountId) => ({
        reportId: report.id,
        platformAccountId,
      }));
      await transactionalEntityManager.insert(
        ReportPlatformAccountMap,
        mappings,
      );
    }

    if (workspaceIds) {
      await transactionalEntityManager.delete(ReportWorkspaceMap, {
        reportId: report.id,
      });
      const mappings = workspaceIds.map((workspaceId) => ({
        reportId: report.id,
        workspaceId,
      }));
      await transactionalEntityManager.insert(ReportWorkspaceMap, mappings);
    }

    if (channels) {
      await transactionalEntityManager.delete(ReportChannelMap, {
        reportId: report.id,
      });
      const mappings = channels.map((channel) => ({
        reportId: report.id,
        channel,
      }));
      await transactionalEntityManager.insert(ReportChannelMap, mappings);
    }

    return savedReport;
  });
};

export const getPlatformAdAccountIdsFromPlatformData = async (
  platformAdAccountRepository: Repository<PlatformAdAccount>,
  platformAccountFilters: { platformAccountId: string; channel: string }[],
) => {
  if (platformAccountFilters.length === 0) {
    return [];
  }

  return platformAdAccountRepository
    .createQueryBuilder('platformAdAccount')
    .select('platformAdAccount.id', 'id')
    .where(
      platformAccountFilters
        .map(
          (_, index) =>
            `(platformAdAccount.platform = :platform${index} AND platformAdAccount.platform_account_id = :platformAccountId${index})`,
        )
        .join(' OR '),
    )
    .setParameters(
      Object.assign(
        {},
        ...platformAccountFilters.map((acct, index) => ({
          [`platform${index}`]: acct.channel,
          [`platformAccountId${index}`]: acct.platformAccountId,
        })),
      ),
    )
    .getRawMany()
    .then((results) => results.map((result) => Number(result.id)));
};

export const normalizeSQL = (query: string): string => {
  return query
    .replace(/\s+/g, ' ') // Replace multiple spaces/newlines with a single space
    .trim(); // Trim leading and trailing spaces
};
