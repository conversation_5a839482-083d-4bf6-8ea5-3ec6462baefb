import { Injectable } from '@nestjs/common';
import { Report } from '../../entities/report.entity';
import { ReportFilter } from '../../entities/report-filter.entity';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Person } from '../../entities/person.entity';
import { Partner } from '../../entities/partner.entity';
import { ReportBuildParams } from '../model/report-build-params';

@Injectable()
export class ReportFactory {
  constructor(
    @InjectRepository(Person)
    private personRepository: Repository<Person>,
    @InjectRepository(Partner)
    private partnerRepository: Repository<Partner>,
  ) {}

  async build(reportBuildParams: ReportBuildParams): Promise<Report> {
    if (reportBuildParams.name === undefined) {
      reportBuildParams.name = 'New Report';
    }
    if (reportBuildParams.description === undefined) {
      reportBuildParams.description = 'This is a new report';
    }
    if (reportBuildParams.filtersVersion === undefined) {
      reportBuildParams.filtersVersion = 1;
    }

    const ownerPromise = this.personRepository.findOneBy({
      id: reportBuildParams.ownerId,
    });
    const partnerPromise = this.partnerRepository.findOneBy({
      id: reportBuildParams.workspaceId,
    });

    return Promise.all([ownerPromise, partnerPromise]).then((values) => {
      const owner = values[0]!;
      const partner = values[1]!;
      const { filters, analyticsFilters } = reportBuildParams;

      let combinedFilters = filters;
      // handle non in-flight filters
      if (Array.isArray(filters)) {
        combinedFilters = analyticsFilters
          ? // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            [...filters, { analyticsFilters }]
          : [...filters];
      }

      const reportFilter = new ReportFilter();
      reportFilter.id = uuidv4();
      reportFilter.owner = owner;
      reportFilter.adHoc = true;
      reportFilter.shared = true;
      reportFilter.filtersVersion = reportBuildParams.filtersVersion;
      reportFilter.filters = JSON.stringify(combinedFilters);
      reportFilter.filterWorkspaces = reportBuildParams.workspaces;
      reportFilter.sortBy = JSON.stringify(reportBuildParams.sortBy);
      reportFilter.groupBy = JSON.stringify(reportBuildParams.groupBy);
      reportFilter.aggregationColumns = JSON.stringify(
        reportBuildParams.aggregationColumns,
      );
      reportFilter.dateCreated = new Date();
      reportFilter.lastUpdated = new Date();
      reportFilter.lastUpdatedByUser = owner;
      reportFilter.deleted = false;

      const report = new Report();
      report.id = uuidv4();
      report.name = reportBuildParams.name!;
      report.description = reportBuildParams.description!;
      report.owner = owner;
      report.partner = partner;
      report.filter = reportFilter;
      report.shared = true;
      report.reportType = reportBuildParams.reportType;
      report.dateCreated = new Date();
      report.lastUpdated = new Date();
      report.lastUpdatedByUser = owner;
      report.deleted = false;

      // Save the Report entity
      return report;
    });
  }
}
