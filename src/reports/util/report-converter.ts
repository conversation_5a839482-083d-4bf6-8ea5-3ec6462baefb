import { Injectable } from '@nestjs/common';
import { Report } from '../../entities/report.entity';
import { DataGridState, ReportMetadataDto } from '../model/report-metadata.dto';
import { ReportMetadataListItemDto } from '../model/report-metadata-list-item.dto';
import { ScoringReportType } from '../model/report';
import { Person } from 'src/entities/person.entity';
import { ReportMetadataOwnerDto } from '../model/report-metadata-owner.dto';
import { ReportFilterOperator } from '../model/report-filters.dto';
import {
  ENTITY_TYPE_FILTER_KEY,
  EntityType,
} from '../../scoring/reports/constants/constants';
import { DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS } from '../constants/constants';

@Injectable()
export class ReportConverter {
  convert(
    report: Report,
    criteriaLastCreatedDate?: Date,
    shouldConvertV2ToV3Format = false,
  ): ReportMetadataDto {
    const parsedFilters = JSON.parse(report.filter.filters);

    let scoringFilters;
    let analyticsFilters;

    // handle non in-flight filters
    if (Array.isArray(parsedFilters)) {
      // Extract the analytics filters object
      analyticsFilters =
        parsedFilters.find((filter: any) => filter.analyticsFilters)
          ?.analyticsFilters || {};

      // Extract the scoring filters object
      scoringFilters = parsedFilters.filter(
        (filter: any) => !filter.analyticsFilters,
      );
      this.handleScoringEntityTypeFilter(report, scoringFilters);
    } else {
      scoringFilters = parsedFilters;
    }

    let dataGridState;
    const groupBy = report.filter.groupBy
      ? JSON.parse(report.filter.groupBy)
      : null;
    const doesReportGroupByNeedConversion =
      shouldConvertV2ToV3Format &&
      report.filter.filtersVersion === 2 &&
      [
        ScoringReportType.IMPRESSION_ADHERENCE,
        ScoringReportType.ADHERENCE,
      ].includes(report.reportType as ScoringReportType) &&
      groupBy;
    if (doesReportGroupByNeedConversion) {
      groupBy.columns = groupBy.rows;
      groupBy.rows = DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS;
      dataGridState = {
        rows: 'COLLAPSED' as DataGridState,
        columns: 'COLLAPSED' as DataGridState,
      };
    }

    return {
      id: report.id,
      name: report.name,
      description: report.description,
      owner: this.getOwner(report.owner),
      shared: report.shared,
      reportType: report.reportType as ScoringReportType,
      dateCreated: report.dateCreated,
      lastUpdated: report.lastUpdated,
      criteriaLastCreatedDate,
      filtersVersion: report.filter.filtersVersion,
      filters: scoringFilters,
      analyticsFilters,
      sortBy: report.filter.sortBy ? JSON.parse(report.filter.sortBy) : null,
      groupBy,
      aggregationColumns: JSON.parse(report.filter.aggregationColumns || '[]'),
      dataGridState,
    };
  }

  convertItem(report: Report): ReportMetadataListItemDto {
    const { filtersVersion } = report.filter;
    const isLegacy = filtersVersion == 1;

    return {
      id: report.id,
      name: report.name,
      description: report.description,
      owner: this.getOwner(report.owner),
      shared: report.shared,
      reportType: report.reportType as ScoringReportType,
      dateCreated: report.dateCreated,
      lastUpdated: report.lastUpdated,
      isLegacy,
      adAccounts:
        report.platformAdAccountMaps?.map((platformAdAccountMap) => ({
          platform_account_id:
            platformAdAccountMap.platformAdAccount.platform_account_id,
          name: platformAdAccountMap.platformAdAccount.name,
        })) || [],
      channels:
        report.channelMaps?.map((channelMap) => channelMap.channel) || [],
    };
  }

  private getOwner(person: Person): ReportMetadataOwnerDto {
    const owner = {
      id: person.id,
      firstName: person.firstName,
      lastName: person.lastName,
      displayName: person.displayName,
      photo: person.photo,
    };

    const isPersonDeactivatedFromOrganization =
      person?.personOrganizationMaps?.length == 0;

    if (isPersonDeactivatedFromOrganization) {
      owner.displayName = `${person.email} - Deactivated User`;
    }

    return owner;
  }

  private handleScoringEntityTypeFilter(report: Report, scoringFilters: any) {
    const isFilterVersion2 = report.filter.filtersVersion === 2;
    const isEntityTypeAppliesToReportType =
      report.reportType === ScoringReportType.ADHERENCE ||
      report.reportType === ScoringReportType.IMPRESSION_ADHERENCE ||
      report.reportType === ScoringReportType.ADOPTION;

    if (isFilterVersion2 && isEntityTypeAppliesToReportType) {
      const entityTypeFilter = scoringFilters.find(
        (filter: any) => filter.key === ENTITY_TYPE_FILTER_KEY,
      );

      if (!entityTypeFilter) {
        scoringFilters.push(this.getDefaultScoringEntityTypeFilter());
      }
    }
  }

  private getDefaultScoringEntityTypeFilter() {
    return {
      key: ENTITY_TYPE_FILTER_KEY,
      operator: ReportFilterOperator.Equals,
      value: EntityType.AD_ASSET,
    };
  }
}
