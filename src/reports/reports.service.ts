import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, In, Repository } from 'typeorm';
import { Report } from '../entities/report.entity';
import { ReportFilter } from '../entities/report-filter.entity';
import { ReportCreationResponseDto } from './model/report-creation-response.dto';
import {
  InflightReportCreationRequestDto,
  ReportCreationRequestDto,
} from './model/report-creation-request.dto';
import { ReportMetadataDto } from './model/report-metadata.dto';
import { ReportConverter } from './util/report-converter';
import { ReportFactory } from './util/report-factory';
import {
  InflightUpdateRequestDto,
  ReportUpdateRequestDto,
} from './model/report-update-request.dto';
import { ReportBuildParams } from './model/report-build-params';
import { AdvancedFilterArg } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReportMetadataListItemDto } from './model/report-metadata-list-item.dto';
import { ScoringReportType } from './model/report';
import { UserInfoDto } from '../scoring/reports/dtos/report-options.dto';
import { DefaultFilterGenerator } from './util/default-filter-generator';
import { SortBy } from './reports-enums';
import {
  PlatformAccountFilter,
  PlatformAccountFilterSchema,
  ReportV2Filter,
  V2OrV3ReportSchema,
} from '../common/types/report-types';
import { PlatformAdAccount } from '../entities/platform-ad-account.entity';
import {
  getPlatformAdAccountIdsFromPlatformData,
  ReportAttributes,
  saveReport,
} from './util/common-report-utils';

import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';
import {
  InflightMetadataAnalyticsFilterDto,
  InflightMetadataResponseDto,
  NumberIdAndNameDto,
  StringIdAndNameDto,
} from './model/inflight-metadata-response.dto';
import {
  CHANNEL_ID_TO_NAME_MAP,
  GetReportOptions,
  MEDIA_TYPES_ID_TO_NAME_MAP,
  SCORE_RESULT_ID_NAME_MAP,
} from './constants/constants';
import { WorkspaceService } from '../account-management/organization/workspace/services/workspace.service';
import { UserDetailsDto } from '../analytics/dto/user-details.dto';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { BrandService as BrandServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/brand.service';
import { CriteriaGroupService } from '../scoring/criteria/criteria-group.service';
import { Market } from '../entities/market.entity';
import { Platform } from '../constants/platform.constants';
import {
  AdvancedFilterEnum,
  AggregateFilterValues,
  FilterValues,
} from '@vidmob/vidmob-nestjs-common/dist/analytics-queries/types/request-response-types';
import {
  ADVANCED_FILTER_TYPE_TO_REQUEST_FILTER_TYPE_MAP,
  FilterItem,
  GetAnalyticsReportFilterOptionsRequestDto,
} from '../analytics/saved-report/dto/get-analytics-filter-values.dto';
import { AnalyticsReportsService } from '../analytics/saved-report/analytics-reports.service';

const criteriaBasedReports = new Set<string>([
  ScoringReportType.ADHERENCE,
  ScoringReportType.ADHERENCE,
  ScoringReportType.IMPRESSION_ADHERENCE,
  ScoringReportType.IN_FLIGHT,
]);

@Injectable()
export class ReportsService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(
    @InjectRepository(Report)
    private reportsRepository: Repository<Report>,
    private readonly defaultFilterGenerator: DefaultFilterGenerator,
    private readonly reportConverter: ReportConverter,
    private readonly reportFactory: ReportFactory,
    @InjectRepository(PlatformAdAccount)
    private platformAdAccountRepository: Repository<PlatformAdAccount>,
    @InjectRepository(Market)
    private readonly marketRepository: Repository<Market>,
    private readonly workspaceService: WorkspaceService,
    private readonly scoringCriteriaService: ScoringCriteriaService,
    private readonly brandServiceSDK: BrandServiceSDK,
    private readonly criteriaGroupService: CriteriaGroupService,
    private readonly analyticsReportsService: AnalyticsReportsService,
  ) {}

  async createNewReport(
    reportType: ScoringReportType,
    userInfo: UserInfoDto,
  ): Promise<ReportCreationResponseDto> {
    const rt = await this.defaultFilterGenerator.createReportType(
      reportType,
      userInfo,
    );
    const newReportResponse = new ReportCreationResponseDto();
    newReportResponse.name = rt.getReportName();
    newReportResponse.description = '';
    newReportResponse.reportType = reportType;
    newReportResponse.filtersVersion = rt.getFilterVersion();
    newReportResponse.filters = rt.generateFilter();
    newReportResponse.sortBy = rt.generateSort();
    newReportResponse.groupBy = rt.generateGroupBy();
    newReportResponse.aggregationColumns =
      await rt.generateAggregationColumns();
    return newReportResponse;
  }

  async createNewReportV2(
    reportType: ScoringReportType,
    userInfo: UserInfoDto,
  ): Promise<ReportCreationResponseDto> {
    const rt = await this.defaultFilterGenerator.createReportType(
      reportType,
      userInfo,
    );
    const newReportResponse = new ReportCreationResponseDto();
    newReportResponse.name = rt.getReportName();
    newReportResponse.description = '';
    newReportResponse.reportType = reportType;
    newReportResponse.filtersVersion = rt.getFiltersVersionV2();
    newReportResponse.filters = rt.generateFilterV2();
    newReportResponse.sortBy = rt.generateSort();
    newReportResponse.groupBy = rt.generateGroupByV2();
    newReportResponse.aggregationColumns =
      await rt.generateAggregationColumns();
    return newReportResponse;
  }

  async createNewReportV3(
    reportType: ScoringReportType,
    userInfo: UserInfoDto,
  ): Promise<ReportCreationResponseDto> {
    const rt = await this.defaultFilterGenerator.createReportType(
      reportType,
      userInfo,
    );
    const newReportResponse = new ReportCreationResponseDto();
    newReportResponse.name = rt.getReportName();
    newReportResponse.description = '';
    newReportResponse.reportType = reportType;
    newReportResponse.filtersVersion = rt.getFiltersVersionV3();
    newReportResponse.filters = rt.generateFilterV2();
    newReportResponse.sortBy = rt.generateSort();
    newReportResponse.groupBy = rt.generateGroupByV3();
    newReportResponse.aggregationColumns =
      await rt.generateAggregationColumns();
    newReportResponse.dataGridState = rt.generateDataGridState();
    return newReportResponse;
  }

  /**
   * Extracts workspace IDs from the report metadata for version 2 filters only.
   * Compatible with both the original and V2 filter metadata shapes.
   *
   * @param {ReportCreationRequestDto} reportData - The report creation data object.
   * @returns {number[]} - An array of workspace IDs, or an empty array if the filter is not found or version is not 2.
   */
  private getWorkspaceIdsFromMetadata(
    reportData: ReportCreationRequestDto | Partial<ReportUpdateRequestDto>,
  ): number[] {
    if (reportData.filtersVersion === 1) {
      return [];
    }

    const workspaceFilter = (reportData.filters as any[]).find(
      (filter) =>
        ('key' in filter && filter.key === 'workspaceId') ||
        ('fieldName' in filter && filter.fieldName === 'workspaceId'),
    );

    if (
      !workspaceFilter ||
      !Array.isArray(workspaceFilter.value) ||
      workspaceFilter.value.length === 0
    ) {
      throw new BadRequestException(
        'Workspaces must be specified in report filters.',
      );
    }

    if ('key' in workspaceFilter) {
      // V2: Extract IDs from objects
      return (workspaceFilter.value as { id: number; name: string }[]).map(
        (workspace) => Number(workspace.id),
      );
    } else {
      // V1: Return array of numbers directly
      return workspaceFilter.value.map((workspaceId: number) =>
        Number(workspaceId),
      );
    }
  }

  private getPlatformAccountIdsFromV2Metadata(
    reportData: ReportCreationRequestDto | Partial<ReportUpdateRequestDto>,
  ): string[] {
    if (reportData.filtersVersion !== 2) {
      return [];
    }

    const platformAccountFilter = (reportData.filters as any[]).find(
      (filter) =>
        ('key' in filter && filter.key === 'platformAccountId') ||
        ('fieldName' in filter && filter.fieldName === 'platformAccountId'),
    );

    if (!platformAccountFilter || !Array.isArray(platformAccountFilter.value)) {
      return [];
    }

    return platformAccountFilter.value.map(
      (platformAccount: { id: string; name: string }) => platformAccount.id,
    );
  }

  private async getPlatformAdAccountIdsForReport(
    reportFilters: ReportV2Filter[],
  ) {
    const platformAccountFilters = (
      reportFilters.filter(
        (filter) => PlatformAccountFilterSchema.safeParse(filter).success,
      ) as PlatformAccountFilter[]
    ).flatMap((filter) => {
      return filter.value.map((platformAccount) => ({
        platformAccountId: platformAccount.id,
        channel: platformAccount.channel,
      }));
    });
    return getPlatformAdAccountIdsFromPlatformData(
      this.platformAdAccountRepository,
      platformAccountFilters,
    );
  }

  private getChannelIdsFromMetadata(
    reportData: ReportCreationRequestDto | Partial<ReportUpdateRequestDto>,
  ): string[] {
    if (reportData.filtersVersion === 1) {
      return [];
    }

    const channelFilter = (reportData.filters as any[]).find(
      (filter) =>
        ('key' in filter && filter.key === 'channel') ||
        ('fieldName' in filter && filter.fieldName === 'channel'),
    );

    if (!channelFilter || !Array.isArray(channelFilter.value)) {
      throw new BadRequestException(
        'Channels must be specified in report filters.',
      );
    }

    return channelFilter.value.map((channel: { id: string; name: string }) =>
      channel.id.toUpperCase(),
    );
  }

  async save(
    userId: number,
    reportData: ReportCreationRequestDto,
  ): Promise<Report> {
    const reportBuildParams = new ReportBuildParams();
    Object.assign(reportBuildParams, reportData);
    reportBuildParams.ownerId = userId;

    // Fetch associated workspace IDs if this is a v2 report.
    const reportWorkspaces = this.getWorkspaceIdsFromMetadata(reportData);

    const channels = this.getChannelIdsFromMetadata(reportData);
    const workspaceIds = this.getWorkspaceIdsFromMetadata(reportData);
    const platformAccountIds =
      this.getPlatformAccountIdsFromV2Metadata(reportData);

    const v2Report = V2OrV3ReportSchema.safeParse(reportData);
    const platformAdAccountIds = v2Report.success
      ? await this.getPlatformAdAccountIdsForReport(v2Report.data.filters)
      : [];

    // Store workspace IDs on the filterWorkspaces field.
    if (reportWorkspaces.length > 0) {
      reportBuildParams.workspaces = reportWorkspaces;
    }

    const report = await this.reportFactory.build(reportBuildParams);
    const dataSource = this.reportsRepository.manager.connection;
    return await saveReport(
      report,
      {
        platformAdAccountIds,
        workspaceIds,
        platformAccountIds,
        channels,
      },
      dataSource,
    );
  }

  async saveInflightReport(
    userId: number,
    reportData: InflightReportCreationRequestDto,
    platformAccountIds: string[],
  ): Promise<Report> {
    reportData.filters.platformAccountIds = platformAccountIds;
    const { workspaceIds, channel } = reportData.filters;
    const reportBuildParams = new ReportBuildParams();
    Object.assign(reportBuildParams, reportData);

    reportBuildParams.ownerId = userId;
    const reportWorkspaceIds = reportData.filters.workspaceIds;

    const platformData = reportData.filters.platformAccountIds.map((id) => {
      return { platformAccountId: id, channel };
    });
    const platformAdAccountIds = await getPlatformAdAccountIdsFromPlatformData(
      this.platformAdAccountRepository,
      platformData,
    );

    // Store workspace IDs on the filterWorkspaces field.
    if (reportWorkspaceIds.length > 0) {
      reportBuildParams.workspaces = reportWorkspaceIds;
    }

    const report = await this.reportFactory.build(reportBuildParams);
    const dataSource = this.reportsRepository.manager.connection;
    return await saveReport(
      report,
      {
        platformAdAccountIds,
        workspaceIds,
        platformAccountIds,
        channels: [channel],
      },
      dataSource,
    );
  }

  async fetchReportToUpdateForUser(
    reportId: string,
    userId: number,
  ): Promise<Report> {
    const report = await this.reportsRepository.findOne({
      where: { id: reportId, deleted: false },
      relations: [
        'owner',
        'partner',
        'filter',
        'lastUpdatedByUser',
        'filter.owner',
        'filter.lastUpdatedByUser',
      ], // Add relations to load them
    });

    // If the report does not exist, throw an error.
    if (!report) {
      throw new NotFoundException(`Report not found ${reportId}`);
    }

    if (report.owner.id !== userId) {
      throw new ForbiddenException(
        'Only the owner of the report can update it',
      );
    }

    return report;
  }

  async updateNonInflightReport(
    reportToUpdateFromDb: Report,
    reportUpdateRequest: Partial<ReportUpdateRequestDto>,
  ): Promise<Report> {
    const oldFilter = { ...reportToUpdateFromDb.filter };
    Object.assign(reportToUpdateFromDb, reportUpdateRequest);
    const newFilter = this.stringifyFilterMembers(
      reportUpdateRequest,
      reportToUpdateFromDb.reportType as ScoringReportType,
    );
    Object.assign(reportToUpdateFromDb.filter, newFilter);

    if (this.reportFilterIsChanged(oldFilter, newFilter)) {
      this.logger.log(
        `Updating report filter ${oldFilter.id} on report ${
          reportToUpdateFromDb.id
        } with data ${JSON.stringify(reportToUpdateFromDb.filter)}`,
      );
      reportToUpdateFromDb.filter.lastUpdated = new Date();
    }

    const reportAttributes = this.reportAttributesForNonInflightReport(
      reportUpdateRequest as Partial<ReportUpdateRequestDto>,
    );

    const report = V2OrV3ReportSchema.safeParse(reportUpdateRequest);
    const isValidV2Report = report.success;
    if (!isValidV2Report) {
      throw new BadRequestException(
        `Update to report ${reportToUpdateFromDb.id} is not a valid V2 report`,
      );
    }
    const platformAdAccountIds = reportUpdateRequest.filters
      ? await this.getPlatformAdAccountIdsForReport(report.data.filters)
      : undefined;

    const dataSource = this.reportsRepository.manager.connection;
    return await saveReport(
      reportToUpdateFromDb,
      {
        ...reportAttributes,
        platformAdAccountIds,
      },
      dataSource,
    );
  }

  async updateInflightReport(
    reportToUpdateFromDb: Report,
    reportUpdateRequest: Partial<InflightUpdateRequestDto>,
  ): Promise<Report> {
    Object.assign(reportToUpdateFromDb, reportUpdateRequest);
    const newFilter = this.stringifyFilterMembers(
      reportUpdateRequest,
      ScoringReportType.IN_FLIGHT,
    );
    Object.assign(reportToUpdateFromDb.filter, newFilter);

    if (this.reportFilterIsChanged(reportToUpdateFromDb.filter, newFilter)) {
      this.logger.log(
        `Updating report filter ${reportToUpdateFromDb.filter.id} on report ${
          reportToUpdateFromDb.id
        } with data ${JSON.stringify(reportToUpdateFromDb.filter)}`,
      );
      reportToUpdateFromDb.filter.lastUpdated = new Date();
    }

    const { channel, workspaceIds, platformAccountIds } = JSON.parse(
      reportToUpdateFromDb.filter.filters,
    );
    if (!channel || !workspaceIds?.length || !platformAccountIds?.length) {
      throw new BadRequestException(
        'Report filter must contain channel, workspaceIds, and platformAccountIds',
      );
    }

    const platformAdAccountIds = await getPlatformAdAccountIdsFromPlatformData(
      this.platformAdAccountRepository,
      platformAccountIds.map((id: string) => ({
        platformAccountId: id,
        channel,
      })),
    );

    const dataSource = this.reportsRepository.manager.connection;
    return await saveReport(
      reportToUpdateFromDb,
      {
        channels: [channel],
        workspaceIds,
        platformAdAccountIds,
      },
      dataSource,
    );
  }

  private reportAttributesForNonInflightReport(
    updateReportRequest: Partial<ReportUpdateRequestDto>,
  ): ReportAttributes {
    const channels = this.getChannelIdsFromMetadata(updateReportRequest);
    const workspaceIds = this.getWorkspaceIdsFromMetadata(updateReportRequest);
    const platformAccountIds =
      this.getPlatformAccountIdsFromV2Metadata(updateReportRequest);

    return { channels, workspaceIds, platformAccountIds };
  }

  private reportFilterIsChanged(
    oldFilter: Partial<ReportFilter>,
    newFilter: Partial<ReportFilter>,
  ) {
    const reportPropertiesToCheck: Array<keyof ReportFilter> = [
      'filters',
      'sortBy',
      'groupBy',
      'aggregationColumns',
      'filtersVersion',
      'filterWorkspaces',
    ];

    return reportPropertiesToCheck.some((property) => {
      return this.isNewValueDifferent(oldFilter[property], newFilter[property]);
    });
  }

  private isNewValueDifferent(
    oldValue: ReportFilter[keyof ReportFilter] | undefined,
    newValue: ReportFilter[keyof ReportFilter] | undefined,
  ) {
    return (
      newValue !== undefined &&
      JSON.stringify(newValue) !== JSON.stringify(oldValue)
    );
  }

  private stringifyFilterMembers(
    updateRequest:
      | Partial<ReportUpdateRequestDto>
      | Partial<InflightUpdateRequestDto>,
    reportType: ScoringReportType,
  ): Partial<ReportFilter> {
    const {
      filters,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore // analyticsFilters is nested in filters for inflight reports
      analyticsFilters,
      sortBy,
      groupBy,
      aggregationColumns,
      ...filterUpdate
    } = {
      ...updateRequest,
    };
    const reportFilterUpdate: Partial<ReportFilter> = filterUpdate;

    let combinedFilters = filters;
    if (Array.isArray(combinedFilters)) {
      combinedFilters = [
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore // analyticsFilters is nested in filters for inflight reports
        ...(filters || []),
        ...(analyticsFilters ? [{ analyticsFilters }] : []),
      ];
    }

    if (filters || analyticsFilters) {
      reportFilterUpdate.filters = JSON.stringify(combinedFilters);
    }
    if (sortBy) {
      reportFilterUpdate.sortBy = JSON.stringify(updateRequest.sortBy);
    }
    if (groupBy) {
      reportFilterUpdate.groupBy = JSON.stringify(updateRequest.groupBy);
    }
    if (aggregationColumns) {
      reportFilterUpdate.aggregationColumns = JSON.stringify(
        updateRequest.aggregationColumns,
      );
    }

    // updates workspaces only for v2 reports
    const reportWorkspaces =
      reportType === ScoringReportType.IN_FLIGHT
        ? (filters as Partial<InflightAggregateRequestDto>)?.workspaceIds
        : this.getWorkspaceIdsFromMetadata(
            updateRequest as ReportCreationRequestDto,
          );

    if (reportWorkspaces?.length) {
      reportFilterUpdate.filterWorkspaces = reportWorkspaces;
    }

    return reportFilterUpdate;
  }

  async getReportMetadata(
    reportId: string,
    organizationId?: string,
    shouldConvertV2ToV3Format = false,
  ): Promise<ReportMetadataDto> {
    const query = this.reportsRepository
      .createQueryBuilder('reports')
      .leftJoinAndSelect('reports.filter', 'filter')
      .leftJoinAndSelect('reports.partner', 'partner')
      .leftJoinAndSelect('reports.owner', 'owner')
      .leftJoinAndSelect(
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = partner.organizationId', // This will include either 1 or 0 personOrganizationMap array length, 0 means a deactivated user
      )
      .where('reports.id = :reportId', { reportId })
      .andWhere('reports.deleted = false');

    if (organizationId) {
      query.andWhere(
        new Brackets((qb) => {
          qb.where('reports.organizationId = :organizationId').orWhere(
            'partner.organizationId = :organizationId',
          );
        }),
        { organizationId },
      );
    }

    const report = await query.getOne();

    if (report == null) {
      throw new NotFoundException(`Report not found ${reportId}`);
    }

    const criteriaLastCreatedDate = await this.getCriteriaLastCreatedDate(
      report,
    );
    return this.reportConverter.convert(
      report,
      criteriaLastCreatedDate,
      shouldConvertV2ToV3Format,
    );
  }

  async updateInflightReportMetadataWithFilterIdAndNames(
    reportMetadataDto: ReportMetadataDto,
    userDetails: UserDetailsDto,
  ): Promise<InflightMetadataResponseDto> {
    const {
      analyticsFilters,
      mediaTypes,
      scoreResults,
      channel,
      mediaNameSearchText,
      creativeAdherence,
      criteriaIsOrganizationCriteria,
      criteriaIsOptional,
      startDate,
      endDate,
      includeStandardCriteria,
      workspaceIds,
      platformAccountIds,
    } =
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      reportMetadataDto.filters as InflightAggregateRequestDto;

    const mediaTypesIdAndName = mediaTypes?.map((mediaTypeId) => ({
      id: mediaTypeId,
      name: MEDIA_TYPES_ID_TO_NAME_MAP[mediaTypeId],
    }));
    const scoreResultsIdAndName = scoreResults?.map(
      (scoreResultId: InflightAggregateRequestDto.ScoreResultsEnum) => ({
        id: scoreResultId,
        name: SCORE_RESULT_ID_NAME_MAP[scoreResultId],
      }),
    );

    const [scopeAndCriteriaFilters, updatedAnalyticsFilters] =
      await Promise.all([
        this.getInflightReportScopeAndCriteriaFilterValues(
          reportMetadataDto.filters as unknown as InflightAggregateRequestDto,
          userDetails,
        ),
        this.getAnalyticsFiltersIdAndName(
          userDetails,
          {
            platform: channel.toLowerCase() as Platform,
            workspaceIds,
            adAccountIds: platformAccountIds,
            dateRange: { startDate, endDate },
          },
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          analyticsFilters,
        ),
      ]);

    return {
      ...reportMetadataDto,
      filters: {
        channel: {
          id: channel,
          name: CHANNEL_ID_TO_NAME_MAP[channel],
        },
        ...scopeAndCriteriaFilters,
        mediaTypes: mediaTypesIdAndName,
        scoreResults: scoreResultsIdAndName,
        mediaNameSearchText,
        creativeAdherence,
        criteriaIsOrganizationCriteria,
        criteriaIsOptional,
        startDate,
        endDate,
        includeStandardCriteria,
        analyticsFilters: analyticsFilters
          ? updatedAnalyticsFilters
          : undefined,
      },
    };
  }

  private async getAnalyticsFiltersIdAndName(
    userDetails: UserDetailsDto,
    partialGetAnalyticsFilterOptionsReq: Partial<GetAnalyticsReportFilterOptionsRequestDto>,
    analyticsFilters: Record<Platform, AdvancedFilterArg[]> | undefined,
  ): Promise<
    Record<Platform, InflightMetadataAnalyticsFilterDto[]> | undefined
  > {
    // inflight report is restricted to a single channel so just take the first
    const channel = Object.keys(analyticsFilters ?? {})[0] as Platform;
    if (
      !analyticsFilters ||
      !channel ||
      !Object.keys(analyticsFilters[channel]).length
    ) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      return {};
    }

    const channelFilterKeyedByType = analyticsFilters[channel].reduce(
      (acc, filter) => ({ ...acc, [filter.type]: filter.values }),
      {},
    );

    if (
      !partialGetAnalyticsFilterOptionsReq.dateRange?.startDate ||
      !partialGetAnalyticsFilterOptionsReq.dateRange?.endDate
    ) {
      partialGetAnalyticsFilterOptionsReq.dateRange = {
        startDate: '2017-01-01',
        endDate: new Date().toISOString().split('T')[0],
      };
    }

    const promises = Object.entries(channelFilterKeyedByType).map(
      ([type, values]: [
        AdvancedFilterEnum,
        FilterValues | AggregateFilterValues,
      ]) => {
        const requestFilterType =
          ADVANCED_FILTER_TYPE_TO_REQUEST_FILTER_TYPE_MAP[type];
        if (!requestFilterType) {
          return [type, values] as [AdvancedFilterEnum, AggregateFilterValues];
        }

        return this.getAdvancedFilterItemsForType(
          userDetails,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          {
            ...partialGetAnalyticsFilterOptionsReq,
            filterType: ADVANCED_FILTER_TYPE_TO_REQUEST_FILTER_TYPE_MAP[type],
          },
          values,
          type,
        );
      },
    );

    const result = await Promise.all(promises);
    const parsedFilters = result.reduce(
      (acc, [type, values]) => ({ ...acc, [type]: values }),
      {},
    );

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return { [channel]: parsedFilters };
  }

  private async getAdvancedFilterItemsForType(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequest: GetAnalyticsReportFilterOptionsRequestDto,
    filterItemIds: string[],
    type: AdvancedFilterEnum,
  ): Promise<[AdvancedFilterEnum, StringIdAndNameDto[]]> {
    let isDoneFetching = false;
    let offset = 0;
    const perPage = 500;
    const items: StringIdAndNameDto[] = [];

    while (!isDoneFetching) {
      // const nextPage: FilterItem[] | PaginatedResultArray<FilterItem> =
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const nextPage: {
        result: FilterItem[];
        pagination?: { totalSize: number };
      } = await this.analyticsReportsService.getAnalyticsReportFilterOptionsV2(
        userDetails,
        getAnalyticsReportFilterOptionsRequest,
        { offset, perPage },
      );
      let pageItems = nextPage.result;
      if (Array.isArray(nextPage.result[0]?.values)) {
        pageItems = nextPage.result.reduce(
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          (acc, highLevelItem) => [...acc, ...highLevelItem.values],
          [],
        );
      }

      items.push(
        ...(pageItems.filter(
          (item) =>
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            filterItemIds.includes(item.id) ||
            filterItemIds.includes(item.name),
        ) as StringIdAndNameDto[]),
      );
      offset += perPage;
      isDoneFetching =
        !nextPage.pagination || offset >= nextPage.pagination.totalSize;
    }
    return [type, items];
  }

  private async getInflightReportScopeAndCriteriaFilterValues(
    inflightReportFilters: InflightAggregateRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<{
    workspaces: NumberIdAndNameDto[];
    platformAccounts: StringIdAndNameDto[];
    brands?: StringIdAndNameDto[];
    markets?: StringIdAndNameDto[];
    criteria?: NumberIdAndNameDto[];
    criteriaGroups?: StringIdAndNameDto[];
  }> {
    const {
      platformAccountIds,
      workspaceIds,
      brandIds,
      markets: marketIds,
      criteriaGroupIds,
      criteriaIds,
    } = inflightReportFilters;

    let brands, markets, criteria, criteriaGroups;

    const [allWorkspaces, platformAdAccounts] = await Promise.all([
      this.workspaceService.getWorkspacesByOrganizationId(
        userDetails.organizationId,
        userDetails.userId,
        userDetails.authorization,
        { offset: 0, perPage: Number.MAX_SAFE_INTEGER },
        {},
      ),
      this.platformAdAccountRepository.findBy({
        platform_account_id: In(platformAccountIds ?? []),
      }),
    ]);

    const workspaces: NumberIdAndNameDto[] = allWorkspaces.items
      .filter((item) => workspaceIds.includes(item.id))
      .map((item) => ({ id: item.id, name: item.name }));
    const platformAccounts = platformAdAccounts.map((item) => ({
      id: item.platform_account_id,
      name: item.name,
    }));

    if (criteriaIds?.length) {
      criteria = await this.fetchCriteriaIdAndNamesForInflightMetadata(
        criteriaIds,
        workspaceIds,
      );
    }

    if (criteriaGroupIds?.length) {
      const allCriteriaGroups =
        await this.criteriaGroupService.getCriteriaGroups(
          userDetails.organizationId,
          // using just any workspace id since it does not filter the groups, only the criteriaGroup.criteriaDetails (i.e list of criteria in the group belong to the workspace) which is irrelevant here
          workspaceIds[0],
          { offset: 0, perPage: Number.MAX_SAFE_INTEGER },
          null,
          'false',
        );
      criteriaGroups = allCriteriaGroups.result
        .filter((cg: { id: string }) => criteriaGroupIds.includes(cg.id))
        .map((cg) => ({ id: cg.id, name: cg.name }));
    }

    if (brandIds?.length) {
      const allBrands = await this.brandServiceSDK.getBrandsAsPromise(
        userDetails.organizationId,
        undefined,
        0,
        Number.MAX_SAFE_INTEGER,
        undefined,
      );
      brands = allBrands.result
        .filter((item: { id: string }) => brandIds.includes(item.id))
        .map((item) => ({ id: item.id, name: item.name }));
    }

    if (marketIds?.length) {
      const allMarkets = await this.marketRepository.findBy({
        isoCode: In(marketIds),
      });
      markets = allMarkets.map((item) => ({ id: item.isoCode, ...item }));
    }

    return {
      workspaces,
      platformAccounts,
      brands,
      markets,
      criteria,
      criteriaGroups,
    };
  }

  async fetchCriteriaIdAndNamesForInflightMetadata(
    criteriaIds: number[],
    workspaceIds: number[],
  ): Promise<NumberIdAndNameDto[]> {
    let isDoneFetching = false;
    let offset = 0;
    const perPage = 50;
    const criteria: NumberIdAndNameDto[] = [];

    while (!isDoneFetching) {
      const singlePageOfCriteria =
        await this.scoringCriteriaService.getCriteriaForListOfWorkspacesAsPromise(
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore // workspace ids are actually numbers
          workspaceIds,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          undefined,
          offset,
          perPage,
          undefined,
        );
      criteria.push(
        ...singlePageOfCriteria.result
          .filter((criterion: { id: string }) =>
            criteriaIds.includes(parseInt(criterion.id)),
          )
          .map((criterion: { id: string; name: string }) => ({
            id: parseInt(criterion.id),
            name: criterion.name,
          })),
      );
      offset += perPage;
      isDoneFetching =
        singlePageOfCriteria.pagination.totalSize <= offset ||
        criteria.length === criteriaIds.length;
    }

    return criteria;
  }

  private async getCriteriaLastCreatedDate(report: Report) {
    if (criteriaBasedReports.has(report.reportType)) {
      const workspaceIds = report.filter.filterWorkspaces?.map((id) =>
        id.toString(),
      );
      if (workspaceIds && workspaceIds.length > 0) {
        const channels =
          report.channelMaps
            ?.map((channelMap) => channelMap.channel)
            .join(',') || undefined;
        const [
          sortOrder,
          identifier,
          sortBy,
          mediaTypes,
          isOptional,
          ownerIds,
          searchText,
          globalStatuses,
          categories,
          startDate,
          endDate,
          criteriaGroupIds,
          offset,
        ] = Array(13).fill(undefined);
        const perPage = Number.MAX_SAFE_INTEGER;
        //TODO this API should return a type ... also the positional args approach is a problem
        const criteria: { result?: { dateCreated: Date }[] } =
          await this.scoringCriteriaService.getCriteriaForListOfWorkspacesAsPromise(
            workspaceIds,
            sortOrder,
            identifier,
            sortBy,
            channels,
            mediaTypes,
            isOptional,
            ownerIds,
            searchText,
            globalStatuses,
            categories,
            startDate,
            endDate,
            criteriaGroupIds,
            offset,
            perPage,
          );
        return criteria.result?.reduce(
          (acc, curr) =>
            new Date(curr.dateCreated) > acc ? new Date(curr.dateCreated) : acc,
          new Date(0),
        );
      }
    }
  }

  async deleteReport(userId: number, reportId: string) {
    const report = await this.reportsRepository.findOne({
      where: { id: reportId, deleted: false },
      relations: ['owner'],
    });
    if (!report) {
      throw new NotFoundException(`Report not found ${reportId}`);
    }
    if (report.owner.id !== userId) {
      throw new ForbiddenException(
        'Only the owner of the report can delete it',
      );
    }
    report.deleted = true;
    await this.reportsRepository.save(report);
    return { deleted: true };
  }

  async getReports(
    getReportOptions: GetReportOptions,
  ): Promise<PaginatedResultArray<ReportMetadataListItemDto>> {
    const {
      workspaceId,
      paginationOptions,
      sortOrder,
      sortBy,
      searchTerm,
      filtersVersions,
      accessibleWorkspaceIds = [],
      customFilters = {},
    } = getReportOptions;

    const { createdBy, dateCreated, lastUpdated, channels, adAccounts } =
      customFilters;
    let types = customFilters.types;
    if (!types || types.length === 0) {
      types = Object.values(ScoringReportType);
    }
    const offset = paginationOptions.offset ?? 0;
    const perPage = paginationOptions.perPage ?? 20;

    const queryBuilder = this.reportsRepository
      .createQueryBuilder('reports')
      .leftJoinAndSelect('reports.filter', 'filter')
      .leftJoinAndSelect('reports.partner', 'partner')
      .leftJoinAndSelect('reports.owner', 'owner')
      .leftJoinAndSelect('reports.platformAdAccountMaps', 'rpam')
      .leftJoinAndSelect('reports.channelMaps', 'cm')
      .leftJoinAndSelect(
        'rpam.platformAdAccount',
        'platformAdAccount',
        'platformAdAccount.id = rpam.platform_ad_account_id',
      )
      .leftJoinAndSelect(
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = partner.organizationId', // This will include either 1 or 0 personOrganizationMap, 0 means a deactivated user
      )
      .where('reports.reportType IN (:...types)', { types })
      .andWhere('partner.id = :workspaceId', { workspaceId })
      .andWhere('reports.deleted = false')
      .andWhere('filter.filtersVersion in (:...filtersVersions)', {
        filtersVersions,
      });

    if (searchTerm) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('reports.name LIKE :searchTerm', {
            searchTerm: `%${searchTerm}%`,
          }).orWhere('reports.description LIKE :searchTerm', {
            searchTerm: `%${searchTerm}%`,
          });
        }),
      );
    }

    if (createdBy) {
      queryBuilder.andWhere('owner.id IN (:...createdBy)', { createdBy });
    }

    if (dateCreated) {
      queryBuilder.andWhere(
        'reports.dateCreated BETWEEN :dateCreatedStart AND :dateCreatedEnd',
        {
          dateCreatedStart: dateCreated.startDate,
          dateCreatedEnd: dateCreated.endDate,
        },
      );
    }

    if (lastUpdated) {
      queryBuilder.andWhere(
        'reports.lastUpdated BETWEEN :lastUpdatedStart AND :lastUpdatedEnd',
        {
          lastUpdatedStart: lastUpdated.startDate,
          lastUpdatedEnd: lastUpdated.endDate,
        },
      );
    }

    if (channels) {
      queryBuilder.andWhere(
        'reports.id IN (SELECT report_id FROM report_channel_map WHERE channel IN (:...channels))',
        { channels },
      );
    }

    if (adAccounts) {
      queryBuilder.andWhere(
        `platformAdAccount.platform_account_id IN (:...adAccounts)`,
        { adAccounts },
      );
    }

    // Sorting logic
    switch (sortBy) {
      case SortBy.CreatedBy:
        queryBuilder.orderBy('owner.displayName', sortOrder);
        break;
      case SortBy.ReportType:
        queryBuilder.orderBy('reports.reportType', sortOrder);
        break;
      case SortBy.Channel:
        queryBuilder.orderBy('cm.channel', sortOrder);
        break;
      case SortBy.AdAccount:
        queryBuilder.orderBy('platformAdAccount.name', sortOrder);
        break;
      default:
        queryBuilder.orderBy(`reports.${sortBy}`, sortOrder);
        break;
    }

    // Filtering by accessible workspaces for v2 reports.

    if (accessibleWorkspaceIds.length > 0) {
      queryBuilder.andWhere(
        new Brackets((qb) => {
          qb.where('filter.filterWorkspaces IS NULL').orWhere(
            `JSON_CONTAINS(JSON_ARRAY(:...accessibleWorkspaceIds), filter.filterWorkspaces) = 1`,
            { accessibleWorkspaceIds },
          );
        }),
      );
    }

    this.logger.debug(
      'Reports query - ' + queryBuilder.getQueryAndParameters(),
    );

    const [result, totalCount] = await queryBuilder
      .take(perPage)
      .skip(offset)
      .getManyAndCount();

    const reports = result.map((report: Report) =>
      this.reportConverter.convertItem(report),
    );

    return new PaginatedResultArray(reports, totalCount);
  }
}
