import { Sort } from './sort';
import { ReportFilterDtoV2 } from './report-filters.dto';
import { CreateReportGroupByDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { AggregationColumnDto } from './aggregation-column.dto';
import { Platform } from '../../constants/platform.constants';
import { AdvancedFilterArg } from '@vidmob/vidmob-nestjs-common';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';

export class BaseUpdateRequestDto {
  name: string;
  description: string;
  filtersVersion: number;
  sortBy: Sort;
  groupBy: CreateReportGroupByDto;
  aggregationColumns: AggregationColumnDto[];
}

export class ReportUpdateRequestDto extends BaseUpdateRequestDto {
  filters: ReportFilterDtoV2[];
  analyticsFilters?: Record<Platform, AdvancedFilterArg[]>;
}

export class InflightUpdateRequestDto extends BaseUpdateRequestDto {
  filters: InflightAggregateRequestDto;
}
