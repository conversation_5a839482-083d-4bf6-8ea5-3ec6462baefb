import {
  CreateReportGroupByDto,
  InflightAggregateRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ReportFilterDto } from './report-filters.dto';
import { ScoringReportType } from './report';
import { Sort } from './sort';
import { AggregationColumnDto } from './aggregation-column.dto';
import { DataGridStateDto } from './report-metadata.dto';

export class ReportCreationResponseDto {
  name: string;
  description: string;
  reportType: ScoringReportType;
  filtersVersion: number;
  filters: InflightAggregateRequestDto | ReportFilterDto[];
  sortBy: Sort;
  groupBy: CreateReportGroupByDto;
  aggregationColumns: AggregationColumnDto[];
  dataGridState?: DataGridStateDto;
}
