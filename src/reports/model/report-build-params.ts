import { ScoringReportType } from './report';
import { ReportFilterDto } from './report-filters.dto';
import { Sort } from './sort';
import { CreateReportGroupByDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { AggregationColumnDto } from './aggregation-column.dto';
import { Platform } from '../../constants/platform.constants';
import { AdvancedFilterArg } from '@vidmob/vidmob-nestjs-common';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';

export class ReportBuildParams {
  name: string | undefined;
  description: string | undefined;
  reportType: ScoringReportType;
  filtersVersion: number;
  filters: ReportFilterDto[] | InflightAggregateRequestDto;
  analyticsFilters: Record<Platform, AdvancedFilterArg[]>;
  sortBy: Sort;
  groupBy: CreateReportGroupByDto;
  workspaces: number[];
  aggregationColumns: AggregationColumnDto[];
  ownerId: number;
  workspaceId: number;
  adAccounts: string[];
  channels: string[];
}
