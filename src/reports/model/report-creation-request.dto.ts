import {
  ReportFilterBetweenDto,
  ReportFilterDto,
  ReportFilterDtoV2,
  ReportFilterEqualsDto,
  ReportFilterInDto,
} from './report-filters.dto';
import { ScoringReportType } from './report';
import { Sort } from './sort';
import { Type } from 'class-transformer';
import { ApiExtraModels, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { CreateReportGroupByDto } from './create-report-group-by.dto';
import { AdvancedFilterArg } from '@vidmob/vidmob-nestjs-common';
import { IsObject, IsOptional } from 'class-validator';
import { Platform } from '../../constants/platform.constants';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';

class BaseReportCreationRequestDto {
  /**
   * The workspace id
   * @example 1234
   */
  workspaceId: number;

  /**
   * Name of the report
   * @example "New report"
   */
  name: string;

  /**
   * Description of the report
   * @example "My personal new report"
   */
  description: string;

  /**
   * The type of the report
   * @example "ADOPTION"
   */
  reportType: ScoringReportType;

  /**
   * The filters' version
   * @example 1
   */
  filtersVersion: number;

  /**
   * The report's sortBy
   */
  @Type(() => Sort)
  sortBy: Sort;

  /**
   * The report's groupBy
   */
  @Type(() => CreateReportGroupByDto)
  groupBy: CreateReportGroupByDto;
}

export class InflightReportCreationRequestDto extends BaseReportCreationRequestDto {
  /**
   * Inflight report filters
   */
  @ApiProperty({
    type: Object,
    example: {
      channel: 'FACEBOOK',
      workspaceId: 1234,
      platformAccountIds: ['92iur8ui49rijjir3uir'],
      startDate: '2022-01-01',
      endDate: '2023-12-31',
    },
  })
  filters: InflightAggregateRequestDto;
}

@ApiExtraModels(
  ReportFilterInDto,
  ReportFilterBetweenDto,
  ReportFilterEqualsDto,
)
export class ReportCreationRequestDto extends BaseReportCreationRequestDto {
  /**
   * The report's filters
   */
  @ApiProperty({
    type: 'array',
    items: {
      oneOf: [
        { $ref: getSchemaPath(ReportFilterEqualsDto) },
        { $ref: getSchemaPath(ReportFilterInDto) },
        { $ref: getSchemaPath(ReportFilterBetweenDto) },
      ],
    },
    example: [
      {
        fieldName: 'market',
        operator: 'in',
        value: ['United States'],
      },
      {
        fieldName: 'workspace',
        operator: 'in',
        value: [123, 456, 789, 12],
      },
      {
        fieldName: 'dateRange',
        operator: 'between',
        value: ['2020-01-01', '2020-01-31'],
      },
      {
        fieldName: 'batchType',
        operator: 'equals',
        value: 'IN_FLIGHT',
      },
      {
        fieldName: 'channel',
        operator: 'in',
        value: ['FACEBOOK', 'PINTEREST'],
      },
    ],
  })
  @Type(() => ReportFilterDto, {
    discriminator: {
      property: 'operator',
      subTypes: [
        { value: ReportFilterInDto, name: 'in' },
        { value: ReportFilterBetweenDto, name: 'between' },
        { value: ReportFilterEqualsDto, name: 'equals' },
      ],
    },
  })
  filters: ReportFilterDtoV2[];

  @IsOptional()
  @IsObject()
  analyticsFilters?: Record<Platform, AdvancedFilterArg[]>;
}
