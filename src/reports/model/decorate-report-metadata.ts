import { ReportMetadataDto } from './report-metadata.dto';
import { ScoringReportType } from './report';
import { ReportFilterDto, ReportFilterOperator } from './report-filters.dto';
import { ApplicabilityMediaTypesEnum } from '../../scoring/scoring-constants';

export default function decorateReportMetadata(
  reportMetadata: ReportMetadataDto,
): ReportMetadataDto {
  const presentFilterNames = reportMetadata.filters?.map(
    (filter) => filter.fieldName,
  );

  let defaultFilters: ReportFilterDto[] = [];
  if (reportMetadata.reportType === ScoringReportType.ADOPTION) {
    defaultFilters = adoptionDefaultFilters();
  } else if (reportMetadata.reportType === ScoringReportType.ADHERENCE) {
    defaultFilters = adherenceDefaultFilters();
  } else if (reportMetadata.reportType === ScoringReportType.IMPRESSION_ADHERENCE) {
    defaultFilters = impressionDefaultFilters();
  } else if (reportMetadata.reportType === ScoringReportType.DIVERSITY) {
    defaultFilters = diversityDefaultFilters();
  }

  const missingFilters = defaultFilters.filter(
    (filter) => !presentFilterNames.includes(filter.fieldName),
  );
  reportMetadata.filters.push(...missingFilters);

  return reportMetadata;
}

const defaultCriteriaSetIdFilter: ReportFilterDto = {
  fieldName: 'criteriaSetId',
  operator: ReportFilterOperator.In,
  value: ['*'],
};

const defaultCriteriaIsOptionalFilter: ReportFilterDto = {
  fieldName: 'criteriaIsOptional',
  operator: ReportFilterOperator.Equals,
  value: false,
};

const defaultCreativeTypeFilter: ReportFilterDto = {
  fieldName: 'creativeType',
  operator: ReportFilterOperator.In,
  value: [
    ApplicabilityMediaTypesEnum.VIDEO,
    ApplicabilityMediaTypesEnum.IMAGE,
    ApplicabilityMediaTypesEnum.HTML,
    ApplicabilityMediaTypesEnum.ANIMATED_IMAGE,
  ],
};

const adoptionDefaultFilters = (): ReportFilterDto[] => {
  return [defaultCriteriaSetIdFilter];
};

const adherenceDefaultFilters = (): ReportFilterDto[] => {
  return [defaultCriteriaSetIdFilter, defaultCriteriaIsOptionalFilter];
};

const diversityDefaultFilters = (): ReportFilterDto[] => {
  return [defaultCreativeTypeFilter];
};

const impressionDefaultFilters = adherenceDefaultFilters;
