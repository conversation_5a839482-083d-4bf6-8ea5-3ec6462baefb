import {
  CreateReportGroupByDto,
  InflightAggregateRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { Sort } from './sort';
import { ReportFilterDto } from './report-filters.dto';
import { AggregationColumnDto } from './aggregation-column.dto';
import { DataGridStateDto } from './report-metadata.dto';

export enum ScoringReportType {
  ADHERENCE = 'ADHERENCE',
  IMPRESSION_ADHERENCE = 'IMPRESSION_ADHERENCE',
  ADOPTION = 'ADOPTION',
  IN_FLIGHT = 'IN_FLIGHT',
  DIVERSITY = 'DIVERSITY',
}

export abstract class Report {
  getReportName() {
    return `Untitled ${this.getType()} Report`;
  }

  abstract getType(): ScoringReportType;

  abstract generateFilter(): ReportFilterDto[];

  abstract generateSort(): Sort;

  abstract generateGroupBy(): CreateReportGroupByDto;

  abstract generateGroupByV2(): CreateReportGroupByDto;

  abstract generateGroupByV3(): CreateReportGroupByDto;

  abstract getFiltersVersionV2(): number;

  abstract getFiltersVersionV3(): number;

  abstract generateFilterV2(): InflightAggregateRequestDto | ReportFilterDto[];

  abstract generateAggregationColumns():
    | AggregationColumnDto[]
    | Promise<AggregationColumnDto[]>;

  abstract generateDataGridState(): DataGridStateDto;

  abstract getFilterVersion(): number;

  getStartOfCurrentMonth = () => {
    const today = new Date(); // get current date
    return new Date(today.getFullYear(), today.getMonth(), 1);
  };

  subtractMonths = (date: Date, months: number) => {
    const newDate = new Date(date.getTime());
    newDate.setMonth(newDate.getMonth() - months);
    return newDate;
  };
}
