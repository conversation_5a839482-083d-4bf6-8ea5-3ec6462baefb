import { ScoringReportType } from './report';
import { ReportMetadataOwnerDto } from './report-metadata-owner.dto';
import AdAccountDto from './report-metadata-adaccount.dto';

export class ReportMetadataListItemDto {
  id: string;
  name: string;
  description: string;
  owner: ReportMetadataOwnerDto;
  shared: boolean;
  reportType: ScoringReportType;
  dateCreated: Date;
  lastUpdated: Date;
  isLegacy: boolean;
  adAccounts: AdAccountDto[];
  channels: string[];
}
