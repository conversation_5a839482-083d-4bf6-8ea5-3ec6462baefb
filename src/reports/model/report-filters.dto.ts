// This is a copy of the definition of report filters (scoring and analytics)
// we probably want to move this to the common library.
import {
  ArrayMaxSize,
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsNumber,
  IsString,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum ReportFilterOperator {
  Equals = 'equals',
  Between = 'between',
  In = 'in',
  Norms = 'norms',
  LessThan = 'less than',
  GreaterThan = 'greater than',
  After = 'after',
  Before = 'before',
}

export class ReportFilterInDto implements ReportFilterDto {
  @IsString()
  fieldName: string;

  @AutoMap()
  @ApiProperty({
    enum: [ReportFilterOperator.In],
  })
  operator: ReportFilterOperator.In;

  @IsArray()
  @ApiProperty({
    oneOf: [
      { type: 'array', items: { type: 'string' } },
      { type: 'array', items: { type: 'number' } },
    ],
  })
  value: string[] | number[];
}

export class ReportFilterBetweenDto implements ReportFilterDto {
  @IsString()
  fieldName: string;

  @AutoMap()
  @ApiProperty({
    enum: [ReportFilterOperator.Between],
  })
  operator: ReportFilterOperator.Between;

  @ArrayMinSize(2)
  @ArrayMaxSize(2)
  @ApiProperty({
    oneOf: [
      { type: 'array', items: { type: 'string' } },
      { type: 'array', items: { type: 'number' } },
    ],
  })
  value: [string, string] | [number, number];
}

export class ReportFilterEqualsDto implements ReportFilterDto {
  @IsString()
  fieldName: string;

  @AutoMap()
  @ApiProperty({
    enum: [ReportFilterOperator.Equals],
  })
  operator: ReportFilterOperator.Equals;

  @ValidateIf((obj, value) => value !== undefined)
  @ValidateNested()
  @Type(() => IsNumberOrStringOrBool)
  @ApiProperty({
    oneOf: [{ type: 'string' }, { type: 'number' }, { type: 'boolean' }],
  })
  value: string | number | boolean;
}

export class ReportFilterDto {
  @IsString()
  /**
   * The name of the field to filter on.
   */
  fieldName: string;

  @AutoMap()
  /**
   * The operator to use for the filter (equals, between, in)
   */
  operator: ReportFilterOperator;

  @AutoMap()
  /**
   * The value to filter on that corresponds with the operator.
   */
  value:
    | string
    | number
    | [string, string]
    | [number, number]
    | string[]
    | number[]
    | boolean
    | null;
}

/**
 * Represents the filter structure for version 2 reports, which has a distinct shape compared to other versions.
 *
 * Example:
 * {
 *   key: 'workspaceId',
 *   operator: 'in',
 *   value: [
 *     { id: 1, name: 'Workspace 1' },
 *     { id: 2, name: 'Workspace 2' }
 *   ]
 * }
 */

export type ReportFilterValue =
  | Array<{ id: string | number; name: string }>
  | string[]
  | [string, string]
  | [number, number]
  | string;

export class ReportFilterDtoV2 {
  @IsString()
  key: string;

  @AutoMap()
  operator: ReportFilterOperator;

  @AutoMap()
  value: ReportFilterValue;
  // - Filters like workspaceId, channel, kpi, brand, market, assetSource use id/name arrays
  // - Static and relative date filters use [string, string]
  // - adherenceRange uses [number, number]
}

class IsNumberOrStringOrBool {
  @IsString()
  stringValue?: string;

  @IsNumber()
  numberValue?: number;

  @IsBoolean()
  booleanValue?: boolean;
}
