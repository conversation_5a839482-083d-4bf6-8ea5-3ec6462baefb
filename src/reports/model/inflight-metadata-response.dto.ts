import { BaseReportMetadataDto } from './report-metadata.dto';
import { Platform } from '../../constants/platform.constants';
import {
  AdvancedFilterEnum,
  AggregateFilterValues,
} from '@vidmob/vidmob-nestjs-common/dist/analytics-queries/types/request-response-types';

export class StringIdAndNameDto {
  id: string;
  name: string;
}

export class NumberIdAndNameDto {
  id: number;
  name: string;
}

class InFlightCreativeAdherenceDto {
  lessThan?: number;
  greaterThan?: number;
}

export class InflightMetadataAnalyticsFilterDto {
  type: AdvancedFilterEnum;
  values: StringIdAndNameDto[] | AggregateFilterValues;
}

export class InflightMetadataFiltersDto {
  /**
   * The report's channel
   */
  channel: StringIdAndNameDto;

  /**
   * The report's selected workspaces
   */
  workspaces: NumberIdAndNameDto[];

  /**
   * The report's selected platform accounts for scope
   */
  platformAccounts: StringIdAndNameDto[];

  /**
   * The report's selected brands for scope
   */
  brands?: StringIdAndNameDto[];

  /**
   * The report's selected markets for scope
   */
  markets?: StringIdAndNameDto[];

  /**
   * The report's start date
   */
  startDate: string;

  /**
   * The report's end date
   */
  endDate: string;

  /**
   * The report's selected criteria groups
   */
  criteriaGroups?: StringIdAndNameDto[];

  /**
   * The report's selected criteria
   */
  criteria?: NumberIdAndNameDto[];

  /**
   * The media types selected in report
   */
  mediaTypes?: StringIdAndNameDto[];

  /**
   * The report's selected scoring reports
   */
  scoreResults?: StringIdAndNameDto[];

  /**
   * The report's creative adherence
   */
  creativeAdherence?: InFlightCreativeAdherenceDto;

  /**
   * If the report is limited to organization criteria
   */
  criteriaIsOrganizationCriteria?: boolean;

  /**
   * If the report is limited to optional or mandatory criteria
   */
  criteriaIsOptional?: boolean;

  /**
   * If the report includes standard Vidmob criteria
   */
  includeStandardCriteria?: boolean;

  /**
   * The media name search text in report
   */
  mediaNameSearchText?: string;

  /**
   * Analytics filters applied in the report
   */
  analyticsFilters?: Record<Platform, InflightMetadataAnalyticsFilterDto[]>;
}

export class InflightMetadataResponseDto extends BaseReportMetadataDto {
  /**
   * The report's filters
   */
  filters: InflightMetadataFiltersDto;
}
