import { ScoringReportType } from './report';
import { Sort } from './sort';
import { ReportFilterDto } from './report-filters.dto';
import { CreateReportGroupByDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { AggregationColumnDto } from './aggregation-column.dto';
import { ReportMetadataOwnerDto } from './report-metadata-owner.dto';
import { Platform } from '../../constants/platform.constants';
import { AdvancedFilterArg } from '@vidmob/vidmob-nestjs-common';

export type DataGridState = 'EXPANDED' | 'COLLAPSED';

export class DataGridStateDto {
  rows: DataGridState;
  columns: DataGridState;
}

export class BaseReportMetadataDto {
  /**
   * The report id (uuid)
   * @example "e35377f7-9a37-4527-9f92-7f76b1d7d81a"
   */
  id: string;

  /**
   * The report name
   * @example "My report"
   */
  name: string;

  /**
   * The report description
   * @example "My personal report"
   */
  description: string;

  /**
   * The report owner
   * @example { id: 12345, username: "<EMAIL>", firstName: "John", lastName: "Doe"}
   */
  owner: ReportMetadataOwnerDto;

  /**
   * whether the report is shared or not
   * @example true
   */
  shared: boolean;

  /**
   * The report's type
   * @example "ADOPTION"
   */
  reportType: ScoringReportType;

  /**
   * The report's creation date
   */
  dateCreated: Date;

  /**
   * The report's update date
   */
  lastUpdated: Date;

  /**
   * The date of the most recently added criteria included in the report
   */
  criteriaLastCreatedDate?: Date;

  /**
   * The filters version
   */
  filtersVersion: number;

  /**
   * The report's sortBy
   */
  sortBy: Sort;

  /**
   * The report's groupBy
   */
  groupBy: CreateReportGroupByDto;

  /**
   * The report's aggregationColumns
   */
  aggregationColumns: AggregationColumnDto[];

  /**
   * Whether the report is expanded or collapsed on the FE
   */
  dataGridState?: DataGridStateDto;
}

export class ReportMetadataDto extends BaseReportMetadataDto {
  /**
   * The report's filters
   */
  filters: ReportFilterDto[];

  analyticsFilters?: Record<Platform, AdvancedFilterArg[]>;
}
