import { ScoringReportType } from '../report';
import { ScoringReport } from './scoring-report';
import { CreateReportGroupByDto } from '../create-report-group-by.dto';
import { AggregationColumnDto } from '../aggregation-column.dto';
import { ReportFilterDto } from '../report-filters.dto';
import { DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS } from '../../constants/constants';
import { DataGridStateDto } from '../report-metadata.dto';

const OMITTED_FILTERS = ['batchType', 'creativeType'];

export class ImpressionAdherence extends ScoringReport {
  getType(): ScoringReportType {
    return ScoringReportType.IMPRESSION_ADHERENCE;
  }

  generateFilter(): ReportFilterDto[] {
    const defaultFilters = super.generateFilter();
    return defaultFilters.filter(
      (filter) => !OMITTED_FILTERS.includes(filter.fieldName),
    );
  }

  generateFilterV2(): ReportFilterDto[] {
    return [];
  }

  generateGroupBy(): CreateReportGroupByDto {
    return {
      columns: ['criteria', 'batchType'],
      rows: ['market', 'workspace'],
    };
  }

  generateGroupByV2(): CreateReportGroupByDto {
    return {
      columns: ['criteria', 'batchType'],
      rows: ['workspace'],
    };
  }

  generateGroupByV3(): CreateReportGroupByDto {
    return {
      columns: ['workspace'],
      rows: DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS,
    };
  }

  generateAggregationColumns(): AggregationColumnDto[] {
    return [
      {
        group: 'Impressions',
        name: 'Count',
        type: 'totalSum',
        matchers: {},
      },
      {
        group: 'Impressions',
        name: 'Avg Adherence',
        type: 'sum',
        matchers: {},
      },
    ];
  }

  getFilterVersion(): number {
    return 1;
  }

  getFiltersVersionV2(): number {
    return 2;
  }

  getFiltersVersionV3(): number {
    return 3;
  }

  generateDataGridState(): DataGridStateDto {
    return {
      rows: 'COLLAPSED',
      columns: 'COLLAPSED',
    };
  }
}
