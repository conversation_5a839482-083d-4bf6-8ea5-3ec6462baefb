import { ScoringReportType } from '../report';
import { ScoringReport } from './scoring-report';
import { AggregationColumnDto } from '../aggregation-column.dto';
import { CreateReportGroupByDto } from '../create-report-group-by.dto';
import { ReportFilterDto } from '../report-filters.dto';
import { DataGridStateDto } from '../report-metadata.dto';

const OMITTED_FILTER_FIELDS: string[] = ['criteriaIsOptional', 'creativeType'];

export class Adoption extends ScoringReport {
  getType(): ScoringReportType {
    return ScoringReportType.ADOPTION;
  }

  generateFilter(): ReportFilterDto[] {
    const defaultFilters = super.generateFilter();
    return defaultFilters.filter(
      (filter) => !OMITTED_FILTER_FIELDS.includes(filter.fieldName),
    );
  }

  generateFilterV2(): ReportFilterDto[] {
    return [];
  }

  generateGroupBy(): CreateReportGroupByDto {
    return {
      columns: ['month', 'batchType'],
      rows: ['market', 'workspace'],
    };
  }

  generateGroupByV2(): CreateReportGroupByDto {
    return {
      columns: ['month', 'batchType'],
      rows: ['workspace'],
    };
  }

  generateGroupByV3(): CreateReportGroupByDto {
    return {
      columns: ['month', 'batchType'],
      rows: ['workspace'],
    };
  }

  generateAggregationColumns(): AggregationColumnDto[] {
    return [
      {
        group: 'Total',
        name: 'Total',
        type: 'sum',
        matchers: {},
      },
      {
        group: 'Total',
        name: 'In-flight',
        type: 'sum',
        matchers: {
          batchType: 'IN_FLIGHT',
        },
      },
      {
        group: 'Total',
        name: 'Pre-flight',
        type: 'sum',
        matchers: {
          batchType: 'PRE_FLIGHT',
        },
      },
    ];
  }

  getFilterVersion(): number {
    return 1;
  }

  getFiltersVersionV2(): number {
    return 2;
  }

  getFiltersVersionV3(): number {
    return 3;
  }

  generateDataGridState(): DataGridStateDto {
    return {
      rows: 'COLLAPSED',
      columns: 'COLLAPSED',
    };
  }
}
