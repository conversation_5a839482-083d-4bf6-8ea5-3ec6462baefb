import { ScoringReportType } from '../report';
import { ScoringReport } from './scoring-report';
import { AggregationColumnDto } from '../aggregation-column.dto';
import { CreateReportGroupByDto } from '../create-report-group-by.dto';
import { ReportFilterDto } from '../report-filters.dto';
import { DataGridStateDto } from '../report-metadata.dto';

const OMITTED_FILTER_FIELDS: string[] = ['criteriaIsOptional', 'criteriaSetId'];

export class Diversity extends ScoringReport {
  getType(): ScoringReportType {
    return ScoringReportType.DIVERSITY;
  }

  generateFilter(): ReportFilterDto[] {
    const defaultFilters = super.generateFilter();
    return defaultFilters.filter(
      (filter) => !OMITTED_FILTER_FIELDS.includes(filter.fieldName),
    );
  }

  generateFilterV2(): ReportFilterDto[] {
    return [];
  }

  getFilterVersion(): number {
    return 1;
  }

  getFiltersVersionV2(): number {
    return 2;
  }

  getFiltersVersionV3(): number {
    return 3;
  }

  generateAggregationColumns():
    | AggregationColumnDto[]
    | Promise<AggregationColumnDto[]> {
    return [];
  }

  generateGroupBy(): CreateReportGroupByDto {
    return { columns: [], rows: [] };
  }

  generateGroupByV2(): CreateReportGroupByDto {
    return { columns: [], rows: [] };
  }

  generateGroupByV3(): CreateReportGroupByDto {
    return { columns: [], rows: [] };
  }

  generateDataGridState(): DataGridStateDto {
    return {
      rows: 'COLLAPSED',
      columns: 'COLLAPSED',
    };
  }
}
