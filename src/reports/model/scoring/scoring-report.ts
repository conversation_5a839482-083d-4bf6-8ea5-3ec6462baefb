import { Report, ScoringReportType } from '../report';
import { ReportFilterDto, ReportFilterOperator } from '../report-filters.dto';
import { ScoringReportsService } from '../../../scoring/reports/scoring-reports.service';
import { Sort } from '../sort';
import { SortOrder } from '../sort-order';
import {
  ScoringReportOptionsDto,
  UserInfoDto,
} from '../../../scoring/reports/dtos/report-options.dto';
import { UnauthorizedException } from '@nestjs/common';

const DEFAULT_REPORTING_PERIOD_MONTHS = 3;

export abstract class ScoringReport extends Report {
  protected options: ScoringReportOptionsDto;
  private channels: string[];

  constructor(private readonly scoringReportsService: ScoringReportsService) {
    super();
  }

  public async setUserInfo(userInfo: UserInfoDto): Promise<void> {
    this.options = await this.getReportOptions(userInfo);

    // channel internationalization and icons are still handled in ACS.
    this.channels = await this.getChannelOptions();
  }

  private getChannelOptions = async () => {
    return await this.scoringReportsService.getChannelIdentifiers();
  };

  getReportName() {
    return '';
  }

  abstract getType(): ScoringReportType;

  async getReportOptions(
    userInfo: UserInfoDto,
  ): Promise<ScoringReportOptionsDto> {
    const { userId, workspaceId } = userInfo;
    const accessiblePartners =
      await this.scoringReportsService.getRelatedAccessiblePartners(
        userId,
        workspaceId,
      );

    if (!accessiblePartners.length) {
      throw new UnauthorizedException(
        `User ${userId} doesn't have permissions for workspaces in this organization.`,
      );
    }

    // grab criteria sets and pass them down
    return await this.scoringReportsService.getReportOptions(
      accessiblePartners,
    );
  }

  generateFilter(): ReportFilterDto[] {
    const endDate = this.getStartOfCurrentMonth(); // end date is exclusive
    const startDate = this.subtractMonths(
      endDate,
      DEFAULT_REPORTING_PERIOD_MONTHS,
    );

    // Format dates to ISO string
    const formattedEndDate = endDate.toISOString();
    const formattedStartDate = startDate.toISOString();

    return [
      {
        fieldName: 'mediaCreateDate',
        operator: ReportFilterOperator.Between,
        value: [formattedStartDate, formattedEndDate],
      },
      {
        fieldName: 'batchType',
        operator: ReportFilterOperator.Equals,
        // these should be available from the scoring service but apparently are not yet
        value: '*',
      },
      {
        fieldName: 'channel',
        operator: ReportFilterOperator.In,
        value: this.channels,
      },
      {
        fieldName: 'criteriaSetId',
        operator: ReportFilterOperator.In,
        value: this.options.criteriaSets.map(
          (criteriaSet) => criteriaSet.value,
        ),
      },
      {
        fieldName: 'market',
        operator: ReportFilterOperator.In,
        value: this.options.markets.map((market) => market.value),
      },
      {
        fieldName: 'workspaceId',
        operator: ReportFilterOperator.In,
        value: this.options.workspaces.map((workspace) => workspace.value),
      },
      {
        fieldName: 'brandId',
        operator: ReportFilterOperator.In,
        value: this.options.brands.map((brand) => brand.value),
      },
      {
        fieldName: 'criteriaIsOptional',
        operator: ReportFilterOperator.Equals,
        value: false,
      },
      {
        fieldName: 'creativeType',
        operator: ReportFilterOperator.In,
        value: this.options.creativeType.map(
          (creativeType) => creativeType.value,
        ),
      },
    ];
  }

  generateSort(): Sort {
    return { sortBy: 'name', sortOrder: SortOrder.DESC };
  }
}
