import { ScoringReport } from './scoring-report';
import { ScoringReportType } from '../report';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ReportFilterDto } from '../report-filters.dto';
import { AggregationColumnDto } from '../aggregation-column.dto';
import { CreateReportGroupByDto } from '../create-report-group-by.dto';
import { Sort } from '../sort';
import { DataGridStateDto } from '../report-metadata.dto';

export class Inflight extends ScoringReport {
  getType(): ScoringReportType {
    return ScoringReportType.IN_FLIGHT;
  }

  generateFilter(): ReportFilterDto[] {
    return super.generateFilter();
  }

  generateFilterV2(): InflightAggregateRequestDto {
    // use ts-ignore to avoid adding a new type where these values can be null
    return {
      /* eslint-disable @typescript-eslint/ban-ts-comment */
      // @ts-ignore
      channel: null,
      workspaceIds: [],
      // @ts-ignore
      startDate: null,
      // @ts-ignore
      endDate: null,
    };
  }

  generateGroupBy(): CreateReportGroupByDto {
    return {
      columns: [],
      rows: [],
    };
  }

  generateGroupByV2(): CreateReportGroupByDto {
    return {
      columns: [],
      rows: [],
    };
  }

  generateGroupByV3(): CreateReportGroupByDto {
    return {
      columns: [],
      rows: [],
    };
  }

  generateSort(): Sort {
    /* eslint-disable @typescript-eslint/ban-ts-comment */
    // @ts-ignore // in flight sorting is tab dependent, allow FE to fall back on default sorting
    return { sortBy: null, sortOrder: null };
  }

  async generateAggregationColumns(): Promise<AggregationColumnDto[]> {
    return [];
  }

  getFilterVersion(): number {
    return 1;
  }

  getFiltersVersionV2(): number {
    return 2;
  }

  getFiltersVersionV3(): number {
    return 3;
  }

  generateDataGridState(): DataGridStateDto {
    return {
      rows: 'COLLAPSED',
      columns: 'COLLAPSED',
    };
  }
}
