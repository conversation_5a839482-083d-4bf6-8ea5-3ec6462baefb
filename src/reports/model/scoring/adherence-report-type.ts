import { ScoringReportType } from '../report';
import { ScoringReport } from './scoring-report';
import { CreateReportGroupByDto } from '../create-report-group-by.dto';
import { AggregationColumnDto } from '../aggregation-column.dto';
import { ReportFilterDto } from '../report-filters.dto';
import { DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS } from '../../constants/constants';
import { DataGridStateDto } from '../report-metadata.dto';

const OMITTED_FILTER_FIELDS: string[] = ['creativeType'];

export class Adherence extends ScoringReport {
  getType(): ScoringReportType {
    return ScoringReportType.ADHERENCE;
  }

  generateFilter(): ReportFilterDto[] {
    const defaultFilters = super.generateFilter();
    return defaultFilters.filter(
      (filter) => !OMITTED_FILTER_FIELDS.includes(filter.fieldName),
    );
  }

  generateFilterV2(): ReportFilterDto[] {
    return [];
  }

  generateGroupBy(): CreateReportGroupByDto {
    return {
      columns: ['criteria', 'batchType'],
      rows: ['market', 'workspace'],
    };
  }

  generateGroupByV2(): CreateReportGroupByDto {
    return {
      columns: ['criteria', 'batchType'],
      rows: ['workspace'],
    };
  }

  generateGroupByV3(): CreateReportGroupByDto {
    return {
      columns: ['workspace'],
      rows: DEFAULT_ADHERENCE_REPORT_GROUP_BY_ROWS,
    };
  }

  async generateAggregationColumns(): Promise<AggregationColumnDto[]> {
    return [
      {
        group: 'Total',
        name: 'Total',
        type: 'sum',
        matchers: {},
      },
      {
        group: 'Total',
        name: 'Pre-flight',
        type: 'sum',
        matchers: {
          batchType: 'PRE_FLIGHT',
        },
      },
      {
        group: 'Total',
        name: 'In-flight',
        type: 'sum',
        matchers: {
          batchType: 'IN_FLIGHT',
        },
      },
    ];
  }

  getFilterVersion(): number {
    return 1;
  }

  getFiltersVersionV2(): number {
    return 2;
  }

  getFiltersVersionV3(): number {
    return 3;
  }

  generateDataGridState(): DataGridStateDto {
    return {
      rows: 'COLLAPSED',
      columns: 'COLLAPSED',
    };
  }
}
