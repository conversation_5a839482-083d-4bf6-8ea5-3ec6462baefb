import { SortOrder } from './sort-order';
import { IsNotEmpty, IsString, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';

export class Sort {
  /*
   * The column to sort by
   */
  @IsString()
  @IsNotEmpty()
  sortBy: string;

  /*
   * The sort order ASC or DESC
   */
  @IsString()
  @Transform(({ value }) => value.toUpperCase())
  @IsEnum(SortOrder)
  sortOrder: SortOrder;
}
