import {
  <PERSON><PERSON>rray,
  Is<PERSON>num,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>Optional,
  IsString,
} from 'class-validator';
import { ScoringReportType } from './report';
import { DateRange } from 'src/analytics/dto/date-range.dto';
import { Type } from 'class-transformer';
import { Platform } from '@vidmob/vidmob-nestjs-common';

export class GetScoringReportBodyDto {
  @IsOptional()
  @IsArray()
  types?: ScoringReportType[];

  @IsOptional()
  @Type(() => DateRange)
  dateCreated?: DateRange;

  @IsOptional()
  @Type(() => DateRange)
  lastUpdated?: DateRange;

  @IsOptional()
  @IsNumber({}, { each: true })
  createdBy?: number[];

  @IsOptional()
  @IsArray()
  @IsEnum(Platform, { each: true })
  channels?: Platform[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  adAccounts?: string[];
}
