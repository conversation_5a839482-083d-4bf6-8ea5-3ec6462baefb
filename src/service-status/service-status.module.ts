import { DynamicModule, InjectionToken, Module } from '@nestjs/common';
import { ServiceStatusService } from './service-status.service';
import { ServiceStatusController } from './service-status.controller';

const SERVICE_NAMES = 'SERVICE_NAMES';

@Module({})
export class ServiceStatusModule {
  static forRoot(config: {
    services: { [key: string]: InjectionToken };
  }): DynamicModule {
    // Must unpack the services into keys (names) and values (service provider classes).
    // This allows them to be injected into the ServiceStatusService and
    // utilize NestJS dependency injection for the actual services.
    const serviceEntries = Object.entries(config.services);
    const serviceNames = serviceEntries.map(([key]) => key);
    const serviceProviders = serviceEntries.map(([, value]) => value);
    return {
      module: ServiceStatusModule,
      controllers: [ServiceStatusController],
      providers: [
        {
          provide: SERVICE_NAMES,
          useValue: serviceNames,
        },
        {
          provide: ServiceStatusService,
          useFactory: (serviceNames: string[], ...services: any[]) =>
            new ServiceStatusService(serviceNames, services),
          inject: [SERVICE_NAMES, ...serviceProviders],
        },
      ],
    };
  }
}
