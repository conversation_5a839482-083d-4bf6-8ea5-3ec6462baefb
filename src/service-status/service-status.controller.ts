import { Controller, Get, VERSION_NEUTRAL } from '@nestjs/common';
import { Public } from '../auth/decorators/public.decorator';
import { ServiceStatusService } from './service-status.service';
import { ApiTags } from '@nestjs/swagger';

@Public()
@ApiTags('Service Status (Public)')
@Controller({
  path: 'service-status',
  version: VERSION_NEUTRAL,
})
export class ServiceStatusController {
  constructor(private readonly serviceStatusService: ServiceStatusService) {}

  @Get()
  getStatus() {
    return this.serviceStatusService.getStatus();
  }
}
