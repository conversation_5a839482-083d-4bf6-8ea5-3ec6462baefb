import { of, throwError } from 'rxjs';
import { ServiceStatusService } from './service-status.service';
import { AxiosError, AxiosResponse } from 'axios';

// create mock AxiosResponse
const mockAxiosResponse = {
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {},
  data: {
    status: 'OK',
    result: {},
  },
};

// create mock AxiosResponse as error
const mockAxiosErrorResponse = {
  status: 500,
  statusText: 'Internal Server Error',
  headers: {},
  config: {},
  data: {
    status: 'ERROR',
    result: {},
  },
} as AxiosResponse<any, any>;

// create mock AxiosError
const mockAxiosError = new AxiosError('Connection refused', 'ECONNREFUSED');

const mockAxiosErrorWithResponse = new AxiosError(
  undefined,
  undefined,
  undefined,
  undefined,
  mockAxiosErrorResponse,
);

const mockAuthorizationDefaultService = {
  check: jest.fn(),
};

describe('ServiceStatusService', () => {
  let service: ServiceStatusService;

  beforeEach(async () => {
    service = new ServiceStatusService(
      ['authorization'],
      [mockAuthorizationDefaultService],
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should merge services', () => {
    const serviceNames = ['authorization'];
    const serviceList = [mockAuthorizationDefaultService];
    const expected = {
      authorization: mockAuthorizationDefaultService,
    };
    expect(service.mergeServices(serviceNames, serviceList)).toMatchObject(
      expected,
    );
  });

  it('should fail on incorrect services', () => {
    const serviceNames = ['authorization'];
    const serviceList: any[] = [];
    expect(() => {
      service.mergeServices(serviceNames, serviceList);
    }).toThrowError();
  });

  it('should call health check on get status', async () => {
    mockAuthorizationDefaultService.check = jest.fn(() => {
      return of(mockAxiosResponse);
    });
    const result = await service.getStatus();
    expect(
      mockAuthorizationDefaultService.check,
    ).toHaveBeenCalled();
    expect(result).toMatchObject({
      services: [
        {
          name: 'authorization',
          responseCode: 200,
          response: {
            status: 'OK',
            result: {},
          },
        },
      ],
    });
  });

  it('get status should handle axios error', async () => {
    // override mockAuthorizationDefaultService with a mock that throws an error
    mockAuthorizationDefaultService.check = jest.fn(() => {
      // return observable that throws an error
      return throwError(() => mockAxiosError);
    });
    const result = await service.getStatus();
    expect(result).toMatchObject({
      services: [
        {
          name: 'authorization',
          error: {
            message: mockAxiosError.message,
            code: mockAxiosError.code,
          },
        },
      ],
    });
  });

  it('get status should handle error response', async () => {
    mockAuthorizationDefaultService.check = jest.fn(() => {
      return throwError(() => mockAxiosErrorWithResponse);
    });
    const result = await service.getStatus();
    expect(result).toMatchObject({
      services: [
        {
          name: 'authorization',
          responseCode: mockAxiosErrorResponse.status,
          response: mockAxiosErrorResponse.data,
        },
      ],
    });
  });

  it('should throw error on unknown error', async () => {
    mockAuthorizationDefaultService.check = jest.fn(() => {
      return throwError(() => new Error('Unknown error'));
    });
    await expect(service.getStatus()).rejects.toThrowError();
  });
});
