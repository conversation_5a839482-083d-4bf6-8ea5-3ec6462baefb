import { Injectable, Logger } from '@nestjs/common';
import {
  ServiceStatus,
  ServiceStatusResponse,
} from './dto/service-status-response.dto';
import {
  Observable,
  catchError,
  firstValueFrom,
  forkJoin,
  map,
  of,
} from 'rxjs';
import { AxiosError, AxiosResponse } from 'axios';

@Injectable()
export class ServiceStatusService {
  private readonly services: Record<string, any> = {};

  /**
   * @param serviceNames List of the names of the services in serviceList
   * @param serviceList List of the service instances
   */
  constructor(readonly serviceNames: string[], readonly serviceList: any[]) {
    this.services = this.mergeServices(serviceNames, serviceList);
  }

  mergeServices(
    serviceNames: string[],
    serviceList: any[],
  ): Record<string, any> {
    if (serviceNames.length !== serviceList.length) {
      throw new Error(
        'ServiceStatusService incorrectly initialized, service names and list must be the same length',
      );
    }
    const services: Record<string, any> = {};
    serviceNames.forEach((name, index) => {
      services[name] = serviceList[index];
    });
    return services;
  }

  async getStatus(): Promise<ServiceStatusResponse> {
    const results = Object.entries(this.services).map(([name, service]) => {
      return this.callServiceHealthCheck(name, service);
    });

    const services = await firstValueFrom(forkJoin(results));

    return { services };
  }

  callServiceHealthCheck(
    name: string,
    service: any,
  ): Observable<ServiceStatus> {
    if (typeof service.check === 'function') {
      const observable = service.check() as Observable<AxiosResponse<any, any>>;
      return this.getServiceStatus(name, observable);
    } else {
      return this.getInvalidServiceStatus(name);
    }
  }

  getServiceStatus(
    name: string,
    observable: Observable<AxiosResponse<any, any>>,
  ): Observable<ServiceStatus> {
    return observable.pipe(
      map((response) => {
        return this.getAxiosResponse(name, response);
      }),
      catchError((err) => {
        if (err instanceof AxiosError) {
          const axiosError = err as AxiosError;
          const serviceStatus = this.getAxiosError(name, axiosError);
          return of(serviceStatus);
        }
        throw err;
      }),
    );
  }

  getInvalidServiceStatus(name: string): Observable<ServiceStatus> {
    return of({
      name,
      error: {
        message: 'Invalid service, no health check defined',
      },
    } as ServiceStatus);
  }

  getAxiosResponse(name: string, response: AxiosResponse): ServiceStatus {
    return {
      name,
      responseCode: response.status,
      response: response.data,
    } as ServiceStatus;
  }

  getAxiosUnknownError(name: string, axiosError: AxiosError): ServiceStatus {
    return {
      name,
      error: {
        code: axiosError.code,
        message: axiosError.message,
      },
    } as ServiceStatus;
  }

  getAxiosError(name: string, axiosError: AxiosError): ServiceStatus {
    if (axiosError.response) {
      return this.getAxiosResponse(name, axiosError.response);
    } else {
      return this.getAxiosUnknownError(name, axiosError);
    }
  }
}
