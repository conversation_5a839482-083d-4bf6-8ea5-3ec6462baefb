import { Test, TestingModule } from '@nestjs/testing';
import { ServiceStatusController } from './service-status.controller';
import { ServiceStatusService } from './service-status.service';

describe('ServiceStatusController', () => {
  let controller: ServiceStatusController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ServiceStatusController],
      providers: [
        {
          provide: ServiceStatusService,
          useValue: new ServiceStatusService([], []),
        },
      ],
    }).compile();

    controller = module.get<ServiceStatusController>(ServiceStatusController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
