import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ConnectorsDTO } from './dto/getDataExportsConnectorsResponse.dto';
import { generateMockConnectors } from './mock-connectors';
import { paginateMockData } from '../../utils/paginate-mock-data';
import { CreateConnectorRequestDto } from './dto/createConnectorRequest.dto';
import { CreateConnectorResponseDto } from './dto/createConnectorResponse.dto';
import { SearchOptions } from './dto/searchOptions.dto';
import { SortOptions } from './dto/sortOptions.dto';
import { searchMockData } from '../../utils/search-mock-data';
import { sortMockData } from '../../utils/sort-mock-data';
import { CreateDataExportConnectorDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportConnectorDto';
import { DataExportConnectorService } from '@vidmob/vidmob-organization-service-sdk/dist/api/dataExportConnector.service';

@Injectable()
export class ConnectorsService {
  constructor(private readonly dataConnectorSDK: DataExportConnectorService) {}
  // TODO: Replace with something that isn't a mock when the BE is in place
  /**
   * Gets list of connectors for a given organization (mock for now)
   * @param organizationId
   * @param paginationOptions
   * @param searchOptions
   * @param sortOptions
   */
  async getDataExportsConnectorsForOrg(
    organizationId: string,
    paginationOptions: PaginationOptions,
    searchOptions: SearchOptions,
    sortOptions: SortOptions,
  ): Promise<PaginatedResultArray<ConnectorsDTO>> {
    // Step 1: Generate mock connectors
    const mockConnectors = generateMockConnectors();

    const searchedConnectors = searchMockData(
      mockConnectors,
      searchOptions.search,
      ['connectorName'],
    );

    const sortedConnectors = sortMockData(
      searchedConnectors,
      sortOptions.sortBy,
      sortOptions.sortOrder,
    );

    return paginateMockData(sortedConnectors, paginationOptions);
  }

  // TODO: Replace with something that isn't a mock when the BE is in place
  /**
   * Create a new Connector
   * @param userId
   * @param organizationId
   * @param dto
   */
  async createConnector(
    userId: number,
    organizationId: string,
    dto: CreateDataExportConnectorDto,
  ) {
    return await this.dataConnectorSDK.createNewDataConnectorForOrganizationAsPromise(
      userId,
      organizationId,
      dto,
    );
  }
}
