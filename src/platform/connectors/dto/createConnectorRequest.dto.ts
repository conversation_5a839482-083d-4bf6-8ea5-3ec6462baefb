import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString, IsOptional } from 'class-validator';
import { Expose } from 'class-transformer';

export class CreateConnectorRequestDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @Expose()
  @ApiProperty({
    description: 'The name of the connector',
    example: 'Amazon S3 Connector',
  })
  connectorName: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @Expose()
  @ApiProperty({
    description: 'The path to the connector resource',
    example: '/path/to/resource',
  })
  path: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @Expose()
  @ApiProperty({
    description: 'The name of the S3 bucket where data is stored',
    example: 'my-s3-bucket',
  })
  s3BucketName: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  @Expose()
  @ApiProperty({
    description: 'The AWS role ARN for user authentication',
    example: 'arn:aws:iam::123456789012:role/S3AccessRole',
  })
  userRoleARN: string;
}
