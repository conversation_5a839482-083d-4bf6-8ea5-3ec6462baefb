import { IdAndNameDto } from '../../data-exports/dto/idAndName.dto';
import { AutoMap } from '@automapper/classes';
import { IsDateString, IsObject, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class Person extends IdAndNameDto {
  photoUrl: string;
}

export class ConnectorsDTO {
  @AutoMap()
  @IsString()
  connectorId: string;

  @AutoMap()
  @IsString()
  connectorName: string;

  @AutoMap()
  @IsString()
  path: string;

  @AutoMap()
  @IsString()
  s3BucketName: string;

  @AutoMap()
  @IsDateString()
  createdOn: string;

  @AutoMap()
  @IsObject()
  @Type(() => Person)
  createdBy: Person;
}
