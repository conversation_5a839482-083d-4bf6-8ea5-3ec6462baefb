import dayjs from 'dayjs';
import {
  ConnectorsDTO,
  Person,
} from './dto/getDataExportsConnectorsResponse.dto';

const mockPersons: Person[] = [
  {
    id: 'person-001',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/john-doe.jpg',
  },
  {
    id: 'person-002',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/jane-smith.jpg',
  },
  {
    id: 'person-003',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/robert-brown.jpg',
  },
  {
    id: 'person-004',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/emily-davis.jpg',
  },
  {
    id: 'person-005',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/michael-taylor.jpg',
  },
  {
    id: 'person-006',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/sarah-wilson.jpg',
  },
  {
    id: 'person-007',
    name: '<PERSON>',
    photoUrl: 'https://example.com/photos/daniel-martinez.jpg',
  },
  {
    id: 'person-008',
    name: '<PERSON> <PERSON>',
    photoUrl: 'https://example.com/photos/laura-clark.jpg',
  },
];

const mockDates: string[] = [];
for (let i = 0; i < 10; i++) {
  const randomDate = dayjs()
    .subtract(Math.floor(Math.random() * 30), 'days')
    .toISOString();
  mockDates.push(randomDate);
}

const paths = [
  '/s3/connectors/data/001',
  '/s3/connectors/data/002',
  '/s3/connectors/data/003',
  '/s3/connectors/data/004',
  '/s3/connectors/data/005',
  '/s3/connectors/data/006',
];

const s3BucketNames = [
  'bucket-1',
  'bucket-2',
  'bucket-3',
  'bucket-4',
  'bucket-5',
  'bucket-6',
  'bucket-7',
  'bucket-8',
  'bucket-9',
];

export const generateMockConnectors = () => {
  const mockConnectors: ConnectorsDTO[] = [];
  for (let i = 0; i < 47; i++) {
    const randomPerson =
      mockPersons[Math.floor(Math.random() * mockPersons.length)];
    const randomDate = mockDates[Math.floor(Math.random() * mockDates.length)];
    const randomPath = paths[Math.floor(Math.random() * paths.length)];
    const randomBucket =
      s3BucketNames[Math.floor(Math.random() * s3BucketNames.length)];

    const connector: ConnectorsDTO = {
      connectorId: `connector-${i + 1}`,
      connectorName: `Connector ${i + 1}`,
      path: randomPath,
      s3BucketName: randomBucket,
      createdOn: randomDate,
      createdBy: randomPerson,
    };

    mockConnectors.push(connector);
  }

  return mockConnectors;
};
