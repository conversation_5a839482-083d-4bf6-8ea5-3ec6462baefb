import { Test, TestingModule } from '@nestjs/testing';
import { ApiKeyManagementController } from './api-key-management.controller';
import { ApiKeyManagementService } from './api-key-management.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  UpdateApiTokenRequestDto,
  GetOrganizationApiKeys200Response,
} from '@vidmob/vidmob-authorization-service-sdk';
import { CreateApiKeyResponseDto } from './dto/create-api-key-response.dto';
import { DeleteApiKeyResponseDto } from './dto/delete-api-key-response.dto';
import { ApiKeyScope, ApiKeyScopePermission } from './types/api-key-scope';
import { CreateApiKeyRequestDto } from './dto/create-api-key-request.dto';

describe('ApiKeyManagementController', () => {
  let controller: ApiKeyManagementController;
  let service: ApiKeyManagementService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ApiKeyManagementController],
      providers: [
        {
          provide: ApiKeyManagementService,
          useValue: {
            getAllApiKeysForOrg: jest.fn(),
            createOrganizationApiKey: jest.fn(),
            updateOrganizationApiKey: jest.fn(),
            deleteOrganizationApiKey: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ApiKeyManagementController>(
      ApiKeyManagementController,
    );
    service = module.get<ApiKeyManagementService>(ApiKeyManagementService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getAllApiKeysForOrg', () => {
    it('should return an array of API keys', async () => {
      const orgId = 'test-org';
      const req = {
        userId: '12345',
      };
      const paginationOptions: PaginationOptions = {
        offset: 0,
        perPage: 10,
        queryId: 'test-query',
      };
      const apiKeysResponse: GetOrganizationApiKeys200Response = {
        status: 'success',
        result: [
          {
            id: '1',
            name: 'API Key 1',
            organizationId: orgId,
            expirationDate: '2025-01-01T00:00:00Z',
            createdBy: { id: 1, displayName: 'User 1' },
            updatedBy: { id: 2, displayName: 'User 2' },
            lastUsedDate: '2024-01-01T00:00:00Z',
            dateCreated: '2023-01-01T00:00:00Z',
            lastUpdated: '2023-01-01T00:00:00Z',
            scopes: [
              {
                scope: ApiKeyScope.SCORING,
                permission: ApiKeyScopePermission.READ,
              },
            ],
          },
        ],
      };

      jest
        .spyOn(service, 'getAllApiKeysForOrg')
        .mockResolvedValue(apiKeysResponse);

      const result = await controller.getAllApiKeysForOrg(
        orgId,
        paginationOptions,
        req,
      );
      expect(result).toEqual(apiKeysResponse);
    });
  });

  describe('createOrganizationApiKey', () => {
    it('should create and return an API key', async () => {
      const orgId = 'test-org';
      const req = {
        userId: '12345',
      };
      const createApiKeyRequestDto: CreateApiKeyRequestDto = {
        name: 'New API Key',
        scopes: [
          {
            scope: ApiKeyScope.SCORING,
            permission: ApiKeyScopePermission.READ,
          },
        ],
      };
      const createApiKeyResponse: CreateApiKeyResponseDto = {
        status: 'success',
        result: {
          id: '1',
          name: 'New API Key',
          organizationId: orgId,
          expirationDate: '2025-01-01T00:00:00Z',
          apiKey: 'abc123',
          createdBy: { id: 1, displayName: 'User 1' },
          updatedBy: { id: 2, displayName: 'User 2' },
          lastUsedDate: '2024-01-01T00:00:00Z',
          dateCreated: '2023-01-01T00:00:00Z',
          lastUpdated: '2023-01-01T00:00:00Z',
          scopes: [
            {
              scope: ApiKeyScope.SCORING,
              permission: ApiKeyScopePermission.READ,
            },
          ],
        },
      };

      jest
        .spyOn(service, 'createOrganizationApiKey')
        .mockResolvedValue(createApiKeyResponse);

      const result = await controller.createOrganizationApiKey(
        orgId,
        createApiKeyRequestDto,
        req,
      );
      expect(result).toEqual(createApiKeyResponse);
    });
  });

  describe('updateOrganizationApiKey', () => {
    it('should update and return an API key', async () => {
      const orgId = 'test-org';
      const apiKeyId = '1';
      const req = {
        userId: '12345',
      };
      const updateApiTokenRequestDto: UpdateApiTokenRequestDto = {
        name: 'Updated API Key',
        scopes: [
          {
            scope: ApiKeyScope.SCORING,
            permission: ApiKeyScopePermission.READ,
          },
        ],
      };
      const updateApiKeyResponse = {
        status: 'success',
        result: {
          id: '1',
          name: 'Updated API Key',
          organizationId: orgId,
          expirationDate: '2025-01-01T00:00:00Z',
          createdBy: { id: 1, displayName: 'User 1' },
          updatedBy: { id: 2, displayName: 'User 2' },
          lastUsedDate: '2024-01-01T00:00:00Z',
          dateCreated: '2023-01-01T00:00:00Z',
          lastUpdated: '2023-01-01T00:00:00Z',
          scopes: [
            {
              scope: ApiKeyScope.SCORING,
              permission: ApiKeyScopePermission.READ,
            },
          ],
        },
      };

      jest
        .spyOn(service, 'updateOrganizationApiKey')
        .mockResolvedValue(updateApiKeyResponse);

      const result = await controller.updateOrganizationApiKey(
        orgId,
        apiKeyId,
        updateApiTokenRequestDto,
        req,
      );
      expect(result).toEqual(updateApiKeyResponse);
    });
  });

  describe('deleteOrganizationApiKey', () => {
    it('should delete an API key', async () => {
      const orgId = 'test-org';
      const apiKeyId = '1';
      const req = {
        userId: '12345',
      };
      const deleteApiKeyResponse: DeleteApiKeyResponseDto = {
        status: 'success',
        result: {},
      };

      jest
        .spyOn(service, 'deleteOrganizationApiKey')
        .mockResolvedValue(deleteApiKeyResponse);

      const result = await controller.deleteOrganizationApiKey(
        orgId,
        apiKeyId,
        req,
      );
      expect(result).toEqual(deleteApiKeyResponse);
    });
  });
});
