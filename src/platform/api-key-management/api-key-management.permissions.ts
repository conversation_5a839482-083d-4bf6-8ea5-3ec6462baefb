import { PermissionSubResource } from '../../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../../auth/enums/permission.domain.enum';
import { PermissionAction } from '../../auth/enums/permission.action.enum';
import { organizationFromParamsHandler } from '../../auth/decorators/permission.decorator';

export const createApiKeyPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.API_KEY,
    },
  ],
};

export const readApiKeysPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.API_KEY,
    },
  ],
};

export const updateApiKeyPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.API_KEY,
    },
  ],
};

export const deleteApiKeyPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.API_KEY,
    },
  ],
};
