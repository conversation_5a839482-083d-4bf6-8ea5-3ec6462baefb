import { Test, TestingModule } from '@nestjs/testing';
import { ApiKeyManagementService } from './api-key-management.service';
import { ApiKeyService } from '@vidmob/vidmob-authorization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ApiKeyScope, ApiKeyScopePermission } from './types/api-key-scope';

jest.mock('@vidmob/vidmob-authorization-service-sdk');

describe('ApiKeyManagementService', () => {
  let service: ApiKeyManagementService;
  let apiKeyService: jest.Mocked<ApiKeyService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApiKeyManagementService,
        {
          provide: ApiKeyService,
          useValue: {
            getOrganizationApiKeysAsPromise: jest.fn(),
            createOrganizationApiKeyAsPromise: jest.fn(),
            updateOrganizationApiKeyAsPromise: jest.fn(),
            deleteOrganizationApiKeyAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ApiKeyManagementService>(ApiKeyManagementService);
    apiKeyService = module.get(ApiKeyService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAllApiKeysForOrg', () => {
    it('should return API keys', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const paginationOptions: PaginationOptions = {
        offset: 0,
        perPage: 10,
        queryId: 'test-query',
      };
      const apiKeysResponse = {
        status: 'success',
        result: [
          {
            id: '1',
            name: 'API Key 1',
            organizationId: orgId,
            expirationDate: '2025-01-01T00:00:00Z',
            createdBy: { id: 1, displayName: 'User 1' },
            updatedBy: { id: 2, displayName: 'User 2' },
            lastUsedDate: '2024-01-01T00:00:00Z',
            dateCreated: '2023-01-01T00:00:00Z',
            lastUpdated: '2023-01-01T00:00:00Z',
            scopes: [
              {
                scope: ApiKeyScope.SCORING,
                permission: ApiKeyScopePermission.READ,
              },
            ],
          },
        ],
      };

      apiKeyService.getOrganizationApiKeysAsPromise.mockResolvedValue(
        apiKeysResponse,
      );

      const result = await service.getAllApiKeysForOrg(
        userId,
        orgId,
        paginationOptions,
      );
      expect(result).toEqual({
        status: 'success',
        result: apiKeysResponse.result.map((apiKey) => ({
          ...apiKey,
          scopes: apiKey.scopes.map((scope) => ({
            ...scope,
            scope: scope.scope as any,
            permission: scope.permission as any,
          })),
        })),
      });
    });

    it('should throw an error if the API call fails', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const paginationOptions: PaginationOptions = {
        offset: 0,
        perPage: 10,
        queryId: 'test-query',
      };

      apiKeyService.getOrganizationApiKeysAsPromise.mockRejectedValue(
        new Error('API error'),
      );

      await expect(
        service.getAllApiKeysForOrg(userId, orgId, paginationOptions),
      ).rejects.toThrow(Error);
    });
  });

  describe('createOrganizationApiKey', () => {
    it('should create and return an API key', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const createApiTokenRequestDto = {
        name: 'New API Key',
        scopes: [
          {
            scope: ApiKeyScope.SCORING,
            permission: ApiKeyScopePermission.READ,
          },
        ],
      };
      const createApiKeyResponse = {
        status: 'success',
        result: {
          id: '1',
          name: 'New API Key',
          organizationId: orgId,
          expirationDate: '2025-01-01T00:00:00Z',
          apiKey: 'abc123',
          createdBy: { id: 1, displayName: 'User 1' },
          updatedBy: { id: 2, displayName: 'User 2' },
          lastUsedDate: '2024-01-01T00:00:00Z',
          dateCreated: '2023-01-01T00:00:00Z',
          lastUpdated: '2023-01-01T00:00:00Z',
          scopes: [
            {
              scope: ApiKeyScope.SCORING,
              permission: ApiKeyScopePermission.READ,
            },
          ],
        },
      };

      apiKeyService.createOrganizationApiKeyAsPromise.mockResolvedValue(
        createApiKeyResponse,
      );

      const result = await service.createOrganizationApiKey(
        userId,
        orgId,
        createApiTokenRequestDto,
      );
      expect(result).toEqual({
        status: 'success',
        result: {
          ...createApiKeyResponse.result,
          scopes: createApiKeyResponse.result.scopes.map((scope) => ({
            ...scope,
            scope: scope.scope as any,
            permission: scope.permission as any,
          })),
        },
      });
    });

    it('should throw an error if the API call fails', async () => {
      const orgId = 'test-org';
      const userId = 12345;
      const createApiTokenRequestDto = {
        name: 'New API Key',
        scopes: [
          {
            scope: ApiKeyScope.SCORING,
            permission: ApiKeyScopePermission.READ,
          },
        ],
      };

      apiKeyService.createOrganizationApiKeyAsPromise.mockRejectedValue(
        new Error('API error'),
      );

      await expect(
        service.createOrganizationApiKey(
          userId,
          orgId,
          createApiTokenRequestDto,
        ),
      ).rejects.toThrow(Error);
    });
  });

  describe('updateOrganizationApiKey', () => {
    it('should update and return an API key', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const apiKeyId = '1';
      const updateApiTokenRequestDto = {
        name: 'Updated API Key',
        scopes: [
          {
            scope: ApiKeyScope.SCORING,
            permission: ApiKeyScopePermission.READ,
          },
        ],
      };
      const updateApiKeyResponse = {
        status: 'success',
        result: {
          id: '1',
          name: 'Updated API Key',
          organizationId: orgId,
          expirationDate: '2025-01-01T00:00:00Z',
          createdBy: { id: 1, displayName: 'User 1' },
          updatedBy: { id: 2, displayName: 'User 2' },
          lastUsedDate: '2024-01-01T00:00:00Z',
          dateCreated: '2023-01-01T00:00:00Z',
          lastUpdated: '2023-01-01T00:00:00Z',
          scopes: [
            {
              scope: ApiKeyScope.SCORING,
              permission: ApiKeyScopePermission.READ,
            },
          ],
        },
      };

      apiKeyService.updateOrganizationApiKeyAsPromise.mockResolvedValue(
        updateApiKeyResponse,
      );

      const result = await service.updateOrganizationApiKey(
        userId,
        orgId,
        apiKeyId,
        updateApiTokenRequestDto,
      );
      expect(result).toEqual({
        status: 'success',
        result: {
          ...updateApiKeyResponse.result,
          scopes: updateApiKeyResponse.result.scopes.map((scope) => ({
            ...scope,
            scope: scope.scope as any,
            permission: scope.permission as any,
          })),
        },
      });
    });

    it('should throw an error if the API call fails', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const apiKeyId = '1';
      const updateApiTokenRequestDto = {
        name: 'Updated API Key',
        scopes: [
          {
            scope: ApiKeyScope.SCORING,
            permission: ApiKeyScopePermission.READ,
          },
        ],
      };

      apiKeyService.updateOrganizationApiKeyAsPromise.mockRejectedValue(
        new Error('API error'),
      );

      await expect(
        service.updateOrganizationApiKey(
          userId,
          orgId,
          apiKeyId,
          updateApiTokenRequestDto,
        ),
      ).rejects.toThrow(Error);
    });
  });

  describe('deleteOrganizationApiKey', () => {
    it('should delete an API key', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const apiKeyId = '1';
      const deleteApiKeyResponse = { status: 'success', result: {} };

      apiKeyService.deleteOrganizationApiKeyAsPromise.mockResolvedValue(
        deleteApiKeyResponse,
      );

      const result = await service.deleteOrganizationApiKey(
        userId,
        orgId,
        apiKeyId,
      );
      expect(result).toEqual(deleteApiKeyResponse);
    });

    it('should throw an error if the API call fails', async () => {
      const userId = 12345;
      const orgId = 'test-org';
      const apiKeyId = '1';

      apiKeyService.deleteOrganizationApiKeyAsPromise.mockRejectedValue(
        new Error('API error'),
      );

      await expect(
        service.deleteOrganizationApiKey(userId, orgId, apiKeyId),
      ).rejects.toThrow(Error);
    });
  });
});
