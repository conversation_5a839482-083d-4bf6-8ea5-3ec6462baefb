import { Is<PERSON><PERSON>, IsNotEmpty } from 'class-validator';
import { AutoMap } from '@automapper/classes';
import {
  ApiKeyScopePermission,
  ApiKeyScope,
  ApiKeyScopeInterface,
} from '../types/api-key-scope';
import { ApiProperty } from '@nestjs/swagger';

export class A<PERSON><PERSON><PERSON>ScopeDto implements ApiKeyScopeInterface {
  @AutoMap()
  @IsNotEmpty()
  @IsEnum(ApiKeyScope)
  @ApiProperty({
    description: 'Scope of the API Key',
    enum: ApiKeyScope,
  })
  scope: ApiKeyScope;

  @AutoMap()
  @IsNotEmpty()
  @IsEnum(ApiKeyScopePermission)
  @ApiProperty({
    description: 'Permission within the API Key scope',
    enum: ApiKeyScopePermission,
  })
  permission: ApiKeyScopePermission;
}
