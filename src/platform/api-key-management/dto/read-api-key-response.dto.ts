import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { ApiKeyScopeDto } from './api-key-scope.dto';
import { ApiKeyUserDto } from './api-key-user.dto';
import { BaseResponseDto } from './base-response.dto';

export class ReadApiKeyResultDto {
  @AutoMap()
  @ApiProperty({
    description: 'Id of the API key',
  })
  id: string;

  @ApiProperty({
    description: 'Name of the API key',
  })
  @AutoMap()
  name: string;

  @ApiProperty({
    description: 'Organization id of the API key',
  })
  @AutoMap()
  organizationId: string;

  @ApiProperty({
    description: 'Expiration date of the API key',
    type: String,
  })
  @AutoMap()
  expirationDate: string;

  @ApiProperty({
    description: 'User that created the API key',
  })
  @AutoMap()
  createdBy: ApiKeyUserDto;

  @ApiProperty({
    description: 'User who last updated the API key',
  })
  @AutoMap()
  updatedBy: ApiKeyUserDto;

  @ApiPropertyOptional({
    description: 'Date the API key was last used',
    type: String,
  })
  @AutoMap()
  lastUsedDate?: string;

  @ApiProperty({
    description: 'Date the API key was created',
    type: String,
  })
  @AutoMap()
  dateCreated: string;

  @ApiProperty({
    description: 'Date the API key was last updated',
    type: String,
  })
  @AutoMap()
  lastUpdated: string;

  @ApiProperty({
    description: 'List of scopes for the API key',
    type: [ApiKeyScopeDto],
  })
  @AutoMap()
  scopes: ApiKeyScopeDto[];
}

export class ReadApiKeyResponseDto extends BaseResponseDto<ReadApiKeyResultDto> {}
