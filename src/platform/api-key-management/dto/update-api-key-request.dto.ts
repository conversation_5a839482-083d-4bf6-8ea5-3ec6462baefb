import { AutoMap } from '@automapper/classes';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiKeyScopeDto } from './api-key-scope.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class UpdateApiKeyRequestDto {
  @AutoMap()
  @IsString()
  @IsOptional()
  @ApiProperty({
    description: 'Name of the API key',
  })
  name?: string;

  @AutoMap(() => ApiKeyScopeDto)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApiKeyScopeDto)
  @IsOptional()
  @ApiProperty({ type: [ApiKeyScopeDto] })
  scopes?: ApiKeyScopeDto[];

  @AutoMap()
  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({
    description: 'User id for the user updating the token',
  })
  updatedByUserId: number;
}
