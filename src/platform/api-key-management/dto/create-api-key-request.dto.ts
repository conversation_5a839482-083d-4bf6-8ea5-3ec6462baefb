import { AutoMap } from '@automapper/classes';
import {
  <PERSON>Array,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { ApiKeyScopeDto } from './api-key-scope.dto';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateApiKeyRequestDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: 'Scoring read/write',
    description: 'Name of the API key',
  })
  name: string;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  @ApiProperty({
    example: '1716468511962',
    description: 'Expiration date of the API key',
    type: String,
    required: false,
  })
  expirationDate?: string;

  @AutoMap(() => ApiKeyScopeDto)
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ApiKeyScopeDto)
  @ApiProperty({ type: [ApiKeyScopeDto] })
  scopes: Api<PERSON>eyScopeDto[];
}
