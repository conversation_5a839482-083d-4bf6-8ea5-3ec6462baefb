import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty } from 'class-validator';

export class ApiKeyUserDto {
  @AutoMap()
  @IsNotEmpty()
  @ApiProperty({
    example: '12345',
    description: 'The user id of the user associated with the API key',
  })
  id: number;

  @AutoMap()
  @IsNotEmpty()
  @ApiProperty({
    example: 'Barbara Exampleperson',
    description: 'The name of the user associated with the API key',
  })
  displayName: string;
}
