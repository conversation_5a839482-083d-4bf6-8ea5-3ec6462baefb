import { Injectable } from '@nestjs/common';
import {
  ApiKeyService,
  GetOrganizationApiKeys200Response,
  CreateApiTokenRequestDto,
  CreateOrganizationApiKey201Response,
  UpdateApiTokenRequestDto,
  ReadApiTokenResponseDto,
  DeleteOrganizationApiKey200Response,
} from '@vidmob/vidmob-authorization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

interface GetOrganizationApiKey200Response {
  status: string;
  result: ReadApiTokenResponseDto;
}

@Injectable()
export class ApiKeyManagementService {
  constructor(private readonly authorizationService: ApiKeyService) {}

  async getAllApiKeysForOrg(
    userId: number,
    organizationId: string,
    paginationOptions: PaginationOptions,
  ): Promise<GetOrganizationApiKeys200Response> {
    const { offset, perPage, queryId } = paginationOptions;

    return await this.authorizationService.getOrganizationApiKeysAsPromise(
      userId,
      organizationId,
      offset,
      perPage,
      queryId,
    );
  }

  async createOrganizationApiKey(
    userId: number,
    organizationId: string,
    createApiTokenRequestDto: CreateApiTokenRequestDto,
  ): Promise<CreateOrganizationApiKey201Response> {
    return await this.authorizationService.createOrganizationApiKeyAsPromise(
      userId,
      organizationId,
      createApiTokenRequestDto,
    );
  }

  async updateOrganizationApiKey(
    userId: number,
    organizationId: string,
    apiKeyId: string,
    updateApiTokenRequestDto: UpdateApiTokenRequestDto,
  ): Promise<GetOrganizationApiKey200Response> {
    return await this.authorizationService.updateOrganizationApiKeyAsPromise(
      userId,
      organizationId,
      apiKeyId,
      updateApiTokenRequestDto,
    );
  }

  async deleteOrganizationApiKey(
    userId: number,
    organizationId: string,
    apiKeyId: string,
  ): Promise<DeleteOrganizationApiKey200Response> {
    return await this.authorizationService.deleteOrganizationApiKeyAsPromise(
      userId,
      organizationId,
      apiKeyId,
    );
  }
}
