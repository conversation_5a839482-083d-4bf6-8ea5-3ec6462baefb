import {
  Controller,
  Get,
  Param,
  Query,
  Post,
  Body,
  Patch,
  Delete,
  UsePipes,
  ValidationPipe,
  Req,
} from '@nestjs/common';
import {
  ApiOperation,
  ApiTags,
  ApiResponse,
  ApiParam,
  ApiSecurity,
} from '@nestjs/swagger';
import { ApiKeyManagementService } from './api-key-management.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  CreateOrganizationApiKey201Response,
  DeleteOrganizationApiKey200Response,
  GetOrganizationApiKey200ResponseAllOf,
  UpdateApiTokenRequestDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { CreateApiKeyResponseDto } from './dto/create-api-key-response.dto';
import { DeleteApiKeyResponseDto } from './dto/delete-api-key-response.dto';
import { ReadApiKeyResponseDto } from './dto/read-api-key-response.dto';
import {
  createApiKeyPermission,
  deleteApiKeyPermission,
  readApiKeysPermission,
  updateApiKeyPermission,
} from './api-key-management.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { CreateApiKeyRequestDto } from './dto/create-api-key-request.dto';

@ApiTags('API Key Management')
@ApiSecurity('Bearer Token')
@Controller('api-key-management')
export class ApiKeyManagementController {
  constructor(private apiKeyManagementService: ApiKeyManagementService) {}

  @Permissions(readApiKeysPermission)
  @Get('organization/:organizationId')
  @ApiOperation({
    summary: 'Retrieve API keys for a given organization',
    description: 'Fetches an array of API keys, so org admins can view them.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an array of API keys for a given organization',
    type: ReadApiKeyResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving API keys',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async getAllApiKeysForOrg(
    @Param('organizationId') organizationId: string,
    @Query() paginationOptions: PaginationOptions,
    @Req() req: any,
  ) {
    const { userId } = req;
    return await this.apiKeyManagementService.getAllApiKeysForOrg(
      userId,
      organizationId,
      paginationOptions,
    );
  }

  @Permissions(createApiKeyPermission)
  @Post('organization/:organizationId')
  @ApiOperation({
    summary: 'Create an API key for an organization user',
  })
  @ApiResponse({
    status: 201,
    description: 'API key successfully created',
    type: CreateApiKeyResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async createOrganizationApiKey(
    @Param('organizationId') organizationId: string,
    @Body() body: CreateApiKeyRequestDto,
    @Req() req: any,
  ): Promise<CreateOrganizationApiKey201Response> {
    const { userId } = req;
    return await this.apiKeyManagementService.createOrganizationApiKey(
      userId,
      organizationId,
      body,
    );
  }

  @Permissions(updateApiKeyPermission)
  @Patch('organization/:organizationId/:apiKeyId')
  @ApiOperation({
    summary: 'Update an API key for an organization',
  })
  @ApiResponse({
    status: 200,
    description: 'API key successfully updated',
    type: ReadApiKeyResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({
    name: 'apiKeyId',
    description: 'The id of the API Key',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async updateOrganizationApiKey(
    @Param('organizationId') organizationId: string,
    @Param('apiKeyId') apiKeyId: string,
    @Body() updateApiTokenRequestDto: UpdateApiTokenRequestDto,
    @Req() req: any,
  ): Promise<GetOrganizationApiKey200ResponseAllOf> {
    const { userId } = req;
    return await this.apiKeyManagementService.updateOrganizationApiKey(
      userId,
      organizationId,
      apiKeyId,
      updateApiTokenRequestDto,
    );
  }

  @Permissions(deleteApiKeyPermission)
  @Delete('organization/:organizationId/:apiKeyId')
  @ApiOperation({
    summary: 'Delete a specific API key for an organization',
  })
  @ApiResponse({
    status: 200,
    description: 'API key successfully deleted',
    type: DeleteApiKeyResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({
    name: 'apiKeyId',
    description: 'The id of the API Key',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async deleteOrganizationApiKey(
    @Param('organizationId') organizationId: string,
    @Param('apiKeyId') apiKeyId: string,
    @Req() req: any,
  ): Promise<DeleteOrganizationApiKey200Response> {
    const { userId } = req;
    return await this.apiKeyManagementService.deleteOrganizationApiKey(
      userId,
      organizationId,
      apiKeyId,
    );
  }
}
