import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CountryRegionMap } from '../entities/country-region-map.entity';
import { Repository } from 'typeorm';
import { Industry } from '../entities/industry.entity';
import { IndustryWithSubIndustriesDto } from '../dto/industry-with-subs.dto';
import { Region } from '../entities/region.entity';
import { RegionCountriesFlatDto } from '../dto/region-countries-flat.dto';
import { RegionWithCountriesDto } from '../dto/region-countries.dto';
import { Country } from '../entities/market.entity';

@Injectable()
export class PlatformMetadataService {
  logger = new Logger(PlatformMetadataService.name);

  constructor(
    @InjectRepository(Country)
    private readonly countryRepository: Repository<Country>,
    @InjectRepository(Industry)
    private readonly industryRepository: Repository<Industry>,
  ) {}

  async getIndustries(): Promise<IndustryWithSubIndustriesDto[]> {
    try {
      const industries = await this.industryRepository.find();

      const industriesWithSubs: IndustryWithSubIndustriesDto[] = industries
        .filter(
          (industry) => industry.parentId === null && industry.rootId === null,
        )
        .map((i) => ({ id: i.id, name: i.name, subIndustries: [] }));

      industries
        .filter(
          (industry) =>
            industry.parentId !== null &&
            industry.rootId !== null &&
            industry.parentId === industry.rootId,
        )
        .forEach((subIndustry) => {
          const parentIndustry = industriesWithSubs.find(
            (industry) => industry.id === subIndustry.parentId,
          );
          if (parentIndustry) {
            parentIndustry.subIndustries.push({
              id: subIndustry.id,
              name: subIndustry.name,
              rootId: subIndustry.rootId,
              parentId: subIndustry.parentId,
            });
          }
        });

      return industriesWithSubs;
    } catch (error) {
      const errorMessage = `Error while fetching industries. ${error}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getIndustryAndSubIndustryNamesByIds(
    industryIds: number[],
  ): Promise<{ industries: string[]; subIndustries: string[] }> {
    try {
      const allIndustries = await this.industryRepository
        .createQueryBuilder('industry')
        .select(['industry.name as name', 'industry.parentId as parentId'])
        .where('industry.id IN (:...industryIds)', { industryIds })
        .getRawMany();

      return allIndustries.reduce(
        (acc, curr) => {
          if (curr.parentId) {
            acc.subIndustries.push(curr.name);
            return acc;
          }

          acc.industries.push(curr.name);
          return acc;
        },
        { industries: [], subIndustries: [] },
      );
    } catch (error) {
      const errorMessage = `Error while fetching industry and sub-industry names. ${error}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getRegions(): Promise<RegionWithCountriesDto[]> {
    try {
      const queryResult = (await this.countryRepository
        .createQueryBuilder('country')
        .innerJoin(CountryRegionMap, 'map', 'map.isoCode = country.isoCode')
        .innerJoin(Region, 'region', 'region.id = map.regionId')
        .select([
          'country.name AS name',
          'country.isoCode AS isoCode',
          'region.region AS region',
          'region.id AS regionId',
        ])
        .getRawMany()) as RegionCountriesFlatDto[];

      const regionMap = new Map<number, RegionWithCountriesDto>();
      queryResult.forEach((row) => {
        if (!regionMap.has(row.regionId)) {
          // If the region is not yet in the map, add it with the current country
          regionMap.set(row.regionId, {
            region: row.region,
            regionId: row.regionId,
            countries: [{ name: row.name, isoCode: row.isoCode }],
          });
        } else {
          // If the region exists, push the current country to its countries array
          regionMap
            .get(row.regionId)
            ?.countries.push({ name: row.name, isoCode: row.isoCode });
        }
      });

      return Array.from(regionMap.values());
    } catch (error) {
      const errorMessage = `Error while fetching regions and countries. ${error}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async getCountryNamesForMarketCodes(
    marketsIsoCodes: string[],
  ): Promise<string[]> {
    try {
      const countries = await this.countryRepository
        .createQueryBuilder('country')
        .select('country.name', 'name')
        .where('country.isoCode IN (:...marketsIsoCodes)', { marketsIsoCodes })
        .getRawMany();

      return countries.map((country) => country.name);
    } catch (error) {
      const errorMessage = `Error while fetching country names for market codes. ${error}`;
      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }
}
