import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Controller, Get } from '@nestjs/common';
import { PlatformMetadataService } from './platform-metadata.service';

@ApiTags('Platform Metadata')
@ApiSecurity('Bearer Token')
@Controller('metadata')
export class PlatformMetadataController {
  constructor(
    private readonly platformMetadataService: PlatformMetadataService,
  ) {}

  /**
   * return all parent industries available in Vidmob and their sub industries.
   */
  @Get('industries')
  async getIndustries() {
    return this.platformMetadataService.getIndustries();
  }

  /**
   * return all regions and their markets.
   */
  @Get('regions')
  async getRegions() {
    return this.platformMetadataService.getRegions();
  }
}
