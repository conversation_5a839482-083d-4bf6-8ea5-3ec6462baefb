import { PlatformMetadataService } from './platform-metadata.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Region } from '../entities/region.entity';
import { Industry } from '../entities/industry.entity';
import { Repository } from 'typeorm';
import { Country } from '../entities/market.entity';
import {
  expectedIndustryWithSubs,
  expectedRegionsAndNestedCountriesResponse,
  mockIndustryRepositoryResponse,
  mockRegionCountriesFlat,
} from '../mocks/mock-data';

describe('PlatformMetadataService', () => {
  let service: PlatformMetadataService;
  let industryRepository: jest.Mocked<Repository<Industry>>;
  let countryRepository: jest.Mocked<Repository<Country>>;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PlatformMetadataService,
        {
          provide: getRepositoryToken(Country),
          useValue: {
            find: jest.fn(),
            createQueryBuilder: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Region),
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Industry),
          useValue: {
            find: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PlatformMetadataService>(PlatformMetadataService);
    industryRepository = module.get(getRepositoryToken(Industry));
    countryRepository = module.get(getRepositoryToken(Country));
  });

  it('should correctly return all parent industries available in Vidmob and their sub industries to two levels', async () => {
    jest
      .spyOn(industryRepository, 'find')
      .mockResolvedValue(mockIndustryRepositoryResponse);

    const response = await service.getIndustries();

    expect(response).toEqual(expectedIndustryWithSubs);
  });

  it('should correctly return all regions and their nested markets', async () => {
    jest.spyOn(countryRepository, 'createQueryBuilder').mockReturnValueOnce({
      innerJoin: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      getRawMany: jest.fn().mockResolvedValue(mockRegionCountriesFlat),
    } as any);

    const response = await service.getRegions();
    expect(response).toEqual(expectedRegionsAndNestedCountriesResponse);
  });

  it('should handle error elegantly', async () => {
    jest.spyOn(industryRepository, 'find').mockImplementationOnce(() => {
      throw new Error('Industry table does not exist');
    });

    await expect(service.getIndustries()).rejects.toThrowError(
      'Error while fetching industries. Error: Industry table does not exist',
    );

    jest
      .spyOn(countryRepository, 'createQueryBuilder')
      .mockImplementationOnce(() => {
        throw new Error('Country table does not exist');
      });
    await expect(service.getRegions()).rejects.toThrowError(
      'Error while fetching regions and countries. Error: Country table does not exist',
    );
  });
});
