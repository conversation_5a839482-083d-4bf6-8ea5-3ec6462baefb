import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlatformMetadataController } from './platform-metadata.controller';
import { PlatformMetadataService } from './platform-metadata.service';
import { Country } from '../entities/market.entity';
import { Industry } from '../entities/industry.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Country, Industry])],
  providers: [PlatformMetadataService],
  controllers: [PlatformMetadataController],
})
export class PlatformMetadataModule {}
