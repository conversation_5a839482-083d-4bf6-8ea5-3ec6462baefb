import { PlatformMetadataController } from './platform-metadata.controller';
import { PlatformMetadataService } from './platform-metadata.service';
import { Test, TestingModule } from '@nestjs/testing';

describe('PlatformMetadataController', () => {
  let controller: PlatformMetadataController;
  const mockGetIndustries = jest.fn();
  const mockGetRegions = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PlatformMetadataController],
      providers: [
        {
          provide: PlatformMetadataService,
          useValue: {
            getIndustries: mockGetIndustries,
            getRegions: mockGetRegions,
          },
        },
      ],
    }).compile();

    controller = module.get<PlatformMetadataController>(
      PlatformMetadataController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call service getIndustries ', () => {
    controller.getIndustries();
    expect(mockGetIndustries).toHaveBeenCalled();
  });

  it('should call service getRegions ', () => {
    controller.getRegions();
    expect(mockGetRegions).toHaveBeenCalled();
  });
});
