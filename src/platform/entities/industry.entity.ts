import { Column, Entity, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity({ name: 'industry' })
export class Industry {
  @AutoMap()
  @PrimaryColumn({ type: 'bigint' })
  id: number;

  @AutoMap(() => String)
  @Column({ name: 'name', type: 'varchar', length: 50 })
  name: string;

  @AutoMap(() => Number)
  @Column({ name: 'parent_id', type: 'bigint', nullable: true })
  parentId: number | null;

  @AutoMap(() => Number)
  @Column({ name: 'root_id', type: 'bigint', nullable: true })
  rootId: number | null;
}
