import { createMap, Mapper } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { FeConstantsPlatformLogo } from '../entities/platform-logo.entities';
import { PlatformLogoDto } from '../dto/platform-logo-response.dto';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';

@Injectable()
export class PlatformLogoProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, FeConstantsPlatformLogo, PlatformLogoDto);
    };
  }
}
