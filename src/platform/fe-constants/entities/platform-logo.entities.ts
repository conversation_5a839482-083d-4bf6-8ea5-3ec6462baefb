import { <PERSON><PERSON><PERSON>, Column, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity('fe_constants_platform_logo')
export class FeConstantsPlatformLogo {
  @PrimaryColumn({ type: 'varchar', length: 255 })
  @AutoMap()
  platform_identifier: string;

  @Column({ type: 'varchar', length: 255 })
  @AutoMap()
  display_name: string;

  @Column({ type: 'varchar', length: 255 })
  @AutoMap()
  logo_url: string;
}
