import { Test, TestingModule } from '@nestjs/testing';
import { FeConstantsController } from './fe-constants.controller';
import { FeConstantsService } from './fe-constants.service';
import { PlatformLogoResponseDto } from './dto/platform-logo-response.dto';

describe('FeConstantsController', () => {
  let controller: FeConstantsController;
  let service: FeConstantsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FeConstantsController],
      providers: [
        {
          provide: FeConstantsService,
          useValue: {
            getConstants: jest.fn().mockResolvedValue([
              {
                platform_identifier: 'GOOGLE',
                display_name: 'Google',
                logo_url: 'https://example.com/logo-google.png',
              },
              {
                platform_identifier: 'META',
                display_name: 'Meta',
                logo_url: 'https://example.com/logo-meta.png',
              },
              {
                platform_identifier: 'X',
                display_name: 'X',
                logo_url: 'https://example.com/logo-x.png',
              },
            ]),
          },
        },
      ],
    }).compile();

    controller = module.get<FeConstantsController>(FeConstantsController);
    service = module.get<FeConstantsService>(FeConstantsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getPlatformLogo', () => {
    it('should return an array of platform logos', async () => {
      const expectedResponse: PlatformLogoResponseDto = {
        platformLogos: [
          {
            platform_identifier: 'GOOGLE',
            display_name: 'Google',
            logo_url: 'https://example.com/logo-google.png',
          },
          {
            platform_identifier: 'META',
            display_name: 'Meta',
            logo_url: 'https://example.com/logo-meta.png',
          },
          {
            platform_identifier: 'X',
            display_name: 'X',
            logo_url: 'https://example.com/logo-x.png',
          },
        ],
      };

      expect(await controller.getPlatformLogo()).toEqual(expectedResponse);
    });

    it('should handle errors appropriately', async () => {
      jest
        .spyOn(service, 'getConstants')
        .mockRejectedValue(new Error('Internal server error'));
      await expect(controller.getPlatformLogo()).rejects.toThrow(
        'Internal server error',
      );
    });
  });
});
