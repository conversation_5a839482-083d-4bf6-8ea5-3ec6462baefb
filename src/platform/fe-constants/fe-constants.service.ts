import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FeConstantsPlatformLogo } from './entities/platform-logo.entities';
import { PlatformLogoDto } from './dto/platform-logo-response.dto';
import { PlatformType } from './fe-constants.enum';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/types';

const { PLATFORM_LOGOS } = PlatformType;

@Injectable()
export class FeConstantsService {
  constructor(
    @InjectRepository(FeConstantsPlatformLogo)
    private platformLogoConstantsRepository: Repository<FeConstantsPlatformLogo>,
    @InjectMapper() private mapper: Mapper,
  ) {}

  async getConstants(type: string): Promise<PlatformLogoDto[]> {
    if (type === PLATFORM_LOGOS) {
      return this.getLogoConstants();
    }
    throw new Error('Invalid constant type');
  }

  private async getLogoConstants(): Promise<PlatformLogoDto[]> {
    const results = await this.platformLogoConstantsRepository.find();
    return this.mapper.mapArray(
      results,
      FeConstantsPlatformLogo,
      PlatformLogoDto,
    );
  }
}
