import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FeConstantsService } from './fe-constants.service';
import { FeConstantsController } from './fe-constants.controller';
import { FeConstantsPlatformLogo } from './entities/platform-logo.entities';
import { PlatformLogoProfile } from './profiles/platform-logo.profile';

@Module({
  imports: [TypeOrmModule.forFeature([FeConstantsPlatformLogo])],
  controllers: [FeConstantsController],
  providers: [FeConstantsService, PlatformLogoProfile],
})
export class FeConstantsModule {}
