import { Controller, Get } from '@nestjs/common';
import {
  ApiOperation,
  ApiSecurity,
  ApiTags,
  ApiResponse,
} from '@nestjs/swagger';
import { FeConstantsService } from './fe-constants.service';
import { PlatformLogoResponseDto } from './dto/platform-logo-response.dto';
import { PlatformType } from './fe-constants.enum';

const { PLATFORM_LOGOS } = PlatformType;

@ApiTags('Frontend Constants')
@ApiSecurity('Bearer Token')
@Controller('fe-constants')
export class FeConstantsController {
  constructor(private constantsService: FeConstantsService) {}

  @Get('platform-logo')
  @ApiOperation({
    summary: 'Retrieve platform logos',
    description:
      'Fetches an array of platform logo URLs for use in frontend applications.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an array of platform logo URLs',
    type: PlatformLogoResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Not Found' })
  @ApiResponse({
    status: 500,
    description: 'Internal server error retrieving platform logos',
  })
  async getPlatformLogo() {
    const platformLogos = await this.constantsService.getConstants(
      PLATFORM_LOGOS,
    );
    return { platformLogos };
  }
}
