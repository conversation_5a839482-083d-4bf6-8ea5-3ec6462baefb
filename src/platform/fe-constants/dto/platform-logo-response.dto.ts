import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsUrl } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class PlatformLogoDto {
  @AutoMap()
  @ApiProperty({
    example: 'SNAPCHAT',
    description: 'The unique identifier of the platform',
  })
  @IsString()
  platform_identifier: string;

  @AutoMap()
  @ApiProperty({
    example: 'Snapchat',
    description: 'The display name of the platform',
  })
  @IsString()
  display_name: string;

  @AutoMap()
  @ApiProperty({
    example: 'https://example.com/logo-snapchat.svg',
    description: 'URL of the platform logo',
  })
  @IsUrl()
  logo_url: string;
}

export class PlatformLogoResponseDto {
  @ApiProperty({
    type: [PlatformLogoDto],
    description: 'Array of platform logos',
  })
  platformLogos: PlatformLogoDto[];
}
