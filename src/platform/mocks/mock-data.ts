import { Industry } from '../entities/industry.entity';
import { IndustryWithSubIndustriesDto } from '../dto/industry-with-subs.dto';
import { RegionCountriesFlatDto } from '../dto/region-countries-flat.dto';
import { RegionWithCountriesDto } from '../dto/region-countries.dto';

export const mockIndustryRepositoryResponse: Industry[] = [
  { id: 1, name: 'Entertainment', parentId: null, rootId: null },
  { id: 2, name: 'TV Shows', parentId: 1, rootId: 1 },
  { id: 3, name: 'Movies', parentId: 1, rootId: 1 },
  { id: 4, name: 'Automotive', parentId: null, rootId: null },
  { id: 5, name: 'Vehicles', parentId: 4, rootId: 4 },
  { id: 6, name: 'Automotive Services', parentId: 4, rootId: 4 },
  { id: 7, name: 'Tires/Parts', parentId: 4, rootId: 4 },
  { id: 8, name: 'Headlights', parentId: 7, rootId: 4 },
];

export const expectedIndustryWithSubs: IndustryWithSubIndustriesDto[] = [
  {
    id: 1,
    name: 'Entertainment',
    subIndustries: [
      { id: 2, name: 'TV Shows', parentId: 1, rootId: 1 },
      { id: 3, name: 'Movies', parentId: 1, rootId: 1 },
    ],
  },
  {
    id: 4,
    name: 'Automotive',
    subIndustries: [
      { id: 5, name: 'Vehicles', parentId: 4, rootId: 4 },
      { id: 6, name: 'Automotive Services', parentId: 4, rootId: 4 },
      { id: 7, name: 'Tires/Parts', parentId: 4, rootId: 4 },
    ],
  },
];

export const mockRegionCountriesFlat: RegionCountriesFlatDto[] = [
  {
    name: 'American Samoa',
    isoCode: 'asm',
    region: 'North America',
    regionId: 1,
  },
  { name: 'Bahamas', isoCode: 'bhs', region: 'North America', regionId: 1 },
  { name: 'Canada', isoCode: 'can', region: 'North America', regionId: 1 },
  {
    name: 'United States',
    isoCode: 'usa',
    region: 'North America',
    regionId: 1,
  },
  { name: 'Benin', isoCode: 'ben', region: 'EMEA', regionId: 2 },
  { name: 'Bulgaria', isoCode: 'bgr', region: 'EMEA', regionId: 2 },
  { name: 'Iraq', isoCode: 'irq', region: 'EMEA', regionId: 2 },
  { name: 'Belize', isoCode: 'blz', region: 'LATAM', regionId: 3 },
  { name: 'Nicaragua', isoCode: 'nic', region: 'LATAM', regionId: 3 },
];

export const expectedRegionsAndNestedCountriesResponse: RegionWithCountriesDto[] =
  [
    {
      regionId: 1,
      region: 'North America',
      countries: [
        {
          isoCode: 'asm',
          name: 'American Samoa',
        },
        {
          isoCode: 'bhs',
          name: 'Bahamas',
        },
        {
          isoCode: 'can',
          name: 'Canada',
        },
        {
          isoCode: 'usa',
          name: 'United States',
        },
      ],
    },
    {
      regionId: 2,
      region: 'EMEA',
      countries: [
        {
          isoCode: 'ben',
          name: 'Benin',
        },
        {
          isoCode: 'bgr',
          name: 'Bulgaria',
        },
        {
          isoCode: 'irq',
          name: 'Iraq',
        },
      ],
    },
    {
      regionId: 3,
      region: 'LATAM',
      countries: [
        {
          isoCode: 'blz',
          name: 'Belize',
        },
        {
          isoCode: 'nic',
          name: 'Nicaragua',
        },
      ],
    },
  ];
