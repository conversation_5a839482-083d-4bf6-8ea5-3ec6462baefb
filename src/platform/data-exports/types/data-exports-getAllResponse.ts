export interface GetOrganizationDataExports200Response {
  status: string;
  result: Array<GetAllDataExportsResponseDto>;
}

export interface GetAllDataExportsResponseDto {
  ad_accounts: IdAndName[];
  brands: IdAndName[];
  channels: string[];
  created_by: string;
  created_on: string;
  expiration: string;
  export_type: string;
  markets: IdAndName[];
  name: string;
  report_id: string;
  status: string;
  workspaces: IdAndName[];
}

interface IdAndName {
  id: string;
  name: string;
}
