import {
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto,
} from '../dto/createDataExportBFFRequest.dto';
import { IdAndNameDto } from '../dto/idAndName.dto';
import { CreateDataExportRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportRequestDto';
import {
  CreateDataExportBFFResponseDto,
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO,
} from '../dto/createDataExportBFFResponse.dto';
import { FrontendStatusEnum } from '../enum/frontend-status.enum';

const MOCK_WORKSPACES: IdAndNameDto[] = [
  {
    id: '12345',
    name: 'Test Workspace 1',
  },
  {
    id: '6789',
    name: 'Test Workspace 2',
  },
];

const MOCK_MARKETS: IdAndNameDto[] = [
  {
    id: '12345',
    name: 'Test Market 1',
  },
  {
    id: '6789',
    name: 'Test Market 2',
  },
];

const MOCK_AD_ACCOUNTS: IdAndNameDto[] = [
  {
    id: '12345',
    name: 'Test Ad Account 1',
  },
  {
    id: '6789',
    name: 'Test Ad Account 2',
  },
];

const MOCK_BRANDS: IdAndNameDto[] = [
  {
    id: '12345',
    name: 'Test Brand 1',
  },
  {
    id: '6789',
    name: 'Test Brand 2',
  },
];

const MOCK_CHANNELS: string[] = ['facebook', 'tiktok'];
const MOCK_START_DATE = '2024-07-09T00:00:00Z';
const MOCK_END_DATE = '2024-07-09T00:00:00Z';
const MOCK_REPORT_NAME = 'Test Report Name';

export const CreateDataExportMockBFFRequest: (
  hasAdAccounts: boolean,
  hasBrands: boolean,
  hasMarkets: boolean,
) => CreateDataExportBFFRequestDto = (
  hasAdAccounts: boolean,
  hasBrands: boolean,
  hasMarkets: boolean,
) => {
  return {
    export_type: 'scoring',
    channels: MOCK_CHANNELS,
    name: MOCK_REPORT_NAME,
    workspaces: MOCK_WORKSPACES,
    start_date: MOCK_START_DATE,
    end_date: MOCK_END_DATE,
    ad_accounts: hasAdAccounts ? MOCK_AD_ACCOUNTS : [],
    brands: hasBrands ? MOCK_BRANDS : [],
    markets: hasMarkets ? MOCK_MARKETS : [],
  };
};

export const CreateDataExportMockRequest: (
  hasAdAccounts: boolean,
  hasBrands: boolean,
  hasMarkets: boolean,
) => CreateDataExportOrganizationRequestDto = (
  hasAdAccounts: boolean,
  hasBrands: boolean,
  hasMarkets: boolean,
) => {
  return {
    reportType: CreateDataExportRequestDto.ReportTypeEnum.Scoring,
    reportName: MOCK_REPORT_NAME,
    startDate: MOCK_START_DATE,
    endDate: MOCK_END_DATE,
    workspaces: MOCK_WORKSPACES.map((w) => Number.parseInt(w.id)),
    channels: MOCK_CHANNELS,
    brands: hasBrands ? MOCK_BRANDS.map((b) => b.id) : undefined,
    markets: hasMarkets ? MOCK_MARKETS.map((m) => m.id) : undefined,
    adAccounts: hasAdAccounts ? MOCK_AD_ACCOUNTS.map((a) => a.id) : undefined,
  };
};

export const dataExportsMockGetAllResponse = [
  {
    ad_accounts: [
      { id: 'account2', name: 'Ad Account 2' },
      { id: 'account1', name: 'Ad Account 1' },
    ],
    brands: [{ id: 'brand1', name: 'Brand 1' }],
    channels: ['tiktok'],
    created_by: 'user456',
    created_on: '2024-06-15T17:44:51.878Z',
    expiration: '2025-06-15T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'uk', name: 'United Kingdom' },
      { id: 'us', name: 'United States' },
    ],
    name: 'test report 1',
    report_id: 'a1b2c3d4',
    status: 'COMPLETED',
    workspaces: [{ id: 'workspace1', name: 'Workspace 1' }],
  },
  {
    ad_accounts: [
      { id: 'account3', name: 'Ad Account 3' },
      { id: 'account2', name: 'Ad Account 2' },
    ],
    brands: [{ id: 'brand2', name: 'Brand 2' }],
    channels: ['facebook'],
    created_by: 'user123',
    created_on: '2024-06-12T17:44:51.878Z',
    expiration: '2025-06-12T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'de', name: 'Germany' },
      { id: 'us', name: 'United States' },
    ],
    name: 'test report 2',
    report_id: 'd4e5f6g7',
    status: 'PROCESSING',
    workspaces: [{ id: 'workspace2', name: 'Workspace 2' }],
  },
  {
    ad_accounts: [
      { id: 'account1', name: 'Ad Account 1' },
      { id: 'account3', name: 'Ad Account 3' },
    ],
    brands: [{ id: 'brand1', name: 'Brand 1' }],
    channels: ['dv360'],
    created_by: 'user789',
    created_on: '2024-06-20T17:44:51.878Z',
    expiration: '2025-06-20T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'uk', name: 'United Kingdom' },
      { id: 'de', name: 'Germany' },
    ],
    name: 'test report 3',
    report_id: 'h8i9j0k1',
    status: 'QUEUED',
    workspaces: [{ id: 'workspace3', name: 'Workspace 3' }],
  },
  {
    ad_accounts: [
      { id: 'account2', name: 'Ad Account 2' },
      { id: 'account1', name: 'Ad Account 1' },
    ],
    brands: [{ id: 'brand2', name: 'Brand 2' }],
    channels: ['tiktok'],
    created_by: 'user456',
    created_on: '2024-06-25T17:44:51.878Z',
    expiration: '2025-06-25T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'us', name: 'United States' },
      { id: 'uk', name: 'United Kingdom' },
    ],
    name: 'test report 4',
    report_id: 'l2m3n4o5',
    status: 'STARTED',
    workspaces: [{ id: 'workspace1', name: 'Workspace 1' }],
  },
  {
    ad_accounts: [
      { id: 'account3', name: 'Ad Account 3' },
      { id: 'account2', name: 'Ad Account 2' },
    ],
    brands: [{ id: 'brand1', name: 'Brand 1' }],
    channels: ['facebook'],
    created_by: 'user123',
    created_on: '2024-06-10T17:44:51.878Z',
    expiration: '2025-06-10T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'de', name: 'Germany' },
      { id: 'us', name: 'United States' },
    ],
    name: 'test report 5',
    report_id: 'p6q7r8s9',
    status: 'FAILED',
    workspaces: [{ id: 'workspace2', name: 'Workspace 2' }],
  },
  {
    ad_accounts: [
      { id: 'account1', name: 'Ad Account 1' },
      { id: 'account3', name: 'Ad Account 3' },
    ],
    brands: [{ id: 'brand2', name: 'Brand 2' }],
    channels: ['dv360'],
    created_by: 'user789',
    created_on: '2024-06-19T17:44:51.878Z',
    expiration: '2025-06-19T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'uk', name: 'United Kingdom' },
      { id: 'de', name: 'Germany' },
    ],
    name: 'test report 6',
    report_id: 't0u1v2w3',
    status: 'DELETED',
    workspaces: [{ id: 'workspace3', name: 'Workspace 3' }],
  },
  {
    ad_accounts: [
      { id: 'account2', name: 'Ad Account 2' },
      { id: 'account1', name: 'Ad Account 1' },
    ],
    brands: [{ id: 'brand1', name: 'Brand 1' }],
    channels: ['tiktok'],
    created_by: 'user456',
    created_on: '2024-06-14T17:44:51.878Z',
    expiration: '2025-06-14T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'us', name: 'United States' },
      { id: 'uk', name: 'United Kingdom' },
    ],
    name: 'test report 7',
    report_id: 'x4y5z6a7',
    status: 'EXPIRED',
    workspaces: [{ id: 'workspace1', name: 'Workspace 1' }],
  },
  {
    ad_accounts: [
      { id: 'account3', name: 'Ad Account 3' },
      { id: 'account2', name: 'Ad Account 2' },
    ],
    brands: [{ id: 'brand2', name: 'Brand 2' }],
    channels: ['facebook'],
    created_by: 'user123',
    created_on: '2024-06-09T17:44:51.878Z',
    expiration: '2025-06-09T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'de', name: 'Germany' },
      { id: 'us', name: 'United States' },
    ],
    name: 'test report 8',
    report_id: 'b8c9d0e1',
    status: 'COMPLETED',
    workspaces: [{ id: 'workspace2', name: 'Workspace 2' }],
  },
  {
    ad_accounts: [
      { id: 'account1', name: 'Ad Account 1' },
      { id: 'account3', name: 'Ad Account 3' },
    ],
    brands: [{ id: 'brand1', name: 'Brand 1' }],
    channels: ['dv360'],
    created_by: 'user789',
    created_on: '2024-06-05T17:44:51.878Z',
    expiration: '2025-06-05T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'uk', name: 'United Kingdom' },
      { id: 'de', name: 'Germany' },
    ],
    name: 'test report 9',
    report_id: 'f2g3h4i5',
    status: 'STARTED',
    workspaces: [{ id: 'workspace3', name: 'Workspace 3' }],
  },
  {
    ad_accounts: [
      { id: 'account2', name: 'Ad Account 2' },
      { id: 'account1', name: 'Ad Account 1' },
    ],
    brands: [{ id: 'brand2', name: 'Brand 2' }],
    channels: ['tiktok'],
    created_by: 'user456',
    created_on: '2024-06-18T17:44:51.878Z',
    expiration: '2025-06-18T17:44:51.878Z',
    export_type: 'scoring',
    markets: [
      { id: 'us', name: 'United States' },
      { id: 'uk', name: 'United Kingdom' },
    ],
    name: 'test report 10',
    report_id: 'j6k7l8m9',
    status: 'PROCESSING',
    workspaces: [{ id: 'workspace1', name: 'Workspace 1' }],
  },
];

export const generateDataExportsMockCreateResponse = (
  body: CreateDataExportBFFRequestDto,
): FrontendCreateReportDTO => {
  return {
    reportId: '12345' + body.name,
    status: FrontendStatusEnum.PROCESSING,
    message: 'Export data requested successfully',
    dateCreated: '2024-07-09T00:00:00Z',
  };
};

export const CreateDataExportOrgResponse: (
  body: CreateDataExportBFFRequestDto,
) => CreateDataExportOrganizationResponseDto = (body) => {
  return {
    reportId: '12345' + body.name,
    status: 'QUEUED',
    message: 'Export data requested successfully',
    dateCreated: '2024-07-09T00:00:00Z',
  };
};

export const MockScheduledDataExportsGetAllResponse = [
  {
    reportId: '1234badfasdghasidog23',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-08-26',
    nextExport: '2024-08-26',
    exportFrequency: 'weekly',
    location: 's3-creative-scoring-reports',
    startDate: '2024-08-26',
    endDate: '2024-09-26',
    runsCompleted: 47,
    runsIndefinitely: true,
    createdOn: '2024-08-26',
    createdBy: {
      id: '12345',
      name: 'Testy B. McTesterson',
      url: 'http://www.photo.com/testy',
    },
  },
  {
    reportId: '2345ghkjsdgjkadf90234',
    exportName: 'Creative Aperture Report 1',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-07-10',
    nextExport: '2024-07-17',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-07-01',
    endDate: '2024-08-01',
    runsCompleted: 30,
    runsRemaining: 10,
    runsIndefinitely: false,
    createdOn: '2024-06-28',
    createdBy: {
      id: '67890',
      name: 'Sammy Q. Samplington',
      url: 'http://www.photo.com/sammy',
    },
  },
  {
    reportId: '3456lkjwer09234hjsdf',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-09-15',
    nextExport: '2024-09-22',
    exportFrequency: 'weekly',
    location: 's3-creative-scoring-reports',
    startDate: '2024-09-01',
    endDate: '2024-10-01',
    runsCompleted: 15,
    runsIndefinitely: true,
    createdOn: '2024-08-30',
    createdBy: {
      id: '12345',
      name: 'Testy B. McTesterson',
      url: 'http://www.photo.com/testy',
    },
  },
  {
    reportId: '4567asdjkl234jhrwe8',
    exportName: 'Creative Aperture Report 2',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-06-20',
    nextExport: '2024-06-27',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-06-01',
    endDate: '2024-07-01',
    runsCompleted: 10,
    runsRemaining: 5,
    runsIndefinitely: false,
    createdOn: '2024-05-25',
    createdBy: {
      id: '54321',
      name: 'Jane D. Doe',
      url: 'http://www.photo.com/jane',
    },
  },
  {
    reportId: '5678qweurh2o34sdkjf',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-10-01',
    nextExport: '2024-10-02',
    exportFrequency: 'daily',
    location: 's3-creative-scoring-reports',
    startDate: '2024-09-01',
    endDate: '2024-10-01',
    runsCompleted: 31,
    runsIndefinitely: true,
    createdOn: '2024-08-29',
    createdBy: {
      id: '67890',
      name: 'Sammy Q. Samplington',
      url: 'http://www.photo.com/sammy',
    },
  },
  {
    reportId: '6789asdkjfl2o34hjasd',
    exportName: 'Creative Aperture Report 3',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-04-15',
    nextExport: '2024-04-22',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-04-01',
    endDate: '2024-05-01',
    runsCompleted: 20,
    runsRemaining: 15,
    runsIndefinitely: false,
    createdOn: '2024-03-28',
    createdBy: {
      id: '98765',
      name: 'Alex R. Example',
      url: 'http://www.photo.com/alex',
    },
  },
  {
    reportId: '7890qwoeir23jsadfj',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-05-10',
    nextExport: '2024-05-11',
    exportFrequency: 'daily',
    location: 's3-creative-scoring-reports',
    startDate: '2024-05-01',
    endDate: '2024-06-01',
    runsCompleted: 11,
    runsIndefinitely: true,
    createdOn: '2024-04-25',
    createdBy: {
      id: '54321',
      name: 'Jane D. Doe',
      url: 'http://www.photo.com/jane',
    },
  },
  {
    reportId: '8901asdkfh3o24skdfj',
    exportName: 'Creative Aperture Report 4',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-02-15',
    nextExport: '2024-02-22',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-02-01',
    endDate: '2024-03-01',
    runsCompleted: 5,
    runsRemaining: 5,
    runsIndefinitely: false,
    createdOn: '2024-01-28',
    createdBy: {
      id: '67890',
      name: 'Sammy Q. Samplington',
      url: 'http://www.photo.com/sammy',
    },
  },
  {
    reportId: '9012qweor93jsadfkl',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-03-05',
    nextExport: '2024-03-12',
    exportFrequency: 'weekly',
    location: 's3-creative-scoring-reports',
    startDate: '2024-03-01',
    endDate: '2024-04-01',
    runsCompleted: 4,
    runsIndefinitely: true,
    createdOn: '2024-02-20',
    createdBy: {
      id: '98765',
      name: 'Alex R. Example',
      url: 'http://www.photo.com/alex',
    },
  },
  {
    reportId: '0123zxcv2034nasdflk',
    exportName: 'Creative Aperture Report 5',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-01-15',
    nextExport: '2024-01-22',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-01-01',
    endDate: '2024-02-01',
    runsCompleted: 10,
    runsRemaining: 10,
    runsIndefinitely: false,
    createdOn: '2023-12-28',
    createdBy: {
      id: '54321',
      name: 'Jane D. Doe',
      url: 'http://www.photo.com/jane',
    },
  },
  {
    reportId: '1234lkjasdf2034iuowr',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-11-01',
    nextExport: '2024-11-02',
    exportFrequency: 'daily',
    location: 's3-creative-scoring-reports',
    startDate: '2024-10-01',
    endDate: '2024-11-01',
    runsCompleted: 31,
    runsIndefinitely: true,
    createdOn: '2024-09-25',
    createdBy: {
      id: '12345',
      name: 'Testy B. McTesterson',
      url: 'http://www.photo.com/testy',
    },
  },
  {
    reportId: '5678asdklfj3209skdjf',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-10-15',
    nextExport: '2024-10-22',
    exportFrequency: 'weekly',
    location: 's3-creative-scoring-reports',
    startDate: '2024-10-01',
    endDate: '2024-11-01',
    runsCompleted: 10,
    runsRemaining: 5,
    runsIndefinitely: false,
    createdOn: '2024-09-30',
    createdBy: {
      id: '54321',
      name: 'Jane D. Doe',
      url: 'http://www.photo.com/jane',
    },
  },
  {
    reportId: '6789qweoi3209asldkf',
    exportName: 'Creative Aperture Report 8',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-08-10',
    nextExport: '2024-08-17',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-08-01',
    endDate: '2024-09-01',
    runsCompleted: 20,
    runsIndefinitely: true,
    createdOn: '2024-07-25',
    createdBy: {
      id: '67890',
      name: 'Sammy Q. Samplington',
      url: 'http://www.photo.com/sammy',
    },
  },
  {
    reportId: '7890asdklfj2309wqerk',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-12-05',
    nextExport: '2024-12-12',
    exportFrequency: 'weekly',
    location: 's3-creative-scoring-reports',
    startDate: '2024-12-01',
    endDate: '2025-01-01',
    runsCompleted: 5,
    runsRemaining: 10,
    runsIndefinitely: false,
    createdOn: '2024-11-20',
    createdBy: {
      id: '98765',
      name: 'Alex R. Example',
      url: 'http://www.photo.com/alex',
    },
  },
  {
    reportId: '8901qweoi3209alsdkj',
    exportName: 'Creative Aperture Report 9',
    exportType: 'Creative Aperture',
    lastSuccessful: '2024-03-20',
    nextExport: '2024-03-27',
    exportFrequency: 'weekly',
    location: 's3-creative-aperture-reports',
    startDate: '2024-03-01',
    endDate: '2024-04-01',
    runsCompleted: 15,
    runsIndefinitely: true,
    createdOn: '2024-02-25',
    createdBy: {
      id: '54321',
      name: 'Jane D. Doe',
      url: 'http://www.photo.com/jane',
    },
  },
  {
    reportId: '9012zxcv3209werlkj',
    exportName: 'Creative Scoring Report',
    exportType: 'Creative Scoring',
    lastSuccessful: '2024-07-10',
    nextExport: '2024-07-17',
    exportFrequency: 'weekly',
    location: 's3-creative-scoring-reports',
    startDate: '2024-07-01',
    endDate: '2024-08-01',
    runsCompleted: 8,
    runsRemaining: 7,
    runsIndefinitely: false,
    createdOn: '2024-06-28',
    createdBy: {
      id: '12345',
      name: 'Testy B. McTesterson',
      url: 'http://www.photo.com/testy',
    },
  },
];
