import {
  CreateDataExportMockBFFRequest,
  CreateDataExportMockRequest,
} from '../data/dataExportsMockData';

/**
 * Test case for Data export BFF Request body to CreateDataExportRequestDto
 */
export const MAP_DATA_EXPORT_REQUEST_BODY_TO_ORG_SERVICE_INPUT_TEST_CASES = [
  [
    CreateDataExportMockBFFRequest(true, true, true),
    CreateDataExportMockRequest(true, true, true),
  ],
  [
    CreateDataExportMockBFFRequest(true, true, false),
    CreateDataExportMockRequest(true, true, false),
  ],
  [
    CreateDataExportMockBFFRequest(true, false, false),
    CreateDataExportMockRequest(true, false, false),
  ],
  [
    CreateDataExportMockBFFRequest(true, false, true),
    CreateDataExportMockRequest(true, false, true),
  ],
  [
    CreateDataExportMockBFFRequest(false, false, true),
    CreateDataExportMockRequest(false, false, true),
  ],
  [
    CreateDataExportMockBFFRequest(false, false, false),
    CreateDataExportMockRequest(false, false, false),
  ],
];

/**
 * Mock BFF Request. Can be used in data-export.profile.spec
 */
export const MOCK_BFF_REQUESTS = [
  CreateDataExportMockBFFRequest(true, true, true),
  CreateDataExportMockBFFRequest(true, true, false),
  CreateDataExportMockBFFRequest(true, false, false),
  CreateDataExportMockBFFRequest(true, false, true),
  CreateDataExportMockBFFRequest(false, false, true),
  CreateDataExportMockBFFRequest(false, false, false),
];
