import {
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto,
} from '../../dto/createDataExportBFFRequest.dto';
import { createMap, forMember, mapFrom, Mapper, Mapping, MappingConfiguration, preCondition } from '@automapper/core';
import { DataExportTypeMap } from '../../data-export.constants';

/**
 * Maps ad accounts from a Create Data Export Request from BFF to
 * an Organization Data export DTO ad account parameter
 *
 */
const MapAdAccountsFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.adAccounts,
  preCondition((source) => source.ad_accounts.length > 0, undefined),
  mapFrom<
    CreateDataExportBFFRequestDto,
    CreateDataExportOrganizationRequestDto
  >((s) => s.ad_accounts.map((a) => a.id)),
);

/**
 * Maps markets from a Create Data Export Request from BFF to
 * an Organization Data export DTO market parameter
 *
 */
const MapMarketsFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.markets,
  preCondition((source) => source.markets.length > 0, undefined),
  mapFrom((source) => source.markets.map((m) => m.id)),
);

/**
 * Maps brands from a Create Data Export Request from BFF to
 * an Organization Data export DTO brands parameter
 *
 */
const MapBrandsFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.brands,
  preCondition((source) => source.brands.length > 0, undefined),
  mapFrom((source) => source.brands.map((b) => b.id)),
);

/**
 * Maps channels from a Create Data Export Request from BFF to
 * an Organization Data export DTO channels parameter
 *
 */
const MapChannelsFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.channels,
  mapFrom((source) => source.channels),
);

/**
 * Maps workspaces from a Create Data Export Request from BFF to
 * an Organization Data export DTO workspaces parameter
 *
 */
const MapWorkspacesFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.workspaces,
  mapFrom((source) => source.workspaces.map((w) => Number.parseInt(w.id))),
);

/**
 * Maps the end time from a Create Data Export Request from BFF to
 * an Organization Data export DTO end time parameter
 *
 */
const MapEndDateFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.endDate,
  mapFrom((source) => source.end_date),
);

/**
 * Maps the start time from a Create Data Export Request from BFF to
 * an Organization Data export DTO
 *
 */
const MapStartDateFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.startDate,
  mapFrom((source) => source.start_date),
);

/**
 * Maps the Report Name from a Create Data Export Request from BFF to
 * an Organization Data export DTO
 *
 */
const MapReportNameFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.reportName,
  preCondition((source) => !!source.name && source.name.length > 0, undefined),
  mapFrom((source) => source.name),
);

/**
 * Maps the Report Type from a Create Data Export Request from BFF to
 * an Organization Data export DTO
 *
 */
const MapReportTypeFromBFFRequest: MappingConfiguration<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = forMember<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
>(
  (d) => d.reportType,
  preCondition((source) => source.export_type in DataExportTypeMap, undefined),
  mapFrom((source) => DataExportTypeMap[source.export_type]),
);

/**
 * Maps a data export request from ACS BFF to the data export request DTO from
 * the Organization Service.
 * @param mapper
 * @constructor
 */
export const MapDataExportRequestFromBFFToOrganizationService: (
  mapper: Mapper,
) => Mapping<
  CreateDataExportBFFRequestDto,
  CreateDataExportOrganizationRequestDto
> = (mapper) =>
  createMap<
    CreateDataExportBFFRequestDto,
    CreateDataExportOrganizationRequestDto
  >(
    mapper,
    CreateDataExportBFFRequestDto,
    CreateDataExportOrganizationRequestDto,
    MapReportTypeFromBFFRequest,
    MapReportNameFromBFFRequest,
    MapStartDateFromBFFRequest,
    MapEndDateFromBFFRequest,
    MapWorkspacesFromBFFRequest,
    MapChannelsFromBFFRequest,
    MapBrandsFromBFFRequest,
    MapMarketsFromBFFRequest,
    MapAdAccountsFromBFFRequest,
  );
