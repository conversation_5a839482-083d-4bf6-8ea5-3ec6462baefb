import {
  createMap,
  extend,
  forMember,
  mapFrom,
  Mapper,
  Mapping,
  MappingConfiguration,
  preCondition,
} from '@automapper/core';
import {
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto,
  FrontEndFullReadReportDto,
  ReadDataExportReportOrganizationDto,
} from '../../dto/getAllDataExportsResponse.dto';
import {
  BackendToFrontendReportType,
  BackendToFrontendStatus,
} from '../../data-export.constants';

const TransformReportIdToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.reportId,
  mapFrom((s) => s.reportId),
);

const TransformReportNameToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.reportName,
  mapFrom((s) => s.reportName),
);

const TransformEndDateToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.endDate,
  mapFrom((s) => s.endDate),
);

const TransformStartDateToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.startDate,
  mapFrom((s) => s.startDate),
);

const TransformDateCreatedToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.dateCreated,
  mapFrom((s) => s.dateCreated),
);

const TransformCreatedByToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.createdBy,
  mapFrom((s) => s.createdBy),
);

const TransformExpirationToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.expiration,
  preCondition((s) => !!s.expiration, undefined),
  mapFrom((s) => s.expiration),
);

const TransformFailureReasonToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.failureReason,
  preCondition((s) => !!s.failureReason, undefined),
  mapFrom((s) => s.failureReason),
);

const TransformHasDataToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.hasData,
  preCondition((s) => s.hasData !== undefined, undefined),
  mapFrom((s) => s.hasData),
);

const TransformRecordCountToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.recordCount,
  preCondition((s) => s.recordCount !== undefined, undefined),
  mapFrom((s) => s.recordCount),
);

/**
 * Transforms the backend status name to its display name
 * that will be shown in the frontend.
 */
const TransformBackendStatusToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.status,
  mapFrom((s) => BackendToFrontendStatus[s.status]),
);

/**
 * Transforms the backend report type to its display name
 * that will be shown in the frontend.
 */
const TransformBackendReportTypeToFrontend: MappingConfiguration<
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto
> = forMember<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
  (d) => d.reportType,
  mapFrom((s) => BackendToFrontendReportType[s.reportType]),
);

/**
 * Creates a mapping from a basic Read Report DTO coming
 * from the organization service, to the DTO being read by
 * the front end
 * @param mapper
 * @constructor
 */
export const MapBasicReadDTOToFrontendDTO: (
  mapper: Mapper,
) => Mapping<BasicReadDataExportReportDto, FrontEndBasicReadReportDto> = (
  mapper,
) =>
  createMap<BasicReadDataExportReportDto, FrontEndBasicReadReportDto>(
    mapper,
    BasicReadDataExportReportDto,
    FrontEndBasicReadReportDto,
    TransformReportIdToFrontend,
    TransformReportNameToFrontend,
    TransformBackendReportTypeToFrontend,
    TransformBackendStatusToFrontend,
    TransformCreatedByToFrontend,
    TransformDateCreatedToFrontend,
    TransformStartDateToFrontend,
    TransformEndDateToFrontend,
    TransformRecordCountToFrontend,
    TransformHasDataToFrontend,
    TransformExpirationToFrontend,
    TransformFailureReasonToFrontend,
  );

const TransformAdAccountsToFrontend: MappingConfiguration<
  ReadDataExportReportOrganizationDto,
  FrontEndFullReadReportDto
> = forMember(
  (d) => d.adAccounts,
  preCondition((s) => !!s.adAccounts && s.adAccounts.length > 0, undefined),
  mapFrom((s) => s.adAccounts),
);

const TransformBrandsToFrontend: MappingConfiguration<
  ReadDataExportReportOrganizationDto,
  FrontEndFullReadReportDto
> = forMember(
  (d) => d.brands,
  preCondition((s) => !!s.brands && s.brands.length > 0, undefined),
  mapFrom((s) => s.brands),
);

const TransformChannelsToFrontend: MappingConfiguration<
  ReadDataExportReportOrganizationDto,
  FrontEndFullReadReportDto
> = forMember(
  (d) => d.channels,
  preCondition((s) => !!s.channels, undefined),
  mapFrom((s) => s.channels),
);

const TransformMarketsToFrontend: MappingConfiguration<
  ReadDataExportReportOrganizationDto,
  FrontEndFullReadReportDto
> = forMember(
  (d) => d.markets,
  preCondition((s) => !!s.markets && s.markets.length > 0, undefined),
  mapFrom((s) => s.markets),
);

const TransformDownloadUrlFrontend: MappingConfiguration<
  ReadDataExportReportOrganizationDto,
  FrontEndFullReadReportDto
> = forMember(
  (d) => d.downloadUrl,
  preCondition((s) => !!s.downloadUrl, undefined),
  mapFrom((s) => s.downloadUrl),
);

const TransformWorkspacesrontend: MappingConfiguration<
  ReadDataExportReportOrganizationDto,
  FrontEndFullReadReportDto
> = forMember(
  (d) => d.workspaces,
  preCondition((s) => !!s.workspaces && s.workspaces.length > 0, undefined),
  mapFrom((s) => s.workspaces),
);

/**
 * Create a mapping from the full data response from the
 * read one endpoint on the organization service to the
 * DTO read on the frontend
 *
 * NOTE: This mapping extends MapBasicReadDTOToFrontendDTO
 *
 * @param mapper
 * @constructor
 */
export const MapFullReadDTOToFrontendDTO: (
  mapper: Mapper,
) => Mapping<ReadDataExportReportOrganizationDto, FrontEndFullReadReportDto> = (
  mapper,
) =>
  createMap<ReadDataExportReportOrganizationDto, FrontEndFullReadReportDto>(
    mapper,
    ReadDataExportReportOrganizationDto,
    FrontEndFullReadReportDto,
    extend(MapBasicReadDTOToFrontendDTO(mapper)),
    TransformAdAccountsToFrontend,
    TransformBrandsToFrontend,
    TransformChannelsToFrontend,
    TransformMarketsToFrontend,
    TransformDownloadUrlFrontend,
    TransformWorkspacesrontend,
  );
