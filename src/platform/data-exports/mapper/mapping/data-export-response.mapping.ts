import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  Mapping,
  MappingConfiguration,
} from '@automapper/core';
import {
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO,
} from '../../dto/createDataExportBFFResponse.dto';
import { BackendToFrontendStatus } from '../../data-export.constants';
import { FilterOption } from '@vidmob/vidmob-organization-service-sdk';
import ReportStatusesEnum = FilterOption.ReportStatusesEnum;

const MapReportIdFromOrgServiceResponse: MappingConfiguration<
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO
> = forMember(
  (d) => d.reportId,
  mapFrom((s) => s.reportId),
);

const MapDateCreatedFromOrgServiceResponse: MappingConfiguration<
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO
> = forMember(
  (d) => d.dateCreated,
  mapFrom((s) => s.dateCreated),
);

const MapMessageFromOrgServiceResponse: MappingConfiguration<
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO
> = forMember(
  (d) => d.message,
  mapFrom((s) => s.message),
);

/**
 * Maps status parameter from the organization service
 * response to an ACS BFF Data Export Response status parameter
 *
 */
const MapReportStateFromOrgServiceResponse: MappingConfiguration<
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO
> = forMember(
  (d) => d.status,
  mapFrom((s) => BackendToFrontendStatus[s.status as ReportStatusesEnum]),
);

/**
 * Map Data Export Response from the Organization Service
 * to an ACS BFF Data Export Response.
 * @param mapper
 * @constructor
 */
export const MapOrganizationServiceDataExportResponseToBFF: (
  mapper: Mapper,
) => Mapping<
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO
> = (mapper) =>
  createMap<CreateDataExportOrganizationResponseDto, FrontendCreateReportDTO>(
    mapper,
    CreateDataExportOrganizationResponseDto,
    FrontendCreateReportDTO,
    MapReportStateFromOrgServiceResponse,
    MapReportIdFromOrgServiceResponse,
    MapDateCreatedFromOrgServiceResponse,
    MapMessageFromOrgServiceResponse,
  );
