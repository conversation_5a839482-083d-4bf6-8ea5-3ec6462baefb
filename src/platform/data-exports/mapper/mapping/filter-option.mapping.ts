import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  Mapping,
  MappingConfiguration,
  preCondition,
} from '@automapper/core';
import {
  BackendFilterOption,
  FrontendFilterOption,
} from '../../dto/createFilteredReportRequest.dto';
import {
  FrontendToBackendReportType,
  FrontendToBackendState,
  FrontendToBackendStatus,
} from '../../data-export.constants';
import { FrontendStatusEnum } from '../../enum/frontend-status.enum';
import { FilterOption } from '@vidmob/vidmob-organization-service-sdk';
import ReportStatusesEnum = FilterOption.ReportStatusesEnum;

const MapSearchToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.search,
  preCondition((s) => !!s.search, undefined),
  mapFrom((s) => s.search),
);

const MapCreatedByIdToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.createdByIds,
  preCondition((s) => !!s.createdByIds, undefined),
  mapFrom((s) => s.createdByIds),
);

const MapCreatedOnDatesToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.createdOnDates,
  preCondition((s) => !!s.createdOnDates, undefined),
  mapFrom((s) => s.createdOnDates),
);

const MapStartDateToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.startDate,
  preCondition((s) => !!s.startDate, undefined),
  mapFrom((s) => s.startDate),
);

const MapEndDateToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.endDate,
  preCondition((s) => !!s.endDate, undefined),
  mapFrom((s) => s.endDate),
);

const MapReportNamesToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.reportNames,
  preCondition((s) => !!s.reportNames, undefined),
  mapFrom((s) => s.reportNames),
);

/**
 * Converts a Frontend Status display name to its backend representation
 * Note that Frontend "Processing" maps started, queued, and processing in
 * the backend.
 * @param statuses
 */
const convertStatusToBackend = (
  statuses: FrontendStatusEnum[],
): ReportStatusesEnum[] => {
  const backendStatuses = new Set<ReportStatusesEnum>();
  for (const status of statuses) {
    const backendStatus = FrontendToBackendStatus[status];
    if (Array.isArray(backendStatus)) {
      backendStatus.forEach((status) => backendStatuses.add(status));
    } else {
      backendStatuses.add(<ReportStatusesEnum>backendStatus);
    }
  }
  return Array.from(backendStatuses);
};

const MapReportTypeToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.reportTypes,
  preCondition((s) => !!s.reportTypes && s.reportTypes.length > 0, undefined),
  mapFrom((s) => s.reportTypes?.map((rt) => FrontendToBackendReportType[rt])),
);

const MapStatusToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.reportStatuses,
  preCondition(
    (s) => !!s.reportStatuses && s.reportStatuses.length > 0,
    undefined,
  ),
  mapFrom((s) =>
    s.reportStatuses ? convertStatusToBackend(s.reportStatuses) : undefined,
  ),
);

const MapStateToBackendName: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.reportStates,
  preCondition((s) => !!s.reportStates && s.reportStates.length > 0, undefined),
  mapFrom((s) => s.reportStates?.map((state) => FrontendToBackendState[state])),
);

const MapReportIdsToBackend: MappingConfiguration<
  FrontendFilterOption,
  BackendFilterOption
> = forMember<FrontendFilterOption, BackendFilterOption>(
  (d) => d.reportIds,
  preCondition((s) => !!s.reportIds, undefined),
  mapFrom((s) => s.reportIds),
);

export const MapFrontendFilterOptionsToBackend: (
  mapper: Mapper,
) => Mapping<FrontendFilterOption, BackendFilterOption> = (mapper: Mapper) =>
  createMap<FrontendFilterOption, BackendFilterOption>(
    mapper,
    FrontendFilterOption,
    BackendFilterOption,
    MapReportTypeToBackendName,
    MapStatusToBackendName,
    MapStateToBackendName,
    MapReportIdsToBackend,
    MapReportNamesToBackendName,
    MapSearchToBackendName,
    MapCreatedByIdToBackendName,
    MapCreatedOnDatesToBackendName,
    MapStartDateToBackendName,
    MapEndDateToBackendName,
  );
