import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  Mapping,
  MappingConfiguration,
  preCondition,
} from '@automapper/core';
import {
  BackendFilterValues,
  FrontendFilterValues,
} from '../../dto/getFilteredValueResponse.dto';
import {
  BackendToFrontendReportType,
  BackendToFrontendStatus,
} from '../../data-export.constants';

const MapExportTypeToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.exportType,
  preCondition((s) => s.exportType?.length > 0, []),
  mapFrom((s) => s.exportType.map((rt) => BackendToFrontendReportType[rt])),
);
const MapCreatedOnToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.createdOn,
  preCondition((s) => s.createdOn?.length > 0, []),
  mapFrom((s) => s.createdOn),
);

const MapCreatedByToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.createdBy,
  preCondition((s) => s.createdBy?.length > 0, []),
  mapFrom((s) => s.createdBy),
);

const MapStatusToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.status,
  preCondition((s) => s.status?.length > 0, []),
  mapFrom((s) =>
    Array.from(new Set(s.status.map((s) => BackendToFrontendStatus[s]))),
  ),
);

const MapChannelToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.channel,
  preCondition((s) => !!s.channel, []),
  mapFrom((s) => s.channel),
);

const MapWorkspaceToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.workspace,
  preCondition((s) => !!s.workspace, []),
  mapFrom((s) => s.workspace),
);

const MapAdAccountToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.adAccount,
  preCondition((s) => !!s.adAccount, []),
  mapFrom((s) => s.adAccount),
);

const MapMarketToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.market,
  preCondition((s) => !!s.market, []),
  mapFrom((s) => s.market),
);

const MapBrandToFrontend: MappingConfiguration<
  BackendFilterValues,
  FrontendFilterValues
> = forMember<BackendFilterValues, FrontendFilterValues>(
  (d) => d.brand,
  preCondition((s) => !!s.brand, []),
  mapFrom((s) => s.brand),
);

export const MapFilterValueToFrontendDto: (
  mapper: Mapper,
) => Mapping<BackendFilterValues, FrontendFilterValues> = (mapper: Mapper) =>
  createMap(
    mapper,
    BackendFilterValues,
    FrontendFilterValues,
    MapExportTypeToFrontend,
    MapCreatedOnToFrontend,
    MapCreatedByToFrontend,
    MapStatusToFrontend,
    MapChannelToFrontend,
    MapWorkspaceToFrontend,
    MapAdAccountToFrontend,
    MapMarketToFrontend,
    MapBrandToFrontend,
  );
