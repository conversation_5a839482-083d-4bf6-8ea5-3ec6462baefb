import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Mapper, MappingProfile } from '@automapper/core';
import { MapDataExportRequestFromBFFToOrganizationService } from './mapping/data-export-request.mapping';
import { MapOrganizationServiceDataExportResponseToBFF } from './mapping/data-export-response.mapping';
import {
  MapBasicReadDTOToFrontendDTO,
  MapFullReadDTOToFrontendDTO,
} from './mapping/get-data-export-metadata-response.mapping';
import { MapFrontendFilterOptionsToBackend } from './mapping/filter-option.mapping';
import { MapFilterValueToFrontendDto } from './mapping/data-export-filter-values.mapping';

export class DataExportsProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }
  get profile(): MappingProfile {
    return (mapper: Mapper) => {
      MapDataExportRequestFromBFFToOrganizationService(mapper);
      MapOrganizationServiceDataExportResponseToBFF(mapper);
      MapBasicReadDTOToFrontendDTO(mapper);
      MapFullReadDTOToFrontendDTO(mapper);
      MapFrontendFilterOptionsToBackend(mapper);
      MapFilterValueToFrontendDto(mapper);
    };
  }
}
