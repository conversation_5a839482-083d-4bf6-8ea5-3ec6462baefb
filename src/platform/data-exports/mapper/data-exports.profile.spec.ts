import { classes } from '@automapper/classes';
import { Mapper } from '@automapper/core';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { Test, TestingModule } from '@nestjs/testing';
import { DataExportsProfile } from './data-exports.profile';
import { MOCK_BFF_REQUESTS } from './data-exports.profile.test-cases';
import {
  CreateDataExportOrgResponse,
  generateDataExportsMockCreateResponse,
} from '../data/dataExportsMockData';
import {
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO,
} from '../dto/createDataExportBFFResponse.dto';
import {
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto,
  FrontEndFullReadReportDto,
  ReadDataExportReportOrganizationDto,
} from '../dto/getAllDataExportsResponse.dto';
import {
  BackendFilterOption,
  FrontendFilterOption,
} from '../dto/createFilteredReportRequest.dto';
import { FrontendStateEnum } from '../enum/frontend-state.enum';
import { FrontendStatusEnum } from '../enum/frontend-status.enum';
import { FrontendReportTypeEnum } from '../enum/frontend-report-type.enum';
import { FilterOption } from '@vidmob/vidmob-organization-service-sdk';
import ReportStatusesEnum = FilterOption.ReportStatusesEnum;
import ReportTypesEnum = FilterOption.ReportTypesEnum;
import ReportStatesEnum = FilterOption.ReportStatesEnum;

describe('DataExportsProfile', () => {
  let mapper: Mapper;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [DataExportsProfile],
    }).compile();
    mapper = module.get<Mapper>(getMapperToken());
  });

  it.each(MOCK_BFF_REQUESTS)(
    'should map Org Service Response to BFF Response',
    (dataExportRequest) => {
      const input = CreateDataExportOrgResponse(dataExportRequest);
      const expectedOutput =
        generateDataExportsMockCreateResponse(dataExportRequest);
      const actual = mapper.map<
        CreateDataExportOrganizationResponseDto,
        FrontendCreateReportDTO
      >(
        input,
        CreateDataExportOrganizationResponseDto,
        FrontendCreateReportDTO,
      );
      expect(actual).toBeInstanceOf(FrontendCreateReportDTO);
      expect(actual).toEqual(expectedOutput);
    },
  );

  it('should map dto to new dto', () => {
    const dtoFromOrg: ReadDataExportReportOrganizationDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'CREATIVE_SCORING',
      status: 'COMPLETED',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      downloadUrl: 's3://test/report/uri',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      expiration: '2024-01-01',
      workspaces: [{ id: '1', name: 'test-workspace' }],
      channels: ['meta'],
      brands: [{ id: 'brand1', name: 'Brand 1' }],
      markets: [{ id: '1', name: 'Test Market' }],
      adAccounts: [{ id: '1', name: 'Test Ad Account' }],
    };

    const actual = mapper.map<
      ReadDataExportReportOrganizationDto,
      FrontEndFullReadReportDto
    >(
      dtoFromOrg,
      ReadDataExportReportOrganizationDto,
      FrontEndFullReadReportDto,
    );

    expect(actual).toBeInstanceOf(FrontEndFullReadReportDto);
    expect(actual).toEqual({
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'Creative Scoring',
      status: 'Completed',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      downloadUrl: 's3://test/report/uri',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      expiration: '2024-01-01',
      workspaces: [{ id: '1', name: 'test-workspace' }],
      channels: ['meta'],
      markets: [{ id: '1', name: 'Test Market' }],
      adAccounts: [{ id: '1', name: 'Test Ad Account' }],
      brands: [{ id: 'brand1', name: 'Brand 1' }],
    });
  });

  it('should map started dto to frontend dto', () => {
    const dtoFromOrg: BasicReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'CREATIVE_SCORING',
      status: 'STARTED',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
    };

    const actual = mapper.map<
      BasicReadDataExportReportDto,
      FrontEndBasicReadReportDto
    >(dtoFromOrg, BasicReadDataExportReportDto, FrontEndBasicReadReportDto);

    expect(actual).toBeInstanceOf(FrontEndBasicReadReportDto);
    expect(actual).toEqual({
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'Creative Scoring',
      status: 'Processing',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
    });
  });

  it('should map failed dto to frontend dto', () => {
    const dtoFromOrg: BasicReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'CREATIVE_SCORING',
      status: 'FAILED',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      failureReason: 'Test Reason',
    };

    const actual = mapper.map<
      BasicReadDataExportReportDto,
      FrontEndBasicReadReportDto
    >(dtoFromOrg, BasicReadDataExportReportDto, FrontEndBasicReadReportDto);

    expect(actual).toBeInstanceOf(FrontEndBasicReadReportDto);
    expect(actual).toEqual({
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'Creative Scoring',
      status: 'Failed',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      failureReason: 'Test Reason',
    });
  });

  it('should map completed no data dto to frontend dto', () => {
    const dtoFromOrg: BasicReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'CREATIVE_SCORING',
      status: 'COMPLETED',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      recordCount: 0,
      hasData: false,
    };

    const actual = mapper.map<
      BasicReadDataExportReportDto,
      FrontEndBasicReadReportDto
    >(dtoFromOrg, BasicReadDataExportReportDto, FrontEndBasicReadReportDto);

    expect(actual).toBeInstanceOf(FrontEndBasicReadReportDto);
    expect(actual).toEqual({
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'Creative Scoring',
      status: 'Completed',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      recordCount: 0,
      hasData: false,
    });
  });

  it('should map completed dto to frontend dto', () => {
    const dtoFromOrg: BasicReadDataExportReportDto = {
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'CREATIVE_SCORING',
      status: 'COMPLETED',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      recordCount: 12421241,
      hasData: true,
    };

    const actual = mapper.map<
      BasicReadDataExportReportDto,
      FrontEndBasicReadReportDto
    >(dtoFromOrg, BasicReadDataExportReportDto, FrontEndBasicReadReportDto);

    expect(actual).toBeInstanceOf(FrontEndBasicReadReportDto);
    expect(actual).toEqual({
      reportId: '7498d2cd-46fb-4b6a-8b3f-9ce11a492086',
      reportName: 'test',
      reportType: 'Creative Scoring',
      status: 'Completed',
      dateCreated: 'Jul 24, 2024',
      startDate: '2024-01-01',
      endDate: '2024-07-01',
      createdBy: {
        id: '1',
        name: 'Test Username',
      },
      recordCount: 12421241,
      hasData: true,
    });
  });

  it('should map filter option from frontend to backend', () => {
    const input: FrontendFilterOption = {
      search: 'test',
      reportIds: ['test_id_1'],
      reportStates: [FrontendStateEnum.ACTIVE],
      reportStatuses: [
        FrontendStatusEnum.PROCESSING,
        FrontendStatusEnum.COMPLETED,
      ],
      reportTypes: [FrontendReportTypeEnum.CREATIVE_SCORING],
      createdOnDates: ['08-12-2023', '08-19-2023'],
      createdByIds: [1, 2],
    };
    const actual = mapper.map(input, FrontendFilterOption, BackendFilterOption);
    expect(actual).toBeInstanceOf(BackendFilterOption);
    expect(actual).toEqual({
      search: 'test',
      reportIds: ['test_id_1'],
      reportStates: [ReportStatesEnum.Active],
      reportStatuses: [
        ReportStatusesEnum.Started,
        ReportStatusesEnum.Queued,
        ReportStatusesEnum.Processing,
        ReportStatusesEnum.Completed,
      ],
      reportTypes: [ReportTypesEnum.Scoring],
      createdByIds: [1, 2],
      createdOnDates: ['08-12-2023', '08-19-2023'],
    });
  });
});
