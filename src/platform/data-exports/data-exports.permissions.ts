import { organizationFromParamsHandler } from 'src/auth/decorators/permission.decorator';
import { PermissionAction } from 'src/auth/enums/permission.action.enum';
import { PermissionDomain } from 'src/auth/enums/permission.domain.enum';
import { PermissionSubResource } from 'src/auth/enums/permission.subresource.enum';

export const readDataExportsPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DATA_EXPORT,
    },
  ],
};

export const createDataExportsPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.DATA_EXPORT,
    },
  ],
};

export const deleteDataExportsPermission = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.DATA_EXPORT,
    },
  ],
};
