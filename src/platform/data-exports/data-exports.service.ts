import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  DataExportService as OrganizationDataExportService,
  GetFilters200Response,
} from '@vidmob/vidmob-organization-service-sdk';
import { CreateDataExportRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportRequestDto';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import {
  CreateDataExportOrganizationResponseDto,
  FrontendCreateReportDTO,
} from './dto/createDataExportBFFResponse.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  BasicReadDataExportReportDto,
  FrontEndBasicReadReportDto,
  FrontEndFullReadReportDto,
  ReadDataExportReportOrganizationDto,
} from './dto/getAllDataExportsResponse.dto';
import { FilterSortByOptions } from '@vidmob/vidmob-organization-service-sdk/dist/model/filterSortByOptions';
import {
  BackendFilterOption,
  CreateFilteredReportRequestDto,
  FrontendFilterOption,
} from './dto/createFilteredReportRequest.dto';
import {
  BackendFilterValues,
  FrontendFilterValues,
} from './dto/getFilteredValueResponse.dto';
import { CreateDataExportResponseDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportResponseDto';
import { GetAllScheduledExportsRequestDto } from './dto/getAllScheduledExportsRequest.dto';
import { MockScheduledDataExportsGetAllResponse } from './data/dataExportsMockData';
import { searchMockData } from '../../utils/search-mock-data';
import { sortMockData } from '../../utils/sort-mock-data';
import { paginateMockData } from '../../utils/paginate-mock-data';

@Injectable()
export class DataExportsService {
  constructor(
    @InjectMapper() private readonly classMapper: Mapper,
    private readonly organizationDataExportService: OrganizationDataExportService,
  ) {}
  async getFilteredReports(
    orgId: string,
    filterSortByOptions: CreateFilteredReportRequestDto,
    pagination: PaginationOptions,
  ) {
    const filterOption =
      this.convertToBackendFilteredReportRequestDto(filterSortByOptions);
    const response =
      await this.organizationDataExportService.getFilteredReportsAsPromise(
        orgId,
        filterOption,
        pagination.offset,
        pagination.perPage,
      );
    const dtos = this.classMapper.mapArray(
      response.result,
      BasicReadDataExportReportDto,
      FrontEndBasicReadReportDto,
    );
    return new PaginatedResultArray(dtos, response.pagination?.totalSize);
  }

  async getAllScheduledExports(
    orgId: string,
    filterSortByOptions: GetAllScheduledExportsRequestDto,
    pagination: PaginationOptions,
  ) {
    const mockData = MockScheduledDataExportsGetAllResponse;

    const searchedData = filterSortByOptions?.filter?.search
      ? searchMockData(mockData, filterSortByOptions.filter.search, [
          'exportName',
        ])
      : mockData;

    const sortOptions =
      filterSortByOptions?.sort && filterSortByOptions.sort.length > 0
        ? filterSortByOptions.sort[0]
        : null;

    const sortedData = sortOptions
      ? sortMockData(searchedData, sortOptions.field, sortOptions.sortOrder)
      : searchedData;

    return paginateMockData(sortedData, pagination);
  }

  async getFilterInformation(
    organizationId: string,
    startDate?: string,
    endDate?: string,
  ) {
    const response: GetFilters200Response =
      await this.organizationDataExportService.getFiltersAsPromise(
        organizationId,
        startDate,
        endDate,
      );
    const results = response.result;
    return this.classMapper.map(
      results,
      BackendFilterValues,
      FrontendFilterValues,
    );
  }

  async deleteReport(organizationId: string, reportId: string) {
    await this.organizationDataExportService.deleteDataExportAsPromise(
      organizationId,
      reportId,
    );
  }

  async getAllDataExportsForOrg(
    organizationId: string,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<FrontEndBasicReadReportDto>> {
    const { offset, perPage } = paginationOptions;
    const metadataResponse =
      await this.organizationDataExportService.listDataExportsAsPromise(
        organizationId,
        offset,
        perPage,
      );

    const dtos = this.classMapper.mapArray(
      metadataResponse.result,
      BasicReadDataExportReportDto,
      FrontEndBasicReadReportDto,
    );
    return new PaginatedResultArray(
      dtos,
      metadataResponse.pagination?.totalSize,
    );
  }

  async getDataExportById(
    organizationId: string,
    reportId: string,
  ): Promise<FrontEndFullReadReportDto> {
    const response =
      await this.organizationDataExportService.getDataExportAsPromise(
        organizationId,
        reportId,
      );
    return this.classMapper.map(
      response.result,
      ReadDataExportReportOrganizationDto,
      FrontEndFullReadReportDto,
    );
  }

  /**
   * Create a new Data Export job by calling the organization service
   * The function will need the userId, orgId, and the request body from
   * the data export controller
   * @param userId
   * @param orgId
   * @param body
   */
  async createOrganizationDataExport(
    userId: number,
    orgId: string,
    body: CreateDataExportRequestDto,
  ) {
    // Call the organization service
    const response = await this.startDataExport(orgId, userId, body);

    const dto = this.classMapper.map(
      response.result,
      CreateDataExportOrganizationResponseDto,
      FrontendCreateReportDTO,
    );
    // Return 200 OK Response
    return dto;
  }

  /**
   * Start the data export job in the organization service.
   * Note that the request object is different from the request body found in
   * the controller endpoint
   *
   * @param organizationId
   * @param userId
   * @param requestDto
   * @private
   */
  private async startDataExport(
    organizationId: string,
    userId: number,
    requestDto: CreateDataExportRequestDto,
  ): Promise<{ status: string; result: CreateDataExportResponseDto }> {
    return (await this.organizationDataExportService.createDataExportAsPromise(
      organizationId,
      userId,
      requestDto,
    )) as unknown as { status: string; result: CreateDataExportResponseDto };
  }

  /**
   * Converts the frontend filter name to backend FilterSortByOptions object
   * @param dto
   * @private
   */
  private convertToBackendFilteredReportRequestDto(
    dto: CreateFilteredReportRequestDto,
  ): FilterSortByOptions {
    const filterOption = !!dto
      ? this.classMapper.map(
          dto.filter,
          FrontendFilterOption,
          BackendFilterOption,
        )
      : {};
    return { filter: filterOption, sort: dto.sort };
  }
}
