import { Module } from '@nestjs/common';
import { DataExportsController } from './data-exports.controller';
import { DataExportsService } from './data-exports.service';
import { DataExportsProfile } from './mapper/data-exports.profile';

@Module({
  controllers: [DataExportsController],
  providers: [DataExportsService, DataExportsProfile],
  exports: [DataExportsService],
})
export class DataExportsModule {}
