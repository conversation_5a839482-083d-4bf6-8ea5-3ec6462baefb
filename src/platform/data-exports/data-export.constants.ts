import { CreateDataExportRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportRequestDto';
import { FilterOption } from '@vidmob/vidmob-organization-service-sdk';
import ReportStatusesEnum = FilterOption.ReportStatusesEnum;
import ReportTypesEnum = FilterOption.ReportTypesEnum;
import { FrontendStatusEnum } from './enum/frontend-status.enum';
import { FrontendReportTypeEnum } from './enum/frontend-report-type.enum';
import ReportStatesEnum = FilterOption.ReportStatesEnum;
import { FrontendStateEnum } from './enum/frontend-state.enum';

type DataExportTypeReportTypeMapping = {
  [export_type: string]: CreateDataExportRequestDto.ReportTypeEnum;
};
export const DataExportTypeMap: DataExportTypeReportTypeMapping = {
  scoring: ReportTypesEnum.Scoring,
};

export enum DataExportStatusEnum {
  OK = 'OK',
  ERROR = 'ERROR',
}

export const BackendToFrontendStatus: Record<
  ReportStatusesEnum,
  FrontendStatusEnum
> = {
  COMPLETED: FrontendStatusEnum.COMPLETED,
  DELETED: FrontendStatusEnum.DELETED,
  FAILED: FrontendStatusEnum.FAILED,
  PROCESSING: FrontendStatusEnum.PROCESSING,
  QUEUED: FrontendStatusEnum.PROCESSING,
  STARTED: FrontendStatusEnum.PROCESSING,
  ARCHIVED: FrontendStatusEnum.ARCHIVED,
};

export const FrontendToBackendStatus: Record<
  FrontendStatusEnum,
  ReportStatusesEnum | ReportStatusesEnum[]
> = {
  [FrontendStatusEnum.COMPLETED]: ReportStatusesEnum.Completed,
  [FrontendStatusEnum.PROCESSING]: [
    ReportStatusesEnum.Started,
    ReportStatusesEnum.Queued,
    ReportStatusesEnum.Processing,
  ],
  [FrontendStatusEnum.FAILED]: ReportStatusesEnum.Failed,
  [FrontendStatusEnum.DELETED]: ReportStatusesEnum.Deleted,
  [FrontendStatusEnum.ARCHIVED]: ReportStatusesEnum.Archived,
};

export const BackendToFrontendReportType: Record<
  ReportTypesEnum,
  FrontendReportTypeEnum
> = {
  CREATIVE_SCORING: FrontendReportTypeEnum.CREATIVE_SCORING,
  CREATIVE_ANALYTICS: FrontendReportTypeEnum.CREATIVE_ANALYTICS,
  CREATIVE_ELEMENTS: FrontendReportTypeEnum.CREATIVE_ELEMENTS,
};

export const FrontendToBackendReportType: Record<
  FrontendReportTypeEnum,
  ReportTypesEnum
> = {
  [FrontendReportTypeEnum.CREATIVE_SCORING]: ReportTypesEnum.Scoring,
  [FrontendReportTypeEnum.CREATIVE_ANALYTICS]: ReportTypesEnum.Analytics,
  [FrontendReportTypeEnum.CREATIVE_ELEMENTS]: ReportTypesEnum.Elements,
};

export const BackendToFrontendState: Record<
  ReportStatesEnum,
  FrontendStateEnum
> = {
  INACTIVE: FrontendStateEnum.INACTIVE,
  ACTIVE: FrontendStateEnum.ACTIVE,
};

export const FrontendToBackendState: Record<
  FrontendStateEnum,
  ReportStatesEnum
> = {
  [FrontendStateEnum.INACTIVE]: ReportStatesEnum.Inactive,
  [FrontendStateEnum.ACTIVE]: ReportStatesEnum.Active,
};
