import { AutoMap } from '@automapper/classes';
import { IdAndNameDto } from './idAndName.dto';
import {
  IsArray,
  IsDateString,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { CreateDataExportRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportRequestDto';
export class CreateDataExportOrganizationRequestDto
  implements CreateDataExportRequestDto
{
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  reportType: CreateDataExportRequestDto.ReportTypeEnum;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  reportName: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  @AutoMap(() => [Number])
  @IsArray()
  @IsNotEmpty()
  workspaces: number[];

  @AutoMap(() => [String])
  @IsNotEmpty()
  @IsArray()
  channels: string[];

  @AutoMap(() => [String])
  @IsOptional()
  @IsArray()
  brands?: string[];

  @AutoMap(() => [String])
  @IsOptional()
  @IsArray()
  markets?: string[];

  @AutoMap(() => [String])
  @IsOptional()
  @IsArray()
  adAccounts?: string[];
}

export class CreateDataExportBFFRequestDto {
  @AutoMap(() => [IdAndNameDto])
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IdAndNameDto)
  @ApiProperty({
    type: [IdAndNameDto],
    description: 'List of ad accounts',
    example: [{ id: '12345', name: 'Account Name' }],
  })
  ad_accounts: IdAndNameDto[];

  @AutoMap(() => [IdAndNameDto])
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IdAndNameDto)
  @ApiProperty({
    type: [IdAndNameDto],
    description: 'List of brands',
    example: [{ id: '12345', name: 'Brand Name' }],
  })
  brands: IdAndNameDto[];

  @AutoMap()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  @ApiProperty({
    type: [String],
    description: 'List of channels i.e. platforms',
    example: ['facebook', 'tiktok'],
  })
  channels: string[];

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({
    example: '2024-07-09T00:00:00Z',
    description: 'The end of the report date range',
  })
  end_date: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({
    example: '2024-07-09T00:00:00Z',
    description: 'The beginning of the report date range',
  })
  start_date: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: 'scoring',
    description: 'The type of export',
  })
  export_type: string;

  @AutoMap(() => [IdAndNameDto])
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IdAndNameDto)
  @ApiProperty({
    type: [IdAndNameDto],
    description: 'List of markets',
    example: [{ id: '12345', name: 'Botswana' }],
  })
  markets: IdAndNameDto[];

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: 'Test Report 22',
    description: 'The name of the export',
  })
  name: string;

  @AutoMap(() => [IdAndNameDto])
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IdAndNameDto)
  @ApiProperty({
    type: [IdAndNameDto],
    description: 'List of workspaces',
    example: [{ id: '12345', name: 'Workspace Name' }],
  })
  workspaces: IdAndNameDto[];
}
