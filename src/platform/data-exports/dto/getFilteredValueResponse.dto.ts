import { IdAndName } from '@vidmob/vidmob-organization-service-sdk/dist/model/idAndName';
import { DataExportFilterValue } from '@vidmob/vidmob-organization-service-sdk/dist/model/dataExportFilterValue';
import { FrontendStatusEnum } from '../enum/frontend-status.enum';
import { FrontendReportTypeEnum } from '../enum/frontend-report-type.enum';
import { AutoMap } from '@automapper/classes';
import { IsArray, IsEnum, IsOptional, IsString } from 'class-validator';

export class BackendFilterValues implements DataExportFilterValue {
  @AutoMap()
  @IsArray()
  @IsEnum(DataExportFilterValue.ExportTypeEnum)
  exportType: Array<DataExportFilterValue.ExportTypeEnum>;

  @AutoMap()
  @IsArray()
  createdOn: Array<string>;

  @AutoMap()
  @IsArray()
  createdBy: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsEnum(DataExportFilterValue.StatusEnum)
  status: Array<DataExportFilterValue.StatusEnum>;

  @AutoMap()
  @IsArray()
  @IsString()
  @IsOptional()
  channel?: Array<string>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  workspace?: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  adAccount?: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  market?: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  brand?: Array<IdAndName>;
}

export class FrontendFilterValues {
  @AutoMap()
  @IsArray()
  @IsEnum(FrontendReportTypeEnum)
  exportType: Array<FrontendReportTypeEnum>;

  @AutoMap()
  @IsArray()
  @IsString()
  createdOn: Array<string>;

  @AutoMap()
  @IsArray()
  createdBy: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsEnum(FrontendStatusEnum)
  status: Array<FrontendStatusEnum>;

  @AutoMap()
  @IsArray()
  @IsString()
  @IsOptional()
  channel?: Array<string>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  workspace?: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  adAccount?: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  market?: Array<IdAndName>;

  @AutoMap()
  @IsArray()
  @IsOptional()
  brand?: Array<IdAndName>;
}
