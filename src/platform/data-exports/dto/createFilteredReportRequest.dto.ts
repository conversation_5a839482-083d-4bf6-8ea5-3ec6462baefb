import { SortByOption } from '@vidmob/vidmob-organization-service-sdk/dist/model/sortByOption';
import { FilterOption } from '@vidmob/vidmob-organization-service-sdk/dist/model/filterOption';
import { FrontendReportTypeEnum } from '../enum/frontend-report-type.enum';
import { FrontendStateEnum } from '../enum/frontend-state.enum';
import { FrontendStatusEnum } from '../enum/frontend-status.enum';
import { AutoMap } from '@automapper/classes';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';

export class BackendFilterOption implements FilterOption {
  @AutoMap()
  @IsString()
  @IsOptional()
  search?: string;

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  reportIds?: Array<string>;

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  reportNames?: Array<string>;

  @AutoMap()
  @IsArray()
  @IsEnum(() => FilterOption.ReportTypesEnum)
  @IsOptional()
  reportTypes?: Array<FilterOption.ReportTypesEnum>;

  @AutoMap()
  @IsArray()
  @IsEnum(() => FilterOption.ReportStatesEnum)
  @IsOptional()
  reportStates?: Array<FilterOption.ReportStatesEnum>;

  @AutoMap()
  @IsArray()
  @IsEnum(() => FilterOption.ReportStatusesEnum)
  @IsOptional()
  reportStatuses?: Array<FilterOption.ReportStatusesEnum>;

  @AutoMap()
  @IsArray()
  @IsDateString()
  @IsOptional()
  createdOnDates?: Array<string>;

  @AutoMap()
  @IsArray()
  @IsNumber()
  @IsOptional()
  createdByIds?: Array<number>;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

export class FrontendFilterOption {
  @AutoMap()
  @IsString()
  @IsOptional()
  search?: string;

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  reportIds?: Array<string>;

  @AutoMap()
  @IsArray()
  @Type(() => String)
  @IsOptional()
  reportNames?: Array<string>;

  @AutoMap()
  @IsArray()
  @IsEnum(() => FrontendReportTypeEnum)
  @IsOptional()
  reportTypes?: Array<FrontendReportTypeEnum>;

  @AutoMap()
  @IsArray()
  @IsEnum(() => FrontendStateEnum)
  @IsOptional()
  reportStates?: Array<FrontendStateEnum>;

  @AutoMap()
  @IsArray()
  @IsEnum(() => FrontendStatusEnum)
  @IsOptional()
  reportStatuses?: Array<FrontendStatusEnum>;

  @AutoMap()
  @IsArray()
  @IsDateString()
  @IsOptional()
  createdOnDates?: Array<string>;

  @AutoMap()
  @IsArray()
  @IsNumber()
  @IsOptional()
  createdByIds?: Array<number>;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  endDate?: string;
}

export class CreateFilteredReportRequestDto {
  @AutoMap()
  @IsObject()
  @Type(() => FrontendFilterOption)
  filter: FrontendFilterOption;

  @AutoMap()
  @IsArray()
  @IsOptional()
  sort?: Array<SortByOption>;
}
