import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  IsDateString,
  IsOptional,
  IsEnum,
  IsObject,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AutoMap } from '@automapper/classes';
import { ReadDataExportReportDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readDataExportReportDto';
import { FrontendStatusEnum } from '../enum/frontend-status.enum';
import { FrontendReportTypeEnum } from '../enum/frontend-report-type.enum';

export class IdAndName {
  id: string;
  name: string;
}

export class BasicReadDataExportReportDto
  implements Partial<ReadDataExportReportDto>
{
  @AutoMap()
  @IsString()
  reportId: string;

  @AutoMap()
  @IsString()
  reportName: string;

  @AutoMap()
  @IsEnum(ReadDataExportReportDto.ReportTypeEnum)
  reportType: ReadDataExportReportDto.ReportTypeEnum;

  @AutoMap()
  @IsEnum(ReadDataExportReportDto.StatusEnum)
  status: ReadDataExportReportDto.StatusEnum;

  @AutoMap(() => [IdAndName])
  @IsObject()
  @Type(() => IdAndName)
  createdBy: IdAndName;

  @AutoMap()
  @IsDateString()
  dateCreated: string;

  @AutoMap()
  @IsDateString()
  startDate: string;

  @AutoMap()
  @IsDateString()
  endDate: string;

  @AutoMap()
  @IsNumber()
  @IsOptional()
  recordCount?: number;

  @AutoMap()
  @IsBoolean()
  @IsOptional()
  hasData?: boolean;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  expiration?: string;

  @AutoMap()
  @IsOptional()
  failureReason?: string;
}

export class ReadDataExportReportOrganizationDto
  extends BasicReadDataExportReportDto
  implements ReadDataExportReportDto
{
  @AutoMap(() => [IdAndName])
  @IsArray()
  adAccounts?: Array<IdAndName>;

  @AutoMap(() => [IdAndName])
  @IsArray()
  brands?: Array<IdAndName>;

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => String)
  channels: string[];

  @AutoMap(() => [IdAndName])
  @IsArray()
  @IsOptional()
  markets?: Array<IdAndName>;

  @AutoMap(() => [IdAndName])
  @IsArray()
  workspaces: Array<IdAndName>;

  @AutoMap()
  @IsOptional()
  downloadUrl?: string;
}

export class FrontEndBasicReadReportDto {
  @AutoMap()
  @IsString()
  reportId: string;

  @AutoMap()
  @IsString()
  reportName: string;

  @AutoMap()
  @IsEnum(FrontendReportTypeEnum)
  reportType: FrontendReportTypeEnum;

  @AutoMap()
  @IsEnum(FrontendStatusEnum)
  status: FrontendStatusEnum;

  @AutoMap(() => [IdAndName])
  @IsObject()
  @Type(() => IdAndName)
  createdBy: IdAndName;

  @AutoMap()
  @IsDateString()
  dateCreated: string;

  @AutoMap()
  @IsDateString()
  startDate: string;

  @AutoMap()
  @IsDateString()
  endDate: string;

  @AutoMap()
  @IsNumber()
  @IsOptional()
  recordCount?: number;

  @AutoMap()
  @IsBoolean()
  @IsOptional()
  hasData?: boolean;

  @AutoMap()
  @IsDateString()
  @IsOptional()
  expiration?: string;

  @AutoMap()
  @IsOptional()
  failureReason?: string;
}

export class FrontEndFullReadReportDto extends FrontEndBasicReadReportDto {
  @AutoMap(() => [IdAndName])
  @IsArray()
  adAccounts?: Array<IdAndName>;

  @AutoMap(() => [IdAndName])
  @IsArray()
  brands?: Array<IdAndName>;

  @AutoMap()
  @IsArray({ each: true })
  @Type(() => String)
  channels?: string[];

  @AutoMap(() => [IdAndName])
  @IsArray()
  @IsOptional()
  markets?: Array<IdAndName>;

  @AutoMap()
  @IsOptional()
  downloadUrl?: string;

  @AutoMap(() => [IdAndName])
  @IsArray()
  workspaces: Array<IdAndName>;
}
