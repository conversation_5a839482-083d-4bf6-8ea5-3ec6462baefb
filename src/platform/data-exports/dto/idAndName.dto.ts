import { IsString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';

export class IdAndNameDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: '12345',
    description: 'The unique identifier of the entity',
  })
  id: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    example: 'Example Name',
    description: 'The name of the entity',
  })
  name: string;
}
