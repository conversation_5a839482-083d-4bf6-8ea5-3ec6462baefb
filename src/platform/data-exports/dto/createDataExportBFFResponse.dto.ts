import { IsDateString, IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CreateDataExportResponseDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createDataExportResponseDto';
import { AutoMap } from '@automapper/classes';
import { FrontendStatusEnum } from '../enum/frontend-status.enum';

export class CreateDataExportOrganizationResponseDto
  implements CreateDataExportResponseDto
{
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Unique identifier for the report',
    example: '1251asd-asfd-1355-asd2-13536fdsfg',
  })
  reportId: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Report status',
    example: 'QUEUED',
  })
  status: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Success or error message about the request',
    example: 'Export data requested successfully',
  })
  message: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Date the export was created',
    example: '2024-05-29 22:46:46',
  })
  dateCreated: string;
}

export class FrontendCreateReportDTO {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Unique identifier for the report',
    example: '1251asd-asfd-1355-asd2-13536fdsfg',
  })
  reportId: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Report status',
    example: 'Processing',
  })
  status: FrontendStatusEnum;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Success or error message about the request',
    example: 'Export data requested successfully',
  })
  message: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Date the export was created',
    example: '2024-05-29 22:46:46',
  })
  dateCreated: string;
}

export class CreateDataExportBFFResponseDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Unique identifier for the report',
    example: '1251asd-asfd-1355-asd2-13536fdsfg',
  })
  report_id: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Report status',
    example: 'QUEUED',
  })
  status: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Success or error message about the request',
    example: 'Export data requested successfully',
  })
  message: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  @ApiProperty({
    type: String,
    description: 'Date the export was created',
    example: '2024-05-29 22:46:46',
  })
  date_created: string;
}
