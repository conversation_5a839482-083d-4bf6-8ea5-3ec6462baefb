import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { Type } from 'class-transformer';
import { FrontendReportTypeEnum } from '../enum/frontend-report-type.enum';

enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

class SortByOption {
  @AutoMap()
  @IsString()
  field: string;

  @AutoMap()
  @IsEnum(SortOrder)
  sortOrder: SortOrder;
}

class PersonDto {
  @AutoMap()
  @IsString()
  id: string;

  @AutoMap()
  @IsString()
  name: string;

  @AutoMap()
  @IsString()
  url: string;
}

export class GetAllScheduledExportsFilterOption {
  @AutoMap()
  @IsString()
  @IsOptional()
  search?: string;

  @AutoMap(() => [String])
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  reportIds?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  exportNames?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsString({ each: true })
  @IsEnum(FrontendReportTypeEnum, { each: true })
  @IsOptional()
  exportTypes: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsOptional()
  @IsDateString({ each: true })
  lastSuccessfulDates?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsOptional()
  @IsDateString({ each: true })
  nextExportDates?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsString({ each: true })
  @IsEnum(['daily', 'weekly', 'monthly'], { each: true })
  @IsOptional()
  exportFrequencies?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  locations?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsOptional()
  @IsDateString({ each: true })
  startDates?: string[];

  @AutoMap(() => [String])
  @IsArray()
  @IsOptional()
  @IsDateString({ each: true })
  endDates?: string[];

  @AutoMap(() => [Number])
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  runsCompleted?: number[];

  @AutoMap(() => [Number])
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  runsRemaining?: number[];

  @AutoMap(() => [Boolean])
  @IsArray()
  @IsBoolean({ each: true })
  @IsOptional()
  runsIndefinitely?: boolean[];

  @AutoMap(() => [String])
  @IsArray()
  @IsDateString({ each: true })
  @IsOptional()
  createdOnDates?: string[];

  @AutoMap(() => [PersonDto])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PersonDto)
  @IsOptional()
  createdBys?: PersonDto[];
}

export class GetAllScheduledExportsRequestDto {
  @AutoMap()
  @IsObject()
  @Type(() => GetAllScheduledExportsFilterOption)
  filter: GetAllScheduledExportsFilterOption;

  @AutoMap()
  @IsArray()
  @IsOptional()
  sort?: Array<SortByOption>;
}
