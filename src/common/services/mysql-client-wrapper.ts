import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';

@Injectable()
export class MySQLClientService {
  private readonly logger = new Logger(MySQLClientService.name);

  constructor(private dataSource: DataSource) {}

  async executeRawQuery(
    query: string,
    bindings: (string | number)[],
  ): Promise<any> {
    return await this.dataSource.manager.query(query, bindings);
  }
}
