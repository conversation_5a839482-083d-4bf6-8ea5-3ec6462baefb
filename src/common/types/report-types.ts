import { z } from 'zod';

const GenericFilterSchema = z.object({
  key: z.string(),
  operator: z.union([
    z.literal('in'),
    z.literal('equals'),
    z.literal('between'),
    z.literal('norms'),
    z.literal('less than'),
    z.literal('greater than'),
  ]),
  value: z.any(),
});

//initially limiting to minimum fields required
export const PlatformAccountFilterSchema = z.object({
  key: z.literal('platformAccountId'),
  operator: z.literal('in'),
  value: z.array(
    z.object({
      id: z.string(), //the platform (external) account id
      // brands: z.array(z.object({})),
      channel: z.string(),
      // markets: z.array(z.object({
      //   id: z.string(),
      //   name: z.string(),
      // })),
      // name: z.string(),
      // processingCompleted: z.boolean(),
    }),
  ),
});
export type PlatformAccountFilter = z.infer<typeof PlatformAccountFilterSchema>;

const ReportFilterV2Schema = z.union([
  PlatformAccountFilterSchema,
  GenericFilterSchema,
]);
export type ReportV2Filter = z.infer<typeof ReportFilterV2Schema>;

export const V2OrV3ReportSchema = z.object({
  filtersVersion: z.union([z.literal(2), z.literal(3)]),
  filters: z.array(ReportFilterV2Schema),
});
