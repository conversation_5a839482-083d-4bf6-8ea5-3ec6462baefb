import { <PERSON>, Get, Param, ParseIntPipe } from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { DraftMediaVariantService } from '../services/draft-media-variant.service';
import { readProjectDraft } from '../../studio.permissions';
import { ReadDraftMediaVariantDto } from '../dto/read-draft-media-variant.dto';
import { VmApiOkResponse } from '@vidmob/vidmob-nestjs-common';

/**
 * * The controller responsible for listing out draft media variant.
 */
@ApiTags('Studio - Project')
@ApiSecurity('Bearer Token')
@Controller('project/:projectId/draft-media-variant')
export class DraftMediaVariantController {
  constructor(
    private readonly draftMediaVariantService: DraftMediaVariantService,
  ) {}
  /**
   * Find the draft media variant url, duration and status by media id.
   * @param mediaId The media id.
   */
  @VmApiOkResponse({
    type: ReadDraftMediaVariantDto,
  })
  @ApiParam({ name: 'mediaId', description: 'The id of the Media' })
  @ApiParam({ name: 'projectId', description: 'The id of the Project' })
  @Permissions(readProjectDraft)
  @Get(':mediaId')
  async findDraftMediaVariantUrl(
    @Param('mediaId', ParseIntPipe) mediaId: number,
    @Param('projectId', ParseIntPipe) projectId: number,
  ) {
    return this.draftMediaVariantService.findMediaVariantUrl(
      mediaId,
      projectId,
    );
  }
}
