import { Test, TestingModule } from '@nestjs/testing';
import { DraftMediaVariantController } from './draft-media-variant.controller';
import { DraftMediaVariantService } from '../services/draft-media-variant.service';

describe('DraftMediaVariantController', () => {
  let controller: DraftMediaVariantController;
  const mockMediaVariantService = {
    findMediaVariantUrl: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DraftMediaVariantController],
      providers: [
        {
          provide: DraftMediaVariantService,
          useValue: mockMediaVariantService,
        },
      ],
    }).compile();

    controller = module.get<DraftMediaVariantController>(
      DraftMediaVariantController,
    );
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findDraftMediaVariantUrl', () => {
    it('should return the mediaVariantUrl for a given mediaId and projectId', async () => {
      const mediaId = 123;
      const projectId = 1;
      const expectedResponse = {
        status: 'OK',
        result: {
          status: 'SUCCEEDED',
          duration: '0.800000',
          mediaVariantUrl: 'https://example.com/mediaVariantUrl',
        },
      };

      mockMediaVariantService.findMediaVariantUrl.mockResolvedValue(
        expectedResponse,
      );

      const result = await controller.findDraftMediaVariantUrl(
        mediaId,
        projectId,
      );
      expect(result).toEqual(expectedResponse);
      expect(mockMediaVariantService.findMediaVariantUrl).toHaveBeenCalledWith(
        mediaId,
        projectId,
      );
    });
  });
});
