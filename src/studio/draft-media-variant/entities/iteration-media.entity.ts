import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity()
export class IterationMedia {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ type: 'bigint', name: 'media_id' })
  mediaId: number;

  @AutoMap()
  @Column({ type: 'bigint', nullable: true, name: 'project_id' })
  projectId: number;

  @AutoMap()
  @Column('bit')
  accepted: boolean;

  @AutoMap()
  @Column({ type: 'int', nullable: true })
  iteration: number;

  @AutoMap()
  @Column({ type: 'int', nullable: true, name: 'iteration_version' })
  iterationVersion: number;

  @AutoMap()
  @Column({ type: 'bigint', nullable: true, name: 'output_video_id' })
  outputVideoId: number;

  @AutoMap()
  @Column({ type: 'bigint', nullable: true, name: 'artifact_id' })
  artifactId: number;

  @AutoMap()
  @Column({
    type: 'varchar',
    length: 24,
    nullable: true,
    name: 'artifact_type',
  })
  artifactType: string;

  @AutoMap()
  @CreateDateColumn({ nullable: true, name: 'date_created' })
  dateCreated: Date;

  @AutoMap()
  @UpdateDateColumn({ nullable: true, name: 'last_updated' })
  lastUpdated: Date;

  @AutoMap()
  @Column({ type: 'tinyint', default: 0 })
  deleted: boolean;
}
