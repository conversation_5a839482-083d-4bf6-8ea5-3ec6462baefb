import { AutoMap } from '@automapper/classes';

type DraftMediaVariantState = 'SUCCEEDED' | 'FAILURE' | 'IN_PROGRESS';

/**
 * Data Transfer Object (DTO) for reading draft media variant details.
 */
export class ReadDraftMediaVariantDto {
  /**
   * Represents the status of the draft media variant processing.
   */
  @AutoMap()
  status: DraftMediaVariantState;

  /**
   * The duration value is in seconds.
   */
  @AutoMap()
  duration?: number;

  /**
   * URL to access the draft media variant. This might be a temporary URL
   * signed with specific access credentials.
   */
  @AutoMap()
  mediaVariantUrl?: string;
}
