import { Test, TestingModule } from '@nestjs/testing';
import { DraftMediaVariantService } from './draft-media-variant.service';
import { DefaultService as MediaVariantServiceSdk } from '@vidmob/vidmob-media-conversion-service-sdk';

describe('MediaVariantService', () => {
  let service: DraftMediaVariantService;

  const mockMediaVariantServiceSdk = {
    mediaVariantControllerGetMediaVariantUrlAsPromise: jest.fn(),
  };

  const mockIterationMediaRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DraftMediaVariantService,
        {
          provide: MediaVariantServiceSdk,
          useValue: mockMediaVariantServiceSdk,
        },
        {
          provide: 'IterationMediaRepository',
          useValue: mockIterationMediaRepository,
        },
      ],
    }).compile();

    service = module.get<DraftMediaVariantService>(DraftMediaVariantService);
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findDraftMediaVariantUrl', () => {
    it('should retrieve the mediaVariantUrl for a given mediaId', async () => {
      const mediaId = 123;
      const projectId = 1;
      const expectedResponse = {
        status: 'OK',
        result: {
          status: 'SUCCEEDED',
          duration: '0.800000',
          mediaVariantUrl: 'https://example.com/mediaVariantUrl',
        },
      };

      mockMediaVariantServiceSdk.mediaVariantControllerGetMediaVariantUrlAsPromise.mockResolvedValue(
        expectedResponse,
      );
      mockIterationMediaRepository.findOne.mockResolvedValue({});

      const result = await service.findMediaVariantUrl(mediaId, projectId);
      expect(result).toEqual(expectedResponse);
      expect(
        mockMediaVariantServiceSdk.mediaVariantControllerGetMediaVariantUrlAsPromise,
      ).toHaveBeenCalledWith(projectId, mediaId);
    });

    it('should return error if media does not belong to the project', async () => {
      const mediaId = 123;
      const projectId = 1;
      const errorMessage = `Media with id ${mediaId} does not belong to project with id ${projectId}.`;

      mockMediaVariantServiceSdk.mediaVariantControllerGetMediaVariantUrlAsPromise.mockResolvedValue(
        errorMessage,
      );

      const result = await service.findMediaVariantUrl(mediaId, projectId);
      expect(result).toEqual(errorMessage);
    });
  });
});
