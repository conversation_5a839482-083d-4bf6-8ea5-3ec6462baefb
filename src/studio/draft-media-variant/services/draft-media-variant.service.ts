import { Injectable, Logger } from '@nestjs/common';
import {
  DefaultService as MediaVariantServiceSdk,
  MediaVariantControllerGetMediaVariantUrl200Response,
} from '@vidmob/vidmob-media-conversion-service-sdk';

@Injectable()
export class DraftMediaVariantService {
  private readonly logger = new Logger(DraftMediaVariantService.name);
  constructor(
    private readonly draftMediaVariantService: MediaVariantServiceSdk,
  ) {}

  /**
   * Find draft media variant url, duration, and status by media and project id.
   *
   * Flow:
   * 1. With the provided mediaId and projectId, this method calls the media conversion service's GetMediaVariantUrl.
   *
   * 2. GetMediaVariantUrl then performs a validation using validateMediaBelongsToProject:
   *    a. First, it checks for IterationMedia records with the provided mediaId and projectId.
   *    b. If a record is found, it's recognized as a draft, and the media is confirmed to belong to the specified project.
   *    c. If no IterationMedia record is found, it checks the OutputGroup using the resource_media_id and projectId.
   *    d. If an OutputGroup record is found, the media belongs to the specified project.
   *    e. If neither IterationMedia nor OutputGroup records are found, the media doesn't belong to the specified project.
   *       In this case, an error message is returned: "Media with id ${mediaId} does not belong to project with id ${projectId}".
   *
   * 3. Once the ownership of the media with the project is confirmed, the service returns the requested data which includes
   *    status, duration, and mediaVariantUrl.
   *
   * @param mediaId - The ID of the media.
   * @param projectId - The ID of the project.
   * @returns A promise that resolves to the media variant details.
   */
  async findMediaVariantUrl(
    mediaId: number,
    projectId: number,
  ): Promise<MediaVariantControllerGetMediaVariantUrl200Response> {
    return this.draftMediaVariantService.mediaVariantControllerGetMediaVariantUrlAsPromise(
      projectId,
      mediaId,
    );
  }
}
