import {
  Controller,
  Param,
  Body,
  Post,
  HttpCode,
  HttpStatus,
  Logger,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ProjectService } from '../services/project.service';
import { UpdateProjectDetailsDto } from '../dtos/update-project-details.dto';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { updateProjectDetails } from '../../../account-management/account-management.permissions';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Studio - Project')
@ApiSecurity('Bearer Token')
@Controller('studio/project')
export class ProjectController {
  private readonly logger = new Logger(ProjectController.name);

  constructor(private readonly projectService: ProjectService) {}

  /**
   * Updates project details. Currently supports updating the delivery date.
   * Future parameters can be added to UpdateProjectDetailsDto.
   * Requires the updateProjectDetails permission (partner.project.update).
   *
   * @param id - The ID of the project.
   * @param updateProjectDetailsDto - The details to update (e.g., deliveryDate)
   */
  @Permissions(updateProjectDetails)
  @Post(':id')
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async updateProjectDetails(
    @Param('id') id: string,
    @Body() updateProjectDetailsDto: UpdateProjectDetailsDto,
  ): Promise<{ success: boolean; message: string }> {
    const projectId = Number(id);

    try {
      await this.projectService.updateProjectDetails(
        projectId,
        updateProjectDetailsDto,
      );

      return {
        success: true,
        message: 'Project details updated successfully.',
      };
    } catch (error) {
      this.logger.error(
        `Failed to update project ${projectId} details: ${error.message}`,
      );
      throw error;
    }
  }
}
