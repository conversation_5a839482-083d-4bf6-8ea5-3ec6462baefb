import { Test, TestingModule } from '@nestjs/testing';
import { ProjectController } from './project.controller';
import { ProjectService } from '../services/project.service';
import { UpdateProjectDetailsDto } from '../dtos/update-project-details.dto';
import {
  NotFoundException,
  BadRequestException,
  ValidationPipe,
} from '@nestjs/common';

describe('ProjectController', () => {
  let projectController: ProjectController;
  let projectService: ProjectService;

  const mockProjectService = {
    updateProjectDetails: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProjectController],
      providers: [
        {
          provide: ProjectService,
          useValue: mockProjectService,
        },
      ],
    }).compile();

    projectController = module.get<ProjectController>(ProjectController);
    projectService = module.get<ProjectService>(ProjectService);
  });

  afterEach(() => {
    jest.clearAllMocks(); // Clear mocks after each test
  });

  it('should be defined', () => {
    expect(projectController).toBeDefined();
  });

  describe('updateProjectDetails', () => {
    const projectId = '123';
    const numericProjectId = 123;
    const validDto: UpdateProjectDetailsDto = {
      workspaceId: 456,
      deliveryDate: '2024-12-05',
    };

    it('should update project details successfully', async () => {
      mockProjectService.updateProjectDetails.mockResolvedValue({
        success: true,
        message: 'Project details updated successfully.',
      });

      const result = await projectController.updateProjectDetails(
        projectId,
        validDto,
      );

      expect(mockProjectService.updateProjectDetails).toHaveBeenCalledWith(
        numericProjectId,
        validDto,
      );
      expect(result).toEqual({
        success: true,
        message: 'Project details updated successfully.',
      });
    });

    it('should throw NotFoundException if project does not exist', async () => {
      mockProjectService.updateProjectDetails.mockImplementation(() => {
        throw new NotFoundException('Project not found');
      });

      await expect(
        projectController.updateProjectDetails(projectId, validDto),
      ).rejects.toThrow(NotFoundException);

      expect(mockProjectService.updateProjectDetails).toHaveBeenCalledWith(
        numericProjectId,
        validDto,
      );
    });

    it('should throw BadRequestException for invalid date format', async () => {
      mockProjectService.updateProjectDetails.mockImplementation(() => {
        throw new BadRequestException('Invalid date format');
      });

      await expect(
        projectController.updateProjectDetails(projectId, validDto),
      ).rejects.toThrow(BadRequestException);

      expect(mockProjectService.updateProjectDetails).toHaveBeenCalledWith(
        numericProjectId,
        validDto,
      );
    });

    it('should validate the input DTO (invalid date)', async () => {
      // Test validation in isolation:
      const invalidDto: UpdateProjectDetailsDto = {
        workspaceId: 456,
        deliveryDate: 'invalid-date',
      };

      const validationPipe = new ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
      });

      await expect(
        validationPipe.transform(invalidDto, {
          type: 'body',
          metatype: UpdateProjectDetailsDto,
        }),
      ).rejects.toThrow(BadRequestException);

      expect(mockProjectService.updateProjectDetails).not.toHaveBeenCalled();
    });
  });
});
