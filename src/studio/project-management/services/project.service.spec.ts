import { Test, TestingModule } from '@nestjs/testing';
import { ProjectService } from './project.service';
import { ProjectService as StudioProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { UpdateProjectDetailsDto } from '../dtos/update-project-details.dto';
import { BadRequestException, NotFoundException } from '@nestjs/common';

describe('ProjectService', () => {
  let projectService: ProjectService;

  const mockStudioProjectService = {
    projectControllerUpdateProjectDetailsAsPromise: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProjectService,
        {
          provide: StudioProjectService,
          useValue: mockStudioProjectService,
        },
      ],
    }).compile();

    projectService = module.get<ProjectService>(ProjectService);
  });

  it('should be defined', () => {
    expect(projectService).toBeDefined();
  });

  describe('updateProjectDetails', () => {
    const projectId = 123;
    const validDto: UpdateProjectDetailsDto = {
      workspaceId: 456,
      deliveryDate: '2024-12-05',
    };

    it('should update project details successfully', async () => {
      const successResponse = {
        success: true,
        message: 'Project details updated successfully.',
      };
      mockStudioProjectService.projectControllerUpdateProjectDetailsAsPromise.mockResolvedValue(
        successResponse,
      );

      const result = await projectService.updateProjectDetails(
        projectId,
        validDto,
      );

      expect(
        mockStudioProjectService.projectControllerUpdateProjectDetailsAsPromise,
      ).toHaveBeenCalledWith(projectId, {
        deliveryDate: validDto.deliveryDate,
      });
      expect(result).toEqual(successResponse);
    });

    it('should throw NotFoundException if project does not exist', async () => {
      mockStudioProjectService.projectControllerUpdateProjectDetailsAsPromise.mockImplementation(
        () => {
          throw new NotFoundException('Project not found');
        },
      );

      await expect(
        projectService.updateProjectDetails(projectId, validDto),
      ).rejects.toThrow(NotFoundException);

      expect(
        mockStudioProjectService.projectControllerUpdateProjectDetailsAsPromise,
      ).toHaveBeenCalledWith(projectId, {
        deliveryDate: validDto.deliveryDate,
      });
    });

    it('should throw BadRequestException for invalid input', async () => {
      mockStudioProjectService.projectControllerUpdateProjectDetailsAsPromise.mockImplementation(
        () => {
          throw new BadRequestException('Invalid date format');
        },
      );

      await expect(
        projectService.updateProjectDetails(projectId, validDto),
      ).rejects.toThrow(BadRequestException);

      expect(
        mockStudioProjectService.projectControllerUpdateProjectDetailsAsPromise,
      ).toHaveBeenCalledWith(projectId, {
        deliveryDate: validDto.deliveryDate,
      });
    });
  });
});
