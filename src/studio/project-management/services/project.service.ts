import { Injectable, Logger } from '@nestjs/common';
import { ProjectService as StudioProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { UpdateProjectDetailsDto } from '../dtos/update-project-details.dto';

@Injectable()
export class ProjectService {
  private readonly logger = new Logger(ProjectService.name);

  constructor(private readonly studioProjectService: StudioProjectService) {}

  /**
   * Updates the project details by calling the studio service.
   * Currently updates the deliveryDate.
   *
   * @param projectId - The ID of the project
   * @param updateProjectDetailsDto - DTO containing the new deliveryDate (and future fields)
   * @returns The response from the studio service.
   */
  async updateProjectDetails(
    projectId: number,
    updateProjectDetailsDto: UpdateProjectDetailsDto,
  ): Promise<{ success: boolean; message: string }> {
    const { deliveryDate } = updateProjectDetailsDto;
    this.logger.log(
      `Updating project ${projectId} with delivery date: ${deliveryDate}`,
    );

    const updateResponse =
      await this.studioProjectService.projectControllerUpdateProjectDetailsAsPromise(
        projectId,
        { deliveryDate },
      );

    return updateResponse;
  }
}
