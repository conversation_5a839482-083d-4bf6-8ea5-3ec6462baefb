import { IsDateString, IsNotEmpty } from 'class-validator';

export class UpdateProjectDetailsDto {
  /*
   * Delivery date of the project
   */
  @IsNotEmpty({ message: 'Delivery date is required.' })
  @IsDateString(
    { strict: true },
    { message: 'Delivery date must be a valid ISO 8601 date string.' },
  )
  deliveryDate: string; // Expected in ISO format (e.g., "2024-12-05")

  /*
   * The ID of the workspace to which the project belongs
   */
  @IsNotEmpty({ message: 'Workspace Id is required.' })
  workspaceId: number;
}
