import { Modu<PERSON> } from '@nestjs/common';
import { DraftMediaVariantController } from './draft-media-variant/controllers/draft-media-variant.controller';
import { DraftMediaVariantService } from './draft-media-variant/services/draft-media-variant.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { IterationMedia } from './draft-media-variant/entities/iteration-media.entity';
import { MediaVariantController } from './media-variant/controllers/media-variant-controller';
import { ProjectCsvDownloadController } from './project-csv-download/controllers/project-csv-download.controller';
import { ProjectCsvDownloadService } from './project-csv-download/services/project-csv-download.service';
import { ProjectController } from './project-management/controllers/project.controller';
import { ProjectService } from './project-management/services/project.service';

@Module({
  imports: [TypeOrmModule.forFeature([IterationMedia])],
  controllers: [
    DraftMediaVariantController,
    MediaVariantController,
    ProjectCsvDownloadController,
    ProjectController,
  ],
  providers: [
    DraftMediaVariantService,
    ProjectCsvDownloadService,
    ProjectService,
  ],
})
export class StudioModule {}
