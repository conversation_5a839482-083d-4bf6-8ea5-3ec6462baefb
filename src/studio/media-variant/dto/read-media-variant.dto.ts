import { AutoMap } from '@automapper/classes';

type MediaVariantState = 'SUCCEEDED' | 'FAILURE' | 'IN_PROGRESS';

/**
 * Data Transfer Object (DTO) for reading media variant details.
 */
export class ReadMediaVariantDto {
  /**
   * Represents the status of the media variant processing.
   */
  @AutoMap()
  status: MediaVariantState;

  /**
   * The duration value is in seconds.
   */
  @AutoMap()
  duration?: number;

  /**
   * URL to access the media variant. This might be a temporary URL
   * signed with specific access credentials.
   */
  @AutoMap()
  mediaVariantUrl?: string;
}
