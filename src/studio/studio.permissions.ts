import { PermissionSubResource } from '../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../auth/enums/permission.domain.enum';
import { PermissionAction } from '../auth/enums/permission.action.enum';

import { projectFromParamsHandler } from '../auth/decorators/permission.decorator';

// Permissions to read media in project
export const readProjectMedia = {
  domain: PermissionDomain.PROJECT,
  domainContextHandler: projectFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.MEDIA,
    },
  ],
};

// Permissions to read draft in project
export const readProjectDraft = {
  domain: PermissionDomain.PROJECT,
  domainContextHandler: projectFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DRAFT,
    },
  ],
};
