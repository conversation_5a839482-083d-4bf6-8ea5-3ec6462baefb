import {
  <PERSON>,
  Get,
  Param,
  Query,
  Res,
  Logger,
  Request,
} from '@nestjs/common';
import { ProjectCsvDownloadService } from '../services/project-csv-download.service';
import { Response } from 'express';
import { CSV_DOWNLOAD_TYPES } from '../constants/project-csv-download.constants';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Studio - Project')
@ApiSecurity('Bearer Token')
@Controller('studio/project')
export class ProjectCsvDownloadController {
  private readonly logger = new Logger(ProjectCsvDownloadController.name);

  constructor(
    private readonly projectCsvDownloadService: ProjectCsvDownloadService,
  ) {}

  /**
   * Downloads a CSV for a project based on the project ID and media IDs provided.
   *
   * @param projectId - The ID of the project.
   * @param iterationMedia - A comma-separated list of media IDs to include in the CSV.
   * @param projectType - The type of project to download (PROJECT_CHAT_CLIENT, ITERATION_MEDIA_COMMENT, or both).
   * @param res - The response object to stream the CSV file.
   */
  @Get(':projectId/downloadCsv')
  async downloadProjectCsv(
    @Request() req: any,
    @Param('projectId') projectId: string,
    @Query('iterationMedia') iterationMedia: string,
    @Query('type') projectType: string,
    @Res() res: Response,
  ) {
    const authorization = req.headers.authorization;
    const mediaIds = iterationMedia
      ? iterationMedia.split(',').map(Number)
      : [];

    try {
      const projectTypes = projectType
        ? projectType.split(',').map((type) => type.trim())
        : [];

      const validProjectTypes = projectTypes.every(
        (type) => type in CSV_DOWNLOAD_TYPES,
      );

      if (!validProjectTypes || projectTypes.length === 0) {
        res.status(400).send('Invalid project type');
        return;
      }

      const csvStream = await this.projectCsvDownloadService.downloadProjectCsv(
        projectId,
        mediaIds,
        projectTypes,
        authorization,
        res,
      );

      csvStream.pipe(res);
    } catch (error) {
      this.logger.error(
        `Failed to download CSV for project: ${projectId}`,
        error,
      );
      res.status(500).send('Failed to download CSV');
    }
  }
}
