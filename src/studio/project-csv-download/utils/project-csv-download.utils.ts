import axios from 'axios';
import { CsvRowDto } from '../dto/project-csv-download-csv-row-response.dto';
import { CSV_DOWNLOAD_TYPES } from '../constants/project-csv-download.constants';

export const fetchProjectData = async (
  projectId: string,
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const projectUrl = `${baseVidMobApiUrl}/api/v2/project/${projectId}`;
  const projectResponse = await axios.get(projectUrl, {
    headers: { Authorization: authorization },
  });
  return projectResponse.data.result;
};

export const fetchOutputVideos = async (
  projectId: string,
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const outputVideoUrl = `${baseVidMobApiUrl}/api/v2/project/${projectId}/outputVideo`;
  const outputVideoResponse = await axios.get(outputVideoUrl, {
    headers: { Authorization: authorization },
  });
  return outputVideoResponse.data.result;
};

export const fetchDrafts = async (
  videoId: string,
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const draftUrl = `${baseVidMobApiUrl}/api/v2/outputVideo/${videoId}/iterationMedia?outputVideoId=${videoId}&perPage=10000`;
  const draftResponse = await axios.get(draftUrl, {
    headers: { Authorization: authorization },
  });
  return draftResponse.data.result;
};

export const fetchAnnotations = async (
  mediaId: number,
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const annotationUrl = `${baseVidMobApiUrl}/api/v2/iteration/${mediaId}/annotation?iterationMediaId=${mediaId}&extraFields=attachments,streams&perPage=1000`;
  const annotationResponse = await axios.get(annotationUrl, {
    headers: { Authorization: authorization },
  });
  return annotationResponse.data.result;
};

export const fetchMessageChannel = async (
  projectId: string,
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const messageChannelResponse = await axios.get(
    `${baseVidMobApiUrl}/api/v2/messageChannel/?projectId=${projectId}&type=PROJECT_EDITOR`,
    {
      headers: { Authorization: authorization },
    },
  );

  return messageChannelResponse.data.result;
};

export const fetchMessageChannelMessages = async (
  channelId: string,
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const messagesResponse = await axios.get(
    `${baseVidMobApiUrl}/api/v2/messageChannel/${channelId}/message?messageChannelId=${channelId}&extraFields=attachments,streams&perPage=25`,
    {
      headers: { Authorization: authorization },
    },
  );

  return messagesResponse.data.result;
};

export const fetchCurrentUser = async (
  authorization: string,
  baseVidMobApiUrl: string,
) => {
  const currentUserResponse = await axios.get(
    `${baseVidMobApiUrl}/api/v1/user`,
    {
      headers: { Authorization: authorization },
    },
  );

  return currentUserResponse.data.result;
};

export const getDraftName = (draft: any) => {
  if (draft.iteration && draft.iterationVersion) {
    return `Draft ${draft.iteration}.${draft.iterationVersion}`;
  } else {
    return 'Draft pending';
  }
};

export const getMentions = (annotationText: string) => {
  const mentionRegex = /@[A-Za-z0-9._]+\s[A-Za-z0-9._]+/g;
  const matches = annotationText.match(mentionRegex);
  let mentionNames: string[] = [];

  if (matches && matches.length > 0) {
    mentionNames = matches;
  }
  return mentionNames;
};

export const formatDateToCustomFormat = (date: Date) => {
  const pad = (num: number) => String(num).padStart(2, '0');

  const month = pad(date.getMonth() + 1); // Months are zero-indexed
  const day = pad(date.getDate());
  const year = date.getFullYear();
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());

  return `${month}-${day}-${year} ${hours}:${minutes}:${seconds}`;
};

export const getFormattedFileDate = () => {
  const now = new Date();
  return now.toISOString().split('T')[0];
};

export const compareCsvRows = (a: CsvRowDto, b: CsvRowDto) => {
  const projectIdComparison = Number(a.projectId) - Number(b.projectId);
  if (projectIdComparison !== 0) return projectIdComparison;

  if (a.type !== CSV_DOWNLOAD_TYPES.PROJECT_CHAT_CLIENT.csvType) {
    const outputIdComparison = Number(a.outputId) - Number(b.outputId);
    if (outputIdComparison !== 0) return outputIdComparison;

    const draftIdComparison = Number(a.draftId) - Number(b.draftId);
    if (draftIdComparison !== 0) return draftIdComparison;
  }

  return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
};

export const mapCsvRow = (row: CsvRowDto) => {
  return {
    Timestamp: row.timestamp,
    Type: row.type,
    'Sender Name': row.senderName,
    Message: row.message,
    Mentions: row.mentions,
    'Reply?': row.isReply,
    'Attachment filename': row.attachmentFilename,
    'Attachment link': row.attachmentLink,
    'Draft name': row.draftName,
    'Output name': row.outputName,
    'Project name': row.projectName,
    'Workspace name': row.workspaceName,
    'Organization name': row.organizationName,
    'Sender id': row.senderId,
    'Draft id': row.draftId,
    'Output id': row.outputId,
    'Project id': row.projectId,
    'Workspace id': row.workspaceId,
    'Organization id': row.organizationId,
  };
};
