import { IsN<PERSON>ber, IsString, IsOptional } from 'class-validator';

export class CsvRowDto {
  @IsString()
  timestamp: string;

  @IsString()
  type: string;

  @IsString()
  senderName: string;

  @IsString()
  message: string;

  @IsString()
  mentions: string;

  @IsString()
  isReply: string;

  @IsOptional()
  @IsString()
  attachmentFilename: string;

  @IsOptional()
  @IsString()
  attachmentLink: string;

  @IsOptional()
  @IsString()
  draftName: string;

  @IsOptional()
  @IsString()
  outputName: string;

  @IsString()
  projectName: string;

  @IsString()
  workspaceName: string;

  @IsString()
  organizationName: string;

  @IsNumber()
  senderId: number | string;

  @IsOptional()
  @IsString()
  draftId: string;

  @IsOptional()
  @IsString()
  outputId: string;

  @IsNumber()
  projectId: string;

  @IsNumber()
  workspaceId: string;

  @IsString()
  organizationId: string;
}
