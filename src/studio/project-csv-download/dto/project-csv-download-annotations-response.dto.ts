import {
  IsBoolean,
  Is<PERSON><PERSON>ber,
  IsString,
  IsO<PERSON>al,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class PersonDto {
  @IsNumber()
  id: number;

  @IsString()
  displayName: string;

  @IsOptional()
  @IsString()
  jobTitle?: string | null;

  @IsString()
  photo: string;
}

class MediaDto {
  @IsString()
  name: string;

  @IsString()
  url: string;
}

class AttachmentDto {
  @ValidateNested()
  @Type(() => MediaDto)
  media: MediaDto;
}

class MediaClipDto {
  @IsNumber()
  id: number;

  @IsNumber()
  mediaId: number;

  @IsString()
  locationType: string;
}

export class ProjectCsvDownloadAnnotationsResponseDto {
  @IsNumber()
  id: number;

  @IsString()
  text: string;

  @IsString()
  visibility: string;

  @IsString()
  contextType: string;

  @IsString()
  contextSubType: string;

  @IsBoolean()
  unread: boolean;

  @ValidateNested()
  @Type(() => PersonDto)
  person: PersonDto;

  @IsString()
  dateCreated: string;

  @IsString()
  lastUpdated: string;

  @IsString()
  scope: string;

  @IsOptional()
  @IsString()
  annotationSet?: string | null;

  @IsString()
  annotationType: string;

  @IsBoolean()
  checked: boolean;

  @IsArray()
  @IsOptional()
  @IsNumber({}, { each: true })
  attachmentIds: number[];

  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => AttachmentDto)
  attachments: AttachmentDto[];

  @ValidateNested()
  @Type(() => MediaClipDto)
  mediaClip: MediaClipDto;

  @IsOptional()
  @IsNumber()
  replyTo?: number | null;
}
