import { IsNumber, IsString, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

class FromDto {
  @IsNumber()
  id: number;

  @IsString()
  displayName: string;
}

class MediaDto {
  @IsString()
  name: string;

  @IsString()
  url: string;
}

class AttachmentDto {
  @Type(() => MediaDto)
  media: MediaDto;
}

export class MessageChannelResponseDto {
  @IsString()
  date: string;

  @IsString()
  text: string;

  @Type(() => FromDto)
  from: FromDto;

  @IsOptional()
  @Type(() => AttachmentDto)
  attachments?: AttachmentDto[];
}
