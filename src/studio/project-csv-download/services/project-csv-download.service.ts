import { Injectable, Logger } from '@nestjs/common';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { InjectDataSource } from '@nestjs/typeorm';
import { Stream } from 'stream';
import * as fastCsv from 'fast-csv';
import { Response } from 'express';
import {
  CSV_DOWNLOAD_TYPES,
  REPLY,
} from '../constants/project-csv-download.constants';
import { ConfigService } from '@nestjs/config';
import { ProjectCsvDownloadAnnotationsResponseDto } from '../dto/project-csv-download-annotations-response.dto';
import { MessageChannelResponseDto } from '../dto/project-csv-download-messages-response.dto';
import { ProjectDataDto } from '../dto/project-csv-download-project-response.dto';
import { CsvRowDto } from '../dto/project-csv-download-csv-row-response.dto';
import { OutputVideoDto } from '../dto/project-csv-download-output-video-response.dto';
import {
  getMentions,
  fetchProjectData,
  fetchOutputVideos,
  fetchDrafts,
  getDraftName,
  fetchAnnotations,
  fetchMessageChannel,
  fetchMessageChannelMessages,
  fetchCurrentUser,
  formatDateToCustomFormat,
  getFormattedFileDate,
  compareCsvRows,
  mapCsvRow,
} from '../utils/project-csv-download.utils';

/**
 * Downloads a CSV for a project with specific iteration media.
 *
 * @param projectId - The ID of the project.
 * @param iterationMedia - A list of media IDs to include in the CSV.
 * @returns A promise that resolves to the CSV file data.
 */

@Injectable()
export class ProjectCsvDownloadService {
  private csvRows: CsvRowDto[] = [];

  constructor(
    @InjectDataSource()
    private readonly projectService: ProjectService,
    private readonly configService: ConfigService,
  ) {}

  private readonly logger = new Logger(ProjectCsvDownloadService.name);

  baseVidMobApiUrl = this.configService.get<string>('baseVidMobApiUrl', '');

  /*
   * Downloads a CSV file based on project and media data
   * @param projectId - The ID of the project.
   * @param iterationMedia - The media IDs to include in the CSV.
   * @param projectType - The type of project data to fetch.
   * @param res - The response object to stream the CSV file.
   */
  async downloadProjectCsv(
    projectId: string,
    iterationMedia: number[],
    projectTypes: string[],
    authorization: string,
    res: Response,
  ): Promise<Stream> {
    this.csvRows = [];

    const csvStream = new Stream.PassThrough();
    const csvWriter = fastCsv.format({ headers: true });
    csvWriter.pipe(csvStream);

    const currentUser = await fetchCurrentUser(
      authorization,
      this.baseVidMobApiUrl,
    );

    const projectData = await fetchProjectData(
      projectId,
      authorization,
      this.baseVidMobApiUrl,
    );

    const filename = `Project Chat and Comments - ${
      projectData.name
    } - ${getFormattedFileDate()}.csv`;

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'text/csv');

    if (currentUser) {
      csvStream.write(
        `Download Timestamp,${formatDateToCustomFormat(new Date())}\n`,
      );
      csvStream.write(`Requester,${currentUser.displayName}\n`);
      csvStream.write(`\n`);
    }

    csvWriter.write([
      'Timestamp',
      'Type',
      'Sender Name',
      'Message',
      'Mentions',
      'Reply?',
      'Attachment filename',
      'Attachment link',
      'Draft name',
      'Output name',
      'Project name',
      'Workspace name',
      'Organization name',
      'Sender id',
      'Draft id',
      'Output id',
      'Project id',
      'Workspace id',
      'Organization id',
    ]);

    const outputVideos = await fetchOutputVideos(
      projectId,
      authorization,
      this.baseVidMobApiUrl,
    );

    for (const projectType of projectTypes) {
      if (projectType === CSV_DOWNLOAD_TYPES.PROJECT_CHAT_CLIENT.downloadType) {
        await this.fetchProjectChatData(projectId, authorization, projectData);
      }

      if (
        projectType === CSV_DOWNLOAD_TYPES.ITERATION_MEDIA_COMMENT.downloadType
      ) {
        await this.fetchIterationMediaComments(
          projectId,
          iterationMedia,
          authorization,
          projectData,
          outputVideos,
        );
      }
    }

    this.csvRows.sort((a, b) => compareCsvRows(a, b));

    this.csvRows.forEach((row) => csvWriter.write(mapCsvRow(row)));

    csvWriter.end();
    return csvStream;
  }

  private async fetchProjectChatData(
    projectId: string,
    authorization: string,
    projectData: ProjectDataDto,
  ) {
    try {
      const messageChannel = await fetchMessageChannel(
        projectId,
        authorization,
        this.baseVidMobApiUrl,
      );

      const messages = await fetchMessageChannelMessages(
        messageChannel.id,
        authorization,
        this.baseVidMobApiUrl,
      );

      messages.forEach((message: MessageChannelResponseDto) => {
        const mentionNames = message.text ? getMentions(message.text) : [];
        const timestamp = message.date
          ? formatDateToCustomFormat(new Date(message.date))
          : '-';
        const senderName = message.from?.displayName || '-';
        const senderId = message.from?.id || '-';
        const attachmentFilename = message.attachments?.[0]?.media?.name || '-';
        const attachmentLink = message.attachments?.[0]?.media?.url || '-';

        this.csvRows.push({
          timestamp,
          type: CSV_DOWNLOAD_TYPES.PROJECT_CHAT_CLIENT.csvType,
          senderName,
          message: message.text || '-',
          mentions: mentionNames.length ? mentionNames.join(', ') : '-',
          isReply: REPLY['N/A'],
          attachmentFilename,
          attachmentLink,
          draftName: '-',
          outputName: '-',
          projectName: projectData?.name || '-',
          workspaceName: projectData?.partner?.name || '-',
          organizationName: projectData?.partner?.organizationName || '-',
          senderId,
          draftId: '-',
          outputId: '-',
          projectId: projectId,
          workspaceId: String(projectData?.partner?.id || '-'),
          organizationId: String(projectData?.partner?.organizationId || '-'),
        });
      });
    } catch (error) {
      this.logger.error(
        `Failed to fetch project chat data for project ${projectId}`,
        error,
      );
      throw new Error('Failed to fetch project chat data');
    }
  }

  private async fetchIterationMediaComments(
    projectId: string,
    iterationMedia: number[],
    authorization: string,
    projectData: ProjectDataDto,
    outputVideos: OutputVideoDto[],
  ) {
    try {
      for (const video of outputVideos) {
        const drafts = await fetchDrafts(
          video.id,
          authorization,
          this.baseVidMobApiUrl,
        );

        for (const draft of drafts) {
          const draftName = getDraftName(draft);
          if (iterationMedia.length > 0) {
            for (const iterationMediaId of iterationMedia) {
              if (draft.id === iterationMediaId) {
                const annotations = await fetchAnnotations(
                  iterationMediaId,
                  authorization,
                  this.baseVidMobApiUrl,
                );
                this.processAnnotations(
                  annotations,
                  draftName,
                  video.name,
                  draft.id,
                  video.id,
                  projectId,
                  projectData,
                );
              }
            }
          } else {
            const annotations = await fetchAnnotations(
              draft.id,
              authorization,
              this.baseVidMobApiUrl,
            );
            this.processAnnotations(
              annotations,
              draftName,
              video.name,
              draft.id,
              video.id,
              projectId,
              projectData,
            );
          }
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to fetch iteration media comments for project ${projectId}`,
        error.response ? error.response.data : error.message,
      );
      throw new Error('Failed to fetch iteration media comments');
    }
  }

  private processAnnotations(
    annotations: ProjectCsvDownloadAnnotationsResponseDto[],
    draftName: string,
    videoName: string,
    draftId: string,
    videoId: string,
    projectId: string,
    projectData: ProjectDataDto,
  ) {
    annotations.forEach(
      (annotation: ProjectCsvDownloadAnnotationsResponseDto) => {
        const mentionNames = getMentions(annotation.text);
        this.csvRows.push({
          timestamp: formatDateToCustomFormat(new Date(annotation.dateCreated)),
          type: CSV_DOWNLOAD_TYPES.ITERATION_MEDIA_COMMENT.csvType,
          senderName: annotation.person.displayName,
          message: annotation.text,
          mentions: mentionNames.length > 0 ? mentionNames.join(', ') : '-',
          isReply: annotation.replyTo ? REPLY.YES : REPLY.NO,
          attachmentFilename: annotation?.attachments[0]?.media.name || '-',
          attachmentLink: annotation?.attachments[0]?.media.url || '-',
          draftName: draftName || '-',
          outputName: videoName || '-',
          projectName: projectData.name,
          workspaceName: projectData.partner.name,
          organizationName: projectData.partner.organizationName,
          senderId: annotation.person.id,
          draftId: draftId || '-',
          outputId: videoId || '-',
          projectId: projectId,
          workspaceId: String(projectData.partner.id),
          organizationId: String(projectData.partner.organizationId),
        });
      },
    );
  }
}
