import { ALL_PLATFORMS } from '../scoring/reports/constants/constants';

export enum AccountType {
  ACCOUNT = 'ACCOUNT',
  ACCOUNT_GROUP = 'ACCOUNT_GROUP',
}

export enum CustomAudienceTargetingStatus {
  INCLUDED = 'INCLUDED',
  EXCLUDED = 'EXCLUDED',
}

export enum Platform {
  FACEBOOK = 'facebook',
  SNAPCHAT = 'snapchat',
  TIKTOK = 'tiktok',
  PINTEREST = 'pinterest',
  DV360 = 'dv360',
  LINKEDIN = 'linkedin',
  TWITTER = 'twitter',
  REDDIT = 'reddit',
  ADWORDS = 'adwords',
  AMAZON_ADVERTISING = 'amazonadvertising',
  AMAZON_ADVERTISING_DSP = 'amazonadvertisingdsp',
  INSTAGRAM_PAGE = 'instagrampage',
  FACEBOOK_PAGE = 'facebookpage',
}

export enum PlatformNewName {
  FACEBOOK = 'meta',
  TWITTER = 'x',
  ADWORDS = 'google ads',
}

const ANALYTICS_PLATFORM_SNAP = 'snap';

// analytics uses 'snap' instead of 'snapchat'
export const PLATFORM_TO_ANALYTICS_PLATFORM_VALUE: Record<Platform, string> = {
  [Platform.SNAPCHAT]: ANALYTICS_PLATFORM_SNAP,
  [Platform.FACEBOOK]: Platform.FACEBOOK,
  [Platform.TIKTOK]: Platform.TIKTOK,
  [Platform.PINTEREST]: Platform.PINTEREST,
  [Platform.DV360]: Platform.DV360,
  [Platform.LINKEDIN]: Platform.LINKEDIN,
  [Platform.TWITTER]: Platform.TWITTER,
  [Platform.REDDIT]: Platform.REDDIT,
  [Platform.ADWORDS]: Platform.ADWORDS,
  [Platform.AMAZON_ADVERTISING]: Platform.AMAZON_ADVERTISING,
  [Platform.AMAZON_ADVERTISING_DSP]: Platform.AMAZON_ADVERTISING_DSP,
  [Platform.FACEBOOK_PAGE]: Platform.FACEBOOK_PAGE,
  [Platform.INSTAGRAM_PAGE]: Platform.INSTAGRAM_PAGE,
};

export const IMPRESSIONS_METRIC_FOR_PLATFORM: Record<Platform, string> = {
  [Platform.FACEBOOK]: 'impressions',
  [Platform.SNAPCHAT]: 'impressions',
  [Platform.TIKTOK]: 'show_cnt',
  [Platform.PINTEREST]: 'IMPRESSION_1',
  [Platform.DV360]: 'impressions',
  [Platform.LINKEDIN]: 'impressions',
  [Platform.TWITTER]: 'impressions',
  [Platform.REDDIT]: 'impressions',
  [Platform.ADWORDS]: 'impressions',
  [Platform.AMAZON_ADVERTISING]: 'impressions',
  [Platform.AMAZON_ADVERTISING_DSP]: 'impressions',
  [Platform.FACEBOOK_PAGE]: 'impressions',
  [Platform.INSTAGRAM_PAGE]: 'impressions',
};

export const CAMPAIGN_DIMENSION = 'campaign';

export enum DimensionGroups {
  PLATFORM = 'platform',
  PLACEMENT = 'placement',
  SERVING_LOCATION = 'serving_location',
  TARGETING_VALUE = 'targeting_value',
  CAMPAIGN_OBJECTIVE = 'campaign[objective]',
  LINE_ITEM_OBJECTIVE = 'lineitem[objective]',
  AD_TYPE = 'ad[type]',
  AD_GROUP_TYPE = 'adgroup[type]',
}

/* Use these keys to get the specific dimension group name and its nesting type we want for a platform's placement filter */
export const PLATFORM_PLACEMENT_VALUE_KEYS_FROM_DIMENSION_GROUPS_RESPONSE = {
  [Platform.FACEBOOK]: [DimensionGroups.PLACEMENT, DimensionGroups.PLATFORM],
  [Platform.PINTEREST]: [
    DimensionGroups.TARGETING_VALUE,
    DimensionGroups.PLACEMENT,
  ],
  [Platform.LINKEDIN]: [
    DimensionGroups.SERVING_LOCATION,
    DimensionGroups.PLATFORM,
  ],
  [Platform.AMAZON_ADVERTISING]: [
    DimensionGroups.PLACEMENT,
    DimensionGroups.PLACEMENT,
  ],
};

export const FACEBOOK_AD_TYPE_VALUE_KEYS_FROM_DIMENSION_GROUPS_RESPONSE = [
  DimensionGroups.PLACEMENT,
  'adType',
];

export const AD_DIMENSION_BY_PLATFORM: Record<Platform, string> = {
  [Platform.FACEBOOK]: 'ad',
  [Platform.SNAPCHAT]: 'ad',
  [Platform.TIKTOK]: 'ad',
  [Platform.PINTEREST]: 'ad',
  [Platform.DV360]: 'ad',
  [Platform.LINKEDIN]: 'ad',
  [Platform.TWITTER]: 'lineitem',
  [Platform.REDDIT]: 'ad',
  [Platform.ADWORDS]: 'ad',
  [Platform.AMAZON_ADVERTISING]: 'campaign',
  [Platform.AMAZON_ADVERTISING_DSP]: 'campaign',
  [Platform.FACEBOOK_PAGE]: 'post',
  [Platform.INSTAGRAM_PAGE]: 'post',
};

export const PLATFORMS_SUPPORT_CAMPAIGN_OBJECTIVE = [
  Platform.FACEBOOK,
  Platform.DV360,
  Platform.LINKEDIN,
  Platform.PINTEREST,
  Platform.SNAPCHAT,
  Platform.TIKTOK,
  Platform.TWITTER,
];

export const PLATFORM_CAMPAIGN_OBJECTIVE_KEY_MAP: Record<string, string> = {
  [Platform.FACEBOOK]: DimensionGroups.CAMPAIGN_OBJECTIVE,
  [Platform.DV360]: DimensionGroups.CAMPAIGN_OBJECTIVE,
  [Platform.LINKEDIN]: DimensionGroups.CAMPAIGN_OBJECTIVE,
  [Platform.PINTEREST]: DimensionGroups.CAMPAIGN_OBJECTIVE,
  [Platform.SNAPCHAT]: DimensionGroups.CAMPAIGN_OBJECTIVE,
  [Platform.TIKTOK]: DimensionGroups.CAMPAIGN_OBJECTIVE,
  [Platform.TWITTER]: DimensionGroups.LINE_ITEM_OBJECTIVE,
};

export const PLATFORMS_SUPPORT_CAMPAIGN_IDENTIFIER = [
  Platform.FACEBOOK,
  Platform.SNAPCHAT,
  Platform.TIKTOK,
  Platform.PINTEREST,
  Platform.DV360,
  Platform.LINKEDIN,
  Platform.TWITTER,
  Platform.REDDIT,
  Platform.ADWORDS,
  Platform.AMAZON_ADVERTISING,
  Platform.AMAZON_ADVERTISING_DSP,
];

export const PLATFORMS_SUPPORT_AD_TYPE_DIMENSION_GROUP = [
  Platform.ADWORDS,
  Platform.DV360,
];

export const AD_GROUP_TYPE_DETAILED = 'ad_group_type_detailed';

/* Campaign Objectives are nested. Use this key to get the specific nesting type we want for a platform's objectives filter */
export enum CampaignObjectiveNestingLevel {
  HIGH_LEVEL_OBJECTIVES = 'high_level_objectives',
  DETAILED_OBJECTIVES = 'detailed_objectives',
}

export const ADSET_DIMENSION_BY_PLATFORM = {
  [Platform.FACEBOOK]: 'adset',
};

export const PLATFORMS_SUPPORT_MULTI_ASSET_ADS = [Platform.FACEBOOK];

export const PLATFORMS_SUPPORT_CONVERSION_KPIS = [Platform.FACEBOOK];

export const SPEND_KPI_NAME = 'Spend';

export const PLATFORMS_SUPPORT_SPEND_KPIS = [
  Platform.FACEBOOK,
  Platform.SNAPCHAT,
  Platform.PINTEREST,
];

export const PLATFORMS_SUPPORT_NORMATIVE_PERFORMANCE: Platform[] = [
  Platform.FACEBOOK,
  Platform.ADWORDS,
  Platform.PINTEREST,
  Platform.LINKEDIN,
  Platform.DV360,
  Platform.TIKTOK,
  Platform.TWITTER,
  Platform.SNAPCHAT,
];

export enum ORGANIZATION_USER_FILTER_BY {
  VIDMOB_ONLY = 'VIDMOB_ONLY',
  NO_VIDMOB = 'NO_VIDMOB',
}

export const PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP: Record<string, string> = {
  [Platform.FACEBOOK]: 'Meta',
  [Platform.SNAPCHAT]: 'Snapchat',
  [Platform.TIKTOK]: 'TikTok',
  [Platform.PINTEREST]: 'Pinterest',
  [Platform.DV360]: 'DV360',
  [Platform.LINKEDIN]: 'LinkedIn',
  [Platform.TWITTER]: 'X',
  [Platform.REDDIT]: 'Reddit',
  [Platform.ADWORDS]: 'Google Ads',
  [Platform.AMAZON_ADVERTISING]: 'Amazon Advertising',
  [Platform.AMAZON_ADVERTISING_DSP]: 'Amazon DSP',
  [Platform.FACEBOOK_PAGE]: 'Facebook Pages',
  [Platform.INSTAGRAM_PAGE]: 'Instagram Pages',
  [ALL_PLATFORMS]: 'Standard Criteria',
  [ALL_PLATFORMS.toLowerCase()]: 'Standard Criteria',
  amazon: 'Amazon',
};
