export const API_TITLE = 'ACS BFF API';
export const API_DESCRIPTION = 'API for ACS';
export const API_TAG_NAME = 'acs-bff-api';
export const API_VERSION = '1.0';
export const DEFAULT_RESPONSE_DESCRIPTION =
  'Internal Server Error in the Authorizer Service.';
export const DEFAULT_RESPONSE_ERROR_TYPE = 'INTERNAL_ERROR';
export const SERVICE_NAME = 'vidmob-acs-bff';
export const URL_ENCODED_BODY_SIZE_LIMIT = '2mb';
export const URL_ENCODED_DEFAULT_BODY_SIZE_LIMIT = '100kb';
// Make it equal to what we have on api-server
export const STRICT_TRANSPORT_SECURITY_MAX_AGE = 2592000;
