export const AS_PLATFORM_PERMISSION_UPDATE_URL =
  '/api/v1/platformPermission/:platform/adAccount/:adAccountId';
export const AS_PLATFORM_PERMISSIONS_GET_URL =
  '/api/v1/user/:userId/platformPermission/:platform/adAccount?useDBRecordAsSourceOfTruth=false';

export const AMS_PLATFORM_PERMISSIONS_GET_BY_USER_URL =
  '/api/v1/platform/:platform/user/:userId/platform_ad_accounts';

export const AMS_PLATFORM_PERMISSIONS_GET_URL =
  '/api/v1/platform/:platform/platform_ad_accounts';

export const AS_PLATFORM_ACCOUNT_GET_URL =
  '/api/v1/platformAccount/:platform/adAccount/:adAccountId';

export const AS_PLATFORM_ACCOUNT_GROUP_GET_URL =
  '/api/v1/platformAccountGroup/:groupId';
export const AS_PLATFORM_ACCOUNT_ANALYTICS_STATS_POST_URL =
  '/api/v1/analytics/:platform';

export const AS_PLATFORM_DIMENSION_GROUPS_URL =
  '/api/v1/platform/:platform/dimensionGroup';

export const AS_PLATFORM_DIMENSION_GROUP_BY_TYPE_URL =
  '/api/v1/platform/:platform/dimensionGroup/:groupType';

export const AS_CLEAR_CACHE_URL = '/api/v1/analyticsCache';

export const AS_PLATFORM_MEDIA_URL = '/api/v2/platformMedia';
