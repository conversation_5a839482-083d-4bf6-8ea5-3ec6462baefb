import { BatchType } from '../scoring/reports/constants/constants';

export enum Platform {
  TWITTER = 'TWITTER',
  FACEBOOK = 'FACEBOOK',
  TIKTOK = 'TIKTOK',
  PINTEREST = 'PINTEREST',
  LINKEDIN = 'LINKEDIN',
  DV360 = 'DV360',
  ADWORDS = 'ADWORDS',
  SNAPCHAT = 'SNAPCHAT',
  AMAZONADVERTISING = 'AMAZONADVERTISING',
  AMAZONADVERTISINGDSP = 'AMAZON_DSP',
  REDDIT = 'REDDIT',
}

export const assetSourceDisplayNames: Record<BatchType, string> = {
  [BatchType.InFlight]: 'In-flight',
  [BatchType.PreFlight]: 'Pre-flight',
};

export const platformDisplayNames: Record<Platform, string> = {
  [Platform.TWITTER]: 'X',
  [Platform.FACEBOOK]: 'Meta',
  [Platform.TIKTOK]: 'TikTok',
  [Platform.PINTEREST]: 'Pinterest',
  [Platform.LINKEDIN]: 'LinkedIn',
  [Platform.DV360]: 'DV360',
  [Platform.ADWORDS]: 'Google Ads',
  [Platform.SNAPCHAT]: 'Snapchat',
  [Platform.AMAZONADVERTISING]: 'Amazon Advertising',
  [Platform.AMAZONADVERTISINGDSP]: 'Amazon DSP',
  [Platform.REDDIT]: 'Reddit',
};
