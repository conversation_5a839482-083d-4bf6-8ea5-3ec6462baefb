export enum INSIGHT_NON_ARCHIVED_STATUS {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
}

export enum INSIGHT_NON_DELETED_STATUS {
  ARCHIVED = 'ARCHIVED',
  ARCHIVED_DRAFT = 'ARCHIVED_DRAFT',
  ARCHIVED_SCHEDULED = 'ARCHIVED_SCHEDULED',
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  SCHEDULED = 'SCHEDULED',
}

export enum INSIGHT_TYPE {
  BRAND = 'BRAND',
  INDUSTRY = 'INDUSTRY',
  PLATFORM = 'PLATFORM',
  COPILOT_BRAND = 'COPILOT_BRAND',
  COPILOT_INDUSTRY = 'COPILOT_INDUSTRY',
}

export enum INSIGHT_SOURCE {
  AUTOMATED = 'AUTOMATED',
  MANUAL = 'MANUAL',
}
