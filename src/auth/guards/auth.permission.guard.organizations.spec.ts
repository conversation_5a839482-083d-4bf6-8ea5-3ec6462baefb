import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, CanActivate } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionAction } from '../enums/permission.action.enum';
import { PermissionSubResource } from '../enums/permission.subresource.enum';
import { PermissionDomain } from '../enums/permission.domain.enum';
import {
  Permissions,
  organizationFromParamsHandler,
  organizationFromQueryHandler,
  partnerFromBodyHandler,
  workspaceFromParamsHandler,
} from '../decorators/permission.decorator';
import { AuthPermissionsGuard } from './auth.permission.guard';
import {
  AuthorizationService,
  CanAccess200Response,
  CanAccessPermissionsResponseDtoDomainId,
} from '@vidmob/vidmob-authorization-service-sdk';

class TestProtectionOnController {
  @Permissions({
    domain: PermissionDomain.ORGANIZATION,
    domainContextHandler: organizationFromParamsHandler,
    required: [
      {
        action: PermissionAction.READ,
        subresource: PermissionSubResource.DETAILS,
      },
      {
        action: PermissionAction.CREATE,
        subresource: PermissionSubResource.USER_INVITE,
      },
    ],
  })
  getProtectedOrgEndpoint1() {
    return 'Protected Endpoint!';
  }

  @Permissions({
    domain: PermissionDomain.ORGANIZATION,
    domainContextHandler: organizationFromQueryHandler,
    required: [
      {
        action: PermissionAction.UPDATE,
        subresource: PermissionSubResource.DETAILS,
      },
    ],
  })
  getProtectedOrgEndpoint2() {
    return 'Protected Endpoint!';
  }

  @Permissions({
    domain: PermissionDomain.ORGANIZATION,
    domainContextHandler: partnerFromBodyHandler, //wrong handler
    required: [
      {
        action: PermissionAction.UPDATE,
        subresource: PermissionSubResource.DETAILS,
      },
    ],
  })
  getProtectedEndpointWrongHandler() {
    return 'Protected Endpoint!';
  }

  @Permissions({
    domain: PermissionDomain.ORGANIZATION,
    domainContextHandler: organizationFromQueryHandler,
    required: [
      {
        action: PermissionAction.DELETE,
        subresource: PermissionSubResource.DETAILS,
      },
    ],
  })
  getProtectedEndpointWithNoPermission() {
    return 'Protected Endpoint!';
  }

  @Permissions([
    {
      domain: PermissionDomain.ORGANIZATION,
      domainContextHandler: organizationFromQueryHandler,
      required: [
        {
          action: PermissionAction.DELETE,
          subresource: PermissionSubResource.DETAILS,
        },
      ],
    },
    {
      domain: PermissionDomain.WORKSPACE,
      domainContextHandler: workspaceFromParamsHandler,
      required: [
        {
          action: PermissionAction.UPDATE,
          subresource: PermissionSubResource.DETAILS,
        },
      ],
    },
  ])
  getEndpointWithMultiplePermissionLevels1() {
    return 'Endpoint With Both Partner and Org Level Permissions!';
  }

  @Permissions([
    {
      domain: PermissionDomain.ORGANIZATION,
      domainContextHandler: organizationFromQueryHandler,
      required: [
        {
          action: PermissionAction.DELETE,
          subresource: PermissionSubResource.DETAILS,
        },
      ],
    },
    {
      domain: PermissionDomain.WORKSPACE,
      domainContextHandler: workspaceFromParamsHandler,
      required: [
        {
          action: PermissionAction.DELETE,
          subresource: PermissionSubResource.DETAILS,
        },
      ],
    },
  ])
  getEndpointWithMultiplePermissionLevels2() {
    return 'Endpoint With Both Partner and Org Level Permissions!';
  }
}

const generateContext = (method: string, id: number) => {
  const request = {
    headers: { authorization: 'FAKE_TOKEN' },
    params: { organizationId: 'uuid', workspaceId: 1234 },
    query: { organizationId: 'uuid' },
    body: { organizationId: 'uuid' },
  };
  const controller = new TestProtectionOnController();

  // get the function on controller from the method string
  const handler = Reflect.get(controller, method);

  return {
    getClass: () => TestProtectionOnController,
    getHandler: () => handler,
    switchToHttp: () => ({ getRequest: () => request }),
  } as any;
};

describe('PermissionGuard', () => {
  let guard: CanActivate;
  let reflector: Reflector;

  beforeEach(async () => {
    reflector = new Reflector();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: AuthorizationService,
          useValue: {
            authorizationService: jest.fn(),
          },
        },
      ],
    }).compile();

    const service = module.get<AuthorizationService>(AuthorizationService);
    guard = new AuthPermissionsGuard(reflector, service);
    service.defaultHeaders = { authorization: '' };
    service.canAccess = jest.fn(
      ({
        domain = PermissionDomain.ORGANIZATION.toString(),
        domainId = 1,
        authorization = 'FAKE_TOKEN',
        checkAccess,
      }) => {
        guard = new AuthPermissionsGuard(reflector, service);
        const ret = new Observable<AxiosResponse<CanAccess200Response>>(
          (subscriber) => {
            const domainIdResponse =
              domainId as CanAccessPermissionsResponseDtoDomainId;
            subscriber.next({
              data: {
                status: 'OK',
                result: {
                  domainId: domainIdResponse,
                  canAccess: checkAccess[0].action !== PermissionAction.DELETE,
                },
              },
              status: 200,
              statusText: 'OK',
              headers: {} as any,
              config: {} as any,
              request: {} as any,
            });
            subscriber.complete();
          },
        );

        return ret;
      },
    );
  });

  it('should allow access if one or more of multiple required permissions are met', async () => {
    const context = generateContext(
      'getEndpointWithMultiplePermissionLevels1',
      1,
    );

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });

  it('should deny access if none of multiple required permissions are not met', async () => {
    const context = generateContext(
      'getEndpointWithMultiplePermissionLevels2',
      1,
    );

    const res = await guard.canActivate(context);
    expect(res).toBe(false);
  });

  it('should allow access Organization resource when user has the required permissions', async () => {
    const context = generateContext('getProtectedOrgEndpoint1', 1);

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });

  it('should allow access Organization resource when user has the required permissions', async () => {
    const context = generateContext('getProtectedOrgEndpoint2', 10);

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });

  it('should deny access Organization resource when user does not have the required permissions', async () => {
    const context = generateContext('getProtectedEndpointWithNoPermission', 2);

    const res = await guard.canActivate(context);
    expect(res).toBe(false);
  });

  it('should deny access to Organization resource due the decorator has set with a wrong handler', async () => {
    const context = generateContext('getProtectedEndpointWrongHandler', 3);

    try {
      await guard.canActivate(context);
      fail('This test  is expected to fail.');
    } catch (e) {
      expect(e).toBeInstanceOf(BadRequestException);
    }
  });
});
