import { Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, CanActivate } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PermissionAction } from '../enums/permission.action.enum';
import { PermissionSubResource } from '../enums/permission.subresource.enum';
import { PermissionDomain } from '../enums/permission.domain.enum';
import {
  Permissions,
  partner<PERSON>rom<PERSON>uery<PERSON>and<PERSON>,
  partnerFromParamsHandler,
  organizationFromParamsHandler,
  projectFromQueryHandler,
  partnerFromBodyHandler,
  projectFromParamsHandler,
} from '../decorators/permission.decorator';
import { AuthPermissionsGuard } from './auth.permission.guard';
import { AuthorizationService, CanAccess200Response, CanAccessPermissionsResponseDtoDomainId } from '@vidmob/vidmob-authorization-service-sdk';

class TestProtectionOnController {
  @Permissions({
    domain: PermissionDomain.WORKSPACE,
    domainContextHandler: partnerFromParamsHandler,
    required: [
      {
        action: PermissionAction.READ,
        subresource: PermissionSubResource.DETAILS,
      },
      {
        action: PermissionAction.CREATE,
        subresource: PermissionSubResource.USER_INVITE,
      },
    ],
  })
  getProtectedParamsPartnerEndpoint() {
    return 'Protected Endpoint!';
  }

  @Permissions({
    domain: PermissionDomain.PROJECT,
    domainContextHandler: projectFromParamsHandler,
    required: [
      {
        action: PermissionAction.UPDATE,
        subresource: PermissionSubResource.DETAILS,
      },
    ],
  })
  getProtectedParamsProjEndpoint() {
    return 'Protected Endpoint!';
  }

  @Permissions({
    domain: PermissionDomain.PROJECT,
    domainContextHandler: projectFromQueryHandler,
    required: [
      {
        action: PermissionAction.UPDATE,
        subresource: PermissionSubResource.DETAILS,
      },
    ],
  })
  getProtectedQueryProjEndpoint() {
    return 'Protected Endpoint!';
  }  

  @Permissions({
    domain: PermissionDomain.WORKSPACE,
    domainContextHandler: partnerFromBodyHandler, 
    required: [
      {
        action: PermissionAction.DELETE,
        subresource: PermissionSubResource.IDEATION_DRAFT_OWN,
      },
    ],
  })
  getProtectedEndpointBodyPartnerWithNoPermission() {
    return 'Protected Endpoint!';
  }

  @Permissions({
    domain: PermissionDomain.WORKSPACE,
    domainContextHandler: partnerFromQueryHandler, 
    required: [
      {
        action: PermissionAction.DELETE,
        subresource: PermissionSubResource.IDEATION_DRAFT_OWN,
      },
    ],
  })
  getProtectedEndpointQueryPartnerWithNoPermission() {
    return 'Protected Endpoint!';
  } 

  @Permissions({
    domain: PermissionDomain.WORKSPACE,
    domainContextHandler: organizationFromParamsHandler, //wrong handler
    required: [
      {
        action: PermissionAction.UPDATE,
        subresource: PermissionSubResource.PROJECT,
      },
    ],
  })
  getProtectedEndpointWrongHandler() {
    return 'Protected Endpoint!';
  }
}

const generateContext = (method: string, id: number) => {
  const request = {
    headers: { authorization: 'FAKE_TOKEN' },
    params: { partnerId: id, projectId: id, workspaceId: id },
    query: { partnerId: id, projectId: id, workspaceId: id   },
    body: { partnerId: id, projectId: id, workspaceId: id   },
  };
  const controller = new TestProtectionOnController();

  // get the function on controller from the method string
  const handler = Reflect.get(controller, method);

  return {
    getClass: () => TestProtectionOnController,
    getHandler: () => handler,
    switchToHttp: () => ({ getRequest: () => request }),
  } as any;
};

describe('PermissionGuard', () => {
  let guard: CanActivate;
  let reflector: Reflector;

  beforeEach(async () => {
    reflector = new Reflector();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: AuthorizationService,
          useValue: {
            authorizationService: jest.fn(),
          },
        },
      ],
    }).compile();

    const service = module.get<AuthorizationService>(AuthorizationService);
    guard = new AuthPermissionsGuard(reflector, service);
    service.defaultHeaders = { authorization: '' };
    service.canAccess = jest.fn((
      {
        domain = PermissionDomain.WORKSPACE.toString(),
        domainId = 1,
        authorization = 'FAKE_TOKEN',
        checkAccess
      }
    ) => {
      guard = new AuthPermissionsGuard(reflector, service);
      const ret = new Observable<AxiosResponse<CanAccess200Response>>((subscriber) => {
        const domainIdResponse = domainId as CanAccessPermissionsResponseDtoDomainId;
        subscriber.next({
          data: { status: 'OK', result: { domainId: domainIdResponse, canAccess: (checkAccess[0].action !== PermissionAction.DELETE) } },
          status: 200,
          statusText: 'OK',
          headers: {} as any,
          config: {} as any,
          request: {} as any
        });
        subscriber.complete();
      });

      return ret;

    });
  });

  it('should allow access Partner resource when user has the required permissions', async () => {
    const context = generateContext('getProtectedParamsPartnerEndpoint', 1);

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });

  it('should allow access Project resource when user has the required permissions', async () => {
    const context = generateContext('getProtectedParamsProjEndpoint', 10);

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });

  it('should allow access Project resource when user has the required permissions', async () => {
    const context = generateContext('getProtectedQueryProjEndpoint', 1);

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });

  it('should allow access Project resource when user has the required permissions', async () => {
    const context = generateContext('getProtectedParamsProjEndpoint', 10);

    const res = await guard.canActivate(context);
    expect(res).toBe(true);
  });  

  it('should deny access Partner resource when user does not have the required permissions', async () => {
    const context = generateContext('getProtectedEndpointBodyPartnerWithNoPermission', 2);

    const res = await guard.canActivate(context);
    expect(res).toBe(false);
  });

  it('should deny access Partner resource when user does not have the required permissions', async () => {
    const context = generateContext('getProtectedEndpointQueryPartnerWithNoPermission', 2);

    const res = await guard.canActivate(context);
    expect(res).toBe(false);
  });  

  it('should deny access Partner resource due the decorator has set with a wrong handler', async () => {
    const context = generateContext('getProtectedEndpointWrongHandler', 3);

    try {
      await guard.canActivate(context);
      fail('This test  is expected to fail.');
    } catch (e) {
      expect(e).toBeInstanceOf(BadRequestException);
    }
  });
});
