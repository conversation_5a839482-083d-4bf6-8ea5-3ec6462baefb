import { Modu<PERSON> } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { AuthPermissionsGuard } from './guards/auth.permission.guard';
import { AuthTokenGuard } from './guards/auth.token.guard';
import { AuthorityGuard } from './guards/auth.authority.guard';

@Module({
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthTokenGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthPermissionsGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthorityGuard,
    },
  ],
})
export class AuthModule {}
