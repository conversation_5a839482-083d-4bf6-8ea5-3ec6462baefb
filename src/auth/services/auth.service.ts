// authorization.service.ts
import { Injectable } from '@nestjs/common';
import { AxiosResponse } from 'axios';
import { Observable } from 'rxjs';
import { firstValueFrom } from 'rxjs';
import {
  AuthorizationService,
  CanAccess200Response,
  StatementPermissionDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { PermissionDomain } from '../enums/permission.domain.enum';
import {
  checkAccountDetailsReadAccess,
  createOrganizationAdAccountsMetadata,
  createWorkspaceAdAccountsMetadata,
  createWorkspaceStandardUserInvite,
  createWorkspaceUserInvite,
  readWorkspaceUsers,
} from '../../account-management/account-management.permissions';

@Injectable()
export class AuthService {
  constructor(private readonly authorizationService: AuthorizationService) {}

  async isResourcePermissionValid(
    domain: PermissionDomain,
    domainId: number | string,
    authorization: string,
    checkAccess: StatementPermissionDto[],
  ): Promise<boolean> {
    try {
      const responseObservable: Observable<
        AxiosResponse<CanAccess200Response>
      > = this.authorizationService.canAccess({
        domain,
        domainId,
        authorization,
        checkAccess,
      });

      const response: AxiosResponse = await firstValueFrom(responseObservable);
      return response?.data?.result?.canAccess;
    } catch (e) {
      return false;
    }
  }

  async canAccessOrganizationAdAccountDetails(
    organizationId: string,
    authorization: string,
  ): Promise<boolean> {
    return this.isResourcePermissionValid(
      PermissionDomain.ORGANIZATION,
      organizationId,
      authorization,
      checkAccountDetailsReadAccess.statements,
    );
  }

  async canAccessWorkspaceAdAccountDetails(
    workspaceId: number,
    authorization: string,
  ): Promise<boolean> {
    return this.isResourcePermissionValid(
      PermissionDomain.WORKSPACE,
      workspaceId,
      authorization,
      checkAccountDetailsReadAccess.statements,
    );
  }

  async canAccessOrganizationOrWorkspaceAdAccountDetails(
    organizationId: string,
    workspaceId: number,
    authorization: string,
  ): Promise<boolean> {
    const organizationPermissionIsValid =
      await this.canAccessOrganizationAdAccountDetails(
        organizationId,
        authorization,
      );

    if (organizationPermissionIsValid) return true;

    const workspacePermissionIsValid =
      await this.canAccessWorkspaceAdAccountDetails(workspaceId, authorization);

    if (workspacePermissionIsValid) return true;

    return false;
  }

  async canAccessOrganizationOrWorkspaceAdAccountMetadata(
    organizationId: string,
    workspaceId: number,
    authorization: string,
  ): Promise<boolean> {
    const organizationPermissionIsValid = await this.isResourcePermissionValid(
      PermissionDomain.ORGANIZATION,
      organizationId,
      authorization,
      createOrganizationAdAccountsMetadata.statements,
    );

    if (organizationPermissionIsValid) return true;

    const workspacePermissionIsValid = await this.isResourcePermissionValid(
      PermissionDomain.WORKSPACE,
      workspaceId,
      authorization,
      createWorkspaceAdAccountsMetadata.statements,
    );

    if (workspacePermissionIsValid) return true;

    return false;
  }

  async canAccessWorkspaceInvite(
    workspaceId: number,
    authorization: string,
  ): Promise<boolean> {
    const workspaceStandardPermissionIsValid =
      await this.isResourcePermissionValid(
        PermissionDomain.WORKSPACE,
        workspaceId,
        authorization,
        createWorkspaceStandardUserInvite.statements,
      );

    if (workspaceStandardPermissionIsValid) return true;

    const workspacePermissionIsValid = await this.isResourcePermissionValid(
      PermissionDomain.WORKSPACE,
      workspaceId,
      authorization,
      createWorkspaceUserInvite.statements,
    );

    return workspacePermissionIsValid;
  }
}
