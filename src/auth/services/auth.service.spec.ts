import { Test } from '@nestjs/testing';
import { AuthService } from './auth.service';
import { AuthorizationService } from '@vidmob/vidmob-authorization-service-sdk';
import { of } from 'rxjs';
import { AxiosResponse } from 'axios';

const TEST_WORKSPACE_ID = 1;
const TEST_USER_AUTHORIZATION = 'bearer token';

describe('AuthService', () => {
  let authService: AuthService;
  let authorizationServiceSdk: AuthorizationService;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: AuthorizationService, useValue: { canAccess: jest.fn() } },
      ],
    }).compile();

    authService = moduleRef.get<AuthService>(AuthService);
    authorizationServiceSdk =
      moduleRef.get<AuthorizationService>(AuthorizationService);
  });

  it('should be defined', () => {
    expect(authService).toBeDefined();
  });

  describe('canAccessWorkspaceInvite', () => {
    afterEach(() => jest.restoreAllMocks());

    const canAccess = {
      data: { result: { canAccess: true } },
    } as AxiosResponse;

    const cannotAccess = {
      data: { result: { canAccess: false } },
    } as AxiosResponse;

    it('should return true if user has user invite permission', async () => {
      jest
        .spyOn(authorizationServiceSdk, 'canAccess')
        .mockReturnValueOnce(of(canAccess));

      await expect(
        authService.canAccessWorkspaceInvite(
          TEST_WORKSPACE_ID,
          TEST_USER_AUTHORIZATION,
        ),
      ).resolves.toBeTruthy();
    });

    it('should return true if user has standard_user invite permission', async () => {
      jest
        .spyOn(authorizationServiceSdk, 'canAccess')
        .mockReturnValueOnce(of(cannotAccess));

      jest
        .spyOn(authorizationServiceSdk, 'canAccess')
        .mockReturnValueOnce(of(canAccess));

      await expect(
        authService.canAccessWorkspaceInvite(
          TEST_WORKSPACE_ID,
          TEST_USER_AUTHORIZATION,
        ),
      ).resolves.toBeTruthy();
    });

    it('should return false if user does not have user invite and standard user invite permission', async () => {
      jest
        .spyOn(authorizationServiceSdk, 'canAccess')
        .mockReturnValueOnce(of(cannotAccess));

      jest
        .spyOn(authorizationServiceSdk, 'canAccess')
        .mockReturnValueOnce(of(cannotAccess));

      await expect(
        authService.canAccessWorkspaceInvite(
          TEST_WORKSPACE_ID,
          TEST_USER_AUTHORIZATION,
        ),
      ).resolves.toBeFalsy();
    });
  });
});
