export enum PermissionSubResource {
  ACCEPTED_BID = 'accepted_bid',
  ACCEPTED_DATA_REPORT_ITERATION = 'accepted_data_report_iteration',
  ACCEPTED_IDEATION_ITERATION = 'accepted_ideation_iteration',
  ACCEPTED_ITERATION = 'accepted_iteration',
  AD_ACCOUNT = 'ad_account',
  AD_ACCOUNT_DETAILS = 'ad_account_details',
  ADMIN_PROJECT_BRIEF_NOTE = 'admin_project_brief_note',
  ADMIN_PROJECT_BRIEF_NOTE_COMMENT = 'admin_project_brief_note_comment',
  ALL_PROJECT_MILESTONE = 'all_project_milestone',
  ALLOW_NEW_MEDIA = 'allow_new_media',
  ANNOTATION = 'annotation',
  ANNOTATION_COLLABORATION = 'annotation_collaboration',
  ANNOTATION_CREATOR = 'annotation_creator',
  ANNOTATION_PROJECT = 'annotation_project',
  ANNOTATION_VISIBILITY = 'annotation_visibility',
  API_KEY = 'api_key',
  APPROVED_ANNOTATION = 'approved_annotation',
  ASSET_LOCKER = 'asset_locker',
  BID = 'bid',
  BRAND = 'brand',
  BULK_USER_WORKSPACE_ASSIGNED = 'bulk_user_workspace_assigned',
  BUSINESS_ADDRESS = 'business_address',
  CLIENT_PROJECT_BRIEF_NOTE = 'client_project_brief_note',
  CLIENT_PROJECT_BRIEF_NOTE_COMMENT = 'client_project_brief_note_comment',
  CLIENT_RATING = 'client_rating',
  COLLABORATOR = 'collaborator',
  COLLABORATOR_INVITE = 'collaborator_invite',
  CONCEPT_ALL_CLIENT_SELF_SERVICE = 'concept_all_client_self_service',
  CONCEPT_REVIEW_CLIENT_SELF_SERVICE = 'concept_review_client_self_service',
  CONCEPT_REVIEWER_CLIENT_SELF_SERVICE = 'concept_reviewer_client_self_service',
  CONFIGURATION = 'configuration',
  CONTEXT_FROM_CREATOR = 'context_from_creator',
  CONTEXT_TO_CLIENT = 'context_to_client',
  CREATOR_FILES_ALL = 'creator_files_all',
  CREATOR_FILES_OWN = 'creator_files_own',
  CREATOR_PROJECT_BRIEF_NOTE = 'creator_project_brief_note',
  CREATOR_PROJECT_BRIEF_NOTE_COMMENT = 'creator_project_brief_note_comment',
  CRITERIA = 'criteria',
  CRITERIA_SET = 'criteria_set',
  CRITERIA_GROUP = 'criteria_group',
  DATA_EXPORT = 'data_export',
  DATA_REPORT_DRAFT = 'data_report_draft',
  DATA_REPORT_DRAFT_ALL_ACCEPTED = 'data_report_draft_all_accepted',
  DATA_REPORT_DRAFT_ALL_APPROVED = 'data_report_draft_all_approved',
  DATA_REPORT_DRAFT_ALL_UNAPPROVED = 'data_report_draft_all_unapproved',
  DATA_REPORT_DRAFT_OWN_INCOMPLETE = 'data_report_draft_own_incomplete',
  DATA_REPORT_DRAFT_REVIEW = 'data_report_draft_review',
  DATA_REPORT_DRAFT_REVIEW_FINAL = 'data_report_draft_review_final',
  DATA_REPORT_DRAFT_REVIEWER = 'data_report_draft_reviewer',
  DETAILS = 'details',
  DOWNLOAD = 'download',
  DRAFT = 'draft',
  DRAFT_REVIEW = 'draft_review',
  DRAFT_REVIEW_FINAL = 'draft_review_final',
  DRAFT_REVIEWER = 'draft_reviewer',
  EDITOR_PAYOUT = 'editor_payout',
  FINAL = 'final',
  FINAL_ASSETS = 'final_assets',
  FINAL_ASSETS_CLIENT_NOTIFIED = 'final_assets_client_notified',
  FOLDER = 'folder',
  GROUP_CHAT = 'group_chat',
  HIL_CODER = 'hil_coder',
  HIL_MEDIA = 'hil_media',
  HIL_QUESTION = 'hil_question',
  HIL_QUESTION_CODER_RESPONSE = 'hil_question_coder_response',
  HIL_QUESTION_CODER_RESPONSE_ALL = 'hil_question_coder_response_all',
  HIL_QUESTION_RESULT = 'hil_question_result',
  IDEATION_CONCEPT_ALL_ACCEPTED = 'ideation_concept_all_accepted',
  IDEATION_CONCEPT_DRAFT_ALL_APPROVED = 'ideation_concept_draft_all_approved',
  IDEATION_DRAFT_ALL = 'ideation_draft_all',
  IDEATION_DRAFT_ALL_APPROVED = 'ideation_draft_all_approved',
  IDEATION_DRAFT_ALL_UNAPPROVED = 'ideation_draft_all_unapproved',
  IDEATION_DRAFT_OWN = 'ideation_draft_own',
  IDEATION_DRAFT_OWN_INCOMPLETE = 'ideation_draft_own_incomplete',
  IDEATION_DRAFT_REVIEW = 'ideation_draft_review',
  IDEATION_DRAFT_REVIEW_ALL = 'ideation_draft_review_all',
  IDEATION_DRAFT_REVIEW_FINAL = 'ideation_draft_review_final',
  IDEATION_DRAFT_REVIEWER = 'ideation_draft_reviewer',
  IDEATION_VIDSCRIPT_DRAFT_ALL_APPROVED = 'ideation_vidscript_draft_all_approved',
  INCOMPLETE_DRAFT = 'incomplete_draft',
  INDUSTRY = 'industry',
  INSIGHT_FULL_DETAIL_IN_PROJECT = 'insight_full_detail_in_project',
  INSIGHT_FOLDER = 'insight_folder',
  INVITE = 'invite',
  LOGIN = 'login',
  MEDIA = 'media',
  MEDIA_BIDDING = 'media_bidding',
  MEDIA_BUILDING = 'media_building',
  MEDIA_BUNDLE = 'media_bundle',
  MEDIA_EDITING = 'media_editing',
  MEDIA_FOLDER = 'media_folder',
  MEDIA_NOTE = 'media_note',
  MEDIA_ORDER = 'media_order',
  MEDIA_SYNC = 'media_sync',
  MESSAGE_ACCOUNT_MANAGER = 'message_account_manager',
  MESSAGE_ACCOUNT_MANAGER_EDITING = 'message_account_manager_editing',
  MESSAGE_EDITOR = 'message_editor',
  MESSAGE_GROUP_CHAT = 'message_group_chat',
  MOBFEED = 'mobfeed',
  OWN_CONTEXT_FROM_CREATOR = 'own_context_from_creator',
  PARTNER_PURCHASE = 'partner_purchase',
  PROJECT = 'project',
  PARTNER = 'partner',
  PROJECT_ADMIN = 'project_admin',
  PROJECT_CREATOR = 'project_creator',
  PROJECT_INSIGHT = 'project_insight',
  PROJECT_NO_PRODUCT = 'project_no_product',
  PROJECT_PURCHASE = 'project_purchase',
  PROJECT_RUSH_DELIVERY = 'project_rush_delivery',
  PROJECT_SURVEY = 'project_survey',
  PURCHASE = 'purchase',
  SCORE_OVERRIDE_REQUEST = 'score_override_request',
  SHARE = 'share',
  SHARE_STATS = 'share_stats',
  SOW = 'sow',
  SPLIT_PAYMENT = 'split_payment',
  SSO_CONFIGURATION = 'sso_configuration',
  STANDARD_USER_INVITE = 'standard_user_invite',
  STOCK_ASSET = 'stock_asset',
  STOCK_ASSET_NOTIFICATION = 'stock_asset_notification',
  STOCK_ASSET_RECOMMENDATION = 'stock_asset_recommendation',
  STOCK_ASSET_RECOMMENDATION_EDITING = 'stock_asset_recommendation_editing',
  SUBMIT = 'submit',
  UNAPPROVED_DRAFT = 'unapproved_draft',
  UPCHARGE = 'upcharge',
  USER = 'user',
  USER_INVITE = 'user_invite',
  USER_ROLE = 'user_role',
  WORKSPACE_ALL = 'workspace_all',
  WORKSPACE_ASSIGNED = 'workspace_assigned',
  WORKSPACE = 'workspace',
  DASHBOARD = 'dashboard',
}
