import { fetchAllDataFromPagination } from './fetch-all-data';

describe('fetchAllDataFromPagination', () => {
  const data = ['data1', 'data2', 'data3'];
  let promiseRequest: jest.Mock;

  beforeEach(() => {
    jest.resetAllMocks();

    promiseRequest = jest
      .fn()
      .mockImplementation((offset: number, limit: number) => {
        const result = data.slice(offset, limit + offset);

        return Promise.resolve({
          result,
          status: 'OK',
          pagination: {
            offset,
            perPage: limit,
            nextOffset: offset + limit >= data.length ? 0 : limit,
            totalSize: data.length,
            queryId: null,
          },
        });
      });
  });

  it('should fetch all data one time', async () => {
    await expect(
      fetchAllDataFromPagination(promiseRequest, 3),
    ).resolves.toEqual(data);

    expect(promiseRequest).toHaveBeenCalledTimes(1);
  });

  it('should fetch data three times', async () => {
    await expect(
      fetchAllDataFromPagination(promiseRequest, 1),
    ).resolves.toEqual(data);

    expect(promiseRequest).toHaveBeenCalledTimes(3);
  });
});
