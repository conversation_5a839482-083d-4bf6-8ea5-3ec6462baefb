import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';

export function paginateMockData<T>(
  data: T[],
  paginationOptions: PaginationOptions,
): PaginatedResultArray<T> {
  const { offset = 0, perPage = 10 } = paginationOptions;

  const paginatedItems = data.slice(offset, offset + perPage);

  return new PaginatedResultArray<T>(
    paginatedItems,
    data.length,
    paginationOptions.queryId,
  );
}
