interface QueryData {
  query: string;
  bindings: any[];
}

export default function generateQuery(queryData: QueryData): string {
  let { query } = queryData;
  const { bindings } = queryData;

  // Replace each ? placeholder with the corresponding binding
  bindings.forEach((binding) => {
    const placeholder = '?';

    // Handle different data types
    const formattedValue = formatBinding(binding);

    // Replace first occurrence of ? with the formatted value
    query = query.replace(placeholder, formattedValue);
  });

  return query;
}

function formatBinding(value: any): string {
  if (value === null) {
    return 'NULL';
  }

  switch (typeof value) {
    case 'string':
      return `'${escapeString(value)}'`;
    case 'number':
      return value.toString();
    case 'boolean':
      return value ? '1' : '0';
    case 'object':
      if (value instanceof Date) {
        return `'${value.toISOString()}'`;
      }
      return `'${JSON.stringify(value)}'`;
    default:
      return 'NULL';
  }
}

function escapeString(str: string): string {
  return str.replace(/'/g, "''");
}
