/**
 * Some functions that are useful for generating stub data
 */

const randomIndex = (length: number): number =>
  Math.floor(Math.random() * length);

export const randomEnumValue = (enumObj: any) => {
  const values = Object.values(enumObj); // Get an array of the enum values
  return values[randomIndex(values.length)]; // Return the random value
};

export const randomListValue = (list: any): any => {
  if (list.length === 0) {
    throw new Error('The provided list must not be empty.');
  }
  return list[generateRandomInteger(0, list.length - 1)];
};

const generateRandom = (characters: string, length: number): string => {
  let result = '';

  for (let i = 0; i < length; i++) {
    result += characters[randomIndex(characters.length)];
  }

  return result;
};

export const generateRandomString = (length: number): string => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return generateRandom(characters, length);
};

export const generateRandomDecimal = (min: number, max: number) => {
  return Math.random() * (max - min) + min;
};

export const generateRandomInteger = (min: number, max: number): number => {
  return Math.floor(generateRandomDecimal(min, max));
};

export const generateRandomId = (length: number): number => {
  const characters = '0123456789';
  return parseInt(generateRandom(characters, length));
};

export const getRandomSopranosCharacter = (): {
  firstName: string;
  lastName: string;
} => {
  const sopranosCharacters = [
    { firstName: 'Tony', lastName: 'Soprano' },
    { firstName: 'Carmela', lastName: 'Soprano' },
    { firstName: 'Christopher', lastName: 'Moltisanti' },
    { firstName: 'Adriana', lastName: 'La Cerva' },
    { firstName: 'Dr. Jennifer', lastName: 'Melfi' },
    { firstName: 'Paulie', lastName: 'Gualtieri' },
    { firstName: 'Silvio', lastName: 'Dante' },
    { firstName: 'Meadow', lastName: 'Soprano' },
    { firstName: 'AJ', lastName: 'Soprano' },
    { firstName: 'Junior', lastName: 'Soprano' },
    { firstName: 'Artie', lastName: 'Bucco' },
    { firstName: 'Livia', lastName: 'Soprano' },
    { firstName: 'Charmaine', lastName: 'Bucco' },
    { firstName: 'Bobby', lastName: 'Bacala' },
  ];

  return randomListValue(sopranosCharacters);
};

export function generateRandomLoremWords(wordCount: number): string {
  const loremWords: string[] = [
    'lorem',
    'ipsum',
    'dolor',
    'sit',
    'amet',
    'consectetur',
    'adipiscing',
    'elit',
    'sed',
    'do',
    'eiusmod',
    'tempor',
    'incididunt',
    'ut',
    'labore',
    'et',
    'dolore',
    'magna',
    'aliqua',
    'ut',
    'enim',
    'ad',
    'minim',
    'veniam',
    'quis',
    'nostrud',
    'exercitation',
    'ullamco',
    'laboris',
    'nisi',
    'ut',
    'aliquip',
    'ex',
    'ea',
    'commodo',
    'consequat',
    'duis',
    'aute',
    'irure',
    'dolor',
    'in',
    'reprehenderit',
    'in',
    'voluptate',
    'velit',
    'esse',
    'cillum',
    'dolore',
    'eu',
    'fugiat',
    'nulla',
    'pariatur',
    'excepteur',
    'sint',
    'occaecat',
    'cupidatat',
    'non',
    'proident',
    'sunt',
    'in',
    'culpa',
    'qui',
    'officia',
    'deserunt',
    'mollit',
    'anim',
    'id',
    'est',
    'laborum',
  ];

  return Array(wordCount)
    .fill('')
    .map((_) => {
      return loremWords[randomIndex(loremWords.length)];
    })
    .join(' ');
}

export function generateRandomStringDate(before: Date): string {
  const beforeMillis: number = before.getTime();
  const nowMillis: number = new Date().getTime();

  if (beforeMillis >= nowMillis) {
    throw new Error('The provided date must be before the current date.');
  }

  return new Date(generateRandomInteger(beforeMillis, nowMillis)).toUTCString();
}

export function generateRandomBoolean(): boolean {
  return [false, true][generateRandomInteger(0, 1)];
}

/**
 generate a list of count items
 the generator function should return the type of item you want to be in the list
 */
export const generateList = (
  count: number,
  generatorFunction: (index?: number) => any,
): any[] => {
  return Array(count)
    .fill('')
    .map((_, index) => generatorFunction(index));
};
