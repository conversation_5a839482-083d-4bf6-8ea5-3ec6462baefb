export const transformPlatform = (platform: string): string => {
  const platformMap: { [key: string]: string } = {
    FACEBOOK: 'Meta',
    TWITTER: 'X',
    LINKEDIN: 'LinkedIn',
    DV360: 'DV360',
  };

  if (platformMap[platform]) {
    return platformMap[platform]; // Use mapped value if it exists
  }

  return transformGeneralStringFormatting(platform);
};

export const transformMediaTypes = (
  mediaTypes: (string | null | undefined)[],
): string[] => {
  return mediaTypes
    .filter((mediaType) => mediaType && mediaType.trim()) // Remove null, undefined, or empty strings
    .map((mediaType) => {
      if (mediaType === 'ANIMATED_IMAGE') {
        return 'GIF';
      }

      return transformGeneralStringFormatting(mediaType);
    });
};

export const transformGeneralStringFormatting = (
  item: string | null | undefined,
): string => {
  if (!item) {
    return '';
  }

  return item
    .trim()
    .replace(/_/g, ' ')
    .toLowerCase()
    .replace(/\b\w/g, (char) => char.toUpperCase());
};
