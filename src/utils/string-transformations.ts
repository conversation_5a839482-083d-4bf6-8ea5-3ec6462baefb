import { PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP } from '../constants/platform.constants';

export const transformPlatform = (
  platform: string | null | undefined,
): string => {
  const platformLabel = platform
    ? PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP[platform.toLowerCase()]
    : platform;
  if (platformLabel) {
    return platformLabel; // Use mapped value if it exists
  }

  return transformGeneralStringFormatting(platform);
};

export const transformMediaTypes = (
  mediaTypes: (string | null | undefined)[],
): string[] => {
  return mediaTypes
    .filter((mediaType) => mediaType && mediaType.trim()) // Remove null, undefined, or empty strings
    .map((mediaType) => {
      if (mediaType === 'ANIMATED_IMAGE') {
        return 'GIF';
      }

      return transformGeneralStringFormatting(mediaType);
    });
};

export const transformGeneralStringFormatting = (
  item: string | null | undefined,
): string => {
  if (!item) {
    return '';
  }

  return item
    .trim()
    .replace(/_/g, ' ')
    .toLowerCase()
    .replace(/\b\w/g, (char) => char.toUpperCase());
};
