import { Stream } from 'stream';
import { generateCsvStream } from './csv-generation';

describe('generateCsvStream', () => {
  const data = [
    {
      id: 1,
      name: 'John Doe 1',
      workspaces: [
        {
          name: 'Workspace 1',
        },
        {
          name: 'Workspace 2',
        },
      ],
    },
    {
      id: 2,
      name: 'John Doe 2',
      workspaces: [
        {
          name: 'Workspace 2',
        },
      ],
    },
  ];

  async function streamToString(stream: Stream): Promise<string> {
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
      stream.on('error', (err) => reject(err));
      stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
    });
  }

  it('should retrieve a csv based on data', async () => {
    const csvStream = generateCsvStream(data, {
      ID: 'id',
      Name: 'name',
      'Workspace Names': (row) =>
        row.workspaces.map((workspace) => workspace.name).join(', '),
    });

    const csvData = await streamToString(csvStream);

    expect(csvData).toBe(
      `ID,Name,Workspace Names\n1,John Doe 1,\"Workspace 1, Workspace 2\"\n2,John Doe 2,Workspace 2`,
    );
  });
});
