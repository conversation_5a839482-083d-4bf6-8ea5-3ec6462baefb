export function searchMockData<T extends Record<string, any>>(
  data: T[],
  search?: string,
  fields?: (keyof T)[],
): T[] {
  if (!search) return data;

  return data.filter((item) => {
    const valuesToSearch: any[] = fields
      ? fields.map((field) => item[field])
      : Object.values(item);

    return valuesToSearch.some(
      (value) =>
        typeof value === 'string' &&
        value.toLowerCase().includes(search.toLowerCase()),
    );
  });
}
