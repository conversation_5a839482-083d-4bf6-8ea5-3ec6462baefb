import { Stream } from 'stream';
import * as fastCsv from 'fast-csv';

type getRowValue<T> = (row: T) => any;

interface DataHeaderMap<T> {
  [csvHeader: string]: getRowValue<T> | string;
}

/**
 * it retuns a csv stream for an array of data and a mapping between the csv header name and the data entry
 * @param data an array of data
 * @param dataHeaderMap a map between the actual CSV header and the data property to use it.
 */
export function generateCsvStream<T extends Record<string, any>>(
  data: T[],
  dataHeaderMap: DataHeaderMap<T>,
): Stream {
  const csvStream = new Stream.PassThrough();
  const csvWriter = fastCsv.format({ headers: true });

  csvWriter.pipe(csvStream);

  data.forEach((row) => {
    const csvData: Record<string, any> = {};

    Object.entries(dataHeaderMap).forEach(([header, rowProperty]) => {
      let rowValue =
        typeof rowProperty === 'function' ? rowProperty(row) : row[rowProperty];
      csvData[header] = rowValue;
    });

    csvWriter.write(csvData);
  });

  csvWriter.end();

  return csvStream;
}
