interface Pagination {
  offset: number;
  perPage: number;
  nextOffset: number;
  totalSize: number;
  queryId?: string;
}

interface Response<T> {
  status: string;
  result: Array<T>;
  pagination?: Pagination | null;
}

type RequestFunction<T> = (
  offset: number,
  limit: number,
) => Promise<Response<T>>;

const DEFAULT_LIMIT = 100;

/**
 * It handles fetching all data from a paginate request.
 * @param requestPromise the request promise, receives the offset and limit params
 * @param limit optional parameter, the limit of data to fetch for every request
 * @returns
 * @example fetchAllDataFromPagination<OrganizationUserResponseDto>(
 *    (offset, limit) =>
 *      this.organizationServiceSdk.findAllAsPromise(
 *         organizationId,
 *         '',
 *         filterBy || '',
 *         offset,
 *         limit,
 *         '',
 *       ),
 *     500,
 *   );
 */
export async function fetchAllDataFromPagination<T>(
  requestPromise: RequestFunction<T>,
  limit: number = DEFAULT_LIMIT,
): Promise<T[]> {
  const data: T[] = [];
  let hasMore = true;
  let offset = 0;

  while (hasMore) {
    const response = await requestPromise(offset, limit);

    data.push(...response.result);

    offset += limit;

    if (response.pagination) {
      hasMore = response.pagination.nextOffset > 0;
    } else {
      hasMore = response.result.length === limit;
    }
  }

  return data;
}
