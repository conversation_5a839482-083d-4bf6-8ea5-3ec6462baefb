import {
  transformGeneralStringFormatting,
  transformMediaTypes,
  transformPlatform,
} from './string-transformations';

describe('String Conversion Functions', () => {
  describe('transformPlatform', () => {
    it('should transform specific platforms based on the platformMap', () => {
      expect(transformPlatform('FACEBOOK')).toBe('Meta');
      expect(transformPlatform('TWITTER')).toBe('X');
      expect(transformPlatform('LINKEDIN')).toBe('LinkedIn');
      expect(transformPlatform('DV360')).toBe('DV360');
    });

    it('should apply general string formatting to unmapped platforms', () => {
      expect(transformPlatform('INSTAGRAM')).toBe('Instagram');
      expect(transformPlatform('PINTEREST')).toBe('Pinterest');
      expect(transformPlatform('SNAP_CHAT')).toBe('Snap Chat');
    });

    it('should return an empty string for null or undefined input', () => {
      expect(transformPlatform(null as any)).toBe('');
      expect(transformPlatform(undefined as any)).toBe('');
    });
  });

  describe('transformMediaTypes', () => {
    it('should transform ANIMATED_IMAGE to GIF', () => {
      expect(transformMediaTypes(['ANIMATED_IMAGE'])).toEqual(['GIF']);
    });

    it('should apply general string formatting to other media types', () => {
      expect(transformMediaTypes(['IMAGE', 'VIDEO', 'HTML5_BANNER'])).toEqual([
        'Image',
        'Video',
        'Html5 Banner',
      ]);
    });

    it('should remove null, undefined, or empty strings', () => {
      expect(
        transformMediaTypes([
          null,
          undefined,
          '',
          ' ',
          'VIDEO',
          'ANIMATED_IMAGE',
        ]),
      ).toEqual(['Video', 'GIF']);
    });

    it('should return an empty array if all mediaTypes are invalid', () => {
      expect(transformMediaTypes([null, undefined, '', ' '])).toEqual([]);
    });
  });

  describe('transformGeneralStringFormatting', () => {
    it('should replace underscores with spaces and capitalize each word', () => {
      expect(transformGeneralStringFormatting('TEST_STRING')).toBe(
        'Test String',
      );
      expect(transformGeneralStringFormatting('HELLO_WORLD')).toBe(
        'Hello World',
      );
    });

    it('should handle mixed case and trim the string', () => {
      expect(transformGeneralStringFormatting(' test_string ')).toBe(
        'Test String',
      );
      expect(transformGeneralStringFormatting('AnOtHeR_ExAmPlE')).toBe(
        'Another Example',
      );
    });

    it('should return an empty string for null, undefined, or empty input', () => {
      expect(transformGeneralStringFormatting(null)).toBe('');
      expect(transformGeneralStringFormatting(undefined)).toBe('');
      expect(transformGeneralStringFormatting('')).toBe('');
    });
  });
});
