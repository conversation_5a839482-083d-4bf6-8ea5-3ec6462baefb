import { SortOrder } from '../platform/connectors/dto/sortOptions.dto';

export function sortMockData<T>(
  data: T[],
  sortBy?: string,
  sortOrder: SortOrder = SortOrder.ASC,
): T[] {
  if (!sortBy) return data;

  return data.sort((a, b) => {
    const valueA = resolveNestedValue(a, sortBy);
    const valueB = resolveNestedValue(b, sortBy);

    if (typeof valueA === 'string' && typeof valueB === 'string') {
      if (sortOrder === SortOrder.ASC) {
        return valueA.localeCompare(valueB);
      } else {
        return valueB.localeCompare(valueA);
      }
    }

    if (sortOrder === SortOrder.ASC) {
      return valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
    } else {
      return valueA > valueB ? -1 : valueA < valueB ? 1 : 0;
    }
  });
}

function resolveNestedValue<T>(obj: T, path: string): any {
  return path.split('.').reduce((acc: any, key: string) => {
    if (acc && typeof acc === 'object' && key in acc) {
      return acc[key];
    }
    return undefined;
  }, obj);
}
