import { tracer } from 'dd-trace';

export function setTagOnParentSpan(tags: Map<string, any>) {
  try {
    const span = tracer.scope().active();
    if (span) {
      const rootSpan = (span.context() as any)._trace.started[0];
      if (rootSpan) {
        tags.forEach((value, key) => {
          rootSpan.setTag(key, value);
        });
      }
    }
  } catch (error: Error | unknown) {
    if (error instanceof Error)
      this.logger.error(
        `An unexpected error occurred trying to log: ${error.message}.`,
      );
  }
}
