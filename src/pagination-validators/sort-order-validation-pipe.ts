import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import { DashboardSortBy } from '../executive-dashboard/constants/constants';
import { SortOrder } from '../reports/model/sort-order';

/*
 if value is 'ASC' or 'DESC' returns the value as a string, otherwise returns 'DESC' as a string
 */
@Injectable()
export class SortOrderValidationPipe implements PipeTransform {
  transform(value: any): string {
    const allowedValues = ['ASC', 'DESC'];
    let sortOrder = value ? value.toUpperCase() : 'DESC';
    if (!allowedValues.includes(sortOrder)) {
      sortOrder = 'DESC';
    }
    return sortOrder;
  }
}

/*
if value is a sort order value ( 'ASC' or 'DESC' ) returns the value as a SortOrder, otherwise throws a bad request exception
 */
@Injectable()
export class StrictSortOrderValidationPipe implements PipeTransform {
  isSortOrder(value: string): value is SortOrder {
    return Object.keys(SortOrder).includes(value);
  }

  transform(value: string): SortOrder | undefined {
    if (!value) {
      return undefined;
    }

    const sortOrder = value.toUpperCase();
    if (this.isSortOrder(sortOrder)) {
      return sortOrder;
    }
    throw new BadRequestException(
      `Invalid sort order value ${sortOrder}, must be one of [${Object.values(
        SortOrder,
      )}]`,
    );
  }
}

/*
if value is a sortBy value ( 'LAST_UPDATED', 'CREATED_AT', etc. ) returns the value as a DashboardSortBy, otherwise throws a bad request exception
 */
@Injectable()
export class MultiSortByPipe implements PipeTransform {
  transform(value: string): DashboardSortBy[] {
    if (!value) return [];
    const parts = value.split(',').map((s) => s.trim());
    for (const p of parts) {
      if (!Object.values(DashboardSortBy).includes(p as any)) {
        throw new BadRequestException(
          `Invalid sortBy value '${p}', must be one of [${Object.values(
            DashboardSortBy,
          ).join(', ')}]`,
        );
      }
    }
    return parts as DashboardSortBy[];
  }
}

@Injectable()
export class MultiSortOrderPipe implements PipeTransform {
  transform(value: string): SortOrder[] {
    if (!value) {
      return [];
    }
    const parts = value.split(',').map((s) => s.trim().toUpperCase());
    for (const p of parts) {
      if (!Object.values(SortOrder).includes(p as any)) {
        throw new BadRequestException(
          `Invalid sortOrder value '${p}', must be one of [${Object.values(
            SortOrder,
          ).join(', ')}]`,
        );
      }
    }
    return parts as SortOrder[];
  }
}
