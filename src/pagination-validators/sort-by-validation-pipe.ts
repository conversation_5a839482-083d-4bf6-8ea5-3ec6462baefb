// pipes/sort-by-validation.pipe.ts
import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { SortBy } from '../reports/reports-enums';

@Injectable()
export class SortByValidationPipe implements PipeTransform {
  transform(value: any): SortBy {
    const allowedValues = Object.values(SortBy);
    if (!value || !allowedValues.includes(value)) {
      return SortBy.DateCreated; // Default value
    }
    return value;
  }
}

@Injectable()
export class StrictSortByValidationPipe implements PipeTransform {
  isSortByCaseSensitive(value: string): value is SortBy {
    return Object.values(SortBy).includes(value as SortBy);
  }

  transform(value: string): SortBy | undefined {
    if (!value) {
      return undefined;
    }
    if (value && this.isSortByCaseSensitive(value)) {
      return value;
    }
    throw new BadRequestException(
      `Invalid sort order value ${value}, must be one of [${Object.values(
        SortBy,
      )}]`,
    );
  }
}

// Modified StrictSortByValidationPipe to accept enum dynamically
@Injectable()
export class DynamicStrictSortByValidationPipe implements PipeTransform {
  constructor(private readonly validEnum: any) {}

  isSortByCaseSensitive(value: string): value is string {
    return Object.values(this.validEnum).includes(value as any);
  }

  transform(value: string): string | undefined {
    if (!value) {
      return undefined;
    }
    if (this.isSortByCaseSensitive(value)) {
      return value;
    }
    throw new BadRequestException(
      `Invalid sort order value "${value}", must be one of [${Object.values(
        this.validEnum,
      )}]`,
    );
  }
}
