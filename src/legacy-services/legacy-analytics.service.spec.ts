import { LegacyAnalyticsService } from './legacy-analytics.service';
import { Test } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { LegacyAnalyticsServiceStatsRequestDto } from '../dto/legacy-analytics-service-stats-request.dto';
import {
  AD_DIMENSION_BY_PLATFORM,
  Platform,
} from '../constants/platform.constants';
import axios from 'axios';
import { PlatformMediaRequestDto } from '../analytics/analytics-media/dto/platform-media-request.dto';
import { UserDetailsDto } from '../analytics/dto/user-details.dto';

jest.mock('axios');
let legacyAnalyticsService: LegacyAnalyticsService;

beforeEach(async () => {
  const moduleRef = await Test.createTestingModule({
    providers: [
      LegacyAnalyticsService,
      {
        provide: ConfigService,
        useValue: {
          get: jest.fn().mockReturnValue('mocked analytics service url'),
        },
      },
    ],
  }).compile();
  legacyAnalyticsService = moduleRef.get<LegacyAnalyticsService>(
    LegacyAnalyticsService,
  );
});

describe('LegacyAnalyticsService', () => {
  it('getAnalyticsDimensionValues correctly request and parse stats with only string values for dimensions', async () => {
    const mockRequest: LegacyAnalyticsServiceStatsRequestDto = {
      tokenWithBearerPrefix: 'mocked token',
      organizationId: 'mocked organization id',
      workspaceIds: [1, 2, 3],
      dimension: AD_DIMENSION_BY_PLATFORM[Platform.SNAPCHAT],
      platform: Platform.SNAPCHAT,
      adAccountIds: ['mocked ad account id'],
      dateRange: {
        startDate: 'mocked start date',
        endDate: 'mocked end date',
      },
    };

    (axios.post as jest.Mock).mockResolvedValue({
      data: {
        result: {
          stats: [
            { ad: '93ow0ne9oe4n9e' },
            { ad: '930w9jrir3jkoi3' },
            { ad: 'mduwieiekiow' },
            { ad: '03kj4ij44wkwl' },
          ],
        },
      },
    });

    const response = await legacyAnalyticsService.getAnalyticsDimensionValues(
      mockRequest,
    );

    expect(response).toEqual([
      { id: '93ow0ne9oe4n9e', name: '93ow0ne9oe4n9e' },
      { id: '930w9jrir3jkoi3', name: '930w9jrir3jkoi3' },
      { id: 'mduwieiekiow', name: 'mduwieiekiow' },
      { id: '03kj4ij44wkwl', name: '03kj4ij44wkwl' },
    ]);
  });

  it('getAnalyticsDimensionValues correctly request and parse stats with id and name values for dimensions', async () => {
    const mockRequest: LegacyAnalyticsServiceStatsRequestDto = {
      tokenWithBearerPrefix: 'mocked token',
      organizationId: 'mocked organization id',
      workspaceIds: [1, 2, 3],
      dimension: AD_DIMENSION_BY_PLATFORM[Platform.FACEBOOK],
      platform: Platform.FACEBOOK,
      adAccountIds: ['mocked ad account id'],
      dateRange: {
        startDate: 'mocked start date',
        endDate: 'mocked end date',
      },
    };

    (axios.post as jest.Mock).mockResolvedValueOnce({
      data: {
        result: {
          stats: [
            { ad: { id: '***************', name: 'name one' } },
            { ad: { id: '**************', name: 'name two' } },
            { ad: { id: '*************', name: 'name three' } },
            { ad: { id: '*************', name: 'name four' } },
          ],
        },
      },
    });
    (axios.post as jest.Mock).mockResolvedValueOnce({
      data: {
        result: {
          stats: [{ ad: { id: '*****************', name: 'name five' } }],
        },
      },
    });

    const response = await legacyAnalyticsService.getAnalyticsDimensionValues(
      mockRequest,
    );

    expect(response).toEqual([
      { id: '***************', name: 'name one' },
      { id: '**************', name: 'name two' },
      { id: '*************', name: 'name three' },
      { id: '*************', name: 'name four' },
      { id: '*****************', name: 'name five' },
    ]);
  });

  describe('getPlatformMedia', () => {
    const mockUserDetails: UserDetailsDto = {
      userId: 1,
      authorization: 'Bearer test-token',
      organizationId: 'org-1',
    };

    const mockRequestDto: PlatformMediaRequestDto = {
      endDate: '2025-01-01',
      platform: Platform.FACEBOOK,
      startDate: '2020-01-01',
      platformMediaIds: ['pm1', 'pm2'],
      workspaceIds: [100, 200],
      adAccountIds: ['adAcc1'],
    };

    it('calls axios.post with correct payload and returns result', async () => {
      const mockResult = { media: ['media1', 'media2'] };
      (axios.post as jest.Mock).mockResolvedValue({
        data: { result: mockResult },
      });

      const result = await legacyAnalyticsService.getPlatformMedia(
        mockUserDetails,
        mockRequestDto,
      );

      expect(axios.post).toHaveBeenCalledWith(
        'mocked analytics service url/api/v2/platformMedia',
        {
          options: { withMedia: true },
          organizationId: 'org-1',
          platformMediaIds: ['pm1', 'pm2'],
          workspaceIds: [100, 200],
          adAccountIds: ['adAcc1'],
        },
        {
          headers: {
            Authorization: 'Bearer test-token',
            'Content-Type': 'application/json',
          },
        },
      );

      expect(result).toEqual(mockResult);
    });
  });
});
