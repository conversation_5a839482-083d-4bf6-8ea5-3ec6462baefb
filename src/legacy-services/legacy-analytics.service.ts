import { ConfigService } from '@nestjs/config';
import {
  AS_CLEAR_CACHE_URL,
  AS_PLATFORM_ACCOUNT_ANALYTICS_STATS_POST_URL,
  AS_PLATFORM_ACCOUNT_GET_URL,
  AS_PLATFORM_ACCOUNT_GROUP_GET_URL,
  AS_PLATFORM_DIMENSION_GROUP_BY_TYPE_URL,
  AS_PLATFORM_DIMENSION_GROUPS_URL,
  AS_PLATFORM_MEDIA_URL,
} from '../constants/external-api-paths.constants';
import { PlatformAccountGroup } from '../custom-audience/ad-account-types';
import axios from 'axios';
import { BadRequestException, Injectable } from '@nestjs/common';
import { FilterItem } from '../analytics/saved-report/dto/get-analytics-filter-values.dto';
import {
  CampaignObjectiveNestingLevel,
  DimensionGroups,
  IMPRESSIONS_METRIC_FOR_PLATFORM,
  Platform,
  PLATFORM_CAMPAIGN_OBJECTIVE_KEY_MAP,
  PLATFORM_PLACEMENT_VALUE_KEYS_FROM_DIMENSION_GROUPS_RESPONSE,
  PLATFORM_TO_ANALYTICS_PLATFORM_VALUE,
  PLATFORMS_SUPPORT_AD_TYPE_DIMENSION_GROUP,
  PLATFORMS_SUPPORT_CAMPAIGN_OBJECTIVE,
  PLATFORMS_SUPPORT_MULTI_ASSET_ADS,
} from '../constants/platform.constants';
import { LegacyAnalyticsServiceStatsRequestDto } from '../dto/legacy-analytics-service-stats-request.dto';
import { UserDetailsDto } from '../analytics/dto/user-details.dto';
import { PlatformMediaRequestDto } from '../analytics/analytics-media/dto/platform-media-request.dto';

@Injectable()
export class LegacyAnalyticsService {
  constructor(private readonly configService: ConfigService) {}

  public async clearLegacyAnalyticsServiceCache(
    organizationId: string,
    tokenWithBearerPrefix: string,
  ) {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    const requestConfig = {
      headers: {
        Authorization: tokenWithBearerPrefix,
        'Content-Type': 'application/json',
      },
    };

    return await axios.post(
      analyticsServiceUrl + AS_CLEAR_CACHE_URL,
      { organizationId },
      requestConfig,
    );
  }

  /**
   * Build the analytics service platform account group get url.
   * @param accountGroupId Platform account group id
   */
  private buildASPlatformAccountGroupGetUrl(accountGroupId: string): string {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    return analyticsServiceUrl
      .concat(AS_PLATFORM_ACCOUNT_GROUP_GET_URL)
      .replace(':groupId', accountGroupId);
  }

  /**
   * Build the analytics service platform account get url.
   * @param platform platform
   * @param platformAccountId platform account id
   */
  private buildASPlatformAccountGetUrl(
    platform: string,
    platformAccountId: string,
  ): string {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    return analyticsServiceUrl
      .concat(AS_PLATFORM_ACCOUNT_GET_URL)
      .replace(':platform', platform)
      .replace(':adAccountId', platformAccountId);
  }

  /**
   * Build the analytics service url for fetching platform account analytics stats.
   * @param platform platform
   */
  private buildASPlatformAccountAnalyticsStatsPostUrl(
    platform: Platform,
  ): string {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    return analyticsServiceUrl
      .concat(AS_PLATFORM_ACCOUNT_ANALYTICS_STATS_POST_URL)
      .replace(':platform', PLATFORM_TO_ANALYTICS_PLATFORM_VALUE[platform]);
  }

  /**
   * Build the analytics service url for fetching platform objectives or placements dimension groups
   * @param platform platform
   * @param dimensionGroupType dimension group type
   */
  private buildASPlatformDimensionGroupByTypeGetUrl(
    platform: Platform,
    dimensionGroupType: 'objectives' | 'placements',
  ): string {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    return analyticsServiceUrl
      .concat(AS_PLATFORM_DIMENSION_GROUP_BY_TYPE_URL)
      .replace(':platform', PLATFORM_TO_ANALYTICS_PLATFORM_VALUE[platform])
      .replace(':groupType', dimensionGroupType);
  }

  private buildASPlatformDimensionGroupGetUrl(platform: Platform): string {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    return analyticsServiceUrl
      .concat(AS_PLATFORM_DIMENSION_GROUPS_URL)
      .replace(':platform', PLATFORM_TO_ANALYTICS_PLATFORM_VALUE[platform]);
  }

  /**
   * this handles facebook needing requests for single asset and multi asset ads
   * other platforms only need one request
   * @param legacyAnalyticsServiceStatsRequestDto
   * @param dimensions
   * @private
   */
  private getAnalyticsRequestsForAllAdLevels(
    legacyAnalyticsServiceStatsRequestDto: LegacyAnalyticsServiceStatsRequestDto,
  ): Record<string, any>[] {
    const {
      adAccountIds,
      dateRange,
      organizationId,
      workspaceIds,
      dimension,
      platform,
    } = legacyAnalyticsServiceStatsRequestDto;
    const baseRequestBody = {
      platform: PLATFORM_TO_ANALYTICS_PLATFORM_VALUE[platform],
      organizationId,
      workspaceIds,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      dimensions: [dimension],
      metrics: [IMPRESSIONS_METRIC_FOR_PLATFORM[platform]],
      filter: {
        account: adAccountIds,
        'analytics_media[file_type]': ['VIDEO', 'IMAGE'],
      },
      cache: true,
    };

    if (PLATFORMS_SUPPORT_MULTI_ASSET_ADS.includes(platform)) {
      const singleAssetAdsRequestBody = {
        ...baseRequestBody,
        filter: {
          ...baseRequestBody.filter,
          'ad[multi_asset]': ['$not(true)'],
        },
      };
      const multiAssetAdsRequestBody = {
        ...baseRequestBody,
        filter: {
          ...baseRequestBody.filter,
          assettype: ['video', 'image'],
        },
      };

      return [singleAssetAdsRequestBody, multiAssetAdsRequestBody];
    }

    return [baseRequestBody];
  }

  async getPlatformAccountGroup(
    tokenWithBearerPrefix: string,
    accountGroupId: string,
  ): Promise<PlatformAccountGroup> {
    const headers = {
      Authorization: tokenWithBearerPrefix,
      'Content-Type': 'application/json',
    };
    const platformAdAccountGroupGetUrl =
      this.buildASPlatformAccountGroupGetUrl(accountGroupId);
    try {
      const response = await axios.get(platformAdAccountGroupGetUrl, {
        headers,
      });
      return response.data.result as PlatformAccountGroup;
    } catch (e) {
      throw new Error(
        `Error when getting platform account group ${accountGroupId}. ` + e,
      );
    }
  }

  async getPlatformAccount(
    tokenWithBearerPrefix: string,
    platform: Platform,
    accountId: string,
  ) {
    const headers = {
      Authorization: tokenWithBearerPrefix,
      'Content-Type': 'application/json',
    };
    const platformAdAccountGetUrl = this.buildASPlatformAccountGetUrl(
      platform,
      accountId,
    );
    try {
      const response = await axios.get(platformAdAccountGetUrl, { headers });
      return response.data.result;
    } catch (e) {
      throw new Error(
        `Error when getting platform ${platform} account ${accountId}. ` + e,
      );
    }
  }

  /**
   * Get all available dimension groups for a platform. e.g facebook returns campaign[objectives] and placements groups
   */
  async getPlatformDimensionGroups(
    platform: Platform,
    tokenWithBearerPrefix: string,
  ): Promise<Record<string, any>> {
    const url = this.buildASPlatformDimensionGroupGetUrl(platform);
    const config = {
      headers: {
        Authorization: tokenWithBearerPrefix,
      },
    };

    const response = await axios.get(url, config);
    return response.data.result;
  }

  private formatDimensionsToFilterItems(dimensionGroups: any[]): FilterItem[] {
    return dimensionGroups.map((highLevelGroup: any) => ({
      id: highLevelGroup.id,
      name: highLevelGroup.name,
      values: highLevelGroup.values.map((nestedValue: any) => ({
        id: nestedValue.id,
        name: nestedValue.value,
      })),
    }));
  }

  async getPlatformObjectives(
    platform: Platform,
    tokenWithBearerPrefix: string,
  ): Promise<FilterItem[]> {
    if (!PLATFORMS_SUPPORT_CAMPAIGN_OBJECTIVE.includes(platform)) {
      throw new BadRequestException(
        `Platform ${platform} does not support campaign objectives filter type`,
      );
    }

    try {
      const response = await this.getPlatformDimensionGroups(
        platform,
        tokenWithBearerPrefix,
      );
      const platformObjectiveDimGroupName =
        PLATFORM_CAMPAIGN_OBJECTIVE_KEY_MAP[platform];
      return this.formatDimensionsToFilterItems(
        response[platformObjectiveDimGroupName][
          CampaignObjectiveNestingLevel.HIGH_LEVEL_OBJECTIVES
        ],
      );
    } catch (error) {
      throw new Error(
        `Error when fetching ${platform} campaign objectives` + error,
      );
    }
  }

  async getPlatformPlacements(
    platform: Platform,
    tokenWithBearerPrefix: string,
    valueKeysFromDimesionGroupResponse?: string[],
  ): Promise<FilterItem[]> {
    const [platformPlacementDimGroupName, platformPlacementGroupingKey] =
      valueKeysFromDimesionGroupResponse ||
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      PLATFORM_PLACEMENT_VALUE_KEYS_FROM_DIMENSION_GROUPS_RESPONSE[platform];
    if (!platformPlacementDimGroupName) {
      throw new BadRequestException(
        `Platform ${platform} does not support ad placement filter type`,
      );
    }

    try {
      const response = await this.getPlatformDimensionGroups(
        platform,
        tokenWithBearerPrefix,
      );
      return this.formatDimensionsToFilterItems(
        response[platformPlacementDimGroupName][platformPlacementGroupingKey],
      );
    } catch (error) {
      throw new Error(`Error when fetching ${platform} placements` + error);
    }
  }

  async getPlatformAdTypes(
    platform: Platform,
    tokenWithBearerPrefix: string,
  ): Promise<
    Record<
      string,
      { id: number; name: string; values: { id: number; value: string }[] }[]
    >
  > {
    if (!PLATFORMS_SUPPORT_AD_TYPE_DIMENSION_GROUP.includes(platform)) {
      throw new BadRequestException(
        `Platform ${platform} does not support ad type dimension group`,
      );
    }

    try {
      const response = await this.getPlatformDimensionGroups(
        platform,
        tokenWithBearerPrefix,
      );

      const adTypeKey =
        platform === Platform.DV360
          ? DimensionGroups.AD_GROUP_TYPE
          : DimensionGroups.AD_TYPE;

      return response[adTypeKey];
    } catch (error) {
      throw new Error(`Error when fetching ${platform} ad types` + error);
    }
  }

  /**
   * Get the dimension values for the given dimension.
   * Example: to get ads, pass the relevant ad dimension for the platform in LegacyAnalyticsServiceStatsRequestDto. See
   * @param legacyAnalyticsServiceStatsRequestDto
   */
  async getAnalyticsDimensionValues(
    legacyAnalyticsServiceStatsRequestDto: LegacyAnalyticsServiceStatsRequestDto,
  ): Promise<FilterItem[]> {
    const { platform } = legacyAnalyticsServiceStatsRequestDto;
    try {
      const requestsBody = this.getAnalyticsRequestsForAllAdLevels(
        legacyAnalyticsServiceStatsRequestDto,
      );
      const requestConfig = {
        headers: {
          Authorization:
            legacyAnalyticsServiceStatsRequestDto.tokenWithBearerPrefix,
          'Content-Type': 'application/json',
        },
      };
      const requests = requestsBody.map((requestBody) =>
        axios.post(
          this.buildASPlatformAccountAnalyticsStatsPostUrl(platform),
          requestBody,
          requestConfig,
        ),
      );

      const responses = await Promise.all(requests);
      return this.getUniqueValuesFromResponsesByDimensionId(
        responses,
        legacyAnalyticsServiceStatsRequestDto.dimension,
      );
    } catch (error) {
      throw new Error(
        `Error when fetching ${legacyAnalyticsServiceStatsRequestDto.dimension} for platform ${platform} account ${legacyAnalyticsServiceStatsRequestDto.adAccountIds} ` +
          error,
      );
    }
  }

  async getPlatformMedia(
    userDetails: UserDetailsDto,
    platformMediaRequestDto: PlatformMediaRequestDto,
  ) {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }

    const requestConfig = {
      headers: {
        Authorization: userDetails.authorization,
        'Content-Type': 'application/json',
      },
    };

    const requestPayload = {
      options: {
        withMedia: true,
      },
      organizationId: userDetails.organizationId,
      platformMediaIds: platformMediaRequestDto.platformMediaIds,
      workspaceIds: platformMediaRequestDto.workspaceIds,
      adAccountIds: platformMediaRequestDto.adAccountIds,
    };

    const response = await axios.post(
      `${analyticsServiceUrl}${AS_PLATFORM_MEDIA_URL}`,
      requestPayload,
      requestConfig,
    );

    return response.data.result;
  }

  private getUniqueValuesFromResponsesByDimensionId(
    responses: any[],
    dimension: string,
  ): FilterItem[] {
    const allStats = responses.flatMap(
      (response) => response.data.result.stats,
    );
    if (allStats.length === 0) {
      return [];
    }

    // e.g snap has no ad names, so only returns id e.g. allStats = [{ad: "ieowo3o3o3oos"}]
    const dimensionHasOnlyIdString =
      !Boolean(allStats[0]?.[dimension]?.id) &&
      Boolean(allStats[0]?.[dimension]);

    if (dimensionHasOnlyIdString) {
      return this.getUniqueValuesFromResponsesForStringOnlyDimension(
        allStats,
        dimension,
      );
    }

    // for when dimension has id and name e.g allStats = [{ad: {id: "ieowo3o3o3oos", name: "ad name"}}]
    return this.getUniqueValuesFromResponsesForIdAndNameDimension(
      allStats,
      dimension,
    );
  }

  private getUniqueValuesFromResponsesForStringOnlyDimension(
    allStats: Record<string, string>[],
    dimension: string,
  ): FilterItem[] {
    const uniqueValuesById = allStats.reduce(
      (acc: Record<string, FilterItem>, singleStat) => {
        const dimensionValue = singleStat[dimension];
        if (!dimensionValue) {
          return acc;
        }
        acc[dimensionValue] = {
          id: dimensionValue,
          name: dimensionValue,
        };
        return acc;
      },
      {},
    );

    const uniqueValues = Object.values(uniqueValuesById);
    return uniqueValues as FilterItem[];
  }

  private getUniqueValuesFromResponsesForIdAndNameDimension(
    allStats: Record<string, { id: string | number; name: string }>[],
    dimension: string,
  ): FilterItem[] {
    const uniqueValuesById = allStats.reduce((acc, singleStat) => {
      const dimensionValue = singleStat[dimension];
      if (!dimensionValue) {
        return acc;
      }
      acc[dimensionValue.id] = {
        id: dimensionValue.id,
        name: dimensionValue.name,
      };
      return acc;
    }, {});

    const uniqueValues = Object.values(uniqueValuesById);
    return uniqueValues as FilterItem[];
  }
}
