import { validate } from 'class-validator';
import { CreateWorkspaceMarketMapDto } from './create-workspace-country-map.dto';

describe('CreateWorkspaceMarketMapDto', () => {
  it('should pass validation for a valid CreateWorkspaceMarketMapDto', async () => {
    const validDto = new CreateWorkspaceMarketMapDto();
    validDto.isoCode = 'USA';

    const errors = await validate(validDto);
    expect(errors.length).toBe(0);
  });

  it('should fail validation for an invalid isoCode CreateWorkspaceMarketMapDto', async () => {
    const invalidDto = new CreateWorkspaceMarketMapDto();
    invalidDto.isoCode = 'XYZ'; // invalid ISO 3166-1 alpha-3 code;

    const errors = await validate(invalidDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty(
      'isISO31661Alpha3',
      'ISO code must be a valid ISO 3166-1 alpha-3 code.',
    );
  });

  it('should fail validation for an invalid isoCode CreateWorkspaceMarketMapDto when not defined', async () => {
    const invalidDto = new CreateWorkspaceMarketMapDto();

    const errors = await validate(invalidDto);
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty(
      'isDefined',
      'ISO code must be defined.',
    );
  });
});
