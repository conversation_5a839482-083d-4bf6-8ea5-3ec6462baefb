import { Test, TestingModule } from '@nestjs/testing';
import { MarketController } from './market.controller';
import { MarketService } from '../services/market.service';
import { CreateWorkspaceMarketMapDto } from '../dto/create-workspace-country-map.dto';
import { DeleteMarketAssignResponseDto } from '../dto/delete-market-assign-response.dto';
import { ReadMarketDto } from '../dto/read-market.dto';
import {
  PaginationOptions,
  paginatedResponse,
} from '@vidmob/vidmob-nestjs-common';

describe('MarketController', () => {
  let controller: MarketController;
  let marketService: MarketService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MarketController],
      providers: [
        {
          provide: MarketService,
          useValue: {
            createMarketToWorkspace: jest.fn().mockResolvedValue({
              status: 'OK',
              result: {
                workspaceId: 17325,
                isoCode: 'bel',
              },
            }),
            removeMarketFromWorkspace: jest.fn().mockResolvedValue({
              status: 'OK',
              result: {
                workspaceId: 17325,
                isoCode: 'bel',
              },
            }),
            getWorkspaceMarkets: jest.fn().mockResolvedValue({
              status: 'OK',
              result: [
                {
                  isoCode: 'bra',
                  name: 'Brazil',
                },
              ],
              pagination: {
                offset: 0,
                perPage: 10,
                nextOffset: 0,
                totalSize: 1,
              },
            }),
          },
        },
      ],
    }).compile();

    controller = module.get<MarketController>(MarketController);
    marketService = module.get<MarketService>(MarketService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call service with valid CreateWorkspaceMarketMapDto for createMarketWorkspace', async () => {
    const workspaceId = 1;
    const validDto: CreateWorkspaceMarketMapDto = {
      isoCode: 'XYZ',
    };

    const response = await controller.createMarketWorkspace(
      workspaceId,
      validDto,
    );

    expect(marketService.createMarketToWorkspace).toHaveBeenCalledWith(
      workspaceId,
      validDto.isoCode,
    );
    expect(response).toEqual({
      status: 'OK',
      result: {
        workspaceId: 17325,
        isoCode: 'bel',
      },
    });
  });

  it('should call service to remove market from workspace', async () => {
    const validDto: DeleteMarketAssignResponseDto = {
      isoCode: 'can',
      workspaceId: 1,
    };

    const response = await controller.removeMarketFromWorkspace(
      validDto.workspaceId,
      validDto,
    );

    expect(marketService.removeMarketFromWorkspace).toHaveBeenCalledWith(
      validDto.workspaceId,
      validDto.isoCode,
    );
    expect(response).toEqual({
      status: 'OK',
      result: {
        workspaceId: 17325,
        isoCode: 'bel',
      },
    });
  });

  it('should return list of markets associated with the workspace', async () => {
    const paginationOptions: PaginationOptions = {};
    const workspaceId = 1;
    const market1: ReadMarketDto = {
      isoCode: 'bra',
      name: 'Brazil',
    };

    const response = await controller.listMarkets(
      workspaceId,
      paginationOptions,
    );

    expect(marketService.getWorkspaceMarkets).toHaveBeenCalledWith(
      workspaceId,
      paginationOptions,
    );
    expect(response).toEqual({
      status: 'OK',
      result: [market1],
      pagination: {
        offset: 0,
        perPage: 10,
        nextOffset: 0,
        totalSize: 1,
      },
    });
  });
});
