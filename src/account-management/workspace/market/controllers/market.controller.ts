import {
  Controller,
  Post,
  Body,
  ValidationPipe,
  Get,
  Param,
  ParseIntPipe,
  Delete,
} from '@nestjs/common';
import { MarketService } from '../services/market.service';
import { CreateWorkspaceMarketMapDto } from '../dto/create-workspace-country-map.dto';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import {
  GetPagination,
  PaginationOptions,
  VmApiCreatedResponse,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReadMarketDto } from '../dto/read-market.dto';
import { CreateWorkspaceMarketDto } from '../dto/create-workspace-market.dto';
import { DeleteMarketAssignResponseDto } from '../dto/delete-market-assign-response.dto';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import {
  readWorkspaceDetails,
  updateWorkspaceDetails,
} from '../../../../account-management/account-management.permissions';

@ApiTags('Market')
@Controller('account-management/workspace/:workspaceId/market')
export class MarketController {
  constructor(private marketService: MarketService) {}

  /**
   * Create a new assignment to a workspace.
   * @param workspaceId - The id of the workspace (Partner).
   * @param isoCode - The id of Market (Country).
   */
  @VmApiCreatedResponse({
    type: CreateWorkspaceMarketDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @ApiParam({ name: 'isoCode', description: 'The id of Market' })
  @Permissions(updateWorkspaceDetails)
  @Post()
  async createMarketWorkspace(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe()) param: CreateWorkspaceMarketMapDto,
  ) {
    return this.marketService.createMarketToWorkspace(
      workspaceId,
      param.isoCode,
    );
  }

  /**
   * Remove the assignment of a market to a workspace.
   * @param workspaceId - The id of the workspace.
   * @param isoCode - The id of the market.
   */
  @VmApiOkResponse({
    type: DeleteMarketAssignResponseDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @ApiParam({ name: 'isoCode', description: 'The id of the market' })
  @Permissions(updateWorkspaceDetails)
  @Delete()
  async removeMarketFromWorkspace(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe()) param: DeleteMarketAssignResponseDto,
  ) {
    return await this.marketService.removeMarketFromWorkspace(
      workspaceId,
      param.isoCode,
    );
  }

  /**
   * List All Markets assigned to a workspace.
   * @param workspaceId - The id of the workspace (Partner).
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadMarketDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readWorkspaceDetails)
  @Get()
  async listMarkets(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.marketService.getWorkspaceMarkets(
      workspaceId,
      paginationOptions,
    );
  }
}
