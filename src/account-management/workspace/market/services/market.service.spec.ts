import { Test, TestingModule } from '@nestjs/testing';
import { MarketService } from './market.service';
import { MarketService as MarketServiceSDK } from '@vidmob/vidmob-organization-service-sdk';

interface MockMarketServiceSDK {
  createMarketWorkspaceAsPromise: jest.Mock;
  removeMarketFromWorkspaceAsPromise: jest.Mock;
  listMarketsAsPromise: jest.Mock;
}

describe('MarketService', () => {
  let service: MarketService;
  let mockMarketServiceSDK: MockMarketServiceSDK;

  beforeEach(async () => {
    mockMarketServiceSDK = {
      createMarketWorkspaceAsPromise: jest.fn(),
      removeMarketFromWorkspaceAsPromise: jest.fn(),
      listMarketsAsPromise: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MarketService,
        {
          provide: MarketServiceSDK,
          useValue: mockMarketServiceSDK,
        },
      ],
    }).compile();

    service = module.get<MarketService>(MarketService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should call marketServiceSDK.createMarketWorkspaceAsPromise', async () => {
    const workspaceId = 1;
    const isoCode = 'XYZ';
    const expectedResult = {
      status: 'OK',
      result: {
        workspaceId: 17325,
        isoCode: 'bel',
      },
    };

    mockMarketServiceSDK.createMarketWorkspaceAsPromise.mockResolvedValue({
      ...expectedResult,
    });

    const result = await service.createMarketToWorkspace(workspaceId, isoCode);

    expect(result).toEqual(expectedResult.result);
    expect(
      mockMarketServiceSDK.createMarketWorkspaceAsPromise,
    ).toHaveBeenCalledWith(workspaceId, isoCode);
  });
});
