import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  MarketService as MarketServiceSDK,
  ReadMarketDto,
} from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class MarketService {
  constructor(private readonly marketServiceSDK: MarketServiceSDK) {}

  async createMarketToWorkspace(workspaceId: number, isoCode: string) {
    const { result } =
      await this.marketServiceSDK.createMarketWorkspaceAsPromise(
        workspaceId,
        isoCode,
      );

    return result;
  }

  async removeMarketFromWorkspace(workspaceId: number, isoCode: string) {
    const { result } =
      await this.marketServiceSDK.removeMarketFromWorkspaceAsPromise(
        workspaceId,
        isoCode,
      );

    return result;
  }

  async getWorkspaceMarkets(
    workspaceId: number,
    paginationOptions: PaginationOptions,
  ) {
    const response = await this.marketServiceSDK.listMarketsAsPromise(
      workspaceId,
      paginationOptions.offset,
      paginationOptions.perPage,
    );

    return new PaginatedResultArray<ReadMarketDto>(
      response.result,
      response.pagination?.totalSize,
    );
  }
}
