import { Injectable, NotFoundException } from '@nestjs/common';
import {
  ReadWorkspacesAdAccountDto,
  WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK,
  OrganizationService as OrganizationServiceSDK,
  CreateAdAccountWorkspaceMapDto,
} from '@vidmob/vidmob-organization-service-sdk';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Workspace } from '../../../../entities/workspace.entity';
import { AccountSearchParamsDto } from '../../../organization/ad-account/entities/ad-account-search-params.dto';

@Injectable()
export class WorkspaceAdAccountService {
  constructor(
    @InjectRepository(Workspace)
    private workspaceRepository: Repository<Workspace>,
    // @InjectMapper() private readonly classMapper: Mapper,
    private readonly organizationService: OrganizationServiceSDK,
    private readonly workspaceAdAccountServiceSDK: WorkspaceAdAccountServiceSDK,
  ) {}

  async findAllAdAccountsForAWorkspace(
    workspaceId: number,
    paginationOptions: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ) {
    const connectedAdAccountsForOrganization200Response =
      await this.workspaceAdAccountServiceSDK.listAllConnectedAdAccountsFromWorkspaceAsPromise(
        workspaceId,
        searchParams.search,
        searchParams.sortBy,
        searchParams.sortOrder,
        undefined,
        undefined,
        paginationOptions.offset,
        paginationOptions.perPage,
      );
    return new PaginatedResultArray<ReadWorkspacesAdAccountDto>(
      connectedAdAccountsForOrganization200Response.result,
      connectedAdAccountsForOrganization200Response.pagination?.totalSize,
    );
  }

  async configureWorkspaceAdAccounts(
    workspaceId: number,
    mapWorkspaceAdAccountDto: CreateAdAccountWorkspaceMapDto,
  ) {
    const workspace = await this.workspaceRepository.findOneBy({
      id: workspaceId,
    });
    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    return await this.organizationService.configureOrganizationWorkspaceAdAccountConnectionsAsPromise(
      workspaceId,
      mapWorkspaceAdAccountDto,
    );
  }
}
