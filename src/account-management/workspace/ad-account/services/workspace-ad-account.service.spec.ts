import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceAdAccountService } from './workspace-ad-account.service';
import {
  OrganizationService as OrganizationServiceSDK,
  WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK,
  CreateAdAccountWorkspaceMapDto,
} from '@vidmob/vidmob-organization-service-sdk';
import { AuthService } from '../../../../auth/services/auth.service';
import { ForbiddenException } from '@nestjs/common';
import { Workspace } from '../../../../entities/workspace.entity';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Mapper, createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { getMapperToken } from '@automapper/nestjs';

class MockOrganizationService {
  async configureOrganizationWorkspaceAdAccountConnectionsAsPromise() {
    return {
      status: 'OK',
      result: 'Link created successfully' as unknown as object,
    };
  }

  async deletePlatformAdAccountFromWorkspaceAsPromise() {
    return {
      status: 'OK',
      result: '',
    };
  }
}

describe('WorkspaceAdAccountService', () => {
  let authService: AuthService;
  let organizationService: OrganizationServiceSDK;
  let workspaceAdAccountService: WorkspaceAdAccountService;
  let workspaceRepo: Repository<Workspace>;
  let classMapper: Mapper;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceAdAccountService,
        {
          provide: getRepositoryToken(Workspace),
          useClass: Repository,
        },
        {
          provide: OrganizationServiceSDK,
          useClass: MockOrganizationService,
        },
        {
          provide: WorkspaceAdAccountServiceSDK,
          useFactory: () => ({
            listAllConnectedAdAccountsFromWorkspaceAsPromise: jest.fn(),
          }),
        },
        {
          provide: AuthService,
          useValue: {
            isResourcePermissionValid: jest.fn(),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    classMapper = moduleRef.get<Mapper>(getMapperToken());
    workspaceRepo = moduleRef.get<Repository<Workspace>>(
      getRepositoryToken(Workspace),
    );
    workspaceAdAccountService = moduleRef.get<WorkspaceAdAccountService>(
      WorkspaceAdAccountService,
    );
    organizationService = moduleRef.get<OrganizationServiceSDK>(
      OrganizationServiceSDK,
    );
    authService = moduleRef.get<AuthService>(AuthService);
  });

  it('should map organization ad accounts and workspace together', async () => {
    const workspaceId = 1;
    const workspace = new Workspace();
    workspace.id = 1;
    workspace.name = 'workspace1';
    workspace.isPrimary = true;
    workspace.organizationId = 'uuid';

    const mockCreateDto: CreateAdAccountWorkspaceMapDto = {
      platformAccountList: [
        {
          platformAccountId: '*********',
          platform: 'FACEBOOK',
        },
      ],
      status: 'CONNECTED',
    };

    jest
      .spyOn(authService, 'isResourcePermissionValid')
      .mockResolvedValue(true);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);

    const result = await workspaceAdAccountService.configureWorkspaceAdAccounts(
      workspaceId,
      mockCreateDto,
    );

    expect(result).toBeDefined();
    expect(result).toBeInstanceOf(Object);
    expect(result).toEqual({
      status: 'OK',
      result: 'Link created successfully' as unknown as object,
    });
  });

  it('should not map ad accounts to workspaces if workspace is not in organization', async () => {
    const workspaceId = 1;
    const workspace = new Workspace();
    workspace.id = 1;
    workspace.name = 'workspace1';
    workspace.isPrimary = true;
    workspace.organizationId = 'uuid';

    const mockCreateDto: CreateAdAccountWorkspaceMapDto = {
      platformAccountList: [
        {
          platformAccountId: '*********',
          platform: 'FACEBOOK',
        },
      ],
      status: 'CONNECTED',
    };

    jest
      .spyOn(authService, 'isResourcePermissionValid')
      .mockResolvedValue(true);

    jest.spyOn(workspaceRepo, 'findOneBy').mockResolvedValueOnce(workspace);

    try {
      await workspaceAdAccountService.configureWorkspaceAdAccounts(
        workspaceId,
        mockCreateDto,
      );
    } catch (e) {
      expect(e).toBeInstanceOf(ForbiddenException);
    }
  });
});
