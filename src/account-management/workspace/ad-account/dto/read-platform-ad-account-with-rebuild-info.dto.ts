import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsDate, IsInt, IsNumber, IsString } from 'class-validator';

/**
 * DTO for reading a Platform Ad Account with rebuild info
 * The Rebuild information comes from the rebuild request table.
 */
export class ReadPlatformAdAccountWithRebuildInfoDto {
  /**
   * Platform Ad Account Id
   * @example 1234
   */
  @AutoMap()
  @IsInt()
  id: number;

  /**
   * Platform for the Ad Account
   * @example 'tiktok'
   */
  @AutoMap()
  @IsString()
  platform: string;

  /**
   * Platform Ad Account Name
   * @example 'VidMob'
   */
  @AutoMap()
  @IsString()
  platformAccountName: string;

  /**
   * Platform Ad Account Id
   * @example 't2_t5vae3ih'
   */
  @AutoMap()
  @IsString()
  platformAccountId: string;

  /**
   * Date when the Platform Ad Account was created
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  dateCreated: Date;

  /**
   * Date when the Platform Ad Account was processed
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsNumber()
  processingCompleted: number;

  /**
   * Tinyint (1 for true, 0 for false) when the Platform Ad Account was processed
   * @example 1
   */
  @AutoMap()
  @IsDate()
  processingCompletedDate: Date;

  /**
   * Date when the Platform Ad Account was last updated
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  lastUpdated: Date;

  /**
   * Tinyint (1 for true, 0 for false) when the Platform Ad Account was last updated
   * @example 1
   */
  @AutoMap()
  @IsInt()
  canAccess: number;

  /**
   * Date when the Platform Ad Account was last successfully processed
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  lastSuccessfulProcessingDate: Date;

  /**
   * Tinyint (1 for true, 0 for false) when the Platform Ad Account was last successfully processed
   * @example 1
   */
  @AutoMap()
  @IsBoolean()
  connected: boolean;
}
