import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsDate, IsString } from 'class-validator';

export class ReadWorkspaceAdAccountDto {
  /**
   * Platform for the Ad Account
   * @example 'tiktok'
   */
  @AutoMap()
  @IsString()
  platform: string;

  /**
   * Platform Ad Account Name
   * @example 'VidMob'
   */
  @AutoMap()
  @IsString()
  platformAccountName: string;

  /**
   * Platform Ad Account Id
   * @example 't2_t5vae3ih'
   */
  @AutoMap()
  @IsString()
  platformAccountId: string;

  /**
   * Date when the Platform Ad Account was created
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  dateCreated: Date;

  /**
   * Flag to indicate if the Platform Ad Account was processed
   * @example true
   */
  @AutoMap()
  @IsBoolean()
  processingCompleted: boolean;

  /**
   * Date when the Platform Ad Account was processed
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  processingCompletedDate: Date;

  /**
   * Date when the Platform Ad Account was last updated
   * @example '2021-01-01T00:00:00.000Z'
   */
  @AutoMap()
  @IsDate()
  lastSuccessfulProcessingDate: Date;

  /**
   * Flag to indicate if the Platform Ad Account is connected
   * @example true
   */
  @AutoMap()
  @IsBoolean()
  connected: boolean;
}
