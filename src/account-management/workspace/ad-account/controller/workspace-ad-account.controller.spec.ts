import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceAdAccountController } from './workspace-ad-account.controller';
import { WorkspaceAdAccountService } from '../services/workspace-ad-account.service';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { CreateAdAccountWorkspaceMapDto } from '@vidmob/vidmob-organization-service-sdk';

describe('OrganizationsController', () => {
  let controller: WorkspaceAdAccountController;
  let service: WorkspaceAdAccountService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceAdAccountController],
      providers: [
        {
          provide: WorkspaceAdAccountService,
          useValue: {
            configureWorkspaceAdAccounts: jest.fn().mockResolvedValue(undefined),
          },
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    controller = module.get<WorkspaceAdAccountController>(WorkspaceAdAccountController);
    service = module.get<WorkspaceAdAccountService>(WorkspaceAdAccountService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('configureOrganizationWorkspaceAdAccounts', () => {
    const mockCreateDto: CreateAdAccountWorkspaceMapDto = {
      platformAccountList: [{
        platformAccountId: '*********',
        platform: 'FACEBOOK',
      }],
      status: 'CONNECTED',
    };

    it('should call workspaceService.configureWorkspaceAdAccounts', async () => {
      await controller.configureWorkspaceAdAccounts(54321, mockCreateDto);
      expect(service.configureWorkspaceAdAccounts).toHaveBeenCalledWith(
        54321,
        {
          platformAccountList: [{
            platformAccountId: '*********',
            platform: 'FACEBOOK',
          }],
          status: 'CONNECTED',
        }
      );
    });
  });
});