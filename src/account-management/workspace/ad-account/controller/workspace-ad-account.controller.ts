import {
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Body,
  Query,
} from '@nestjs/common';
import { ApiParam, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { WorkspaceAdAccountService } from '../services/workspace-ad-account.service';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import {
  updateWorkspaceAdAccountMapping,
  readOrganizationWorkspaceAdAccountMapping,
} from '../../../account-management.permissions';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ReadPlatformAdAccountWithRebuildInfoDto } from '../dto/read-platform-ad-account-with-rebuild-info.dto';
import {
  ConfigureOrganizationWorkspaceAdAccountConnections201Response,
  CreateAdAccountWorkspaceMapDto,
} from '@vidmob/vidmob-organization-service-sdk';
import { AccountSearchParamsDto } from '../../../organization/ad-account/entities/ad-account-search-params.dto';

@ApiTags('Ad Account')
@ApiSecurity('Bearer Token')
@Controller('account-management/workspace/:workspaceId/ad-account')
export class WorkspaceAdAccountController {
  constructor(
    private readonly workspaceAdAccountService: WorkspaceAdAccountService,
  ) {}

  @VmApiOkPaginatedArrayResponse({
    type: ReadPlatformAdAccountWithRebuildInfoDto,
  })
  @ApiParam({
    name: 'workspaceId',
    description:
      'The workspace ID represents the unique identifier of the workspace to which all ad accounts belong.',
  })
  @ApiQuery({
    name: 'search',
    description:
      'The search param to filter ad accounts by name, id or channel.',
    example: '?search=name',
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'The sort by string to determine the field to sort results.',
    example: '?sortBy=platform, dateCreated, platformAccountName (default)',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The sort by order.',
    example: '?sortOrder=ASC - ASC(default) or DESC.',
  })
  @Permissions(readOrganizationWorkspaceAdAccountMapping)
  @Get()
  async findAllAdAccountsForAWorkspace(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: AccountSearchParamsDto,
  ) {
    return this.workspaceAdAccountService.findAllAdAccountsForAWorkspace(
      workspaceId,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * Maps Platform Ad Accounts to Workspace.
   * @param req: The request object.
   * @param workspaceId: The workspace ID represents the unique identifier of the workspaces that belongs to an organization.
   * @param MapWorkspaceAdAccountDto: The DTO that contains the workspace id to map to the ad accounts.
   */
  @VmApiOkResponse({
    type: String,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'System assigned workspace id',
  })
  @Permissions(updateWorkspaceAdAccountMapping)
  @Post()
  async configureWorkspaceAdAccounts(
    @Param('workspaceId') workspaceId: number,
    @Body()
    mapWorkspaceAdAccountDto: CreateAdAccountWorkspaceMapDto,
  ): Promise<ConfigureOrganizationWorkspaceAdAccountConnections201Response> {
    return this.workspaceAdAccountService.configureWorkspaceAdAccounts(
      workspaceId,
      mapWorkspaceAdAccountDto,
    );
  }
}
