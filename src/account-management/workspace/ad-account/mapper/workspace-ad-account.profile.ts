import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import {
  createMap,
  forMember,
  mapFrom,
  Mapper,
  MappingProfile,
} from '@automapper/core';
import { ReadWorkspaceAdAccountDto } from '../dto/read-workspace-ad-account.dto';
import { ReadPlatformAdAccountWithRebuildInfoDto } from '../dto/read-platform-ad-account-with-rebuild-info.dto';
import { Injectable } from '@nestjs/common';

@Injectable()
export class WorkspaceAdAccountProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  get profile(): MappingProfile {
    return (mapper: Mapper) => {
      createMap<
        ReadPlatformAdAccountWithRebuildInfoDto,
        ReadWorkspaceAdAccountDto
      >(
        mapper,
        ReadPlatformAdAccountWithRebuildInfoDto,
        ReadWorkspaceAdAccountDto,
        forMember(
          (destination) => destination.processingCompleted,
          mapFrom<
            ReadPlatformAdAccountWithRebuildInfoDto,
            ReadWorkspaceAdAccountDto
          >((source) => source.processingCompleted === 1),
        ),
      );
    };
  }
}
