import { WorkspaceBrandController } from './workspace-brand.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceBrandService } from '../services/workspace-brand.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
// import { LocalRemoveWorkspaceBrandMapDto } from '../dto/local-remove-workspace-brand-map.dto';
import { LocalWorkspaceBrandMapDto } from '../dto/local-workspace-brand-map.dto';

describe('WorkspaceBrandController', () => {
  let brandController: WorkspaceBrandController;
  let brandService: WorkspaceBrandService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceBrandController],
      providers: [
        {
          provide: WorkspaceBrandService,
          useValue: {
            createWorkspaceBrand: jest.fn().mockResolvedValue(undefined),
            removeBrandFromWorkspace: jest.fn().mockResolvedValue(undefined),
            getWorkspaceBrands: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    brandController = module.get<WorkspaceBrandController>(
      WorkspaceBrandController,
    );
    brandService = module.get<WorkspaceBrandService>(WorkspaceBrandService);
  });

  it('should be defined', () => {
    expect(brandController).toBeDefined();
  });

  describe('createBrand', () => {
    it('should call service with valid CreateWorkspaceBrandMapDto', async () => {
      const workspaceId = 123;
      const workspaceBrandMapDto: LocalWorkspaceBrandMapDto = {
        brandId: '68ec7ff3-41ed-4a58-8d49-2b3c8af63c29',
      };

      await brandController.createBrand(workspaceId, workspaceBrandMapDto);

      expect(brandService.createWorkspaceBrand).toHaveBeenCalledWith(
        workspaceId,
        workspaceBrandMapDto,
      );
    });
  });

  describe('removeBrandFromWorkspace', () => {
    it('should call service with valid workspaceId and brandId', async () => {
      const workspaceId = 123;
      const brandId = '68ec7ff3-41ed-4a58-8d49-2b3c8af63c29';

      await brandController.removeBrandFromWorkspace(workspaceId, brandId);

      expect(brandService.removeBrandFromWorkspace).toHaveBeenCalledWith(
        workspaceId,
        brandId,
      );
    });
  });

  describe('getWorkspaceBrands', () => {
    it('should call service with valid ReadWorkspaceBrandMapDto', async () => {
      const workspaceId = 123;
      const paginationOptions: PaginationOptions = {};
      await brandController.getWorkspaceBrands(workspaceId, paginationOptions);

      expect(brandService.getWorkspaceBrands).toHaveBeenCalledWith(
        workspaceId,
        paginationOptions,
      );
    });
  });
});
