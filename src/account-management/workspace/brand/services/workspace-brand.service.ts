import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WorkspaceBrandMap } from '../entities/workspace-brand-map.entity';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { Brand } from '../../../organization/brand/entities/brand.entity';
import { Workspace } from '../../../../entities/workspace.entity';

import {
  WorkspaceBrandService as WorkspaceBrandServiceSDK,
  LocalWorkspaceBrandMapDto,
} from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class WorkspaceBrandService {
  constructor(
    @InjectRepository(WorkspaceBrandMap)
    private readonly WorkspaceBrandMapRepo: Repository<WorkspaceBrandMap>,

    @InjectRepository(Workspace)
    private readonly workspaceRepo: Repository<Workspace>,

    @InjectRepository(Brand)
    private readonly brandRepository: Repository<Brand>,

    private readonly workspaceBrandServiceSDK: WorkspaceBrandServiceSDK,

    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  async createWorkspaceBrand(
    workspaceId: number,
    createWorkspaceBrandMap: LocalWorkspaceBrandMapDto,
  ) {
    const readWorkspaceBrandMapDto =
      await this.workspaceBrandServiceSDK.createBrandAsPromise(
        workspaceId,
        createWorkspaceBrandMap.brandId,
        createWorkspaceBrandMap,
      );

    return readWorkspaceBrandMapDto;
  }

  async removeBrandFromWorkspace(workspaceId: number, brandId: string) {
    const removeWorkspaceBrandMapResultDto =
      await this.workspaceBrandServiceSDK.removeBrandFromWorkspaceAsPromise(
        workspaceId,
        brandId,
      );

    return removeWorkspaceBrandMapResultDto;
  }

  /**
   * find all brands
   *
   * @returns
   */
  async getWorkspaceBrands(
    workspaceId: number,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadBrandDto>> {
    const getBrands200Response =
      await this.workspaceBrandServiceSDK.getWorkspaceBrandsAsPromise(
        workspaceId,
        paginationOptions.offset,
        paginationOptions.perPage,
        paginationOptions.queryId,
      );

    const ReadWorkspaceBrandMapDtos: ReadBrandDto[] = this.classMapper.mapArray(
      getBrands200Response.result,
      Brand,
      ReadBrandDto,
    );
    return new PaginatedResultArray(
      ReadWorkspaceBrandMapDtos,
      getBrands200Response.pagination?.totalSize,
    );
  }
}
