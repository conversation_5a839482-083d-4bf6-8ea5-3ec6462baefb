import { AutoMap } from '@automapper/classes';
import { IsDefined, <PERSON>N<PERSON>ber, IsUUID } from 'class-validator';

export class CreateWorkspaceBrandMapDto {
  @AutoMap()
  @IsDefined({ message: 'Workspace ID must be defined.' })
  @IsNumber({}, { message: 'Invalid workspace ID.' })
  workspaceId: number;

  @AutoMap()
  @IsDefined({ message: 'Brand ID must be defined.' })
  @IsUUID('all', { message: 'Invalid brand ID.' })
  brandId: string;
}
