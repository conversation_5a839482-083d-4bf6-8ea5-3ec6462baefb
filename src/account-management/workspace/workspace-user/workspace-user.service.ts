import { Injectable } from '@nestjs/common';
import { WorkspaceUserService as WorkspaceUserServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
@Injectable()
export class WorkspaceUserService {
  constructor(
    private readonly workspaceUserServiceSDK: WorkspaceUserServiceSDK,
  ) {}

  async findAllWorkspaceUsers(workspaceId: number, filterUserBy?: string) {
    return await this.workspaceUserServiceSDK.findAllAsPromise(
      workspaceId,
      filterUserBy || '',
    );
  }
}
