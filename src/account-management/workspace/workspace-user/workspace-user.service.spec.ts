import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceUserService } from './workspace-user.service';
import { WorkspaceUserService as WorkspaceUserServiceSDK } from '@vidmob/vidmob-organization-service-sdk';

const TEST_WORKSPACE_ID = 1;

const workspaceUsers = [
  {
    id: 2,
    email: '<EMAIL>',
    displayName: 'external user',
    firstName: 'external',
    lastName: 'user',
    photo: null,
    jobTitle: null,
    role: {
      id: 3,
      name: 'Standard',
      type: 'business_entity',
      identifier: 'standard',
      description: '',
    },
  },
];

describe('WorkspaceUserService', () => {
  let service: WorkspaceUserService;
  let workspaceUserServiceSdk: WorkspaceUserServiceSDK;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceUserService,
        {
          provide: WorkspaceUserServiceSDK,
          useValue: {
            findAllAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<WorkspaceUserService>(WorkspaceUserService);
    workspaceUserServiceSdk = module.get<WorkspaceUserServiceSDK>(
      WorkspaceUserServiceSDK,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAllWorkspaceUsers', () => {
    const response = {
      status: 'OK',
      result: workspaceUsers,
    };

    it('should return a list of workspace users', async () => {
      jest
        .spyOn(workspaceUserServiceSdk, 'findAllAsPromise')
        .mockResolvedValue(response);

      await expect(
        service.findAllWorkspaceUsers(TEST_WORKSPACE_ID),
      ).resolves.toEqual(response);
    });
  });
});
