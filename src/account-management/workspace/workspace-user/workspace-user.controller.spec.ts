import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceUserController } from './workspace-user.controller';
import { WorkspaceUserService as WorkspaceUserServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { WorkspaceUserService } from './workspace-user.service';

const TEST_WORKSPACE_ID = 1;

const workspaceUsers = [
  {
    id: 2,
    email: '<EMAIL>',
    displayName: 'external user',
    firstName: 'external',
    lastName: 'user',
    photo: null,
    jobTitle: null,
    role: {
      id: 3,
      name: 'Standard',
      type: 'business_entity',
      identifier: 'standard',
      description: '',
    },
  },
];

describe('WorkspaceUserController', () => {
  let controller: WorkspaceUserController;
  let workspaceUserServiceSdk: WorkspaceUserServiceSDK;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceUserController],
      providers: [
        WorkspaceUserService,
        {
          provide: WorkspaceUserServiceSDK,
          useValue: {
            findAllAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkspaceUserController>(WorkspaceUserController);
    workspaceUserServiceSdk = module.get<WorkspaceUserServiceSDK>(
      WorkspaceUserServiceSDK,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('findAll', () => {
    const response = {
      status: 'OK',
      result: workspaceUsers,
    };

    it('should return a list of workspace users', async () => {
      jest
        .spyOn(workspaceUserServiceSdk, 'findAllAsPromise')
        .mockResolvedValue(response);

      await expect(controller.findAll(TEST_WORKSPACE_ID)).resolves.toEqual(
        response,
      );
    });
  });
});
