import { Request, Response } from 'express';
import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Logger,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
} from '@nestjs/common';
import { ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';
import { Public } from 'src/auth/decorators/public.decorator';
import { SsoService } from './sso.service';
import {
  CreateSessionDto,
  CreateSsoConfigurationDto,
  UpdateSsoConfigurationDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { Permissions } from 'src/auth/decorators/permission.decorator';
import {
  createSsoConfiguration,
  deleteSsoConfiguration,
  readSsoConfiguration,
  updateSsoConfiguration,
} from '../account-management.permissions';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';

@ApiTags('SSO')
@Controller('account-management')
export class SsoController {
  private readonly logger = new Logger(SsoController.name);

  constructor(private readonly ssoService: SsoService) {}

  /**
   * This endpoint will be called by Cognito Hosted Login via callback,
   * and will redirect to a web client via state parameter, passing the cognito
   * authorization code via code query string.
   * @param state
   * @param code
   * @param res
   */
  @ApiQuery({
    name: 'state',
    description:
      'a string which contain either the url to redirect the user or a json base64 encoded organization invite',
    required: true,
  })
  @ApiQuery({
    name: 'code',
    description: 'The cognito authentication code',
    required: true,
  })
  @Public()
  @Get('noauth/sso/login')
  async redirectToState(
    @Query('state') state: string,
    @Query('code') code: string,
    @Res() res: Response,
  ) {
    const stateData = this.ssoService.parseStateParameter(state);

    if (this.ssoService.doesStateContainInviteCode(stateData)) {
      await this.ssoService.acceptInvite(stateData);
    }

    const urlWithAuthCode = `${stateData.url}?code=${code}`;

    res.redirect(HttpStatus.MOVED_PERMANENTLY, urlWithAuthCode);
  }

  /**
   * Returns the SSO options for the organization domain related to user email domain
   */
  @ApiQuery({
    name: 'email',
    type: String,
    description: 'user email',
    required: true,
  })
  @Public()
  @Get('noauth/sso/option')
  async getUserSsoOptions(@Query('email') email: string) {
    return await this.ssoService.getUserSsoConfiguration(email);
  }

  /**
   * Endpoint responsible to exchange cognito auth code for sessionId, accessToken and expiresIn
   * @param createSessionDto
   * @returns
   */
  @Post('noauth/sso/session')
  @Public() // needs to be public because user doesn't have the access token yet
  async createSsoSession(@Body() createSessionDto: CreateSessionDto) {
    this.logger.log(`Create Session request received %o`, createSessionDto);
    return await this.ssoService.createSsoSession(createSessionDto);
  }

  /**
   * It creates a sso configuration for a particular organization_domain
   * In order to create an SSO configuration we need to make sure there is an organization domain set to be used,
   * also an SSO configuration mustn't not exists for that particular organization domain.
   *
   * To create the SSO configuration:
   *  1 - Create the Cognito Identity Provider on Cognito;
   *  2 - Update the Cognito User pool and Client Hosted UX to enable the just created Cognito IdP;
   *  3 - Create a SSO configuration within the database.
   *
   * If anything goes wrong on the create sso configuration, rollbacks the changes made on Cognito.
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @Post('sso/organization/:organizationId/configuration')
  @Permissions(createSsoConfiguration)
  async createSsoConfiguration(
    @Req() req: Request,
    @Param('organizationId') organizationId: string,
    @Body()
    createSsoConfigurationDto: CreateSsoConfigurationDto,
  ) {
    const { authorization } = req.headers;
    return await this.ssoService.createSsoConfiguration(
      authorization as string,
      organizationId,
      createSsoConfigurationDto,
    );
  }

  /**
   * Return a paginated response containing all sso configurations an organization has
   * @param organizationId
   * @returns
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @Get('sso/organization/:organizationId/configuration')
  @Permissions(readSsoConfiguration)
  async listSsoConfiguration(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return await this.ssoService.listSsoConfiguration(
      organizationId,
      paginationOptions,
    );
  }

  /*
   * Returns a SSO Configuration
   * @param organizationId
   * @param configurationId
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({
    name: 'configurationId',
    description: 'The id of the ssoConfiguration',
  })
  @Get('sso/organization/:organizationId/configuration/:configurationId')
  @Permissions(readSsoConfiguration)
  async getSsoConfiguration(
    @Param('organizationId') organizationId: string,
    @Param('configurationId') configurationId: string,
  ) {
    return await this.ssoService.getSsoConfiguration(
      organizationId,
      configurationId,
    );
  }

  /*
   * Endpoint responsible to update a SSO configuration.
   * Basically it only updates database fields, it doesn't update anything on Cognito side.
   * If a default workspace is passed during the update, we validate if the workspace belongs to the organization
   * @param organizationId
   * @param configurationId
   * @param updateSsoConfigurationDto
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({
    name: 'configurationId',
    description: 'The id of the ssoConfiguration',
  })
  @Patch('sso/organization/:organizationId/configuration/:configurationId')
  @Permissions(updateSsoConfiguration)
  async updateSsoConfiguration(
    @Req() req: Request,
    @Param('organizationId') organizationId: string,
    @Param('configurationId') configurationId: string,
    @Body() updateSsoConfigurationDto: UpdateSsoConfigurationDto,
  ) {
    const { authorization } = req.headers;

    return await this.ssoService.updateSsoConfiguration(
      authorization as string,
      organizationId,
      configurationId,
      updateSsoConfigurationDto,
    );
  }

  /**
   * Endpoint responsible to delete an sso configuration and remove the cognito IdP
   * @param organizationId
   * @param configurationId
   * @returns
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({
    name: 'configurationId',
    description: 'The id of the ssoConfiguration',
  })
  @Delete('sso/organization/:organizationId/configuration/:configurationId')
  @Permissions(deleteSsoConfiguration)
  async deleteSsoConfiguration(
    @Req() req: Request,
    @Param('organizationId') organizationId: string,
    @Param('configurationId') configurationId: string,
  ) {
    const { authorization } = req.headers;

    return await this.ssoService.deleteSsoConfiguration(
      authorization as string,
      organizationId,
      configurationId,
    );
  }
}
