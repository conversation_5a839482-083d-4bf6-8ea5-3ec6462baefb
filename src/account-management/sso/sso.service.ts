import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  CreateSessionDto,
  CreateSsoConfigurationDto,
  SsoControllerService as SsoServiceSdk,
  UpdateSsoConfigurationDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { OrganizationInviteService } from '@vidmob/vidmob-organization-service-sdk';

interface StateData {
  organizationId?: string;
  inviteCode?: string;
  validationCode?: string;
  url: string;
}

@Injectable()
export class SsoService {
  private readonly logger = new Logger(SsoService.name);
  private readonly HTTP_PROTOCOL = 'http';

  private acsSsoDefaultUrl: string;

  constructor(
    private readonly ssoServiceSdk: SsoServiceSdk,
    private readonly organizationInviteService: OrganizationInviteService,
    private readonly configService: ConfigService,
  ) {
    this.acsSsoDefaultUrl =
      this.configService.getOrThrow<string>('acs.sso.defaultUrl');
  }

  async getUserSsoConfiguration(email: string) {
    return this.ssoServiceSdk.getUserSsoOptionsAsPromise(email);
  }

  async createSsoSession(createSessionDto: CreateSessionDto) {
    return this.ssoServiceSdk.createSsoSessionAsPromise(createSessionDto);
  }

  async createSsoConfiguration(
    authorization: string,
    organizationId: string,
    createSsoConfigurationDto: CreateSsoConfigurationDto,
  ) {
    this.ssoServiceSdk.defaultHeaders.authorization = authorization;

    return this.ssoServiceSdk.createSsoConfigurationAsPromise(
      organizationId,
      createSsoConfigurationDto,
    );
  }

  async listSsoConfiguration(
    organizationId: string,
    paginationOptions: PaginationOptions,
  ) {
    return await this.ssoServiceSdk.listSsoConfigurationAsPromise(
      organizationId,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async getSsoConfiguration(organizationId: string, configurationId: string) {
    return await this.ssoServiceSdk.getSsoConfigurationAsPromise(
      organizationId,
      configurationId,
    );
  }

  async updateSsoConfiguration(
    authorization: string,
    organizationId: string,
    configurationId: string,
    updateSsoConfigurationDto: UpdateSsoConfigurationDto,
  ) {
    this.ssoServiceSdk.defaultHeaders.authorization = authorization;

    return await this.ssoServiceSdk.updateSsoConfigurationAsPromise(
      organizationId,
      configurationId,
      updateSsoConfigurationDto,
    );
  }

  async deleteSsoConfiguration(
    authorization: string,
    organizationId: string,
    configurationId: string,
  ) {
    this.ssoServiceSdk.defaultHeaders.authorization = authorization;

    return await this.ssoServiceSdk.deleteSsoConfigurationAsPromise(
      organizationId,
      configurationId,
    );
  }

  /**
   * It will parse the state parameter. If the state parameter is just an URL, returns an StateData object containing only the URL,
   * if it's the invite info itself in base64, then decoded and parse the JSON and return the full StateData object.
   * @param state
   * @returns
   */
  parseStateParameter(state: string): StateData {
    if (state.startsWith(this.HTTP_PROTOCOL)) {
      // the state parameter is just the URL
      return { url: state };
    }

    try {
      const stateDataJson = Buffer.from(state, 'base64').toString();
      const stateData: StateData = JSON.parse(stateDataJson);

      return stateData;
    } catch (error) {
      this.logger.error(`Parsing state: ${state} parameter failed`, error);
    }

    return { url: this.acsSsoDefaultUrl };
  }

  /**
   * It will accept the invite if the invite info has the organizationId, validation code and invite code.
   * If doesn't have, just return it.
   * @param stateData
   * @returns
   */
  async acceptInvite(stateData: StateData) {
    if (
      !stateData.organizationId ||
      !stateData.inviteCode ||
      !stateData.validationCode
    ) {
      this.logger.error(`Incomplete state data for stateData: %o`, stateData);
      return;
    }

    try {
      const organizationInvite =
        await this.organizationInviteService.getOrganizationInviteByInviteCodeAndValidationCodeAsPromise(
          stateData.organizationId,
          stateData.inviteCode,
          stateData.validationCode,
        );

      const { email: inviteeEmail, id: inviteId } = organizationInvite.result;

      // If the invite is not pending, just log the error and return it.
      if (organizationInvite.result.status !== 'PENDING') {
        this.logger.error(
          `Organization Invite %o already accepted or revoked`,
          organizationInvite,
        );

        return;
      }

      await this.organizationInviteService.acceptOrganizationInviteAsPromise(
        stateData.organizationId,
        inviteId,
        {
          inviteeEmail,
        },
      );
    } catch (error) {
      this.logger.error(
        `Accepting invite through SSO callback failed for stateData: %o`,
        stateData,
        error,
      );
    }
  }

  doesStateContainInviteCode(stateData: StateData): boolean {
    return stateData.inviteCode ? true : false;
  }
}
