import { Test, TestingModule } from '@nestjs/testing';
import { SsoController } from './sso.controller';
import { Response } from 'express';
import { SsoService } from './sso.service';

describe('SsoController', () => {
  let controller: SsoController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [SsoController],
      providers: [
        {
          provide: SsoService,
          useValue: {
            parseStateParameter: jest
              .fn()
              .mockReturnValue({ url: 'https://test-redirect.com' }),
            acceptInvite: jest.fn().mockResolvedValue({}),
            doesStateContainInviteCode: jest.fn().mockReturnValue(true),
          },
        },
      ],
    }).compile();

    controller = module.get<SsoController>(SsoController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('redirectToState', () => {
    const res = { redirect: jest.fn() };

    it('should respond to a redirect', async () => {
      await controller.redirectToState(
        'https://test-redirect.com',
        '12345',
        res as unknown as Response,
      );

      expect(res.redirect).toHaveBeenCalledTimes(1);
      expect(res.redirect).toHaveBeenCalledWith(
        301,
        'https://test-redirect.com?code=12345',
      );
    });
  });
});
