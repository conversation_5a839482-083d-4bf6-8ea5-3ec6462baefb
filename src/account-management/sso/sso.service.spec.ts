import { Test, TestingModule } from '@nestjs/testing';
import { SsoService } from './sso.service';
import {
  ListSsoConfiguration200Response,
  ReadSsoConfigurationDto,
  SsoControllerService as SsoServiceSdk,
  UpdateSsoConfigurationDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import { OrganizationInviteService } from '@vidmob/vidmob-organization-service-sdk';
import { ConfigService } from '@nestjs/config';

describe('SsoService', () => {
  let service: SsoService;
  let ssoServiceSdk: SsoServiceSdk;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SsoService,
        {
          provide: SsoServiceSdk,
          useValue: {
            getUserSsoOptionsAsPromise: jest.fn(),
            createSsoSessionAsPromise: jest.fn(),
            createSsoConfigurationAsPromise: jest.fn(),
            listSsoConfigurationAsPromise: jest.fn(),
            deleteSsoConfigurationAsPromise: jest.fn(),
            updateSsoConfigurationAsPromise: jest.fn(),
            defaultHeaders: {},
          },
        },
        {
          provide: OrganizationInviteService,
          useValue: {
            getOrganizationInviteByInviteCodeAndValidationCodeAsPromise:
              jest.fn(),
            acceptOrganizationInviteAsPromise: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            getOrThrow: jest.fn().mockReturnValue('http://test.com'),
          },
        },
      ],
    }).compile();

    service = module.get<SsoService>(SsoService);
    ssoServiceSdk = module.get<SsoServiceSdk>(SsoServiceSdk);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  const userSsoOptions = {
    canUseLegacyLogin: false,
    cognitoHostedLogin: {
      url: 'https://cognito-hosted-login.com',
      responseType: 'code',
      scope: 'email+openid+phone',
      redirectUri: 'https://redirect-uri.com',
      identityProvider: 'test.com',
      clientId: 'client id',
    },
    ssoOptions: [
      {
        identifier: 'TEST',
        displayName: 'test.com',
        logo: 'https://test-logo/image.jpg',
      },
    ],
    hasInvite: true,
  };

  const createSessionDto = {
    code: 'xyz',
    clientId: 'client_id',
    clientSecret: 'client_secret',
  };

  const createSsoSessionResponse = {
    accessToken: 'id_token',
    expiresIn: 2000,
    sessionId: 'session_id',
  };

  const createSsoConfiguration = {
    domain: 'test.com',
    samlIdpMetadata: 'SAML IdP metadata',
  };

  const createSsoConfigurationResponse = {
    identifier: 'TEST',
    domain: 'test.com',
    configurationStatus: 'CONFIGURED',
    defaultWorkspaceId: 1,
    defaultRoleId: 1,
    ssoLoginOnly: true,
    canCreateNewUser: true,
    companyLogoUrl: 'https://test-logo/image.jpg',
    dateCreated: new Date(),
    lastUpdated: new Date(),
    id: 'xxx-yyy-zzz',
    createdBy: {
      id: 1,
      displayName: 'Admin user',
      photo: null,
    },
    organizationDomainId: 1,
    organizationDomain: {
      organizationId: 'xxx',
      id: 1,
      version: 0,
      domain: 'test.com',
      dateCreated: new Date(),
      lastUpdated: new Date(),
    },
  };

  const readSsoConfiguration = {
    identifier: 'TEST',
    providerName: 'test.com',
    configurationStatus: 'CONFIGURED',
    defaultWorkspaceId: 1,
    defaultRoleId: 1,
    ssoLoginOnly: true,
    canCreateNewUser: true,
    companyLogoUrl: 'https://test-logo/image.jpg',
    dateCreated: new Date(Date.now()),
    lastUpdated: new Date(Date.now()),
    id: 'xxx-yyy-zzz',
    adminContactEmail: '<EMAIL>',
  };

  const listSsoConfigurationResponse = {
    status: 'OK',
    result: [readSsoConfiguration],
  };

  const updateSsoConfiguration = {
    workspaceId: 1,
    ssoLoginOnly: true,
    canCreateNewUser: true,
    configurationStatus: 'CONFIGURED',
    companyLogoUrl: 'https://fake/logo,jpg',
  } as UpdateSsoConfigurationDto;

  describe('getUserSsoConfiguration', () => {
    beforeEach(() => {
      jest
        .spyOn(ssoServiceSdk, 'getUserSsoOptionsAsPromise')
        .mockResolvedValue(userSsoOptions);
    });

    it('should return the user sso options', async () => {
      await expect(
        service.getUserSsoConfiguration('<EMAIL>'),
      ).resolves.toStrictEqual(userSsoOptions);
    });
  });

  describe('createSsoSession', () => {
    beforeEach(() => {
      jest
        .spyOn(ssoServiceSdk, 'createSsoSessionAsPromise')
        .mockResolvedValue(createSsoSessionResponse);
    });

    it('should create the sso session', async () => {
      await expect(
        service.createSsoSession(createSessionDto),
      ).resolves.toStrictEqual(createSsoSessionResponse);
    });
  });

  describe('createSsoConfiguration', () => {
    beforeEach(() => {
      jest
        .spyOn(ssoServiceSdk, 'createSsoConfigurationAsPromise')
        .mockResolvedValue(
          createSsoConfigurationResponse as unknown as ReadSsoConfigurationDto,
        );
    });

    it('should create sso configuration successfully', async () => {
      await expect(
        service.createSsoConfiguration(
          'authorization',
          'organization-id',
          createSsoConfiguration,
        ),
      ).resolves.toBe(createSsoConfigurationResponse);
    });
  });

  describe('listSsoConfiguration', () => {
    beforeEach(() => {
      jest
        .spyOn(ssoServiceSdk, 'listSsoConfigurationAsPromise')
        .mockResolvedValue(
          listSsoConfigurationResponse as unknown as ListSsoConfiguration200Response,
        );
    });

    it('should list all sso configurations', async () => {
      await expect(
        service.listSsoConfiguration('organization-id', {}),
      ).resolves.toBe(listSsoConfigurationResponse);
    });
  });

  describe('updateSsoConfiguration', () => {
    beforeEach(() => {
      jest
        .spyOn(ssoServiceSdk, 'updateSsoConfigurationAsPromise')
        .mockResolvedValue(
          readSsoConfiguration as unknown as ReadSsoConfigurationDto,
        );
    });

    it('should update a sso configuration', async () => {
      await expect(
        service.updateSsoConfiguration(
          'authorization',
          'organization-id',
          'configuration-id',
          updateSsoConfiguration,
        ),
      ).resolves.toStrictEqual(readSsoConfiguration);
    });
  });

  describe('deleteSsoConfiguration', () => {
    beforeEach(() => {
      jest
        .spyOn(ssoServiceSdk, 'deleteSsoConfigurationAsPromise')
        .mockResolvedValue({});
    });

    it('should ldelete a sso configuration', async () => {
      await expect(
        service.deleteSsoConfiguration(
          'authorization',
          'organization-id',
          'configuration-id',
        ),
      ).resolves.toStrictEqual({});
    });
  });
});
