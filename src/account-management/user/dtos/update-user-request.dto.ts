import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * This DTO should enable on the future the user to only update fields that doesn't require password.
 * When enabling these updates, mark the code and password as optional and update the update user flow to handle different update cases
 */
export class UpdateUserRequestDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  code: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  password: string;

  @AutoMap()
  @IsString()
  @IsNotEmpty()
  platform: string;
}
