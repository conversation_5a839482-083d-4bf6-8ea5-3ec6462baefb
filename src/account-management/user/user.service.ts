import { Injectable, Logger } from '@nestjs/common';
import { PersonService } from '@vidmob/vidmob-authorization-service-sdk';
import { UpdateUserRequestDto } from './dtos/update-user-request.dto';
import { CreateEmailTokenRequestDto } from './dtos/create-email-token-request.dto';
import { rethrowAxiosError } from '../organization/workspace/utils/api.utils';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(private readonly personService: PersonService) {}

  async update(userId: number, updateRequestDto: UpdateUserRequestDto) {
    try {
      return await this.personService.updateUserAsPromise(
        userId,
        updateRequestDto,
      );
    } catch (error) {
      rethrowAxiosError(error);
    }
  }

  async generateEmailCode(
    userId: number,
    emailTokenRequest: CreateEmailTokenRequestDto,
  ) {
    return await this.personService.generateEmailCodeAsPromise(
      userId,
      emailTokenRequest,
    );
  }
}
