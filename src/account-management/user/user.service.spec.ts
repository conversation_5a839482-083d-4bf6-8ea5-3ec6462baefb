import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';
import { PersonService } from '@vidmob/vidmob-authorization-service-sdk';

describe('UserService', () => {
  let service: UserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: PersonService,
          useValue: {
            updateUserAsPromise: jest.fn(),
            generateEmailCodeAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
