import { Body, Controller, Patch, Post, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UserService } from './user.service';
import { CreateEmailTokenRequestDto } from './dtos/create-email-token-request.dto';
import { UpdateUserRequestDto } from './dtos/update-user-request.dto';

@ApiTags('user')
@Controller('account-management/user')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Patch()
  async updateUser(
    @Body() updateRequestDto: UpdateUserRequestDto,
    @Req() req: any,
  ) {
    const { userId } = req;
    return await this.userService.update(userId, updateRequestDto);
  }

  @Post('email-code')
  async generateEmailCode(
    @Body() emailTokenRequest: CreateEmailTokenRequestDto,
    @Req() req: any,
  ) {
    const { userId } = req;
    return await this.userService.generateEmailCode(userId, emailTokenRequest);
  }
}
