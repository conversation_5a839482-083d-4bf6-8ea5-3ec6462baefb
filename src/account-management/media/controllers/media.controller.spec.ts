import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { MediaController } from './media.controller';
import { MediaService } from '../services/media.service';

describe('MediaController', () => {
  let mediaController: MediaController;
  let mediaService: MediaService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [MediaController],
      providers: [
        {
          provide: MediaService,
          useValue: {
            findOrganizationsByMediaId: jest.fn(),
          },
        },
      ],
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
    }).compile();

    mediaController = moduleRef.get<MediaController>(MediaController);
    mediaService = moduleRef.get<MediaService>(MediaService);
  });

  it('test findOrganizationsByMediaId method ', async () => {
    const mediaId = 1234;
    const findOrganizationsByMediaIdSpy = jest.spyOn(
      mediaService,
      'findOrganizationsByMediaId',
    );
    await mediaController.findOrganizationsByMediaId(mediaId);
    expect(findOrganizationsByMediaIdSpy).toHaveBeenCalledWith(mediaId);
  });
});
