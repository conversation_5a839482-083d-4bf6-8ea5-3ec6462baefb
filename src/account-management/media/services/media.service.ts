import { Injectable, Logger } from '@nestjs/common';
import { MediaService as OrganizationMediaService } from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class MediaService {
  private readonly logger = new Logger(MediaService.name);
  constructor(
    private readonly organizationMediaService: OrganizationMediaService,
  ) {}

  /**
   * Find organizations by media id
   * @param mediaId media id
   */
  async findOrganizationsByMediaId(mediaId: number) {
    this.logger.log(`Finding organizations for media id ${mediaId}`);
    const response =
      await this.organizationMediaService.findOrganizationsForMediaAsPromise(
        mediaId,
      );
    return response.result;
  }
}
