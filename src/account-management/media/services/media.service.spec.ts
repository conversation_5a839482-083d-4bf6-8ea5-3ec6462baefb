import { Test } from '@nestjs/testing';
import { MediaService as OrganizationMediaService } from '@vidmob/vidmob-organization-service-sdk';
import { MediaService } from './media.service';

const TEST_MEDIA_ID = 1234;

describe('MediaService', () => {
  let mediaService: MediaService;
  let organizationMediaService: OrganizationMediaService;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        MediaService,
        {
          provide: OrganizationMediaService,
          useValue: {
            findOrganizationsForMediaAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    mediaService = moduleRef.get<MediaService>(MediaService);
    organizationMediaService = moduleRef.get<OrganizationMediaService>(
      OrganizationMediaService,
    );
  });

  it('should list organization for an media id', async () => {
    const organizationMediaServiceSpy = jest.spyOn(
      organizationMediaService,
      'findOrganizationsForMediaAsPromise',
    );
    organizationMediaServiceSpy.mockResolvedValueOnce({
      status: 'OK',
      result: {
        id: '0e801e54-cdef-44f1-9bb0-ca7fb859e408',
        name: 'Test Organization',
        status: 'ENABLED',
        dateCreated: '2023-07-19T01:15:32.000Z',
        lastUpdated: '2023-07-19T01:15:32.000Z',
        workspaces: [],
      },
    });
    await mediaService.findOrganizationsByMediaId(TEST_MEDIA_ID);
    expect(organizationMediaServiceSpy).toHaveBeenCalledWith(TEST_MEDIA_ID);
  });
});
