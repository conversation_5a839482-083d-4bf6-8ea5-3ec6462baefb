import { IsBoolean, IsDefined, IsString } from 'class-validator';

export class CreateOrganizationFeatureWhitelistDto {
  /**
   * Feature Whitelist identifier
   */
  @IsDefined({ message: 'Feature whitelist is a required field.' })
  @IsString({ message: 'Feature whitelist must be a string.' })
  featureWhitelist: string;

  /**
   * Feature Enabled
   */
  @IsDefined({ message: 'Feature whitelist enabled is a required field.' })
  @IsBoolean({ message: 'Feature whitelist enabled must be a boolean.' })
  featureEnabled: boolean;
}
