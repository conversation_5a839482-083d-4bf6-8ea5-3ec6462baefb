import {
  Body,
  Controller,
  Param,
  Post,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { FeaturesService } from '../services/features.service';
import { SuccessfulMessageResponseDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/successfulMessageResponseDto';
import { CreateOrganizationFeatureWhitelistDto } from '../dto/create-organization-feature-whitelist.dto';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Authorities } from '../../../auth/decorators/authority.decorator';

@ApiTags('Organization')
@ApiSecurity('Bearer Token')
@Controller('account-management/feature')
export class FeaturesController {
  constructor(private readonly featuresService: FeaturesService) {}

  /**
   * Enable or disable a feature whitelist for an organization.
   * @param req The request object.
   * @param organizationId organization id
   * @param createFWODto includes featureWhitelist identifier and featureEnabled flag.
   */
  @ApiParam({
    name: 'organizationId',
    type: 'string',
    description: 'organization id',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Post('organization/:organizationId/whitelist')
  @UsePipes(new ValidationPipe())
  async handleOrganizationFeatureWhitelist(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() createFWODto: CreateOrganizationFeatureWhitelistDto,
  ): Promise<SuccessfulMessageResponseDto[]> {
    const { userId } = req;
    return await this.featuresService.handleOrganizationFeatureWhitelist(
      userId,
      organizationId,
      createFWODto,
    );
  }
}
