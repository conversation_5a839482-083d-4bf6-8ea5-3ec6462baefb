import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { FeaturesController } from './features.controller';
import { FeaturesService } from '../services/features.service';
import { CreateOrganizationFeatureWhitelistDto } from '../dto/create-organization-feature-whitelist.dto';

describe('FeaturesController', () => {
  let featuresController: FeaturesController;
  let featuresService: FeaturesService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [FeaturesController],
      providers: [
        {
          provide: FeaturesService,
          useValue: {
            handleOrganizationFeatureWhitelist: jest.fn(),
          },
        },
      ],
    }).compile();

    featuresController = moduleRef.get<FeaturesController>(FeaturesController);
    featuresService = moduleRef.get<FeaturesService>(FeaturesService);
  });

  it('test handleOrganizationFeatureWhitelist method ', async () => {
    const req = { userId: 123 };
    const createFWODto: CreateOrganizationFeatureWhitelistDto = {
      featureWhitelist: 'TEST_FEATURE_WHITELIST',
      featureEnabled: true,
    };
    const handleOrganizationFeatureWhitelistSpy = jest.spyOn(
      featuresService,
      'handleOrganizationFeatureWhitelist',
    );
    await featuresController.handleOrganizationFeatureWhitelist(
      req,
      'TEST_ORGANIZATION_ID',
      createFWODto,
    );
    expect(handleOrganizationFeatureWhitelistSpy).toHaveBeenCalledWith(
      req.userId,
      'TEST_ORGANIZATION_ID',
      createFWODto,
    );
  });
});
