import { Injectable } from '@nestjs/common';
import { FeaturesService as FeaturesServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { CreateFeatureWhitelistDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createFeatureWhitelistDto';
import { SuccessfulMessageResponseDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/successfulMessageResponseDto';
import { CreateOrganizationFeatureWhitelistDto } from '../dto/create-organization-feature-whitelist.dto';

@Injectable()
export class FeaturesService {
  constructor(
    private readonly organizationFeatureService: FeaturesServiceSDK,
  ) {}

  /**
   * Enable or disable a feature whitelist for an organization.
   * @param userId The user id making the request.
   * @param organizationId organization id
   * @param createFWODto includes featureWhitelist identifier and featureEnabled flag.
   */
  async handleOrganizationFeatureWhitelist(
    userId: number,
    organizationId: string,
    createFWODto: CreateOrganizationFeatureWhitelistDto,
  ): Promise<SuccessfulMessageResponseDto[]> {
    const createFeatureWhitelistDto: CreateFeatureWhitelistDto = {
      ...createFWODto,
      userId: userId,
    };
    const response =
      await this.organizationFeatureService.handleOrganizationFeatureWhitelistAsPromise(
        organizationId,
        createFeatureWhitelistDto,
      );
    return response.result;
  }
}
