import { Test } from '@nestjs/testing';
import { FeaturesService } from './features.service';
import { FeaturesService as FeaturesServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { CreateOrganizationFeatureWhitelistDto } from '../dto/create-organization-feature-whitelist.dto';

const TEST_MEDIA_ID = 1234;

describe('FeaturesService', () => {
  let featuresService: FeaturesService;
  let featuresServiceSDK: FeaturesServiceSDK;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        FeaturesService,
        {
          provide: FeaturesServiceSDK,
          useValue: {
            handleOrganizationFeatureWhitelistAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    featuresService = moduleRef.get<FeaturesService>(FeaturesService);
    featuresServiceSDK = moduleRef.get<FeaturesServiceSDK>(
      FeaturesServiceSDK,
    );
  });

  it('Test handleOrganizationFeatureWhitelist method', async () => {
    const TEST_USER_ID = 123;
    const createFWODto: CreateOrganizationFeatureWhitelistDto = {
      featureWhitelist: 'TEST_FEATURE_WHITELIST',
      featureEnabled: true,
    };
    const handleOrganizationFeatureWhitelistSpy = jest.spyOn(
      featuresServiceSDK,
      'handleOrganizationFeatureWhitelistAsPromise',
    ).mockResolvedValue({ status: 'OK', result: [] });
    await featuresService.handleOrganizationFeatureWhitelist(
      TEST_USER_ID,
      'TEST_ORGANIZATION_ID',
      createFWODto,
    );
    expect(handleOrganizationFeatureWhitelistSpy).toHaveBeenCalledWith(
      'TEST_ORGANIZATION_ID',
      {
        ...createFWODto,
        userId: TEST_USER_ID,
      },
    );
  });
});
