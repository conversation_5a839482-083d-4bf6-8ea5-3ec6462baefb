import { PermissionSubResource } from '../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../auth/enums/permission.domain.enum';
import { PermissionAction } from '../auth/enums/permission.action.enum';

import {
  organization<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  organizationFromParamsHandler,
  partnerFromBodyHandler,
  workspaceFromParamsHandler,
} from '../auth/decorators/permission.decorator';

export const readWorkspaces = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const createWorkspace = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.WORKSPACE,
    },
  ],
};
export const readWorkspaceConfiguration = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CONFIGURATION,
    },
  ],
};

export const updateWorkspaceConfiguration = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.CONFIGURATION,
    },
  ],
};

export const updateWorkspaceDetails = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.DETAILS,
    },
  ],
};

export const readWorkspaceDetails = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DETAILS,
    },
  ],
};

export const readBrandConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.BRAND,
    },
  ],
};
export const createBrandConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.BRAND,
    },
  ],
};

export const updateBrandConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.BRAND,
    },
  ],
};

export const deleteBrandConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.BRAND,
    },
  ],
};

export const readUserConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.USER,
    },
  ],
};
export const createUserConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.USER,
    },
  ],
};
export const updateUserConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.USER,
    },
  ],
};
export const deleteUserConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.USER,
    },
  ],
};

export const updateOrganizationToAdAccountMapping = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromBodyHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const updateOrganizationParamsToAdAccountMapping = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const readOrganizationToAdAccountMapping = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const checkOrganizationReadAllWorkspaces = {
  statements: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ALL,
    },
  ],
};

export const readAllWorkspaces = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [...checkOrganizationReadAllWorkspaces.statements],
};

export const readOrganizationWorkspacePlatformAdAccountMapping = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const readOrganizationWorkspaceAdAccountMapping = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const updateWorkspaceAdAccountMapping = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const checkAccountDetailsReadAccess = {
  statements: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT_DETAILS,
    },
  ],
};

export const createWorkspaceAdAccountsMetadata = {
  statements: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.AD_ACCOUNT_DETAILS,
    },
  ],
};

export const createOrganizationAdAccountsMetadata = {
  statements: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.AD_ACCOUNT_DETAILS,
    },
  ],
};

export const createOrganizationBulkUserWorkspace = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.BULK_USER_WORKSPACE_ASSIGNED,
    },
  ],
};

export const readOrganizationInvites = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.INVITE,
    },
  ],
};

export const createWorkspaceUserInvite = {
  statements: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.USER_INVITE,
    },
  ],
};

export const createWorkspaceStandardUserInvite = {
  statements: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.STANDARD_USER_INVITE,
    },
  ],
};

export const deleteOrganizationInvites = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.INVITE,
    },
  ],
};

export const updateOrganizationInvites = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.INVITE,
    },
  ],
};

export const readWorkspaceUsers = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.USER,
    },
  ],
};

export const createSsoConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.SSO_CONFIGURATION,
    },
  ],
};

export const readSsoConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.SSO_CONFIGURATION,
    },
  ],
};

export const deleteSsoConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.SSO_CONFIGURATION,
    },
  ],
};

export const updateSsoConfiguration = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.SSO_CONFIGURATION,
    },
  ],
};

export const readIndustryData = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.INDUSTRY,
    },
  ],
};

export const updateProjectDetails = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromBodyHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.PROJECT,
    },
  ],
};
