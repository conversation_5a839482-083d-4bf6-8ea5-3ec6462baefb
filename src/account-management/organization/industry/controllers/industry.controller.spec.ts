import { IndustryController } from './industry.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { IndustryService } from '../services/industry.service';
import { OrganizationService } from '@vidmob/vidmob-organization-service-sdk/dist/api/organization.service';

describe('IndustryController', () => {
  let industryController: IndustryController;
  let industryService: IndustryService;
  const ORGANIZATION_ID = '27a7e882-43de-4bfa-8f53-3b62875b8432';
  const INDUSTRY_GROUP_ID = 123;
  const INDUSTRY_ID = 234;
  const SUB_INDUSTRY_ID = 345;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [IndustryController],
      providers: [
        IndustryService,
        OrganizationService,
        {
          provide: OrganizationService,
          useFactory: () => ({
            getIndustryGroupsAsPromise: jest.fn(),
            getIndustriesAsPromise: jest.fn(),
            getSubIndustriesAsPromise: jest.fn(),
          }),
        },
      ],
    }).compile();

    industryController = module.get<IndustryController>(IndustryController);
    industryService = module.get<IndustryService>(IndustryService);
  });

  it('IndustryController should be defined', () => {
    expect(industryController).toBeDefined();
  });

  it('IndustryController should return mock industry group response', async () => {
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: INDUSTRY_GROUP_ID,
          name: 'ABC',
          identifier: 'ABC',
        },
      ],
    } as any;
    jest
      .spyOn(industryService, 'getIndustryGroups')
      .mockResolvedValueOnce(expectedResponse);
    const result = await industryController.getIndustryGroups(ORGANIZATION_ID);
    expect(industryService.getIndustryGroups).toHaveBeenCalledWith(
      ORGANIZATION_ID,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('IndustryController should return mock industry response', async () => {
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: INDUSTRY_ID,
          name: 'DEF',
          identifier: 'ABC:DEF',
        },
      ],
    } as any;
    jest
      .spyOn(industryService, 'getIndustries')
      .mockResolvedValueOnce(expectedResponse);
    const result = await industryController.getIndustries(
      ORGANIZATION_ID,
      INDUSTRY_GROUP_ID,
    );
    expect(industryService.getIndustries).toHaveBeenCalledWith(
      ORGANIZATION_ID,
      INDUSTRY_GROUP_ID,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('IndustryController should return mock sub-industry response', async () => {
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: SUB_INDUSTRY_ID,
          name: 'GHI',
          identifier: 'ABC:DEF:GHI',
        },
      ],
    } as any;
    jest
      .spyOn(industryService, 'getSubIndustries')
      .mockResolvedValueOnce(expectedResponse);
    const result = await industryController.getSubIndustries(
      ORGANIZATION_ID,
      INDUSTRY_ID,
    );
    expect(industryService.getSubIndustries).toHaveBeenCalledWith(
      ORGANIZATION_ID,
      INDUSTRY_ID,
    );
    expect(result).toEqual(expectedResponse);
  });
});
