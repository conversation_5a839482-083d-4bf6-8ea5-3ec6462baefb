import { Injectable } from '@nestjs/common';
import { OrganizationService as OrganizationServiceSdk } from '@vidmob/vidmob-organization-service-sdk/dist/api/organization.service';

@Injectable()
export class IndustryService {
  constructor(private readonly organizationService: OrganizationServiceSdk) {}

  async getIndustryGroups(organizationId: string) {
    return await this.organizationService.getIndustryGroupsAsPromise(
      organizationId,
    );
  }

  async getIndustries(organizationId: string, industryGroupId: number) {
    return await this.organizationService.getIndustriesAsPromise(
      organizationId,
      industryGroupId,
    );
  }

  async getSubIndustries(organizationId: string, industryId: number) {
    return await this.organizationService.getSubIndustriesAsPromise(
      organizationId,
      industryId,
    );
  }
}
