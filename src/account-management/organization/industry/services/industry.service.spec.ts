import { Test, TestingModule } from '@nestjs/testing';
import { IndustryService } from './industry.service';
import { OrganizationService } from '@vidmob/vidmob-organization-service-sdk/dist/api/organization.service';

describe('IndustryService', () => {
  let industryService: IndustryService;
  let orgService: OrganizationService;
  const ORGANIZATION_ID = '27a7e882-43de-4bfa-8f53-3b62875b8432';
  const INDUSTRY_GROUP_ID = 123;
  const INDUSTRY_ID = 234;
  const SUB_INDUSTRY_ID = 345;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        IndustryService,
        OrganizationService,
        {
          provide: OrganizationService,
          useFactory: () => ({
            getIndustryGroupsAsPromise: jest.fn(),
            getIndustriesAsPromise: jest.fn(),
            getSubIndustriesAsPromise: jest.fn(),
          }),
        },
      ],
    }).compile();

    industryService = module.get<IndustryService>(IndustryService);
    orgService = module.get<OrganizationService>(OrganizationService);
  });

  it('IndustryService should be defined', () => {
    expect(industryService).toBeDefined();
  });

  it('IndustryService should return mock industry group response', async () => {
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: INDUSTRY_GROUP_ID,
          name: 'ABC',
          identifier: 'ABC',
        },
      ],
    } as any;
    jest
      .spyOn(orgService, 'getIndustryGroupsAsPromise')
      .mockResolvedValueOnce(expectedResponse);
    const result = await industryService.getIndustryGroups(ORGANIZATION_ID);
    expect(result).toEqual(expectedResponse);
  });

  it('IndustryService should return mock industry response', async () => {
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: INDUSTRY_ID,
          name: 'DEF',
          identifier: 'ABC:DEF',
        },
      ],
    } as any;
    jest
      .spyOn(orgService, 'getIndustriesAsPromise')
      .mockResolvedValueOnce(expectedResponse);
    const result = await industryService.getIndustries(
      ORGANIZATION_ID,
      INDUSTRY_GROUP_ID,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('IndustryService should return mock sub-industry response', async () => {
    const expectedResponse = {
      status: 'OK',
      result: [
        {
          id: SUB_INDUSTRY_ID,
          name: 'GHI',
          identifier: 'ABC:DEF:GHI',
        },
      ],
    } as unknown as any;
    jest
      .spyOn(orgService, 'getSubIndustriesAsPromise')
      .mockResolvedValueOnce(expectedResponse);
    const result = await industryService.getSubIndustries(
      ORGANIZATION_ID,
      INDUSTRY_ID,
    );
    expect(result).toEqual(expectedResponse);
  });
});
