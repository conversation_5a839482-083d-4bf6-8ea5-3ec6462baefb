import { ApiProperty } from '@nestjs/swagger';

export class IndustryResponseDTO {
  @ApiProperty({
    description: 'The unique ID of the industry',
    example: 23,
  })
  id: number;

  @ApiProperty({
    description: 'The name of the industry',
    example: 'Apparel',
  })
  name: string;

  @ApiProperty({
    description: 'The unique identifier for the industry',
    example: 'RETAIL:APPAREL',
  })
  identifier: string;
}
