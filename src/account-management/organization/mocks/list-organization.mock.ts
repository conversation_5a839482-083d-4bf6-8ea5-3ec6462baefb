// mockOrganizations.ts

/*
 * This file contains initial mock data for organizations. It's designed to
 * serve as a temporary data source and does not connect to any database.
 * The primary intention behind this mock is to unblock front-end development
 * activities and provide them with a structure to work against until the
 * backend services are fully operational.
 */

export const mockData = {
  status: 'OK',
  result: [
    {
      id: '0c598994-f0db-413a-8089-2c498bfe485e',
      name: 'Dyna Corp',
      associatedWorkspaces: '4',
      redirectWorkspaceId: '30262',
    },
    {
      id: 'bc5330e7-0dfa-4abc-8205-e23a3a733952',
      name: '<PERSON> Titans',
      associatedWorkspaces: '3',
      redirectWorkspaceId: '1223',
    },
    {
      id: '27a7e882-43de-4bfa-8f53-3b62875b8432',
      name: 'Nest Geeks',
      associatedWorkspaces: '43',
      redirectWorkspaceId: '31583',
    },
    {
      id: '000b85cc-7648-49c1-84cc-555bc19a9241',
      name: '<EMAIL>',
      associatedWorkspaces: '19',
      redirectWorkspaceId: '31434',
    },
    {
      id: '5959b5b6-3a70-4dab-98e1-5df15965fe49',
      name: 'Visionary Ventures',
      associatedWorkspaces: '1',
      redirectWorkspaceId: '23524',
    },
  ],
  pagination: {
    offset: 0,
    perPage: 10,
    nextOffset: 0,
    totalSize: 5,
  },
};
