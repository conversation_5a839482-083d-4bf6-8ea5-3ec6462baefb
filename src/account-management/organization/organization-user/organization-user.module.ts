import { Module } from '@nestjs/common';
import { OrganizationUserService } from './organization-user.service';
import { OrganizationUserController } from './organization-user.controller';
import { OrganizationUserCsvService } from './organization-user-csv.service';

@Module({
  controllers: [OrganizationUserController],
  providers: [OrganizationUserService, OrganizationUserCsvService],
  exports: [OrganizationUserService],
})
export class OrganizationUserModule {}
