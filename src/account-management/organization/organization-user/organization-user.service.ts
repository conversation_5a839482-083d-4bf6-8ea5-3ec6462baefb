import { Injectable } from '@nestjs/common';
import { UpdateOrganizationUserDto } from './dto/update-organization-user.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  OrganizationUserResponseDto,
  OrganizationUserService as OrganizationUserServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { fetchAllDataFromPagination } from 'src/utils/fetch-all-data';
import { OrganizationUserWorkspaceResponseDto } from './dto/organization-user-workspace-response.dto';

@Injectable()
export class OrganizationUserService {
  constructor(
    private readonly organizationServiceSdk: OrganizationUserServiceSdk,
  ) {}

  /**
   * Lightweight method to load all users within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all users, without much querying.
   *
   */
  async getAllLightweight(organizationId: string) {
    return await this.organizationServiceSdk.getAllUsersAsPromise(
      organizationId,
    );
  }

  /**
   * It will return the user inside the organization (user details and role(s) in that org)
   * @param organizationId
   * @param userId
   * @returns
   */
  async getUserInOrganization(organizationId: string, userId: number) {
    return await this.organizationServiceSdk.getUserAsPromise(
      organizationId,
      userId,
    );
  }

  async findAll(
    organizationId: string,
    search: string | undefined,
    filterBy: string | undefined,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<OrganizationUserResponseDto>> {
    const response = await this.organizationServiceSdk.findAllAsPromise(
      organizationId,
      search || '',
      filterBy || '',
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
    return new PaginatedResultArray(
      response.result,
      response.pagination?.totalSize,
    );
  }

  async findAllForCsv(
    organizationId: string,
    filterBy: string | undefined,
  ): Promise<OrganizationUserWorkspaceResponseDto[]> {
    return await fetchAllDataFromPagination<OrganizationUserWorkspaceResponseDto>(
      (offset, limit) =>
        this.organizationServiceSdk.findAllUserWorkspaceAsPromise(
          organizationId,
          filterBy || '',
          offset,
          limit,
          '',
        ),
      500,
    );
  }

  async update(
    organizationId: string,
    userId: number,
    updateOrganizationUserDto: UpdateOrganizationUserDto,
  ): Promise<OrganizationUserResponseDto> {
    const response = await this.organizationServiceSdk.updateAsPromise(
      organizationId,
      userId,
      updateOrganizationUserDto,
    );
    return response.result;
  }

  async remove(organizationId: string, userId: number) {
    const response = await this.organizationServiceSdk.removeAsPromise(
      organizationId,
      userId,
    );
    return response.result;
  }
}
