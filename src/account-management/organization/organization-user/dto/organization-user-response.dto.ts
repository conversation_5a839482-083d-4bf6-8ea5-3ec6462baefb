import { AutoMap } from '@automapper/classes';
import { OrganizationUserRoleResponseDto } from './organization-user-role-response.dto';

/**
 * User within an organization
 */
export class OrganizationUserResponseDto {
  /**
   * Id of the User
   * @example 123
   */
  @AutoMap()
  id: number;

  /**
   * Username
   * @example <EMAIL>
   */
  @AutoMap()
  username: string;

  /**
   * First name of user
   * @example FirstName
   */
  @AutoMap()
  firstName: string;

  /**
   * Last name of user
   * @example LastName
   */
  @AutoMap()
  lastName: string;

  /**
   * Display name of user
   * @example FirstName LastName
   */
  @AutoMap()
  displayName: string;

  /**
   * Email of user
   * @example <EMAIL>
   */
  @AutoMap()
  email: string;

  /**
   * Job title of user
   * @example Pilot
   */
  @AutoMap()
  jobTitle: string;

  /**
   * Photo of user
   * @example https://d2mcrmdbaugu8j.cloudfront.net/ABC123DEFHIJ/user/avatar/user-avatar.png
   */
  @AutoMap()
  photo: string;

  /**
   * Roles of user on organization
   */
  @AutoMap()
  roles: OrganizationUserRoleResponseDto[];

  /**
   * Last login date
   * @example 2024-04-04T18:50:10.000Z
   */
  @AutoMap()
  lastLoginDate: string;
}
