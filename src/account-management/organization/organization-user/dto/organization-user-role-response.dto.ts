import { AutoMap } from '@automapper/classes';

/**
 * Role of a user within an organization
 */
export class OrganizationUserRoleResponseDto {
  /**
   * Id of the role
   * @example 123
   */
  @AutoMap()
  id: number;

  /**
   * Name of the role
   * @example Organization Admin
   * @example Organization Standard
   */
  @AutoMap()
  name: string;

  /**
   * Type of the role
   * @example organization_entity
   */
  @AutoMap()
  type: string;

  /**
   * Identifier of the role
   * @example ORG_ADMIN
   */
  @AutoMap()
  identifier: string;

  /**
   * Description of the role
   * @example Able to view all details of the organization entity.
   */
  @AutoMap()
  description: string;
}
