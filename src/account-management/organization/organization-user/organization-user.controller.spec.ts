import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationUserController } from './organization-user.controller';
import { OrganizationUserService } from './organization-user.service';
import { OrganizationUserService as OrganizationUserServiceSdk } from '@vidmob/vidmob-organization-service-sdk';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { BadRequestException } from '@nestjs/common';
import { OrganizationUserCsvService } from './organization-user-csv.service';

const TEST_USER_ID = 1;
const TEST_ORGANIZATION_ID = 'xxxxx';

describe('OrganizationUserController', () => {
  let controller: OrganizationUserController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationUserController],
      providers: [
        OrganizationUserService,
        OrganizationUserCsvService,
        {
          provide: OrganizationUserServiceSdk,
          useValue: {
            findAllAsPromise: jest.fn(() =>
              Promise.resolve(new PaginatedResultArray([], 0)),
            ),
          },
        },
      ],
    }).compile();

    controller = module.get<OrganizationUserController>(
      OrganizationUserController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('remove', () => {
    it('throw an error if the requester user is trying to remove themself from an org', async () => {
      await expect(async () => {
        await controller.remove(
          { userId: TEST_USER_ID },
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
        );
      }).rejects.toThrow(
        new BadRequestException(
          'User is not allowed to remove themself from an organization',
        ),
      );
    });
  });
});
