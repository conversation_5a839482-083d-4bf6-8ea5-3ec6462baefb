import {
  Controller,
  Get,
  Body,
  Put,
  Param,
  Delete,
  ParseIntPipe,
  Query,
  Request,
  BadRequestException,
  <PERSON><PERSON>,
  Header,
} from '@nestjs/common';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { ApiParam, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';
import { OrganizationUserService } from './organization-user.service';
import { UpdateOrganizationUserDto } from './dto/update-organization-user.dto';
import { OrganizationUserResponseDto } from './dto/organization-user-response.dto';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import {
  deleteUserConfiguration,
  readUserConfiguration,
  updateUserConfiguration,
} from '../../account-management.permissions';
import { OrganizationUserCsvService } from './organization-user-csv.service';
import { Writable } from 'stream';
import { ORGANIZATION_USER_FILTER_BY } from 'src/constants/platform.constants';
import { Response } from 'express';

@ApiTags('Organization User')
@ApiSecurity('Bearer Token')
@Controller('account-management/organization/:organizationId/user')
export class OrganizationUserController {
  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationUserCsvService: OrganizationUserCsvService,
  ) {}

  /**
   * Lightweight endpoint to load all users within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all users, without much querying.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all users belong.
   * @returns - all organization users: name, id and email
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @Permissions(readUserConfiguration)
  @Get('all')
  async getAllUsers(@Param('organizationId') organizationId: string) {
    return await this.organizationUserService.getAllLightweight(organizationId);
  }

  /**
   * Return all users for the organization.
   * @param organizationId org id
   */
  @VmApiOkPaginatedArrayResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiQuery({
    name: 'search',
    type: 'string',
    description: 'Search string on display name or username of user',
  })
  @ApiQuery({
    name: 'filterBy',
    type: 'string',
    description:
      'filter the results whether for vidmob employees or no vidmob employees. Example: VIDMOB_ONLY or NO_VIDMOB',
  })
  @Permissions(readUserConfiguration)
  @Get()
  findAll(
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('organizationId') organizationId: string,
    @Query('search') search?: string,
    @Query('filterBy') filterBy?: string,
  ) {
    return this.organizationUserService.findAll(
      organizationId,
      search,
      filterBy,
      paginationOptions,
    );
  }

  /**
   * Return a .csv file for all users for the organization.
   * @param organizationId org id
   * @param res
   * @param filterBy
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiQuery({
    name: 'filterBy',
    type: 'string',
    description:
      'filter the results whether for vidmob employees or no vidmob employees. Example: VIDMOB_ONLY or NO_VIDMOB',
  })
  @Permissions(readUserConfiguration)
  @Get('download-csv')
  @Header('Content-Type', 'text/csv')
  @Header('Content-Disposition', 'attachment; filename="users.csv"')
  async downloadCsv(
    @Param('organizationId') organizationId: string,
    @Res() res: Response,
    @Query('filterBy') filterBy?: ORGANIZATION_USER_FILTER_BY,
  ) {
    const users = await this.organizationUserService.findAllForCsv(
      organizationId,
      filterBy,
    );

    const csvStream = this.organizationUserCsvService.generateCsv(
      users,
      filterBy,
    );
    csvStream.pipe(res as unknown as Writable);
  }

  /**
   * Update roles of a user on an organization.
   */
  @VmApiOkResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'id',
    type: 'number',
    description: 'User ID to update on the organization',
  })
  @Permissions(updateUserConfiguration)
  @Put(':id')
  update(
    @Param('organizationId') organizationId: string,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrganizationUserDto: UpdateOrganizationUserDto,
  ) {
    return this.organizationUserService.update(
      organizationId,
      id,
      updateOrganizationUserDto,
    );
  }

  /**
   * Remove a user from an organization.
   */
  @VmApiOkResponse({
    type: OrganizationUserResponseDto,
  })
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'id',
    type: 'number',
    description: 'User ID to update on the organization',
  })
  @Permissions(deleteUserConfiguration)
  @Delete(':id')
  remove(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('id', ParseIntPipe) id: number,
  ) {
    const { userId } = req;

    if (userId === id) {
      throw new BadRequestException(
        'User is not allowed to remove themself from an organization',
      );
    }

    return this.organizationUserService.remove(organizationId, id);
  }
}
