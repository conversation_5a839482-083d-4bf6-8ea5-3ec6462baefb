import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationUserService } from './organization-user.service';
import { OrganizationUserService as OrganizationUserServiceSdk } from '@vidmob/vidmob-organization-service-sdk';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';

const userResponse = {
  status: 'OK',
  result: {
    id: 1,
    username: '',
    firstName: '',
    lastName: '',
    displayName: '',
    email: '',
    jobTitle: '',
    photo: '',
    roles: [
      {
        id: 32,
        name: 'Admin',
        type: 'organization_entity',
        identifier: 'ORG_ADMIN',
        description: '',
      },
    ],
  },
};

const mockOrganizationUserServiceSdk = {
  findAllAsPromise: jest.fn(() =>
    Promise.resolve({ result: [], pagination: { totalSize: 0 } }),
  ),
  updateAsPromise: jest.fn(() => Promise.resolve({ result: { id: '2' } })),
  removeAsPromise: jest.fn(() => Promise.resolve({ result: { id: '2' } })),
  getUserAsPromise: jest.fn(() => Promise.resolve(userResponse)),
};

describe('OrganizationUserService', () => {
  let service: OrganizationUserService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationUserService,
        {
          provide: OrganizationUserServiceSdk,
          useValue: mockOrganizationUserServiceSdk,
        },
      ],
    }).compile();

    service = module.get<OrganizationUserService>(OrganizationUserService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find one user', async () => {
    await expect(service.getUserInOrganization('1234', 1)).resolves.toBe(
      userResponse,
    );
  });

  it('should find all', () => {
    const result = service.findAll('1', 'search', undefined, {
      offset: 0,
      perPage: 10,
    });
    expect(result).resolves.toEqual(new PaginatedResultArray([], 0));
    expect(mockOrganizationUserServiceSdk.findAllAsPromise).toBeCalledWith(
      '1',
      'search',
      '',
      0,
      10,
      undefined,
    );
  });

  it('should update', () => {
    const result = service.update('1', 2, {
      roles: [],
    });
    expect(result).resolves.toMatchObject({ id: '2' });
    expect(mockOrganizationUserServiceSdk.updateAsPromise).toBeCalledWith(
      '1',
      2,
      { roles: [] },
    );
  });

  it('should remove', () => {
    const result = service.remove('1', 2);
    expect(result).resolves.toMatchObject({ id: '2' });
    expect(mockOrganizationUserServiceSdk.removeAsPromise).toBeCalledWith(
      '1',
      2,
    );
  });
});
