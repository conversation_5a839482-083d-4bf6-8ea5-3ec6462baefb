import { Stream } from 'stream';
import { OrganizationUserWorkspaceResponseDto } from './dto/organization-user-workspace-response.dto';
import { OrganizationUserCsvService } from './organization-user-csv.service';
import { Test, TestingModule } from '@nestjs/testing';
import { ORGANIZATION_USER_FILTER_BY } from 'src/constants/platform.constants';

describe('OrganizationUserCsvService', () => {
  let service: OrganizationUserCsvService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OrganizationUserCsvService],
    }).compile();

    service = module.get<OrganizationUserCsvService>(
      OrganizationUserCsvService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  async function streamToString(stream: Stream): Promise<string> {
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
      stream.on('error', (err) => reject(err));
      stream.on('end', () => resolve(Buffer.concat(chunks).toString('utf8')));
    });
  }

  const data: OrganizationUserWorkspaceResponseDto[] = [
    {
      id: 1,
      displayName: 'John Doe',
      workspaces: [
        {
          id: 1,
          name: 'Workspace 1',
          isPrimary: false,
        },
        {
          id: 2,
          name: 'Workspace 2',
          isPrimary: false,
        },
      ],
      username: '<EMAIL>',
      firstName: 'john',
      lastName: 'doe',
      email: '<EMAIL>',
      jobTitle: '',
      photo: '',
      roles: [
        {
          id: 1,
          name: 'Admin',
          type: 'organization_entity',
          identifier: 'ORG_ADMIN',
          description: '',
        },
      ],
      lastLoginDate: new Date().toISOString(),
    },
  ];

  it('should generate vidmob only csv data', async () => {
    const csvStream = service.generateCsv(
      data,
      ORGANIZATION_USER_FILTER_BY.VIDMOB_ONLY,
    );

    const csvData = await streamToString(csvStream);

    expect(csvData).toBe(
      `ID,Name,Org Role,Email,Workspace Names\n1,John Doe,Admin,<EMAIL>,\"Workspace 1, Workspace 2\"`,
    );
  });

  it('should generate non vidmob csv data', async () => {
    const csvStream = service.generateCsv(
      data,
      ORGANIZATION_USER_FILTER_BY.NO_VIDMOB,
    );

    const csvData = await streamToString(csvStream);
    const lastLoginDate = new Date(data[0].lastLoginDate).toLocaleDateString(
      'en-US',
    );
    expect(csvData).toBe(
      `ID,Name,Org Role,Email,Workspace Names,Last Login Date\n1,John Doe,Admin,<EMAIL>,\"Workspace 1, Workspace 2\",${lastLoginDate}`,
    );
  });
});
