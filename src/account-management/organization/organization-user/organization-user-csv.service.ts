import { Injectable } from '@nestjs/common';
import { Stream } from 'stream';
import { generateCsvStream } from 'src/utils/csv-generation';
import { OrganizationUserWorkspaceResponseDto } from './dto/organization-user-workspace-response.dto';
import { ORGANIZATION_USER_FILTER_BY } from 'src/constants/platform.constants';

@Injectable()
export class OrganizationUserCsvService {
  generateCsv(
    users: OrganizationUserWorkspaceResponseDto[],
    filterBy?: ORGANIZATION_USER_FILTER_BY,
  ): Stream {
    const headersMapping =
      filterBy === ORGANIZATION_USER_FILTER_BY.NO_VIDMOB
        ? this.nonVidmobPeoplePageHeadersMapping()
        : this.commomHeadersMapping();

    return generateCsvStream<OrganizationUserWorkspaceResponseDto>(
      users,
      headersMapping,
    );
  }

  private nonVidmobPeoplePageHeadersMapping() {
    // Instantiate a date formatter.
    // since every .toLocaleDateString calls results it has to perform a search in a big database of localization strings,
    // which is potentially inefficient
    const dateFormatter = new Intl.DateTimeFormat('en-US');

    const headersMapping = this.commomHeadersMapping();

    return {
      ...headersMapping,
      'Last Login Date': (row: OrganizationUserWorkspaceResponseDto) => {
        if (row.lastLoginDate) {
          return dateFormatter.format(new Date(row.lastLoginDate));
        }

        return '';
      },
    };
  }

  private commomHeadersMapping() {
    return {
      ID: 'id',
      Name: 'displayName',
      'Org Role': (row: OrganizationUserWorkspaceResponseDto) =>
        row.roles[0].name,
      Email: 'email',
      'Workspace Names': (row: OrganizationUserWorkspaceResponseDto) =>
        row.workspaces.map((workspace) => workspace.name).join(', '),
    };
  }
}
