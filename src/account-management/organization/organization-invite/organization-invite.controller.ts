import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Header,
  Param,
  Patch,
  Post,
  Query,
  Request,
  Res,
} from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  createOrganizationBulkUserWorkspace,
  deleteOrganizationInvites,
  readOrganizationInvites,
  updateOrganizationInvites,
} from '../../../account-management/account-management.permissions';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { UUID } from 'typeorm/driver/mongodb/bson.typings';
import { OrganizationInviteService } from './organization-invite.service';
import { CreateBulkUserWorkspaceDto } from './dto/create-bulk-user-workspace.dto';
import { CreateBulkUserSingleWorkspaceDto } from './dto/create-bulk-user-single-workspace.dto';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { OrganizationInviteSearchParamsDto } from './dto/organization-invite-search-params.dto';
import { Public } from '../../../auth/decorators/public.decorator';
import { AuthService } from 'src/auth/services/auth.service';
import { OrganizationUserService } from '../organization-user/organization-user.service';
import { OrganizationUserRoles } from 'src/constants/role.constants';
import { Writable } from 'stream';
import { OrganizationInviteCsvService } from './organization-invite-csv.service';

@ApiTags('Organization Invite')
@ApiSecurity('Bearer Token')
@Controller('account-management')
export class OrganizationInviteController {
  constructor(
    private readonly authService: AuthService,
    private readonly organizationInviteService: OrganizationInviteService,
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationInviteCsvService: OrganizationInviteCsvService,
  ) {}

  /**
   * Public facing endpoint to retrieve an organization invite.
   * This endpoint needs to be public because a user might not be logged or not even have an account yet.
   * @param organizationId org id
   * @param inviteCode The invitation code
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization to invite users',
  })
  @ApiParam({
    name: 'inviteCode',
    description: 'The invitation code',
  })
  @Public()
  @Get('noauth/organization/:organizationId/invite/:inviteCode')
  async getOrganizationInviteByInviteCode(
    @Param('organizationId') organizationId: string,
    @Param('inviteCode') inviteCode: string,
    @Query('validationCode') validationCode: string,
  ) {
    return await this.organizationInviteService.getByInviteCode(
      organizationId,
      inviteCode,
      validationCode,
    );
  }

  /**
   * It bulk maps multiple users to multiple workspaces with a predefined role.
   * @param organizationId org id
   * @param createBulkUserWorkspaceDto
   * @returns
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @Permissions(createOrganizationBulkUserWorkspace)
  @Post('organization/:organizationId/invite')
  async bulkInviteMultipleUsersToMultipleWorkspaces(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() createBulkUserWorkspaceDto: CreateBulkUserWorkspaceDto,
  ) {
    const { userId } = req;

    return await this.organizationInviteService.bulkInviteMultipleUsersToMultipleWorkspaces(
      organizationId,
      createBulkUserWorkspaceDto,
      userId,
    );
  }

  /**
   * It returns a paginated response with all the organization pending invitations.
   * @param organizationId org id
   * @returns
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @Permissions(readOrganizationInvites)
  @Get('organization/:organizationId/invite')
  async getPendingInvites(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() searchParams: OrganizationInviteSearchParamsDto,
  ) {
    return await this.organizationInviteService.getPendingInvites(
      organizationId,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * Return a .csv file for all pending invites for the organization.
   * @param organizationId org id
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @Permissions(readOrganizationInvites)
  @Get('/organization/:organizationId/invite/download-csv')
  @Header('Content-Type', 'text/csv')
  @Header('Content-Disposition', 'attachment; filename="invites.csv"')
  async downloadInvitesCsv(
    @Param('organizationId') organizationId: string,
    @Res() res: Response,
  ) {
    const invites =
      await this.organizationInviteService.getAllPendingInvitesForCsv(
        organizationId,
      );

    const csvStream = this.organizationInviteCsvService.generateCsv(invites);
    csvStream.pipe(res as unknown as Writable);
  }

  /**
   * It sets the organization invite status as revoked.
   * @param organizationId org id
   * @param organizationInviteId organization invite id
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'organizationInviteId',
    type: UUID,
    description: 'Id of the Organization invite that will be revoked',
  })
  @Permissions(deleteOrganizationInvites)
  @Delete('organization/:organizationId/invite/:organizationInviteId')
  async cancelOrganizationInvite(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
  ) {
    const { userId } = req;

    const canUserCancelInvite = await this.isUserOrgAdminOrCreatorOfOrgInvite(
      organizationId,
      userId,
      organizationInviteId,
    );

    if (!canUserCancelInvite) {
      throw new ForbiddenException(
        'User is not allowed to cancel this organization invite',
      );
    }

    return await this.organizationInviteService.cancelOrganizationInvite(
      organizationId,
      organizationInviteId,
    );
  }

  /**
   * It resents the organization invite.
   * @param organizationId org id
   * @param organizationInviteId organization invite id
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'organizationInviteId',
    type: UUID,
    description: 'Id of the Organization invite that will be revoked',
  })
  @Permissions(updateOrganizationInvites)
  @Patch('organization/:organizationId/invite/:organizationInviteId')
  async resendOrganizationInvite(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
  ) {
    const { userId } = req;

    const canUserResendInvite = await this.isUserOrgAdminOrCreatorOfOrgInvite(
      organizationId,
      userId,
      organizationInviteId,
    );

    if (!canUserResendInvite) {
      throw new ForbiddenException(
        'User is not allowed to resend this organization invite',
      );
    }

    return await this.organizationInviteService.resendOrganizationInvite(
      organizationId,
      organizationInviteId,
    );
  }

  /**
   * It will handle the accept organization invite.
   * Since this request will come from an user which is not part of organization, there is no permissions we can check
   * @param organizationId org id
   * @param organizationInviteId organization invite id
   */
  @ApiParam({
    name: 'organizationId',
    type: UUID,
    description: 'Id of the Organization',
  })
  @ApiParam({
    name: 'organizationInviteId',
    type: UUID,
    description: 'Id of the Organization invite that will be revoked',
  })
  @Patch('organization/:organizationId/invite/:organizationInviteId/accept')
  async acceptOrganizationInvite(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('organizationInviteId') organizationInviteId: string,
  ) {
    const { userId } = req;

    return await this.organizationInviteService.acceptOrganizationInvite(
      organizationId,
      organizationInviteId,
      userId,
    );
  }

  private async isUserOrgAdminOrCreatorOfOrgInvite(
    organizationId: string,
    userId: number,
    organizationInviteId: string,
  ) {
    const isUserOrgAdmin = await this.isUserOrgAdmin(organizationId, userId);

    if (isUserOrgAdmin) return true;

    const isUserCreatorOfOrgInvite = await this.isUserCreatorOfOrgInvite(
      organizationId,
      organizationInviteId,
      userId,
    );

    return isUserCreatorOfOrgInvite;
  }

  private async isUserOrgAdmin(organizationId: string, userId: number) {
    const response = await this.organizationUserService.getUserInOrganization(
      organizationId,
      userId,
    );

    return response.result.roles.some(
      (role) => role.identifier === OrganizationUserRoles.ORG_ADMIN,
    );
  }

  private async isUserCreatorOfOrgInvite(
    organizationId: string,
    organizationInviteId: string,
    userId: number,
  ) {
    const response = await this.organizationInviteService.getInviteById(
      organizationId,
      organizationInviteId,
    );

    return response.result.invitedBy.id == userId;
  }
}
