import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationInviteService } from './organization-invite.service';
import {
  GetOrganizationInviteByInviteCodeAndValidationCode200Response,
  OrganizationInviteService as OrganizationInviteServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';

const TEST_ORG_ID = '1234';
const TEST_USER_ID = 1234;
const TEST_WORKSPACE_ID = 1234;

describe('OrganizationInviteService', () => {
  let service: OrganizationInviteService;
  let organizationInviteServiceSdk: OrganizationInviteServiceSdk;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationInviteService,
        {
          provide: OrganizationInviteServiceSdk,
          useValue: {
            bulkInviteUsersToWorkspacesAsPromise: jest.fn(),
            getPendingInvitesAsPromise: jest.fn(),
            cancelOrganizationInviteAsPromise: jest.fn(),
            resendOrganizationInviteAsPromise: jest.fn(),
            getOrganizationInviteByInviteCodeAndValidationCodeAsPromise:
              jest.fn(),
            acceptOrganizationInviteAsPromise: jest.fn(),
            getOrganizationInviteByIdAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<OrganizationInviteService>(OrganizationInviteService);
    organizationInviteServiceSdk = module.get<OrganizationInviteServiceSdk>(
      OrganizationInviteServiceSdk,
    );
  });

  afterEach(() => jest.restoreAllMocks());

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getInviteById', () => {
    const response = {
      status: 'OK',
      result: {
        id: 'xxxx',
        userEmail: '<EMAIL>',
        role: {
          id: 1,
          name: 'org admin',
          type: '',
          identifier: 'ORG_ADMIN',
          description: '',
        },
        lastUpdated: '',
        status: 'PENDING',
        invitedBy: {
          id: 1,
          username: '',
          firstName: '',
          lastName: '',
          displayName: '',
          email: '',
          jobTitle: '',
          photo: '',
        },
      },
    };

    it('should get the organization invite by id', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByIdAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        service.getInviteById(TEST_ORG_ID, 'wwwwww'),
      ).resolves.toStrictEqual(response);
    });
  });

  describe('getByInviteCode', () => {
    afterEach(() => jest.restoreAllMocks());

    const response = {
      status: '',
      result: {
        id: 'xxxx',
        organizationId: 'yyyy',
        email: '<EMAIL>',
        status: 'PENDING',
      },
    } as GetOrganizationInviteByInviteCodeAndValidationCode200Response;

    it('should get the organization invite by invite code', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByInviteCodeAndValidationCodeAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        service.getByInviteCode(TEST_ORG_ID, 'wwwwww', 'zzzzz'),
      ).resolves.toStrictEqual(response);
    });
  });

  describe('bulkInviteMultipleUsersToMultipleWorkspaces', () => {
    afterEach(() => jest.restoreAllMocks());

    const response = {
      status: 'Ok',
      result: {
        message: 'Successfully assigned user(s) to 2 workspace(s)',
        inactiveEmails: [],
        pendingInviteEmails: [],
      },
    };

    const createBulkUserWorkspaceDto = {
      roleId: 1,
      workspaces: [TEST_WORKSPACE_ID, 5678],
      users: ['<EMAIL>'],
    };

    it('should bulk invite users to multiple workspaces', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'bulkInviteUsersToWorkspacesAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        service.bulkInviteMultipleUsersToMultipleWorkspaces(
          TEST_ORG_ID,
          createBulkUserWorkspaceDto,
          TEST_USER_ID,
        ),
      ).resolves.toStrictEqual(response);
    });
  });

  describe('getPendingInvites', () => {
    const response = {
      status: 'OK',
      result: [
        {
          id: 'a1a950e8-9ac9-43d3-aeb5-a7bd4b25f4b9',
          status: 'PENDING',
          lastUpdated: '2023-12-20T14:36:48.000Z',
          invitedBy: {
            id: 51498,
            username: '<EMAIL>',
            firstName: 'Test',
            lastName: '1',
            displayName: 'Test 1',
            email: '<EMAIL>',
            jobTitle: '',
            photo: '',
          },
          role: {
            id: 35,
            name: 'Scoring Manager',
            type: 'business_entity',
            identifier: 'ADMIN_OVERRIDE_REQUEST_REVIEWER',
            description:
              'Can override Scoring results and assumes admin permissions.',
          },
          userEmail: '<EMAIL>',
        },
      ],
      pagination: {
        offset: 1,
        perPage: 1,
        nextOffset: 2,
        totalSize: 3,
      },
    };

    afterEach(() => jest.restoreAllMocks());

    it('should return a list of pending invites', async () => {
      jest
        .spyOn(organizationInviteServiceSdk, 'getPendingInvitesAsPromise')
        .mockResolvedValueOnce(response);

      await expect(
        service.getPendingInvites(TEST_ORG_ID, {}, {}),
      ).resolves.toBe(response);
    });
  });

  describe('cancelOrganizationInvite', () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Organization <NAME_EMAIL> canceled',
      },
    };

    it('should cancel an organization invite', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'cancelOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        service.cancelOrganizationInvite(TEST_ORG_ID, 'xxx'),
      ).resolves.toBe(response);
    });
  });

  describe('resendOrganizationInvite', () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Organization <NAME_EMAIL> resent',
      },
    };

    it('should resend an organization invite', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'resendOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        service.resendOrganizationInvite(TEST_ORG_ID, 'xxx'),
      ).resolves.toBe(response);
    });
  });

  describe('acceptOrganizationInvite', () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Organization invitation accepted successfully',
      },
    };

    it('should accept the organization invite', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'acceptOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        service.acceptOrganizationInvite(TEST_ORG_ID, 'xxx', 1234),
      ).resolves.toBe(response);
    });
  });
});
