import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationInviteController } from './organization-invite.controller';
import { OrganizationInviteService } from './organization-invite.service';
import {
  GetOrganizationInviteByInviteCodeAndValidationCode200Response,
  OrganizationInviteService as OrganizationInviteServiceSdk,
  OrganizationUserService as OrganizationUserServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { AuthService } from 'src/auth/services/auth.service';
import { ForbiddenException } from '@nestjs/common';
import { OrganizationUserService } from '../organization-user/organization-user.service';
import { OrganizationInviteCsvService } from './organization-invite-csv.service';

const TEST_ORG_ID = '1234';
const TEST_USER_ID = 1234;
const TEST_USER_AUTHORIZATION = 'bearer token';
const TEST_WORKSPACE_ID = 1234;
const TEST_ORG_INVITE_ID = '5678';

const orgAdminRole = {
  id: 32,
  name: 'Admin',
  type: 'organization_entity',
  identifier: 'ORG_ADMIN',
  description: '',
};

const orgStandardRole = {
  id: 31,
  name: 'Standard',
  type: 'organization_entity',
  identifier: 'ORG_Standard',
  description: '',
};

const userResponse = {
  status: 'OK',
  result: {
    id: TEST_USER_ID,
    username: '',
    firstName: '',
    lastName: '',
    displayName: '',
    email: '',
    jobTitle: '',
    photo: '',
  },
};

const orgAdminUserResponse = {
  ...userResponse,
  result: {
    ...userResponse.result,
    roles: [orgAdminRole],
    lastLoginDate: new Date().toISOString(),
  },
};

const orgStandardUserResponse = {
  ...userResponse,
  result: {
    ...userResponse.result,
    roles: [orgStandardRole],
    lastLoginDate: new Date().toISOString(),
  },
};

const organizationInviteResponse = {
  status: 'OK',
  result: {
    id: TEST_ORG_INVITE_ID,
    userEmail: '<EMAIL>',
    role: orgAdminRole,
    lastUpdated: '',
    status: 'PENDING',
    invitedBy: userResponse.result,
  },
};

describe('OrganizationInviteController', () => {
  let controller: OrganizationInviteController;
  let organizationInviteServiceSdk: OrganizationInviteServiceSdk;
  let organizationUserServiceSdk: OrganizationUserServiceSdk;
  let authService: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationInviteService,
        OrganizationUserService,
        OrganizationInviteCsvService,
        {
          provide: OrganizationInviteServiceSdk,
          useValue: {
            bulkInviteUsersToWorkspacesAsPromise: jest.fn(),
            getPendingInvitesAsPromise: jest.fn(),
            cancelOrganizationInviteAsPromise: jest.fn(),
            resendOrganizationInviteAsPromise: jest.fn(),
            getOrganizationInviteByInviteCodeAndValidationCodeAsPromise:
              jest.fn(),
            acceptOrganizationInviteAsPromise: jest.fn(),
            getOrganizationInviteByIdAsPromise: jest.fn(),
          },
        },
        {
          provide: OrganizationUserServiceSdk,
          useValue: {
            getUserAsPromise: jest.fn(),
          },
        },
        {
          provide: AuthService,
          useValue: {
            canAccessWorkspaceInvite: jest.fn(),
          },
        },
      ],
      controllers: [OrganizationInviteController],
    }).compile();

    controller = module.get<OrganizationInviteController>(
      OrganizationInviteController,
    );
    organizationInviteServiceSdk = module.get<OrganizationInviteServiceSdk>(
      OrganizationInviteServiceSdk,
    );
    organizationUserServiceSdk = module.get<OrganizationUserServiceSdk>(
      OrganizationUserServiceSdk,
    );
    authService = module.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getOrganizationInviteByInviteCode', () => {
    afterEach(() => jest.restoreAllMocks());

    const response = {
      status: 'Ok',
      result: {
        id: 'xxxx',
        organizationId: 'yyyy',
        email: '<EMAIL>',
        status: 'PENDING',
      },
    } as GetOrganizationInviteByInviteCodeAndValidationCode200Response;

    it('should get the organization invite by invite code', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByInviteCodeAndValidationCodeAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.getOrganizationInviteByInviteCode(
          TEST_ORG_ID,
          'wwwwww',
          'zzzzz',
        ),
      ).resolves.toStrictEqual(response);
    });
  });

  describe('bulkInviteMultipleUsersToMultipleWorkspaces', () => {
    afterEach(() => jest.restoreAllMocks());

    const response = {
      status: 'Ok',
      result: {
        message: 'Successfully assigned user(s) to 2 workspace(s)',
        inactiveEmails: [],
        pendingInviteEmails: [],
      },
    };

    const createBulkUserWorkspaceDto = {
      roleId: 1,
      workspaces: [TEST_WORKSPACE_ID, 5678],
      users: ['<EMAIL>'],
    };

    it('should bulk invite users to multiple workspaces', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'bulkInviteUsersToWorkspacesAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.bulkInviteMultipleUsersToMultipleWorkspaces(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          createBulkUserWorkspaceDto,
        ),
      ).resolves.toStrictEqual(response);
    });
  });

  describe('getPendingInvites', () => {
    const response = {
      status: 'OK',
      result: [
        {
          id: 'a1a950e8-9ac9-43d3-aeb5-a7bd4b25f4b9',
          status: 'PENDING',
          lastUpdated: '2023-12-20T14:36:48.000Z',
          invitedBy: {
            id: 51498,
            username: '<EMAIL>',
            firstName: 'Test',
            lastName: '1',
            displayName: 'Test 1',
            email: '<EMAIL>',
            jobTitle: '',
            photo: '',
          },
          role: {
            id: 35,
            name: 'Scoring Manager',
            type: 'business_entity',
            identifier: 'ADMIN_OVERRIDE_REQUEST_REVIEWER',
            description:
              'Can override Scoring results and assumes admin permissions.',
          },
          userEmail: '<EMAIL>',
        },
      ],
      pagination: {
        offset: 1,
        perPage: 1,
        nextOffset: 2,
        totalSize: 3,
      },
    };

    afterEach(() => jest.restoreAllMocks());

    it('should return a list of pending invites', async () => {
      jest
        .spyOn(organizationInviteServiceSdk, 'getPendingInvitesAsPromise')
        .mockResolvedValueOnce(response);

      await expect(
        controller.getPendingInvites(TEST_ORG_ID, {}, {}),
      ).resolves.toBe(response);
    });
  });

  describe('cancelOrganizationInvite', () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Organization <NAME_EMAIL> canceled',
      },
    };

    it('should cancel an organization invite as ORG ADMIN user', async () => {
      jest
        .spyOn(organizationUserServiceSdk, 'getUserAsPromise')
        .mockResolvedValueOnce(orgAdminUserResponse);
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'cancelOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.cancelOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          TEST_ORG_INVITE_ID,
        ),
      ).resolves.toBe(response);
    });

    it('should cancel an organization invite as ORG STANDARD and creator of org invite', async () => {
      jest
        .spyOn(organizationUserServiceSdk, 'getUserAsPromise')
        .mockResolvedValueOnce(orgStandardUserResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByIdAsPromise',
        )
        .mockResolvedValueOnce(organizationInviteResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'cancelOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.cancelOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          TEST_ORG_INVITE_ID,
        ),
      ).resolves.toBe(response);
    });

    it('should throw forbidden exception as ORG STANDARD and not the creator of org invite', async () => {
      jest
        .spyOn(organizationUserServiceSdk, 'getUserAsPromise')
        .mockResolvedValueOnce(orgStandardUserResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByIdAsPromise',
        )
        .mockResolvedValueOnce({
          ...organizationInviteResponse,
          result: {
            ...organizationInviteResponse.result,
            invitedBy: {
              ...organizationInviteResponse.result.invitedBy,
              id: 9,
            },
          },
        });

      await expect(
        controller.cancelOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          TEST_ORG_INVITE_ID,
        ),
      ).rejects.toThrow(
        new ForbiddenException(
          'User is not allowed to cancel this organization invite',
        ),
      );
    });
  });

  describe('resendOrganizationInvite', () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Organization <NAME_EMAIL> resent',
      },
    };

    it('should resend an organization invite as ORG ADMIN', async () => {
      jest
        .spyOn(organizationUserServiceSdk, 'getUserAsPromise')
        .mockResolvedValueOnce(orgAdminUserResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'resendOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.resendOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          TEST_ORG_INVITE_ID,
        ),
      ).resolves.toBe(response);
    });

    it('should resend an organization invite as ORG STANDARD and creator of org invite', async () => {
      jest
        .spyOn(organizationUserServiceSdk, 'getUserAsPromise')
        .mockResolvedValueOnce(orgStandardUserResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByIdAsPromise',
        )
        .mockResolvedValueOnce(organizationInviteResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'resendOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.resendOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          TEST_ORG_INVITE_ID,
        ),
      ).resolves.toBe(response);
    });

    it('should throw forbidden exception as ORG STANDARD and not the creator of org invite', async () => {
      jest
        .spyOn(organizationUserServiceSdk, 'getUserAsPromise')
        .mockResolvedValueOnce(orgStandardUserResponse);

      jest
        .spyOn(
          organizationInviteServiceSdk,
          'getOrganizationInviteByIdAsPromise',
        )
        .mockResolvedValueOnce({
          ...organizationInviteResponse,
          result: {
            ...organizationInviteResponse.result,
            invitedBy: {
              ...organizationInviteResponse.result.invitedBy,
              id: 9,
            },
          },
        });

      await expect(
        controller.resendOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          TEST_ORG_INVITE_ID,
        ),
      ).rejects.toThrow(
        new ForbiddenException(
          'User is not allowed to resend this organization invite',
        ),
      );
    });
  });

  describe('acceptOrganizationInvite', () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Organization invitation accepted successfully',
      },
    };

    it('should accept the organization invite', async () => {
      jest
        .spyOn(
          organizationInviteServiceSdk,
          'acceptOrganizationInviteAsPromise',
        )
        .mockResolvedValueOnce(response);

      await expect(
        controller.acceptOrganizationInvite(
          { userId: TEST_USER_ID },
          TEST_ORG_ID,
          'xxx',
        ),
      ).resolves.toBe(response);
    });
  });
});
