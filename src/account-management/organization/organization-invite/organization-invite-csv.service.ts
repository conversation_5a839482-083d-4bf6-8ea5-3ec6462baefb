import { ReadOrganizationInvitesDto } from '@vidmob/vidmob-organization-service-sdk';
import { Stream } from 'stream';
import { Injectable } from '@nestjs/common';
import * as fastCsv from 'fast-csv';

@Injectable()
export class OrganizationInviteCsvService {
  generateCsv(invites: ReadOrganizationInvitesDto[]): Stream {
    const csvStream = new Stream.PassThrough();
    const csvWriter = fastCsv.format({ headers: true });

    csvWriter.pipe(csvStream);

    invites.forEach((invite) => {
      csvWriter.write({
        ID: invite.id,
        Email: invite.userEmail,
        Role: invite.role.name,
        Status: invite.status,
        'Last Updated': invite.lastUpdated,
        'Invited By': invite.invitedBy.displayName,
      });
    });

    csvWriter.end();

    return csvStream;
  }
}
