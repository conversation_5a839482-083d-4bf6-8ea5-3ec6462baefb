import { Modu<PERSON> } from '@nestjs/common';
import { OrganizationInviteController } from './organization-invite.controller';
import { OrganizationInviteService } from './organization-invite.service';
import { AuthService } from 'src/auth/services/auth.service';
import { OrganizationUserService } from '../organization-user/organization-user.service';
import { OrganizationInviteCsvService } from './organization-invite-csv.service';

@Module({
  controllers: [OrganizationInviteController],
  providers: [
    OrganizationInviteService,
    AuthService,
    OrganizationUserService,
    OrganizationInviteCsvService,
  ],
})
export class OrganizationInviteModule {}
