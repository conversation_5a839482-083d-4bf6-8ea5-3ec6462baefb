import { Injectable } from '@nestjs/common';
import { CreateBulkUserWorkspaceDto } from './dto/create-bulk-user-workspace.dto';
import { OrganizationInviteService as OrganizationInviteServiceSdk } from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { OrganizationInviteSearchParamsDto } from './dto/organization-invite-search-params.dto';
@Injectable()
export class OrganizationInviteService {
  constructor(
    private readonly organizationInviteServiceSdk: OrganizationInviteServiceSdk,
  ) {}

  async getInviteById(organizationId: string, organizationInviteId: string) {
    return await this.organizationInviteServiceSdk.getOrganizationInviteByIdAsPromise(
      organizationId,
      organizationInviteId,
    );
  }

  async getByInviteCode(
    organizationId: string,
    inviteCode: string,
    validationCode: string,
  ) {
    return await this.organizationInviteServiceSdk.getOrganizationInviteByInviteCodeAndValidationCodeAsPromise(
      organizationId,
      inviteCode,
      validationCode,
    );
  }

  async bulkInviteMultipleUsersToMultipleWorkspaces(
    organizationId: string,
    createBulkUserWorkspaceDto: CreateBulkUserWorkspaceDto,
    inviterId: number,
  ) {
    const body = {
      ...createBulkUserWorkspaceDto,
      inviterId,
    };
    return await this.organizationInviteServiceSdk.bulkInviteUsersToWorkspacesAsPromise(
      organizationId,
      body,
    );
  }

  async getPendingInvites(
    organizationId: string,
    paginationOptions: PaginationOptions,
    searchParams: OrganizationInviteSearchParamsDto,
  ) {
    return await this.organizationInviteServiceSdk.getPendingInvitesAsPromise(
      organizationId,
      searchParams.search,
      searchParams.sortBy,
      searchParams.sortOrder,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async getAllPendingInvitesForCsv(organizationId: string) {
    const allInvites = [];
    let hasMore = true;
    let offset = 0;
    const limit = 100; // Fetch 100 records at a time

    while (hasMore) {
      const response =
        await this.organizationInviteServiceSdk.getPendingInvitesAsPromise(
          organizationId,
          undefined,
          undefined,
          undefined,
          offset,
          limit,
          '', // No queryId
        );

      allInvites.push(...response.result);

      offset += limit;
      hasMore = response.result.length === limit;
    }

    return allInvites;
  }

  async cancelOrganizationInvite(
    organizationId: string,
    organizationInviteId: string,
  ) {
    return await this.organizationInviteServiceSdk.cancelOrganizationInviteAsPromise(
      organizationId,
      organizationInviteId,
    );
  }

  async resendOrganizationInvite(
    organizationId: string,
    organizationInviteId: string,
  ) {
    return await this.organizationInviteServiceSdk.resendOrganizationInviteAsPromise(
      organizationId,
      organizationInviteId,
    );
  }

  async acceptOrganizationInvite(
    organizationId: string,
    organizationInviteId: string,
    inviteeId: number,
  ) {
    return await this.organizationInviteServiceSdk.acceptOrganizationInviteAsPromise(
      organizationId,
      organizationInviteId,
      { inviteeId },
    );
  }
}
