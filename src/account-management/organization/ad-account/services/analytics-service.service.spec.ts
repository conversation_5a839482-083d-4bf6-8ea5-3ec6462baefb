import { ConfigService } from '@nestjs/config';
import { Logger, UnauthorizedException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { AnalyticsService } from './analytics-service.service';
import axios from 'axios';
import { UpdateAccountPlatformPermissionDto } from '../entities/update-account-platform-permission.dto';
import { AnalyticsUserService } from '../../../../analytics/analytics-user-service/analytics-user-service';

const ANALYTICS_URL = 'http://localhost:3000';
const TEST_PLATFORM_ACCOUNT_ID = '**********';
const TEST_USER_ID = 123;
const TEST_ORGANIZATION_ID = '0e801e54-cdef-44f1-9bb0-ca7fb859e408';
const TEST_PLATFORM = 'MOCK';
const TEST_AUTHORIZATION = 'Bearer token';
const TEST_IAV3_PLATFORM = 'PINTEREST';

describe('AnalyticsService', () => {
  let analyticsService: AnalyticsService;
  let configService: ConfigService;
  let analyticsUserService: AnalyticsUserService;
  let originalLoggerError: any;

  const updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto =
    {
      platform: 'platform',
      permission: 'ALLOW',
      organizationId: TEST_ORGANIZATION_ID,
    };

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AnalyticsService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue(ANALYTICS_URL),
          },
        },
        {
          provide: AnalyticsUserService,
          useValue: {},
        },
      ],
    }).compile();

    configService = moduleRef.get<ConfigService>(ConfigService);
    analyticsService = moduleRef.get<AnalyticsService>(AnalyticsService);
    analyticsUserService =
      moduleRef.get<AnalyticsUserService>(AnalyticsUserService);

    originalLoggerError = Logger.prototype.error;
    Logger.prototype.error = jest.fn();
  });

  afterEach(() => {
    jest.restoreAllMocks();
    Logger.prototype.error = originalLoggerError;
  });

  it('should test updateAccountPermissionsViaAnalyticsService function', async () => {
    const analyticsServiceResponse = { result: 'Success' };
    jest.spyOn(axios, 'post').mockResolvedValue({
      data: analyticsServiceResponse,
    });

    const response =
      await analyticsService.updateAccountPermissionsViaAnalyticsService(
        TEST_PLATFORM_ACCOUNT_ID,
        TEST_AUTHORIZATION,
        updateAccountPlatformPermissionDto,
      );

    expect(response).toEqual(analyticsServiceResponse.result);
  });

  it('should test updateAccountPermissionsViaAnalyticsService throw an error', async () => {
    const error = new Error('Service unavailable');
    jest.spyOn(axios, 'post').mockRejectedValue(error);

    await expect(
      analyticsService.updateAccountPermissionsViaAnalyticsService(
        TEST_PLATFORM_ACCOUNT_ID,
        TEST_AUTHORIZATION,
        updateAccountPlatformPermissionDto,
      ),
    ).rejects.toEqual(
      new Error(
        `Unable to update the account permissions ${error} for ad account ${TEST_PLATFORM_ACCOUNT_ID} in Analytics Service.`,
      ),
    );
  });

  it('should throw an error if analytics service URL is not defined', async () => {
    jest.spyOn(configService, 'get').mockReturnValueOnce(undefined);

    await expect(
      analyticsService.updateAccountPermissionsViaAnalyticsService(
        TEST_PLATFORM_ACCOUNT_ID,
        TEST_AUTHORIZATION,
        updateAccountPlatformPermissionDto,
      ),
    ).rejects.toEqual(new Error('Analytics Service URL is not defined.'));
  });

  it('should test getPlatformAdAccountsWithPermissions function', async () => {
    const analyticsServiceResponse = { result: 'Success' };
    const axiosGetSpy = jest.spyOn(axios, 'get');
    axiosGetSpy.mockResolvedValue({
      data: analyticsServiceResponse,
    });

    const response =
      await analyticsService.getPlatformAdAccountsWithPermissions(
        TEST_USER_ID,
        TEST_PLATFORM,
        TEST_AUTHORIZATION,
      );

    expect(axiosGetSpy).toHaveBeenCalledWith(
      `${ANALYTICS_URL}/api/v1/user/${TEST_USER_ID}/platformPermission/${TEST_PLATFORM}/adAccount?useDBRecordAsSourceOfTruth=false`,
      expect.anything(),
    );
    expect(response).toEqual(analyticsServiceResponse.result);
  });

  it('should test getPlatformAdAccountsWithPermissions function for IA v3 platform', async () => {
    const analyticsServiceResponse = { result: 'Success' };
    const axiosGetSpy = jest.spyOn(axios, 'get');
    axiosGetSpy.mockResolvedValue({
      data: analyticsServiceResponse,
    });

    const response =
      await analyticsService.getPlatformAdAccountsWithPermissions(
        TEST_USER_ID,
        TEST_IAV3_PLATFORM,
        TEST_AUTHORIZATION,
      );

    expect(axiosGetSpy).toHaveBeenCalledWith(
      `${ANALYTICS_URL}/api/v1/platform/${TEST_IAV3_PLATFORM}/platform_ad_accounts`,
      expect.anything(),
    );
    expect(response).toEqual(analyticsServiceResponse.result);
  });

  it('should test getPlatformAdAccountsWithPermissions function for IA v3 platform with user permissions', async () => {
    const analyticsServiceResponse = { result: 'Success' };
    const axiosGetSpy = jest.spyOn(axios, 'get');
    axiosGetSpy.mockResolvedValue({
      data: analyticsServiceResponse,
    });

    const response =
      await analyticsService.getPlatformAdAccountsWithPermissions(
        TEST_USER_ID,
        TEST_IAV3_PLATFORM,
        TEST_AUTHORIZATION,
        'ALL',
      );

    expect(axiosGetSpy).toHaveBeenCalledWith(
      `${ANALYTICS_URL}/api/v1/platform/${TEST_IAV3_PLATFORM}/user/${TEST_USER_ID}/platform_ad_accounts`,
      expect.anything(),
    );
    expect(response).toEqual(analyticsServiceResponse.result);
  });

  it('should test getPlatformAdAccountsWithPermissions throw unauthorized exception', async () => {
    jest.spyOn(axios, 'get').mockRejectedValue({
      response: {
        status: 403,
      },
    });

    await expect(
      analyticsService.getPlatformAdAccountsWithPermissions(
        TEST_USER_ID,
        TEST_PLATFORM,
        TEST_AUTHORIZATION,
      ),
    ).rejects.toThrowError(
      new UnauthorizedException(
        `User ${TEST_USER_ID} is not authorized to access platform ${TEST_PLATFORM}`,
      ),
    );
  });

  it('should test getPlatformAdAccountsWithPermissions throw an error', async () => {
    const error = new Error('Service unavailable');
    jest.spyOn(axios, 'get').mockRejectedValue(error);

    await expect(
      analyticsService.getPlatformAdAccountsWithPermissions(
        TEST_USER_ID,
        TEST_PLATFORM,
        TEST_AUTHORIZATION,
      ),
    ).rejects.toThrowError(
      new Error(
        `Unable to get the user platform permissions for user Id ${TEST_USER_ID}, platform ${TEST_PLATFORM} from Analytics Service. Error - ${error}`,
      ),
    );
  });
});
