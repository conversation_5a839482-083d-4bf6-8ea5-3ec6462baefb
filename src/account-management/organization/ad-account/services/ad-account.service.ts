import {
  BadRequestException,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import {
  AdAccountService as OrganizationAdAccountService,
  OrganizationService,
  ReadAdAccountOrganizationsDto,
  ReadHealthDashboardAdAccountDto,
  ReadPlatformAdAccountWithAdditionalInfoDto,
} from '@vidmob/vidmob-organization-service-sdk';
import { UpdateAccountPlatformPermissionDto } from '../entities/update-account-platform-permission.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { AnalyticsService } from './analytics-service.service';
import { ReadAccountPlatformPermissionsDto } from '../entities/read-account-platform-permissions.dto';
import { AccountSearchParamsDto } from '../entities/ad-account-search-params.dto';
import { WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/workspaceAdAccount.service';
import { ENTITY_PERMISSIONS } from '../../../../constants/permission.constants';
import { CreateAdAccountBrandMapDto } from '../../workspace/dto/create-ad-account-brand-map.dto';
import { ReadAdAccountMapDto } from '../../workspace/dto/read-ad-account-map.dto';
import { CreateAdAccountMarketMapDto } from '../../workspace/dto/create-ad-account-market-map.dto';
import { UpdateBulkPlatformPermissionsDto } from '../entities/update-bulk-platform-permission.dto';
import { CreateIndustryAdAccountsRequestDto } from '../entities/create-industry-ad-accounts-request.dto';
import { UserAccessMode } from '../../constants/constants';
import { CreatePlatformAdAccountSequentialFailureDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createPlatformAdAccountSequentialFailureDto';
import { UpdatePlatformAdAccountRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/updatePlatformAdAccountRequestDto';

@Injectable()
export class AdAccountService {
  private readonly logger = new Logger(AdAccountService.name);
  private readonly DEFAULT_PERMISSION = ENTITY_PERMISSIONS.UNKNOWN_PERMISSION;
  private readonly ALLOW_PERMISSION = ENTITY_PERMISSIONS.ALLOW_PERMISSION;

  constructor(
    private readonly organizationService: OrganizationService,
    private readonly organizationAdAccountService: OrganizationAdAccountService,
    private readonly analyticsService: AnalyticsService,
    private readonly workspaceAdAccountServiceSDK: WorkspaceAdAccountServiceSDK,
  ) {}

  /**
   * Map the ad account to an organization.
   * @param adAccountId The ad account id.
   * @param userId The user id.
   * @param updateAccountPlatformPermissionDto The update account platform permission dto.
   */
  private async mapAdAccountToOrganization(
    adAccountId: string,
    userId: number,
    updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto,
  ) {
    const { permission, organizationId } = updateAccountPlatformPermissionDto;
    try {
      this.logger.log(
        `Mapping ad account ${adAccountId} to organization` +
          ` - ${updateAccountPlatformPermissionDto.organizationId}.`,
      );
      const createOrganizationPlatformAdAccountDto = {
        userId: userId,
        permission: permission,
        platformAccountId: adAccountId,
      };
      await this.organizationService.mapAdAccountToOrganizationAsPromise(
        organizationId,
        createOrganizationPlatformAdAccountDto,
      );
    } catch (e) {
      this.logger.error(`Error while mapping ad account to organization ${e}`);
      throw new Error(
        `Unable to map the ad account ${adAccountId} to organization id - ${organizationId}.`,
      );
    }
  }

  /**
   * Update platform permissions to ALLOW, if user have ALLOW permission on the account across organizations
   * The value is saved in person_platform_permissions table and used for imports purpose
   * @param adAccountId ad account id
   * @param userId user id
   * @param updateAccountPlatformPermissionDto update account platform permission dto
   * @return updated account platform permission dto if permission is ALLOW in any of the orgs
   */
  private async updatePermissionToSaveInPersonPlatformPermissions(
    adAccountId: string,
    userId: number,
    updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto,
  ) {
    const newUpdateAccountPlatformPermissionDto = {
      ...updateAccountPlatformPermissionDto,
    };
    if (
      updateAccountPlatformPermissionDto.permission !== this.ALLOW_PERMISSION
    ) {
      const accountPermissions =
        await this.organizationAdAccountService.getUserAccountPermissionsAcrossOrganizationsAsPromise(
          adAccountId,
          userId,
        );
      const currentOrgWithAllowCount = accountPermissions.result.filter(
        (accountPermission) =>
          accountPermission.organizationId !=
            updateAccountPlatformPermissionDto.organizationId &&
          accountPermission.permission == this.ALLOW_PERMISSION,
      ).length;
      if (currentOrgWithAllowCount > 0) {
        newUpdateAccountPlatformPermissionDto.permission =
          this.ALLOW_PERMISSION;
      }
    }
    return newUpdateAccountPlatformPermissionDto;
  }

  private hasMultipleOrgAssociationsWhenAccountConnectedInOrg(
    accountOrganizationIds: string[],
    organizationId: string,
  ) {
    return (
      accountOrganizationIds.includes(organizationId) &&
      accountOrganizationIds.length > 1
    );
  }

  private hasMultipleOrgAssociationsWhenAccountNotConnectedInOrg(
    accountOrganizationIds: string[],
    organizationId: string,
  ) {
    return (
      !accountOrganizationIds.includes(organizationId) &&
      accountOrganizationIds.length > 0
    );
  }

  private isAccountConnectedInOtherOrganizations(
    accountOrganizationIds: string[],
    organizationId: string,
  ) {
    return (
      this.hasMultipleOrgAssociationsWhenAccountConnectedInOrg(
        accountOrganizationIds,
        organizationId,
      ) ||
      this.hasMultipleOrgAssociationsWhenAccountNotConnectedInOrg(
        accountOrganizationIds,
        organizationId,
      )
    );
  }

  private async disconnectAccountsFromPersonalOrganizations(
    orgAdAccountMapping: ReadAdAccountOrganizationsDto[],
  ) {
    const removeAccountPersonalOrganizationsPromises = orgAdAccountMapping
      .filter(
        (accountOrgMapping) =>
          accountOrgMapping.personalOrganizationIds?.length > 0,
      )
      .map((accountOrgMapping) => {
        return this.organizationAdAccountService.removeAdAccountFromOrganizationsAsPromise(
          accountOrgMapping.platformAccountId,
          {
            organizationIds: accountOrgMapping.personalOrganizationIds,
          },
        );
      });
    await Promise.all(removeAccountPersonalOrganizationsPromises);
  }

  /**
   * Check for the unique ad account organization condition.
   * @param organizationId
   * @param adAccountIds
   * @private
   */
  private async validateUniqueAdAccountOrganization(
    organizationId: string,
    adAccountIds: string[],
  ) {
    const orgAdAccountMapping =
      await this.organizationAdAccountService.getAdAccountAssociatedOrganizationsAsPromise(
        {
          platformAccountIds: adAccountIds,
        },
      );
    await this.disconnectAccountsFromPersonalOrganizations(
      orgAdAccountMapping.result,
    );
    const accountOrgMappings = orgAdAccountMapping.result.reduce(
      (acc, accountOrgMapping) => {
        acc.set(
          accountOrgMapping.platformAccountId,
          accountOrgMapping.organizationIds,
        );
        return acc;
      },
      new Map<string, string[]>(),
    );
    accountOrgMappings.forEach((orgIds, accountId) => {
      if (this.isAccountConnectedInOtherOrganizations(orgIds, organizationId)) {
        throw new BadRequestException(
          `Ad account ${accountId} can only be associated with one organization. Current organizations: ${orgIds}`,
        );
      }
    });
    return true;
  }

  /**
   * Verify if the account id is present in the user accounts. Throw an Unauthorized Exception if not
   * @param userId user id
   * @param adAccountId ad account id
   * @param userAccounts user accounts
   */
  validateAdAccountIdsPresentInUserAccounts(
    userId: number,
    adAccountIds: string[],
    userAccounts: ReadAccountPlatformPermissionsDto[],
  ) {
    const userAuthorizedAccountsSet = new Set(
      userAccounts.map((account) => account.adAccountId),
    );

    const unauthorizedAccountIds = adAccountIds.filter(
      (adAccountId) => !userAuthorizedAccountsSet.has(adAccountId),
    );

    if (unauthorizedAccountIds.length > 0) {
      throw new UnauthorizedException(
        `User ${userId} is not authorized to access the ad account(s) ${unauthorizedAccountIds}.`,
      );
    }
  }

  /**
   * validate user access to the platform ad account.
   * @param adAccountId  ad account id
   * @param userId user id
   * @param platform platform
   * @param authorization authorization header
   */
  async validateUserAdAccountsAuthorization(
    adAccountIds: string[],
    userId: number,
    platform: string,
    authorization: string,
  ): Promise<void> {
    const userAuthorizedAdAccounts: ReadAccountPlatformPermissionsDto[] =
      await this.analyticsService.getPlatformAdAccountsWithPermissions(
        userId,
        platform,
        authorization,
      );
    if (!userAuthorizedAdAccounts) {
      throw new Error(
        `Unable to fetch accounts with permissions for user id ${userId} and platform ${platform}.`,
      );
    }
    this.validateAdAccountIdsPresentInUserAccounts(
      userId,
      adAccountIds,
      userAuthorizedAdAccounts,
    );
  }

  async updateDBAndAnalyticsServicePermissions(
    userId: number,
    authorization: string,
    adAccountId: string,
    updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto,
  ) {
    const newUpdateAccountPlatformPermissionDto =
      await this.updatePermissionToSaveInPersonPlatformPermissions(
        adAccountId,
        userId,
        updateAccountPlatformPermissionDto,
      );
    const result =
      await this.analyticsService.updateAccountPermissionsViaAnalyticsService(
        adAccountId,
        authorization,
        newUpdateAccountPlatformPermissionDto,
      );
    await this.mapAdAccountToOrganization(
      adAccountId,
      userId,
      updateAccountPlatformPermissionDto,
    );
    return result;
  }

  async updatePlatformPermissions(
    userId: number,
    authorization: string,
    organizationId: string,
    platform: string,
    updateBulkPlatformPermissionDto: UpdateBulkPlatformPermissionsDto,
  ) {
    this.logger.log(
      `Received a request to update platform permissions for user id ${userId} and platform ${platform}.`,
    );

    const adAccountIds = updateBulkPlatformPermissionDto.permissions.map(
      (permission) => permission.adAccountId,
    );

    // validate user has access to accounts (via AS)
    await this.validateUserAdAccountsAuthorization(
      adAccountIds,
      userId,
      platform,
      authorization,
    );

    // validate requested allowed accounts not connected to any other orgs
    const allowedAccountIds = updateBulkPlatformPermissionDto.permissions
      .filter((permission) => permission.permission === this.ALLOW_PERMISSION)
      .map((permission) => permission.adAccountId);
    if (allowedAccountIds.length > 0) {
      await this.validateUniqueAdAccountOrganization(
        organizationId,
        allowedAccountIds,
      );
    }

    const updates = updateBulkPlatformPermissionDto.permissions.map(
      (permission) => {
        const updateAccountPlatformPermissionDto = {
          permission: permission.permission,
          platform,
          organizationId,
        };
        return this.updateDBAndAnalyticsServicePermissions(
          userId,
          authorization,
          permission.adAccountId,
          updateAccountPlatformPermissionDto,
        );
      },
    );

    return await Promise.all(updates);
  }

  /**
   * Update the account platform permissions.
   * @param adAccountId  ad account id
   * @param userId user id
   * @param authorization authorization header
   * @param updateAccountPlatformPermissionDto update account platform permission dto
   */
  async updateAccountPlatformPermissions(
    adAccountId: string,
    userId: number,
    authorization: string,
    updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto,
  ) {
    this.logger.log(
      `Received a request to update platform account permissions for ad account id ${adAccountId} and user id ${userId}.`,
    );
    if (!updateAccountPlatformPermissionDto.organizationId) {
      throw new BadRequestException('Organization id is not provided.');
    }

    // validate user has access to accounts (via AS)
    await this.validateUserAdAccountsAuthorization(
      [adAccountId],
      userId,
      updateAccountPlatformPermissionDto.platform,
      authorization,
    );

    // validate requested allowed account not connected to any other orgs
    if (
      updateAccountPlatformPermissionDto.permission === this.ALLOW_PERMISSION
    ) {
      await this.validateUniqueAdAccountOrganization(
        updateAccountPlatformPermissionDto.organizationId,
        [adAccountId],
      );
    }

    return await this.updateDBAndAnalyticsServicePermissions(
      userId,
      authorization,
      adAccountId,
      updateAccountPlatformPermissionDto,
    );
  }

  /**
   * Find organizations by ad account id
   * @param adAccountId ad account id
   */
  async findOrganizationsByAdAccountId(adAccountId: string) {
    this.logger.log(`Finding organizations for ad account id ${adAccountId}`);
    const response =
      await this.organizationAdAccountService.findOrganizationsForAdAccountAsPromise(
        adAccountId,
      );
    return response.result;
  }

  /**
   * Find ad accounts by organization id
   * @param organizationId organization id
   * @param workspaceId workspace id
   * @param paginationOptions pagination options
   * @param searchParams
   */
  async findAdAccountsByOrganizationId(
    organizationId: string,
    workspaceId: number,
    paginationOptions: PaginationOptions,
    searchParams: AccountSearchParamsDto,
  ) {
    this.logger.log(
      `Finding ad accounts for organization Id ${organizationId}`,
    );
    const response =
      await this.organizationService.getConnectedAdAccountsForOrganizationAsPromise(
        organizationId,
        searchParams.search,
        searchParams.sortBy,
        searchParams.sortOrder,
        searchParams.brands,
        searchParams.markets,
        workspaceId,
        paginationOptions.offset,
        paginationOptions.perPage,
      );
    return new PaginatedResultArray<ReadPlatformAdAccountWithAdditionalInfoDto>(
      response.result,
      response.pagination?.totalSize,
    );
  }

  /**
   * Find all workspaces for an organization ad account.
   * @param adAccountId The ad account id.
   * @param organizationId The organization id.
   * @param paginationOptions The pagination options.
   */
  async findAllWorkspacesForOrganizationAdAccount(
    adAccountId: string,
    organizationId: string,
    paginationOptions: PaginationOptions,
  ) {
    return await this.organizationService.getWorkspacesForAdAccountAsPromise(
      organizationId,
      adAccountId,
      paginationOptions.offset,
      paginationOptions.perPage,
    );
  }

  private async getAdAccountAssociatedOrganizations(
    accountPermissions: ReadAccountPlatformPermissionsDto[],
  ) {
    if (accountPermissions.length === 0) {
      return [];
    }
    const accountOrganizations =
      await this.organizationAdAccountService.getAdAccountAssociatedOrganizationsAsPromise(
        {
          platformAccountIds: accountPermissions.map((dto) => dto.adAccountId),
        },
      );
    return accountOrganizations.result;
  }

  async addOrgLevelPermissionsToAdAccounts(
    organizationId: string,
    userId: number,
    platform: string,
    accountPermissions: ReadAccountPlatformPermissionsDto[],
  ) {
    const [userAccountOrgLevelPermissions, allAccountOrganizations] =
      await Promise.all([
        this.organizationService.getUserAccountPermissionsInOrganizationAsPromise(
          organizationId,
          platform,
          userId,
        ),
        this.getAdAccountAssociatedOrganizations(accountPermissions),
      ]);
    const accountPermissionMap = userAccountOrgLevelPermissions.result.reduce(
      (acc, accountPermission) => {
        acc.set(
          accountPermission.platformAccountId,
          accountPermission.permission,
        );
        return acc;
      },
      new Map<string, string>(),
    );
    const allAccountOrgMappings = allAccountOrganizations.reduce(
      (acc, accountOrgMapping) => {
        acc.set(
          accountOrgMapping.platformAccountId,
          accountOrgMapping.organizationIds,
        );
        return acc;
      },
      new Map<string, string[]>(),
    );
    accountPermissions.map((accountPermission) => {
      accountPermission.permission =
        accountPermissionMap.get(accountPermission.adAccountId) ||
        this.DEFAULT_PERMISSION;
      const accountOrgIds =
        allAccountOrgMappings.get(accountPermission.adAccountId) || [];
      accountPermission.connectedInOtherOrganizations =
        this.isAccountConnectedInOtherOrganizations(
          accountOrgIds,
          organizationId,
        );
    });
    return accountPermissions;
  }

  /**
   * Sync platform ad accounts to database asynchronously in the background.
   * @param organizationId organization id
   * @param platform platform
   * @param userId user id
   * @param accountDTOs account DTOs
   */
  async syncPlatformAdAccountsToDatabase(
    organizationId: string,
    platform: string,
    userId: number,
    accountDTOs: ReadAccountPlatformPermissionsDto[],
  ) {
    const accountIds = accountDTOs.map((accountDTO) => accountDTO.adAccountId);
    await this.organizationAdAccountService.syncPlatformAdAccountsToDatabaseAsPromise(
      organizationId,
      platform,
      userId,
      {
        platformAccountIds: accountIds,
      },
    );
  }

  /**
   * Get the accounts with platform permissions in organization.
   * @param organizationId  organization id
   * @param userId user id
   * @param platform platform
   * @param authorization authorization header
   * @param userAccessMode user access mode 'SELF' (regular users) or 'ALL' (role-reporting users)
   */
  async getAccountsWithPermissionsViaAnalyticsService(
    organizationId: string,
    userId: number,
    platform: string,
    authorization: string,
    userAccessMode: UserAccessMode = 'SELF',
  ) {
    this.logger.log(
      `Received a request to get accounts with permissions for organization id ${organizationId}, ` +
        `user id ${userId} and platform: ${platform}.`,
    );
    if (!organizationId) {
      throw new BadRequestException('Organization id is not provided.');
    }
    const response: ReadAccountPlatformPermissionsDto[] =
      await this.analyticsService.getPlatformAdAccountsWithPermissions(
        userId,
        platform,
        authorization,
        userAccessMode,
      );
    if (!response) {
      if (
        platform?.toUpperCase() === 'REDDIT' ||
        platform?.toUpperCase() === 'PINTEREST'
      ) {
        this.logger.warn(
          `Unable to fetch accounts with permissions for organization id ${organizationId}, ` +
            `user id ${userId} and platform: ${platform}.`,
        );
        return new PaginatedResultArray<ReadAccountPlatformPermissionsDto>(
          [],
          0,
        );
      }
      throw new Error(
        `Unable to fetch accounts with permissions for organization id ${organizationId}, ` +
          `user id ${userId} and platform: ${platform}.`,
      );
    } else {
      await this.syncPlatformAdAccountsToDatabase(
        organizationId,
        platform,
        userId,
        response,
      );
      const finalResponse = await this.addOrgLevelPermissionsToAdAccounts(
        organizationId,
        userId,
        platform,
        response,
      );
      return new PaginatedResultArray<ReadAccountPlatformPermissionsDto>(
        finalResponse,
        finalResponse.length,
      );
    }
  }

  /**
   * Get the ad accounts details for health dashboard in organization.
   * @param organizationId
   * @param userId
   * @param searchParams
   * @param paginationOptions
   */
  async getOrganizationAdAccountsListForHealthDashboard(
    organizationId: string,
    userId: number,
    searchParams: AccountSearchParamsDto,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadHealthDashboardAdAccountDto>> {
    this.logger.log(
      `Fetching ad account health dashboard data for user ${userId} in organization ` +
        `- ${organizationId}, Search Params - ${searchParams}, Pagination Options - ${paginationOptions}`,
    );
    const response =
      await this.organizationService.getOrganizationAdAccountsListForHealthDashBoardAsPromise(
        organizationId,
        userId,
        searchParams.importStatus,
        searchParams.connectionStatus,
        searchParams.sortBy,
        searchParams.workspaces,
        searchParams.search,
        searchParams.sortOrder,
        searchParams.brands,
        searchParams.markets,
        paginationOptions.offset,
        paginationOptions.perPage,
      );
    return new PaginatedResultArray<ReadHealthDashboardAdAccountDto>(
      response.result,
      response.pagination?.totalSize,
    );
  }

  /**
   * Get the ad accounts details for health dashboard in workspace.
   * @param organizationId
   * @param workspaceId
   * @param userId
   * @param searchParams
   * @param paginationOptions
   */
  async getWorkspaceAdAccountsListForHealthDashboard(
    organizationId: string,
    workspaceId: number,
    userId: number,
    searchParams: AccountSearchParamsDto,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<ReadHealthDashboardAdAccountDto>> {
    this.logger.log(
      `Fetching ad account health dashboard data for user ${userId} in workspace ` +
        `- ${workspaceId}, Search Params - ${searchParams}, Pagination Options - ${paginationOptions}`,
    );
    const response =
      await this.organizationService.getWorkspaceAdAccountsListForHealthDashBoardAsPromise(
        organizationId,
        workspaceId,
        userId,
        searchParams.importStatus,
        searchParams.connectionStatus,
        searchParams.sortBy,
        searchParams.workspaces,
        searchParams.search,
        searchParams.sortOrder,
        searchParams.brands,
        searchParams.markets,
        paginationOptions.offset,
        paginationOptions.perPage,
      );
    return new PaginatedResultArray<ReadHealthDashboardAdAccountDto>(
      response.result,
      response.pagination?.totalSize,
    );
  }

  async fetchAdAccountsForWorkspacesAndPlatform(
    workspaces: number[],
    platform: string,
    paginationOptions: PaginationOptions,
  ) {
    return this.workspaceAdAccountServiceSDK.getConnectedAdAccountsForWorkspacesAndPlatformAsPromise(
      {
        workspaces,
        platform,
      },
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async createAdAccountBrandMap(
    organizationId: string,
    workspaceId: number,
    request: CreateAdAccountBrandMapDto,
  ): Promise<ReadAdAccountMapDto> {
    const response =
      await this.organizationService.createBulkMapBetweenAdAccountAndBrandsAsPromise(
        organizationId,
        workspaceId,
        request,
      );

    return response.result;
  }

  async createAdAccountMarketMap(
    organizationId: string,
    workspaceId: number,
    request: CreateAdAccountMarketMapDto,
  ) {
    const response =
      await this.organizationService.createBulkMapBetweenAdAccountAndMarketsAsPromise(
        organizationId,
        workspaceId,
        request,
      );

    return response.result;
  }

  async getPlatformAdAccountBrands(
    organizationId: string,
    workspaceId: number,
    adAccountId: string,
  ) {
    return await this.organizationService.getOrganizationAdAccountBrandsAsPromise(
      adAccountId,
      organizationId,
      workspaceId,
    );
  }

  async getBrands(
    organizationId: string,
    workspaceIds: number[],
    adAccountIds: string[],
  ) {
    const filter = {
      workspaceIds: workspaceIds,
      adAccountIds: adAccountIds,
    };
    return this.organizationService.getFilteredBrandsAsPromise(
      organizationId,
      filter,
    );
  }

  async getPlatformAdAccountMarkets(
    organizationId: string,
    workspaceId: number,
    adAccountId: string,
  ) {
    return await this.organizationService.getOrganizationAdAccountMarketsAsPromise(
      adAccountId,
      organizationId,
      workspaceId,
    );
  }

  async getPlatformAdAccountMarketsFromWorkspaces(
    workspaceIds: number[],
    paginationOptions: PaginationOptions,
  ) {
    return await this.organizationService.getAdAccountMarketsByWorkspacesAsPromise(
      {
        workspaceIds,
      },
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async mapIndustryGroupToAdAccounts(
    organizationId: string,
    userId: number,
    body: CreateIndustryAdAccountsRequestDto,
  ) {
    return await this.organizationService.mapIndustryGroupToBulkAdAccountsAsPromise(
      organizationId,
      userId,
      body,
    );
  }

  async mapIndustryToAdAccounts(
    organizationId: string,
    userId: number,
    industryGroupId: number,
    body: CreateIndustryAdAccountsRequestDto,
  ) {
    return await this.organizationService.mapIndustryToBulkAdAccountsAsPromise(
      organizationId,
      userId,
      industryGroupId,
      body,
    );
  }

  async mapSubIndustryToAdAccounts(
    organizationId: string,
    userId: number,
    industryId: number,
    body: CreateIndustryAdAccountsRequestDto,
  ) {
    return await this.organizationService.mapSubIndustryToBulkAdAccountsAsPromise(
      organizationId,
      userId,
      industryId,
      body,
    );
  }

  /**
   * Get Organization Users Map per platform
   * @param platform - The Platform Name
   * @param importV3EnabledOnly - Import V3 Enabled Only
   * @returns The Organization Users Map
   */
  async getOrganizationUsersPerPlatform(
    platform: string,
    importV3EnabledOnly: boolean,
  ) {
    this.logger.log(`Loading all organization users for platform ${platform}.`);
    const response =
      await this.organizationAdAccountService.getOrganizationUsersPerPlatformAsPromise(
        platform,
        importV3EnabledOnly,
        true,
      );
    return response.result;
  }

  /**
   * Get ad account information along with import info
   * @deprecated Use getOrgPlatformAdAccountWithImportInfo instead
   * @param platform platform name
   * @param adAccountId ad account id
   */
  async getPlatformAdAccountWithImportInfo(
    platform: string,
    adAccountId: string,
  ) {
    this.logger.log(
      `Fetching ad account details with import info for ad account ${adAccountId} in platform ${platform}`,
    );
    const response =
      await this.organizationAdAccountService.getPlatformAdAccountWithImportInfoAsPromise(
        platform,
        adAccountId,
      );
    return response.result;
  }

  /**
   * Get organization ad account information along with import info
   * @param organizationId organization id
   * @param platform platform name
   * @param adAccountId ad account id
   */
  async getOrgPlatformAdAccountWithImportInfo(
    organizationId: string,
    platform: string,
    adAccountId: string,
  ) {
    this.logger.log(
      `Fetching ad account details with import info for ad account ${adAccountId} in platform ${platform}, organization ${organizationId}`,
    );
    const response =
      await this.organizationAdAccountService.getOrgPlatformAdAccountWithImportInfoAsPromise(
        organizationId,
        platform,
        adAccountId,
      );
    return response.result;
  }

  /**
   * Save platform ad account sequential failure record
   * @deprecated the logic will be handled in update account import data endpoint
   * @param createPlatformAdAccountSequentialFailureDto The sequential failure DTO
   */
  async savePlatformAdAccountSequentialFailure(
    adAccountId: string,
    createPlatformAdAccountSequentialFailureDto: CreatePlatformAdAccountSequentialFailureDto,
  ) {
    const response =
      await this.organizationAdAccountService.savePlatformAdAccountSequentialFailureAsPromise(
        adAccountId,
        createPlatformAdAccountSequentialFailureDto,
      );
    return response.result;
  }

  /**
   * Delete all sequential failures for a platform ad account
   * @deprecated the logic will be handled in update account import data endpoint
   * @param adAccountId The ad account id
   */
  async deleteSequentialFailuresByPlatformAdAccountId(adAccountId: string) {
    const response =
      await this.organizationAdAccountService.deleteSequentialFailuresByPlatformAdAccountIdAsPromise(
        adAccountId,
      );
    return response.result;
  }

  /**
   * Update ad account with the given parameters
   * @param organizationId The organization id
   * @param platform The platform name
   * @param adAccountId The ad account id
   * @param updateDto The update DTO request
   * @returns The updated platform ad account
   */
  async updatePlatformAdAccount(
    organizationId: string,
    platform: string,
    adAccountId: string,
    updateDto: UpdatePlatformAdAccountRequestDto,
  ) {
    const response =
      await this.organizationAdAccountService.updatePlatformAdAccountAsPromise(
        organizationId,
        platform,
        adAccountId,
        updateDto,
      );
    return response.result;
  }

  async getAvailableFilters() {
    return await this.organizationAdAccountService.getAvailableFiltersForAdAccountsAsPromise();
  }
}
