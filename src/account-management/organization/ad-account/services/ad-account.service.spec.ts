import { Test } from '@nestjs/testing';
import {
  BadRequestException,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { AdAccountService } from './ad-account.service';
import {
  OrganizationService,
  AdAccountService as OrganizationAdAccountService,
  WorkspaceAdAccountService,
} from '@vidmob/vidmob-organization-service-sdk';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';
import { UpdateAccountPlatformPermissionDto } from '../entities/update-account-platform-permission.dto';
import {
  AuthorizationService,
  CanAccess200Response,
  CanAccessPermissionsResponseDtoDomainId,
} from '@vidmob/vidmob-authorization-service-sdk';
import { Observable } from 'rxjs';
import { AnalyticsService } from './analytics-service.service';
import { ReadAccountPlatformPermissionsDto } from '../entities/read-account-platform-permissions.dto';
import { ENTITY_PERMISSIONS } from '../../../../constants/permission.constants';
import { ReadAdAccountMarketsDto } from '../../workspace/dto/read-ad-account-markets.dto';
import { CreatePlatformAdAccountSequentialFailureDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createPlatformAdAccountSequentialFailureDto';

jest.mock('axios');

const TEST_ORGANIZATION_ID = '0e801e54-cdef-44f1-9bb0-ca7fb859e408';
const TEST_SECOND_ORGANIZATION_ID = '6a6e7350-0cbb-449e-8126-de867769829d';

const TEST_WORKSPACE_ID = 123;
const TEST_USER_ID = 123;
const TEST_PLATFORM_ACCOUNT_ID = '**********';
const TEST_AUTHORIZATION = 'bearer token';
const ANALYTICS_URL = 'http://localhost:3000';
const TEST_PLATFORM = 'facebook';

describe('AdAccountService', () => {
  let adAccountService: AdAccountService;
  let organizationService: OrganizationService;
  let organizationAdAccountService: OrganizationAdAccountService;
  let configService: ConfigService;
  let authService: AuthorizationService;
  let analyticsService: AnalyticsService;
  const updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto =
    {
      platform: 'platform',
      permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
      organizationId: TEST_ORGANIZATION_ID,
    };
  const accountsPermissionsResponse: ReadAccountPlatformPermissionsDto[] = [
    {
      platform: TEST_PLATFORM,
      adAccountName: 'adAccountName',
      adAccountId: TEST_PLATFORM_ACCOUNT_ID,
      dateCreated: new Date(),
      permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
      canAccess: true,
      processingComplete: true,
      connectedInOtherOrganizations: false,
      importPriority: 0,
    },
  ];

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AdAccountService,
        AnalyticsService,
        {
          provide: OrganizationService,
          useValue: {
            mapAdAccountToOrganizationAsPromise: jest.fn(),
            getUserAccountPermissionsInOrganizationAsPromise: jest.fn(),
            createBulkMapBetweenAdAccountAndBrandsAsPromise: jest.fn(),
            createBulkMapBetweenAdAccountAndMarketsAsPromise: jest.fn(),
            getOrganizationAdAccountBrandsAsPromise: jest.fn(),
            getOrganizationAdAccountMarketsAsPromise: jest.fn(),
            mapIndustryGroupToBulkAdAccountsAsPromise: jest.fn(),
            mapIndustryToBulkAdAccountsAsPromise: jest.fn(),
            mapSubIndustryToBulkAdAccountsAsPromise: jest.fn(),
          },
        },
        {
          provide: OrganizationAdAccountService,
          useValue: {
            findOrganizationsForAdAccountAsPromise: jest.fn(),
            getOrganizationUsersPerPlatformAsPromise: jest.fn(),
            getOrgPlatformAdAccountWithImportInfoAsPromise: jest.fn(),
            savePlatformAdAccountSequentialFailureAsPromise: jest.fn(),
            deleteSequentialFailuresByPlatformAdAccountIdAsPromise: jest.fn(),
            getUserAccountPermissionsAcrossOrganizationsAsPromise: jest
              .fn()
              .mockResolvedValue({
                result: [
                  {
                    organizationId: TEST_ORGANIZATION_ID,
                    platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
                    userId: 1234,
                    permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
                  },
                ],
              }),
            getAdAccountAssociatedOrganizationsAsPromise: jest
              .fn()
              .mockResolvedValue({
                result: [
                  {
                    platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
                    organizationIds: [TEST_ORGANIZATION_ID],
                  },
                ],
              }),
            syncPlatformAdAccountsToDatabaseAsPromise: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: AuthorizationService,
          useValue: {
            canAccess: jest.fn(),
          },
        },
        {
          provide: WorkspaceAdAccountService,
          useValue: {
            getConnectedAdAccountsForWorkspacesAndPlatformAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    adAccountService = moduleRef.get<AdAccountService>(AdAccountService);
    organizationService =
      moduleRef.get<OrganizationService>(OrganizationService);
    organizationAdAccountService = moduleRef.get<OrganizationAdAccountService>(
      OrganizationAdAccountService,
    );
    configService = moduleRef.get<ConfigService>(ConfigService);
    authService = moduleRef.get<AuthorizationService>(AuthorizationService);
    analyticsService = moduleRef.get<AnalyticsService>(AnalyticsService);
    jest.spyOn(authService, 'canAccess').mockReturnValue(
      new Observable<AxiosResponse<CanAccess200Response>>((subscriber) => {
        const domainIdResponse =
          TEST_ORGANIZATION_ID as CanAccessPermissionsResponseDtoDomainId;
        subscriber.next({
          data: {
            status: 'OK',
            result: { domainId: domainIdResponse, canAccess: true },
          },
          status: 200,
          statusText: 'OK',
          headers: {} as any,
          config: {} as any,
          request: {} as any,
        });
        subscriber.complete();
      }),
    );
    Logger.prototype.error = jest.fn();
  });

  describe('updateAccountPlatformPermissions', () => {
    it('should update account platform permissions and map ad account to organization', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      const analyticsServiceResponse = { result: 'Success' };
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);
      (axios.post as jest.Mock).mockResolvedValue({
        data: analyticsServiceResponse,
      });

      await expect(
        adAccountService.updateAccountPlatformPermissions(
          TEST_PLATFORM_ACCOUNT_ID,
          TEST_USER_ID,
          TEST_AUTHORIZATION,
          updateAccountPlatformPermissionDto,
        ),
      ).resolves.toEqual(analyticsServiceResponse.result);

      expect(configService.get).toHaveBeenCalledWith(
        'legacyAnalyticsServiceUrl',
      );
      expect(axios.post).toHaveBeenCalledWith(
        `${ANALYTICS_URL}/api/v1/platformPermission/platform/adAccount/${TEST_PLATFORM_ACCOUNT_ID}`,
        { permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION },
        {
          headers: {
            Authorization: TEST_AUTHORIZATION,
            'Content-Type': 'application/json',
          },
        },
      );

      expect(
        organizationService.mapAdAccountToOrganizationAsPromise,
      ).toHaveBeenCalledWith(TEST_ORGANIZATION_ID, {
        userId: TEST_USER_ID,
        permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
        platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
      });
    });

    it('should update account platform permissions when ad account organizations count is null', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      const analyticsServiceResponse = { result: 'Success' };
      (axios.post as jest.Mock).mockResolvedValue({
        data: analyticsServiceResponse,
      });
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);
      jest
        .spyOn(
          organizationAdAccountService,
          'getAdAccountAssociatedOrganizationsAsPromise',
        )
        .mockResolvedValue({
          result: [
            {
              platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
              organizationIds: [],
              personalOrganizationIds: [],
            },
          ],
          status: '200',
        });

      await expect(
        adAccountService.updateAccountPlatformPermissions(
          TEST_PLATFORM_ACCOUNT_ID,
          TEST_USER_ID,
          TEST_AUTHORIZATION,
          {
            organizationId: TEST_ORGANIZATION_ID,
            platform: 'platform',
            permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
          },
        ),
      ).resolves.toEqual(analyticsServiceResponse.result);

      expect(configService.get).toHaveBeenCalledWith(
        'legacyAnalyticsServiceUrl',
      );

      expect(
        organizationService.mapAdAccountToOrganizationAsPromise,
      ).toBeCalledTimes(1);
    });

    it('should throw an error if mapping ad account to organization fails', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      const analyticsServiceResponse = { result: 'Success' };
      (axios.post as jest.Mock).mockResolvedValue({
        data: analyticsServiceResponse,
      });
      jest
        .spyOn(organizationService, 'mapAdAccountToOrganizationAsPromise')
        .mockImplementationOnce(() => {
          throw new Error('Failed to map ad account');
        });
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);

      await expect(
        adAccountService.updateAccountPlatformPermissions(
          TEST_PLATFORM_ACCOUNT_ID,
          TEST_USER_ID,
          TEST_AUTHORIZATION,
          updateAccountPlatformPermissionDto,
        ),
      ).rejects.toThrowError(
        `Unable to map the ad account ${TEST_PLATFORM_ACCOUNT_ID} to organization id - ${TEST_ORGANIZATION_ID}.`,
      );

      expect(
        organizationService.mapAdAccountToOrganizationAsPromise,
      ).toHaveBeenCalledWith(TEST_ORGANIZATION_ID, {
        userId: TEST_USER_ID,
        permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
        platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
      });
    });

    it('should change account permissions when more than 1 org has ALLOW value', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      const analyticsServiceResponse = { result: 'Success' };
      const postSpy = jest.spyOn(axios, 'post');
      postSpy.mockResolvedValue({
        data: analyticsServiceResponse,
      });
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);
      const updateAdAccountPermissionsDto = {
        organizationId: TEST_SECOND_ORGANIZATION_ID,
        platform: 'platform',
        permission: ENTITY_PERMISSIONS.DENY_PERMISSION,
      };

      await expect(
        adAccountService.updateAccountPlatformPermissions(
          TEST_PLATFORM_ACCOUNT_ID,
          TEST_USER_ID,
          TEST_AUTHORIZATION,
          updateAdAccountPermissionsDto,
        ),
      ).resolves.toEqual(analyticsServiceResponse.result);

      expect(postSpy).toHaveBeenCalledWith(
        `http://localhost:3000/api/v1/platformPermission/platform/adAccount/${TEST_PLATFORM_ACCOUNT_ID}`,
        { permission: ENTITY_PERMISSIONS.ALLOW_PERMISSION },
        {
          headers: {
            Authorization: 'bearer token',
            'Content-Type': 'application/json',
          },
        },
      );

      expect(
        organizationService.mapAdAccountToOrganizationAsPromise,
      ).toHaveBeenCalledWith(TEST_SECOND_ORGANIZATION_ID, {
        userId: TEST_USER_ID,
        permission: updateAdAccountPermissionsDto.permission,
        platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
      });
    });
  });

  it('should list organization for an account id', async () => {
    const organizationAdAccountServiceSpy = jest.spyOn(
      organizationAdAccountService,
      'findOrganizationsForAdAccountAsPromise',
    );
    organizationAdAccountServiceSpy.mockResolvedValueOnce({
      status: 'OK',
      result: [
        {
          id: '0e801e54-cdef-44f1-9bb0-ca7fb859e408',
          name: 'Test Organization',
          status: 'ENABLED',
          dateCreated: '2023-07-19T01:15:32.000Z',
          lastUpdated: '2023-07-19T01:15:32.000Z',
          workspaces: [],
        },
      ],
    });
    await adAccountService.findOrganizationsByAdAccountId(
      TEST_PLATFORM_ACCOUNT_ID,
    );
    expect(organizationAdAccountServiceSpy).toHaveBeenCalledWith(
      TEST_PLATFORM_ACCOUNT_ID,
    );
  });

  describe('Test getAccountsWithPermissionsViaAnalyticsService method', () => {
    it('should throw an error if analytics service call fails', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(undefined);

      await expect(
        adAccountService.getAccountsWithPermissionsViaAnalyticsService(
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        ),
      ).rejects.toThrowError(
        `Unable to fetch accounts with permissions for organization id ${TEST_ORGANIZATION_ID}, ` +
          `user id ${TEST_USER_ID} and platform: ${TEST_PLATFORM}.`,
      );
      expect(
        organizationService.getUserAccountPermissionsInOrganizationAsPromise,
      ).not.toHaveBeenCalled();
    });

    it('should return analytics response when org id is null', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);

      await expect(
        adAccountService.getAccountsWithPermissionsViaAnalyticsService(
          '',
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        ),
      ).rejects.toThrow(BadRequestException);
    });

    it('should add default org level permissions to ad account when no permissions exists', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);
      jest
        .spyOn(
          organizationService,
          'getUserAccountPermissionsInOrganizationAsPromise',
        )
        .mockResolvedValueOnce({
          status: 'OK',
          result: [],
        });

      const response =
        await adAccountService.getAccountsWithPermissionsViaAnalyticsService(
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        );
      expect(response.items[0].permission).toEqual(
        ENTITY_PERMISSIONS.UNKNOWN_PERMISSION,
      );
      expect(response.items[0].adAccountId).toEqual(TEST_PLATFORM_ACCOUNT_ID);
    });

    it('should add org level permissions to ad account when permissions exists', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);
      jest
        .spyOn(
          organizationService,
          'getUserAccountPermissionsInOrganizationAsPromise',
        )
        .mockResolvedValueOnce({
          status: 'OK',
          result: [
            {
              platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
              permission: ENTITY_PERMISSIONS.DENY_PERMISSION,
            },
          ],
        });

      const response =
        await adAccountService.getAccountsWithPermissionsViaAnalyticsService(
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        );
      expect(response.items[0].permission).toEqual(
        ENTITY_PERMISSIONS.DENY_PERMISSION,
      );
      expect(response.items[0].adAccountId).toEqual(TEST_PLATFORM_ACCOUNT_ID);
    });
  });

  describe('createAdAccountBrandMap', () => {
    it('should create a 2 ad account mapping brands', async () => {
      jest
        .spyOn(
          organizationService,
          'createBulkMapBetweenAdAccountAndBrandsAsPromise',
        )
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            message:
              'Selected brands successfully added/removed from to/from 2 ad account(s).',
          },
        });

      const response = await adAccountService.createAdAccountBrandMap(
        TEST_ORGANIZATION_ID,
        TEST_WORKSPACE_ID,
        {
          accounts: ['act_123', 'act_456'],
          selected_brands: ['123', '456'],
          unselected_brands: ['789'],
        },
      );

      expect(response).toEqual({
        message:
          'Selected brands successfully added/removed from to/from 2 ad account(s).',
      });
    });
  });

  describe('createAdAccountMarketMap', () => {
    it('should create a 2 ad account mapping markets', async () => {
      jest
        .spyOn(
          organizationService,
          'createBulkMapBetweenAdAccountAndMarketsAsPromise',
        )
        .mockResolvedValueOnce({
          status: 'OK',
          result: {
            message:
              'Selected markets successfully added/removed from to/from 2 ad account(s).',
          },
        });

      const response = await adAccountService.createAdAccountMarketMap(
        TEST_ORGANIZATION_ID,
        TEST_WORKSPACE_ID,
        {
          accounts: ['act_123', 'act_456'],
          selected_markets: ['123', '456'],
          unselected_markets: ['789'],
        },
      );

      expect(response).toEqual({
        message:
          'Selected markets successfully added/removed from to/from 2 ad account(s).',
      });
    });
  });

  describe('getPlatformAdAccountBrands', () => {
    const response = {
      status: 'OK',
      result: {
        brands: [
          {
            id: '7534120d-2f8f-4ba8-98b8-35cfd7906907',
            name: 'Awesome! Its Updated',
          },
          {
            id: 'df226c28-b078-42f5-91b1-f502e5965133',
            name: 'Ben Nye',
          },
        ],
      },
    };

    it('should return the brands for the ad account', async () => {
      jest
        .spyOn(organizationService, 'getOrganizationAdAccountBrandsAsPromise')
        .mockResolvedValueOnce(response as any);

      await expect(
        adAccountService.getPlatformAdAccountBrands(
          TEST_ORGANIZATION_ID,
          TEST_WORKSPACE_ID,
          TEST_PLATFORM_ACCOUNT_ID,
        ),
      ).resolves.toEqual(response);
    });
  });

  describe('getPlatformAdAccountBrands', () => {
    const markets = {
      markets: [
        {
          isoCode: 'usa',
          name: 'United States',
        },
        {
          isoCode: 'bra',
          name: 'Brazil',
        },
      ],
    } as ReadAdAccountMarketsDto;

    const response = {
      status: 'OK',
      result: {
        markets,
      },
    };

    it('should return the markets for the ad account', async () => {
      jest
        .spyOn(organizationService, 'getOrganizationAdAccountMarketsAsPromise')
        .mockResolvedValueOnce(response as any);

      await expect(
        adAccountService.getPlatformAdAccountMarkets(
          TEST_ORGANIZATION_ID,
          TEST_WORKSPACE_ID,
          TEST_PLATFORM_ACCOUNT_ID,
        ),
      ).resolves.toEqual(response);
    });
  });

  describe('Test validateUsersAccessToAdAccount method', () => {
    it('should throw an error if analytics service call fails', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(undefined);

      await expect(
        adAccountService.validateUserAdAccountsAuthorization(
          [TEST_PLATFORM_ACCOUNT_ID],
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        ),
      ).rejects.toThrowError(
        `Unable to fetch accounts with permissions for user id ${TEST_USER_ID} and platform ${TEST_PLATFORM}.`,
      );
    });

    it('should return true if the user has access to the ad account', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);

      await expect(
        adAccountService.validateUserAdAccountsAuthorization(
          [TEST_PLATFORM_ACCOUNT_ID],
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        ),
      ).toBeTruthy();
    });

    it('should throw exception if the user does not has access to the ad account', async () => {
      jest.spyOn(configService, 'get').mockReturnValueOnce(ANALYTICS_URL);
      jest
        .spyOn(analyticsService, 'getPlatformAdAccountsWithPermissions')
        .mockResolvedValueOnce(accountsPermissionsResponse);

      await expect(
        adAccountService.validateUserAdAccountsAuthorization(
          ['1234'],
          TEST_USER_ID,
          TEST_PLATFORM,
          TEST_AUTHORIZATION,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('Test Ad accounts to industry mapping functions', () => {
    it('should return response for mapIndustryGroupToAdAccounts method.', async () => {
      const response = {
        status: 'OK',
        result: {
          message:
            'All the mappings with industry group id - 7 are removed from the selected ad accounts successfully',
        },
      };
      jest
        .spyOn(organizationService, 'mapIndustryGroupToBulkAdAccountsAsPromise')
        .mockResolvedValueOnce(response as any);

      await expect(
        adAccountService.mapIndustryGroupToAdAccounts(
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
          {
            accountIds: ['act_123', 'act_456'],
            unselectedIndustryGroupIds: [1],
          },
        ),
      ).resolves.toEqual(response);
    });

    it('should return response for mapIndustryToAdAccounts method.', async () => {
      const response = {
        status: 'OK',
        result: {
          message: 'Industry - 2 assigned to 2 ad accounts successfully',
        },
      };
      jest
        .spyOn(organizationService, 'mapIndustryToBulkAdAccountsAsPromise')
        .mockResolvedValueOnce(response as any);
      await expect(
        adAccountService.mapIndustryToAdAccounts(
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
          1,
          {
            accountIds: ['act_123', 'act_456'],
            selectedIndustryId: 2,
          },
        ),
      ).resolves.toEqual(response);
    });

    it('should return response for mapSubIndustryToAdAccounts method.', async () => {
      const response = {
        status: 'OK',
        result: {
          message: 'Sub-industry - 3 assigned to 2 ad accounts successfully',
        },
      };
      jest
        .spyOn(organizationService, 'mapSubIndustryToBulkAdAccountsAsPromise')
        .mockResolvedValueOnce(response as any);
      await expect(
        adAccountService.mapSubIndustryToAdAccounts(
          TEST_ORGANIZATION_ID,
          TEST_USER_ID,
          2,
          {
            accountIds: ['act_123', 'act_456'],
            selectedSubIndustryId: 3,
          },
        ),
      ).resolves.toEqual(response);
    });
  });

  describe('Test list organization users for a platform', () => {
    it('should return response for getOrganizationUsersPerPlatform method.', async () => {
      const response = {
        status: 'OK',
        result: [
          {
            organizationId: 'ORG_1',
            userIds: [123],
          },
        ],
      };
      jest
        .spyOn(
          organizationAdAccountService,
          'getOrganizationUsersPerPlatformAsPromise',
        )
        .mockResolvedValueOnce(response as never);
      await expect(
        adAccountService.getOrganizationUsersPerPlatform(
          'TEST_PLATFORM',
          false,
        ),
      ).resolves.toEqual(response.result);
    });
  });

  describe('Test get Org Platform Ad Account With Import Info', () => {
    it('should return response for getPlatformAdAccountWithImportInfo method when valid account is requested.', async () => {
      const response = {
        status: 'OK',
        result: {
          id: 1,
          userId: TEST_USER_ID,
          platform: 'MOCK',
          platformUserId: '0uk1yjLowp',
          platformAccountId: TEST_PLATFORM_ACCOUNT_ID,
          platformAccountName: 'account_1',
          processingCompleted: 1,
          failuresCount: 6,
          linkedWorkspacesInOrg: 3,
        },
      };
      jest
        .spyOn(
          organizationAdAccountService,
          'getOrgPlatformAdAccountWithImportInfoAsPromise',
        )
        .mockResolvedValueOnce(response as never);
      await expect(
        adAccountService.getOrgPlatformAdAccountWithImportInfo(
          TEST_ORGANIZATION_ID,
          'TEST_PLATFORM',
          TEST_PLATFORM_ACCOUNT_ID,
        ),
      ).resolves.toEqual(response.result);
    });

    it('should return error for getPlatformAdAccountWithImportInfo method when invalid account is requested.', async () => {
      jest
        .spyOn(
          organizationAdAccountService,
          'getOrgPlatformAdAccountWithImportInfoAsPromise',
        )
        .mockRejectedValue(
          new Error(
            `Ad account with id ${TEST_PLATFORM_ACCOUNT_ID} not found for platform MOCK.`,
          ),
        );
      await expect(
        adAccountService.getOrgPlatformAdAccountWithImportInfo(
          TEST_ORGANIZATION_ID,
          'TEST_PLATFORM',
          TEST_PLATFORM_ACCOUNT_ID,
        ),
      ).rejects.toThrowError(
        `Ad account with id ${TEST_PLATFORM_ACCOUNT_ID} not found for platform MOCK.`,
      );
    });
  });

  describe('Test Save Platform Ad Account Sequential Failure method', () => {
    it('should return response for savePlatformAdAccountSequentialFailure method when saved correctly.', async () => {
      const response = {
        status: 'OK',
        result: {
          message:
            'Platform ad account sequential failure record saved successfully for account id: 123',
        },
      };
      jest
        .spyOn(
          organizationAdAccountService,
          'savePlatformAdAccountSequentialFailureAsPromise',
        )
        .mockResolvedValueOnce(response as never);
      await expect(
        adAccountService.savePlatformAdAccountSequentialFailure(
          TEST_PLATFORM_ACCOUNT_ID,
          {} as CreatePlatformAdAccountSequentialFailureDto,
        ),
      ).resolves.toEqual(response.result);
    });

    it('should return error for savePlatformAdAccountSequentialFailure method when not saved correctly.', async () => {
      jest
        .spyOn(
          organizationAdAccountService,
          'savePlatformAdAccountSequentialFailureAsPromise',
        )
        .mockRejectedValue(
          new Error(
            `Unable to save platform ad account sequential failure record for account id: ${TEST_PLATFORM_ACCOUNT_ID}`,
          ),
        );
      await expect(
        adAccountService.savePlatformAdAccountSequentialFailure(
          TEST_PLATFORM_ACCOUNT_ID,
          {} as CreatePlatformAdAccountSequentialFailureDto,
        ),
      ).rejects.toThrowError(
        `Unable to save platform ad account sequential failure record for account id: ${TEST_PLATFORM_ACCOUNT_ID}`,
      );
    });
  });

  describe('Test delete Platform Ad Account Sequential Failures method', () => {
    it('should return response for deleteSequentialFailuresByPlatformAdAccountId method when deleted correctly.', async () => {
      const response = {
        status: 'OK',
        result: {
          message:
            'Platform ad account sequential failure record deleted successfully for account id: 123',
        },
      };
      jest
        .spyOn(
          organizationAdAccountService,
          'deleteSequentialFailuresByPlatformAdAccountIdAsPromise',
        )
        .mockResolvedValueOnce(response as never);
      await expect(
        adAccountService.deleteSequentialFailuresByPlatformAdAccountId(
          TEST_PLATFORM_ACCOUNT_ID,
        ),
      ).resolves.toEqual(response.result);
    });

    it('should return error for deleteSequentialFailuresByPlatformAdAccountId method when not deleted correctly.', async () => {
      jest
        .spyOn(
          organizationAdAccountService,
          'deleteSequentialFailuresByPlatformAdAccountIdAsPromise',
        )
        .mockRejectedValue(
          new Error(
            `Unable to delete platform ad account sequential failure record for account id: ${TEST_PLATFORM_ACCOUNT_ID}`,
          ),
        );
      await expect(
        adAccountService.deleteSequentialFailuresByPlatformAdAccountId(
          TEST_PLATFORM_ACCOUNT_ID,
        ),
      ).rejects.toThrowError(
        `Unable to delete platform ad account sequential failure record for account id: ${TEST_PLATFORM_ACCOUNT_ID}`,
      );
    });
  });
});
