import {
  HttpStatus,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import axios from 'axios';
import {
  AMS_PLATFORM_PERMISSIONS_GET_BY_USER_URL,
  AMS_PLATFORM_PERMISSIONS_GET_URL,
  AS_PLATFORM_PERMISSION_UPDATE_URL,
  AS_PLATFORM_PERMISSIONS_GET_URL,
} from '../../../../constants/external-api-paths.constants';
import { ConfigService } from '@nestjs/config';
import { UpdateAccountPlatformPermissionDto } from '../entities/update-account-platform-permission.dto';
import { UserAccessMode } from '../../constants/constants';

@Injectable()
export class AnalyticsService {
  constructor(private readonly configService: ConfigService) {}
  private readonly logger = new Logger(AnalyticsService.name);
  private readonly PLATFORM_PINTEREST = 'PINTEREST';
  private readonly PLATFORM_REDDIT = 'REDDIT';
  private readonly AMS_SUPPORTED_PLATFORMS = [
    this.PLATFORM_PINTEREST,
    this.PLATFORM_REDDIT,
  ];

  /**
   * Get the analytics service url.
   */
  private getAnalyticsServiceUrl(): string {
    const analyticsServiceUrl = this.configService.get<string>(
      'legacyAnalyticsServiceUrl',
    );
    if (!analyticsServiceUrl) {
      throw new Error(`Analytics Service URL is not defined.`);
    }
    return analyticsServiceUrl;
  }

  /**
   * Get the Account Management service url.
   */
  private getAccountManagementServiceUrl(): string {
    const apiGWUrl = this.configService.get<string>('legacyApiGwUrl');
    if (!apiGWUrl) {
      throw new Error(`API GW URL not defined.`);
    }
    return apiGWUrl;
  }

  /**
   * Get the platform permission update url.
   * @param platform platform
   * @param platformAccountId platform account id
   * @returns url platform permission update url
   */
  private getPlatformPermissionUpdateURL(
    platform: string,
    platformAccountId: string,
  ) {
    return this.getAnalyticsServiceUrl()
      .concat(AS_PLATFORM_PERMISSION_UPDATE_URL)
      .replace(':platform', platform)
      .replace(':adAccountId', platformAccountId);
  }

  /**
   * Get the platform permission get url.
   * @param platform platform
   * @param userId user id
   * @param roleReporting flag to use role reporting permission
   * @returns url platform permission get url
   */
  private getPlatformPermissionGetURL(
    platform: string,
    userId: number,
    userAccessMode: UserAccessMode = 'SELF',
  ) {
    if (this.AMS_SUPPORTED_PLATFORMS.includes(platform.toUpperCase())) {
      if (userAccessMode === 'ALL') {
        return this.getAccountManagementServiceUrl()
          .concat(AMS_PLATFORM_PERMISSIONS_GET_BY_USER_URL)
          .replace(':platform', platform)
          .replace(':userId', userId.toString());
      } else {
        return this.getAccountManagementServiceUrl()
          .concat(AMS_PLATFORM_PERMISSIONS_GET_URL)
          .replace(':platform', platform);
      }
    } else {
      return this.getAnalyticsServiceUrl()
        .concat(AS_PLATFORM_PERMISSIONS_GET_URL)
        .replace(':platform', platform)
        .replace(':userId', userId.toString());
    }
  }

  /**
   * Update the platform permissions of an ad account in Analytics Service.
   * @param adAccountId The Ad Account id.
   * @param authorization The authorization header.
   * @param updateAccountPlatformPermissionDto The update account platform permission dto.
   */
  async updateAccountPermissionsViaAnalyticsService(
    adAccountId: string,
    authorization: string,
    updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto,
  ) {
    this.logger.log(
      `Sending request to AS to update permissions of the ad account ${adAccountId}`,
    );
    const { platform, permission } = updateAccountPlatformPermissionDto;
    const url = this.getPlatformPermissionUpdateURL(platform, adAccountId);
    try {
      const headers = {
        Authorization: authorization,
        'Content-Type': 'application/json',
      };
      const response = await axios.post(
        url,
        {
          permission: permission,
        },
        { headers },
      );
      return response.data.result;
    } catch (e) {
      this.logger.error(`Error while updating permissions in AS ${e}`);
      throw new Error(
        `Unable to update the account permissions ${e} for ad account ${adAccountId} in Analytics Service.`,
      );
    }
  }

  /**
   * Get platform permissions of an ad account from Analytics Service.
   * @param userId The user id.
   * @param platform The platform.
   * @param authorization The authorization token.
   */
  async getPlatformAdAccountsWithPermissions(
    userId: number,
    platform: string,
    authorization: string,
    userAccessMode: UserAccessMode = 'SELF',
  ) {
    const url = this.getPlatformPermissionGetURL(
      platform,
      userId,
      userAccessMode,
    );
    this.logger.log(
      `Sending request to AS to get platform permissions for user ${userId}, platform: ${platform}, url: ${url}`,
    );
    try {
      const headers = {
        Authorization: authorization,
        'Content-Type': 'application/json',
      };
      const response = await axios.get(url, { headers });
      return response.data.result;
    } catch (e) {
      this.logger.error(
        `Error while getting user platform permissions in AS ${e}`,
      );

      if (e.response?.status === HttpStatus.FORBIDDEN) {
        throw new UnauthorizedException(
          `User ${userId} is not authorized to access platform ${platform}`,
        );
      }

      throw new Error(
        `Unable to get the user platform permissions for user Id ${userId}, platform ${platform}` +
          ` from Analytics Service. Error - ${e}`,
      );
    }
  }
}
