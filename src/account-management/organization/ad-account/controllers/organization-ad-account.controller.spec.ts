import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { OrganizationAdAccountController } from './organization-ad-account.controller';
import { AdAccountService } from '../services/ad-account.service';
import { GetWorkspacesForAdAccount200Response } from '@vidmob/vidmob-organization-service-sdk/dist/model/getWorkspacesForAdAccount200Response';
import { Request } from '@nestjs/common';

describe('OrganizationAdAccountController', () => {
  const TEST_ORGANIZATION_ID = '0e801e54-cdef-44f1-9bb0-ca7fb859e408';
  const TEST_REQUEST = {
    userId: 123,
  };

  let organizationAdAccountController: OrganizationAdAccountController;
  let adAccountService: AdAccountService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationAdAccountController],
      providers: [
        {
          provide: AdAccountService,
          useValue: {
            findAdAccountsByOrganizationId: jest.fn(),
            findAllWorkspacesForOrganizationAdAccount: jest.fn(),
            getAccountsWithPermissionsViaAnalyticsService: jest.fn(),
            mapIndustryGroupToAdAccounts: jest.fn(),
            mapIndustryToAdAccounts: jest.fn(),
            mapSubIndustryToAdAccounts: jest.fn(),
            updatePlatformAdAccount: jest.fn(),
          },
        },
      ],
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
    }).compile();

    organizationAdAccountController =
      moduleRef.get<OrganizationAdAccountController>(
        OrganizationAdAccountController,
      );
    adAccountService = moduleRef.get<AdAccountService>(AdAccountService);
  });

  it('test findAdAccountsByOrganizationId method ', async () => {
    const TEST_ORG_ID = '1234';
    const TEST_OFFSET = 0;
    const TEST_PER_PAGE = 10;
    const findAdAccountsByOrganizationIdSpy = jest.spyOn(
      adAccountService,
      'findAdAccountsByOrganizationId',
    );
    await organizationAdAccountController.findAdAccountsByOrganizationId(
      TEST_ORG_ID,
      123,
      {
        search: 'vidmob',
      },
      {
        offset: TEST_OFFSET,
        perPage: TEST_PER_PAGE,
      },
    );
    expect(findAdAccountsByOrganizationIdSpy).toHaveBeenCalledWith(
      TEST_ORG_ID,
      123,
      {
        offset: TEST_OFFSET,
        perPage: TEST_PER_PAGE,
      },
      {
        search: 'vidmob',
      },
    );
  });

  it('test getOrganizationPlatformAdAccountsWithPermissions method ', async () => {
    const TEST_ORG_ID = '1234';
    const TEST_USER_ID = 123;
    const TEST_PLATFORM = 'facebook';
    const getAccountsWithPermissionsViaAnalyticsServiceSpy = jest.spyOn(
      adAccountService,
      'getAccountsWithPermissionsViaAnalyticsService',
    );
    await organizationAdAccountController.getOrganizationPlatformAdAccountsWithPermissions(
      { userId: TEST_USER_ID, headers: { authorization: 'token' } },
      TEST_ORG_ID,
      TEST_PLATFORM,
    );
    expect(
      getAccountsWithPermissionsViaAnalyticsServiceSpy,
    ).toHaveBeenCalledWith(
      TEST_ORG_ID,
      TEST_USER_ID,
      TEST_PLATFORM,
      'token',
      'SELF',
    );
  });

  it("should return a list of workspaces in an from an org's ad account", async () => {
    const expectedValue = create200MockResponse(
      [
        {
          name: 'test',
          status: 'ENABLED',
          dateCreated: '2021-08-05T18:00:00.000Z',
        },
      ],
      true,
    );
    jest
      .spyOn(adAccountService, 'findAllWorkspacesForOrganizationAdAccount')
      .mockResolvedValue(
        expectedValue as unknown as GetWorkspacesForAdAccount200Response,
      );
    const actualValue =
      await organizationAdAccountController.getWorkspacesByOrganizationAndAdAccount(
        '1234',
        '5678',
        {
          offset: 0,
          perPage: 10,
        },
      );
    expect(actualValue).toEqual(expectedValue);
  });

  it('should return response for mapIndustryGroupToAdAccounts method.', async () => {
    const response = {
      status: 'OK',
      result: {
        message:
          'All the mappings with industry group id - 7 are removed from the selected ad accounts successfully',
      },
    };
    jest
      .spyOn(adAccountService, 'mapIndustryGroupToAdAccounts')
      .mockResolvedValueOnce(response as any);

    await expect(
      organizationAdAccountController.mapIndustryGroupToAdAccounts(
        TEST_REQUEST as unknown as Request,
        TEST_ORGANIZATION_ID,
        {
          accountIds: ['act_123', 'act_456'],
          unselectedIndustryGroupIds: [1],
        },
      ),
    ).resolves.toEqual(response);
  });

  it('should return response for mapIndustryToAdAccounts method.', async () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Industry - 2 assigned to 2 ad accounts successfully',
      },
    };
    jest
      .spyOn(adAccountService, 'mapIndustryToAdAccounts')
      .mockResolvedValueOnce(response as any);
    await expect(
      organizationAdAccountController.mapIndustryToAdAccounts(
        TEST_REQUEST as unknown as Request,
        TEST_ORGANIZATION_ID,
        1,
        {
          accountIds: ['act_123', 'act_456'],
          selectedIndustryId: 2,
        },
      ),
    ).resolves.toEqual(response);
  });

  it('should return response for mapSubIndustryToAdAccounts method.', async () => {
    const response = {
      status: 'OK',
      result: {
        message: 'Sub-industry - 3 assigned to 2 ad accounts successfully',
      },
    };
    jest
      .spyOn(adAccountService, 'mapSubIndustryToAdAccounts')
      .mockResolvedValueOnce(response as any);
    await expect(
      organizationAdAccountController.mapSubIndustryToAdAccounts(
        TEST_REQUEST as unknown as Request,
        TEST_ORGANIZATION_ID,
        2,
        {
          accountIds: ['act_123', 'act_456'],
          selectedSubIndustryId: 3,
        },
      ),
    ).resolves.toEqual(response);
  });

  it('should return response for updatePlatformAdAccount method.', async () => {
    const TEST_PLATFORM = 'MOCK';
    const TEST_ACCOUNT_ID = '123456';
    const response = {
      status: 'OK',
      result: {
        platform: TEST_PLATFORM,
        platformAccountName: 'TEST_ACCOUNT',
        platformAccountId: TEST_ACCOUNT_ID,
        dateCreated: new Date(),
        processingCompleted: 1,
        processingCompletedDate: new Date(),
        lastSuccessfulProcessingDate: new Date(),
        importStatus: 'ENABLED',
      },
    };
    jest
      .spyOn(adAccountService, 'updatePlatformAdAccount')
      .mockResolvedValueOnce(response as any);
    await expect(
      organizationAdAccountController.updatePlatformAdAccount(
        TEST_ORGANIZATION_ID,
        TEST_PLATFORM,
        TEST_ACCOUNT_ID,
        {
          accountImportData: {
            importId: '6660ad50-d841-43f0-ad7a-b0e203b4230d',
            importCompleteDate: new Date().toISOString(),
            importStatus: 'SUCCESS',
          },
        },
      ),
    ).resolves.toEqual(response);
  });
});

const create200MockResponse = <T>(data: T, hasPagination: boolean) => {
  const status = 'OK';
  const results = data;
  if (hasPagination) {
    return {
      status,
      results,
      pagination: {
        offset: 0,
        perPage: 10,
        totalCount: 1,
      },
    };
  } else {
    return {
      status,
      results,
    };
  }
};
