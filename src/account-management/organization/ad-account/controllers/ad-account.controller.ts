import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AdAccountService } from '../services/ad-account.service';
import { UpdateAccountPlatformPermissionDto } from '../entities/update-account-platform-permission.dto';
import { ApiParam, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Authorities } from '../../../../auth/decorators/authority.decorator';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import {
  updateOrganizationParamsToAdAccountMapping,
  updateOrganizationToAdAccountMapping,
} from '../../../account-management.permissions';
import { UpdateBulkPlatformPermissionsDto } from '../entities/update-bulk-platform-permission.dto';
import { ReadPlatformOrganizationUsersMapDto } from '@vidmob/vidmob-organization-service-sdk';
import { CreatePlatformAdAccountSequentialFailureDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createPlatformAdAccountSequentialFailureDto';

/**
 * The controller responsible for managing Ad Accounts related logic.
 */
@ApiTags('Ad Account')
@ApiSecurity('Bearer Token')
@Controller('account-management/ad-account')
export class AdAccountController {
  constructor(private readonly adAccountService: AdAccountService) {}

  @Get('filters')
  async getAvailableFiltersForAdAccounts() {
    return this.adAccountService.getAvailableFilters();
  }

  @Post('organization/:organizationId/platform/:platform/platform-permission')
  @Permissions(updateOrganizationParamsToAdAccountMapping)
  @UsePipes(new ValidationPipe())
  async updatePlatformPermissions(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Body()
    updateBulkPlatformPermissionDto: UpdateBulkPlatformPermissionsDto,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    return this.adAccountService.updatePlatformPermissions(
      userId,
      authorization,
      organizationId,
      platform,
      updateBulkPlatformPermissionDto,
    );
  }
  /**
   * Update the platform permissions of an ad account.
   * @param req The request object.
   * @param adAccountId The Ad Account id.
   * @param updateAccountPlatformPermissionDto The update account platform permission dto.
   */
  @ApiParam({
    name: 'adAccountId',
    description: 'The id of the Ad Account',
  })
  @Post(':adAccountId/platform-permission')
  @Permissions(updateOrganizationToAdAccountMapping)
  @UsePipes(new ValidationPipe())
  async updateAccountPlatformPermissions(
    @Request() req: any,
    @Param('adAccountId') adAccountId: string,
    @Body()
    updateAccountPlatformPermissionDto: UpdateAccountPlatformPermissionDto,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    return this.adAccountService.updateAccountPlatformPermissions(
      adAccountId,
      userId,
      authorization,
      updateAccountPlatformPermissionDto,
    );
  }

  /**
   * Find all the organizations that have the ad account mapped to them.
   * @param adAccountId The ad account id.
   */
  @ApiParam({ name: 'adAccountId', description: 'The id of the Ad Account' })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Get(':adAccountId/organizations')
  async findOrganizationsByAdAccountId(
    @Param('adAccountId') adAccountId: string,
  ) {
    return this.adAccountService.findOrganizationsByAdAccountId(adAccountId);
  }

  /**
   * Get list of all organizations with users for a platform.
   * @param platform platform name
   * @param importV3EnabledOnly import v3 enabled only
   */
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiQuery({
    name: 'importV3EnabledOnly',
    type: 'boolean',
    description: 'import v3 enabled only',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Get('import/platform/:platform/organization-users')
  async getOrganizationUsersPerPlatform(
    @Param('platform') platform: string,
    @Query('importV3EnabledOnly') importV3EnabledOnly = false,
  ): Promise<ReadPlatformOrganizationUsersMapDto[]> {
    return this.adAccountService.getOrganizationUsersPerPlatform(
      platform,
      importV3EnabledOnly,
    );
  }

  /**
   * Get ad account information along with import info
   * @deprecated Use getOrganizationPlatformAdAccountWithImportInfo instead
   * @param platform platform name
   * @param adAccountId ad account id
   */
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiQuery({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Get('import/platform/:platform/account/:adAccountId')
  async getPlatformAdAccountWithImportInfo(
    @Param('platform') platform: string,
    @Param('adAccountId') adAccountId: string,
  ) {
    return this.adAccountService.getPlatformAdAccountWithImportInfo(
      platform,
      adAccountId,
    );
  }

  /**
   * Get organization ad account information along with import info
   * @param organizationId organization id
   * @param platform platform name
   * @param adAccountId ad account id
   */
  @ApiParam({
    name: 'organizationId',
    type: 'string',
    description: 'organization id',
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiQuery({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Get(
    'import/organization/:organizationId/platform/:platform/account/:adAccountId',
  )
  async getOrganizationPlatformAdAccountWithImportInfo(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('adAccountId') adAccountId: string,
  ) {
    return this.adAccountService.getOrgPlatformAdAccountWithImportInfo(
      organizationId,
      platform,
      adAccountId,
    );
  }

  /**
   * Save platform ad account sequential failure record
   * @deprecated the logic will be handled in update account import data endpoint
   * @param createPlatformAdAccountSequentialFailureDto The sequential failure DTO
   */
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Post('import/account/:adAccountId/failure')
  async savePlatformAdAccountSequentialFailure(
    @Param('adAccountId') adAccountId: string,
    @Body() dto: CreatePlatformAdAccountSequentialFailureDto,
  ) {
    return this.adAccountService.savePlatformAdAccountSequentialFailure(
      adAccountId,
      dto,
    );
  }

  /**
   * Delete all sequential failures for a platform ad account
   * @deprecated the logic will be handled in update account import data endpoint
   * @param adAccountId The ad account id
   */
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Delete('import/account/:adAccountId/failure')
  async deleteSequentialFailuresByPlatformAdAccountId(
    @Param('adAccountId') adAccountId: string,
  ) {
    return this.adAccountService.deleteSequentialFailuresByPlatformAdAccountId(
      adAccountId,
    );
  }
}
