import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { AdAccountService } from '../services/ad-account.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
} from '@vidmob/vidmob-nestjs-common';
import { Permissions } from '../../../../auth/decorators/permission.decorator';
import {
  readOrganizationToAdAccountMapping,
  readOrganizationWorkspacePlatformAdAccountMapping,
  updateOrganizationParamsToAdAccountMapping,
} from '../../../account-management.permissions';
import { ReadPlatformAdAccountWithRebuildInfoDto } from '../../../workspace/ad-account/dto/read-platform-ad-account-with-rebuild-info.dto';
import { ReadAccountPlatformPermissionsDto } from '../entities/read-account-platform-permissions.dto';
import { WorkspacesAdAccountRequestDto } from '../entities/workspaces-ad-account-request.dto';
import { AccountSearchParamsDto } from '../entities/ad-account-search-params.dto';
import { ReadAdAccountMarketsRequestDto } from '../entities/read-ad-account-markets-request.dto';
import { CreateIndustryAdAccountsRequestDto } from '../entities/create-industry-ad-accounts-request.dto';
import { Authorities } from '../../../../auth/decorators/authority.decorator';
import { UpdatePlatformAdAccountRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/updatePlatformAdAccountRequestDto';

/**
 * The controller responsible for managing Ad Accounts related logic in organization.
 */
@ApiTags('Ad Account')
@ApiSecurity('Bearer Token')
@Controller('account-management/organization/:organizationId')
export class OrganizationAdAccountController {
  constructor(private readonly adAccountService: AdAccountService) {}

  /**
   * Find all the ad accounts in an organization.
   * @param organizationId The organization id.
   * @param paginationOptions The pagination options.
   * @param searchParams The search params.
   * @param workspaceId The workspace id.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadPlatformAdAccountWithRebuildInfoDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @Get('ad-account')
  @Permissions(readOrganizationToAdAccountMapping)
  async findAdAccountsByOrganizationId(
    @Param('organizationId') organizationId: string,
    @Query('workspaceId') workspaceId: number,
    @Query() searchParams: AccountSearchParamsDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.adAccountService.findAdAccountsByOrganizationId(
      organizationId,
      workspaceId,
      paginationOptions,
      searchParams,
    );
  }

  /**
   * Get all workspaces by organization and an ad account.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param adAccountId - The ad account ID represents the unique identifier of the ad account to which all workspaces belong.
   * @param paginationOptions - The pagination options.
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'AdAccountId',
    description: 'The id of an ad account',
  })
  @Permissions(readOrganizationToAdAccountMapping)
  @Get('ad-account/:adAccountId/workspaces')
  async getWorkspacesByOrganizationAndAdAccount(
    @Param('adAccountId') adAccountId: string,
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.adAccountService.findAllWorkspacesForOrganizationAdAccount(
      adAccountId,
      organizationId,
      paginationOptions,
    );
  }

  /**
   * Get all accounts with permissions by user, organization and platform.
   * @param req - The request object.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param platform - The platform represents the unique identifier of the platform in which ad account is connected.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadAccountPlatformPermissionsDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'platform',
  })
  @Permissions(readOrganizationToAdAccountMapping)
  @Get('ad-account/:platform/ad-account-permissions')
  async getOrganizationPlatformAdAccountsWithPermissions(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    return this.adAccountService.getAccountsWithPermissionsViaAnalyticsService(
      organizationId,
      userId,
      platform,
      authorization,
      'SELF', // Default user access mode only allows access to own accounts
    );
  }

  /**
   * Get all accounts with permissions on behalf user, organization and platform for import purposes.
   * @param req - The request object.
   * @param userId - The user ID represents the unique identifier of the user on whose behalf the request is being made.
   * @param organizationId - The organization ID represents the unique identifier of the organization to which all workspaces belong.
   * @param platform - The platform represents the unique identifier of the platform in which ad account is connected.
   */
  @VmApiOkPaginatedArrayResponse({
    type: ReadAccountPlatformPermissionsDto,
  })
  @ApiParam({
    name: 'userId',
    description: 'The user id on whose behalf the request is being made',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'platform',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Get('ad-account/:platform/user/:userId/ad-account-permissions')
  async getUserOrganizationPlatformAdAccountsWithPermissions(
    @Request() req: any,
    @Param('userId', ParseIntPipe) userId: number,
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
  ) {
    const authorization = req.headers.authorization;
    return this.adAccountService.getAccountsWithPermissionsViaAnalyticsService(
      organizationId,
      userId,
      platform,
      authorization,
      'ALL', // Role Reporting can access all users' accounts
    );
  }

  @Permissions(readOrganizationWorkspacePlatformAdAccountMapping)
  @Post('workspaces/ad-account')
  async fetchConnectedAdAccounts(
    @Param('organizationId') organizationId: string,
    @Body() body: WorkspacesAdAccountRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { workspaces, platform } = body;
    return await this.adAccountService.fetchAdAccountsForWorkspacesAndPlatform(
      workspaces,
      platform,
      paginationOptions,
    );
  }

  @Permissions(readOrganizationWorkspacePlatformAdAccountMapping)
  @Post('ad-account/markets')
  async fetchMarketsForAdAccounts(
    @Param('organizationId') organizationId: string,
    @Body() body: ReadAdAccountMarketsRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { workspaceIds } = body;
    return await this.adAccountService.getPlatformAdAccountMarketsFromWorkspaces(
      workspaceIds,
      paginationOptions,
    );
  }

  @Permissions(updateOrganizationParamsToAdAccountMapping)
  @Post('ad-accounts/industry-group')
  @UsePipes(new ValidationPipe())
  async mapIndustryGroupToAdAccounts(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() body: CreateIndustryAdAccountsRequestDto,
  ) {
    const { userId } = req;
    return this.adAccountService.mapIndustryGroupToAdAccounts(
      organizationId,
      userId,
      body,
    );
  }

  @Permissions(updateOrganizationParamsToAdAccountMapping)
  @Post('ad-accounts/industry-group/:industryGroupId/industry')
  @UsePipes(new ValidationPipe())
  async mapIndustryToAdAccounts(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('industryGroupId', ParseIntPipe) industryGroupId: number,
    @Body() body: CreateIndustryAdAccountsRequestDto,
  ) {
    const { userId } = req;
    return this.adAccountService.mapIndustryToAdAccounts(
      organizationId,
      userId,
      industryGroupId,
      body,
    );
  }

  @Permissions(updateOrganizationParamsToAdAccountMapping)
  @Post('ad-accounts/industry/:industryId/sub-industry')
  @UsePipes(new ValidationPipe())
  async mapSubIndustryToAdAccounts(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('industryId', ParseIntPipe) industryId: number,
    @Body() body: CreateIndustryAdAccountsRequestDto,
  ) {
    const { userId } = req;
    return this.adAccountService.mapSubIndustryToAdAccounts(
      organizationId,
      userId,
      industryId,
      body,
    );
  }

  /**
   * Update ad account upon import completion
   */
  @ApiParam({
    name: 'organizationId',
    type: 'string',
    description: 'organization id',
  })
  @ApiParam({
    name: 'platform',
    type: 'string',
    description: 'platform name',
  })
  @ApiParam({
    name: 'adAccountId',
    type: 'string',
    description: 'ad account id',
  })
  @Authorities(['ROLE_REPORTING_OPERATIONS'])
  @Post('/platform/:platform/account/:adAccountId')
  async updatePlatformAdAccount(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('adAccountId') adAccountId: string,
    @Body() updateAccountDto: UpdatePlatformAdAccountRequestDto,
  ) {
    return this.adAccountService.updatePlatformAdAccount(
      organizationId,
      platform,
      adAccountId,
      updateAccountDto,
    );
  }
}
