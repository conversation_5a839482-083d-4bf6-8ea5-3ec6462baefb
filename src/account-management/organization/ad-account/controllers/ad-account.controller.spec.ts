import { AdAccountController } from './ad-account.controller';
import { AdAccountService } from '../services/ad-account.service';
import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { Request } from '@nestjs/common';
import { OrganizationService } from '@vidmob/vidmob-organization-service-sdk';

const TEST_AD_ACCOUNT_ID = 'act_1234';

describe('AdAccountController', () => {
  let adAccountController: AdAccountController;
  let adAccountService: AdAccountService;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [AdAccountController],
      providers: [
        {
          provide: AdAccountService,
          useValue: {
            updateAccountPlatformPermissions: jest.fn(),
            findOrganizationsByAdAccountId: jest.fn(),
            getOrganizationUsersPerPlatform: jest.fn(),
          },
        },
        {
          provide: OrganizationService,
          useValue: {},
        },
      ],
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
    }).compile();

    adAccountController =
      moduleRef.get<AdAccountController>(AdAccountController);
    adAccountService = moduleRef.get<AdAccountService>(AdAccountService);
  });

  it('test updateAccountPlatformPermissions method ', async () => {
    const updateAccountPlatformPermissionsSpy = jest.spyOn(
      adAccountService,
      'updateAccountPlatformPermissions',
    );
    const userId = 123;
    const authorization = 'Bearer token';
    const organizationId = '0e801e54-cdef-44f1-9bb0-ca7fb859e408';
    const requestMock = {
      userId,
      headers: {
        authorization,
      },
    };
    const updateAccountPlatformPermissionDto = {
      permission: 'ALLOW',
      platform: 'platform',
      organizationId: organizationId,
    };
    await adAccountController.updateAccountPlatformPermissions(
      requestMock as unknown as Request,
      TEST_AD_ACCOUNT_ID,
      updateAccountPlatformPermissionDto,
    );
    expect(updateAccountPlatformPermissionsSpy).toHaveBeenCalledWith(
      TEST_AD_ACCOUNT_ID,
      userId,
      authorization,
      updateAccountPlatformPermissionDto,
    );
  });

  it('test findOrganizationsByAdAccountId method ', async () => {
    const platformAdAccountId = '**********';
    const findOrganizationsByAdAccountIdSpy = jest.spyOn(
      adAccountService,
      'findOrganizationsByAdAccountId',
    );
    await adAccountController.findOrganizationsByAdAccountId(
      platformAdAccountId,
    );
    expect(findOrganizationsByAdAccountIdSpy).toHaveBeenCalledWith(
      platformAdAccountId,
    );
  });

  it('should find all organizations for an ad account', async () => {
    const platformAdAccountId = '**********';
    const findOrganizationsByAdAccountIdSpy = jest.spyOn(
      adAccountService,
      'findOrganizationsByAdAccountId',
    );
    await adAccountController.findOrganizationsByAdAccountId(
      platformAdAccountId,
    );
    expect(findOrganizationsByAdAccountIdSpy).toHaveBeenCalledWith(
      platformAdAccountId,
    );
  });

  it('should find all organization users for a platform', async () => {
    const TEST_PLATFORM = 'test';
    const getOrganizationUsersPerPlatformSpy = jest.spyOn(
      adAccountService,
      'getOrganizationUsersPerPlatform',
    );
    await adAccountController.getOrganizationUsersPerPlatform(TEST_PLATFORM);
    expect(getOrganizationUsersPerPlatformSpy).toHaveBeenCalledWith(
      TEST_PLATFORM,
      false,
    );
  });
});
