import { IsBoolean, IsDate, IsOptional, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';

/**
 * Read Account Platform Permissions DTO
 */
export class ReadAccountPlatformPermissionsDto {
  /**
   * The platform of the ad account.
   */
  @AutoMap()
  @IsString()
  platform: string;

  /**
   * The name of the ad account.
   */
  @AutoMap()
  @IsString()
  adAccountName: string;

  /**
   * The id of the ad account.
   */
  @AutoMap()
  @IsString()
  adAccountId: string;

  /**
   * The date created.
   */
  @AutoMap()
  @IsDate()
  dateCreated: Date;

  /**
   * permission
   */
  @AutoMap()
  @IsString()
  permission: string;

  /**
   * can access flag
   */
  @AutoMap()
  @IsBoolean()
  canAccess: boolean;

  /**
   * processing complete flag
   */
  @AutoMap()
  @IsBoolean()
  processingComplete: boolean;

  /**
   * connected in other organizations flag
   */
  @AutoMap()
  @IsBoolean()
  connectedInOtherOrganizations: boolean;

  /**
   * Import priority
   */
  @AutoMap()
  @IsBoolean()
  @IsOptional()
  importPriority?: number;
}
