import { IsDefined, IsOptional, IsString } from 'class-validator';

export class UpdateAccountPlatformPermissionDto {
  @IsDefined({ message: 'Platform is a required field.' })
  @IsString({ message: 'Platform must be a string.' })
  platform: string;

  @IsDefined({ message: 'Permission is a required field.' })
  @IsString({ message: 'Permission must be a string.' })
  permission: string;

  @IsDefined({ message: 'Organization Id is a required field.' })
  @IsString({ message: 'Organization Id must be a string.' })
  @IsOptional()
  organizationId: string;
}
