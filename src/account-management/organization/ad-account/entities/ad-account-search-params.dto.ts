import { IsEnum, IsOptional, IsString } from 'class-validator';
import { SortOrder } from '../../../../reports/model/sort-order';
import { Transform } from 'class-transformer';
import { Platform, PlatformNewName } from 'src/constants/platform.constants';

export class AccountSearchParamsDto {
  /**
   * Search string to filter ad accounts from organizations and workspaces
   * @example "account_id"
   */
  @IsOptional()
  @IsString()
  @Transform(({ value }) => {
    switch (value.toLowerCase()) {
      case PlatformNewName.FACEBOOK:
        return Platform.FACEBOOK;
      case PlatformNewName.TWITTER:
        return Platform.TWITTER;
      case PlatformNewName.ADWORDS:
        return Platform.ADWORDS;
      default:
        return value;
    }
  })
  search?: string;

  /**
   * Sort by string
   * @example "account_id"
   */
  @IsOptional()
  @IsString()
  sortBy?: string;

  /**
   * Sort Order string
   * @example "ASC"
   */
  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: string;

  /**
   * Brands string
   * @example "none"
   */
  @IsOptional()
  @IsString()
  brands?: string;

  /**
   * ImportStatus string
   * @example "Suspended,Not Importing"
   */
  @IsOptional()
  @IsString()
  importStatus?: string;

  /**
   * Markets string
   * @example "none"
   */
  @IsOptional()
  @IsString()
  markets?: string;

  /**
   * Connection Status string
   * @example "Disconnected,Connected"
   */
  @IsOptional()
  @IsString()
  connectionStatus?: string;

  /**
   * Workspaces string
   * @example "none"
   */
  @IsOptional()
  @IsString()
  workspaces?: string;
}
