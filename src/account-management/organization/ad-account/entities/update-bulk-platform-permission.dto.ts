import { Type } from 'class-transformer';
import {
  IsA<PERSON>y,
  IsDefined,
  IsEnum,
  IsString,
  ValidateNested,
} from 'class-validator';

export enum AccountPermission {
  ALLOW = 'ALLOW',
  DENY = 'DENY',
}

export class AccountPermissionDto {
  @IsDefined({ message: 'Account ID is a required field.' })
  @IsString({ message: 'Account ID must be a string.' })
  adAccountId: string;

  @IsDefined({ message: 'Permission is a required field.' })
  @IsEnum(AccountPermission, {
    message: 'Permission must be either ALLOW or DENY.',
  })
  permission: string;
}

export class UpdateBulkPlatformPermissionsDto {
  @IsDefined({ message: 'Permissions is a required field.' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AccountPermissionDto)
  permissions: Array<AccountPermissionDto>;
}
