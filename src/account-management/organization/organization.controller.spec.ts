import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationController } from './organization.controller';
import { OrganizationService } from './services/organization.service';
import { mockData } from './mocks/list-organization.mock';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

describe('OrganizationController', () => {
  let controller: OrganizationController;
  let service: OrganizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganizationController],
      providers: [
        OrganizationService,
        {
          provide: OrganizationService,
          useValue: {
            findAllByUserId: jest.fn().mockResolvedValue(mockData),
          },
        },
      ],
    }).compile();

    controller = module.get<OrganizationController>(OrganizationController);
    service = module.get<OrganizationService>(OrganizationService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should return mock organizations for a given user', async () => {
    const mockUserId = 12345;
    const mockSearch = 'Dyna Corp';
    const mockPaginationOptions: PaginationOptions = {
      offset: 0,
      perPage: 10,
    };
    const result = await controller.findAllByUser(
      { userId: mockUserId },
      mockPaginationOptions,
      mockSearch,
    );

    expect(result).toEqual(mockData);
    expect(service.findAllByUserId).toHaveBeenCalledWith(
      mockUserId,
      mockSearch,
      mockPaginationOptions,
    );
  });
});
