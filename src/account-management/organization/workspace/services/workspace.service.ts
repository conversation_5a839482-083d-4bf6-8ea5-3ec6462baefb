// workspace.service.ts
import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  CreateWorkspaceDto,
  FindWorkspacesByOrganization200Response,
  OrganizationService,
  ReadWorkspaceDto,
  UpdateWorkspaceDto as UpdateWorkspaceDtoSDK,
  WorkspaceService as WorkspaceServiceSDK,
} from '@vidmob/vidmob-organization-service-sdk';
import { SearchParamsDto } from '../dto/search-params.dto';
import { PermissionDomain } from '../../../../auth/enums/permission.domain.enum';
import { checkOrganizationReadAllWorkspaces } from '../../../account-management.permissions';
import { AuthService } from '../../../../auth/services/auth.service';
import { UUID } from 'crypto';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { CreateWorkspaceDto as LocalCreateWorkspaceDto } from '../dto/create-workspace.dto';
import { OrganizationUserRoles } from '../../../../constants/role.constants';
import { OrganizationUserService } from '../../organization-user/organization-user.service';

@Injectable()
export class WorkspaceService {
  constructor(
    private readonly organizationService: OrganizationService,
    private readonly organizationUserService: OrganizationUserService,
    private readonly workspaceServiceSDK: WorkspaceServiceSDK,
    private readonly authService: AuthService,
  ) {}

  async canReadWorkspaceAll(authorization: string, organizationId: string) {
    const domain = PermissionDomain.ORGANIZATION;

    const responseObservable = await this.authService.isResourcePermissionValid(
      domain,
      organizationId,
      authorization,
      checkOrganizationReadAllWorkspaces.statements,
    );

    return responseObservable;
  }

  async getWorkspacesByOrganizationId(
    organizationId: string,
    userId: number,
    authorization: string,
    paginationOptions: PaginationOptions,
    searchParams: SearchParamsDto,
  ) {
    const canReadAll = await this.canReadWorkspaceAll(
      authorization,
      organizationId,
    );

    if (canReadAll) {
      return this.getWorkspacesByOrganizationIdAndSearch(
        organizationId,
        searchParams,
        paginationOptions,
      );
    } else {
      return this.getWorkspaceByOrganizationIdAndUserAndSearch(
        organizationId,
        userId,
        paginationOptions,
        searchParams,
      );
    }
  }

  async getWorkspaceByOrganizationIdAndUserAndSearch(
    organizationId: string,
    userId: number,
    paginationOptions: PaginationOptions,
    searchParams: SearchParamsDto,
  ) {
    const { search, market, brand } = searchParams;
    const findWorkspacesByOrganization200Response: FindWorkspacesByOrganization200Response =
      await this.organizationService.findWorkspacesByOrganizationAndUserAsPromise(
        organizationId,
        userId,
        search,
        market,
        brand,
        paginationOptions.offset,
        paginationOptions.perPage,
      );

    const results = findWorkspacesByOrganization200Response.result;
    const total = findWorkspacesByOrganization200Response.pagination?.totalSize;

    return new PaginatedResultArray<ReadWorkspaceDto>(results, total);
  }

  private async isUserOrgAdmin(organizationId: string, userId: number) {
    const response = await this.organizationUserService.getUserInOrganization(
      organizationId,
      userId,
    );

    return response.result.roles.some(
      (role) => role.identifier === OrganizationUserRoles.ORG_ADMIN,
    );
  }

  async getWorkspacesByOrganizationIdForUser(
    organizationId: string,
    userId: number,
    pagination: PaginationOptions,
    searchParams: SearchParamsDto,
  ) {
    const isOrgAdmin = await this.isUserOrgAdmin(organizationId, userId);
    return isOrgAdmin
      ? this.getWorkspacesByOrganizationIdAndSearch(
          organizationId,
          searchParams,
          pagination,
        )
      : this.getWorkspaceByOrganizationIdAndUserAndSearch(
          organizationId,
          userId,
          pagination,
          searchParams,
        );
  }

  async getWorkspacesByOrganizationIdAndSearch(
    organizationId: string,
    searchParams: SearchParamsDto,
    paginationOptions: PaginationOptions,
  ) {
    const { market, search, brand } = searchParams;

    try {
      const findWorkspacesByOrganization200Response: FindWorkspacesByOrganization200Response =
        await this.organizationService.findWorkspacesByOrganizationAsPromise(
          organizationId,
          search,
          market,
          brand,
          paginationOptions.offset,
          paginationOptions.perPage,
        );

      const results = findWorkspacesByOrganization200Response.result;
      const total =
        findWorkspacesByOrganization200Response.pagination?.totalSize;
      return new PaginatedResultArray<ReadWorkspaceDto>(results, total);
    } catch (e) {
      throw e;
    }
  }

  async getWorkspaceById(id: number): Promise<any> {
    const { result } = await this.workspaceServiceSDK.findOneAsPromise(id);
    return result;
  }

  async createWorkspace(
    organizationId: UUID,
    createWorkspaceDto: LocalCreateWorkspaceDto,
  ) {
    const createPayload: CreateWorkspaceDto = {
      ...createWorkspaceDto,
      organizationId: organizationId,
    };

    const { result } = await this.workspaceServiceSDK.createAsPromise(
      createPayload,
    );

    return result;
  }

  async updateWorkspace(
    organizationId: UUID,
    id: number,
    updateWorkspaceDto: UpdateWorkspaceDto,
  ) {
    const payload: UpdateWorkspaceDtoSDK = {
      ...updateWorkspaceDto,
      organizationId: organizationId,
    };
    const response = await this.workspaceServiceSDK.updateAsPromise(
      id,
      payload,
    );

    return response.result;
  }

  async areWorkspacesAccessibleInOrganization(
    orgId: string,
    workspaceIds: number[],
  ) {
    try {
      const validatedResponse =
        await this.organizationService.verifyWorkspacesByIdsAsPromise(orgId, {
          workspaceIds: workspaceIds,
        });
      return validatedResponse && validatedResponse.result;
    } catch (e) {
      return false;
    }
  }

  async findUserWorkspacesForAllOrgs(
    userId: number,
    organizationId: string | undefined,
    workspaceFeatures: string | undefined,
  ) {
    const { result } =
      await this.workspaceServiceSDK.findAllUserWorkspaceForAllOrgsAsPromise(
        userId,
        organizationId,
        workspaceFeatures,
      );
    return result;
  }

  /**
   * Lightweight method to load all workspaces within an organization.
   * The purpose of this endpoint is to provide a fast query to retrieve all workspaces, without much querying.
   *
   */
  async getAllLightweight(organizationId: string) {
    return await this.organizationService.getAllLightWeightWorkspacesAsPromise(
      organizationId,
    );
  }

  /**
   * Fetch all features for a given workspace.
   */
  async getWorkspaceFeatures(
    workspaceId: number,
  ): Promise<Record<string, boolean>> {
    const { result } = await this.workspaceServiceSDK.getFeaturesAsPromise(
      workspaceId,
    );
    return result;
  }
}
