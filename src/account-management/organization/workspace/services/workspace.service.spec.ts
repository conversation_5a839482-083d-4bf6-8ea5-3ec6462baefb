import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceService } from './workspace.service';
import {
  AdAccountService as AdAccountServiceSDK,
  OrganizationService,
  WorkspaceService as WorkspaceServiceSDK,
  WorkspaceAdAccountService,
} from '@vidmob/vidmob-organization-service-sdk';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { SearchParamsDto } from '../dto/search-params.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { AuthService } from '../../../../auth/services/auth.service';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { CreateVerifyOrganizationWorkspaceDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createVerifyOrganizationWorkspaceDto';
import { VerifyWorkspacesByIds200Response } from '@vidmob/vidmob-organization-service-sdk/dist/model/verifyWorkspacesByIds200Response';
import { ReadWorkspaceDto } from '../dto/read-workspace.dto';
import { OrganizationUserService } from '../../organization-user/organization-user.service';

// Create a custom mock implementation of OrganizationService
class MockOrganizationService {
  findWorkspacesByOrganizationAndUserAsPromise(id: string) {
    return {
      result: [
        {
          id: 1,
          name: 'Workspace 1',
          isPrimary: false,
          logoUrl: 'https://www.vidmob.com/logo.png',
          organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
        },
        {
          id: 3,
          name: 'Workspace 2',
          isPrimary: true,
          logoUrl: 'https://www.vidmob.com/logo.png',
          organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
        },
      ],
      pagination: {
        totalSize: 2,
        offset: 0,
        perPage: 10,
      },
    };
  }

  async verifyWorkspacesByIdsAsPromise(
    orgId: string,
    dto: CreateVerifyOrganizationWorkspaceDto,
  ): Promise<VerifyWorkspacesByIds200Response> {
    return {
      status: 'OK',
      result: [
        {
          id: 1,
        },
        {
          id: 2,
        },
      ],
    };
  }

  async findWorkspacesByOrganizationAsPromise(
    organizationId: string,
  ): Promise<any> {
    // Mock the response as needed
    const mockedResponse = {
      result: [
        {
          id: 1,
          name: 'Workspace 1',
          isPrimary: false,
          organizationId: organizationId,
        },
        {
          id: 2,
          name: 'Workspace 2',
          isPrimary: true,
          organizationId: organizationId,
        },
      ],
      pagination: {
        totalSize: 2,
        offset: 0,
        perPage: 10,
        nextOffset: null,
      },
    };
    return mockedResponse;
  }

  async mapPlatformAdAccountsToWorkspacesAsPromise() {
    return {
      status: 'OK',
      result: 'Link created successfully' as unknown as object,
    };
  }
}

class MockworkspaceServiceSDK {
  mockedResponse: ReadWorkspaceDto = {
    id: 1,
    name: 'Workspace 1',
    isPrimary: false,
    organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
    dateCreated: new Date('2021-03-03T20:00:00.000Z'),
    users: [],
    markets: undefined,
    totalUser: 1,
  };

  async updateAsPromise(id: string, payload: UpdateWorkspaceDto): Promise<any> {
    const mockedResponse = {
      result: {
        id: 1,
        name: 'Workspace 1',
        isPrimary: false,
        organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
        logoUrl: 'https://www.vidmob.com/logo.png',
      },
    };
    return mockedResponse;
  }

  async findWorkspacesByOrganizationAsPromise() {
    return {
      result: [
        {
          id: 1,
          name: 'Workspace 1',
          isPrimary: false,
          logoUrl: 'https://www.vidmob.com/logo.png',
          organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
        },
        {
          id: 2,
          name: 'Workspace 2',
          isPrimary: true,
          logoUrl: 'https://www.vidmob.com/logo.png',
          organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
        },
      ],
      pagination: {
        totalSize: 2,
        offset: 0,
        perPage: 10,
      },
    };
  }

  async findOneAsPromise(id: string): Promise<any> {
    return {
      result: this.mockedResponse,
    };
  }

  async createAsPromise(
    organizationId: string,
    payload: CreateWorkspaceDto,
  ): Promise<any> {
    const newMockedWorkspce = {
      result: {
        id: 12345,
        name: 'New Awesome Workspace',
        logoUrl: null,
        isPrimary: false,
        organizationId: '000b85cc-7648-49c1-84cc-555bc19a9241',
      },
    };
    return newMockedWorkspce;
  }
}

class MockAdAccountServiceSDK {
  async findOrganizationsForAdAccountAsPromise(adAccountId: string) {
    return {
      result: {
        id: '27a7e882-43de-4bfa-8f53-3b62875b8432',
        name: 'VidMob',
        status: 'ENABLED',
        dateCreated: '2021-03-03T20:00:00.000Z',
        lastUpdated: '2021-03-03T20:00:00.000Z',
        workspaces: [],
      },
      status: 'OK',
    };
  }
}

describe('WorkspaceService', () => {
  let workspaceService: WorkspaceService;
  let authService: AuthService;
  let organizationService: OrganizationService;
  let oneWorkspaceMock: ReadWorkspaceDto;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspaceService,
        {
          provide: OrganizationService,
          useClass: MockOrganizationService,
        },
        {
          provide: OrganizationUserService,
          useValue: {
            getUserInOrganization: jest.fn(),
          },
        },
        {
          provide: AuthService,
          useValue: {
            canAccessOrganizationAsWorkspaceAll: jest.fn(),
          },
        },
        {
          provide: AdAccountServiceSDK,
          useClass: MockAdAccountServiceSDK,
        },
        {
          provide: WorkspaceServiceSDK,
          useClass: MockworkspaceServiceSDK,
        },
      ],
    }).compile();

    workspaceService = moduleRef.get<WorkspaceService>(WorkspaceService);
    oneWorkspaceMock = new MockworkspaceServiceSDK().mockedResponse;
    organizationService =
      moduleRef.get<OrganizationService>(OrganizationService);
    authService = moduleRef.get<AuthService>(AuthService);
  });

  it('should return a paginated array of ReadWorkspaceDto', async () => {
    // Arrange
    const organizationId = '27a7e882-43de-4bfa-8f53-3b62875b8432';
    const searchParams: SearchParamsDto = {
      market: 'bra',
      search: 'My Cool Workspace',
    };

    const paginationOptions: PaginationOptions = {
      offset: 0,
      perPage: 10,
    };

    // Act
    const result =
      await workspaceService.getWorkspacesByOrganizationIdAndSearch(
        organizationId,
        searchParams,
        paginationOptions,
      );

    // Assert
    expect(result.totalCount).toEqual(2);
    expect(result.items.length).toEqual(2);
    expect(result.items[0].id).toEqual(1);
    expect(result.items[0].name).toEqual('Workspace 1');
    expect(result.items[0].isPrimary).toEqual(false);
    expect(result.items[0].organizationId).toEqual(organizationId);

    expect(result.items[1].id).toEqual(2);
    expect(result.items[1].name).toEqual('Workspace 2');
    expect(result.items[1].isPrimary).toEqual(true);
    expect(result.items[1].organizationId).toEqual(organizationId);
  });

  it('should return one workspace', async () => {
    const workspaceId = 1;
    // Act
    const result = await workspaceService.getWorkspaceById(workspaceId);

    // Assert
    expect(result).toEqual(oneWorkspaceMock);
  });

  it('should update workspace', async () => {
    const organizationId = '27a7e882-43de-4bfa-8f53-3b62875b8432';
    const workspaceId = 123;
    const updateWorkspaceDto: UpdateWorkspaceDto = {
      name: 'Workspace 1',
      isPrimary: false,
    };

    const result = await workspaceService.updateWorkspace(
      organizationId,
      workspaceId,
      updateWorkspaceDto,
    );
    expect(result).toBeDefined();
    expect(result).toBeInstanceOf(Object);
    expect(result.name).toEqual('Workspace 1');
    expect(result.organizationId).toEqual(organizationId);
  });

  it('should create a new workspace', async () => {
    const organizationId = '27a7e882-43de-4bfa-8f53-3b62875b8432';
    const createWorkspaceDto: CreateWorkspaceDto = {
      name: 'New Awesome Workspace',
      isPrimary: false,
      logoUrl: 'https://www.vidmob.com/logo.png',
    };

    const result = await workspaceService.createWorkspace(
      organizationId,
      createWorkspaceDto,
    );

    expect(result).toBeDefined();
    expect(result).toBeInstanceOf(Object);
    expect(result.id).toEqual(12345);
    expect(result.name).toEqual(createWorkspaceDto.name);
    expect(result.organizationId).toEqual(
      '000b85cc-7648-49c1-84cc-555bc19a9241',
    );
  });
});
