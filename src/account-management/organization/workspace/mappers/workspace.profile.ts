import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { Workspace } from '../../../../entities/workspace.entity';
import { ReadWorkspaceDto } from '../dto/read-workspace.dto';

/**
 * Profile to create mapping between DTOs and Entities.
 * Updates the mapper with a collection of maps where
 * each map defines the relationship and flow between a DTO and an Entity class.
 */
@Injectable()
export class WorkspaceProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        Workspace,
        ReadWorkspaceDto,
        forMember(
          (dest) => dest.users,
          mapFrom((src) => src.users.map(({ id, ...user }) => user)),
        ),
        forMember(
          (dest) => dest.markets,
          mapFrom((src) => src.markets.map((market) => market.name)),
        ),
      );
    };
  }
}
