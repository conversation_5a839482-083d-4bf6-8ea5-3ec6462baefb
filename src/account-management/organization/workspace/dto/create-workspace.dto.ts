import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class CreateWorkspaceDto {
  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  @IsNotEmpty()
  isPrimary: boolean;

  /**
   * The list of brand ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brands?: string[];

  /**
   * The list of market ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  markets?: string[];

  /**
   * The Logo Url of the workspace
   * @example "https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png"
   */
  @AutoMap()
  @IsString()
  @IsOptional()
  logoUrl: string;
}
