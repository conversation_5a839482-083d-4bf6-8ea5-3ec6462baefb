import { AutoMap } from '@automapper/classes';
import { WorkspaceUserDto } from './workspace-user.dto';
import { MarketDto } from './workspace-market.dto';

export class ReadWorkspaceDto {
  /**
   * System assigned id of the Workspace
   */
  @AutoMap()
  id: number;

  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  name: string;

  /**
   * The organization ID of the workspace
   */
  @AutoMap()
  organizationId: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  isPrimary: boolean;

  /**
   * The date the workspace was created
   */
  @AutoMap()
  dateCreated: Date;

  /**
   * Array of workspace users
   */
  @AutoMap(() => WorkspaceUserDto)
  users: WorkspaceUserDto[];

  /**
   * Array of markets associated with the workspace
   */
  @AutoMap(() => MarketDto)
  markets: any;

  /**
   * Total number of users in the workspace
   */
  @AutoMap()
  totalUser: number;
}
