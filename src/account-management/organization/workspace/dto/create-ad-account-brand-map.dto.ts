import { AutoMap } from '@automapper/classes';
import { Is<PERSON>rra<PERSON>, IsOptional } from 'class-validator';

export class CreateAdAccountBrandMapDto {
  /**
   * List of brands ids to map
   * @example ['xxx-xxx-xxx', 'yyy-yyy']
   */
  @AutoMap()
  @IsOptional()
  @IsArray()
  selected_brands: string[];

  /**
   * List of brands ids to unmap
   * @example ['xxx-xxx-xxx', 'yyy-yyy']
   */
  @AutoMap()
  @IsOptional()
  @IsArray()
  unselected_brands: string[];

  /**
   * List of ad account ids
   * @example ['*****************', 't2_t5vae3ih']
   */
  @AutoMap()
  @IsArray()
  accounts: string[];
}
