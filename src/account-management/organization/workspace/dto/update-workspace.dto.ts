import {
  IsArray,
  IsBoolean,
  IsOptional,
  IsString,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';

export class UpdateWorkspaceDto {
  /**
   * The name of the workspace
   * @example "My Workspace"
   */
  @AutoMap()
  @IsOptional()
  @IsString()
  name?: string;

  /**
   * Is this workspace the primary workspace for the organization?
   * @example true
   */
  @AutoMap()
  @IsOptional()
  @IsBoolean()
  isPrimary?: boolean;

  /**
   * The list of brand ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  brands?: string[];

  /**
   * The list of market ids to be associated with the workspace
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  markets?: string[];
}
