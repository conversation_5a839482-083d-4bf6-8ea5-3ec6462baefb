import { Workspace } from '../../../../entities/workspace.entity';

export const workspaceOne: Workspace = {
  id: 1,
  organizationId: '1234-5678-9101',
  name: 'Workspace 1',
  isPrimary: false,
  dateCreated: new Date(),
  totalUser: 1,
  users: [
    {
      id: 1,
      username: '<PERSON><PERSON>@vidmob.com',
      firstName: '<PERSON>',
      lastName: '<PERSON><PERSON>',
      workspace: [],
    },
  ],
  markets: [
    {
      isoCode: 'bra',
      name: 'Brazil',
      workspaces: [],
    },
  ],
  brands: [],
};

export const workspaceTwo: Workspace = {
  id: 2,
  organizationId: '1234-5678-9101',
  name: 'Workspace 2',
  isPrimary: false,
  dateCreated: new Date(),
  totalUser: 1,
  users: [
    {
      id: 2,
      username: '<PERSON><PERSON>@vidmob.com',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      workspace: [],
    },
  ],
  markets: [
    {
      isoCode: 'usa',
      name: 'United States',
      workspaces: [],
    },
    {
      isoCode: 'bra',
      name: 'Brazil',
      workspaces: [],
    },
  ],
  brands: [],
};
