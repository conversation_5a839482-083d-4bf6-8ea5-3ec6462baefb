import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceController } from './workspace.controller';
import { WorkspaceService } from '../services/workspace.service';
import { SearchParamsDto } from '../dto/search-params.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ReadWorkspaceDto } from '@vidmob/vidmob-organization-service-sdk'; // Update this import path
import { CreateWorkspaceDto as LocalCreateWorkspaceDto } from '../dto/create-workspace.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { UUID } from 'crypto';
import { AdAccountService } from '../../ad-account/services/ad-account.service';
import { AuthService } from '../../../../auth/services/auth.service';
import { UnauthorizedException } from '@nestjs/common';
import { ReadAdAccountMapDto } from '../dto/read-ad-account-map.dto';

// Create a custom mock implementation of WorkspaceService
class MockWorkspaceService {
  mockedResponse: ReadWorkspaceDto = {
    id: 23142,
    name: 'Things Are Fine',
    logoUrl: 'https://www.vidmob.com/logo.png',
    isPrimary: false, // Updated to boolean
    organizationId: '27a7e882-43de-4bfa-8f53-3b62875b8432',
  };

  async getWorkspacesByOrganizationId(): Promise<
    PaginatedResultArray<ReadWorkspaceDto>
  > {
    // Mocked response
    const mockedResponse: PaginatedResultArray<ReadWorkspaceDto> = {
      totalCount: 1,
      items: [this.mockedResponse],
      queryId: undefined,
    };
    return mockedResponse;
  }

  async getWorkspaceById(workspaceId: number): Promise<ReadWorkspaceDto> {
    return {
      ...this.mockedResponse,
      id: (workspaceId as number) || 23142,
    };
  }

  async updateWorkspace(organizationId: UUID) {
    const mockResponse = {
      status: 'OK',
      result: {
        id: 31234,
        name: 'My Workspace 3',
        logoUrl: null,
        isPrimary: true,
        organizationId: organizationId,
      },
    };
    return mockResponse;
  }

  async createWorkspace(organizationId: UUID) {
    const mockResponse = {
      status: 'OK',
      result: {
        id: 12345,
        name: 'Vidmob Workspace',
        logoUrl: null,
        isPrimary: true,
        organizationId: organizationId,
      },
    };

    return mockResponse;
  }
}

describe('WorkspaceController', () => {
  let workspaceController: WorkspaceController;
  let workspaceService: WorkspaceService;
  let adAccountService: AdAccountService;
  let authService: AuthService;
  let oneWorkspaceMock: ReadWorkspaceDto;
  const paginationOptions: PaginationOptions = { offset: 0, perPage: 10 };
  const requestMock = { userId: 123, headers: { authorization: 'token' } };
  const organizationId = '27a7e882-43de-4bfa-8f53-3b62875b8432';
  const workspaceId = 23142;
  const adAccountId = 'act_12345';

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [WorkspaceController],
      providers: [
        // Use the custom mock implementation
        { provide: WorkspaceService, useClass: MockWorkspaceService },
        {
          provide: AdAccountService,
          useValue: {
            getOrganizationAdAccountsListForHealthDashboard: jest.fn(),
            getWorkspaceAdAccountsListForHealthDashboard: jest.fn(),
            createAdAccountBrandMap: jest.fn(),
            createAdAccountMarketMap: jest.fn(),
            getPlatformAdAccountBrands: jest.fn(),
            getPlatformAdAccountMarkets: jest.fn(),
          },
        },
        {
          provide: AuthService,
          useValue: {
            canAccessOrganizationAdAccountDetails: jest.fn(),
            canAccessWorkspaceAdAccountDetails: jest.fn(),
            canAccessOrganizationOrWorkspaceAdAccountDetails: jest.fn(),
            canAccessOrganizationOrWorkspaceAdAccountMetadata: jest.fn(),
          },
        },
      ],
    }).compile();

    workspaceController =
      moduleRef.get<WorkspaceController>(WorkspaceController);
    workspaceService = moduleRef.get<WorkspaceService>(WorkspaceService);
    oneWorkspaceMock = new MockWorkspaceService().mockedResponse;
    adAccountService = moduleRef.get<AdAccountService>(AdAccountService);
    authService = moduleRef.get<AuthService>(AuthService);
  });

  it('should be defined', () => {
    expect(workspaceController).toBeDefined();
  });

  it('should return a paginated array of ReadWorkspaceDto', async () => {
    // Arrange
    const searchParams: SearchParamsDto = {
      market: 'bra',
      search: 'My Cool Workspace',
    };

    // Act
    const result =
      await workspaceController.getWorkspacesByOrganizationIdAndSearch(
        requestMock, // Use the mocked request object with userId
        organizationId,
        searchParams,
        paginationOptions,
      );

    // Assert
    expect(result.totalCount).toEqual(1);
    expect(result.items.length).toEqual(1);
    expect(result.items[0]).toEqual(oneWorkspaceMock);
  });

  it('should return a one worskpace', async () => {
    // Act
    const result = await workspaceController.showWorkspaceById(workspaceId);
    expect(result).toEqual(oneWorkspaceMock);
  });

  it('should update workspace', async () => {
    const updateWorkspaceSpy = jest.spyOn(workspaceService, 'updateWorkspace');

    const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
    const workspaceName = 'Pepsi';
    const workspaceId = 123;

    const updateWorkspaceDto: UpdateWorkspaceDto = {
      name: workspaceName,
      markets: [],
      brands: [],
      isPrimary: false,
    };

    await workspaceController.updateWorkspace(
      organizationId,
      workspaceId,
      updateWorkspaceDto,
    );

    expect(updateWorkspaceSpy).toHaveBeenCalledWith(
      organizationId,
      workspaceId,
      updateWorkspaceDto,
    );
  });

  it('should create workspace', async () => {
    const createWorkspaceSpy = jest.spyOn(workspaceService, 'createWorkspace');

    const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
    const workspaceName = 'My Awesome Workspace';

    const createWorkspaceDto: LocalCreateWorkspaceDto = {
      name: workspaceName,
      isPrimary: false,
      logoUrl: 'https://www.vidmob.com/logo.png',
    };

    await workspaceController.createWorkspace(
      organizationId,
      createWorkspaceDto,
    );

    expect(createWorkspaceSpy).toHaveBeenCalledWith(
      organizationId,
      createWorkspaceDto,
    );
  });

  it('should create a new workspace with the same data inserted', async () => {
    const createWorkspaceSpy = jest.spyOn(workspaceService, 'createWorkspace');

    const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
    const workspaceName = 'Vidmob Workspace';

    const createWorkspaceDto: LocalCreateWorkspaceDto = {
      name: workspaceName,
      isPrimary: false,
      logoUrl: 'https://www.vidmob.com/logo.png',
    };

    const result = await workspaceController.createWorkspace(
      organizationId,
      createWorkspaceDto,
    );

    expect(createWorkspaceSpy).toHaveBeenCalledWith(
      organizationId,
      createWorkspaceDto,
    );
    expect(result).toEqual({
      status: 'OK',
      result: {
        id: 12345,
        name: workspaceName,
        logoUrl: null,
        isPrimary: true,
        organizationId: organizationId,
      },
    });
  });

  describe('getAdAccountsListForHealthDashboard', () => {
    const searchParams = {
      search: 'search',
      sortBy: 'account_id',
      sortOrder: 'ASC',
    };
    const expectedResponse = new PaginatedResultArray([], 1, undefined);

    it('should return ad accounts from organization', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationAdAccountDetails')
        .mockResolvedValueOnce(true);
      jest
        .spyOn(
          adAccountService,
          'getOrganizationAdAccountsListForHealthDashboard',
        )
        .mockResolvedValueOnce(expectedResponse);

      const result =
        await workspaceController.getAdAccountsListForHealthDashboard(
          requestMock,
          organizationId,
          workspaceId,
          searchParams,
          paginationOptions,
        );

      expect(result).toEqual(expectedResponse);
    });

    it('should return ad accounts from workspace', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationAdAccountDetails')
        .mockResolvedValueOnce(false);
      jest
        .spyOn(authService, 'canAccessWorkspaceAdAccountDetails')
        .mockResolvedValueOnce(true);
      jest
        .spyOn(adAccountService, 'getWorkspaceAdAccountsListForHealthDashboard')
        .mockResolvedValueOnce(expectedResponse);

      const result =
        await workspaceController.getAdAccountsListForHealthDashboard(
          requestMock,
          organizationId,
          workspaceId,
          searchParams,
          paginationOptions,
        );

      expect(result).toEqual(expectedResponse);
    });

    it('should throw UnauthorizedException when no access', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationAdAccountDetails')
        .mockResolvedValueOnce(false);
      jest
        .spyOn(authService, 'canAccessWorkspaceAdAccountDetails')
        .mockResolvedValueOnce(false);

      // You can use Jest's .toThrow to check for specific exceptions
      await expect(
        workspaceController.getAdAccountsListForHealthDashboard(
          requestMock,
          organizationId,
          workspaceId,
          searchParams,
          paginationOptions,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('createPlatformAdAccountsAndBrandsMaps', () => {
    const payload = {
      accounts: ['act_123', 'act_456'],
      selected_brands: ['123', '456'],
      unselected_brands: [],
    };

    const expectedResponse: ReadAdAccountMapDto = {
      message:
        'Selected brands successfully added/removed from to/from 2 ad account(s).',
    };

    it('should allow map successfully 2 ad accounts with brands', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountMetadata')
        .mockResolvedValueOnce(true);
      jest
        .spyOn(adAccountService, 'createAdAccountBrandMap')
        .mockResolvedValueOnce(expectedResponse);

      const result =
        await workspaceController.createPlatformAdAccountsAndBrandsMaps(
          organizationId,
          workspaceId,
          requestMock,
          payload,
        );

      expect(result).toEqual(expectedResponse);
    });

    it('should throw UnauthorizedException with no access', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountMetadata')
        .mockResolvedValueOnce(false);
      jest
        .spyOn(adAccountService, 'createAdAccountBrandMap')
        .mockResolvedValueOnce(expectedResponse);

      await expect(
        workspaceController.createPlatformAdAccountsAndBrandsMaps(
          organizationId,
          workspaceId,
          requestMock,
          payload,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('createPlatformAdAccountsAndMarketsMaps', () => {
    const payload = {
      accounts: ['act_123', 'act_456'],
      selected_markets: ['123', '456'],
      unselected_markets: [],
    };

    const expectedResponse: ReadAdAccountMapDto = {
      message:
        'Selected markets successfully added/removed from to/from 2 ad account(s).',
    };

    it('should allow map successfully 2 ad accounts with markets', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountMetadata')
        .mockResolvedValueOnce(true);
      jest
        .spyOn(adAccountService, 'createAdAccountMarketMap')
        .mockResolvedValueOnce(expectedResponse);

      const result =
        await workspaceController.createPlatformAdAccountsAndMarketsMaps(
          organizationId,
          workspaceId,
          requestMock,
          payload,
        );

      expect(result).toEqual(expectedResponse);
    });

    it('should throw UnauthorizedException with no access', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountMetadata')
        .mockResolvedValueOnce(false);
      jest
        .spyOn(adAccountService, 'createAdAccountMarketMap')
        .mockResolvedValueOnce(expectedResponse);

      await expect(
        workspaceController.createPlatformAdAccountsAndMarketsMaps(
          organizationId,
          workspaceId,
          requestMock,
          payload,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('getPlatformAdAccountBrands', () => {
    const expectedResponse = {
      status: 'OK',
      result: {
        brands: [
          {
            id: '7534120d-2f8f-4ba8-98b8-35cfd7906907',
            name: 'Awesome! Its Updated',
          },
          {
            id: 'df226c28-b078-42f5-91b1-f502e5965133',
            name: 'Ben Nye',
          },
        ],
      },
    };

    it('should allow fetch brands for this ad account', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountDetails')
        .mockResolvedValueOnce(true);
      jest
        .spyOn(adAccountService, 'getPlatformAdAccountBrands')
        .mockResolvedValueOnce(expectedResponse as any);

      const result = await workspaceController.getPlatformAdAccountBrands(
        requestMock,
        organizationId,
        workspaceId,
        adAccountId,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should throw UnauthorizedException with no access', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountDetails')
        .mockResolvedValueOnce(false);
      jest
        .spyOn(adAccountService, 'getPlatformAdAccountBrands')
        .mockResolvedValueOnce(expectedResponse as any);

      await expect(
        workspaceController.getPlatformAdAccountBrands(
          requestMock,
          organizationId,
          workspaceId,
          adAccountId,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('getPlatformAdAccountMarkets', () => {
    const expectedResponse = {
      status: 'OK',
      result: {
        markets: [
          {
            isoCode: 'usa',
            name: 'United Stated',
          },
          {
            isoCode: 'bra',
            name: 'Brazil',
          },
        ],
      },
    };

    it('should allow fetch markets for this ad account', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountDetails')
        .mockResolvedValueOnce(true);
      jest
        .spyOn(adAccountService, 'getPlatformAdAccountMarkets')
        .mockResolvedValueOnce(expectedResponse as any);

      const result = await workspaceController.getPlatformAdAccountMarkets(
        requestMock,
        organizationId,
        workspaceId,
        adAccountId,
      );

      expect(result).toEqual(expectedResponse);
    });

    it('should throw UnauthorizedException with no access', async () => {
      jest
        .spyOn(authService, 'canAccessOrganizationOrWorkspaceAdAccountDetails')
        .mockResolvedValueOnce(false);
      jest
        .spyOn(adAccountService, 'getPlatformAdAccountMarkets')
        .mockResolvedValueOnce(expectedResponse as any);

      await expect(
        workspaceController.getPlatformAdAccountMarkets(
          requestMock,
          organizationId,
          workspaceId,
          adAccountId,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });
});
