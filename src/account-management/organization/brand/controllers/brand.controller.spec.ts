import { Test, TestingModule } from '@nestjs/testing';
import { BrandController } from './brand.controller';
import { BrandService } from '../services/brand.service';
import { CreateBrandDto } from '../dto/create-brand.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { Repository } from 'typeorm';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Brand } from '../entities/brand.entity';
import { BrandProfile } from '../mappers/brand.profile';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import { Request } from 'express';
import { WorkspaceBrandMap } from '../../../workspace/brand/entities/workspace-brand-map.entity';
import { BadRequestException } from '@nestjs/common';
import { CreateBrandResultDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createBrandResultDto';
import { BrandService as BrandServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { createMapper, Mapper } from '@automapper/core';
import { BrandUserDto } from '../dto/brand-user.dto';
import { User } from '../../../../entities/user.entity';

describe('BrandController', () => {
  let brandController: BrandController;
  let brandService: BrandService;
  let workspaceBrandMapRepo: Repository<WorkspaceBrandMap>;
  let brandRepo: Repository<Brand>;
  let brandServiceSDK: BrandServiceSDK;
  let classMapper: Mapper;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      controllers: [BrandController],
      providers: [
        BrandService,
        BrandProfile,
        BrandServiceSDK,
        {
          provide: getRepositoryToken(Brand),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceBrandMap),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: BrandServiceSDK,
          useFactory: () => ({
            getBrandsAsPromise: jest.fn(),
            createBrandAsPromise: jest.fn(),
            deleteBrandAsPromise: jest.fn(),
            updateBrandAsPromise: jest.fn(),
          }),
        },
      ],
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
    }).compile();

    workspaceBrandMapRepo = moduleRef.get<Repository<WorkspaceBrandMap>>(
      getRepositoryToken(WorkspaceBrandMap),
    );
    brandRepo = moduleRef.get<Repository<Brand>>(getRepositoryToken(Brand));

    brandController = moduleRef.get<BrandController>(BrandController);
    brandService = moduleRef.get<BrandService>(BrandService);
    brandServiceSDK = moduleRef.get<BrandServiceSDK>(BrandServiceSDK);
    classMapper = moduleRef.get<Mapper>(getMapperToken());
  });

  describe('getBrands', () => {
    it('should return a paginated array of ReadBrandDto', async () => {
      const organizationId = '123';
      const paginationOptions: PaginationOptions = { offset: 0, perPage: 10 };
      const expectedResult: PaginatedResultArray<ReadBrandDto> = {
        totalCount: 1,
        items: [
          {
            id: 'daddc0ad-d678-434f-8015-9817f5d39bae',
            name: 'Coke',
            description: '',
            organizationId: organizationId,
            dateCreated: '2023-06-30T20:37:35.000Z',
            lastUpdated: '2023-06-30T20:37:35.000Z',
            workspaceCount: 0,
            updatedByPerson: new BrandUserDto(),
            createdByPerson: new BrandUserDto(),
          },
        ],
        queryId: undefined,
      };

      jest.spyOn(brandService, 'getBrands').mockResolvedValue(expectedResult);

      const result = await brandController.getBrands(
        organizationId,
        paginationOptions,
        undefined,
      );

      expect(result).toEqual(expectedResult);
      expect(brandService.getBrands).toHaveBeenCalledWith(
        organizationId,
        paginationOptions,
        undefined,
      );
    });
  });

  describe('createBrand', () => {
    it('should call brandService.createBrand with the correct parameters', async () => {
      const userId = 123;
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandName = 'Coke';
      const brandDescription = 'Coke Description';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const createBrandDto: CreateBrandDto = {
        name: brandName,
        description: brandDescription,
      };

      const expectedResult: CreateBrandResultDto = {
        id: 'daddc0ad-d678-434f-8015-9817f5d39bae',
        name: brandName,
        description: brandDescription,
        organizationId: organizationId,
        dateCreated: '2023-06-30T20:37:35.000Z',
        lastUpdated: '2023-06-30T20:37:35.000Z',
        createdByPerson: new User(),
      };

      jest.spyOn(brandService, 'createBrand').mockResolvedValue(expectedResult);

      await brandController.createBrand(
        request as Request,
        organizationId,
        createBrandDto,
      );

      expect(brandService.createBrand).toHaveBeenCalledWith(
        userId,
        organizationId,
        createBrandDto,
      );
    });
  });

  describe('updateBrand', () => {
    it('should call brandService.updateBrand with the correct parameters', async () => {
      const userId = '123';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandName = 'Pepsi';
      const brandDescription = 'Pepsi Description';
      const brandId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const updateBrandDto: UpdateBrandDto = {
        name: brandName,
        description: brandDescription,
      };

      const expectedResult = new ReadBrandDto();
      expectedResult.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      expectedResult.name = brandName;
      expectedResult.description = brandDescription;
      expectedResult.organizationId = organizationId;
      expectedResult.dateCreated = '2023-06-30T20:37:35.000Z';
      expectedResult.lastUpdated = '2023-06-30T20:37:35.000Z';

      jest.spyOn(brandService, 'updateBrand').mockResolvedValue(expectedResult);

      await brandController.updateBrand(
        request as Request,
        organizationId,
        brandId,
        updateBrandDto,
      );

      expect(brandService.updateBrand).toHaveBeenCalledWith(
        userId,
        organizationId,
        brandId,
        updateBrandDto,
      );
    });
  });

  describe('deleteBrand', () => {
    it('should call brandService.deleteBrand with the correct parameters', async () => {
      const userId = '123';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const expectedResult = new ReadBrandDto();
      expectedResult.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      expectedResult.organizationId = organizationId;
      expectedResult.dateCreated = '2023-06-30T20:37:35.000Z';
      expectedResult.lastUpdated = '2023-06-30T20:37:35.000Z';

      jest.spyOn(workspaceBrandMapRepo, 'find').mockResolvedValueOnce([]);

      jest.spyOn(brandService, 'deleteBrand').mockResolvedValue(expectedResult);

      await brandController.deleteBrand(
        request as Request,
        organizationId,
        brandId,
      );

      expect(brandService.deleteBrand).toHaveBeenCalledWith(
        userId,
        organizationId,
        brandId,
      );
    });

    it('should throw BadRequestException when deleting a brand associated with a workspace', async () => {
      // Your test data
      const userId = '123';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';

      const request: Request = {
        userId: userId,
      } as unknown as Request;

      const brand = new Brand();
      brand.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      brand.organizationId = organizationId;
      brand.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));

      const expectedResult = new WorkspaceBrandMap();
      expectedResult.brandId = brandId;
      expectedResult.workspaceId = 123;

      jest.spyOn(brandRepo, 'findOneBy').mockResolvedValueOnce(brand);

      jest
        .spyOn(workspaceBrandMapRepo, 'find')
        .mockResolvedValueOnce([expectedResult]);
      jest
        .spyOn(brandService, 'isBrandAssociatedWithWorkspace')
        .mockResolvedValue(true);

      // Wrap the function call inside an async function and use expect().rejects.toThrow()
      await expect(
        brandController.deleteBrand(
          request as Request,
          organizationId,
          brandId,
        ),
      ).rejects.toThrow(BadRequestException);
    });
  });
});
