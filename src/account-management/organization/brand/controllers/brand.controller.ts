import {
  Controller,
  Get,
  Put,
  Post,
  Param,
  ValidationPipe,
  Body,
  Request,
  Delete,
  Query,
} from '@nestjs/common';
import { BrandService } from '../services/brand.service';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import { CreateBrandDto } from '../dto/create-brand.dto';
import {
  ApiConflictResponse,
  ApiParam,
  ApiQuery,
  ApiSecurity,
  ApiTags,
} from '@nestjs/swagger';
import {
  readBrandConfiguration,
  createBrandConfiguration,
  updateBrandConfiguration,
  deleteBrandConfiguration,
} from '../../../account-management.permissions';
import { Permissions } from '../../../../auth/decorators/permission.decorator';

@ApiTags('Brand')
@ApiSecurity('Bearer Token')
@Controller('account-management/organization/:organizationId/brand')
/**
 * This endpoint returns brands from organization
 */
export class BrandController {
  constructor(private brandService: BrandService) {}
  @VmApiOkPaginatedArrayResponse({
    type: ReadBrandDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @Permissions(readBrandConfiguration)
  @ApiQuery({
    name: 'search',
    description: 'Search term for brand name',
    required: false,
    type: String,
  })
  @Get()
  async getBrands(
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('search') search?: string,
  ): Promise<PaginatedResultArray<ReadBrandDto>> {
    return this.brandService.getBrands(
      organizationId,
      paginationOptions,
      search,
    );
  }

  /**
   * Create a new brand to a organization.
   * @param organizationId - The id of the Organization.
   * @param name - The name of Brand.
   */
  @ApiConflictResponse({
    description: 'Brand name already exists.',
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the Organization',
  })
  @ApiParam({ name: 'name', description: 'The name of Brand' })
  @ApiParam({ name: 'description', description: 'The description of Brand' })
  @Permissions(createBrandConfiguration)
  @Post()
  async createBrand(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(new ValidationPipe()) param: CreateBrandDto,
  ) {
    const { userId } = req;
    return this.brandService.createBrand(userId, organizationId, param);
  }

  /**
   * Update a brand by id
   *
   * @param id
   * @param updateWorkspaceDto
   * @returns
   */
  @ApiConflictResponse({
    description: 'Brand name already exists.',
  })
  @VmApiOkResponse({
    type: ReadBrandDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({ name: 'brandId', description: 'The id of the brand' })
  @Put(':brandId')
  @Permissions(updateBrandConfiguration)
  updateBrand(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('brandId') brandId: string,
    @Body(new ValidationPipe()) updateBrandDto: UpdateBrandDto,
  ) {
    const { userId } = req;
    return this.brandService.updateBrand(
      userId,
      organizationId,
      brandId,
      updateBrandDto,
    );
  }

  /**
   * Soft Delete a brand by id
   *
   * @param id
   * @returns
   */
  @VmApiOkResponse({
    type: ReadBrandDto,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({ name: 'brandId', description: 'The id of the brand' })
  @Permissions(deleteBrandConfiguration)
  @Delete(':brandId')
  deleteBrand(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('brandId') brandId: string,
  ) {
    const { userId } = req;
    return this.brandService.deleteBrand(userId, organizationId, brandId);
  }
}
