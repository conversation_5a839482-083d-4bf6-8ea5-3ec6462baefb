import { Test, TestingModule } from '@nestjs/testing';
import { BrandService } from './brand.service';
import { Repository } from 'typeorm';
import { Brand } from '../entities/brand.entity';
import { WorkspaceBrandMap } from '../../../workspace/brand/entities/workspace-brand-map.entity';
import { User } from '../../../../entities/user.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BrandProfile } from '../mappers/brand.profile';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { CreateBrandResultDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createBrandResultDto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import { BrandService as BrandServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/brand.service';

describe('BrandService', () => {
  let brandService: BrandService;
  let brandRepo: Repository<Brand>;
  let workspaceBrandMapRepo: Repository<WorkspaceBrandMap>;
  let brandServiceSDK: BrandServiceSDK;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        BrandService,
        BrandProfile,
        BrandServiceSDK,
        {
          provide: getRepositoryToken(Brand),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(WorkspaceBrandMap),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: BrandServiceSDK,
          useFactory: () => ({
            getBrandsAsPromise: jest.fn(),
            createBrandAsPromise: jest.fn(),
            deleteBrandAsPromise: jest.fn(),
            updateBrandAsPromise: jest.fn(),
          }),
        },
      ],
    }).compile();

    brandService = moduleRef.get<BrandService>(BrandService);
    brandRepo = moduleRef.get<Repository<Brand>>(getRepositoryToken(Brand));
    workspaceBrandMapRepo = moduleRef.get<Repository<WorkspaceBrandMap>>(
      getRepositoryToken(WorkspaceBrandMap),
    );
    brandServiceSDK = moduleRef.get<BrandServiceSDK>(BrandServiceSDK);
  });

  it('should be defined', () => {
    expect(brandService).toBeDefined();
  });

  describe('getBrands', () => {
    it('should find all brands', async () => {
      const person = new User();
      person.id = 1;
      person.username = '<EMAIL>';
      person.firstName = 'Foo';
      person.lastName = 'Bar';

      const brand1 = new Brand();
      brand1.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      brand1.name = 'Coke';
      brand1.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      brand1.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand1.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand1.updatedByPerson = person;

      const brand2 = new Brand();
      brand2.id = '011fd28b-24fd-4e62-a29b-19525c449046';
      brand2.name = 'Pepsi';
      brand2.description = 'Pepsi Description';
      brand2.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      brand2.dateCreated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand2.lastUpdated = new Date(Date.parse('2023-06-30T20:37:35.000Z'));
      brand2.updatedByPerson = person;

      const readBrandDto1 = new ReadBrandDto();
      readBrandDto1.id = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      readBrandDto1.name = 'Coke';
      readBrandDto1.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      readBrandDto1.dateCreated = '2023-06-30T20:37:35.000Z';
      readBrandDto1.lastUpdated = '2023-06-30T20:37:35.000Z';

      const readBrandDto2 = new ReadBrandDto();
      readBrandDto2.id = '011fd28b-24fd-4e62-a29b-19525c449046';
      readBrandDto2.name = 'Pepsi';
      readBrandDto2.description = 'Pepsi Description';
      readBrandDto2.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      readBrandDto2.dateCreated = '2023-06-30T20:37:35.000Z';
      readBrandDto2.lastUpdated = '2023-06-30T20:37:35.000Z';

      const mockConvertedResult = [readBrandDto1, readBrandDto2];

      jest.spyOn(brandServiceSDK, 'getBrandsAsPromise').mockResolvedValue({
        status: 'OK',
        result: mockConvertedResult,
        pagination: { offset: 0, perPage: 10, totalSize: 2, nextOffset: 10 },
      });
      const paginatedResultArray = await brandService.getBrands(
        '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a',
        {
          offset: 0,
          perPage: 10,
        },
      );

      expect(paginatedResultArray).toBeDefined();
      expect(paginatedResultArray).toBeInstanceOf(PaginatedResultArray);
    });
  });

  describe('createBrand', () => {
    it('should create a new brand', async () => {
      const id = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const name = 'Foo';
      const description = 'Foo Description';
      const userId = 123;

      const createBrandResultDto: CreateBrandResultDto = {
        id: id,
        organizationId: organizationId,
        name: name,
        description: description,
        dateCreated: '2023-06-30T20:37:35.000Z',
        lastUpdated: '2023-06-30T20:37:35.000Z',
        createdByPerson: new User(),
      };

      jest
        .spyOn(brandServiceSDK, 'createBrandAsPromise')
        .mockResolvedValue(createBrandResultDto);

      const result = await brandService.createBrand(userId, organizationId, {
        name: name,
        description: description,
      });

      expect(result).toBeDefined();
      expect(result.name).toEqual(name);
      expect(result.description).toEqual(description);
      expect(result.organizationId).toEqual(organizationId);
      expect(result.id).toEqual(id);
    });
  });

  describe('updateBrand', () => {
    it('should update a new brand', async () => {
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const name = 'Foo';
      const description = 'Foo Description';
      const userId = 123;

      const savedBrand = new Brand();
      savedBrand.id = brandId;
      savedBrand.updatedByPersonId = userId;
      savedBrand.organizationId = organizationId;
      savedBrand.name = name;

      const updateBrandDto = new UpdateBrandDto();
      updateBrandDto.name = name;

      const readBrandDto1 = new ReadBrandDto();
      readBrandDto1.id = '011fd28b-24fd-4e62-a29b-19525c449046';
      readBrandDto1.name = 'Foo';
      readBrandDto1.description = description;
      readBrandDto1.organizationId = '14a1d02a-5f0c-4e37-84fe-6d3c8703c61a';
      readBrandDto1.dateCreated = '2023-06-30T20:37:35.000Z';
      readBrandDto1.lastUpdated = '2023-06-30T20:37:35.000Z';

      jest.spyOn(brandRepo, 'findOneBy').mockResolvedValueOnce(savedBrand);
      jest
        .spyOn(brandServiceSDK, 'updateBrandAsPromise')
        .mockResolvedValueOnce({ status: 'OK', result: readBrandDto1 });

      const result = await brandService.updateBrand(
        userId,
        organizationId,
        brandId,
        updateBrandDto,
      );

      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(ReadBrandDto);
      expect(result.name).toEqual(name);
      expect(result.description).toEqual(description);
      expect(result.id).toEqual(brandId);
    });
  });

  describe('deleteBrand', () => {
    it('should soft delete a brand', async () => {
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const userId = 123;

      const savedBrand = new Brand();
      savedBrand.id = brandId;
      savedBrand.updatedByPersonId = userId;
      savedBrand.organizationId = organizationId;

      const readBrandDto1 = new ReadBrandDto();
      readBrandDto1.id = '011fd28b-24fd-4e62-a29b-19525c449046';
      readBrandDto1.name = 'Foo';
      readBrandDto1.organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      readBrandDto1.dateCreated = '2023-06-30T20:37:35.000Z';
      readBrandDto1.lastUpdated = '2023-06-30T20:37:35.000Z';

      jest.spyOn(brandRepo, 'findOneBy').mockResolvedValueOnce(savedBrand);
      jest.spyOn(workspaceBrandMapRepo, 'count').mockResolvedValueOnce(0);

      jest
        .spyOn(brandServiceSDK, 'deleteBrandAsPromise')
        .mockResolvedValueOnce({ status: 'OK', result: readBrandDto1 });

      const result = await brandService.deleteBrand(
        userId,
        organizationId,
        brandId,
      );

      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(ReadBrandDto);
      expect(result.id).toEqual(brandId);
    });
  });
});
