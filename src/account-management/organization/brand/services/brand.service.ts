import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { Brand } from '../entities/brand.entity';
import { WorkspaceBrandMap } from '../../../workspace/brand/entities/workspace-brand-map.entity';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { UpdateBrandDto } from '../dto/update-brand.dto';
import {
  BrandService as BrandServiceSDK,
  GetBrandsByName200Response,
} from '@vidmob/vidmob-organization-service-sdk';
import { CreateBrandDto } from '../dto/create-brand.dto';

@Injectable()
export class BrandService {
  constructor(
    @InjectRepository(Brand)
    private brandRepository: Repository<Brand>,
    @InjectRepository(WorkspaceBrandMap)
    private workspaceBrandMapRepository: Repository<WorkspaceBrandMap>,
    private readonly brandServiceSDK: BrandServiceSDK,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * find all brands
   *
   * @returns
   */
  async getBrands(
    organizationId: string,
    paginationOptions: PaginationOptions,
    search?: string,
  ): Promise<PaginatedResultArray<ReadBrandDto>> {
    const getBrands200Response = await this.brandServiceSDK.getBrandsAsPromise(
      organizationId,
      search,
      paginationOptions?.offset,
      paginationOptions?.perPage,
      paginationOptions.queryId,
    );

    const ReadBrandDtos: ReadBrandDto[] = this.classMapper.mapArray(
      getBrands200Response.result,
      ReadBrandDto,
      ReadBrandDto,
    );

    return new PaginatedResultArray(
      ReadBrandDtos,
      getBrands200Response.pagination?.totalSize,
    );
  }

  /**
   * find all brands
   *
   * @returns
   */
  async getBrandsByName(
    organizationId: string,
    brandNames: string[],
    paginationOptions?: PaginationOptions,
  ): Promise<GetBrandsByName200Response> {
    const getBrands200Response =
      await this.brandServiceSDK.getBrandsByNameAsPromise(
        organizationId,
        brandNames.join(','), //TODO: Temporary until we will change the API to receive string[]
        paginationOptions?.offset,
        paginationOptions?.perPage,
        paginationOptions?.queryId,
      );
    return getBrands200Response;
  }

  /**
   * Create a brand
   *
   * @param userId
   * @param organizationId
   * @param brandDTO
   * @returns
   */
  async createBrand(
    userId: number,
    organizationId: string,
    brandDTO: CreateBrandDto,
  ) {
    return this.brandServiceSDK.createBrandAsPromise(
      organizationId,
      userId,
      brandDTO.description || '',
      brandDTO.name,
      brandDTO,
    );
  }

  /**
   * Update a brand
   *
   * @param id
   * @param updateBrandDto
   * @returns
   */
  async updateBrand(
    userId: number,
    organizationId: string,
    brandId: string,
    updateBrandDto: UpdateBrandDto,
  ) {
    const updateBrand200Response =
      await this.brandServiceSDK.updateBrandAsPromise(
        organizationId,
        brandId,
        userId,
        updateBrandDto,
      );
    return updateBrand200Response.result;
  }

  /**
   * Delete a brand
   *
   * @param id
   * @returns
   */
  async deleteBrand(userId: number, organizationId: string, brandId: string) {
    const brand = await this.getBrandEntity(brandId, organizationId);

    const isBrandAssociatedWithWorkspace =
      await this.isBrandAssociatedWithWorkspace(brandId);

    if (isBrandAssociatedWithWorkspace) {
      throw new BadRequestException(
        'Cannot delete brand. It is associated with one or more workspaces.',
      );
    }

    brand.deleted = true;
    brand.updatedByPersonId = userId;
    const deleteBrand200Response =
      await this.brandServiceSDK.deleteBrandAsPromise(organizationId, brandId);
    return deleteBrand200Response.result;
  }

  /**
   * Check if the brand is associated with any workspace
   *
   * @param brandId
   * @returns
   */
  async isBrandAssociatedWithWorkspace(brandId: string): Promise<boolean> {
    const associatedWorkspaces = await this.workspaceBrandMapRepository.count({
      where: { brandId },
    });

    return associatedWorkspaces > 0;
  }

  /**
   * Get a brand entity by id for a specific organization. Throws NotFoundException if not found.
   *
   * @param id Find a brand by id
   * @param organizationId Find a brand within a specific organization
   * @returns
   */
  async getBrandEntity(id: string, organizationId: string) {
    const brand = await this.brandRepository.findOneBy({ id, organizationId });

    if (!brand) {
      throw new NotFoundException(
        `Brand with ID ${id} not found in the organization with ID ${organizationId}`,
      );
    }
    return brand;
  }
}
