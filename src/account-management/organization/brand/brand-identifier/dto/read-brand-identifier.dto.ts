import { AutoMap } from '@automapper/classes';

export class ReadBrandIdentifierDto {
  /**
   * System assigned Id of the Brand Identifier
   * @example 123e4567-e89b-12d3-a456-426614174000
   */
  @AutoMap()
  id: string;

  /**
   * System assigned Id of the Brand
   * @example 123e4567-e89b-12d3-a456-426614174000
   */
  @AutoMap()
  brandId: string;

  /**
   * Identifier of the Brand
   * @example "ABC123"
   */
  @AutoMap()
  identifier: string;

  /**
   * Creation date of the Brand Identifier
   */
  @AutoMap()
  dateCreated: Date;

  /**
   * Updated date of the Brand Identifier
   */
  @AutoMap()
  lastUpdated: Date;
}
