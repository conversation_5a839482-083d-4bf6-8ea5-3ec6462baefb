import { Test, TestingModule } from '@nestjs/testing';
import { BrandIdentifierController } from './brand-identifier.controller';
import { BrandIdentifierService } from '../services/brand-identifier.service';
import { CreateBrandIdentifierResultDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createBrandIdentifierResultDto';
import { CreateBrandIdentifierDto } from '../dto/create-brand-identifier.dto';
import { UpdateBrandIdentifierDto } from '../dto/update-brand-identifier.dto';
import { BrandIdentifier } from '../entities/brand-identifier.entity';

describe('BrandIdentifierController', () => {
  let controller: BrandIdentifierController;
  let service: BrandIdentifierService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BrandIdentifierController],
      providers: [
        {
          provide: BrandIdentifierService,
          useValue: {
            createBrandIdentifier: jest.fn().mockResolvedValue(undefined),
            getBrandIdentifierById: jest.fn().mockResolvedValue(undefined),
            deleteBrandIdentifierByIdAndBrandId: jest,
            softDeleteBrandIdentifierById: jest

              .fn()
              .mockResolvedValue(undefined),
            updateBrandIdentifier: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    controller = module.get<BrandIdentifierController>(
      BrandIdentifierController,
    );
    service = module.get<BrandIdentifierService>(BrandIdentifierService);
  });

  describe('createBrandIdentifier', () => {
    it('should create a new Brand Identifier', async () => {
      // Mocking dependencies
      const brandIdentifierDto: CreateBrandIdentifierDto = {
        identifier: 'identifier-value',
      };

      const brandId = '42376154-a6fe-495e-a279-400825694846';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const createdBrandIdentifier: CreateBrandIdentifierResultDto = {
        id: 'brand-identifier-id',
        brandId: '42376154-a6fe-495e-a279-400825694846',
        identifier: 'identifier-value',
        dateCreated: '2023-07-15T16:37:28.000Z',
        lastUpdated: '2023-07-15T16:37:28.000Z',
      }; // Mock the response from the service

      jest
        .spyOn(service, 'createBrandIdentifier')
        .mockResolvedValue(createdBrandIdentifier);

      // Execute the controller method
      const result = await controller.createBrandIdentifier(
        brandId,
        organizationId,
        brandIdentifierDto,
      );

      // Assertions
      expect(service.createBrandIdentifier).toHaveBeenCalledWith(
        brandId,
        brandIdentifierDto.identifier,
        organizationId,
        brandIdentifierDto,
      );

      expect(result).toEqual(createdBrandIdentifier);
    });
  });

  describe('softDeleteBrandIdentifier', () => {
    it('should delete the brand identifier', async () => {
      const id = '987e6543-e21b-34d5-ba98-084716426614';
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      const softDeleteBrandIdentifierByIdSpy = jest.spyOn(
        service,
        'softDeleteBrandIdentifierById',
      );

      await controller.softDeleteBrandIdentifierById(
        organizationId,
        brandId,
        id,
      );

      expect(softDeleteBrandIdentifierByIdSpy).toHaveBeenCalledWith(
        organizationId,
        brandId,
        id,
      );
    });
  });

  describe('updateBrandIdentifier', () => {
    it('should call brandIdentifierService.updateBrand with the correct parameters', async () => {
      const id = '14a1d02a-5f0c-4e37-84fe-vidmob';
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      const updateBrandIdentifier: UpdateBrandIdentifierDto = {
        identifier: 'Pepsi',
      };

      const expectedResult = new BrandIdentifier();
      expectedResult.id = id;
      expectedResult.identifier = '';
      expectedResult.dateCreated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );
      expectedResult.lastUpdated = new Date(
        Date.parse('2023-06-30T20:37:35.000Z'),
      );

      jest
        .spyOn(service, 'updateBrandIdentifier')
        .mockResolvedValue(expectedResult);

      await controller.updateBrandIdentifier(
        organizationId,
        brandId,
        id,
        updateBrandIdentifier,
      );

      expect(service.updateBrandIdentifier).toHaveBeenCalledWith(
        organizationId,
        brandId,
        id,
        updateBrandIdentifier,
      );
    });
  });
});
