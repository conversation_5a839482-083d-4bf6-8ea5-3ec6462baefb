import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, Mapper } from '@automapper/core';
import { Injectable } from '@nestjs/common';
import { BrandIdentifier } from '../entities/brand-identifier.entity';
import { CreateBrandIdentifierResultDto } from '../dto/create-brand-identifier-result.dto';
import { ReadBrandIdentifierDto } from '../dto/read-brand-identifier.dto';
import { DeleteBrandIdentifierDto } from '../dto/delete-brand-identifier.dto';
import { UpdateBrandIdentifierResultDto } from '../dto/update-brand-identifier-result.dto';

@Injectable()
export class BrandIdentifierProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, BrandIdentifier, CreateBrandIdentifierResultDto);

      createMap(mapper, BrandIdentifier, ReadBrandIdentifierDto);

      createMap(mapper, BrandIdentifier, DeleteBrandIdentifierDto);

      createMap(mapper, BrandIdentifier, UpdateBrandIdentifierResultDto);
    };
  }
}
