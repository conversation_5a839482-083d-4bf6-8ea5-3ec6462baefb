import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BrandIdentifier } from '../entities/brand-identifier.entity';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { DeleteBrandIdentifierDto } from '../dto/delete-brand-identifier.dto';
import { UpdateBrandIdentifierDto } from '../dto/update-brand-identifier.dto';
import { BrandService } from '../../../brand/services/brand.service';
import { UUID } from 'crypto';
import { BrandIdentifierService as BrandIdentifierServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { CreateBrandIdentifierDto } from '../dto/create-brand-identifier.dto';

@Injectable()
export class BrandIdentifierService {
  constructor(
    private brandService: BrandService,

    @InjectRepository(BrandIdentifier)
    private brandIdentifierRepository: Repository<BrandIdentifier>,
    private brandIdentifierServiceSDK: BrandIdentifierServiceSDK,

    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * Create a brand
   *
   * @param organizationId
   * @param name
   * @returns
   */
  async createBrandIdentifier(
    brandId: string,
    identifier: string,
    organizationId: string,
    createBrandIdentifierDto: CreateBrandIdentifierDto,
  ) {
    const brandIdentifier = new BrandIdentifier();
    brandIdentifier.brandId = brandId;
    brandIdentifier.identifier = identifier;

    const createBrandIdentifier200Response =
      await this.brandIdentifierServiceSDK.createBrandIdentifierAsPromise(
        brandId,
        identifier,
        organizationId,
        createBrandIdentifierDto,
      );

    return createBrandIdentifier200Response.result;
  }

  /**
   * Soft Delete a brand identifier by its id and brand id
   *
   * @param organizationId
   * @param brandId
   * @param id
   * @returns
   */
  async softDeleteBrandIdentifierById(
    organizationId: UUID,
    brandId: UUID,
    id: UUID,
  ) {
    await this.brandService.getBrandEntity(brandId, organizationId);

    const brandIdentifier = await this.brandIdentifierRepository
      .createQueryBuilder('brandIdentifier')
      .where('brandIdentifier.id = :id', { id: id })
      .andWhere('brandIdentifier.brand_id = :brand_id', { brand_id: brandId })
      .andWhere('brandIdentifier.deleted = :deleted', { deleted: false })
      .getOne();

    if (!brandIdentifier) {
      throw new NotFoundException('Brand Identifier not found');
    }

    brandIdentifier.deleted = true;

    const updatedBrandIdentifier = await this.brandIdentifierRepository.save(
      brandIdentifier,
    );

    return this.classMapper.map(
      updatedBrandIdentifier,
      BrandIdentifier,
      DeleteBrandIdentifierDto,
    );
  }

  /**
   * Update a brand Identifier
   *
   * @param organizationId
   * @param brandId
   * @param id
   * @param updateBrandDto
   * @returns
   */
  async updateBrandIdentifier(
    organizationId: UUID,
    brandId: UUID,
    id: UUID,
    updateBrandIdentifierDto: UpdateBrandIdentifierDto,
  ) {
    await this.brandService.getBrandEntity(brandId, organizationId);
    const brandIdentifier = await this.getBrandIdentifierEntity(id, brandId);

    const { identifier } = updateBrandIdentifierDto;
    brandIdentifier.identifier = identifier;

    const updateBrandIdentifier200Response =
      await this.brandIdentifierServiceSDK.updateBrandIdentifierAsPromise(
        organizationId,
        brandId,
        id,
        identifier,
        updateBrandIdentifierDto,
      );

    return updateBrandIdentifier200Response.result;
  }

  /**
   * Get a brand entity by id. Throws NotFoundException if not found.
   *
   * @param id Find a brand by id
   * @returns brand entity
   */
  private async getBrandIdentifierEntity(id: UUID, brandId: UUID) {
    const brandIdentifier = await this.brandIdentifierRepository.findOneBy({
      id,
      brandId,
    });

    if (!brandIdentifier) {
      throw new NotFoundException(
        `Brand Identifier with ID ${id} not found in the brand with ID ${brandId}`,
      );
    }

    return brandIdentifier;
  }
}
