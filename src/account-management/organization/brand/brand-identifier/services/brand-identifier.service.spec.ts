import { Test, TestingModule } from '@nestjs/testing';
import { BrandIdentifierService } from './brand-identifier.service';
import { Repository } from 'typeorm';
import { Brand } from '../../entities/brand.entity';
import { BrandIdentifier } from '../entities/brand-identifier.entity';
import { CreateBrandIdentifierResultDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/createBrandIdentifierResultDto';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BrandIdentifierProfile } from '../mappers/brand-identifier.profile';
import { getMapperToken } from '@automapper/nestjs';
import { createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { NotFoundException } from '@nestjs/common';
import { UpdateBrandIdentifierDto } from '../dto/update-brand-identifier.dto';
import { DeleteBrandIdentifierDto } from '../dto/delete-brand-identifier.dto';
import { BrandService } from '../../../brand/services/brand.service';
import {
  BrandIdentifierService as BrandIdentifierServiceSDK,
  UpdateBrandIdentifier200Response,
} from '@vidmob/vidmob-organization-service-sdk';
import { CreateBrandIdentifier200Response } from '@vidmob/vidmob-organization-service-sdk/dist/model/createBrandIdentifier200Response';
import { CreateBrandIdentifierDto } from '../dto/create-brand-identifier.dto';

describe('BrandIdentifierService', () => {
  let brandIdentifierService: BrandIdentifierService;
  let brandService: BrandService;
  let brandIdentifierRepository: Repository<BrandIdentifier>;
  let brandIdentifierServiceSDK: BrandIdentifierServiceSDK;

  beforeEach(async () => {
    const moduleRef: TestingModule = await Test.createTestingModule({
      providers: [
        BrandIdentifierService,
        BrandIdentifierServiceSDK,
        {
          provide: BrandService,
          useValue: {
            getBrandEntity: jest.fn(),
          },
        },
        BrandIdentifierProfile,
        {
          provide: getRepositoryToken(BrandIdentifier),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
        {
          provide: BrandIdentifierServiceSDK,
          useFactory: () => ({
            createBrandIdentifierAsPromise: jest.fn(),
            updateBrandIdentifierAsPromise: jest.fn(),
          }),
        },
      ],
    }).compile();

    brandIdentifierService = moduleRef.get<BrandIdentifierService>(
      BrandIdentifierService,
    );
    brandService = moduleRef.get<BrandService>(BrandService);
    brandIdentifierRepository = moduleRef.get<Repository<BrandIdentifier>>(
      getRepositoryToken(BrandIdentifier),
    );
    brandIdentifierServiceSDK = moduleRef.get<BrandIdentifierServiceSDK>(
      BrandIdentifierServiceSDK,
    );
  });

  describe('createBrandIdentifier', () => {
    it('should create a new Brand Identifier', async () => {
      const brandId = '6c1f2ad4-15a7-42be-9be5-980d23c14287';
      const identifier = 'testIdentifier';

      const brandIdentifier = new BrandIdentifier();
      brandIdentifier.brandId = brandId;
      brandIdentifier.identifier = identifier;

      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';
      const brandIdentifierDto = new CreateBrandIdentifierDto();
      const createBrandIdentifierResultDto: CreateBrandIdentifierResultDto = {
        id: '6c1f2ad4-15a7-42be-9be5-980d2372893',
        brandId: '6c1f2ad4-15a7-42be-9be5-980d23c14287',
        identifier: 'testIdentifier',
        dateCreated: '2023-03-03T20:00:00.000Z',
        lastUpdated: '2023-03-03T20:00:00.000Z',
      };

      const expectedResult: CreateBrandIdentifier200Response = {
        status: 'OK',
        result: createBrandIdentifierResultDto,
      };

      jest
        .spyOn(brandIdentifierServiceSDK, 'createBrandIdentifierAsPromise')
        .mockResolvedValueOnce(expectedResult);

      const result = await brandIdentifierService.createBrandIdentifier(
        brandId,
        identifier,
        organizationId,
        brandIdentifierDto,
      );

      expect(result).toBe(expectedResult.result);
    });
  });

  describe('softDeleteBrandIdentifierById', () => {
    it('should soft delete a Brand Identifier', async () => {
      const brandIdentifierId = '6c1f2ad4-15a7-42be-9be5-980d23c14287';
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      const brandIdentifier = new BrandIdentifier();
      brandIdentifier.id = brandIdentifierId;

      const brand = new Brand();
      brand.id = brandId;

      jest
        .spyOn(brandIdentifierRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValueOnce(brandIdentifier),
        } as any);
      jest
        .spyOn(brandIdentifierRepository, 'save')
        .mockResolvedValueOnce(brandIdentifier);

      const result = await brandIdentifierService.softDeleteBrandIdentifierById(
        organizationId,
        brandId,
        brandIdentifierId,
      );

      expect(brandIdentifierRepository.createQueryBuilder).toHaveBeenCalled();
      expect(brandIdentifier.deleted).toBe(true);
      expect(brandIdentifierRepository.save).toHaveBeenCalledWith(
        brandIdentifier,
      );
      expect(result).toBeInstanceOf(DeleteBrandIdentifierDto);
    });

    it('should throw NotFoundException when Brand Identifier not found', async () => {
      const brandIdentifierId = '6c1f2ad4-15a7-42be-9be5-980d23c14287';
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      jest
        .spyOn(brandIdentifierRepository, 'createQueryBuilder')
        .mockReturnValueOnce({
          where: jest.fn().mockReturnThis(),
          andWhere: jest.fn().mockReturnThis(),
          getOne: jest.fn().mockResolvedValueOnce(undefined),
        } as any);

      await expect(
        brandIdentifierService.softDeleteBrandIdentifierById(
          organizationId,
          brandId,
          brandIdentifierId,
        ),
      ).rejects.toThrowError(NotFoundException);
    });
  });

  describe('updateBrandIdentifier', () => {
    it('should update a Brand Identifier', async () => {
      const id = '14a1d02a-5f0c-4e37-84fe-vidmob';
      const updateBrandIdentifierDto: UpdateBrandIdentifierDto = {
        identifier: 'Pepsi',
      };
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      const brandIdentifier = new BrandIdentifier();
      brandIdentifier.id = id;
      brandIdentifier.identifier = 'Old Identifier';

      const expectedResult: UpdateBrandIdentifier200Response = {
        status: 'OK',
        result: {
          identifier: 'Pepsi',
        },
      };

      jest
        .spyOn(brandIdentifierRepository, 'findOneBy')
        .mockResolvedValueOnce(brandIdentifier);

      jest
        .spyOn(brandIdentifierServiceSDK, 'updateBrandIdentifierAsPromise')
        .mockResolvedValueOnce(expectedResult);

      const result = await brandIdentifierService.updateBrandIdentifier(
        organizationId,
        brandId,
        id,
        updateBrandIdentifierDto,
      );

      expect(brandIdentifierRepository.findOneBy).toHaveBeenCalledWith({
        id,
        brandId,
      });
      expect(result).toBe(expectedResult.result);
    });

    it('should throw NotFoundException when brand identifier is not found', async () => {
      const id = '14a1d02a-5f0c-4e37-84fe-vidmob';
      const updateBrandIdentifierDto: UpdateBrandIdentifierDto = {
        identifier: 'Pepsi',
      };
      const brandId = '011fd28b-24fd-4e62-a29b-19525c449046';
      const organizationId = 'daddc0ad-d678-434f-8015-9817f5d39bae';

      jest
        .spyOn(brandIdentifierRepository, 'findOne')
        .mockResolvedValueOnce(null);
      jest
        .spyOn(brandIdentifierRepository, 'findOneBy')
        .mockResolvedValueOnce(null);

      await expect(
        brandIdentifierService.updateBrandIdentifier(
          organizationId,
          brandId,
          id,
          updateBrandIdentifierDto,
        ),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
