import { AutoMap } from '@automapper/classes';
import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Brand } from '../../entities/brand.entity';

@Entity()
export class BrandIdentifier {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @Column({ name: 'brand_id' })
  brandId: string;

  @ManyToOne(() => Brand, (brand) => brand.brandIdentifiers)
  @JoinColumn({ name: 'brand_id' })
  brands: Brand;

  @AutoMap()
  @Column()
  identifier: string;

  @AutoMap()
  @Column({ default: false })
  deleted: boolean;

  @AutoMap()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;

  @AutoMap()
  @UpdateDateColumn({ name: 'last_updated' })
  lastUpdated: Date;
}
