import { User } from '../../../../entities/user.entity';
import {
  En<PERSON>ty,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  ManyToMany,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { BrandIdentifier } from '../brand-identifier/entities/brand-identifier.entity';
import { Workspace } from '../../../../entities/workspace.entity';

@Entity()
export class Brand {
  /**
   * System assigned Id of the Brand
   */
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Name of the Organization
   */
  @AutoMap()
  @Column({ length: 255 })
  name: string;

  /**
   * Description of the Organization
   */
  @AutoMap()
  @Column({ length: 1024 })
  description: string;

  /**
   * Indicates if the Brand is deleted or not
   */
  @AutoMap()
  @Column({ default: false })
  deleted: boolean;

  /**
   * ID of the user who last updated the Brand
   */
  @AutoMap()
  @Column({ name: 'updated_by_person_id', type: 'bigint', nullable: false })
  updatedByPersonId: number;

  /**
   * ID of the user who created the Brand
   */
  @AutoMap()
  @Column({ name: 'created_by_person_id', type: 'bigint', nullable: false })
  createdByPersonId: number;

  /**
   * ID of the Organization to which the Brand belongs
   */
  @AutoMap()
  @Column({
    name: 'organization_id',
    type: 'char',
    length: 36,
    nullable: false,
  })
  organizationId: string;

  /**
   * Creation date of the Brand. This is in the format YYYY-MM-DD.
   */
  @AutoMap()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;

  /**
   * Last updated date of the Brand
   */
  @AutoMap()
  @UpdateDateColumn({ name: 'last_updated' })
  lastUpdated: Date;

  /**
   * The number of workspaces associated with the brand
   */
  @AutoMap()
  @Column({ name: 'workspace_count', type: 'int', default: 0 })
  workspaceCount: number;

  /**
   * Brand associations
   */

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by_person_id', referencedColumnName: 'id' })
  updatedByPerson: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by_person_id', referencedColumnName: 'id' })
  createdByPerson: User;

  @OneToMany(() => BrandIdentifier, (brandIdentifier) => brandIdentifier.brands)
  brandIdentifiers: BrandIdentifier[];

  @AutoMap()
  @ManyToMany(() => Workspace, (workspace) => workspace.brands)
  workspaces: Workspace[];
}
