import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { createMap, forMember, mapFrom, Mapper } from '@automapper/core';
import { Brand } from '../entities/brand.entity';
import { ReadBrandDto } from '../dto/read-brand.dto';
import { CreateBrandResultDto } from '../dto/create-brand-result.dto';

/**
 * Define the mapping between criteria DTOs and entities.
 */
@Injectable()
export class BrandProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  /**
   * A custom profile to define how the automapper should transform criteria. The primary function is to convert the
   * parameters from a string to an object.
   */
  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        Brand,
        ReadBrandDto,
        forMember(
          (dest) => dest.updatedByPerson,
          mapFrom((src) => src.updatedByPerson),
        ),
        forMember(
          (dest) => dest.createdByPerson,
          mapFrom((src) => src.createdByPerson),
        ),
      );

      createMap(mapper, Brand, CreateBrandResultDto);

      createMap(mapper, ReadBrandDto, ReadBrandDto);
    };
  }
}
