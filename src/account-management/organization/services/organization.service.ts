import { Injectable } from '@nestjs/common';
import {
  OrganizationUserService,
  OrganizationService as OrganizationServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { OrganizationWithWorkspaceCountDto } from '../dto/organization-with-workspace-count.dto';

@Injectable()
export class OrganizationService {
  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationService: OrganizationServiceSdk,
  ) {}

  async getOrgFeatureRecord(organizationId: string, features: string[]) {
    return await this.organizationService.getOrganizationWhitelistFeatureEndpointAsPromise(
      organizationId,
      features,
    );
  }

  async findAllByUserId(
    userId: number,
    search?: string,
    paginationOptions?: PaginationOptions,
  ): Promise<PaginatedResultArray<OrganizationWithWorkspaceCountDto>> {
    const response =
      await this.organizationUserService.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise(
        userId,
        search || '',
        paginationOptions?.offset,
        paginationOptions?.perPage,
        paginationOptions?.queryId,
      );

    return new PaginatedResultArray(
      response.result as unknown as OrganizationWithWorkspaceCountDto[],
      response.pagination?.totalSize,
    );
  }
}
