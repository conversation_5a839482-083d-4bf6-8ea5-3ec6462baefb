import { Injectable } from '@nestjs/common';
import {
  OrganizationUserService,
  OrganizationService as OrganizationServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Organization } from '../../../entities/organization.entity';
import { OrganizationWithWorkspaceCountDto } from '../dto/organization-with-workspace-count.dto';
import { DEFAULT_SESSION_TIMEOUT_MIN } from '../constants/organization.constants';

@Injectable()
export class OrganizationService {
  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationService: OrganizationServiceSdk,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
  ) {}

  async getOrgFeatureRecord(organizationId: string, features: string[]) {
    return await this.organizationService.getOrganizationWhitelistFeatureEndpointAsPromise(
      organizationId,
      features,
    );
  }

  async findAllByUserId(
    userId: number,
    search?: string,
    paginationOptions?: PaginationOptions,
  ): Promise<PaginatedResultArray<OrganizationWithWorkspaceCountDto>> {
    const response =
      await this.organizationUserService.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise(
        userId,
        search || '',
        paginationOptions?.offset,
        paginationOptions?.perPage,
        paginationOptions?.queryId,
      );

    // TODO: Once the SDK is updated to include sessionTimeoutMin in the response,
    // this additional query can be removed and we can return the response directly

    // Check if SDK already includes sessionTimeoutMin
    if (response.result.length > 0 && 'sessionTimeoutMin' in response.result[0]) {
      // SDK already includes sessionTimeoutMin, return as-is
      return new PaginatedResultArray(
        response.result as unknown as OrganizationWithWorkspaceCountDto[],
        response.pagination?.totalSize,
      );
    }

    // Fallback: Fetch session timeout information separately
    // This can be removed once SDK is updated
    const organizationIds = response.result.map((org: any) => org.id);

    if (organizationIds.length === 0) {
      return new PaginatedResultArray([], 0);
    }

    const organizations = await this.organizationRepository.find({
      where: { id: In(organizationIds) },
      select: ['id', 'sessionTimeoutMin'],
    });

    const sessionTimeoutMap = new Map(
      organizations.map((org) => [org.id, org.sessionTimeoutMin]),
    );

    const enrichedResult = response.result.map((org: any) => ({
      ...org,
      sessionTimeoutMin: sessionTimeoutMap.get(org.id) ?? DEFAULT_SESSION_TIMEOUT_MIN,
    }));

    return new PaginatedResultArray(
      enrichedResult,
      response.pagination?.totalSize,
    );
  }
}
