import { Injectable } from '@nestjs/common';
import {
  OrganizationUserService,
  OrganizationService as OrganizationServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Organization } from '../../../entities/organization.entity';
import { OrganizationWithWorkspaceCountDto } from '../dto/organization-with-workspace-count.dto';

@Injectable()
export class OrganizationService {
  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly organizationService: OrganizationServiceSdk,
    @InjectRepository(Organization)
    private readonly organizationRepository: Repository<Organization>,
  ) {}

  async getOrgFeatureRecord(organizationId: string, features: string[]) {
    return await this.organizationService.getOrganizationWhitelistFeatureEndpointAsPromise(
      organizationId,
      features,
    );
  }

  async findAllByUserId(
    userId: number,
    search?: string,
    paginationOptions?: PaginationOptions,
  ): Promise<PaginatedResultArray<OrganizationWithWorkspaceCountDto>> {
    const response =
      await this.organizationUserService.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise(
        userId,
        search || '',
        paginationOptions?.offset,
        paginationOptions?.perPage,
        paginationOptions?.queryId,
      );

    // Get organization IDs from the response
    const organizationIds = response.result.map((org: any) => org.id);

    // Fetch session timeout information for these organizations
    const organizations = await this.organizationRepository.find({
      where: { id: In(organizationIds) },
      select: ['id', 'sessionTimeoutMin'],
    });

    // Create a map for quick lookup
    const sessionTimeoutMap = new Map(
      organizations.map((org) => [org.id, org.sessionTimeoutMin]),
    );

    // Merge the data
    const enrichedResult = response.result.map((org: any) => ({
      ...org,
      sessionTimeoutMin: sessionTimeoutMap.get(org.id) ?? 60, // Default to 60 if not found
    }));

    return new PaginatedResultArray(
      enrichedResult,
      response.pagination?.totalSize,
    );
  }
}
