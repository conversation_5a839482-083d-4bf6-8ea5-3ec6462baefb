import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationService } from './organization.service';
import {
  OrganizationUserService as OrganizationUserServiceSdk,
  OrganizationService as OrganizationServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { mockData } from '../mocks/list-organization.mock';

const mockOrganizationUserServiceSdk = {
  findUserAssociatedOrganizationsAndWorkspaceCountAsPromise: jest.fn(() =>
    Promise.resolve({
      result: mockData.result,
      pagination: { totalSize: mockData.result.length },
    }),
  ),
};

describe('OrganizationService', () => {
  let service: OrganizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationService,
        {
          provide: OrganizationUserServiceSdk,
          useValue: mockOrganizationUserServiceSdk,
        },
        {
          provide: OrganizationServiceSdk,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<OrganizationService>(OrganizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should fetch organizations by user id', async () => {
    const mockUserId = 12345;
    const mockSearch = 'Dyna Corp';
    const mockPaginationOptions = {
      offset: 0,
      perPage: 2,
    };

    await service.findAllByUserId(
      mockUserId,
      mockSearch,
      mockPaginationOptions,
    );

    expect(
      mockOrganizationUserServiceSdk.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise,
    ).toBeCalledWith(
      mockUserId,
      mockSearch,
      mockPaginationOptions.offset,
      mockPaginationOptions.perPage,
      undefined,
    );
  });
});
