import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationService } from './organization.service';
import {
  OrganizationUserService as OrganizationUserServiceSdk,
  OrganizationService as OrganizationServiceSdk,
} from '@vidmob/vidmob-organization-service-sdk';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { mockData } from '../mocks/list-organization.mock';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Organization } from '../../../entities/organization.entity';
import { Repository } from 'typeorm';

const mockOrganizationUserServiceSdk = {
  findUserAssociatedOrganizationsAndWorkspaceCountAsPromise: jest.fn(() =>
    Promise.resolve({
      result: mockData.result,
      pagination: { totalSize: mockData.result.length },
    }),
  ),
};

const mockOrganizationRepository = {
  find: jest.fn(() =>
    Promise.resolve([
      { id: '0c598994-f0db-413a-8089-2c498bfe485e', sessionTimeoutMin: 60 },
      { id: 'bc5330e7-0dfa-4abc-8205-e23a3a733952', sessionTimeoutMin: 120 },
      { id: '27a7e882-43de-4bfa-8f53-3b62875b8432', sessionTimeoutMin: 90 },
      { id: '000b85cc-7648-49c1-84cc-555bc19a9241', sessionTimeoutMin: null },
      { id: '5959b5b6-3a70-4dab-98e1-5df15965fe49', sessionTimeoutMin: 45 },
    ]),
  ),
};

describe('OrganizationService', () => {
  let service: OrganizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationService,
        {
          provide: OrganizationUserServiceSdk,
          useValue: mockOrganizationUserServiceSdk,
        },
        {
          provide: OrganizationServiceSdk,
          useValue: {},
        },
        {
          provide: getRepositoryToken(Organization),
          useValue: mockOrganizationRepository,
        },
      ],
    }).compile();

    service = module.get<OrganizationService>(OrganizationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should fetch organizations by user id', async () => {
    const mockUserId = 12345;
    const mockSearch = 'Dyna Corp';
    const mockPaginationOptions = {
      offset: 0,
      perPage: 2,
    };

    await service.findAllByUserId(
      mockUserId,
      mockSearch,
      mockPaginationOptions,
    );

    expect(
      mockOrganizationUserServiceSdk.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise,
    ).toBeCalledWith(
      mockUserId,
      mockSearch,
      mockPaginationOptions.offset,
      mockPaginationOptions.perPage,
      undefined,
    );
  });
});
