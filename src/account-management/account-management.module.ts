import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MarketController } from './workspace/market/controllers/market.controller';
import { BrandController } from './organization/brand/controllers/brand.controller';
import { WorkspaceBrandController } from './workspace/brand/controllers/workspace-brand.controller';
import { MarketService } from './workspace/market/services/market.service';
import { BrandService } from './organization/brand/services/brand.service';
import { WorkspaceBrandService } from './workspace/brand/services/workspace-brand.service';
import { WorkspaceBrandProfile } from './workspace/brand/mappers/workspace-brand.profile';
import { BrandProfile } from './organization/brand/mappers/brand.profile';
import { Brand as BrandOrganization } from './organization/brand/entities/brand.entity';
import { User } from '../entities/user.entity';
import { Workspace } from '../entities/workspace.entity';
import { Market } from '../entities/market.entity';
import { WorkspaceService } from './organization/workspace/services/workspace.service';
import { WorkspaceController } from './organization/workspace/controllers/workspace.controller';
import { WorkspaceProfile } from './organization/workspace/mappers/workspace.profile';
import { BrandIdentifierController } from './organization/brand/brand-identifier/controllers/brand-identifier.controller';
import { BrandIdentifier } from './organization/brand/brand-identifier/entities/brand-identifier.entity';
import { BrandIdentifierService } from './organization/brand/brand-identifier/services/brand-identifier.service';
import { BrandIdentifierProfile } from './organization/brand/brand-identifier/mappers/brand-identifier.profile';
import { WorkspaceBrandMap } from './workspace/brand/entities/workspace-brand-map.entity';
import { HttpModule } from '@nestjs/axios';
import { AdAccountController } from './organization/ad-account/controllers/ad-account.controller';
import { AdAccountService } from './organization/ad-account/services/ad-account.service';
import { MediaController } from './media/controllers/media.controller';
import { MediaService } from './media/services/media.service';
import { OrganizationAdAccountController } from './organization/ad-account/controllers/organization-ad-account.controller';
import { AuthService } from '../auth/services/auth.service';
import { OrganizationUserModule } from './organization/organization-user/organization-user.module';
import { WorkspaceAdAccountController } from './workspace/ad-account/controller/workspace-ad-account.controller';
import { WorkspaceAdAccountService } from './workspace/ad-account/services/workspace-ad-account.service';
import { WorkspaceAdAccountProfile } from './workspace/ad-account/mapper/workspace-ad-account.profile';
import { AnalyticsService } from './organization/ad-account/services/analytics-service.service';
import { WorkspaceAllController } from './account-management.controller';
import { OrganizationController } from './organization/organization.controller';
import { OrganizationService } from './organization/services/organization.service';
import { OrganizationInviteModule } from './organization/organization-invite/organization-invite.module';
import { WorkspaceUserModule } from './workspace/workspace-user/workspace-user.module';
import { SsoModule } from './sso/sso.module';
import { IndustryController } from './organization/industry/controllers/industry.controller';
import { IndustryService } from './organization/industry/services/industry.service';
import { FeaturesController } from './features/controllers/features.controller';
import { FeaturesService } from './features/services/features.service';
import { UserModule } from './user/user.module';
import { UserController } from './user/user.controller';

@Module({
  controllers: [
    MarketController,
    BrandController,
    WorkspaceController,
    WorkspaceBrandController,
    BrandIdentifierController,
    AdAccountController,
    WorkspaceAdAccountController,
    MediaController,
    OrganizationController,
    OrganizationAdAccountController,
    WorkspaceAllController,
    IndustryController,
    FeaturesController,
  ],
  providers: [
    OrganizationService,
    MarketService,
    BrandService,
    WorkspaceService,
    WorkspaceBrandService,
    WorkspaceProfile,
    BrandProfile,
    WorkspaceAdAccountService,
    WorkspaceAdAccountProfile,
    WorkspaceBrandProfile,
    BrandIdentifierService,
    BrandIdentifierProfile,
    AdAccountService,
    AnalyticsService,
    MediaService,
    AuthService,
    IndustryService,
    FeaturesService,
  ],
  imports: [
    TypeOrmModule.forFeature([
      Workspace,
      Market,
      BrandOrganization,
      User,
      WorkspaceBrandMap,
      BrandIdentifier,
    ]),
    HttpModule,
    OrganizationUserModule,
    OrganizationInviteModule,
    WorkspaceUserModule,
    SsoModule,
    UserModule,
  ],
  exports: [AdAccountService, BrandService, WorkspaceService],
})
export class AccountManagementModule {}
