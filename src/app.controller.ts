import { Controller, Get, Logger, Query, Request } from '@nestjs/common';
import { AppService } from './app.service';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import { UserResponseDto } from './dto/user-response.dto';
import { Public } from './auth/decorators/public.decorator';
import {
  partner<PERSON>rom<PERSON>ueryHandler,
  Permissions,
  projectFromQueryHandler,
} from './auth/decorators/permission.decorator';
import { PermissionAction } from './auth/enums/permission.action.enum';
import { PermissionSubResource } from './auth/enums/permission.subresource.enum';
import { PermissionDomain } from './auth/enums/permission.domain.enum';

@ApiTags('Test')
@ApiSecurity('Bearer Token')
@Controller()
export class AppController {
  private readonly logger = new Logger(AppController.name);

  constructor(private readonly appService: AppService) {}

  /**
   * Decorator Public will become this endpoint public, for instance used for health check or login
   * @returns text
   */
  @Public()
  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  /**
   * This endpoint will check permission for a Workspace (commonly known as partner), which is passed in request but got by  domainContextHandler.
   * After getting the permission, the attributes from legacy will be represented by: type (action),
   * resource (@Permissions ({ domain: PermissionDomain.WORKSPACE, ...}) ) and subresource feature/data to be accessed.
   * @returns text
   */
  @Get('helloPartner')
  @Permissions({
    domain: PermissionDomain.WORKSPACE,
    domainContextHandler: partnerFromQueryHandler,
    required: [
      {
        action: PermissionAction.READ,
        subresource: PermissionSubResource.DETAILS,
      },
      {
        action: PermissionAction.CREATE,
        subresource: PermissionSubResource.USER_INVITE,
      },
    ],
  })
  getHelloPartner(@Query('partnerId') partnerId: string): string {
    return this.appService.getHello() + ' Partner ID ' + partnerId;
  }

  /**
   * This endpoint will check permission for a Workspace (commonly known as partner), which is passed in request but got by  domainContextHandler.
   * After getting the permission, the attributes from legacy will be represented by: type (action),
   * resource (@Permissions ({ domain: PermissionDomain.WORKSPACE, ...}) ) and subresource feature/data to be accessed.
   * @returns text
   */
  @Get('helloProject')
  @Permissions({
    domain: PermissionDomain.PROJECT,
    domainContextHandler: projectFromQueryHandler,
    required: [
      {
        action: PermissionAction.READ,
        subresource: PermissionSubResource.PROJECT,
      },
    ],
  })
  getHelloProject(@Query('projectId') projectId: string): string {
    return this.appService.getHello() + ' Project ID ' + projectId;
  }

  /**
   * Without @Public decorator all requests to this endpoint will be checked for permission based on Auth Bearer Token
   */
  @Get('user')
  async getUser(@Request() req: any): Promise<UserResponseDto> {
    const { userId, username, authorities } = req;
    this.logger.log(
      `Test called with user ${userId} ${username} authorities=${JSON.stringify(
        authorities,
      )}`,
    );
    return {
      id: userId,
      username,
      authorities,
    };
  }
}
