import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { Person } from './person.entity';
import { Partner } from './partner.entity';
import { ReportFilter } from './report-filter.entity';
import { AutoMap } from '@automapper/classes';
import { ScoringReportType } from '../reports/model/report';
import { AnalyticsReportType } from '../analytics/saved-report/model/analytics-report';
import { Organization } from './organization.entity';
import { ReportWorkspaceMap } from './report-workspace-map.entity';
import { ReportPlatformAccountMap } from './report-platform-account-map.entity';
import { ReportPlatformAdAccountMap } from './report-platform-ad-account-map.entity';
import { ReportChannelMap } from './report-channel.map.entity';
import { Exclude } from 'class-transformer';

@Entity('report')
export class Report {
  @AutoMap()
  @PrimaryColumn({ type: 'char', length: 36 })
  id: string;

  @AutoMap()
  @Column({ type: 'char', length: 255, nullable: true })
  name: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  description: string;

  @AutoMap()
  @ManyToOne(() => Person)
  @JoinColumn({ name: 'owner_id' })
  owner: Person;

  @AutoMap()
  @Column({ name: 'organization_id', type: 'char', length: 36, nullable: true })
  organizationId?: string;

  @AutoMap()
  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'organization_id' })
  organization?: Organization;

  @AutoMap()
  @Column({ name: 'partner_id', type: 'bigint', nullable: true })
  partnerId?: number;

  @AutoMap()
  @OneToOne(() => Partner)
  @JoinColumn({ name: 'partner_id' })
  partner?: Partner;

  @AutoMap()
  @OneToOne(() => ReportFilter, {
    cascade: ['insert', 'update'],
  })
  @JoinColumn({ name: 'filter_id' })
  filter: ReportFilter;

  @AutoMap()
  @Column({ type: 'boolean', default: false })
  shared: boolean;

  @AutoMap()
  @Column({ name: 'report_type', type: 'char', length: 255 })
  reportType: ScoringReportType | AnalyticsReportType;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @Column({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @AutoMap()
  @ManyToOne(() => Person)
  @JoinColumn({ name: 'last_updated_by_user_id' })
  lastUpdatedByUser: Person;

  @AutoMap()
  @Column({ type: 'boolean', default: false })
  deleted: boolean;

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'id' })
  @OneToMany(
    () => ReportWorkspaceMap,
    (reportWorkspaceMap) => reportWorkspaceMap.report,
  )
  workspaceMaps?: ReportWorkspaceMap[];

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'id' })
  @OneToMany(
    () => ReportPlatformAccountMap,
    (reportPlatformAccountMap) => reportPlatformAccountMap.report,
  )
  platformAccountMaps?: ReportPlatformAccountMap[]; // deprecated

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'id' })
  @OneToMany(
    () => ReportPlatformAdAccountMap,
    (reportPlatformAdAccountMap) => reportPlatformAdAccountMap.report,
  )
  platformAdAccountMaps?: ReportPlatformAdAccountMap[];

  @AutoMap()
  @Exclude()
  @JoinColumn({ name: 'id' })
  @OneToMany(
    () => ReportChannelMap,
    (reportChannelMap) => reportChannelMap.report,
  )
  channelMaps?: ReportChannelMap[];
}
