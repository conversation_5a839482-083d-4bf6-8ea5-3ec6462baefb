import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { Person } from './person.entity';

@Entity('person_organization_map')
export class PersonOrganizationMap {
  @PrimaryColumn({ type: 'varchar', name: 'organization_id' })
  organizationId: string;

  @PrimaryColumn({ type: 'bigint', name: 'person_id' })
  userId: number;

  @ManyToOne(() => Person)
  @JoinColumn({ name: 'person_id' })
  person: Person;
}
