import { DashboardSharingScope } from '../executive-dashboard/constants/constants';
import {
  Entity,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DashboardWidget } from './dashboard-widget.entity';
import { Person } from './person.entity';
import { ReportFilter } from './report-filter.entity';
import { DashboardUserConfig } from './dashboard-user-config.entity';

@Entity({ name: 'dashboard' })
export class Dashboard {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('uuid', { name: 'organization_id' })
  organizationId: string;

  @Column('varchar', { length: 255 })
  name: string;

  @Column('text', { nullable: true })
  description?: string;

  @Column('char', { length: 36, name: 'source_dashboard_id', nullable: true })
  sourceDashboardId?: string;

  @ManyToOne(() => Dashboard, { nullable: true, onDelete: 'SET NULL' })
  @JoinColumn({ name: 'source_dashboard_id' })
  sourceDashboard?: Dashboard;

  @Column('char', { length: 36, name: 'dashboard_filter_id', nullable: true })
  dashboardFilterId?: string;

  @ManyToOne(() => ReportFilter, { nullable: true })
  @JoinColumn({ name: 'dashboard_filter_id' })
  dashboardFilter?: ReportFilter;

  @Column({
    name: 'sharing_scope',
    type: 'enum',
    enum: DashboardSharingScope,
    default: DashboardSharingScope.PRIVATE,
  })
  sharingScope: DashboardSharingScope;

  @CreateDateColumn({
    name: 'date_created',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  dateCreated: Date;

  @UpdateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  lastUpdated: Date;

  @Column('bigint', { name: 'created_by' })
  createdBy: number;

  @Column('bigint', { name: 'last_modified_by' })
  lastModifiedBy: number;

  @ManyToOne(() => Person)
  @JoinColumn({ name: 'created_by' })
  createdByPerson?: Person;

  @ManyToOne(() => Person)
  @JoinColumn({ name: 'last_modified_by' })
  lastModifiedByPerson?: Person;

  @Column('tinyint', { width: 1, name: 'is_deleted', default: () => 0 })
  isDeleted: boolean;

  @Column('timestamp', { name: 'delete_date', nullable: true })
  deleteDate?: Date;

  @Column('int', { default: () => 1 })
  version: number;

  @OneToMany(() => DashboardUserConfig, (cfg) => cfg.dashboard)
  dashboardUserConfigs: DashboardUserConfig[];

  @OneToMany(() => DashboardWidget, (w) => w.dashboard)
  widgets: DashboardWidget[];
}
