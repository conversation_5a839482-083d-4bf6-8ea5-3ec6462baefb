import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Report } from './report.entity';

@Entity('report_workspace_map')
export class ReportWorkspaceMap {
  @AutoMap()
  @PrimaryColumn({ name: 'report_id', type: 'char', length: 36 })
  reportId: string;

  @AutoMap()
  @JoinColumn({ name: 'report_id' })
  @ManyToOne(() => Report)
  report: Report;

  @AutoMap()
  @PrimaryColumn({
    type: 'bigint',
    nullable: false,
    name: 'workspace_id',
    transformer: { to: (value) => value, from: (value) => parseInt(value) },
  })
  workspaceId: number;
}
