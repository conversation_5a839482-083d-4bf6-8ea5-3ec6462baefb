import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { PlatformAdAccount } from './platform-ad-account.entity';
import { Report } from './report.entity';

@Entity('report_platform_ad_account_map')
export class ReportPlatformAdAccountMap {
  @AutoMap()
  @PrimaryColumn({ name: 'report_id', type: 'char', length: 36 })
  reportId: string;

  @AutoMap()
  @PrimaryColumn({ name: 'platform_ad_account_id', type: 'bigint' })
  platformAdAccountId: number;

  @AutoMap()
  @ManyToOne(() => Report)
  @JoinColumn({
    name: 'report_id',
    referencedColumnName: 'id',
  })
  report: Report;

  @AutoMap()
  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({
    name: 'platform_ad_account_id',
    referencedColumnName: 'id',
  })
  platformAdAccount: PlatformAdAccount;
}
