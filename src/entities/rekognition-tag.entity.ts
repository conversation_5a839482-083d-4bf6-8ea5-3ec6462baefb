import { Entity, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity('tags_rekognition')
export class RekognitionTag {
  @AutoMap()
  @PrimaryColumn({ name: 'label', type: 'varchar', nullable: false })
  label: string;

  @AutoMap()
  @PrimaryColumn({ name: 'category', type: 'varchar', nullable: false })
  category: string;

  @AutoMap()
  @PrimaryColumn({ name: 'version', type: 'int', nullable: false })
  version: number;
}
