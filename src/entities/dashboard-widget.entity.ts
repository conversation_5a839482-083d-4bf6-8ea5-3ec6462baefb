// src/executive-dashboard/dashboard/entities/dashboard-widget.entity.ts

import {
  Entity,
  PrimaryColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Dashboard } from './dashboard.entity';
import { Person } from './person.entity';
import {
  ViewByPeriods,
  VisualizationType,
  WidgetType,
} from '../executive-dashboard/dashboard-types';
import { ReportFilter } from './report-filter.entity';

@Entity({ name: 'dashboard_widget' })
export class DashboardWidget {
  @PrimaryColumn('char', { length: 36 })
  id: string;

  @Column('char', { length: 36, name: 'dashboard_id' })
  dashboardId: string;

  @ManyToOne(() => Dashboard, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'dashboard_id' })
  dashboard: Dashboard;

  @Column('char', { length: 36, name: 'organization_id' })
  organizationId: string;

  @Column('varchar', { length: 128, name: 'widget_type' })
  widgetType: WidgetType;

  @Column('varchar', { length: 255, nullable: true })
  name?: string;

  @Column('text', { nullable: true })
  description?: string;

  @Column('varchar', { length: 128, name: 'visualization_type' })
  visualizationType: VisualizationType;

  @Column('json', { nullable: true })
  parameters?: Record<string, unknown>;

  @Column('int', { name: 'grid_x' })
  gridX: number;

  @Column('int', { name: 'grid_y' })
  gridY: number;

  @Column('int', { name: 'grid_width' })
  gridWidth: number;

  @Column('int', { name: 'grid_height' })
  gridHeight: number;

  @Column('char', { length: 36, name: 'filter_id', nullable: true })
  filterId?: string;

  @ManyToOne(() => ReportFilter, { nullable: true })
  @JoinColumn({ name: 'filter_id' })
  filter?: ReportFilter;

  @Column('tinyint', {
    width: 1,
    name: 'is_compare_to_previous_period_enabled',
    default: () => 0,
  })
  isCompareToPreviousPeriodEnabled: boolean;

  @Column('tinyint', {
    width: 1,
    name: 'is_view_data_labels_enabled',
    default: () => 0,
  })
  isViewDataLabelsEnabled: boolean;

  @Column('tinyint', {
    width: 1,
    name: 'is_include_total_enabled',
    default: () => 0,
  })
  isIncludeTotalEnabled: boolean;

  @Column('tinyint', {
    width: 1,
    name: 'is_kpi_lift_enabled',
    default: () => 0,
  })
  isKpiLiftEnabled: boolean;

  @CreateDateColumn({ name: 'date_created', type: 'timestamp' })
  dateCreated: Date;

  @UpdateDateColumn({
    name: 'last_updated',
    type: 'timestamp',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  lastUpdated: Date;

  // Keep the raw FK column for assignments:
  @Column('bigint', { name: 'created_by' })
  createdById: number;

  // “Computed” relation so you can load Person when needed:
  @ManyToOne(() => Person, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'created_by' })
  createdBy: Person;

  @Column('bigint', { name: 'last_modified_by' })
  lastModifiedById: number;

  @ManyToOne(() => Person, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'last_modified_by' })
  lastModifiedBy: Person;

  @Column('tinyint', { width: 1, name: 'is_deleted', default: () => 0 })
  isDeleted: boolean;

  @Column('timestamp', { name: 'delete_date', nullable: true })
  deleteDate?: Date;

  @Column('int', { default: () => 1 })
  version: number;

  @Column('enum', {
    enum: ViewByPeriods,
    nullable: true,
    name: 'view_by_period',
  })
  viewByPeriod?: ViewByPeriods;
}
