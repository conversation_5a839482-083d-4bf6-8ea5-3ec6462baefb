import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { PersonOrganizationMap } from './person-organization-map.entity';

@Entity('person')
export class Person {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  username: string;

  @Column({ name: 'first_name', type: 'varchar', nullable: true })
  firstName: string;

  @Column({ name: 'last_name', type: 'varchar', nullable: true })
  lastName: string;

  @Column({ name: 'email', type: 'varchar', nullable: false })
  email: string;

  @Column({ name: 'photo', type: 'varchar', nullable: true })
  photo: string;

  @Column({ name: 'display_name', type: 'varchar', nullable: true })
  displayName: string;

  @OneToMany(
    () => PersonOrganizationMap,
    (personOrganizationMap) => personOrganizationMap.person,
  )
  personOrganizationMaps: PersonOrganizationMap[];
}
