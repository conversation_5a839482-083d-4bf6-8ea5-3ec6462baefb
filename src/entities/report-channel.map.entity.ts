import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Report } from './report.entity';

@Entity('report_channel_map')
export class ReportChannelMap {
  @AutoMap()
  @PrimaryColumn({ name: 'report_id', type: 'char', length: 36 })
  reportId: string;

  @AutoMap()
  @JoinColumn({ name: 'report_id' })
  @ManyToOne(() => Report)
  report: Report;

  @AutoMap()
  @PrimaryColumn({
    type: 'varchar',
    length: 100,
    nullable: false,
    name: 'channel',
  })
  channel: string;
}
