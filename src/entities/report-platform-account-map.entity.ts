import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { AutoMap } from "@automapper/classes";
import { Report } from "./report.entity";
import { PlatformAdAccount } from "./platform-ad-account.entity";

/**
 * @deprecated ReportPlatformAccountMap is deprecated. Use ReportPlatformAdAccountMap instead.
 */
@Entity('report_platform_account_map')
export class ReportPlatformAccountMap {
  @AutoMap()
  @PrimaryColumn({ name: 'report_id', type: 'char', length: 36 })
  reportId: string;

  @AutoMap()
  @JoinColumn({ name: 'report_id' })
  @ManyToOne(() => Report)
  report: Report;

  @AutoMap()
  @PrimaryColumn({
    type: 'varchar',
    nullable: false,
    name: 'platform_account_id',
  })
  platformAccountId: string;

  @AutoMap()
  @ManyToOne(() => PlatformAdAccount)
  @JoinColumn({
    name: 'platform_account_id',
    referencedColumnName: 'platform_account_id',
  })
  platformAdAccount: PlatformAdAccount;

  @AutoMap()
  get platformAccountName(): string {
    return this.platformAdAccount?.name;
  }
}
