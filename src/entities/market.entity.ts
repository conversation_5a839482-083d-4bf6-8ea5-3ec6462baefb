import { <PERSON><PERSON>ty, <PERSON>umn, PrimaryColumn, ManyToMany } from 'typeorm';
import { Workspace } from './workspace.entity';
import { AutoMap } from '@automapper/classes';

@Entity('country')
export class Market {
  @AutoMap()
  @PrimaryColumn({ name: 'iso_code', length: 3 })
  isoCode: string;

  @AutoMap()
  @Column({ name: 'name', length: 100, unique: true })
  name: string;

  @AutoMap()
  @ManyToMany(() => Workspace, (workspace) => workspace.markets)
  workspaces: Workspace[];
}
