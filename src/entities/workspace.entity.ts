import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  ManyToMany,
  JoinTable,
} from 'typeorm';
import { Market } from './market.entity';
import { User } from './user.entity';
import { AutoMap } from '@automapper/classes';
import { Expose } from 'class-transformer';
import { Brand } from '../account-management/organization/brand/entities/brand.entity';

@Entity('partner')
export class Workspace {
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column({ name: 'name', type: 'varchar', length: 100, nullable: false })
  name: string;

  @AutoMap()
  @Column({ name: 'is_primary', type: 'tinyint', nullable: true })
  isPrimary: boolean;

  @Column({ name: 'organization_id', type: 'varchar', length: 100 })
  organizationId: string;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
  })
  dateCreated: Date;

  @AutoMap()
  @Expose()
  get totalUser(): number {
    return this.users.length;
  }

  @AutoMap()
  @ManyToMany(() => Market, (market) => market.workspaces)
  @JoinTable({
    name: 'workspace_country_map',
    joinColumns: [{ name: 'partner_id' }],
    inverseJoinColumns: [{ name: 'iso_code' }],
  })
  markets: Market[];

  @AutoMap()
  @ManyToMany(() => Brand, (brand) => brand.workspaces)
  @JoinTable({
    name: 'workspace_brand_map',
    joinColumns: [{ name: 'partner_id' }],
    inverseJoinColumns: [{ name: 'brand_id' }],
  })
  brands: Brand[];

  @OneToMany(() => User, (user) => user.workspace)
  users: User[];
}
