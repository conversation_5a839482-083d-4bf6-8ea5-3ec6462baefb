import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Dashboard } from './dashboard.entity';
import { Person } from './person.entity';

@Entity('dashboard_user_config')
export class DashboardUserConfig {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('char', { length: 36, name: 'dashboard_id' })
  dashboardId: string;

  @ManyToOne(() => Dashboard, (dashboard) => dashboard.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'dashboard_id' })
  dashboard: Dashboard;

  @Column('bigint', { name: 'user_id' })
  userId: number;

  @ManyToOne(() => Person)
  @JoinColumn({ name: 'user_id' })
  user: Person;

  @Column('tinyint', { width: 1, name: 'is_favorite', default: () => 0 })
  isFavorite: boolean;
}
