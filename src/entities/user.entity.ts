import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { Workspace } from './workspace.entity';

@Entity('person')
export class User {
  @AutoMap()
  @PrimaryGeneratedColumn()
  id: number;

  @AutoMap()
  @Column()
  username: string;

  @AutoMap()
  @Column({ name: 'first_name', length: 255 })
  firstName: string;

  @AutoMap()
  @Column({ name: 'last_name', length: 255 })
  lastName: string;

  @ManyToOne(() => Workspace, (workspace) => workspace.users)
  @JoinColumn({ name: 'partner_id' })
  workspace: Workspace[];
}
