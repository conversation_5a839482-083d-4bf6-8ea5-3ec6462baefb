import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { AutoMap } from '@automapper/classes';
import { DEFAULT_SESSION_TIMEOUT_MIN } from '../account-management/organization/constants/organization.constants';

@Entity()
export class Organization {
  /**
   * System assigned Id of the Organization
   */
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Name of the Organization
   */
  @AutoMap()
  @Column({ length: 255 })
  name: string;

  /**
   * Status of the Organization
   */
  @AutoMap()
  @Column({ length: 50 })
  status: string;

  /**
   * Creation date of the Org. This is of the format YYYY-MM-DD
   */
  @AutoMap()
  @CreateDateColumn({ name: 'date_created' })
  dateCreated: Date;

  /**
   * Updated date of the Org
   */
  @AutoMap()
  @UpdateDateColumn({ name: 'last_updated' })
  lastUpdated: Date;

  /**
   * ACS session timeout in minutes
   */
  @AutoMap()
  @Column({
    name: 'session_timeout_min',
    type: 'int',
    nullable: false,
    default: DEFAULT_SESSION_TIMEOUT_MIN,
  })
  sessionTimeoutMin: number;
}
