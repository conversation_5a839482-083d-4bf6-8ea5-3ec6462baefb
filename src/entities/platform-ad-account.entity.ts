import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('platform_ad_account')
export class PlatformAdAccount {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'platform', length: 50 })
  platform: string;

  @Column({ name: 'platform_account_id', length: 100 })
  platform_account_id: string;

  @Column({ name: 'platform_account_name', length: 255 })
  name: string;
}
