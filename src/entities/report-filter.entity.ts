import {
  <PERSON><PERSON><PERSON>,
  Colum<PERSON>,
  <PERSON>To<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Person } from './person.entity';
import { AutoMap } from '@automapper/classes';

@Entity('report_filter')
export class ReportFilter {
  @AutoMap()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @AutoMap()
  @ManyToOne(() => Person)
  @JoinColumn({ name: 'owner_id' })
  owner: Person;
  @AutoMap()
  @Column({ name: 'ad_hoc', type: 'boolean', default: true })
  adHoc: boolean;

  @AutoMap()
  @Column({ type: 'boolean', default: true })
  shared: boolean;

  @AutoMap()
  @Column({ name: 'filters_version', type: 'int' })
  filtersVersion: number;

  @AutoMap()
  @Column({ type: 'text' })
  filters: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  sortBy?: string;

  @AutoMap()
  @Column({ type: 'text', nullable: true })
  groupBy?: string;

  @AutoMap()
  @Column({ type: 'text' })
  aggregationColumns: string;

  @AutoMap()
  @Column({
    name: 'date_created',
    type: 'timestamp',
  })
  dateCreated: Date;

  @AutoMap()
  @Column({
    name: 'last_updated',
    type: 'timestamp',
  })
  lastUpdated: Date;

  @AutoMap()
  @ManyToOne(() => Person)
  @JoinColumn({ name: 'last_updated_by_user_id' })
  lastUpdatedByUser: Person;
  @AutoMap()
  @Column({ type: 'boolean', default: false })
  deleted: boolean;

  @AutoMap()
  @Column({ name: 'filter_workspaces', type: 'json' })
  filterWorkspaces?: number[];
}
