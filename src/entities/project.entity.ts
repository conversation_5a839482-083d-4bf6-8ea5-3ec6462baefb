import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('project')
export class Project {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'bigint' })
  version: number;

  @Column({ type: 'bit' })
  isPublic: boolean;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'int' })
  status: number;

  @Column({ type: 'varchar', length: 30, nullable: true })
  kickOffStatus: string;

  @Column({ type: 'int' })
  totalIterations: number;

  @Column({ type: 'timestamp', nullable: true })
  dateCreated: Date;

  @Column({ type: 'timestamp', nullable: true })
  dateSubmitted: Date;

  @Column({ type: 'timestamp', nullable: true })
  dateAcceptedBid: Date;

  @Column({ type: 'timestamp', nullable: true })
  dateCompleted: Date;

  @Column({ type: 'tinyint', default: 0, nullable: true })
  deleted: boolean;

  @Column({ type: 'longtext', nullable: true })
  description: string;
}
