import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import {
  CreateReportDto,
  CriteriaStatusDto,
  PreFlightAggregateRequestDto,
  ReadScoreRequestDto,
  ScorecardService,
  ScoreService as ScoreSDKService,
  ScoringNormsRequestDto,
  SingleMediaAggregateScoreRequestDto,
  SingleMediaCriteriaScoreRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { catchError, firstValueFrom, map } from 'rxjs';
import { rethrowAxiosError } from '../api.utils';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Industry } from '../../platform/entities/industry.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { ScoringNormsRequest } from './dto/scoring-norms-request.dto';
import { CountryRegionMap } from '../../platform/entities/country-region-map.entity';
import { ScoringNormsVersion } from './entities/scoring-norms-version.entity';
import {
  Platform,
  PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP,
} from '../../constants/platform.constants';
import { isScoringNormsRequest } from '../reports/utils/adherence-utils';
import { ReadIndividualMediaScoresRequestDto } from './dto/read-individual-media-scores-request.dto';
import {
  ReadIndividualMediaScoresResponse,
  ReadIndividualScorecardMediaScoresResponse,
  ScoreDto,
} from './dto/read-individual-media-scores-response.dto';
import { ScoreCardService } from '../scorecard/score-card.service';
import { NormativeScopeDto } from '../../dto/normative-scope.dto';
import { ReportFiltersService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { CriteriaService } from '../criteria/criteria.service';
import { MediaService as MediaServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/media.service';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { MediaObjectAndScoresDto } from '../reports/dtos/media-object-and-scores.dto';
import { PreFlightSortOptions } from './dto/pre-flight-sort-options.dto';
import {
  CriteriaAndScoresCSVAccumulator,
  INTERNAL_TO_CSV_OUTPUT_SCORE_MAP,
} from '../reports/constants/constants';

interface NormScope {
  id: number;
  key: string;
  displayLabel: string;
  databaseValue: string;
  monthsPrior: number;
}

const normativeScopes: NormScope[] = [
  {
    id: 1,
    key: 'last_six_months',
    displayLabel: 'Last 6 Months',
    databaseValue: 'Last 6 Months',
    monthsPrior: 6,
  },
  {
    id: 2,
    key: 'last_year',
    displayLabel: 'Last Year',
    databaseValue: 'Previous Year',
    monthsPrior: 12,
  },
  {
    id: 3,
    key: 'last_two_years',
    displayLabel: 'Last 2 Years',
    databaseValue: 'Previous Two Years',
    monthsPrior: 24,
  },
];

@Injectable()
export class ScoreService {
  private readonly logger = new Logger(ScoreService.name);

  constructor(
    private readonly scorecardService: ScoreCardService,
    private readonly scorecardServiceSdk: ScorecardService,
    private readonly criteriaService: CriteriaService,
    private readonly scoreService: ScoreSDKService,
    private readonly organizationMediaServiceSDK: MediaServiceSDK,
    private readonly reportFiltersService: ReportFiltersService,
    private readonly scoringAuthService: ScoringAuthService,
    @InjectRepository(CountryRegionMap)
    private readonly countryRegionMapRepository: Repository<CountryRegionMap>,
    @InjectRepository(ScoringNormsVersion)
    private readonly scoringNormsVersionRepository: Repository<ScoringNormsVersion>,
    @InjectDataSource()
    private readonly datasource: DataSource,
  ) {}

  readonly normDateFormatter = new Intl.DateTimeFormat('en', {
    month: 'short',
    year: 'numeric',
  });

  async getAggregateScores(
    readScoreRequest: ReadScoreRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    return this.scoreService
      .getAggregateScores(
        readScoreRequest,
        paginationOptions.offset,
        paginationOptions.perPage,
        paginationOptions.queryId,
      )
      .pipe(
        map((axiosResponse) => axiosResponse.data),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  async getAggregateCriteriaScoresForPreFlight(
    preFlightAggregateRequestDto: PreFlightAggregateRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    return this.scoreService
      .getPreflightCriteriaAggregateScores(
        preFlightAggregateRequestDto,
        paginationOptions.offset,
        paginationOptions.perPage,
        paginationOptions.queryId,
      )
      .pipe(
        map((axiosResponse) => axiosResponse.data),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  async getAggregateChannelOrCriteriaGroupScoresForPreFlight(
    preFlightAggregateRequestDto: PreFlightAggregateRequestDto,
  ) {
    return this.scoreService
      .getPreflightGroupAggregateScores(preFlightAggregateRequestDto)
      .pipe(
        map((axiosResponse) => {
          if (
            !preFlightAggregateRequestDto.groupBy ||
            preFlightAggregateRequestDto.groupBy ===
              PreFlightAggregateRequestDto.GroupByEnum.Channel
          ) {
            return {
              ...axiosResponse.data.result,
              groupAggregates: axiosResponse.data.result.groupAggregates.map(
                (groupAgg) => ({
                  ...groupAgg,
                  displayName:
                    PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP[
                      groupAgg.displayName.toLowerCase()
                    ],
                }),
              ),
            };
          }

          return axiosResponse.data;
        }),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  async getPreflightScoredMediaList(
    preflightAggregateRequestDto: PreFlightAggregateRequestDto,
    paginationOptions: PaginationOptions,
    preflightReportSortOptions: PreFlightSortOptions,
    useDetailedScoreStatus: boolean,
  ) {
    const mediaScoresResponse: any =
      await this.scoreService.getPreflightCreativeListAndScoresAsPromise(
        preflightReportSortOptions.sortOrder,
        preflightReportSortOptions.sortBy === 'passedPercentByChannel'
          ? 'passedPercentByGroup'
          : preflightReportSortOptions.sortBy,
        preflightReportSortOptions.sortByGroupId ||
          preflightReportSortOptions.sortByChannel,
        preflightAggregateRequestDto,
        paginationOptions.offset,
        paginationOptions.perPage,
      );

    const resultWithMediaObject: MediaObjectAndScoresDto[] = await Promise.all(
      mediaScoresResponse.result.map(
        async (mediaScore: MediaObjectAndScoresDto) => {
          const { result: mediaObject } =
            await this.organizationMediaServiceSDK.getMediaByIdAsPromise(
              mediaScore.id,
            );
          const scores = mediaScore.scores.map((score: CriteriaStatusDto) => ({
            ...score,
            status: useDetailedScoreStatus
              ? score.detailedStatus || score.status
              : score.status,
            detailedStatus: undefined, //Hide detailedStatus in the response
          }));
          return {
            ...mediaScore,
            scores,
            mediaObject,
          };
        },
      ),
    );

    return new PaginatedResultArray(
      resultWithMediaObject,
      mediaScoresResponse.pagination?.totalSize ?? 0,
    );
  }

  async getMediaScoreDetails(
    mediaScoreDetailsRequestDto: ReadScoreRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    const observable = this.scoreService
      .getMediaScoreDetails(
        mediaScoreDetailsRequestDto,
        paginationOptions.offset,
        paginationOptions.perPage,
        paginationOptions.queryId,
      )
      .pipe(
        map((axiosResponse) => axiosResponse.data),
        catchError((err) => rethrowAxiosError(err)),
      );

    return await firstValueFrom(observable);
  }

  /**
   * Straightforward call to get media score details without reading impressions.
   * Will respond with any scores regardless of impressions in range or dates.
   * @param mediaScoreDetailsRequestDto
   * @param paginationOptions
   */
  async getMediaScoreDetailsOnly(workspaceId: number, mediaId: number) {
    const observable = this.scoreService
      .getSingleMediaDetails({
        workspaceId,
        mediaId,
      })
      .pipe(
        map((axiosResponse) => axiosResponse.data),
        catchError((err) => rethrowAxiosError(err)),
      );
    return await firstValueFrom(observable);
  }

  async getPreflightIndividualMediaScores(
    userId: number,
    currentWorkspaceId: number,
    readIndividualMediaScoresRequestDto: ReadIndividualMediaScoresRequestDto,
    useDetailedScoreStatus = false,
  ): Promise<ReadIndividualScorecardMediaScoresResponse> {
    // fetch scorecard ID if not provided

    let scorecardId = readIndividualMediaScoresRequestDto.scorecardId;
    let scorecardStatus = null;

    // fetch scorecard ID if not provided
    if (!scorecardId) {
      const scorecardInfo = await this.getScorecardIdByMediaId(
        readIndividualMediaScoresRequestDto.mediaId,
      );
      scorecardId = scorecardInfo.scorecardId;
      scorecardStatus = scorecardInfo.scorecardStatus;
      readIndividualMediaScoresRequestDto.scorecardId = scorecardId;
    }

    if (!scorecardStatus) {
      const scorecardStatusInfo = await this.getScorecardIdByMediaId(
        readIndividualMediaScoresRequestDto.mediaId,
      );
      scorecardStatus = scorecardStatusInfo.scorecardStatus;
    }

    // fetch the workspace ID associated with the scorecard
    const { mediaId, rootScorecardId } = readIndividualMediaScoresRequestDto;

    // If scorecardId is null, return null values in the response
    if (!scorecardId) {
      return {
        scores: [],
        scorecardId: null,
        scorecardStatus: null,
        versions: null,
      };
    }

    const workspaceIdForScorecard =
      await this.scoringAuthService.canUserAccessPartnerForScorecard(
        userId,
        scorecardId,
        currentWorkspaceId,
      );

    // fetch media object and scores
    const mediaDetailsResponse = await this.getMediaScoreDetailsOnly(
      Number(workspaceIdForScorecard), // this coercion to number is temporary, will revisit after the release
      mediaId,
    );

    const mediaObject =
      mediaDetailsResponse.result?.[0]?.mediaDetailsWithScores?.[0]
        ?.mediaObject;

    if (!mediaObject) {
      throw new Error('Media object is missing in the response.');
    }

    const { applicabilityType } = mediaObject ?? {};

    const mediaScoresList: CriteriaStatusDto[] =
      mediaDetailsResponse.result[0]?.mediaDetailsWithScores[0]?.scores ?? [];

    // fetch current batch and asset versions
    let versions = null;

    if (rootScorecardId) {
      const assetVersionsResponse =
        await this.scorecardService.getScorecardAssetVersions(
          rootScorecardId.toString(),
          scorecardId.toString(),
          mediaId.toString(),
        );

      versions = assetVersionsResponse?.result || null;
    }

    // fetch criteria for the workspace

    const platformString = readIndividualMediaScoresRequestDto.platforms
      .map((platform: any) => platform.toString())
      .join(',');

    const workspaceCriteria =
      await this.criteriaService.getCriteriaForListOfWorkspaces(
        [workspaceIdForScorecard.toString()],
        'ASC',
        'platform',
        platformString,
        applicabilityType,
      );

    // decorate criteria with signed URLs if available
    const criteriaWithSignedIconUrls =
      await this.criteriaService.decorateCriteriaWithSignedUrls(
        workspaceCriteria.result,
      );

    // build list of criteria-score objects for response
    const scores = await this.buildIndividualMediaScoresResponse(
      mediaScoresList,
      criteriaWithSignedIconUrls,
      useDetailedScoreStatus,
    );

    return {
      scores,
      scorecardId,
      scorecardStatus,
      versions,
    };
  }

  async getMediaById(mediaId: number) {
    return await this.organizationMediaServiceSDK.getMediaByIdAsPromise(
      mediaId,
    );
  }

  public async handleUserAccessToMediaValidation(
    mediaId: number,
    userId: number,
  ): Promise<boolean> {
    const { result } =
      await this.organizationMediaServiceSDK.validatePersonCanViewMediaAsPromise(
        mediaId,
        userId,
      );

    if (!result?.canView) {
      const errorMessage = `UserId - ${userId} does not have permission to view mediaId - ${mediaId}`;
      this.logger.error(errorMessage);
      throw new UnauthorizedException(errorMessage);
    }

    return Boolean(result.canView);
  }

  /**
   * Gets scores for individual media item.
   * Fetches criteria details using provided workspace ID.
   * @param currentWorkspaceId
   * @param readIndividualMediaScoresRequestDto
   * @param useDetailedScoreStatus
   */
  async getInflightIndividualMediaScores(
    currentWorkspaceId: number,
    readIndividualMediaScoresRequestDto: ReadIndividualMediaScoresRequestDto,
    useDetailedScoreStatus = false,
  ): Promise<ReadIndividualMediaScoresResponse> {
    const { mediaId, platforms } = readIndividualMediaScoresRequestDto;

    // Fetch media object and scores
    const mediaDetailsResponse = await this.getMediaScoreDetailsOnly(
      currentWorkspaceId,
      mediaId,
    );

    const mediaObject =
      mediaDetailsResponse.result[0]?.mediaDetailsWithScores[0]?.mediaObject;

    if (!mediaObject) {
      return { scores: [], versions: null };
    }

    const { applicabilityType } = mediaObject;
    const mediaScoresList =
      mediaDetailsResponse.result[0]?.mediaDetailsWithScores[0]?.scores ?? [];

    // Fetch criteria for the workspace
    const platformString = platforms
      .map((platform: any) => platform.toString())
      .join(',');
    const workspaceCriteria =
      await this.criteriaService.getCriteriaForListOfWorkspaces(
        [currentWorkspaceId.toString()],
        'ASC',
        'platform',
        platformString,
        applicabilityType,
      );

    // Decorate criteria with signed URLs if available
    const criteriaWithSignedIconUrls =
      await this.criteriaService.decorateCriteriaWithSignedUrls(
        workspaceCriteria.result,
      );

    // Build list of criteria-score objects for response
    const scores = await this.buildIndividualMediaScoresResponse(
      mediaScoresList,
      criteriaWithSignedIconUrls,
      useDetailedScoreStatus,
    );

    return {
      scores,
      media: mediaObject,
    };
  }

  public async getIndividualMediaChannelOrCriteriaGroupAggregates(
    workspaceId: number,
    requestDto: SingleMediaAggregateScoreRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    return this.scoreService
      .getMediaChannelOrCriteriaGroupAggregate(
        workspaceId,
        requestDto,
        paginationOptions.offset,
        paginationOptions.perPage,
      )
      .pipe(
        map((axiosResponse) => {
          if (
            requestDto.groupBy ===
            SingleMediaAggregateScoreRequestDto.GroupByEnum.Channel
          ) {
            return new PaginatedResultArray(
              axiosResponse.data.result.map((item) => ({
                ...item,
                displayName:
                  PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP[
                    item.displayName.toLowerCase()
                  ] ?? item.displayName,
              })),
              axiosResponse.data.pagination?.totalSize,
            );
          }

          return new PaginatedResultArray(
            axiosResponse.data.result,
            axiosResponse.data.pagination?.totalSize,
          );
        }),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  public async getIndividualMediaCriteriaAndScore(
    workspaceId: number,
    requestDto: SingleMediaCriteriaScoreRequestDto,
    paginationOptions: PaginationOptions,
    searchTerm?: string,
  ) {
    return this.scoreService.getMediaCriteriaAndScoresAsPromise(
      workspaceId,
      searchTerm ?? '',
      requestDto,
      paginationOptions.offset,
      paginationOptions.perPage,
    );
  }

  async getIndividualMediaScoresCSV(
    currentWorkspaceId: number,
    requestDto: ReadIndividualMediaScoresRequestDto,
  ): Promise<{ csv: string; fileName: string }> {
    const { media, scores }: ReadIndividualMediaScoresResponse =
      await this.getInflightIndividualMediaScores(
        currentWorkspaceId,
        requestDto,
      );

    const header = ['Item, Value'];
    const { platformAggregate, criteriaAndScores } = scores.reduce(
      (acc: CriteriaAndScoresCSVAccumulator, criteriaAndScore) => {
        const criteriaName =
          `${criteriaAndScore.platformIdentifier} / ${criteriaAndScore.name} / ${criteriaAndScore.rule}`.replace(
            /,/gm,
            '_',
          );
        const criteriaResult =
          INTERNAL_TO_CSV_OUTPUT_SCORE_MAP[criteriaAndScore.result];
        acc.criteriaAndScores.push(`${criteriaName}, ${criteriaResult}`);

        if (!acc.platformAggregate[criteriaAndScore.platformIdentifier]) {
          acc.platformAggregate[criteriaAndScore.platformIdentifier] = {
            totalCount: 0,
            passedCount: 0,
          };
        }

        const shouldCountScore =
          !criteriaAndScore.isOptional &&
          criteriaAndScore.result !== CriteriaStatusDto.StatusEnum.NoData;
        if (shouldCountScore) {
          acc.platformAggregate[criteriaAndScore.platformIdentifier]
            .totalCount++;
          acc.platformAggregate[
            criteriaAndScore.platformIdentifier
          ].passedCount +=
            criteriaAndScore.result === CriteriaStatusDto.StatusEnum.Pass
              ? 1
              : 0;
        }

        return acc;
      },
      { platformAggregate: {}, criteriaAndScores: [] },
    );

    const platformAggregateCSV = Object.entries(platformAggregate).reduce(
      (acc: string[], [platform, { totalCount, passedCount }]) => {
        acc.push(
          `${platform}, ${((passedCount * 100) / totalCount).toFixed(2)}`,
        );
        return acc;
      },
      [],
    );

    const csv = [
      ...header,
      ...criteriaAndScores.sort(),
      ...platformAggregateCSV,
    ].join('\n');
    return { csv, fileName: media.displayName };
  }

  private async getScorecardIdByMediaId(
    mediaId: number,
  ): Promise<{ scorecardId: number | null; scorecardStatus: string | null }> {
    const scorecardResponse =
      await this.scorecardServiceSdk.getScorecardByMediaIdAsPromise(mediaId);

    if (
      !scorecardResponse ||
      !scorecardResponse.result ||
      !scorecardResponse.result.id
    ) {
      return { scorecardId: null, scorecardStatus: null };
    }

    return {
      scorecardId: Number(scorecardResponse.result.id),
      scorecardStatus: scorecardResponse.result.status,
    };
  }

  private async buildIndividualMediaScoresResponse(
    mediaScoresList: CriteriaStatusDto[],
    criteriaList: any[],
    useDetailedScoreStatus: boolean,
  ): Promise<ScoreDto[]> {
    return criteriaList.reduce<ScoreDto[]>((acc, criteria) => {
      const score = mediaScoresList.find(
        (score) => score.criteriaId == criteria.id,
      );

      if (!score) {
        return acc;
      }

      const {
        id,
        identifier,
        parameters,
        name,
        platform,
        isOptional,
        isCustom,
        customValues,
        rule,
        description,
        isBestPractice,
        category,
        applicability,
        criteriaSet,
        customIconUrl,
      } = criteria;

      const result = useDetailedScoreStatus
        ? score.detailedStatus || score.status
        : score.status;

      acc.push({
        id,
        identifier,
        parameters,
        rule,
        description,
        name,
        platformIdentifier: platform,
        result,
        isOptional,
        isCustom,
        customValues,
        isBestPractice: Boolean(isBestPractice),
        category,
        mediaTypes: applicability.split(','),
        isGlobal: Boolean(criteriaSet?.isGlobal),
        customIconUrl,
      });

      return acc;
    }, []);
  }

  extractNormsRequest(
    createReportDto: CreateReportDto,
  ): ScoringNormsRequest | undefined {
    const normsConfigurationFilterIndex = createReportDto.filters.findIndex(
      (f) => f.fieldName === 'normsConfiguration',
    );
    if (normsConfigurationFilterIndex !== -1) {
      const filter = createReportDto.filters.splice(
        normsConfigurationFilterIndex,
        1,
      )[0];
      if (isScoringNormsRequest(filter.value)) {
        //Allow lookup by both key and display label (currently we are sending the display label)
        //We should transition to sending the key only, this supports that transition
        //That work is documented in VID-3987
        const scopes = new Map<string, NormScope>();
        normativeScopes.forEach((scope) => {
          scopes.set(scope.key, scope);
          scopes.set(scope.displayLabel, scope);
        });

        filter.value.scopes = filter.value.scopes.map((scope) => {
          const databaseValue = scopes.get(scope)?.databaseValue;
          if (!databaseValue) {
            throw new Error(`Invalid normative scope: ${scope}`);
          }
          return databaseValue;
        });
        return filter.value;
      }
    }
    return undefined;
  }

  async buildNormsRequest(
    normsFilter: ScoringNormsRequest,
  ): Promise<ScoringNormsRequestDto> {
    const defaultChannels: string[] = Object.values(Platform).map((platform) =>
      platform.toUpperCase(),
    );

    const applicableChannels = normsFilter.channels ?? defaultChannels;

    const markets = await this.getRequestedMarkets(normsFilter);

    const request = {
      channels: applicableChannels,
      scopes: normsFilter.scopes,
      objectives: normsFilter.objectives,
      industryId: normsFilter.industryId,
      marketIds: markets,
      regionIds: normsFilter.regionIds,
    };

    return request;
  }

  async getRequestedMarkets(normsFilter: ScoringNormsRequest) {
    const nonNumericIds =
      normsFilter.marketIds?.filter((id) => isNaN(Number(id))) ?? [];
    const regionIds: Array<number> =
      normsFilter.marketIds?.map(Number).filter((num) => !isNaN(num)) ?? [];

    if (regionIds.length > 0) {
      const marketIsos = await this.countryRegionMapRepository
        .createQueryBuilder('crm')
        .where('crm.region_id IN (:...regionIds)', { regionIds })
        .getMany();

      marketIsos.forEach((m) => nonNumericIds.push(m.isoCode));
    }

    if (nonNumericIds.length > 0) {
      return nonNumericIds;
    }
    return undefined;
  }

  getScoringNorms(scoringNormsRequest: ScoringNormsRequestDto) {
    return this.scoreService.getScoringNorms(scoringNormsRequest);
  }

  async getScoringNormsScopes(): Promise<NormativeScopeDto[]> {
    const scoringNormsVersion = await this.scoringNormsVersionRepository
      .createQueryBuilder('snv')
      .orderBy('snv.normsVersion', 'DESC')
      .getOne();

    if (!scoringNormsVersion) {
      this.logger.warn('No scoring norms version found');
      return [];
    }
    const normsCreationDate = scoringNormsVersion.createdDate;

    const endDate = this.getDateByMonthsInThePast(
      { monthsPrior: 0 },
      normsCreationDate,
    );

    return normativeScopes.map((scopeItem) => ({
      id: scopeItem.id,
      scope: scopeItem.displayLabel,
      scopeKey: scopeItem.key,
      startDate: this.getDateByMonthsInThePast(scopeItem, normsCreationDate),
      endDate,
    }));
  }

  getDateByMonthsInThePast(
    { monthsPrior }: { monthsPrior: number },
    normsCreationDate: Date,
  ) {
    return this.normDateFormatter.format(
      new Date(
        normsCreationDate.getFullYear(),
        normsCreationDate.getMonth() - monthsPrior,
        normsCreationDate.getDate(),
      ),
    );
  }

  getAllSubIndustryIds(industries: Industry[], subIndustryId: number) {
    const subIndustries = industries.filter(
      (industry) => industry.parentId == subIndustryId,
    );
    const allSubIndustryIds = new Set(
      subIndustries.map((subIndustry) => +subIndustry.id),
    );
    for (const subIndustry of subIndustries) {
      const subIndustryIds = this.getAllSubIndustryIds(
        industries,
        subIndustry.id,
      );
      subIndustryIds.forEach((id) => allSubIndustryIds.add(+id));
    }
    allSubIndustryIds.add(+subIndustryId);
    return allSubIndustryIds;
  }

  async getScoringNormsObjectives() {
    return this.reportFiltersService.getNormativeObjectiveGroups().pipe(
      map(
        (axiosResponse) =>
          axiosResponse.data.result as unknown as {
            data: { id: number; value: string }[];
          },
      ),
    );
  }
}
