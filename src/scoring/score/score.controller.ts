import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  Res,
  ValidationPipe,
  Version,
} from '@nestjs/common';
import { ScoreService } from './score.service';
import { ReadIndividualMediaScoresRequestDto } from './dto/read-individual-media-scores-request.dto';
import {
  PreFlightAggregateRequestDto,
  ReadScoreRequestDto,
  SingleMediaAggregateScoreRequestDto,
  SingleMediaCriteriaScoreRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  readDetails,
  readPartnerAssetFolderMedia,
} from '../scoring.permissions';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { SortOrder } from '../../reports/model/sort-order';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { MediaObjectAndScoresDto } from '../reports/dtos/media-object-and-scores.dto';
import { PreFlightSortOptions } from './dto/pre-flight-sort-options.dto';
import { Response } from 'express';

@ApiTags('Scores')
@ApiSecurity('Bearer Token')
// this should really be called score controller, leaving for now since it is not a priority
@Controller('scorecard/workspace/:workspaceId')
export class ScoreController {
  constructor(
    private readonly scoreService: ScoreService,
    private readonly scoringAuthService: ScoringAuthService,
    private readonly organizationMediaService: ScoringAuthService,
  ) {}

  /**
   * Calculates the aggregate scores for a scorecard or an ad account.
   * @param workspaceId
   * @param paginationOptions
   * @param readScoreRequestDto
   */
  @Permissions(readDetails)
  @Post('/aggregate')
  async getAggregateScores(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Body(new ValidationPipe()) readScoreRequestDto: ReadScoreRequestDto,
    @Request() req: any,
  ): Promise<any> {
    const userId: number = req['userId'] as number;
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      readScoreRequestDto.scorecardId,
      workspaceId,
    );
    return this.scoreService.getAggregateScores(
      readScoreRequestDto,
      paginationOptions,
    );
  }

  /**
   * Calculates the aggregate criteria scores for a pre-flight scorecard.
   * In-flight scorecards have transitioned to in-flight reports. See reports controller.
   * @param workspaceId
   * @param paginationOptions
   * @param preFlightAggregateRequestDto
   * @param req
   */
  @Permissions(readDetails)
  @Post('/criteria/aggregate')
  async getAggregateCriteriaScoresForPreFlight(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Body(new ValidationPipe())
    preFlightAggregateRequestDto: PreFlightAggregateRequestDto,
    @Request() req: any,
  ): Promise<any> {
    const userId: number = req['userId'] as number;
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      preFlightAggregateRequestDto.scorecardId,
      workspaceId,
    );
    return this.scoreService.getAggregateCriteriaScoresForPreFlight(
      preFlightAggregateRequestDto,
      paginationOptions,
    );
  }

  /**
   * @deprecated. Use getAggregateGroupScoresForPreFlight() instead.
   */
  @Permissions(readDetails)
  @Post('/channel/aggregate')
  async getAggregateChannelScoresForPreFlight(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    preFlightAggregateRequestDto: PreFlightAggregateRequestDto,
    @Request()
    req: any,
  ) {
    const userId: number = req['userId'] as number;
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      preFlightAggregateRequestDto.scorecardId,
      workspaceId,
    );
    return this.scoreService.getAggregateChannelOrCriteriaGroupScoresForPreFlight(
      preFlightAggregateRequestDto,
    );
  }

  @Permissions(readDetails)
  @Post('/group/aggregate')
  async getAggregateGroupScoresForPreFlight(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    preFlightAggregateRequestDto: PreFlightAggregateRequestDto,
    @Request()
    req: any,
  ) {
    const userId: number = req['userId'] as number;
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      preFlightAggregateRequestDto.scorecardId,
      workspaceId,
    );

    return this.scoreService.getAggregateChannelOrCriteriaGroupScoresForPreFlight(
      preFlightAggregateRequestDto,
    );
  }

  /**
   * Obtain score details for a scorecard or an ad account.
   * @param paginationOptions
   * @param mediaScoreDetailsRequestDto
   */
  @Permissions(readDetails)
  @Post('/score')
  async getMediaScoreDetails(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Body(new ValidationPipe())
    mediaScoreDetailsRequestDto: ReadScoreRequestDto,
    @Request()
    req: any,
  ) {
    const userId: number = req['userId'] as number;
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      mediaScoreDetailsRequestDto.scorecardId,
      workspaceId,
    );
    return this.scoreService.getMediaScoreDetails(
      mediaScoreDetailsRequestDto,
      paginationOptions,
    );
  }

  @Post('pre-flight/media-list')
  @Version('2')
  @Permissions(readDetails)
  async getPreflightMediaAndScoresV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() preflightAggregateRequestDto: PreFlightAggregateRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() sortOptions: PreFlightSortOptions,
    @Request() req: any,
  ): Promise<PaginatedResultArray<MediaObjectAndScoresDto>> {
    const useDetailedScoreStatus = true;
    return this.getPreflightMediaAndScores(
      workspaceId,
      preflightAggregateRequestDto,
      paginationOptions,
      sortOptions,
      req,
      useDetailedScoreStatus,
    );
  }

  @Post('pre-flight/media-list')
  @Permissions(readDetails)
  async getPreflightMediaAndScores(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() preflightAggregateRequestDto: PreFlightAggregateRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() sortOptions: PreFlightSortOptions,
    @Request() req: any,
    useDetailedScoreStatus = false,
  ): Promise<PaginatedResultArray<MediaObjectAndScoresDto>> {
    const userId: number = req['userId'] as number;
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      preflightAggregateRequestDto.scorecardId,
      workspaceId,
    );
    return this.scoreService.getPreflightScoredMediaList(
      preflightAggregateRequestDto,
      paginationOptions,
      {
        sortOrder: sortOptions.sortOrder ?? SortOrder.DESC,
        sortBy: sortOptions.sortBy ?? 'displayName',
        sortByChannel: sortOptions.sortByChannel ?? 'FACEBOOK',
        sortByGroupId: sortOptions.sortByGroupId,
      },
      useDetailedScoreStatus,
    );
  }

  /**
   * Gets scores for individual media item.
   * Collates score and criteria details for the media item.
   * @param req
   * @param readScoreRequestDto
   */
  @Permissions(readDetails)
  @Post('/media')
  async getIndividualMediaScores(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    readScoreRequestDto: ReadIndividualMediaScoresRequestDto,
    @Request()
    req: any,
  ) {
    const userId: number = req['userId'] as number;
    return this.scoreService.getPreflightIndividualMediaScores(
      userId,
      workspaceId,
      readScoreRequestDto,
    );
  }

  /**
   * Gets media object details for a given media id. Authorizes on workspace.
   * @param req
   * @param workspaceId
   * @param mediaId
   */
  @Get('/media-object/:mediaId')
  @Permissions(readPartnerAssetFolderMedia)
  async getSingleMediaObject(
    @Request() req: any,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param('mediaId', ParseIntPipe) mediaId: number,
    // ideally this would be moved to a dedicated media controller.
  ) {
    const { userId } = req;
    await this.scoreService.handleUserAccessToMediaValidation(mediaId, userId);
    console.info(
      `getSingleMediaObject called with workspaceId: ${workspaceId} and mediaId: ${mediaId}`,
    );
    return await this.scoreService.getMediaById(mediaId);
  }

  /**
   * Gets scores for individual media item.
   * Collates score and criteria details for the media item.
   * @param readScoreRequestDto
   */
  @Permissions(readDetails)
  @Post('/media')
  @Version('2')
  async getIndividualMediaScoresV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    readScoreRequestDto: ReadIndividualMediaScoresRequestDto,
  ) {
    return this.scoreService.getInflightIndividualMediaScores(
      workspaceId,
      readScoreRequestDto,
    );
  }

  /**
   * Gets scores for individual media item.
   * Collates score and criteria details for the media item.
   * @param workspaceId
   * @param readScoreRequestDto
   * @param req
   */
  @Permissions(readDetails)
  @Post('/media')
  @Version('3')
  async getIndividualMediaScoresV3(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    readScoreRequestDto: ReadIndividualMediaScoresRequestDto,
    @Request()
    req: any,
  ) {
    const scorecardId = readScoreRequestDto.scorecardId;
    const useDetailedScoreStatus = true;
    if (scorecardId) {
      //The Preflight media path is still looking for a scorecard
      const userId: number = req['userId'] as number;
      return this.scoreService.getPreflightIndividualMediaScores(
        userId,
        workspaceId,
        readScoreRequestDto,
        useDetailedScoreStatus,
      );
    }
    //The Inflight media path has moved away from using a scorecard
    return this.scoreService.getInflightIndividualMediaScores(
      workspaceId,
      readScoreRequestDto,
      useDetailedScoreStatus,
    );
  }

  /**
   * Gets channels/criteria groups and the media's aggregate scores for criteria within each.
   * @param workspaceId
   * @param singleMediaAggregateScoreRequestDto
   * @param paginationOptions
   * @param req
   */
  @Permissions(readDetails)
  @Post('media/aggregate')
  async getIndividualMediaChannelOrCriteriaGroupAggregates(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    singleMediaAggregateScoreRequestDto: SingleMediaAggregateScoreRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
  ) {
    const { userId } = req;

    await this.scoreService.handleUserAccessToMediaValidation(
      singleMediaAggregateScoreRequestDto.mediaId,
      userId,
    );
    return this.scoreService.getIndividualMediaChannelOrCriteriaGroupAggregates(
      workspaceId,
      singleMediaAggregateScoreRequestDto,
      paginationOptions,
    );
  }

  /**
   * Gets criteria and the media's scores for each.
   * @param workspaceId
   * @param singleMediaCriteriaScoreRequestDto
   * @param paginationOptions
   * @param searchTerm
   * @param req
   */
  @ApiQuery({
    name: 'searchTerm',
    description: 'Search term to filter criteria by name',
  })
  @Permissions(readDetails)
  @Post('media/criteria')
  async getIndividualMediaCriteriaAndScore(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    singleMediaCriteriaScoreRequestDto: SingleMediaCriteriaScoreRequestDto,
    @Query('searchTerm') searchTerm: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
  ) {
    const { userId } = req;

    await this.scoreService.handleUserAccessToMediaValidation(
      singleMediaCriteriaScoreRequestDto.mediaId,
      userId,
    );
    return this.scoreService.getIndividualMediaCriteriaAndScore(
      workspaceId,
      singleMediaCriteriaScoreRequestDto,
      paginationOptions,
      searchTerm,
    );
  }

  /**
   * Gets scores for individual media item. Returns CSV of V2 get media and scores api.
   * Collates score and criteria details for the media item.
   * @param workspaceId
   * @param readScoreRequestDto
   * @param response
   */
  @Permissions(readDetails)
  @Post('/media/csv')
  async getIndividualMediaScoresCSV(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(new ValidationPipe())
    readScoreRequestDto: ReadIndividualMediaScoresRequestDto,
    @Res() response: Response,
  ) {
    const { csv, fileName } =
      await this.scoreService.getIndividualMediaScoresCSV(
        workspaceId,
        readScoreRequestDto,
      );

    response.setHeader('Content-Type', 'text/csv');
    response.setHeader(
      'Content-Disposition',
      `attachment; filename="${fileName}.csv"`,
    );
    response.send(csv);
  }
}
