import { GetAssetVersionsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk';

export interface ReadIndividualMediaScoresResponse {
  scores: ScoreDto[];
  versions?: GetAssetVersionsResponseDto | null;
  media?: any;
}

export interface ReadIndividualScorecardMediaScoresResponse
  extends ReadIndividualMediaScoresResponse {
  scorecardId: number | null;
  scorecardStatus: string | null;
}

export interface ScoreDto {
  id: string;
  identifier: string;
  parameters: ParametersDto;
  rule: string;
  description: string | null;
  name: string | null;
  platformIdentifier: string;
  result: string;
  isOptional: number;
  isCustom: number;
  customValues: CustomValueDto[];
  isBestPractice: boolean;
  category: string;
  mediaTypes: string[];
  isGlobal: boolean;
  customIconUrl: string | null;
}

interface ParametersDto {
  [key: string]: any; // Dynamic properties, e.g., "maxFirstBrandAppearance": 10
}

interface CustomValueDto {
  id: string;
  value: string;
  criteriaId: number;
  dateCreated: string;
  lastUpdated: string;
}
