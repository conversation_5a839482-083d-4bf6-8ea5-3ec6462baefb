import { GetCriteriaQueryParamsDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getCriteriaQueryParamsDto';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class ReadIndividualMediaScoresRequestDto {
  @IsOptional()
  @IsNumber()
  scorecardId?: number | null;

  @IsNumber()
  mediaId: number;

  @IsString({ each: true })
  platforms: GetCriteriaQueryParamsDto.PlatformsEnum[];

  @IsOptional()
  @IsNumber()
  rootScorecardId?: number;
}
