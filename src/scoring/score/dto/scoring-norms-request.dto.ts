import { ApiProperty } from '@nestjs/swagger';
import { Platform } from '@vidmob/vidmob-nestjs-common';

export class ScoringNormsRequest {
  @ApiProperty({
    example: ['DV360', 'FACEBOOK', 'ADWORDS'],
    enum: Platform,
  })
  channels?: Array<string>;

  @ApiProperty({ example: 'Last 2 Years' })
  scopes: Array<string>;

  @ApiProperty({
    example: ['Awareness', 'Consideration', 'Conversion'],
    description: 'The normative object groups to fetch norms for',
  })
  objectives?: Array<string>;

  @ApiProperty({ example: 90 })
  industryId: number;

  @ApiProperty({
    example: ['usa'],
    description: 'Country codes for markets to fetch norms for',
  })
  marketIds?: Array<string>;

  @ApiProperty({
    example: [3],
    description: 'The region ids to fetch norms for',
  })
  regionIds?: Array<number>;

  @ApiProperty({ example: true })
  isEnabled?: boolean;
}
