export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export interface PreFlightSortOptions {
  sortOrder: SortOrder;
  sortBy:
    | 'displayName'
    | 'fileType'
    | 'passedPercent'
    | 'passedPercentByChannel'
    | 'passedPercentByGroup';
  sortByChannel:
    | 'ALL_PLATFORMS'
    | 'TWITTER'
    | 'FACEBOOK'
    | 'TIKTOK'
    | 'PINTEREST'
    | 'LINKEDIN'
    | 'DV360'
    | 'ADWORDS'
    | 'SNAPCHAT'
    | 'AMAZON'
    | 'REDDIT';
  // use this instead of sortByChannel to specify the channel identifier or criteria group identifier
  sortByGroupId?: string;
}
