import { Test, TestingModule } from '@nestjs/testing';
import { ReadIndividualMediaScoresRequestDto } from './dto/read-individual-media-scores-request.dto';
import { ScoreController } from './score.controller';
import { ScoreService } from './score.service';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';

describe.only('ScoreController', () => {
  let controller: ScoreController;
  let scoreService: ScoreService;
  let criteriaService: ScoringCriteriaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScoreController],
      providers: [
        {
          provide: ScoreService,
          useValue: {},
        },
        {
          provide: ScoringCriteriaService,
          useValue: {},
        },
        {
          provide: ScoringAuthService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<ScoreController>(ScoreController);
    scoreService = module.get<ScoreService>(ScoreService);
    criteriaService = module.get<ScoringCriteriaService>(
      ScoringCriteriaService,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should get individual media scores', async () => {
    const readScoreRequestDto: ReadIndividualMediaScoresRequestDto = {
      scorecardId: 4321,
      mediaId: 1,
      platforms: ['FACEBOOK'],
    };

    // Mock implementations specific to this test
    scoreService.getPreflightIndividualMediaScores = jest
      .fn()
      .mockResolvedValue({
        status: 200,
        result: {
          scores: {
            '148': {
              id: '148',
              identifier: 'FRAMED_FOR_MOBILE',
              parameters: { aspectRatios: ['9:16'] },
              name: 'Mobile Aspect Ratio',
              platformIdentifier: 'FACEBOOK',
              result: '0',
              isOptional: false,
              isCustom: false,
              custom: {
                customValues: [],
              },
            },
          },
        },
      });
    criteriaService.getCriteriaForListOfWorkspacesAsPromise = jest
      .fn()
      .mockResolvedValue({
        result: [
          {
            id: '148',
            dateCreated: '2021-07-09T00:38:20.000Z',
            criteriaTemplateId: '22',
            identifier: 'FRAMED_FOR_MOBILE',
            criteriaType: 'VIDMOB_OPTIONAL',
            platform: 'FACEBOOK',
            usesBrandIdentifier: 'false',
            sequence: 630,
            name: 'Mobile Aspect Ratio',
            isOptional: 0,
            isBestPractice: 0,
            scoringMethod: 'FRAMED_FOR_MOBILE',
            isCustom: 'false',
            customValues: [],
            applicability: 'IMAGE,VIDEO',
            parameters: { aspectRatios: ['9:16'] },
            criteriaSetId: '3',
            criteriaSet: { id: 3, isGlobal: 'false', workspaceId: 23142 },
            rule: 'Fits aspect ratio 9:16',
            description:
              "Confirms if the proportional relationship between the creative's width and height matches the specified rule.",
            defaultDisplayName: 'Fits Aspect Ratio',
          },
        ],
      });

    const workspaceId = 43112;
    const userId = 1234;

    const result = await controller.getIndividualMediaScores(
      workspaceId,
      readScoreRequestDto,
      {
        userId,
      },
    );

    expect(scoreService.getPreflightIndividualMediaScores).toHaveBeenCalledWith(
      userId,
      workspaceId,
      readScoreRequestDto,
    );
    expect(result).toEqual({
      status: 200,
      result: {
        scores: {
          '148': {
            id: '148',
            identifier: 'FRAMED_FOR_MOBILE',
            parameters: { aspectRatios: ['9:16'] },
            name: 'Mobile Aspect Ratio',
            platformIdentifier: 'FACEBOOK',
            result: '0',
            isOptional: false,
            isCustom: false,
            custom: {
              customValues: [],
            },
          },
        },
      },
    });
  });

  it('should get individual media scores with no scorecardId on body payload', async () => {
    const readScoreRequestDto: ReadIndividualMediaScoresRequestDto = {
      mediaId: 1,
      platforms: ['FACEBOOK'],
    };

    scoreService.getPreflightIndividualMediaScores = jest
      .fn()
      .mockResolvedValue({
        status: 200,
        result: {
          scores: {
            '148': {
              id: '148',
              identifier: 'FRAMED_FOR_MOBILE',
              parameters: { aspectRatios: ['9:16'] },
              name: 'Mobile Aspect Ratio',
              platformIdentifier: 'FACEBOOK',
              result: '0',
              isOptional: false,
              isCustom: false,
              custom: {
                customValues: [],
              },
            },
          },
          scorecardId: 123,
          versions: null,
        },
      });
    criteriaService.getCriteriaForListOfWorkspacesAsPromise = jest
      .fn()
      .mockResolvedValue({
        result: [
          {
            id: '148',
            dateCreated: '2021-07-09T00:38:20.000Z',
            criteriaTemplateId: '22',
            identifier: 'FRAMED_FOR_MOBILE',
            criteriaType: 'VIDMOB_OPTIONAL',
            platform: 'FACEBOOK',
            usesBrandIdentifier: 'false',
            sequence: 630,
            name: 'Mobile Aspect Ratio',
            isOptional: 0,
            isBestPractice: 0,
            scoringMethod: 'FRAMED_FOR_MOBILE',
            isCustom: 'false',
            customValues: [],
            applicability: 'IMAGE,VIDEO',
            parameters: { aspectRatios: ['9:16'] },
            criteriaSetId: '3',
            criteriaSet: { id: 3, isGlobal: 'false', workspaceId: 23142 },
            rule: 'Fits aspect ratio 9:16',
            description:
              "Confirms if the proportional relationship between the creative's width and height matches the specified rule.",
            defaultDisplayName: 'Fits Aspect Ratio',
          },
        ],
      });

    const workspaceId = 43112;
    const userId = 1234;

    const result = await controller.getIndividualMediaScores(
      workspaceId,
      readScoreRequestDto,
      { userId },
    );

    expect(scoreService.getPreflightIndividualMediaScores).toHaveBeenCalledWith(
      userId,
      workspaceId,
      readScoreRequestDto,
    );
    expect(result).toEqual({
      status: 200,
      result: {
        scores: {
          '148': {
            id: '148',
            identifier: 'FRAMED_FOR_MOBILE',
            parameters: { aspectRatios: ['9:16'] },
            name: 'Mobile Aspect Ratio',
            platformIdentifier: 'FACEBOOK',
            result: '0',
            isOptional: false,
            isCustom: false,
            custom: {
              customValues: [],
            },
          },
        },
        scorecardId: 123,
        versions: null,
      },
    });
  });

  it('should get individual media scores with versions', async () => {
    const readScoreRequestDto: ReadIndividualMediaScoresRequestDto = {
      scorecardId: 4321,
      mediaId: 1,
      platforms: ['FACEBOOK'],
      rootScorecardId: 2010,
    };

    const versionsResponse = {
      currentBatch: {
        id: '5827',
        name: 'testing performance widget',
        batchType: 'PRE_FLIGHT',
        status: 'COMPLETE',
        partnerAssetFolderId: '7628',
        partnerId: '29450',
        personId: '21503',
        criteriaSetId: '17',
        platform: '',
        isOutdated: 1,
        isInternal: 0,
        reasonOutdated: 'CRITERIA_CHANGE',
        mediaSessionStart: '2023-03-08T07:03:43.000Z',
        dateCreated: '2023-03-08T07:03:42.000Z',
        lastUpdated: '2024-03-28T01:03:38.642Z',
        startDate: null,
        endDate: null,
        assetLifeCycleBaseReport: '2010',
        score: 61,
      },
      currentVersionMediaId: '168420',
      baseMediaId: '155312',
      mediaRevisions: [
        // Sample revision data
      ],
    };

    // Mock implementations specific to this test
    scoreService.getPreflightIndividualMediaScores = jest
      .fn()
      .mockResolvedValue({
        status: 200,
        result: {
          scores: {
            '148': {
              id: '148',
              identifier: 'FRAMED_FOR_MOBILE',
              parameters: { aspectRatios: ['9:16'] },
              name: 'Mobile Aspect Ratio',
              platformIdentifier: 'FACEBOOK',
              result: '0',
              isOptional: false,
              isCustom: false,
              custom: {
                customValues: [],
              },
            },
          },
          versions: versionsResponse,
        },
      });

    const workspaceId = 43112;
    const userId = 1234;

    const result = await controller.getIndividualMediaScores(
      workspaceId,
      readScoreRequestDto,
      {
        userId,
      },
    );

    expect(scoreService.getPreflightIndividualMediaScores).toHaveBeenCalledWith(
      userId,
      workspaceId,
      readScoreRequestDto,
    );

    expect(result).toEqual({
      status: 200,
      result: {
        scores: {
          '148': {
            id: '148',
            identifier: 'FRAMED_FOR_MOBILE',
            parameters: { aspectRatios: ['9:16'] },
            name: 'Mobile Aspect Ratio',
            platformIdentifier: 'FACEBOOK',
            result: '0',
            isOptional: false,
            isCustom: false,
            custom: {
              customValues: [],
            },
          },
        },
        versions: versionsResponse,
      },
    });
  });

  it('should return null values when mediaId has no association with any scorecard throught compliance_batch_media_map"', async () => {
    const readScoreRequestDto: ReadIndividualMediaScoresRequestDto = {
      mediaId: 1,
      platforms: ['FACEBOOK'],
    };

    // Mock implementations specific to this test
    scoreService.getPreflightIndividualMediaScores = jest
      .fn()
      .mockResolvedValue({
        status: 200,
        result: {
          scores: [],
          scorecardId: null,
          scorecardStatus: null,
          versions: null,
        },
      });

    const workspaceId = 43112;
    const userId = 1234;

    const result = await controller.getIndividualMediaScores(
      workspaceId,
      readScoreRequestDto,
      { userId },
    );

    expect(scoreService.getPreflightIndividualMediaScores).toHaveBeenCalledWith(
      userId,
      workspaceId,
      readScoreRequestDto,
    );
    expect(result).toEqual({
      status: 200,
      result: {
        scores: [],
        scorecardId: null,
        scorecardStatus: null,
        versions: null,
      },
    });
  });
});
