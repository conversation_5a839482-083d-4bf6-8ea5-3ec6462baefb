import { CreateDateColumn, <PERSON>tity, PrimaryColumn } from 'typeorm';
import { AutoMap } from '@automapper/classes';

@Entity({ name: 'scoring_norms_version' })
export class ScoringNormsVersion {
  @AutoMap()
  @PrimaryColumn({ name: 'norms_version', type: 'int' })
  normsVersion: number;

  @AutoMap(() => Date)
  @CreateDateColumn({ name: 'created_date', type: 'datetime' })
  createdDate: Date;
}
