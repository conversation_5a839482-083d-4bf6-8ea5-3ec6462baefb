import { PermissionSubResource } from '../auth/enums/permission.subresource.enum';
import { PermissionDomain } from '../auth/enums/permission.domain.enum';
import { PermissionAction } from '../auth/enums/permission.action.enum';
import {
  organization<PERSON>rom<PERSON><PERSON><PERSON><PERSON><PERSON>,
  organizationFromParamsHandler,
  partnerFromParamsHandler,
  workspaceFromParamsHandler,
} from '../auth/decorators/permission.decorator';

export const updateConfiguration = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.CONFIGURATION,
    },
  ],
};

export const readConfiguration = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CONFIGURATION,
    },
  ],
};

export const createCriteria = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.WORKSPACE,
    },
  ],
};

export const createCriteriaSet = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromBodyHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.CRITERIA_SET,
    },
  ],
};

export const readCriteria = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CRITERIA,
    },
  ],
};

export const readCriteriaSet = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CRITERIA_SET,
    },
  ],
};

export const updateCriteria = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.CRITERIA,
    },
  ],
};

export const updateCriteriaSet = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.CRITERIA_SET,
    },
  ],
};

export const deleteCriteria = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.CRITERIA,
    },
  ],
};

export const deleteCriteriaSet = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.CRITERIA_SET,
    },
  ],
};

export const readDetails = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DETAILS,
    },
  ],
};

export const updateScoreOverrideRequest = {
  domain: PermissionDomain.ORGANIZATION,
  // domainContextHandler: organizationPermissionsPlaceholder,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.SCORE_OVERRIDE_REQUEST,
    },
  ],
};

export const readCriteriaGroup = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CRITERIA_GROUP,
    },
  ],
};

export const createCriteriaGroup = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.CRITERIA_GROUP,
    },
  ],
};

export const updateCriteriaGroup = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.CRITERIA_GROUP,
    },
  ],
};

export const deleteCriteriaGroup = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.CRITERIA_GROUP,
    },
  ],
};

export const updateCriteriaGroupForWorkspace = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.CRITERIA_GROUP,
    },
  ],
};

export const readPartnerAssetFolderMedia = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: workspaceFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.ASSET_LOCKER,
    },
  ],
};
