import {
  ConstraintDimension,
  CriteriaRuleOperator,
  ValueType,
} from './criteria-rule.enum';

export class ConstraintDTO {
  dimension: ConstraintDimension;
  allowedOperators: CriteriaRuleOperator[];
  valueType: ValueType;
  selectedOperator?: CriteriaRuleOperator;
  allowedValues?: number[] | string[];
  value?: string | number | number[] | string[];

  constructor(init?: Partial<ConstraintDTO>) {
    Object.assign(this, init);
  }
}

export class CriteriaRuleDTO {
  has: boolean;
  category: string;
  subCategory?: string;
  name: string;
  constraints: ConstraintDTO[];

  constructor(init?: Partial<CriteriaRuleDTO>) {
    Object.assign(this, init);
  }
}
