import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { RekognitionTag } from '../../entities/rekognition-tag.entity';
import { ConstraintDTO, CriteriaRuleDTO } from './criteria-rule.dto';
import {
  ConstraintDimension,
  CriteriaRuleOperator,
  ValueType,
} from './criteria-rule.enum';
import {
  CriteriaSetService,
  CustomCriteriaService as SoaCriteriaService,
  GetCriteriaQueryParamsDto,
  ReadCriteriaSetDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { CreateCustomCriteriaDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/createCustomCriteriaDto';
import { GetWorkspaceCriteriaSets200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getWorkspaceCriteriaSets200Response';
import GlobalStatusesEnum = GetCriteriaQueryParamsDto.GlobalStatusesEnum;

@Injectable()
export class CustomCriteriaService {
  private readonly logger = new Logger(CustomCriteriaService.name);

  constructor(
    @InjectRepository(RekognitionTag)
    private rekognitionTagRepository: Repository<RekognitionTag>,
    private readonly scoringCriteriaSetsService: CriteriaSetService,
    private readonly soaCriteriaService: SoaCriteriaService,
  ) {}

  async getRuleOptions(): Promise<CriteriaRuleDTO[]> {
    try {
      const elementCriteriaRules = await this.getElementRules();
      const brandingCriteriaRules = this.getBrandingRules();
      const formattingCriteriaRules = this.getFormattingRules();
      const keyVisualsCriteriaRules = this.getKeyVisualsRules();
      const messagingEffectivenessCriteriaRules =
        this.getMessagingEffectivenessRules();

      return [
        ...brandingCriteriaRules,
        ...formattingCriteriaRules,
        ...elementCriteriaRules,
        ...keyVisualsCriteriaRules,
        ...messagingEffectivenessCriteriaRules,
      ];
    } catch (error) {
      this.logger.error('Error fetching Rekognition tags', error.stack);
      throw new Error('Could not fetch Rekognition tags');
    }
  }

  getMessagingEffectivenessRules(): CriteriaRuleDTO[] {
    const timeConstraint = new ConstraintDTO({
      dimension: ConstraintDimension.Time,
      valueType: ValueType.Number,
      allowedOperators: [
        CriteriaRuleOperator.Before,
        CriteriaRuleOperator.After,
        CriteriaRuleOperator.Anytime,
      ],
    });
    const callToActionPresent = new CriteriaRuleDTO({
      category: 'Messaging effectiveness',
      name: 'Call to Action Present',
      has: true,
      constraints: [timeConstraint],
    });
    const promotionalOfferPresent = new CriteriaRuleDTO({
      category: 'Messaging effectiveness',
      name: 'Promotional Offer Present',
      has: true,
      constraints: [timeConstraint],
    });
    const textAndVoicePresent = new CriteriaRuleDTO({
      category: 'Messaging effectiveness',
      name: 'Text and Voice Present',
      has: true,
      constraints: [timeConstraint],
    });

    const textPresent = new CriteriaRuleDTO({
      category: 'Messaging effectiveness',
      name: 'Text Present',
      has: true,
      constraints: [timeConstraint],
    });

    return [
      callToActionPresent,
      promotionalOfferPresent,
      textAndVoicePresent,
      textPresent,
    ];
  }

  getKeyVisualsRules(): CriteriaRuleDTO[] {
    const timeConstraint = new ConstraintDTO({
      dimension: ConstraintDimension.Time,
      valueType: ValueType.Number,
      allowedOperators: [
        CriteriaRuleOperator.Before,
        CriteriaRuleOperator.After,
        CriteriaRuleOperator.Anytime,
      ],
    });
    const visualConstraint = new ConstraintDTO({
      dimension: ConstraintDimension.VisualThreshold,
      valueType: ValueType.Number,
      allowedOperators: [CriteriaRuleOperator.Is],
    });

    const human = new CriteriaRuleDTO({
      category: 'Key Visuals',
      name: 'Human',
      has: true,
      constraints: [timeConstraint, visualConstraint],
    });
    const motion = new CriteriaRuleDTO({
      category: 'Key Visuals',
      name: 'Motion',
      has: true,
      constraints: [timeConstraint, visualConstraint],
    });
    const positiveEmotion = new CriteriaRuleDTO({
      category: 'Key Visuals',
      name: 'Positive Emotion',
      has: true,
      constraints: [timeConstraint, visualConstraint],
    });
    const sceneChanges = new CriteriaRuleDTO({
      category: 'Key Visuals',
      name: 'Scene Changes',
      has: true,
      constraints: [
        timeConstraint,
        new ConstraintDTO({
          dimension: ConstraintDimension.SceneChanges,
          valueType: ValueType.Number,
          allowedOperators: [
            CriteriaRuleOperator.Is,
            CriteriaRuleOperator.Between,
          ],
        }),
      ],
    });
    const CustomColor = new CriteriaRuleDTO({
      category: 'Key Visuals',
      name: 'Custom Color',
      has: true,
      constraints: [
        timeConstraint,
        new ConstraintDTO({
          dimension: ConstraintDimension.Color,
          valueType: ValueType.String,
          allowedOperators: [CriteriaRuleOperator.Is],
        }),
      ],
    });

    return [human, motion, positiveEmotion, sceneChanges, CustomColor];
  }

  getFormattingRules(): CriteriaRuleDTO[] {
    const timeConstraint = new ConstraintDTO({
      dimension: ConstraintDimension.Time,
      valueType: ValueType.Number,
      allowedOperators: [
        CriteriaRuleOperator.Before,
        CriteriaRuleOperator.After,
        CriteriaRuleOperator.Anytime,
      ],
    });

    const audioPresent = new CriteriaRuleDTO({
      category: 'Formatting',
      name: 'Audio Present',
      has: true,
      constraints: [timeConstraint],
    });

    const fitsSize = new CriteriaRuleDTO({
      category: 'Formatting',
      name: 'Fits Size',
      has: true,
      constraints: [
        new ConstraintDTO({
          dimension: ConstraintDimension.FileSize,
          valueType: ValueType.SingleSelect,
          allowedValues: ['160x600', '320x1200'],
          allowedOperators: [CriteriaRuleOperator.Is],
        }),
      ],
    });
    const fitsAspectRatio = new CriteriaRuleDTO({
      category: 'Formatting',
      name: 'Fits Aspect Ratio',
      has: true,
      constraints: [
        new ConstraintDTO({
          dimension: ConstraintDimension.AspectRatio,
          valueType: ValueType.SingleSelect,
          allowedValues: ['1x1', '3x4'],
          allowedOperators: [CriteriaRuleOperator.Is],
        }),
      ],
    });
    const videoLengthWithinRange = new CriteriaRuleDTO({
      category: 'Formatting',
      name: 'Video Length Within Range',
      has: true,
      constraints: [
        new ConstraintDTO({
          dimension: ConstraintDimension.Duration,
          valueType: ValueType.Number,
          allowedOperators: [CriteriaRuleOperator.Between],
        }),
      ],
    });
    const maxFileSizeMet = new CriteriaRuleDTO({
      category: 'Formatting',
      name: 'Max File Size Met',
      has: true,
      constraints: [
        new ConstraintDTO({
          dimension: ConstraintDimension.FileSize,
          valueType: ValueType.Number,
          allowedOperators: [CriteriaRuleOperator.Is],
        }),
      ],
    });

    return [
      audioPresent,
      fitsSize,
      fitsAspectRatio,
      videoLengthWithinRange,
      maxFileSizeMet,
    ];
  }

  getBrandingRules(): CriteriaRuleDTO[] {
    const timeConstraint = new ConstraintDTO({
      dimension: ConstraintDimension.Time,
      valueType: ValueType.Number,
      allowedOperators: [
        CriteriaRuleOperator.Before,
        CriteriaRuleOperator.After,
        CriteriaRuleOperator.Anytime,
      ],
    });

    const brandNameMention = new CriteriaRuleDTO({
      category: 'Branding',
      name: 'Brand Name Mention',
      has: true,
      constraints: [timeConstraint],
    });

    const brandNameAndLogo = new CriteriaRuleDTO({
      category: 'Branding',
      name: 'Brand Name or Logo',
      has: true,
      constraints: [timeConstraint],
    });

    return [brandNameMention, brandNameAndLogo];
  }

  async getElementRules(): Promise<CriteriaRuleDTO[]> {
    const tags = await this.rekognitionTagRepository.find();
    const timeConstraint = new ConstraintDTO({
      dimension: ConstraintDimension.Time,
      valueType: ValueType.Number,
      allowedOperators: [
        CriteriaRuleOperator.Before,
        CriteriaRuleOperator.After,
        CriteriaRuleOperator.Anytime,
      ],
    });

    return tags.map((tag) => {
      const dto = new CriteriaRuleDTO();
      dto.category = 'Elements';
      dto.subCategory = tag.category;
      dto.name = tag.label;
      dto.has = true;
      dto.constraints = [timeConstraint];
      return dto;
    });
  }

  async getRuleOptionsV2(stages = '') {
    return this.soaCriteriaService.getCriteriaRulesAsPromise(stages);
  }

  async getElementList() {
    return this.soaCriteriaService.getElementListAsPromise();
  }

  async getElementListV3(organizationId: string) {
    return this.soaCriteriaService.getElementListV2AsPromise(organizationId);
  }

  async createCustomCriteria(
    userId: number,
    workspaceId: number,
    body: CreateCustomCriteriaDto,
  ) {
    //TODO phase 1 defaults to adding it as a workspace criteria
    // phase 2 will add it to the organization rules table and then create a
    // criteria that links to the organization rule
    const criteriaSets: GetWorkspaceCriteriaSets200Response =
      await this.scoringCriteriaSetsService.getWorkspaceCriteriaSetsAsPromise(
        workspaceId,
      );
    const defaultCriteriaSet: ReadCriteriaSetDto | undefined =
      criteriaSets.result.find(
        (value) => value.name === GlobalStatusesEnum.Default,
      );
    if (!defaultCriteriaSet) {
      const message =
        'No default criteria set found for workspace id: ' + workspaceId;
      this.logger.error(message);
      throw new NotFoundException(message);
    }

    return this.soaCriteriaService.createCustomCriteriaAsPromise(
      defaultCriteriaSet.id,
      userId,
      body,
    );
  }
}
