export enum CriteriaRuleOperator {
  Is = 'is',
  In = 'in',
  Any = 'any',
  Between = 'between',
  After = 'after',
  Before = 'before',
  Anytime = 'anytime',
}

export enum ValueType {
  String = 'string',
  MultiString = 'multiString',
  SingleSelect = 'singleSelect',
  MultiSelect = 'multiSelect',
  Number = 'number',
}

export enum ConstraintDimension {
  Time = 'time',
  VisualThreshold = 'visualThreshold',
  FileSize = 'fileSize',
  Duration = 'duration',
  AspectRatio = 'aspectRatio',
  Color = 'color',
  SceneChanges = 'sceneChanges',
}

export enum SetOperator {
  ALL = 'ALL',
  ANY = 'ANY',
}
