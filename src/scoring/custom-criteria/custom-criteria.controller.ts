import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  Version,
} from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { CustomCriteriaService } from './custom-criteria.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { updateConfiguration } from '../scoring.permissions';
import { CreateCustomCriteriaDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/createCustomCriteriaDto';

@ApiTags('Custom Criteria')
@ApiSecurity('Bearer Token')
@Controller('custom-criteria')
export class CustomCriteriaController {
  constructor(private customCriteriaService: CustomCriteriaService) {}

  @Get('/rules')
  async getCriteriaRules(
    @Query('workspaceId', ParseIntPipe) workspaceId?: number,
  ) {
    return this.customCriteriaService.getRuleOptions();
  }

  @Get('/rules')
  @Version('2')
  async getCriteriaRulesV2(@Query('stages') stages?: string) {
    return this.customCriteriaService.getRuleOptionsV2(stages);
  }

  @Get('/elements')
  @Version('2')
  async getElementList() {
    return this.customCriteriaService.getElementList();
  }

  @Get('/elements')
  @Version('3')
  async getElementListV3(@Query('organizationId') organizationId: string) {
    return this.customCriteriaService.getElementListV3(organizationId);
  }

  /**
   * Create a new criteria as part of a criteria set or in the partner's default criteria set if not specified in
   * the body of the request.
   * @param req - used to get the user id.
   * @param workspaceId - The ID of the workspace.
   * @param createCustomCriteriaDto - The details of the custom criteria to create.
   */

  @ApiParam({
    name: 'workspaceId',
    description:
      'The unique identifier of the workspace for which the custom criteria will belong to.',
  })
  @Permissions(updateConfiguration)
  @Post('/workspace/:workspaceId')
  @Version('2')
  async createCustomRule(
    @Request() req: any,
    @Param('workspaceId') workspaceId: number,
    @Body() createCustomCriteriaDto: CreateCustomCriteriaDto,
  ) {
    const { userId } = req;
    console.log('sending body', JSON.stringify(createCustomCriteriaDto));
    return this.customCriteriaService.createCustomCriteria(
      userId,
      workspaceId,
      createCustomCriteriaDto,
    );
  }
}
