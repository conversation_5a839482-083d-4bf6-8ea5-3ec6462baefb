import {
  CustomCriteriaDTO,
  CustomCriteriaSetDTO,
  RootCustomCriteriaDTO,
} from './custom-criteria.dto';
import { CriteriaRuleDTO } from './criteria-rule.dto';

/**
 * Normalizes and stringifies the RootCustomCriteriaDTO, ensuring consistent JSON output
 * regardless of the order of `rules`.
 */
export function normalizeAndStringifyCriteria(
  criteria: RootCustomCriteriaDTO,
): string {
  // Deep clone to avoid mutating the original input
  const clonedCriteria = JSON.parse(
    JSON.stringify(criteria),
  ) as RootCustomCriteriaDTO;

  // Sort the top-level rules array
  clonedCriteria.rules.sort(compareRules);

  // Recursively sort nested rules if any
  for (const rule of clonedCriteria.rules) {
    if (isCustomCriteriaSetDTO(rule)) {
      sortCriteria(rule);
    }
  }

  return JSON.stringify(clonedCriteria);
}

/**
 * Recursively sorts the `rules` in CustomCriteriaSetDTO.
 */
function sortCriteria(criteria: CustomCriteriaSetDTO): void {
  // Sort the rules array within the CustomCriteriaSetDTO
  criteria.rules.sort(compareRules);

  // Recursively sort nested CustomCriteriaSetDTOs within the rules
  for (const rule of criteria.rules) {
    if (isCustomCriteriaSetDTO(rule)) {
      sortCriteria(rule);
    }
  }
}

/**
 * Comparison function to determine the order of two rules.
 */
function compareRules(a: CustomCriteriaDTO, b: CustomCriteriaDTO): number {
  // If both are CriteriaRuleDTO, compare by name (primary) and category (secondary)
  if (isCriteriaRuleDTO(a) && isCriteriaRuleDTO(b)) {
    const nameComparison = a.name.localeCompare(b.name);
    if (nameComparison !== 0) return nameComparison;
    return (a.category ?? '').localeCompare(b.category ?? '');
  }

  // If both are CustomCriteriaSetDTO, compare by operator (primary) and rule length (secondary)
  if (isCustomCriteriaSetDTO(a) && isCustomCriteriaSetDTO(b)) {
    const operatorComparison = a.operator.localeCompare(b.operator);
    if (operatorComparison !== 0) return operatorComparison;
    return a.rules.length - b.rules.length;
  }

  // If one is a CriteriaRuleDTO and the other is a CustomCriteriaSetDTO, define a fixed order
  if (isCriteriaRuleDTO(a) && isCustomCriteriaSetDTO(b)) return -1;
  if (isCustomCriteriaSetDTO(a) && isCriteriaRuleDTO(b)) return 1;

  return 0;
}

/**
 * Type guard to check if a CustomCriteriaDTO is a CustomCriteriaSetDTO.
 */
function isCustomCriteriaSetDTO(
  criteria: CustomCriteriaDTO,
): criteria is CustomCriteriaSetDTO {
  return 'operator' in criteria && 'rules' in criteria;
}

/**
 * Type guard to check if a CustomCriteriaDTO is a CriteriaRuleDTO.
 */
function isCriteriaRuleDTO(
  criteria: CustomCriteriaDTO,
): criteria is CriteriaRuleDTO {
  return 'has' in criteria && 'category' in criteria && 'name' in criteria;
}
