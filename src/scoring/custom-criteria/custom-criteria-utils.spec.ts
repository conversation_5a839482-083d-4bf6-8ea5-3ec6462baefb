import { RootCustomCriteriaDTO } from './custom-criteria.dto';
import {
  ConstraintDimension,
  CriteriaRuleOperator,
  SetOperator,
  ValueType,
} from './criteria-rule.enum';
import { normalizeAndStringifyCriteria } from './custom-criteria-utils';

describe('normalizeAndStringifyCriteria', () => {
  it('should produce consistent JSON output for RootCustomCriteriaDTO with identical rules in different order', () => {
    // Original input
    const criteria1: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [
        {
          category: 'Formatting',
          name: 'Fits Aspect Ratio',
          has: true,
          constraints: [
            {
              dimension: ConstraintDimension.AspectRatio,
              valueType: ValueType.SingleSelect,
              allowedValues: ['1x1', '3x4'],
              allowedOperators: [CriteriaRuleOperator.Is],
              selectedOperator: CriteriaRuleOperator.Is,
              value: '1x1',
            },
          ],
        },
        {
          category: 'Elements',
          subCategory: 'Person Description',
          name: 'Woman',
          has: true,
          constraints: [
            {
              dimension: ConstraintDimension.Time,
              valueType: ValueType.Number,
              allowedOperators: [
                CriteriaRuleOperator.Before,
                CriteriaRuleOperator.After,
                CriteriaRuleOperator.Anytime,
              ],
              selectedOperator: CriteriaRuleOperator.Before,
              value: 5,
            },
          ],
        },
      ],
    };

    // Same rules, but in different order
    const criteria2: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [
        {
          category: 'Elements',
          subCategory: 'Person Description',
          name: 'Woman',
          has: true,
          constraints: [
            {
              dimension: ConstraintDimension.Time,
              valueType: ValueType.Number,
              allowedOperators: [
                CriteriaRuleOperator.Before,
                CriteriaRuleOperator.After,
                CriteriaRuleOperator.Anytime,
              ],
              selectedOperator: CriteriaRuleOperator.Before,
              value: 5,
            },
          ],
        },
        {
          category: 'Formatting',
          name: 'Fits Aspect Ratio',
          has: true,
          constraints: [
            {
              dimension: ConstraintDimension.AspectRatio,
              valueType: ValueType.SingleSelect,
              allowedValues: ['1x1', '3x4'],
              allowedOperators: [CriteriaRuleOperator.Is],
              selectedOperator: CriteriaRuleOperator.Is,
              value: '1x1',
            },
          ],
        },
      ],
    };

    // Act: normalize both criteria and compare JSON strings
    const normalizedJson1 = normalizeAndStringifyCriteria(criteria1);
    const normalizedJson2 = normalizeAndStringifyCriteria(criteria2);

    // Assert: both JSON outputs should be the same
    expect(normalizedJson1).toEqual(normalizedJson2);
  });

  it('should handle empty rules array correctly', () => {
    const criteria: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [],
    };

    const normalizedJson = normalizeAndStringifyCriteria(criteria);

    expect(normalizedJson).toEqual(JSON.stringify(criteria));
  });

  it('should handle nested CustomCriteriaSetDTO with identical nested rules in different order', () => {
    const criteria1: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [
        {
          operator: SetOperator.ANY,
          rules: [
            {
              category: 'Formatting',
              name: 'Fits Aspect Ratio',
              has: true,
              constraints: [
                {
                  dimension: ConstraintDimension.AspectRatio,
                  valueType: ValueType.SingleSelect,
                  allowedValues: ['1x1', '3x4'],
                  allowedOperators: [CriteriaRuleOperator.Is],
                  selectedOperator: CriteriaRuleOperator.Is,
                  value: '1x1',
                },
              ],
            },
            {
              category: 'Elements',
              subCategory: 'Person Description',
              name: 'Woman',
              has: true,
              constraints: [
                {
                  dimension: ConstraintDimension.Time,
                  valueType: ValueType.Number,
                  allowedOperators: [
                    CriteriaRuleOperator.Before,
                    CriteriaRuleOperator.After,
                    CriteriaRuleOperator.Anytime,
                  ],
                  selectedOperator: CriteriaRuleOperator.Before,
                  value: 5,
                },
              ],
            },
          ],
        },
      ],
    };

    const criteria2: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [
        {
          operator: SetOperator.ANY,
          rules: [
            {
              category: 'Elements',
              subCategory: 'Person Description',
              name: 'Woman',
              has: true,
              constraints: [
                {
                  dimension: ConstraintDimension.Time,
                  valueType: ValueType.Number,
                  allowedOperators: [
                    CriteriaRuleOperator.Before,
                    CriteriaRuleOperator.After,
                    CriteriaRuleOperator.Anytime,
                  ],
                  selectedOperator: CriteriaRuleOperator.Before,
                  value: 5,
                },
              ],
            },
            {
              category: 'Formatting',
              name: 'Fits Aspect Ratio',
              has: true,
              constraints: [
                {
                  dimension: ConstraintDimension.AspectRatio,
                  valueType: ValueType.SingleSelect,
                  allowedValues: ['1x1', '3x4'],
                  allowedOperators: [CriteriaRuleOperator.Is],
                  selectedOperator: CriteriaRuleOperator.Is,
                  value: '1x1',
                },
              ],
            },
          ],
        },
      ],
    };

    // Act: normalize both criteria and compare JSON strings
    const normalizedJson1 = normalizeAndStringifyCriteria(criteria1);
    const normalizedJson2 = normalizeAndStringifyCriteria(criteria2);

    // Assert: both JSON outputs should be the same
    expect(normalizedJson1).toEqual(normalizedJson2);
  });
});

describe('normalizeAndStringifyCriteria', () => {
  it('should produce consistent JSON output for deeply nested RootCustomCriteriaDTO with multiple rules and sets', () => {
    const criteria1: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [
        {
          // Level 1 rule
          operator: SetOperator.ANY,
          rules: [
            {
              // Level 2 rule
              operator: SetOperator.ALL,
              rules: [
                {
                  category: 'Category A',
                  name: 'Rule 1',
                  has: true,
                  constraints: [
                    {
                      dimension: ConstraintDimension.Color,
                      valueType: ValueType.MultiSelect,
                      allowedValues: ['Red', 'Blue'],
                      allowedOperators: [CriteriaRuleOperator.In],
                      selectedOperator: CriteriaRuleOperator.In,
                      value: ['Red', 'Blue'],
                    },
                  ],
                },
                {
                  category: 'Category B',
                  name: 'Rule 2',
                  has: false,
                  constraints: [
                    {
                      dimension: ConstraintDimension.Duration,
                      valueType: ValueType.Number,
                      allowedOperators: [CriteriaRuleOperator.Between],
                      selectedOperator: CriteriaRuleOperator.Between,
                      value: [30, 60],
                    },
                  ],
                },
              ],
            },
            {
              category: 'Category C',
              name: 'Rule 3',
              has: true,
              constraints: [
                {
                  dimension: ConstraintDimension.Time,
                  valueType: ValueType.Number,
                  allowedOperators: [
                    CriteriaRuleOperator.After,
                    CriteriaRuleOperator.Before,
                  ],
                  selectedOperator: CriteriaRuleOperator.Before,
                  value: 5,
                },
              ],
            },
          ],
        },
        {
          category: 'Category D',
          name: 'Rule 4',
          has: true,
          constraints: [
            {
              dimension: ConstraintDimension.FileSize,
              valueType: ValueType.Number,
              allowedOperators: [CriteriaRuleOperator.Is],
              selectedOperator: CriteriaRuleOperator.Is,
              value: 100,
            },
          ],
        },
      ],
    };

    // Same structure as criteria1 but with different order in rules
    const criteria2: RootCustomCriteriaDTO = {
      version: 1,
      operator: SetOperator.ALL,
      rules: [
        {
          category: 'Category D',
          name: 'Rule 4',
          has: true,
          constraints: [
            {
              dimension: ConstraintDimension.FileSize,
              valueType: ValueType.Number,
              allowedOperators: [CriteriaRuleOperator.Is],
              selectedOperator: CriteriaRuleOperator.Is,
              value: 100,
            },
          ],
        },
        {
          operator: SetOperator.ANY,
          rules: [
            {
              category: 'Category C',
              name: 'Rule 3',
              has: true,
              constraints: [
                {
                  dimension: ConstraintDimension.Time,
                  valueType: ValueType.Number,
                  allowedOperators: [
                    CriteriaRuleOperator.After,
                    CriteriaRuleOperator.Before,
                  ],
                  selectedOperator: CriteriaRuleOperator.Before,
                  value: 5,
                },
              ],
            },
            {
              operator: SetOperator.ALL,
              rules: [
                {
                  category: 'Category B',
                  name: 'Rule 2',
                  has: false,
                  constraints: [
                    {
                      dimension: ConstraintDimension.Duration,
                      valueType: ValueType.Number,
                      allowedOperators: [CriteriaRuleOperator.Between],
                      selectedOperator: CriteriaRuleOperator.Between,
                      value: [30, 60],
                    },
                  ],
                },
                {
                  category: 'Category A',
                  name: 'Rule 1',
                  has: true,
                  constraints: [
                    {
                      dimension: ConstraintDimension.Color,
                      valueType: ValueType.MultiSelect,
                      allowedValues: ['Red', 'Blue'],
                      allowedOperators: [CriteriaRuleOperator.In],
                      selectedOperator: CriteriaRuleOperator.In,
                      value: ['Red', 'Blue'],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    };

    // Act: normalize both criteria objects and compare JSON outputs
    const normalizedJson1 = normalizeAndStringifyCriteria(criteria1);
    const normalizedJson2 = normalizeAndStringifyCriteria(criteria2);

    // Assert: both JSON outputs should be the same
    expect(normalizedJson1).toEqual(normalizedJson2);
  });
});
