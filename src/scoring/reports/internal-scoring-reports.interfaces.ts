//The organization service doesn't currently export these correctly
export interface Brand {
  id: string;
  name: string;
}

export interface Market {
  isoCode: string;
  name: string;
}

export interface ReadWorkspaceSearchDto {
  id: number;
  name: string;
  organizationId: string;
  markets?: Market[];
  brands?: Brand[];
}

export interface ImpressionAdherenceReportTotalItem {
  row: {
    market: string;
    workspace: string;
    brand: string;
  };
  column: string;
  data: {
    type: string;
    value: number;
  };
}

export interface AdoptionReportDataItem {
  totalScored: string;
}

export interface AdherenceReportDataItem {
  assetsScored: string;
  assetsPassed: string;
  criteria: {
    criteriaIdentifier: string;
    criteriaParameters: string;
    criteriaPlatform?: string; //I think we should remove this
    criteriaIsOptional?: boolean;
    criteriaIsBestPractice?: boolean;
    criteriaName?: string;
  };
  batchType?: string;
}

export interface ImpressionAdherenceReportDataItem {
  row: {
    market: string;
    workspace: string;
    brand: string;
  };
  column: {
    criteria: {
      isOptional?: boolean;
      isBestPractice?: boolean;
      criteriaIdentifier: string;
      criteriaParameters: string;
      criteriaPlatform?: string;
      criteriaName?: string;
    };
    batchType: 'IN_FLIGHT' | 'PRE_FLIGHT';
  };
  data: {
    type: string;
    value: number[];
  };
}

export interface CsvLabel {
  uniqueName: string;
  displayName: string;
}
