import { BadRequestException, UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { CreateReportDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ReportsController } from './reports.controller';
import { ScoringReportsService } from './scoring-reports.service';
import { ScoringReportFiltersService } from './scoring-report-filters.service';
import { ReportFilterOperator } from '../../reports/model/report-filters.dto';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import {
  AuthorizationService,
  OAuth2Service,
} from '@vidmob/vidmob-authorization-service-sdk';
import { MediaAnnotationService } from '../../media-annotation/media-annotation.service';
import {
  MediaService,
  OrganizationService,
} from '@vidmob/vidmob-organization-service-sdk';
import { AnalyticsUserService } from '../../analytics/analytics-user-service/analytics-user-service';
import { WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/workspaceAdAccount.service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { WorkspaceService as WorkspaceServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';

describe('ReportsController', () => {
  let controller: ReportsController;
  let reportsService: ScoringReportsService;
  let scoringAuthService: ScoringAuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReportsController],
      providers: [
        AnalyticsUserService,
        WorkspaceService,
        AuthService,
        { provide: OAuth2Service, useValue: {} },
        { provide: MediaAnnotationService, useValue: {} },
        {
          provide: OrganizationUserService,
          useValue: { getUserInOrganization: jest.fn() },
        },
        {
          provide: ScoringReportsService,
          useValue: {
            generateAdoptionReport: jest.fn(),
            generateAdherenceReport: jest.fn(),
          },
        },
        {
          provide: ScoringAuthService,
          useValue: {
            getRelatedAccessiblePartners: jest.fn(),
          },
        },
        {
          provide: ScoringReportFiltersService,
          useValue: {
            addOrganizationIdFilter: jest.fn(),
          },
        },
        { provide: MediaService, useValue: {} },
        { provide: WorkspaceAdAccountServiceSDK, useValue: {} },
        {
          provide: OrganizationService,
          useValue: {},
        },
        {
          provide: WorkspaceServiceSDK,
          useValue: {},
        },
        {
          provide: AuthorizationService,
          useValue: {},
        },
      ],
    }).compile();

    controller = module.get<ReportsController>(ReportsController);
    reportsService = module.get<ScoringReportsService>(ScoringReportsService);
    scoringAuthService = module.get<ScoringAuthService>(ScoringAuthService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  const req = { userId: 1234 } as unknown as Request;
  const workspaceId = 1;
  const DUMMY_REPORT_RESPONSE = {};
  const groupBy = {
    columns: ['market'],
    rows: ['month'],
  };

  it('fail when there are no workspace filters', async () => {
    const reportDto: CreateReportDto = {
      filters: [],
      groupBy,
    };
    try {
      await controller.generateAdoptionReport(workspaceId, reportDto, req);
      expect(true).toBe(false);
    } catch (e) {
      expect(e).toBeInstanceOf(BadRequestException);
    }
  });

  it('fail on between workspace filter', async () => {
    const reportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'workspaceId',
          operator: ReportFilterOperator.Between,
          value: [1, 100],
        },
      ],
      groupBy,
    };
    try {
      await controller.generateAdoptionReport(workspaceId, reportDto, req);
      expect(true).toBe(false);
    } catch (e) {
      expect(e).toBeInstanceOf(BadRequestException);
    }
  });

  it('Fail on total workspace mismatch', async () => {
    const reportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'workspaceId',
          operator: ReportFilterOperator.Equals,
          value: workspaceId,
        },
      ],
      groupBy,
    };
    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue([{ workspaceId: 999 }]);
    try {
      await controller.generateAdoptionReport(workspaceId, reportDto, req);
      expect(true).toBe(false);
    } catch (e) {
      expect(e).toBeInstanceOf(UnauthorizedException);
    }
  });

  it('Fail on partial workspace mismatch', async () => {
    const reportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'workspaceId',
          operator: ReportFilterOperator.In,
          value: [workspaceId, 999],
        },
      ],
      groupBy,
    };
    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue([workspaceId]);
    try {
      await controller.generateAdoptionReport(workspaceId, reportDto, req);
      expect(true).toBe(false);
    } catch (e) {
      expect(e).toBeInstanceOf(UnauthorizedException);
    }
  });

  it('Equals filter should generate adoption report', async () => {
    const reportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'workspaceId',
          operator: 'equals',
          value: workspaceId,
        },
      ],
      groupBy,
    };
    reportsService.generateAdoptionReport = jest
      .fn()
      .mockResolvedValue(DUMMY_REPORT_RESPONSE);
    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue([{ id: workspaceId }]);
    const result = await controller.generateAdoptionReport(
      workspaceId,
      reportDto,
      req,
    );
    expect(result).toBe(DUMMY_REPORT_RESPONSE);
  });

  it('In filter should generate adoption report', async () => {
    const workspaceList = [
      { partnerId: 1 },
      { partnerId: 2 },
      { partnerId: 3 },
    ];
    const reportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'workspaceId',
          operator: 'in',
          value: workspaceList.map((p) => p.partnerId),
        },
      ],
      groupBy,
    };
    reportsService.generateAdoptionReport = jest
      .fn()
      .mockResolvedValue(DUMMY_REPORT_RESPONSE);
    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue(
        workspaceList.map((p) => {
          return { id: p.partnerId };
        }),
      );
    const result = await controller.generateAdoptionReport(
      workspaceId,
      reportDto,
      req,
    );
    expect(result).toBe(DUMMY_REPORT_RESPONSE);
  });

  it('Equals filter should generate adherence report', async () => {
    const reportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'workspaceId',
          operator: 'equals',
          value: workspaceId,
        },
      ],
      groupBy,
    };
    reportsService.generateAdherenceReport = jest
      .fn()
      .mockResolvedValue(DUMMY_REPORT_RESPONSE);
    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue([{ id: workspaceId }]);
    const result = await controller.generateAdherenceReport(
      workspaceId,
      reportDto,
      req,
    );
    expect(result).toBe(DUMMY_REPORT_RESPONSE);
  });
});
