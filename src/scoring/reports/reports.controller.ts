import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Post,
  Query,
  Request,
  Res,
  UnauthorizedException,
  Version,
} from '@nestjs/common';
import {
  CreateReportDto,
  EarliestImpressionDateRequestDto,
  GetPreflightChannelAggregateScores200Response,
  GetCriteriaAggregateScoresV2200Response,
  InflightAggregateRequestDto,
  ReadReportDto as IReadReportDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringReportsService } from './scoring-reports.service';
import { ScoringReportFiltersService } from './scoring-report-filters.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { ApiParam, ApiQuery, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { readCriteria, readDetails } from '../scoring.permissions';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { Response } from 'express';
import { ReadWorkspaceSearchDto } from './internal-scoring-reports.interfaces';
import { ScoringReportOptionsDto } from './dtos/report-options.dto';
import { ReportFlagDto } from './dtos/report-flag.dto';
import { AggregationColumnDto } from '../../reports/model/aggregation-column.dto';
import { AccessibleReportTypesDto } from './dtos/accessible-report-types.dto';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { OAuth2Service as AuthorizationServiceSdk } from '@vidmob/vidmob-authorization-service-sdk';
import { Public } from '../../auth/decorators/public.decorator';
import { MediaService } from '@vidmob/vidmob-organization-service-sdk';
import { SortOrder } from '../../reports/model/sort-order';
import { SortOrderValidationPipe } from 'src/pagination-validators/sort-order-validation-pipe';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { MediaObjectAndScoresDto } from './dtos/media-object-and-scores.dto';
import { Observable } from 'rxjs';

type InflightSortBy =
  | 'passedPercent'
  | 'displayName'
  | 'fileType'
  | 'passedPercentByChannel'
  | 'passedPercentByGroup';

type InflightSortByChannel =
  | 'AMAZON'
  | 'ALL_PLATFORMS'
  | 'TWITTER'
  | 'FACEBOOK'
  | 'TIKTOK'
  | 'PINTEREST'
  | 'LINKEDIN'
  | 'DV360'
  | 'ADWORDS'
  | 'SNAPCHAT'
  | 'REDDIT';

/**
 * Local definition of the report dto to support swagger documentation
 */
class ReadReportDto implements IReadReportDto {
  data: string[];
  flags: ReportFlagDto[];
  aggregationColumns: AggregationColumnDto[];
}

@ApiTags('Reports')
@ApiSecurity('Bearer Token')
@Controller('reports')
export class ReportsController {
  private readonly logger = new Logger(ReportsController.name);

  constructor(
    private readonly reportsService: ScoringReportsService,
    private readonly scoringAuthService: ScoringAuthService,
    private readonly scoringReportFiltersService: ScoringReportFiltersService,
    private readonly authorizationServiceSdk: AuthorizationServiceSdk,
    private readonly mediaService: MediaService,
  ) {}

  private getRelatedAccessiblePartners(partnerId: number, req: any) {
    const userId = req['userId'] as number;
    return this.scoringAuthService.getRelatedAccessiblePartners(
      userId,
      partnerId,
    );
  }

  /**
   * A common method to determine if the user has access to all the partners in a report creation request
   * and to ensure those partners are all from the same organization.
   * @param partnerId - the partnerId the call is being made from (determines the org)
   * @param reportRequestDto - the report creation request
   * @param req - used to get the userId to validate access
   * @private
   */
  private async validateDataSourceAccessAndGrouping(
    partnerId: number,
    reportRequestDto: CreateReportDto,
    req: any,
  ) {
    const workspaceFilters = reportRequestDto.filters.filter(
      (x) => x.fieldName === 'workspaceId',
    );

    this.logger.debug(
      `The number of workspace filters is ${workspaceFilters.length}`,
    );
    //Check that the required partner / workspace filters are present
    if (workspaceFilters.length != 1) {
      throw new BadRequestException(
        'The report must have a single filter for workspaceId',
      );
    }
    const workspaceFilter = workspaceFilters[0];
    if (workspaceFilter.operator === 'between') {
      throw new BadRequestException(
        'Between is not a supported operator for a workspace filter',
      );
    }

    //Get a list of all workspaces in the same org as the starting partner that the user has access to
    const accessiblePartners = await this.getRelatedAccessiblePartners(
      partnerId,
      req,
    );
    this.logger.debug('Got back accessible partners', accessiblePartners);

    if (workspaceFilter.operator === 'equals') {
      this.canAccessPartner(partnerId, accessiblePartners);
    } else if (workspaceFilter.operator === 'in') {
      const partnerIdsToMatch = workspaceFilter.value as number[];
      this.canAccessAllPartners(partnerIdsToMatch, accessiblePartners);
    } else {
      throw new BadRequestException(
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        `${workspaceFilter.operator} is not a supported operator for a workspace filter`,
      );
    }

    const channelFilter = reportRequestDto.filters.find(
      (filter) => filter.fieldName === 'channel' && Array.isArray(filter.value),
    );

    if (channelFilter && Array.isArray(channelFilter.value)) {
      channelFilter.value = (channelFilter.value as string[]).map((value) =>
        value === 'AMAZONADVERTISING' ? 'AMAZON' : value,
      );
    }
  }

  private canAccessPartner(
    partnerId: number,
    accessiblePartners: ReadWorkspaceSearchDto[],
  ) {
    const canAccessPartner = accessiblePartners.some(
      (workspace) => `${workspace.id}` === `${partnerId}`,
    );
    if (!canAccessPartner) {
      throw new UnauthorizedException(
        `The user does not have access to workspace ${partnerId}`,
      );
    }
  }

  private canAccessAllPartners(
    partnerIdsToMatch: number[],
    accessiblePartners: ReadWorkspaceSearchDto[],
  ) {
    const hasAllPartnerIds = partnerIdsToMatch.every((partnerId) =>
      accessiblePartners.some(
        (workspace) => `${workspace.id}` === `${partnerId}`,
      ),
    );
    if (!hasAllPartnerIds) {
      throw new UnauthorizedException(
        `The user does not have access to all of the workspaces in the filter`,
      );
    }
  }

  /**
   * Retrieves the accessible report types based on the given workspace ID and the user ID.
   */
  @VmApiOkResponse({
    description:
      'Returns the accessible scoring report types based on workspace and user',
    type: AccessibleReportTypesDto,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the current workspace',
  })
  @Permissions(readDetails)
  @Get('workspace/:workspaceId/report-types')
  async getAccessibleReportTypes(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
  ) {
    return this.reportsService.getAccessibleReportTypes(workspaceId);
  }

  @VmApiOkResponse({
    description: 'The data options available for scoring report customization',
    type: ScoringReportOptionsDto,
  })
  @ApiParam({
    name: 'workspaceId',
    description: 'The id of the current workspace',
  })
  @Permissions(readDetails)
  @Get('workspace/:workspaceId/options')
  async getReportOptions(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Request() req: any,
  ) {
    const accessiblePartners = await this.getRelatedAccessiblePartners(
      workspaceId,
      req,
    );
    return this.reportsService.getReportOptions(accessiblePartners);
  }

  //simple get request for all markets

  @Get('markets')
  async getMarketsForReport() {
    return this.reportsService.getMarkets();
  }

  /**
   * Create and return a diversity report based on the input settings.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Diversity report successfully created',
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Post('diversity/workspace/:workspaceId')
  async generateDiversityReport(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateDiversityReport(reportDto);
  }

  @VmApiOkResponse({
    description: 'Diversity report successfully created',
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('2')
  @Post('diversity/workspace/:workspaceId')
  async generateDiversityReportV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateDiversityReportV2(reportDto);
  }

  /**
   * Create and return an adoption report based on the input settings.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Adoption report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Post('adoption/workspace/:workspaceId')
  async generateAdoptionReport(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateAdoptionReport(reportDto);
  }

  @VmApiOkResponse({
    description: 'Adoption report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('2')
  @Post('adoption/workspace/:workspaceId')
  async generateAdoptionReportV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateAdoptionReportV2(reportDto);
  }

  /**
   * Create and return an adherence report based on the input settings.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Adherence report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Post('adherence/workspace/:workspaceId')
  async generateAdherenceReport(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateAdherenceReport(reportDto, false);
  }

  /**
   * Create and return an adherence report based on the input settings.
   * This version supports analytics advanced filters.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Adherence report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('2')
  @Post('adherence/workspace/:workspaceId')
  async generateAdherenceReportV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    // todo: update validation
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    await this.scoringReportFiltersService.addOrganizationIdFilter(
      reportDto,
      workspaceId,
    );
    return this.reportsService.generateAdherenceReport(reportDto, true);
  }

  /**
   * Create and return an adherence norms report based on the input settings.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Adherence norms report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Post('adherence-norms/workspace/:workspaceId')
  async generateAdherenceNormsReport(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateAdherenceNormsReport(reportDto, false);
  }

  /**
   * Create and return an adherence norms report based on the input settings.
   * This version supports analytics advanced filters.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Adherence norms report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('2')
  @Post('adherence-norms/workspace/:workspaceId')
  async generateAdherenceNormsReportV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    await this.scoringReportFiltersService.addOrganizationIdFilter(
      reportDto,
      workspaceId,
    );
    const result = this.reportsService.generateAdherenceNormsReport(
      reportDto,
      true,
    );
    return result;
  }

  /**
   * Create and return an adherence norms report based on the input settings.
   * This version supports analytics advanced filters.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Adherence norms report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('3')
  @Post('adherence-norms/workspace/:workspaceId')
  async generateAdherenceNormsReportV3(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    await this.scoringReportFiltersService.addOrganizationIdFilter(
      reportDto,
      workspaceId,
    );
    const result =
      this.reportsService.generateAdherenceNormsReportV3(reportDto);
    return result;
  }

  /**
   * Create and return an impression-adherence report based on the input settings.
   * @param workspaceId - the workspace to generate the report for
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   */
  @VmApiOkResponse({
    description: 'Impression Adherence report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Post('impression-adherence/workspace/:workspaceId')
  async generateImpressionAdherenceReport(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    return this.reportsService.generateImpressionAdherenceReport(reportDto);
  }

  @VmApiOkResponse({
    description: 'Impression Adherence report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('2')
  @Post('impression-adherence/workspace/:workspaceId')
  async generateImpressionAdherenceReportV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    await this.scoringReportFiltersService.addOrganizationIdFilter(
      reportDto,
      workspaceId,
    );
    return this.reportsService.generateImpressionAdherenceReportV2(reportDto);
  }

  @VmApiOkResponse({
    description: 'Impression Adherence report successfully created',
    type: ReadReportDto,
  })
  @ApiParam({ name: 'workspaceId', description: 'The id of the workspace' })
  @Permissions(readDetails)
  @Version('3')
  @Post('impression-adherence/workspace/:workspaceId')
  async generateImpressionAdherenceReportV3(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    await this.scoringReportFiltersService.addOrganizationIdFilter(
      reportDto,
      workspaceId,
    );
    return this.reportsService.generateImpressionAdherenceReportV3(reportDto);
  }

  /**
   * Create and return an adoption report CSV based on the input settings.
   * @param workspaceId - the workspace to generate the report from (for input validation)
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   * @param res - express response object
   */
  @Permissions(readDetails)
  @Post('adoption/workspace/:workspaceId/csv')
  async generateAdoptionReportCsv(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
    @Res() res: Response,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    res.setHeader('Content-Type', 'text/csv');
    res.send(await this.reportsService.generateAdoptionReportCsv(reportDto));
  }

  /**
   * Create and return an adherence report CSV based on the input settings.
   * @param workspaceId - the workspace to generate the report from (for input validation)
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   * @param res - express response object
   */
  @Permissions(readDetails)
  @Post('adherence/workspace/:workspaceId/csv')
  async generateAdherenceReportCsv(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
    @Res() res: Response,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    res.setHeader('Content-Type', 'text/csv');
    const csvStream = await this.reportsService.generateAdherenceReportCsv(
      reportDto,
      false,
    );
    csvStream.pipe(res);
  }

  /**
   * Create and return an adherence report CSV based on the input settings.
   * This version supports analytics advanced filters.
   * @param workspaceId - the workspace to generate the report from (for input validation)
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   * @param res - express response object
   */
  @Permissions(readDetails)
  @Version('2')
  @Post('adherence/workspace/:workspaceId/csv')
  async generateAdherenceReportCsvV2(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
    @Res() res: Response,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    res.setHeader('Content-Type', 'text/csv');
    await this.scoringReportFiltersService.addOrganizationIdFilter(
      reportDto,
      workspaceId,
    );
    const csvStream = await this.reportsService.generateAdherenceReportCsv(
      reportDto,
      true,
    );
    csvStream.pipe(res);
  }

  /**
   * Create and return an impression-adherence report CSV based on the input settings.
   * @param workspaceId - the workspace to generate the report from (for input validation)
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   * @param res - express response object
   */
  @Permissions(readDetails)
  @Post('impression-adherence/workspace/:workspaceId/csv')
  async generateImpressionAdherenceReportCsv(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
    @Res() res: Response,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      'attachment; impression-adherence.csv',
    );
    const csvStream =
      await this.reportsService.generateImpressionAdherenceReportCsv(reportDto);
    csvStream.pipe(res);
  }

  /**
   * Return metadata on the media included in an adherence report row
   * @param workspaceId - the workspace to generate the report from (for input validation)
   * @param reportDto - the report settings
   * @param req - express request object (to get the userId)
   * @param res - express response object
   */
  @Permissions(readDetails)
  @Post('adherence/workspace/:workspaceId/metadata/csv')
  async generateAdherenceReportMetadataCsv(
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body() reportDto: CreateReportDto,
    @Request() req: Request,
    @Res() res: Response,
  ) {
    await this.validateDataSourceAccessAndGrouping(workspaceId, reportDto, req);
    res.setHeader('Content-Type', 'text/csv');
    res.send(
      await this.reportsService.generateAdherenceReportMetadataCsv(reportDto),
    );
  }

  /**
   * Redirects to download url for media files in the adherence report row
   * Is public since link is shared externally in metadata csv
   */
  @Public()
  @Get('noauth/adherence/media/:mediaId/download')
  async downloadAdherenceMetadataMediaById(
    @Param('mediaId', ParseIntPipe) mediaId: number,
    @Query('token') token: string,
    @Res() response: Response,
  ) {
    try {
      // will throw UnauthorizedException if media id is not valid for this token
      await this.authorizationServiceSdk
        .validateJWTAndMetadataAsPromise({
          jwt: token,
          validationMetadata: { mediaId },
        })
        .then((response) => {
          return response.result;
        });

      const { result } = await this.mediaService.getMediaByIdAsPromise(mediaId);
      return response.redirect(result.downloadUrl);
    } catch (error) {
      throw new UnauthorizedException('This URL is invalid.');
    }
  }

  /**
   * Returns the earliest impression date for ad accounts in the given request, so the FE can present lifetime data
   * @param organizationId
   * @param req
   * @param earliestImpressionDateRequest
   */
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @VmApiOkResponse({
    description: 'Earliest impression date for ad accounts',
    type: () => EarliestImpressionDateRequestDto,
  })
  @Post('/organization/:organizationId/adAccount/earliest-impression-date')
  @Permissions(readCriteria)
  async getEarliestImpressionDate(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Body() earliestImpressionDateRequest: EarliestImpressionDateRequestDto,
  ) {
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    const platformAccountIds =
      await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
        earliestImpressionDateRequest,
        userDetails,
      );
    return this.reportsService.getAccountsEarliestImpressionDate({
      ...earliestImpressionDateRequest,
      platformAccountIds,
    });
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Version('2')
  @Post('organization/:organizationId/in-flight/summary')
  @Permissions(readCriteria)
  async getInFlightReportGroupAggregate(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Body() inflightAggregateRequestDto: InflightAggregateRequestDto,
  ): Promise<Observable<GetPreflightChannelAggregateScores200Response>> {
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    const platformAccountIds =
      await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
        inflightAggregateRequestDto,
        userDetails,
      );

    return this.reportsService.getInFlightReportGroupAggregate(
      {
        ...inflightAggregateRequestDto,
        platformAccountIds,
      },
      organizationId,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Version('2')
  @Post('organization/:organizationId/in-flight/criteria')
  @Permissions(readCriteria)
  async getInFlightReportCriteriaAggregate(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Body() inflightAggregateRequestDto: InflightAggregateRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<GetCriteriaAggregateScoresV2200Response> {
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    const platformAccountIds =
      await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
        inflightAggregateRequestDto,
        userDetails,
      );

    return this.reportsService.getInFlightReportCriteriaAggregate(
      {
        ...inflightAggregateRequestDto,
        platformAccountIds,
      },
      paginationOptions,
      organizationId,
    );
  }

  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the media by',
    example: 'passedPercent',
  })
  @ApiQuery({
    name: 'sortByChannel',
    description:
      "What channel's passedPercentByChannel to sort by. Only in use if sortBy = passedPercentByChannel",
    example: 'ALL_PLATFORMS',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order to sort the media by',
    example: 'ASC',
    enum: SortOrder,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Version('3')
  @Post('organization/:organizationId/in-flight/media-list')
  @Permissions(readCriteria)
  async getInflightMediaAndScoresV2(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Body() inflightAggregateRequestDto: InflightAggregateRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('sortOrder', SortOrderValidationPipe)
    sortOrder: SortOrder = SortOrder.ASC,
    @Query('sortBy')
    sortBy: InflightSortBy = 'displayName',
    @Query('sortByChannel')
    sortByChannel: InflightSortByChannel = 'ALL_PLATFORMS',
    @Query('sortByGroupId') sortByGroupId?: string,
  ) {
    const useDetailedScoreStatus = true;
    return this.getInflightMediaAndScores(
      organizationId,
      req,
      inflightAggregateRequestDto,
      paginationOptions,
      sortOrder,
      sortBy,
      sortByChannel,
      sortByGroupId,
      useDetailedScoreStatus,
    );
  }

  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the media by',
    example: 'passedPercent',
  })
  @ApiQuery({
    name: 'sortByChannel',
    description:
      "What channel's passedPercentByChannel to sort by. Only in use if sortBy = passedPercentByChannel",
    example: 'ALL_PLATFORMS',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order to sort the media by',
    example: 'ASC',
    enum: SortOrder,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Version('2')
  @Post('organization/:organizationId/in-flight/media-list')
  @Permissions(readCriteria)
  async getInflightMediaAndScores(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Body() inflightAggregateRequestDto: InflightAggregateRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('sortOrder', SortOrderValidationPipe)
    sortOrder: SortOrder = SortOrder.ASC,
    @Query('sortBy')
    sortBy: InflightSortBy = 'displayName',
    @Query('sortByChannel')
    sortByChannel: InflightSortByChannel = 'ALL_PLATFORMS',
    @Query('sortByGroupId') sortByGroupId?: string,
    useDetailedScoreStatus = false,
  ): Promise<PaginatedResultArray<MediaObjectAndScoresDto>> {
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    inflightAggregateRequestDto.platformAccountIds =
      await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
        inflightAggregateRequestDto,
        userDetails,
      );

    return this.reportsService.getInflightReportMediaList(
      organizationId,
      inflightAggregateRequestDto,
      paginationOptions,
      {
        sortOrder,
        sortBy,
        sortByChannel,
        sortByGroupId,
      },
      useDetailedScoreStatus,
    );
  }

  @ApiQuery({
    name: 'sortBy',
    description: 'The field to sort the media by',
    example: 'passedPercent',
  })
  @ApiQuery({
    name: 'sortByChannel',
    description:
      "What channel's passedPercentByChannel to sort by. Only in use if sortBy = passedPercentByChannel",
    example: 'ALL_PLATFORMS',
  })
  @ApiQuery({
    name: 'sortOrder',
    description: 'The order to sort the media by',
    example: 'ASC',
    enum: SortOrder,
  })
  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization workspaces and accounts belong to',
  })
  @Version('2')
  @Post('organization/:organizationId/in-flight/csv')
  async downloadInflightReportCSV(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Res() res: Response,
    @Body() inflightAggregateRequestDto: InflightAggregateRequestDto,
    @Query('sortOrder', SortOrderValidationPipe)
    sortOrder: SortOrder = SortOrder.ASC,
    @Query('sortBy')
    sortBy: InflightSortBy = 'displayName',
    @Query('sortByChannel')
    sortByChannel: InflightSortByChannel = 'ALL_PLATFORMS',
    sortByGroupId: string,
  ) {
    const userDetails = {
      organizationId,
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    inflightAggregateRequestDto.platformAccountIds =
      await this.scoringAuthService.validateAndReturnPlatformAccountIdsForInflightReport(
        inflightAggregateRequestDto,
        userDetails,
      );

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; inflight-report.csv');
    res.send(
      await this.reportsService.generateInflightReportCSV(
        req.userId,
        organizationId,
        inflightAggregateRequestDto,
        {
          sortOrder,
          sortBy,
          sortByChannel,
          sortByGroupId,
        },
      ),
    );
  }
}
