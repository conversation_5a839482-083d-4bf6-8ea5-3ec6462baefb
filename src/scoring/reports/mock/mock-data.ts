import { IN_FLIGHT, PRE_FLIGHT } from '../constants/constants';
import { CreateReportDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';
import { AdvancedFilterEnum } from '@vidmob/vidmob-nestjs-common';
import {
  AdherenceReportV3DataItemDto,
  CreativeOrImpressionCountDataDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringNormsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/scoringNormsResponseDto';

export type MockDEIResponse = {
  status: string;
  result: any; // Replace 'any' with a more specific type if needed
};

export const MOCK_RESPONSE_DTO = {
  data: [
    {
      totalScored: '5',
      month: '2023-01',
      batchType: IN_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '3',
      month: '2023-02',
      batchType: IN_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '3',
      month: '2023-03',
      batchType: IN_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '2',
      month: '2023-03',
      batchType: PRE_FLIGHT,
      market: 'Albania',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '2',
      month: '2023-03',
      batchType: PRE_FLIGHT,
      market: 'Belgium',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '1',
      month: '2023-03',
      batchType: PRE_FLIGHT,
      market: 'Japan',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '1',
      month: '2023-03',
      batchType: PRE_FLIGHT,
      market: 'Norway',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '1',
      month: '2023-03',
      batchType: PRE_FLIGHT,
      market: 'NOT_SPECIFIED',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '11',
      month: '2023-03',
      batchType: PRE_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '5',
      month: '2023-04',
      batchType: IN_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '1',
      month: '2023-04',
      batchType: PRE_FLIGHT,
      market: 'Albania',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '13',
      month: '2023-04',
      batchType: PRE_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '10',
      month: '2023-05',
      batchType: PRE_FLIGHT,
      market: 'Brazil',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '4',
      month: '2023-05',
      batchType: PRE_FLIGHT,
      market: 'Canada',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '3',
      month: '2023-05',
      batchType: PRE_FLIGHT,
      market: 'Japan',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '4',
      month: '2023-05',
      batchType: PRE_FLIGHT,
      market: 'Mexico',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '17',
      month: '2023-05',
      batchType: PRE_FLIGHT,
      market: 'NOT_SPECIFIED',
      workspace: 'Things Are Fine',
    },
    {
      totalScored: '10',
      month: '2023-05',
      batchType: PRE_FLIGHT,
      market: 'United States',
      workspace: 'Things Are Fine',
    },
  ],
};

export const MOCK_BRANDS = [
  {
    id: '42376154-a6fe-495e-a279-400825694846',
    name: 'Pepsi',
  },
  {
    id: '7534120d-2f8f-4ba8-98b8-35cfd7906907',
    name: 'Coke',
  },
];

export const MOCK_REPORT_RESULT = {
  data: {
    result: MOCK_RESPONSE_DTO,
  },
};

const skeletonCreateReportDto: CreateReportDto = {
  groupBy: {
    columns: ['criteria', 'batchType'],
    rows: ['market', 'workspace'],
  },
  filters: [
    {
      fieldName: 'mediaCreateDate',
      operator: ReportFilterOperator.Between,
      value: ['2024-05-01T00:00:00.000Z', '2024-08-01T00:00:00.000Z'],
    },
    {
      fieldName: 'channel',
      operator: ReportFilterOperator.In,
      value: ['ADWORDS', 'ALL_PLATFORMS', 'AMAZON', 'DV360', 'FACEBOOK'],
    },
    {
      fieldName: 'market',
      operator: ReportFilterOperator.Equals,
      value: 'usa',
    },
    {
      fieldName: 'workspaceId',
      operator: ReportFilterOperator.Equals,
      value: '23245',
    },
    {
      fieldName: 'brandId',
      operator: ReportFilterOperator.In,
      value: [
        '731d44b2-402d-4299-85b7-80c9a9fa2486',
        'df9f97f9-e62d-4761-8ba0-153fd6edb053',
        'af2bb426-1452-4681-bf3d-4cfcbd639431',
        'NOT_SPECIFIED',
      ],
    },
    {
      fieldName: 'criteriaIsOptional',
      operator: ReportFilterOperator.Equals,
      value: false,
    },
  ],
};

export const MOCK_IN_FLIGHT_REPORT_DTO: CreateReportDto = {
  ...skeletonCreateReportDto,
  filters: [
    ...skeletonCreateReportDto.filters,
    {
      fieldName: 'batchType',
      operator: ReportFilterOperator.Equals,
      value: IN_FLIGHT,
    },
  ],
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  analyticsFilters: {
    FACEBOOK: [
      {
        type: AdvancedFilterEnum.CAMPAIGN_OBJECTIVE,
        values: ['CONVERSIONS', 'AWARENESS'],
      },
      {
        type: AdvancedFilterEnum.ADSET_IDENTIFIER,
        values: ['adset-id-one', 'adset-id-two'],
      },
      {
        type: AdvancedFilterEnum.AD_PLACEMENT,
        values: ['placement:one', 'placement:two'],
      },
    ],
  },
};

export const MOCK_PRE_FLIGHT_REPORT_DTO: CreateReportDto = {
  ...skeletonCreateReportDto,
  filters: [
    ...skeletonCreateReportDto.filters,
    {
      fieldName: 'batchType',
      operator: ReportFilterOperator.Equals,
      value: PRE_FLIGHT,
    },
  ],
};

export const MOCK_PRE_FLIGHT_METADATA_RESPONSE = {
  status: 'OK',
  result: [
    {
      platform: 'ALL_PLATFORMS',
      brand: 'Brand 13',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      campaignObjective: '-',
      mediaId: 191749,
      mediaName: 'pepsi-6s-test.mp4',
      mediaType: 'VIDEO',
      assetDuration: 6,
      date: '2023-12-06',
      criteriaAndScores: {
        'AMAZON / Brand Name or Logo Early': 1,
        'AMAZON / Call to Action Present': 0,
        'AMAZON / Disclaimer(s) Present': 1,
        'AMAZON / Fits Aspect Ratio': 0,
        'AMAZON / Motion Early': 1,
        'AMAZON / Text Present': 1,
        'AMAZON / Video Length Within Range': 1,
        'AMAZON / Word Limit Per Frame': 0,
      },
    },
    {
      platform: 'ADWORDS',
      brand: 'Brand 9',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      campaignObjective: '-',
      mediaId: 191749,
      mediaName: 'pepsi-6s-test.mp4',
      mediaType: 'VIDEO',
      assetDuration: 6,
      date: '2023-12-06',
      criteriaAndScores: {
        'AMAZON / Brand Name or Logo Early': 0,
        'AMAZON / Call to Action Present': 0,
        'AMAZON / Disclaimer(s) Present': 1,
        'AMAZON / Fits Aspect Ratio': 1,
        'AMAZON / Text Present': 1,
        'AMAZON / Video Length Within Range': 1,
        'AMAZON / Word Limit Per Frame': 0,
      },
    },
    {
      platform: 'SNAPCHAT',
      brand: 'NOT_SPECIFIED',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      campaignObjective: '-',
      mediaId: 158064,
      mediaName: 'Screen_Recording_2022-08-30_at_1.45.53_PM.mov',
      mediaType: 'VIDEO',
      assetDuration: 18,
      date: '2022-09-12',
      criteriaAndScores: {
        'AMAZON / Brand Name or Logo Early': 0,
        'AMAZON / Call to Action Present': 0,
        'AMAZON / Disclaimer(s) Present': 0,
        'AMAZON / Fits Aspect Ratio': 1,
        'AMAZON / Text Present': 0,
        'AMAZON / Video Length Within Range': 1,
        'AMAZON / Word Limit Per Frame': 1,
      },
    },
  ],
};

export const MOCK_IN_FLIGHT_METADATA_RESPONSE = {
  status: 'OK',
  result: [
    {
      platform: 'TIKTOK',
      brand: 'Brand 10',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      adAccountName: 'VidMob 1 - Acquisition',
      adAccountId: '***************',
      campaignName: 'OM|Lead Ads|Prospecting',
      campaignId: '*************',
      campaignObjective: 'LEAD_GENERATION',
      adGroupOrSetName: 'OM|1% - Dyn',
      adGroupOrSetId: '*************',
      mediaId: 158059,
      platformMediaId: '****************',
      mediaName: 'Auto_Cropped_AR_4_X_5_DCO_V4_VidMob_Data_Driven_1x1_D.mp4',
      mediaType: 'VIDEO',
      assetDuration: 29,
      date: '2022-09-12',
      impressions: 1015,
      adName: 'Brand Ads 7/8/22',
      adId: '*************',
      adTypes: [],
      criteriaAndScores: {
        "TIKTOK / Includes: Test CTA in the creative's audio track, anytime": 0,
        'TIKTOK / Speech present anytime': 1,
      },
    },
    {
      platform: 'FACEBOOK',
      brand: 'Brand 10',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      adAccountName: 'VidMob 1 - Acquisition',
      adAccountId: '***************',
      campaignName: 'OM|Lead Ads|Prospecting',
      campaignId: '*************',
      campaignObjective: 'LEAD_GENERATION',
      adGroupOrSetName: 'OM|1%',
      adGroupOrSetId: '*************',
      mediaId: 149168,
      platformMediaId: '***************',
      mediaName: 'are your ads built for success.mp4',
      mediaType: 'VIDEO',
      assetDuration: 15,
      date: '2022-01-22',
      impressions: 2598110,
      adName: 'Demo Video V2 - Copy',
      adId: '*************',
      adTypes: [],
      criteriaAndScores: {
        'FACEBOOK / Includes brand colors: [0,48,135] included in the creative': 0,
        'FACEBOOK / Speech present anytime': 1,
        "FACEBOOK / Includes: Test CTA in the creative's audio track, anytime": 0,
      },
    },
    {
      platform: 'FACEBOOK',
      brand: 'Brand 10',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      adAccountName: 'VidMob 1 - Acquisition',
      adAccountId: '***************',
      campaignName: 'OM|Remarketing',
      campaignId: '*************',
      campaignObjective: 'CONVERSIONS',
      adGroupOrSetName: 'OM|Website Visitors 10 Days',
      adGroupOrSetId: '*************',
      mediaId: 151841,
      platformMediaId: '6e58220010ae2992e44ff80d5f08ee9c',
      mediaName: 'static-does-model-16x9.jpg_105',
      mediaType: 'IMAGE',
      assetDuration: 0,
      date: '2022-03-21',
      impressions: 810260,
      adName: 'Eye Gaze Model - Copy',
      adId: '*************',
      adTypes: [],
      criteriaAndScores: {
        'FACEBOOK / At least 10 visual cuts/shots within the first 20 seconds': 0,
        "FACEBOOK / Includes: Test CTA in the creative's audio track, anytime": 0,
        'FACEBOOK / Creative contains UGC-style visuals': 0,
        'FACEBOOK / Video length is within 2 and 7 seconds': 0,
        'FACEBOOK / Includes brand colors: [0,48,135] included in the creative': 0,
      },
    },
    {
      platform: 'REDDIT',
      brand: 'Brand 10',
      market: 'usa',
      workspace: 'Things Are Fine',
      workspaceId: 23142,
      adAccountName: 'VidMob 1 - Acquisition',
      adAccountId: '***************',
      campaignName: 'OM|Lead Ads|Prospecting',
      campaignId: '*************',
      campaignObjective: 'LEAD_GENERATION',
      adGroupOrSetName: 'OM|1% - Dyn',
      adGroupOrSetId: '*************',
      mediaId: 158092,
      platformMediaId: '***************',
      mediaName: 'Auto_Cropped_AR_4_X_5_DCO_VidMob_Data_Driven_1x1_C.mp4',
      mediaType: 'VIDEO',
      assetDuration: 29,
      date: '2022-09-14',
      impressions: 54810,
      adName: 'Brand Ads 7/8/22',
      adId: '*************',
      adTypes: [],
      criteriaAndScores: {
        'REDDIT / Includes brand colors: [0,48,135] included in the creative': 1,
        'REDDIT / Speech present anytime': 1,
      },
    },
  ],
};

export const expectedInFlightMetadataCSV = `"Platform","Brand","Market","Workspace Name","Workspace ID","Ad Account Name","Ad Account ID","Campaign Name","Campaign ID","Campaign Objective","Ad Group/Set Name","Ad Group/Set ID","Media ID","Platform Media ID","Media Name","Link to Download","Link Expiration Date","Media Type","Asset Duration","Date","Impressions","Ad Name","Ad ID","Ad Types","Overall Score","FACEBOOK / At least 10 visual cuts/shots within the first 20 seconds","FACEBOOK / Creative contains UGC-style visuals","FACEBOOK / Includes brand colors: [0,48,135] included in the creative","FACEBOOK / Includes: Test CTA in the creative's audio track, anytime","FACEBOOK / Speech present anytime","FACEBOOK / Video length is within 2 and 7 seconds","REDDIT / Includes brand colors: [0,48,135] included in the creative","REDDIT / Speech present anytime","TIKTOK / Includes: Test CTA in the creative's audio track, anytime","TIKTOK / Speech present anytime"
"TIKTOK","Brand 10","United States","Things Are Fine",23142,"VidMob 1 - Acquisition","***************","OM|Lead Ads|Prospecting","*************","LEAD_GENERATION","OM|1% - Dyn","*************",158059,"****************","Auto_Cropped_AR_4_X_5_DCO_V4_VidMob_Data_Driven_1x1_D.mp4","https://bff.vidmob.com/v1/reports/noauth/adherence/media/158059/download?token=json.web.token","Tue Jan 16 2024","VIDEO",29,"2022-09-12",1015,"Brand Ads 7/8/22","*************",,50,-,-,-,-,-,-,-,-,0,1
"FACEBOOK","Brand 10","United States","Things Are Fine",23142,"VidMob 1 - Acquisition","***************","OM|Lead Ads|Prospecting","*************","LEAD_GENERATION","OM|1%","*************",149168,"***************","are your ads built for success.mp4","https://bff.vidmob.com/v1/reports/noauth/adherence/media/149168/download?token=json.web.token","Tue Jan 16 2024","VIDEO",15,"2022-01-22",2598110,"Demo Video V2 - Copy","*************",,33.33333,-,-,0,0,1,-,-,-,-,-
"FACEBOOK","Brand 10","United States","Things Are Fine",23142,"VidMob 1 - Acquisition","***************","OM|Remarketing","*************","CONVERSIONS","OM|Website Visitors 10 Days","*************",151841,"6e58220010ae2992e44ff80d5f08ee9c","static-does-model-16x9.jpg_105","https://bff.vidmob.com/v1/reports/noauth/adherence/media/151841/download?token=json.web.token","Tue Jan 16 2024","IMAGE",0,"2022-03-21",810260,"Eye Gaze Model - Copy","*************",,0,0,0,0,0,-,0,-,-,-,-
"REDDIT","Brand 10","United States","Things Are Fine",23142,"VidMob 1 - Acquisition","***************","OM|Lead Ads|Prospecting","*************","LEAD_GENERATION","OM|1% - Dyn","*************",158092,"***************","Auto_Cropped_AR_4_X_5_DCO_VidMob_Data_Driven_1x1_C.mp4","https://bff.vidmob.com/v1/reports/noauth/adherence/media/158092/download?token=json.web.token","Tue Jan 16 2024","VIDEO",29,"2022-09-14",54810,"Brand Ads 7/8/22","*************",,100,-,-,-,-,-,-,1,1,-,-`;

export const expectedPreFlightMetadataCSV = `"Platform","Brand","Market","Workspace Name","Workspace ID","Campaign Objective","Media ID","Media Name","Link to Download","Link Expiration Date","Media Type","Asset Duration","Date","Overall Score","AMAZON / Brand Name or Logo Early","AMAZON / Call to Action Present","AMAZON / Disclaimer(s) Present","AMAZON / Fits Aspect Ratio","AMAZON / Motion Early","AMAZON / Text Present","AMAZON / Video Length Within Range","AMAZON / Word Limit Per Frame"
"Standard Criteria","Brand 13","United States","Things Are Fine",23142,"-",191749,"pepsi-6s-test.mp4","https://bff.vidmob.com/v1/reports/noauth/adherence/media/191749/download?token=json.web.token","Tue Jan 16 2024","VIDEO",6,"2023-12-06",62.5,1,0,1,0,1,1,1,0
"ADWORDS","Brand 9","United States","Things Are Fine",23142,"-",191749,"pepsi-6s-test.mp4","https://bff.vidmob.com/v1/reports/noauth/adherence/media/191749/download?token=json.web.token","Tue Jan 16 2024","VIDEO",6,"2023-12-06",57.14286,0,0,1,1,-,1,1,0
"SNAPCHAT","NOT_SPECIFIED","United States","Things Are Fine",23142,"-",158064,"Screen_Recording_2022-08-30_at_1.45.53_PM.mov","https://bff.vidmob.com/v1/reports/noauth/adherence/media/158064/download?token=json.web.token","Tue Jan 16 2024","VIDEO",18,"2022-09-12",42.85714,0,0,0,1,-,0,1,1`;

export const mockCriteriaGroupImpressionCountData: CreativeOrImpressionCountDataDto[] =
  [
    {
      row: {},
      data: {
        value: 253100,
        type: 'number',
      },
      column: {
        market: 'Overall average',
        workspace: 'Overall average',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 8204,
        type: 'number',
      },
      column: {
        market: 'Overall average',
        workspace: 'Overall average',
      },
    },
    {
      row: {},
      data: {
        value: 8310,
        type: 'number',
      },
      column: {
        market: 'usa',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 3674,
        type: 'number',
      },
      column: {
        market: 'Overall average',
        workspace: 'Overall average',
      },
    },
    {
      row: {},
      data: {
        value: 4819,
        type: 'number',
      },
      column: {
        market: 'usa',
        workspace: 'Learning',
      },
    },
    {
      row: {},
      data: {
        value: 7712,
        type: 'number',
      },
      column: {
        market: 'usa',
        workspace: 'Teaching',
      },
    },
    {
      row: {},
      data: {
        value: 2750,
        type: 'number',
      },
      column: {
        market: 'can',
      },
    },
    {
      row: {},
      data: {
        value: 6184,
        type: 'number',
      },
      column: {
        market: 'can',
        workspace: 'Learning',
      },
    },
    {
      row: {},
      data: {
        value: 4960,
        type: 'number',
      },
      column: {
        market: 'can',
        workspace: 'Teaching',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 8147,
        type: 'number',
      },
      column: {
        market: 'usa',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 3204,
        type: 'number',
      },
      column: {
        market: 'usa',
        workspace: 'Learning',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 9693,
        type: 'number',
      },
      column: {
        market: 'usa',
        workspace: 'Teaching',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 4590,
        type: 'number',
      },
      column: {
        market: 'can',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 275,
        type: 'number',
      },
      column: {
        market: 'can',
        workspace: 'Learning',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: 'No group',
          displayName: 'No group',
          color: null,
        },
      },
      data: {
        value: 7324,
        type: 'number',
      },
      column: {
        market: 'can',
        workspace: 'Teaching',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 3674,
        type: 'number',
      },
      column: {
        market: 'usa',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 8287,
        type: 'number',
      },
      column: {
        market: 'usa',
        workspace: 'Learning',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 4539,
        type: 'number',
      },
      column: {
        market: 'usa',
        workspace: 'Teaching',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 9668,
        type: 'number',
      },
      column: {
        market: 'can',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 9635,
        type: 'number',
      },
      column: {
        market: 'can',
        workspace: 'Learning',
      },
    },
    {
      row: {
        criteriaGroup: {
          id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
          displayName: 'User Generated Content',
          color: '#000000',
        },
      },
      data: {
        value: 3982,
        type: 'number',
      },
      column: {
        market: 'can',
        workspace: 'Teaching',
      },
    },
  ] as CreativeOrImpressionCountDataDto[];

export const mockChannelCreativeCountData: CreativeOrImpressionCountDataDto[] =
  [
    {
      row: {},
      data: {
        value: 120333,
        type: 'number',
      },
      column: {
        brand: 'Overall average',
      },
    },
    {
      row: {
        channel: {
          id: 'LINKEDIN',
          displayName: 'LINKEDIN',
          color: null,
        },
      },
      data: {
        value: 20589,
        type: 'number',
      },
      column: {
        brand: 'Overall average',
      },
    },
    {
      row: {
        channel: {
          id: 'REDDIT',
          displayName: 'REDDIT',
          color: null,
        },
      },
      data: {
        value: 78965,
        type: 'number',
      },
      column: {
        brand: 'Overall average',
      },
    },
    {
      row: {
        channel: {
          id: 'SNAPCHAT',
          displayName: 'SNAPCHAT',
          color: null,
        },
      },
      data: {
        value: 98097,
        type: 'number',
      },
      column: {
        brand: 'Overall average',
      },
    },
    {
      row: {
        channel: {
          id: 'AMAZON',
          displayName: 'AMAZON',
          color: null,
        },
      },
      data: {
        value: 879,
        type: 'number',
      },
      column: {
        brand: 'Overall average',
      },
    },
    {
      row: {
        channel: {
          id: 'FACEBOOK',
          displayName: 'FACEBOOK',
          color: null,
        },
      },
      data: {
        value: 78799,
        type: 'number',
      },
      column: {
        brand: 'Overall average',
      },
    },
  ] as CreativeOrImpressionCountDataDto[];

export const mockV3AdherenceReportData = [
  {
    row: {
      criteria: {
        id: 'LINKEDIN:CUSTOM_CRITERIA',
        criteriaIdentifier: 'CUSTOM_CRITERIA',
        criteriaParameters: '{}',
        criteriaPlatform: 'LINKEDIN',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
        customIconUrls: ['https://website.com/logo.png'],
      },
      criteriaGroup: {
        id: 'No group',
        displayName: 'No group',
        color: null,
      },
      channel: {
        id: 'LINKEDIN',
        displayName: 'LINKEDIN',
        color: null,
      },
    },
    column: {
      workspace: 'Learning',
      workspaceId: 1111,
      brand: 'Textbooks',
      brandId: 'af2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'usa',
      marketId: 'usa',
    },
    data: {
      type: 'percent',
      value: [60, 82],
    },
  },
  {
    row: {
      criteria: {
        id: 'REDDIT:CUSTOM_CRITERIA',
        criteriaIdentifier: 'CUSTOM_CRITERIA',
        criteriaParameters: '{}',
        criteriaPlatform: 'REDDIT',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
        customIconUrls: ['https://website.com/logo.png'],
      },
      criteriaGroup: {
        id: 'No group',
        displayName: 'No group',
        color: null,
      },
      channel: {
        id: 'REDDIT',
        displayName: 'REDDIT',
        color: null,
      },
    },
    column: {
      workspace: 'Teaching',
      workspaceId: 2222,
      brand: 'Textbooks',
      brandId: 'af2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'usa',
      marketId: 'usa',
    },
    data: {
      type: 'percent',
      value: [10, 12],
    },
  },
  {
    row: {
      criteria: {
        id: 'SNAPCHAT:CUSTOM_CRITERIA',
        criteriaIdentifier: 'CUSTOM_CRITERIA',
        criteriaParameters: '{}',
        criteriaPlatform: 'SNAPCHAT',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: null,
        customIconUrls: [],
      },
      criteriaGroup: {
        id: 'No group',
        displayName: 'No group',
        color: null,
      },
      channel: {
        id: 'SNAPCHAT',
        displayName: 'SNAPCHAT',
        color: null,
      },
    },
    column: {
      workspace: 'Learning',
      workspaceId: 1111,
      brand: 'School Bags',
      brandId: '8f2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'can',
      marketId: 'can',
    },
    data: {
      type: 'percent',
      value: [19, 20],
    },
  },
  {
    row: {
      criteria: {
        id: 'LINKEDIN:CUSTOM_CRITERIA',
        criteriaIdentifier: 'CUSTOM_CRITERIA',
        criteriaParameters: '{}',
        criteriaPlatform: 'LINKEDIN',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
        customIconUrls: ['https://website.com/logo.png'],
      },
      criteriaGroup: {
        id: 'No group',
        displayName: 'No group',
        color: null,
      },
      channel: {
        id: 'LINKEDIN',
        displayName: 'LINKEDIN',
        color: null,
      },
    },
    column: {
      workspace: 'Teaching',
      workspaceId: 2222,
      brand: 'School Bags',
      brandId: '8f2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'can',
      marketId: 'can',
    },
    data: {
      type: 'percent',
      value: [5, 10],
    },
  },
  {
    row: {
      criteria: {
        id: 'LINKEDIN:CUSTOM_CRITERIA',
        criteriaIdentifier: 'CUSTOM_CRITERIA',
        criteriaParameters: '{}',
        criteriaPlatform: 'LINKEDIN',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
        customIconUrls: ['https://website.com/logo.png'],
      },
      criteriaGroup: {
        id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
        displayName: 'User Generated Content',
        color: '#000000',
      },
      channel: {
        id: 'LINKEDIN',
        displayName: 'LINKEDIN',
        color: null,
      },
    },
    column: {
      workspace: 'Learning',
      workspaceId: 1111,
      brand: 'Textbooks',
      brandId: 'af2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'usa',
      marketId: 'usa',
    },
    data: {
      type: 'percent',
      value: [14, 21],
    },
  },
  {
    row: {
      criteria: {
        id: 'AMAZON:CUSTOM_CRITERIA',
        criteriaIdentifier: 'CUSTOM_CRITERIA',
        criteriaParameters: '{}',
        criteriaPlatform: 'AMAZON',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
        customIconUrls: ['https://website.com/logo.png'],
      },
      criteriaGroup: {
        id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
        displayName: 'User Generated Content',
        color: '#000000',
      },
      channel: {
        id: 'AMAZON',
        displayName: 'AMAZON',
        color: null,
      },
    },
    column: {
      workspace: 'Teaching',
      workspaceId: 2222,
      brand: 'Textbooks',
      brandId: 'af2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'usa',
      marketId: 'usa',
    },
    data: {
      type: 'percent',
      value: [5, 7],
    },
  },
  {
    row: {
      criteria: {
        criteriaIdentifier: 'CUSTOM_COLOR',
        criteriaParameters: '{}',
        criteriaPlatform: 'SNAPCHAT',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: null,
        customIconUrls: [],
      },
      criteriaGroup: {
        id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
        displayName: 'User Generated Content',
        color: '#000000',
      },
      channel: {
        id: 'SNAPCHAT',
        displayName: 'SNAPCHAT',
        color: null,
      },
    },
    column: {
      workspace: 'Learning',
      workspaceId: 1111,
      brand: 'School Bags',
      brandId: '8f2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'can',
      marketId: 'can',
    },
    data: {
      type: 'percent',
      value: [19, 20],
    },
  },
  {
    row: {
      criteria: {
        criteriaIdentifier: 'CUSTOM_COLOR',
        criteriaParameters: '{}',
        criteriaPlatform: 'FACEBOOK',
        isOptional: true,
        criteriaName: 'new criterion',
        isBestPractice: false,
        criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
        customIconUrls: ['https://website.com/logo.png'],
      },
      criteriaGroup: {
        id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
        displayName: 'User Generated Content',
        color: '#000000',
      },
      channel: {
        id: 'FACEBOOK',
        displayName: 'FACEBOOK',
        color: null,
      },
    },
    column: {
      workspace: 'Teaching',
      workspaceId: 2222,
      brand: 'School Bags',
      brandId: '8f2bb426-1452-4681-bf3d-4cfcbd639431',
      market: 'can',
      marketId: 'can',
    },
    data: {
      type: 'percent',
      value: [7, 10],
    },
  },
] as AdherenceReportV3DataItemDto[];

export const expectedAdherenceResponseGroupByCriteriaGroupMarketAndWorkspace = {
  columns: [
    {
      adherencePercent: 76.3736,
      displayName: 'Overall average',
      id: 'Overall average-Overall average',
      impressions: 253100,
      parentId: null,
    },
    {
      adherencePercent: 83.3333,
      displayName: 'Canada',
      id: 'can',
      impressions: 2750,
      parentId: null,
      identifiers: {
        marketId: 'can',
      },
    },
    {
      adherencePercent: 95,
      displayName: 'Learning',
      id: 'can-Learning',
      impressions: 6184,
      parentId: 'can',
      identifiers: {
        marketId: 'can',
        workspaceId: 1111,
      },
    },
    {
      adherencePercent: 60,
      displayName: 'Teaching',
      id: 'can-Teaching',
      impressions: 4960,
      parentId: 'can',
      identifiers: {
        marketId: 'can',
        workspaceId: 2222,
      },
    },
    {
      adherencePercent: 72.9508,
      displayName: 'United States of America',
      id: 'usa',
      impressions: 8310,
      parentId: null,
      identifiers: {
        marketId: 'usa',
      },
    },
    {
      adherencePercent: 71.8447,
      displayName: 'Learning',
      id: 'usa-Learning',
      impressions: 4819,
      parentId: 'usa',
      identifiers: {
        marketId: 'usa',
        workspaceId: 1111,
      },
    },
    {
      adherencePercent: 78.9474,
      displayName: 'Teaching',
      id: 'usa-Teaching',
      impressions: 7712,
      parentId: 'usa',
      identifiers: {
        marketId: 'usa',
        workspaceId: 2222,
      },
    },
  ],
  rows: [
    {
      color: null,
      columns: [
        {
          adherencePercent: 75.8065,
          id: 'Overall average-Overall average',
          impressions: 8204,
        },
        {
          adherencePercent: 80,
          id: 'can',
          impressions: 4590,
        },
        {
          adherencePercent: 95,
          id: 'can-Learning',
          impressions: 275,
        },
        {
          adherencePercent: 50,
          id: 'can-Teaching',
          impressions: 7324,
        },
        {
          adherencePercent: 74.4681,
          id: 'usa',
          impressions: 8147,
        },
        {
          adherencePercent: 73.1707,
          id: 'usa-Learning',
          impressions: 3204,
        },
        {
          adherencePercent: 83.3333,
          id: 'usa-Teaching',
          impressions: 9693,
        },
      ],
      displayName: 'No group',
      id: 'No group',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 70.6522,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 50,
          id: 'can',
        },
        {
          adherencePercent: 50,
          id: 'can-Teaching',
        },
        {
          adherencePercent: 73.1707,
          id: 'usa',
        },
        {
          adherencePercent: 73.1707,
          id: 'usa-Learning',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'LINKEDIN',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: 'No group-LINKEDIN:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'No group',
    },
    {
      columns: [
        {
          adherencePercent: 83.3333,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 83.3333,
          id: 'usa',
        },
        {
          adherencePercent: 83.3333,
          id: 'usa-Teaching',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'REDDIT',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: 'No group-REDDIT:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'No group',
    },
    {
      columns: [
        {
          adherencePercent: 95,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 95,
          id: 'can',
        },
        {
          adherencePercent: 95,
          id: 'can-Learning',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'SNAPCHAT',
      criteriaRuleId: null,
      customIconUrls: [],
      displayName: 'new criterion',
      id: 'No group-SNAPCHAT:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'No group',
    },
    {
      color: '#000000',
      columns: [
        {
          adherencePercent: 77.5862,
          id: 'Overall average-Overall average',
          impressions: 3674,
        },
        {
          adherencePercent: 86.6667,
          id: 'can',
          impressions: 9668,
        },
        {
          adherencePercent: 95,
          id: 'can-Learning',
          impressions: 9635,
        },
        {
          adherencePercent: 70,
          id: 'can-Teaching',
          impressions: 3982,
        },
        {
          adherencePercent: 67.8571,
          id: 'usa',
          impressions: 3674,
        },
        {
          adherencePercent: 66.6667,
          id: 'usa-Learning',
          impressions: 8287,
        },
        {
          adherencePercent: 71.4286,
          id: 'usa-Teaching',
          impressions: 4539,
        },
      ],
      displayName: 'User Generated Content',
      id: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 66.6667,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 66.6667,
          id: 'usa',
        },
        {
          adherencePercent: 66.6667,
          id: 'usa-Learning',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'LINKEDIN',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: '0109u0ey72-ihiuh0-3e0yh208yy2y0-LINKEDIN:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
    },
    {
      columns: [
        {
          adherencePercent: 71.4286,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 71.4286,
          id: 'usa',
        },
        {
          adherencePercent: 71.4286,
          id: 'usa-Teaching',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'AMAZON',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: '0109u0ey72-ihiuh0-3e0yh208yy2y0-AMAZON:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
    },
    {
      columns: [
        {
          adherencePercent: 95,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 95,
          id: 'can',
        },
        {
          adherencePercent: 95,
          id: 'can-Learning',
        },
      ],
      criteriaIdentifier: 'CUSTOM_COLOR',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'SNAPCHAT',
      criteriaRuleId: null,
      customIconUrls: [],
      displayName: 'new criterion',
      id: '0109u0ey72-ihiuh0-3e0yh208yy2y0-SNAPCHAT:CUSTOM_COLOR:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
    },
    {
      columns: [
        {
          adherencePercent: 70,
          id: 'Overall average-Overall average',
        },
        {
          adherencePercent: 70,
          id: 'can',
        },
        {
          adherencePercent: 70,
          id: 'can-Teaching',
        },
      ],
      criteriaIdentifier: 'CUSTOM_COLOR',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'FACEBOOK',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: '0109u0ey72-ihiuh0-3e0yh208yy2y0-FACEBOOK:CUSTOM_COLOR:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: '0109u0ey72-ihiuh0-3e0yh208yy2y0',
    },
  ],
};

export const expectedAdherenceResponseGroupByChannelAndBrand = {
  columns: [
    {
      adherencePercent: 76.3736,
      displayName: 'Overall average',
      id: 'Overall average',
      parentId: null,
      creativeCount: 120333,
    },
    {
      adherencePercent: 83.3333,
      displayName: 'School Bags',
      id: 'School Bags',
      parentId: null,
      identifiers: {
        brandId: '8f2bb426-1452-4681-bf3d-4cfcbd639431',
      },
    },
    {
      adherencePercent: 72.9508,
      displayName: 'Textbooks',
      id: 'Textbooks',
      parentId: null,
      identifiers: {
        brandId: 'af2bb426-1452-4681-bf3d-4cfcbd639431',
      },
    },
  ],
  rows: [
    {
      color: null,
      columns: [
        {
          adherencePercent: 69.9115,
          id: 'Overall average',
          creativeCount: 20589,
        },
        {
          adherencePercent: 50,
          id: 'School Bags',
        },
        {
          adherencePercent: 71.8447,
          id: 'Textbooks',
        },
      ],
      displayName: 'LinkedIn',
      id: 'LINKEDIN',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 69.9115,
          id: 'Overall average',
        },
        {
          adherencePercent: 50,
          id: 'School Bags',
        },
        {
          adherencePercent: 71.8447,
          id: 'Textbooks',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'LINKEDIN',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: 'LINKEDIN-LINKEDIN:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'LINKEDIN',
    },
    {
      color: null,
      columns: [
        {
          adherencePercent: 83.3333,
          id: 'Overall average',
          creativeCount: 78965,
        },
        {
          adherencePercent: 83.3333,
          id: 'Textbooks',
        },
      ],
      displayName: 'Reddit',
      id: 'REDDIT',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 83.3333,
          id: 'Overall average',
        },
        {
          adherencePercent: 83.3333,
          id: 'Textbooks',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'REDDIT',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: 'REDDIT-REDDIT:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'REDDIT',
    },
    {
      color: null,
      columns: [
        {
          adherencePercent: 95,
          id: 'Overall average',
          creativeCount: 98097,
        },
        {
          adherencePercent: 95,
          id: 'School Bags',
        },
      ],
      displayName: 'Snapchat',
      id: 'SNAPCHAT',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 95,
          id: 'Overall average',
        },
        {
          adherencePercent: 95,
          id: 'School Bags',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'SNAPCHAT',
      criteriaRuleId: null,
      customIconUrls: [],
      displayName: 'new criterion',
      id: 'SNAPCHAT-SNAPCHAT:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'SNAPCHAT',
    },
    {
      color: null,
      columns: [
        {
          adherencePercent: 71.4286,
          id: 'Overall average',
          creativeCount: 879,
        },
        {
          adherencePercent: 71.4286,
          id: 'Textbooks',
        },
      ],
      displayName: 'Amazon',
      id: 'AMAZON',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 71.4286,
          id: 'Overall average',
        },
        {
          adherencePercent: 71.4286,
          id: 'Textbooks',
        },
      ],
      criteriaIdentifier: 'CUSTOM_CRITERIA',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'AMAZON',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: 'AMAZON-AMAZON:CUSTOM_CRITERIA:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'AMAZON',
    },
    {
      columns: [
        {
          adherencePercent: 95,
          id: 'Overall average',
        },
        {
          adherencePercent: 95,
          id: 'School Bags',
        },
      ],
      criteriaIdentifier: 'CUSTOM_COLOR',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'SNAPCHAT',
      criteriaRuleId: null,
      customIconUrls: [],
      displayName: 'new criterion',
      id: 'SNAPCHAT-SNAPCHAT:CUSTOM_COLOR:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'SNAPCHAT',
    },
    {
      color: null,
      columns: [
        {
          adherencePercent: 70,
          id: 'Overall average',
          creativeCount: 78799,
        },
        {
          adherencePercent: 70,
          id: 'School Bags',
        },
      ],
      displayName: 'Meta',
      id: 'FACEBOOK',
      parentId: null,
    },
    {
      columns: [
        {
          adherencePercent: 70,
          id: 'Overall average',
        },
        {
          adherencePercent: 70,
          id: 'School Bags',
        },
      ],
      criteriaIdentifier: 'CUSTOM_COLOR',
      criteriaName: 'new criterion',
      criteriaParameters: '{}',
      criteriaPlatform: 'FACEBOOK',
      criteriaRuleId: '093260d0-6c01-4af4-b13c-153042336c91',
      customIconUrls: ['https://website.com/logo.png'],
      displayName: 'new criterion',
      id: 'FACEBOOK-FACEBOOK:CUSTOM_COLOR:new_criterion:{}:OPTIONAL',
      isBestPractice: false,
      isOptional: true,
      parentId: 'FACEBOOK',
    },
  ],
};

export const mockV3NormsData: ScoringNormsResponseDto = {
  scores: [
    {
      channel: 'FACEBOOK',
      criteriaId: Object('CUSTOM_COLOR:{}'),
      pass: 162,
      total: 180,
    },
    {
      channel: 'SNAPCHAT',
      criteriaId: Object('CUSTOM_CRITERIA:{}'),
      pass: 1,
      total: 7,
    },
    {
      channel: 'LINKEDIN',
      criteriaId: Object('CUSTOM_CRITERIA:{}'),
      pass: 131,
      total: 180,
    },
    {
      channel: 'REDDIT',
      criteriaId: Object('CUSTOM_CRITERIA:{}'),
      pass: 11,
      total: 14,
    },
  ],
};
