import {
  CreateReportDto,
  CreateReportDtoFiltersInner,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import dayjs from 'dayjs';
import { ReportFlagDto } from '../dtos/report-flag.dto';

export type RaiseFlagReducerFn = (
  flags: ReportFlagDto[],
  createReportDto: CreateReportDto,
) => ReportFlagDto[];

const getDateFilter = (createReportDto: CreateReportDto) => {
  const { filters } = createReportDto;
  return filters.find((filter) => filter.fieldName === 'mediaCreateDate');
};

const getDayjsDateFilter = (dateFilter: CreateReportDtoFiltersInner) => {
  const dateFilterValue = dateFilter.value as string[];
  return dateFilterValue.map((date) => dayjs(date));
};

const isStartOfMonth = (date: dayjs.Dayjs) => date.date() === 1;
const isEndOfMonth = (date: dayjs.Dayjs) => date.date() === date.daysInMonth();

const isStartOfQuarter = (date: dayjs.Dayjs) =>
  date.month() % 3 === 0 && date.date() === 1;
const isEndOfQuarter = (date: dayjs.Dayjs) =>
  date.month() % 3 === 2 && date.date() === date.daysInMonth();

const isStartOfYear = (date: dayjs.Dayjs) =>
  date.month() === 0 && date.date() === 1;
const isEndOfYear = (date: dayjs.Dayjs) =>
  date.month() === 11 && date.date() === 31;

export const FlagsGroupReducer: Record<string, RaiseFlagReducerFn> = {
  month: (flags: Array<ReportFlagDto>, createReportDto: CreateReportDto) => {
    const dateFilter = getDateFilter(createReportDto);
    if (!dateFilter) {
      return flags;
    }
    const [startDate, endDate] = getDayjsDateFilter(dateFilter);
    const startMonth = startDate.format('YYYY-MM');
    const endMonth = endDate.format('YYYY-MM');

    if (!isStartOfMonth(startDate)) {
      flags.push({
        matchers: {
          month: startMonth,
        },
        status: 'incomplete',
      });

      if (startMonth === endMonth) {
        return flags;
      }
    }

    if (!isEndOfMonth(endDate)) {
      flags.push({
        matchers: {
          month: endMonth,
        },
        status: 'incomplete',
      });
    }

    return flags;
  },
  quarter: (flags: ReportFlagDto[], createReportDto: CreateReportDto) => {
    const dateFilter = getDateFilter(createReportDto);
    if (!dateFilter) {
      return flags;
    }
    const [startDate, endDate] = getDayjsDateFilter(dateFilter);
    const startQuarter = `${startDate.year()}-Q${
      Math.floor(startDate.month() / 3) + 1
    }`;
    const endQuarter = `${endDate.year()}-Q${
      Math.floor(endDate.month() / 3) + 1
    }`;

    if (!isStartOfQuarter(startDate)) {
      flags.push({
        matchers: {
          quarter: startQuarter,
        },
        status: 'incomplete',
      });

      if (startQuarter === endQuarter) {
        return flags;
      }
    }

    if (!isEndOfQuarter(endDate)) {
      flags.push({
        matchers: {
          quarter: endQuarter,
        },
        status: 'incomplete',
      });
    }

    return flags;
  },
  year: (flags: ReportFlagDto[], createReportDto: CreateReportDto) => {
    const dateFilter = getDateFilter(createReportDto);
    if (!dateFilter) {
      return flags;
    }
    const [startDate, endDate] = getDayjsDateFilter(dateFilter);
    const startYear = startDate.year();
    const endYear = endDate.year();

    if (!isStartOfYear(startDate)) {
      flags.push({
        matchers: {
          year: startDate.year(),
        },
        status: 'incomplete',
      });

      if (startYear === endYear) {
        return flags;
      }
    }

    if (!isEndOfYear(endDate)) {
      flags.push({
        matchers: {
          year: endDate.year(),
        },
        status: 'incomplete',
      });
    }

    return flags;
  },
};
