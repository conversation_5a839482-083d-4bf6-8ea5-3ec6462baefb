import {
  AdherenceColumn,
  AdherenceColumnTitle,
  AdherenceRow,
} from '../dtos/adherence-row.dto';
import { ScoringNormsRequest } from '../../score/dto/scoring-norms-request.dto';
import { ScoringNormsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/scoringNormsResponseDto';

import { ImpressionAdherenceReportDataItem } from '../internal-scoring-reports.interfaces';
import { AdherenceReportV3ResponseDto } from '../dtos/adherence-report-v3-response.dto';
import {
  ADHERENCE_PERCENTAGE_DECIMAL_PLACES,
  OVERALL_COLUMN_HEADER_DISPLAY_NAME,
} from '../constants/constants';

export function findAndReplaceWithMergedColumn(
  row: AdherenceRow,
  newData: AdherenceColumn,
): AdherenceRow {
  const columnIndex = row.columns.findIndex(
    (column) => column.id === newData.id,
  );
  if (columnIndex === -1) {
    row.columns.push(newData);
    return row;
  }
  const existingColumn = row.columns[columnIndex];
  const mergedColumn = mergeColumn(existingColumn, newData);
  row.columns.splice(columnIndex, 1, mergedColumn);
  return row;
}

export function mergeColumn(
  col1: AdherenceColumn,
  col2: AdherenceColumn,
): AdherenceColumn {
  if (col1.id !== col2.id) {
    throw new Error('Cannot merge columns with different IDs.');
  }
  return {
    id: col1.id,
    displayName: col1.displayName || col2.displayName,
    criteriaIdentifier: col1.criteriaIdentifier,
    criteriaPlatform: col1.criteriaPlatform,
    criteriaParameters: col1.criteriaParameters,
    criteriaIsOptional: col1.criteriaIsOptional,
    criteriaIsBestPractice: col1.criteriaIsBestPractice,
    inFlight: null,
    preFlight: null,
    inFlightPassed: col1.inFlightPassed + col2.inFlightPassed,
    inFlightTotal: col1.inFlightTotal + col2.inFlightTotal,
    preFlightPassed: col1.preFlightPassed + col2.preFlightPassed,
    preFlightTotal: col1.preFlightTotal + col2.preFlightTotal,
  };
}

export function groupRowsByParentId(
  rows: AdherenceRow[],
): Map<string, AdherenceRow[]> {
  const groups = new Map<string, AdherenceRow[]>();
  rows.forEach((row) => {
    if (row.parentId) {
      if (!groups.has(row.parentId)) {
        groups.set(row.parentId, []);
      }
      groups.get(row.parentId)?.push(row);
    }
  });
  return groups;
}

export function mergeColumns(columns: AdherenceColumn[]): AdherenceColumn[] {
  const columnMap = new Map<string, AdherenceColumn>();

  columns.forEach((col) => {
    const existingCol = columnMap.get(col.id);
    if (!existingCol) {
      columnMap.set(col.id, {
        ...col,
        inFlight: null,
        preFlight: null,
        inFlightPassed: col.inFlightPassed,
        inFlightTotal: col.inFlightTotal,
        preFlightPassed: col.preFlightPassed,
        preFlightTotal: col.preFlightTotal,
      });
    } else {
      existingCol.inFlightPassed += col.inFlightPassed || 0;
      existingCol.inFlightTotal += col.inFlightTotal || 0;
      existingCol.preFlightPassed += col.preFlightPassed || 0;
      existingCol.preFlightTotal += col.preFlightTotal || 0;
    }
  });

  return Array.from(columnMap.values());
}

export function createParentRows(rows: AdherenceRow[]): AdherenceRow[] {
  const groups = groupRowsByParentId(rows);
  const newRows: AdherenceRow[] = [];

  groups.forEach((groupRows, parentId) => {
    const allColumns = groupRows.flatMap((row) => row.columns);
    const mergedColumns = mergeColumns(allColumns);
    newRows.push({
      id: parentId,
      parentId: undefined,
      displayName: parentId,
      columns: mergedColumns,
    });
  });

  return newRows;
}

export function calculateTotalColumn(rows: AdherenceRow[]) {
  rows.forEach((row) => {
    const totalColumn: AdherenceColumn = {
      id: 'total',
      displayName: 'Total',
      criteriaIdentifier: '',
      criteriaPlatform: '',
      criteriaParameters: '',
      criteriaIsOptional: false,
      criteriaIsBestPractice: false,
      inFlight: null,
      preFlight: null,
      inFlightPassed: 0,
      inFlightTotal: 0,
      preFlightPassed: 0,
      preFlightTotal: 0,
    };
    row.columns.forEach((column) => {
      totalColumn.inFlightPassed += column.inFlightPassed;
      totalColumn.inFlightTotal += column.inFlightTotal;
      totalColumn.preFlightPassed += column.preFlightPassed;
      totalColumn.preFlightTotal += column.preFlightTotal;
    });
    row.columns.push(totalColumn);
  });
}

const PASSED_INDEX = 0;
const SCORED_INDEX = 1;

export function buildAdherenceColumn(
  item: ImpressionAdherenceReportDataItem,
): AdherenceColumn {
  const isPreFlight = item.column.batchType == 'PRE_FLIGHT'; // 'PRE_FLIGHT'
  const criteria = item.column.criteria;
  const data: AdherenceColumn = {
    id: buildCriteriaIdString(criteria),
    displayName: criteria.criteriaName ?? criteria.criteriaIdentifier,
    criteriaPlatform: criteria.criteriaPlatform ?? '',
    criteriaIsOptional: Boolean(criteria.isOptional),
    criteriaIsBestPractice: Boolean(criteria.isBestPractice),
    criteriaIdentifier: criteria.criteriaIdentifier,
    criteriaParameters: criteria.criteriaParameters,
    inFlight: null,
    preFlight: null,
    inFlightPassed: 0,
    inFlightTotal: 0,
    preFlightPassed: 0,
    preFlightTotal: 0,
  };
  if (isPreFlight) {
    data.preFlightPassed = +item.data.value[PASSED_INDEX];
    data.preFlightTotal = +item.data.value[SCORED_INDEX];
  } else {
    data.inFlightPassed = +item.data.value[PASSED_INDEX];
    data.inFlightTotal = +item.data.value[SCORED_INDEX];
  }
  return data;
}

export function buildCriteriaIdString(criteria: {
  isOptional?: boolean | undefined;
  isBestPractice?: boolean | undefined;
  criteriaIdentifier: string;
  criteriaParameters: string;
  criteriaPlatform?: string | undefined;
  criteriaName?: string | undefined;
}) {
  return `${criteria.criteriaPlatform ?? ''}:${criteria.criteriaIdentifier}:${
    criteria.criteriaName?.replace(/\s+/g, '_') ?? 'NO_NAME'
  }:${criteria.criteriaParameters}:${
    criteria.isOptional ? 'OPTIONAL' : 'MANDATORY'
  }${criteria.isBestPractice ? ':BEST_PRACTICE' : ''}`;
}

export function createAdherenceNormsList(
  rows: AdherenceRow[],
): AdherenceColumnTitle[] {
  const normsMap = new Map<string, AdherenceColumnTitle>();

  rows.forEach((row) => {
    row.columns.forEach((column) => {
      let norm = normsMap.get(column.id);
      if (!norm) {
        // Initialize a new norm with the current column's id and zeroed numerical values
        norm = {
          ...column,
          totalNorms: null,
          totalNormsPassed: 0,
          totalNormsTotal: 0,
        };
      } else {
        if (column.inFlight) {
          norm.inFlight = (norm.inFlight ?? 0) + column.inFlight;
        }
        if (column.preFlight) {
          norm.preFlight = (norm.preFlight ?? 0) + column.preFlight;
        }
        if (column.inFlightPassed) {
          norm.inFlightPassed =
            (norm.inFlightPassed ?? 0) + column.inFlightPassed;
        }
        norm.inFlightTotal += column.inFlightTotal;
        norm.preFlightPassed += column.preFlightPassed;
        norm.preFlightTotal += column.preFlightTotal;
      }
      normsMap.set(column.id, norm);
    });
  });

  // Convert the map values to an array
  return Array.from(normsMap.values());
}

export function isScoringNormsRequest(obj: any): obj is ScoringNormsRequest {
  return (
    Array.isArray(obj.scopes) &&
    (obj.channels === undefined || Array.isArray(obj.channels)) &&
    (obj.objectiveIds === undefined || Array.isArray(obj.objectiveIds)) &&
    (obj.industryIds === undefined || Array.isArray(obj.industryIds)) &&
    (obj.subIndustryIds === undefined || Array.isArray(obj.subIndustryIds)) &&
    (obj.marketIds === undefined || Array.isArray(obj.marketIds))
  );
}

export function calculateAdherence(
  adherenceRows: AdherenceRow[],
  titleRow: AdherenceColumnTitle[],
) {
  adherenceRows.forEach((row) => {
    row.columns.forEach((column) => {
      // Calculate inFlight percentage
      if (column.inFlightTotal > 0) {
        column.inFlight = Math.round(
          (column.inFlightPassed / column.inFlightTotal) * 100,
        );
      } else {
        column.inFlight = null; // or 0, based on what you consider as default for undefined totals
      }

      // Calculate preFlight percentage
      if (column.preFlightTotal > 0) {
        column.preFlight = Math.round(
          (column.preFlightPassed / column.preFlightTotal) * 100,
        );
      } else {
        column.preFlight = null; // or 0, similar to inFlight
      }
    });
  });
  titleRow.forEach((column) => {
    // Calculate inFlight percentage
    if (column.inFlightTotal > 0) {
      column.inFlight = Math.round(
        (column.inFlightPassed / column.inFlightTotal) * 100,
      );
    } else {
      column.inFlight = null; // or 0, based on what you consider as default for undefined totals
    }

    // Calculate preFlight percentage
    if (column.preFlightTotal > 0) {
      column.preFlight = Math.round(
        (column.preFlightPassed / column.preFlightTotal) * 100,
      );
    } else {
      column.preFlight = null; // or 0, similar to inFlight
    }
  });
}

const safeJsonParse = (str: string): unknown => {
  try {
    return JSON.parse(str);
  } catch {
    return undefined;
  }
};

const normalizeCriteriaString = (str: string) => {
  const colonIndex = str.indexOf(':');
  if (colonIndex === -1) {
    return str.toLowerCase().replace(/\s/g, '');
  }

  const identifier = str.slice(0, colonIndex);
  const parameters = str.slice(colonIndex + 1);
  const parsedParameters = safeJsonParse(parameters);

  if (parsedParameters && typeof parsedParameters === 'object') {
    const normalizedParams = JSON.stringify(
      parsedParameters,
      Object.keys(parsedParameters).sort(),
    ).replace(/\s/g, '');

    return `${identifier.toLowerCase()}:${normalizedParams}`;
  }

  return str.toLowerCase().replace(/\s/g, '');
};

const compareCriteriaStrings = (str1: string, str2: string) => {
  return normalizeCriteriaString(str1) === normalizeCriteriaString(str2);
};

export function addNormsData(
  titleRow: AdherenceColumnTitle[],
  normsData: ScoringNormsResponseDto | undefined,
) {
  if (!normsData) {
    return;
  }

  titleRow.forEach((column) => {
    // skip if criteria is not a best practice
    if (!column.criteriaIsBestPractice) return;

    const matchingNorm = normsData.scores.find((score) => {
      // score.criteriaId is scoring_norms.identifier with the format: ABI_DYNAMIC_START:{"maxMotionPresenceTime": 1}
      // column.criteriaIdentifier is from the adherence report with the format: {"maxMotionPresenceTime":1}
      const columnCriteria =
        column.criteriaIdentifier + ':' + column.criteriaParameters;

      const normCriteria = score.criteriaId.toString();
      return (
        column.criteriaPlatform === score.channel &&
        compareCriteriaStrings(columnCriteria, normCriteria)
      );
    });

    // Update the totalNorms, totalNormsPassed, and totalNormsTotal if a matching norm is found
    if (matchingNorm) {
      column.totalNormsPassed = matchingNorm.pass;
      column.totalNormsTotal = matchingNorm.total;

      // Calculate the total norms percentage, if total is greater than zero
      if (matchingNorm.total > 0) {
        column.totalNorms = Math.round(
          (matchingNorm.pass / matchingNorm.total) * 100,
        );
      } else {
        column.totalNorms = null; // or 0, based on your handling for undefined totals
      }
    }
  });
}

/**
 * Adds norms data to appropriate row columns.
 * v3 requires its own utility. Can't extend or use v2 addNormsData() function
 * because rows and columns have been flipped in v3.
 * @param adherenceData
 * @param normsData
 */
export function addNormsDataV3(
  adherenceData: AdherenceReportV3ResponseDto,
  normsData: ScoringNormsResponseDto | undefined,
) {
  if (!normsData) {
    return;
  }

  const { rows } = adherenceData;

  rows?.forEach((row) => {
    // no norms for parent rows
    if (row.criteriaIdentifier) {
      row?.columns.forEach((column) => {
        // scoring soa provides criteria level norms only
        // it does not provide norms broken down by country or market
        // so adding norms only to overall-average column
        if (
          column.id
            .trim()
            .toLowerCase()
            .includes(OVERALL_COLUMN_HEADER_DISPLAY_NAME.toLowerCase())
        ) {
          const matchingNorm = normsData.scores?.find((score) => {
            const rowCriteriaIdentifier = `${row.criteriaIdentifier}:${row.criteriaParameters}`;
            const normScoreCriteriaIdentifier = score.criteriaId.toString();

            return (
              row.criteriaPlatform === score.channel &&
              compareCriteriaStrings(
                rowCriteriaIdentifier,
                normScoreCriteriaIdentifier,
              )
            );
          });

          if (matchingNorm) {
            const normsAdherencePassRate = Number(
              matchingNorm.pass / matchingNorm.total,
            );
            if (normsAdherencePassRate > 0 && column.adherencePercent) {
              column.normsPercentLift = Number(
                (
                  (column.adherencePercent / 100 / normsAdherencePassRate - 1) *
                  100
                ).toFixed(ADHERENCE_PERCENTAGE_DECIMAL_PLACES),
              );
            }
          }
        }
      });
    }
  });
}
