import { mergeColumns } from './adherence-utils';
import { AdherenceColumn } from '../dtos/adherence-row.dto';

describe('Test Adherence Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('mergeColumns should total values correctly', () => {
    const id = 'brand name early';
    const column1 = {
      id,
      inFlightPassed: 1,
      inFlightTotal: 1,
      preFlightPassed: 0,
      preFlightTotal: 1,
    };
    const column2 = {
      id,
      inFlightPassed: 20,
      inFlightTotal: 50,
      preFlightPassed: 10,
      preFlightTotal: 20,
    };
    const columns = [column1, column2] as AdherenceColumn[];
    const result = mergeColumns(columns);
    expect(result.length).toBe(1);
    const mergedColumn = result[0];
    expect(mergedColumn.id).toBe(id);
    expect(mergedColumn.inFlightPassed).toBe(
      column1.inFlightPassed + column2.inFlightPassed,
    );
    expect(mergedColumn.inFlightTotal).toBe(
      column1.preFlightTotal + column2.inFlightTotal,
    );
    expect(mergedColumn.preFlightPassed).toBe(
      column1.preFlightPassed + column2.preFlightPassed,
    );
    expect(mergedColumn.preFlightTotal).toBe(
      column1.preFlightTotal + column2.preFlightTotal,
    );
  });
});
