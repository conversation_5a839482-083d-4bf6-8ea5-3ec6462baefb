import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { ScoringReportFiltersService } from './scoring-report-filters.service';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';
import { CreateReportDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ReadWorkspaceDto } from '@vidmob/vidmob-organization-service-sdk';

describe('ScoringReportFilterUtils', () => {
  let service: ScoringReportFiltersService;
  let workspaceService: WorkspaceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScoringReportFiltersService,
        {
          provide: WorkspaceService,
          useValue: {
            findOneAsPromise: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            error: jest.fn(),
            warn: jest.fn(),
            log: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ScoringReportFiltersService>(
      ScoringReportFiltersService,
    );
    workspaceService = module.get<WorkspaceService>(WorkspaceService);
  });

  it('should log an error and return when organizationId is not found', async () => {
    jest.spyOn(workspaceService, 'findOneAsPromise').mockResolvedValue({
      status: 'OK',
      result: {} as ReadWorkspaceDto,
    });
    const loggerSpy = jest.spyOn(service['logger'], 'error');
    const createReportDto: CreateReportDto = {
      filters: [],
      groupBy: { columns: [], rows: [] },
    };

    await service.addOrganizationIdFilter(createReportDto, 123);

    expect(loggerSpy).toHaveBeenCalledWith(
      'Organization ID not found for workspace ID 123. Skipping add organization id filter.',
    );
    expect(createReportDto.filters.length).toBe(0);
  });

  it('should add a new organizationId filter if not present', async () => {
    jest.spyOn(workspaceService, 'findOneAsPromise').mockResolvedValue({
      status: 'OK',
      result: { organizationId: 'org123' } as ReadWorkspaceDto,
    });
    const createReportDto: CreateReportDto = {
      filters: [],
      groupBy: { columns: [], rows: [] },
    };

    await service.addOrganizationIdFilter(createReportDto, 123);

    expect(createReportDto.filters.length).toBe(1);
    expect(createReportDto.filters[0].fieldName).toBe('organizationId');
    expect(createReportDto.filters[0].value).toBe('org123');
  });

  it('should not add a duplicate organizationId filter if already present', async () => {
    jest.spyOn(workspaceService, 'findOneAsPromise').mockResolvedValue({
      status: 'OK',
      result: { organizationId: 'org123' } as ReadWorkspaceDto,
    });
    const createReportDto: CreateReportDto = {
      filters: [
        { fieldName: 'organizationId', operator: 'equals', value: 'org123' },
      ],
      groupBy: { columns: [], rows: [] },
    };

    await service.addOrganizationIdFilter(createReportDto, 123);

    expect(createReportDto.filters.length).toBe(1);
  });
});
