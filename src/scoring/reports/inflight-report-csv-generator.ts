import { ReadCriteriaDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/readCriteriaDto';
import { MediaObjectAndScoresDto } from './dtos/media-object-and-scores.dto';
import { GetCriteriaQueryParamsDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getCriteriaQueryParamsDto';
import {
  InflightAggregateRequestDto,
  GetCriteriaInCriteriaSets200Response,
  GetPreflightChannelAggregateScores200Response,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { Injectable } from '@nestjs/common';
import { ChannelAggregateResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/channelAggregateResponseDto';
import { INTERNAL_TO_CSV_OUTPUT_SCORE_MAP } from './constants/constants';

@Injectable()
export class InflightReportCsvGenerator {
  private readonly DEFAULT_CSV_OUTPUT_SCORE = 'No Data';
  private readonly BASE_CSV_HEADERS = [
    'Platform',
    'Platform Compliance (%)',
    'Media Name',
    'Media ID',
    'Media Compliance (%)',
  ];

  public getCriteriaListQueryParamsForInflightReportCsv(
    inflightAggregateRequestDto: InflightAggregateRequestDto,
  ): GetCriteriaQueryParamsDto {
    const {
      criteriaIsOptional,
      criteriaGroupIds,
      criteriaIsOrganizationCriteria,
    } = inflightAggregateRequestDto;
    const channel =
      inflightAggregateRequestDto.channel.toUpperCase() as GetCriteriaQueryParamsDto.PlatformsEnum;
    const globalStatuses =
      criteriaIsOrganizationCriteria == null
        ? undefined
        : criteriaIsOrganizationCriteria
        ? [GetCriteriaQueryParamsDto.GlobalStatusesEnum.Global]
        : [GetCriteriaQueryParamsDto.GlobalStatusesEnum.Default];
    const isOptional =
      criteriaIsOptional == null
        ? undefined
        : criteriaIsOptional
        ? [GetCriteriaQueryParamsDto.IsOptionalEnum.Optional]
        : [GetCriteriaQueryParamsDto.IsOptionalEnum.Mandatory];

    return {
      platforms: [
        channel,
        GetCriteriaQueryParamsDto.PlatformsEnum.AllPlatforms,
      ],
      isOptional,
      globalStatuses,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      criteriaGroupIds,
    };
  }

  public async getInflightReportCSV(
    mediaScoresResponse: PaginatedResultArray<MediaObjectAndScoresDto>,
    channelAggregateScores: GetPreflightChannelAggregateScores200Response,
    criteria: GetCriteriaInCriteriaSets200Response,
    inflightAggregateRequestDto: InflightAggregateRequestDto,
  ) {
    const channel = inflightAggregateRequestDto.channel.toUpperCase();
    const criteriaIdsFilter = inflightAggregateRequestDto.criteriaIds;

    const criteriaKeyedById: Record<number, ReadCriteriaDto> =
      criteria.result.reduce(
        (acc: Record<number, ReadCriteriaDto>, criterion: ReadCriteriaDto) => {
          acc[criterion.id] = criterion;
          return acc;
        },
        {} as Record<number, ReadCriteriaDto>,
      );
    const criteriaIds =
      criteriaIdsFilter ??
      (Object.keys(criteriaKeyedById) as unknown as number[]);
    const criteriaNamesForHeader = criteriaIds.map((id) =>
      `${criteriaKeyedById[id].platform} / ${criteriaKeyedById[id].name} / ${criteriaKeyedById[id].rule}`.replace(
        /,/gm,
        '_',
      ),
    );

    const headers = [...this.BASE_CSV_HEADERS, ...criteriaNamesForHeader];
    const rowsWithMediaScores = this.getRowsWithMediaScore(
      mediaScoresResponse,
      channel,
      criteriaIds,
    );
    const rowsWithChannelScores =
      channelAggregateScores.result.channelAggregates.map(
        (channelScore: ChannelAggregateResponseDto) =>
          `${channelScore.channel},${channelScore.passedPercent},`,
      );

    const allRows = [
      headers.join(','),
      ...rowsWithMediaScores,
      ...rowsWithChannelScores,
    ];

    return allRows.join('\n');
  }

  private getRowsWithMediaScore(
    mediaScoresResponse: PaginatedResultArray<MediaObjectAndScoresDto>,
    channel: string,
    criteriaIds: number[],
  ) {
    return mediaScoresResponse.items.reduce(
      (acc: string[], mediaScore: MediaObjectAndScoresDto) => {
        const mediaScoresByCriteriaId = mediaScore.scores.reduce(
          (acc: Record<number, string>, criteriaScore) => {
            acc[criteriaScore.criteriaId as unknown as number] =
              INTERNAL_TO_CSV_OUTPUT_SCORE_MAP[criteriaScore.status] ??
              this.DEFAULT_CSV_OUTPUT_SCORE;
            return acc;
          },
          {} as Record<number, string>,
        );

        const rowDetails = [
          channel,
          '',
          mediaScore.mediaObject.displayName ?? mediaScore.mediaObject.name,
          mediaScore.id,
          mediaScore.passedPercent,
          ...criteriaIds.map((id) => mediaScoresByCriteriaId[id] ?? 'No Data'),
        ];

        acc.push(rowDetails.join(','));
        return acc;
      },
      [],
    );
  }
}
