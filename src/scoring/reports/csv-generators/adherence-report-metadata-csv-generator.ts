import { countryDataMapByIsoCode } from '../constants/country-data';
import {
  ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT,
  ADHERENCE_METADATA_KEY_TO_CSV_HEADER_MAP,
  ALL_PLATFORMS,
  BLANK_CSV_VALUE,
  CRITERIA_AND_SCORES_KEY,
  STANDARD_CRITERIA,
  ADHERENCE_METADATA_RESPONSE_KEYS,
  JWT_EXPIRATION_MILLI_SECONDS,
  JWT_REQUESTS_BATCH_SIZE,
} from '../constants/constants';
import { AdherenceMetadataDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OAuth2Service as AuthorizationServiceSdk } from '@vidmob/vidmob-authorization-service-sdk';

@Injectable()
export class AdherenceReportMetadataCsvGenerator {
  constructor(
    private readonly configService: ConfigService,
    private readonly authorizationService: AuthorizationServiceSdk,
  ) {}

  private escapeCsvValue(
    value: string | number | string[] | undefined,
  ): string | number {
    if (value == undefined) {
      return BLANK_CSV_VALUE;
    }

    if (typeof value === 'string') {
      return `"${value
        .replace(ALL_PLATFORMS, STANDARD_CRITERIA)
        .replace(/"/g, '""')}"`;
    }

    if (Array.isArray(value)) {
      return value.toString();
    }

    return value;
  }

  private getComplianceScore(criteriaAndScores: object): number {
    const criteriaScores = Object.values(criteriaAndScores);
    const countPassed = criteriaScores.reduce((acc, score) => acc + score, 0);
    return Number(((countPassed * 100) / criteriaScores.length).toFixed(5));
  }

  /**
   * Builds the noauth api for downloading media from S3
   * @param environment
   * @param mediaId
   * @param token - JWT token for the specific media download
   * @private
   */
  private getBffMediaDownloadLink(
    environment: string,
    mediaId: number,
    token: string,
  ): string {
    const environmentSuffix = environment === 'prod' ? '' : `-${environment}`;
    return `https://bff${environmentSuffix}.vidmob.com/v1/reports/noauth/adherence/media/${mediaId}/download?token=${token}`;
  }

  private async getJWTsKeyByMediaId(
    metadataRows: AdherenceMetadataDto[],
  ): Promise<Record<number, string>> {
    const uniqueMediaIds = [...new Set(metadataRows.map((row) => row.mediaId))];
    const jwtKeyedByMediaId: Record<number, string> = {};

    for (let i = 0; i < uniqueMediaIds.length; i += JWT_REQUESTS_BATCH_SIZE) {
      const batchedMediaIds = uniqueMediaIds.slice(
        i,
        i + JWT_REQUESTS_BATCH_SIZE,
      );
      const jwtResponses = await Promise.all(
        batchedMediaIds.map((mediaId) =>
          this.authorizationService.createJWTWithCustomDurationAndValidationMetadataAsPromise(
            {
              validationMetadata: { mediaId },
              durationMilliSeconds: JWT_EXPIRATION_MILLI_SECONDS,
            },
          ),
        ),
      );
      jwtResponses.reduce((acc, mediaJWTResponse, index) => {
        acc[batchedMediaIds[index]] = mediaJWTResponse.result.accessToken;

        return acc;
      }, jwtKeyedByMediaId);
    }

    return jwtKeyedByMediaId;
  }

  private async formatRowsWithCriteriaAndScores(
    metadataRows: AdherenceMetadataDto[],
  ): Promise<{
    rows: Record<string, string | string[] | number>[];
    uniqueCriteriaHeaders: string[];
  }> {
    const uniqueCriteriaHeadersSet = new Set<string>();

    const jwtExpirationTime = new Date(
      Date.now() + JWT_EXPIRATION_MILLI_SECONDS,
    );
    const environment = this.configService.get<string>('NODE_ENV') ?? '';
    const allJWTsKeyedByMediaId = await this.getJWTsKeyByMediaId(metadataRows);

    const rows = metadataRows.map((row) => {
      const { criteriaAndScores, market, ...remainingAttributes } = row;
      Object.keys(criteriaAndScores).forEach((criteria) => {
        uniqueCriteriaHeadersSet.add(criteria);
      });
      const overallScore = this.getComplianceScore(criteriaAndScores);

      return {
        ...remainingAttributes,
        ...criteriaAndScores,
        [ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.linkToDownload]:
          this.getBffMediaDownloadLink(
            environment,
            row.mediaId,
            allJWTsKeyedByMediaId[row.mediaId],
          ),
        [ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.linkExpirationDate]:
          jwtExpirationTime.toDateString(),
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        market: countryDataMapByIsoCode[market] ?? market,
        overallScore,
      };
    });

    const uniqueCriteriaHeaders = Array.from(uniqueCriteriaHeadersSet).sort();
    return {
      rows,
      uniqueCriteriaHeaders,
    };
  }

  private getCSVHeaderKeys(
    metadataRows: AdherenceMetadataDto[],
    uniqueCriteriaHeaders: string[],
  ): string[] {
    const headersByBatchTypeWithoutCriteria = Object.keys(
      metadataRows[0],
    ).filter((item) => item != CRITERIA_AND_SCORES_KEY);

    // add "Link to Download" and "Link Expiration" after "mediaName"
    headersByBatchTypeWithoutCriteria.splice(
      headersByBatchTypeWithoutCriteria.indexOf(
        ADHERENCE_METADATA_RESPONSE_KEYS.mediaName,
      ) + 1,
      0,
      ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.linkToDownload,
      ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.linkExpirationDate,
    );

    return [
      ...headersByBatchTypeWithoutCriteria,
      ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.overallScore,
      ...uniqueCriteriaHeaders,
    ];
  }

  public async generateCsv(
    metadataRows: AdherenceMetadataDto[],
  ): Promise<string> {
    const { rows, uniqueCriteriaHeaders } =
      await this.formatRowsWithCriteriaAndScores(metadataRows);
    const headerKeys = this.getCSVHeaderKeys(
      metadataRows,
      uniqueCriteriaHeaders,
    );

    const headersRow = headerKeys
      .map((key) =>
        this.escapeCsvValue(
          ADHERENCE_METADATA_KEY_TO_CSV_HEADER_MAP[key] ?? key,
        ),
      )
      .join(',');

    const dataRows = rows.map((row) =>
      headerKeys.map((key) => this.escapeCsvValue(row[key])).join(','),
    );

    return [headersRow, ...dataRows].join('\n');
  }
}
