import { Test, TestingModule } from '@nestjs/testing';
import {
  PersonService,
  ReadPartnerRolesDto,
} from '@vidmob/vidmob-authorization-service-sdk';
import {
  BrandService,
  MediaService,
  OrganizationService,
  OrganizationUserService,
  ScopeFilterService,
  WorkspaceService,
} from '@vidmob/vidmob-organization-service-sdk';
import {
  CreateReportDto,
  DEIReportService,
  RollupReportsService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { CriteriaSetService } from '../criteria/criteria-set.service';
import { ScoringReportsService } from './scoring-reports.service';
import { ReadWorkspaceSearchDto } from './internal-scoring-reports.interfaces';
import { getDisplayNameFor } from './constants/criteria-names';
import { of } from 'rxjs';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { DataSource, Repository } from 'typeorm';
import { Market } from '../../entities/market.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ScoreService } from '../score/score.service';
import {
  EntityType,
  IN_FLIGHT,
  NOT_SPECIFIED_LABEL,
  NOT_SPECIFIED_VALUE,
} from './constants/constants';
import {
  expectedInFlightMetadataCSV,
  expectedPreFlightMetadataCSV,
  MOCK_BRANDS,
  MOCK_IN_FLIGHT_METADATA_RESPONSE,
  MOCK_IN_FLIGHT_REPORT_DTO,
  MOCK_PRE_FLIGHT_METADATA_RESPONSE,
  MOCK_PRE_FLIGHT_REPORT_DTO,
  MOCK_REPORT_RESULT,
  MOCK_RESPONSE_DTO,
  MockDEIResponse,
  expectedAdherenceResponseGroupByChannelAndBrand,
  expectedAdherenceResponseGroupByCriteriaGroupMarketAndWorkspace,
  mockV3AdherenceReportData,
  mockCriteriaGroupImpressionCountData,
  mockChannelCreativeCountData,
} from './mock/mock-data';
import { OAuth2Service as AuthorizationServiceSdk } from '@vidmob/vidmob-authorization-service-sdk';
import { ConfigService } from '@nestjs/config';
import { AdherenceReportMetadataCsvGenerator } from './csv-generators/adherence-report-metadata-csv-generator';
import { AnalyticsUserService } from '../../analytics/analytics-user-service/analytics-user-service';
import { CriteriaService } from '../criteria/criteria.service';
import { InflightReportCsvGenerator } from './inflight-report-csv-generator';

describe('ReportsService', () => {
  let service: ScoringReportsService;
  let reportService: RollupReportsService;
  let workspaceService: WorkspaceService;
  let organizationService: OrganizationService;
  let organizationUserService: OrganizationUserService;
  let personService: PersonService;
  let criteriaSetService: CriteriaSetService;
  let deiReportService: DEIReportService;
  let scoringAuthService: ScoringAuthService;
  let brandService: BrandService;
  let marketRepository: Repository<Market>;

  const mockCreateSnowflakeAdherenceReportV2AsPromise = jest.fn(() => ({
    result: { data: [] },
  }));

  const MARKET_REPOSITORY_TOKEN = getRepositoryToken(Market);

  beforeAll(() => {
    jest.spyOn(global.Date, 'now').mockImplementation(() => *************);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScoringReportsService,
        AdherenceReportMetadataCsvGenerator,
        InflightReportCsvGenerator,
        {
          provide: AnalyticsUserService,
          useValue: {
            validateUserAccessToAdAccountsAndWorkspaces: jest.fn(),
          },
        },
        {
          provide: ScopeFilterService,
          useValue: {
            filterAdAccountsByScopeAsPromise: jest.fn(),
          },
        },
        {
          provide: ScoreService,
          useValue: {
            createAdoptionReport: jest.fn(),
            createAdherenceReport: jest.fn(),
          },
        },
        {
          provide: RollupReportsService,
          useValue: {
            createAdoptionReport: jest.fn(() => of(MOCK_REPORT_RESULT)),
            createAdherenceReport: jest.fn(() => of(MOCK_REPORT_RESULT)),
            createSnowflakeAdherenceReportV2AsPromise:
              mockCreateSnowflakeAdherenceReportV2AsPromise,
            getAdherenceReportRowMetadataAsPromise: jest.fn(),
          },
        },
        {
          provide: ScoringAuthService,
          useValue: {
            checkPermissions: jest.fn(),
            getIsUserOrgAdmin: jest.fn(),
            getIsUserPartnerManager: jest.fn(),
            getRelatedAccessiblePartners: jest.fn(),
          },
        },
        {
          provide: WorkspaceService,
          useValue: {
            findOneAsPromise: jest
              .fn()
              .mockResolvedValue({ result: { organizationId: '1234' } }),
          },
        },
        {
          provide: OrganizationService,
          useValue: {
            findWorkspacesByOrganizationAsPromise: jest.fn(),
          },
        },
        {
          provide: OrganizationUserService,
          useValue: {
            findUserAssociatedOrganizationsAndWorkspaceCountAsPromise:
              jest.fn(),
          },
        },
        {
          provide: PersonService,
          useValue: {
            getAllRolesForUser: jest.fn(),
          },
        },
        {
          provide: CriteriaSetService,
          useValue: {
            getCriteriaSets: jest.fn(() => of([])),
          },
        },
        {
          provide: CriteriaService,
          useValue: {},
        },
        {
          provide: DEIReportService,
          useValue: {
            getDEIDemographicReportAsPromise: jest.fn(),
          },
        },
        {
          provide: BrandService,
          useValue: {
            getBrandsAsPromise: jest.fn(() =>
              Promise.resolve({
                result: MOCK_BRANDS,
              }),
            ),
          },
        },
        {
          provide: MARKET_REPOSITORY_TOKEN,
          useValue: {
            find: jest.fn(() => []),
          },
        },
        {
          provide: DataSource,
          useValue: {
            query: jest.fn(),
          },
        },
        {
          provide: AuthorizationServiceSdk,
          useValue: {
            createJWTWithCustomDurationAndValidationMetadataAsPromise: jest
              .fn()
              .mockResolvedValue({ result: { accessToken: 'json.web.token' } }),
          },
        },
        {
          provide: MediaService,
          useValue: {
            getMediaByIdAsPromise: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: { get: jest.fn().mockReturnValue('prod') },
        },
      ],
    }).compile();

    service = module.get<ScoringReportsService>(ScoringReportsService);
    personService = module.get<PersonService>(PersonService);
    workspaceService = module.get<WorkspaceService>(WorkspaceService);
    organizationService = module.get<OrganizationService>(OrganizationService);
    organizationUserService = module.get<OrganizationUserService>(
      OrganizationUserService,
    );
    reportService = module.get<RollupReportsService>(RollupReportsService);
    criteriaSetService = module.get<CriteriaSetService>(CriteriaSetService);
    deiReportService = module.get<DEIReportService>(DEIReportService);
    scoringAuthService = module.get<ScoringAuthService>(ScoringAuthService);
    brandService = module.get<BrandService>(BrandService);
    marketRepository = module.get<Repository<Market>>(MARKET_REPOSITORY_TOKEN);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  const axiosResponse = (result: object) => of({ data: { result } });

  it('test partner access when not org admin', async () => {
    scoringAuthService.isUserPartnerManager = jest.fn().mockReturnValue(false);
    const personId = 123;
    const partnerId = 456;

    const roles: ReadPartnerRolesDto = {
      personId,
      roles: [
        {
          partnerId,
          role: 'STANDARD',
        },
        {
          partnerId: 999,
          role: 'ADMIN',
        },
      ],
    };
    personService.getAllRolesForUser = jest
      .fn()
      .mockReturnValue(axiosResponse(roles));
    const organizationId = '123';
    const relatedPartners: ReadWorkspaceSearchDto[] = [
      {
        id: partnerId,
        name: 'Test Partner',
        organizationId,
      },
      {
        id: 100,
        name: 'Test Partner 2',
        organizationId,
      },
    ];

    const relatedAccessiblePartners = relatedPartners.filter(
      (p) => p.id === partnerId,
    );
    workspaceService.findOneAsPromise = jest
      .fn()
      .mockResolvedValue({ result: { organizationId } });

    organizationService.findWorkspacesByOrganizationAsPromise = jest
      .fn()
      .mockResolvedValue({ result: relatedPartners });

    organizationUserService.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise =
      jest.fn().mockResolvedValue({ result: [] });

    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue(relatedAccessiblePartners);

    const result = await service.getRelatedAccessiblePartners(
      personId,
      partnerId,
    );
    expect(result).toEqual(relatedAccessiblePartners);
  });

  it('test partner access when org admin', async () => {
    scoringAuthService.isUserPartnerManager = jest.fn().mockReturnValue(false);
    scoringAuthService.getIsUserOrgAdmin = jest.fn().mockReturnValue(true);
    const personId = 123;
    const partnerId = 456;

    const roles: ReadPartnerRolesDto = {
      personId,
      roles: [
        {
          partnerId,
          role: 'STANDARD',
        },
        {
          partnerId: 999,
          role: 'ADMIN',
        },
      ],
    };
    personService.getAllRolesForUser = jest
      .fn()
      .mockReturnValue(axiosResponse(roles));
    const organizationId = '123';
    const relatedPartners: ReadWorkspaceSearchDto[] = [
      {
        id: partnerId,
        name: 'Test Partner',
        organizationId,
      },
      {
        id: 100,
        name: 'Test Partner 2',
        organizationId,
      },
    ];

    const relatedAccessiblePartners = relatedPartners.filter(
      (p) => p.id === partnerId,
    );

    workspaceService.findOneAsPromise = jest
      .fn()
      .mockResolvedValue({ result: { organizationId } });

    organizationService.findWorkspacesByOrganizationAsPromise = jest
      .fn()
      .mockResolvedValue({ result: relatedPartners });

    organizationUserService.findUserAssociatedOrganizationsAndWorkspaceCountAsPromise =
      jest.fn().mockResolvedValue({ result: [] });

    scoringAuthService.getRelatedAccessiblePartners = jest
      .fn()
      .mockResolvedValue(relatedAccessiblePartners);

    const result = await service.getRelatedAccessiblePartners(
      personId,
      partnerId,
    );
    expect(result).toEqual(relatedAccessiblePartners);
  });

  it('Check the shape of the report options response', async () => {
    const organizationId = '123';
    const relatedWorkspaces: ReadWorkspaceSearchDto[] = [
      {
        id: 1,
        name: 'Test Workspace 1',
        organizationId,
      },
      {
        id: 2,
        name: 'Test Workspace 2',
        organizationId,
      },
    ];
    brandService.getBrandsAsPromise = jest
      .fn()
      .mockReturnValue(Promise.resolve({ result: [] }));
    const result = await service.getReportOptions(relatedWorkspaces);
    expect(result).toBeDefined();
    expect(result).toHaveProperty('markets');
    expect(result.markets).toBeInstanceOf(Array);
    expect(result).toHaveProperty('brands');
    expect(result.brands).toBeInstanceOf(Array);
    expect(result.brands.length).toBe(0); //not specified is not included
    expect(result).toHaveProperty('workspaces');
    expect(result.workspaces).toBeInstanceOf(Array);
    expect(result.workspaces.length).toBe(relatedWorkspaces.length);
  });

  const workspaceId = 123;
  const createDto: CreateReportDto = {
    filters: [
      {
        fieldName: 'workspaceId',
        operator: 'equals',
        value: workspaceId,
      },
      {
        fieldName: 'date',
        operator: 'between',
        value: ['2023-01-01', '2023-06-01'],
      },
    ],
    groupBy: {
      columns: ['month', 'batchType'],
      rows: ['market', 'workspace'],
    },
  };

  it('Not Specified added when other options are present', async () => {
    const organizationId = '123';
    const relatedWorkspaces: ReadWorkspaceSearchDto[] = [
      {
        id: 1,
        name: 'Test Workspace 1',
        organizationId,
        markets: [], //workspace markets are no longer used
        brands: [], //workspace brands are no longer used
      },
    ];
    marketRepository.find = jest
      .fn()
      .mockResolvedValue([{ isoCode: 'US', name: 'United States' }]);
    const result = await service.getReportOptions(relatedWorkspaces);
    expect(result).toBeDefined();
    expect(result).toHaveProperty('markets');
    expect(result.markets).toBeInstanceOf(Array);
    const lastMarketIndex = result.markets.length - 1;
    expect(result.markets[lastMarketIndex].value).toBe(NOT_SPECIFIED_VALUE);

    expect(result).toHaveProperty('brands');
    expect(result.brands).toBeInstanceOf(Array);
    expect(result.brands.length).toBe(MOCK_BRANDS.length + 1);
    const lastBrandIndex = result.brands.length - 1;
    expect(result.brands[lastBrandIndex].value).toBe(NOT_SPECIFIED_VALUE);

    expect(result).toHaveProperty('workspaces');
  });

  it('Test adoption csv logic', async () => {
    reportService.createAdoptionReport = jest
      .fn()
      .mockReturnValue(axiosResponse(MOCK_RESPONSE_DTO));
    const result = await service.generateAdoptionReportCsv(createDto);
    expect(result).toBeDefined();
    const lines = result.split('\n');

    const columnHeader = 1;
    const dataRows = new Set(MOCK_RESPONSE_DTO.data.map((cell) => cell.market))
      .size;
    //check that the response is the expected number of lines
    expect(lines.length).toEqual(columnHeader + dataRows);

    //check that Not Specified comes last and is not an identifier
    expect(lines[lines.length - 1]).toMatch(/^Not Specified/);

    const numberOfColumns = lines[0].split(',').length;
    //check that the number of columns is greater than 0
    expect(numberOfColumns).toBeGreaterThan(0);

    //check that all lines are the same length
    lines.forEach((line) => {
      expect(line.split(',').length).toEqual(numberOfColumns);
    });

    // Initialize an object to store the totals per workspace/market/batch type
    const totals: Record<string, number> = {};
    MOCK_RESPONSE_DTO.data.forEach((item) => {
      const { workspace, market, batchType, totalScored } = item;
      const key = `${workspace}_${market}_${batchType}`;
      // Ensure the key exists in the totals object
      if (!totals[key]) {
        totals[key] = 0;
      }
      // Add the totalScored to the corresponding key
      totals[key] += parseInt(totalScored, 10);
    });
    const totalValues = Object.values(totals).map((e) => e.toString());

    // get the expected value and sort them (including totals)
    let expectedValues = MOCK_RESPONSE_DTO.data.map((cell) => cell.totalScored);
    expectedValues.push(...totalValues);
    expectedValues = expectedValues.sort();

    const foundValues: string[] = [];
    //skip column header
    lines.slice(1).forEach((line) => {
      const splitLine = line.split(',');
      //skip the row label(s)
      foundValues.push(
        ...splitLine
          .slice(createDto.groupBy.rows.length)
          .filter((value) => value && value !== '0'),
      );
    });
    //check that all the input values are present in the output
    expect(foundValues.sort()).toEqual(expectedValues);
  });

  it('Test no parameters criteria name translation', () => {
    const label = getDisplayNameFor('OFFER_MESSAGE', {});
    expect(label).toEqual('Promotional offer(s)');
  });

  it('Test single parameter criteria name translation', () => {
    const parameters = {
      maxFirstBrandAppearance: 3,
    };
    const label = getDisplayNameFor('BRAND_NAME_OR_LOGO', parameters);
    expect(label).toEqual(
      `Brand name / logo, first ${parameters.maxFirstBrandAppearance} seconds`,
    );
  });

  it('Test multiple parameter criteria name translation', () => {
    const parameters = {
      minDuration: 21,
      maxDuration: 34,
    };
    const label = getDisplayNameFor('VIDEO_DURATION', parameters);
    expect(label).toEqual(
      `Video length (${parameters.minDuration} - ${parameters.maxDuration}) seconds`,
    );
  });

  it('Test complex parameter criteria name translation', () => {
    const parameters = { aspectRatios: ['4:3', '16:9'] };
    const label = getDisplayNameFor('FRAMED_FOR_MOBILE', parameters);
    expect(label).toEqual(`Aspect ratio ${parameters.aspectRatios.join(', ')}`);
  });

  it('Test de-dup and sorting of brands', async () => {
    const organizationId = '123';
    const relatedWorkspaces: ReadWorkspaceSearchDto[] = [
      {
        id: 1,
        name: 'Test Workspace 1',
        organizationId,
        brands: [], //workspace brands are no longer used
      },
      {
        id: 2,
        name: 'Test Workspace 2',
        organizationId,
        brands: [], //workspace brands are no longer used
      },
    ];
    const reportOptions = await service.getReportOptions(relatedWorkspaces);
    const sortedLabels = MOCK_BRANDS.map((brand) => brand.name).sort();
    sortedLabels.push(NOT_SPECIFIED_LABEL);
    expect(reportOptions.brands.map((b) => b.label)).toEqual(sortedLabels);
  });

  it('Test addition of All Criteria filter option', async () => {
    const criteriaSets = [
      {
        id: 1,
        name: 'Test Criteria Set 1',
      },
    ];
    criteriaSetService.getCriteriaSets = jest
      .fn()
      .mockReturnValue(of(criteriaSets));
    const organizationId = '123';
    const relatedWorkspaces: ReadWorkspaceSearchDto[] = [
      {
        id: 1,
        name: 'Test Workspace 1',
        organizationId,
      },
    ];

    const allCriteriaOption = {
      label: 'All Criteria',
      value: '*',
    };
    const matchesAll = (obj: any) =>
      obj.label === allCriteriaOption.label &&
      obj.value === allCriteriaOption.value;

    const reportOptions = await service.getReportOptions(relatedWorkspaces);
    expect(reportOptions.criteriaSets.some((obj) => matchesAll(obj))).toBe(
      true,
    );
  });

  test('should respond with correct accessible report types', async () => {
    const organizationId = '27a7e882-43de-4bfa-8f53-3b62875b8432';
    const workspaceId = 123;
    const workspace = { result: { organizationId } };
    workspaceService.findOneAsPromise = jest.fn().mockResolvedValue(workspace);
    const result = await service.getAccessibleReportTypes(workspaceId);
    expect(result.accessibleReportTypes).toEqual([
      'ADHERENCE',
      'IMPRESSION_ADHERENCE',
      'ADOPTION',
      'IN_FLIGHT',
      'DIVERSITY',
    ]);
  });

  it('should aggregate reports for all identifiers', async () => {
    const mockResponses: { [key: string]: MockDEIResponse } = {
      'skin-tone': {
        status: 'OK',
        result: {
          /* mock data for skin-tone */
        },
      },
      age: {
        status: 'OK',
        result: {
          /* mock data for age */
        },
      },
      gender: {
        status: 'OK',
        result: {
          /* mock data for gender */
        },
      },
    };

    deiReportService.getDEIDemographicReportAsPromise = jest.fn((identifier) =>
      Promise.resolve(mockResponses[identifier]),
    );

    const result = await service.generateDiversityReport(createDto);

    expect(result).toEqual({
      data: Object.values(mockResponses).map((response) => response.result),
    });

    expect(
      deiReportService.getDEIDemographicReportAsPromise,
    ).toHaveBeenCalledWith('skin-tone', createDto);
    expect(
      deiReportService.getDEIDemographicReportAsPromise,
    ).toHaveBeenCalledWith('age', createDto);
    expect(
      deiReportService.getDEIDemographicReportAsPromise,
    ).toHaveBeenCalledWith('gender', createDto);
  });

  it('should send the correct modified create report requests to the reports service', () => {
    const createReportDto: CreateReportDto = {
      filters: [
        {
          fieldName: 'batchType',
          operator: 'equals',
          value: IN_FLIGHT,
        },
        {
          fieldName: 'workspaceId',
          operator: 'equals',
          value: '123',
        },
        {
          fieldName: 'date',
          operator: 'between',
          value: ['2023-01-01', '2023-06-01'],
        },
      ],
      groupBy: {
        columns: ['month', 'batchType'],
        rows: ['market', 'workspace'],
      },
    };
    const expectedModifiedCreateReportDto: CreateReportDto = {
      filters: [
        { fieldName: 'workspaceId', operator: 'equals', value: '123' },
        {
          fieldName: 'date',
          operator: 'between',
          value: ['2023-01-01', '2023-06-01'],
        },
        { fieldName: 'batchType', operator: 'equals', value: IN_FLIGHT },
      ],
      groupBy: {
        columns: ['month', 'batchType'],
        rows: ['market', 'workspace'],
      },
    };
    service.generateAdherenceReport(createReportDto, true);
    expect(
      mockCreateSnowflakeAdherenceReportV2AsPromise,
    ).toHaveBeenNthCalledWith(
      1,
      EntityType.AD_ASSET,
      expectedModifiedCreateReportDto,
    );
  });

  it('should correctly create csv for IN_FLIGHT adherence report metadata', async () => {
    reportService.getAdherenceReportRowMetadataAsPromise = jest
      .fn()
      .mockResolvedValueOnce(MOCK_IN_FLIGHT_METADATA_RESPONSE);

    const csvResponse = await service.generateAdherenceReportMetadataCsv(
      MOCK_IN_FLIGHT_REPORT_DTO,
    );
    expect(csvResponse).toBe(expectedInFlightMetadataCSV);
  });

  it('should correctly create csv for PRE_FLIGHT adherence report metadata', async () => {
    reportService.getAdherenceReportRowMetadataAsPromise = jest
      .fn()
      .mockResolvedValueOnce(MOCK_PRE_FLIGHT_METADATA_RESPONSE);

    const csvResponse = await service.generateAdherenceReportMetadataCsv(
      MOCK_PRE_FLIGHT_REPORT_DTO,
    );
    expect(csvResponse).toBe(expectedPreFlightMetadataCSV);
  });

  it('should throw error when no metadata returned for adherence metadata csv api', async () => {
    reportService.getAdherenceReportRowMetadataAsPromise = jest
      .fn()
      .mockResolvedValueOnce({ status: 'OK', result: [] });

    await expect(
      service.generateAdherenceReportMetadataCsv(MOCK_PRE_FLIGHT_REPORT_DTO),
    ).rejects.toThrow(
      'No data found for the given report settings and row selection',
    );
  });

  it('should correctly aggregate and calculate column and row data for v3 adherence report', () => {
    const firstResponse = service.mergeResultsReportV3(
      {
        data: mockV3AdherenceReportData,
        impressionCountData: mockCriteriaGroupImpressionCountData,
      },
      {
        columns: ['market', 'workspace'],
        rows: ['criteriaGroup', 'criteria'],
      },
      new Map([
        ['can', 'Canada'],
        ['usa', 'United States of America'],
      ]),
    );

    expect(firstResponse).toEqual(
      expectedAdherenceResponseGroupByCriteriaGroupMarketAndWorkspace,
    );

    const secondResponse = service.mergeResultsReportV3(
      {
        data: mockV3AdherenceReportData,
        creativeCountData: mockChannelCreativeCountData,
      },
      {
        columns: ['brand'],
        rows: ['channel', 'criteria'],
      },
      new Map([
        ['can', 'Canada'],
        ['usa', 'United States of America'],
      ]),
    );

    expect(secondResponse).toEqual(
      expectedAdherenceResponseGroupByChannelAndBrand,
    );
  });
});
