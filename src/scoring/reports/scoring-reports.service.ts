import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import {
  AdherenceReportV3Dto,
  CreateReportDto,
  CreateReportDtoFiltersInner,
  CreateReportGroupByDto,
  CriteriaStatusDto,
  DEIReportService,
  EarliestImpressionDateRequestDto,
  GetPreflightChannelAggregateScores200Response,
  GetCriteriaAggregateScoresV2200Response,
  GetEarliestImpressionDate200Response,
  GetMediaScoresForInflightReport200Response,
  RollupReportsService,
  AdherenceReportV3DataItemDto,
  CreativeOrImpressionCountDataDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { CriteriaSetService } from '../criteria/criteria-set.service';
import {
  catchError,
  firstValueFrom,
  forkJoin,
  from,
  map,
  Observable,
  of,
  switchMap,
} from 'rxjs';
import { rethrowAxiosError } from '../api.utils';
import {
  BrandService,
  MediaService,
  WorkspaceService,
} from '@vidmob/vidmob-organization-service-sdk';
import {
  InflightReportSortOptions,
  Option,
  ScoringReportOptionsDto,
} from './dtos/report-options.dto';
import {
  AdoptionReportDataItem,
  ImpressionAdherenceReportDataItem,
  ImpressionAdherenceReportTotalItem,
  ReadWorkspaceSearchDto,
} from './internal-scoring-reports.interfaces';
import { ReportFlagDto } from './dtos/report-flag.dto';
import { FlagsGroupReducer } from './utils/flags-group-reducer';
import { AggregationColumnDto } from '../../reports/model/aggregation-column.dto';
import md5 from 'md5';
import * as fastcsv from 'fast-csv';
import { Readable } from 'stream';
import { AccessibleReportTypesDto } from './dtos/accessible-report-types.dto';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Market } from '../../entities/market.entity';
import { AdherenceRow } from './dtos/adherence-row.dto';
import {
  addNormsData,
  buildAdherenceColumn,
  buildCriteriaIdString,
  calculateAdherence,
  calculateTotalColumn,
  createAdherenceNormsList,
  createParentRows,
  findAndReplaceWithMergedColumn,
} from './utils/adherence-utils';
import { GetScoringNorms200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getScoringNorms200Response';
import { ScoringNormsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/scoringNormsResponseDto';
import { ScoreService } from '../score/score.service';
import { ScoringNormsRequest } from '../score/dto/scoring-norms-request.dto';
import {
  ReportFilterEqualsDto,
  ReportFilterOperator,
} from '../../reports/model/report-filters.dto';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import {
  ALL_SUPPORTED_REPORTS,
  ALL_SUPPORTED_REPORTS_EXCEPT_DIVERSITY,
  ORGANIZATIONS_WITH_DIVERSITY_REPORT_ACCESS,
} from './constants/access.constants';
import {
  ADHERENCE_REPORT_GROUP_BY_COLUMNS_OPTIONS,
  ADHERENCE_PERCENTAGE_DECIMAL_PLACES,
  ALL_VALUE,
  BatchType,
  DiversityIdentifier,
  ENTITY_TYPE_FILTER_KEY,
  EntityType,
  IN_FLIGHT,
  NOT_SPECIFIED,
  NOT_SPECIFIED_LABEL,
  NOT_SPECIFIED_VALUE,
  PRE_FLIGHT,
  AdherenceReportParentRowKey,
  AdherenceReportChildRowKey,
  AdherenceReportColumnKey,
  AdherenceAggregateRowDataToAccumulatorParams,
  OVERALL_COLUMN_HEADER_DISPLAY_NAME,
  AdherenceAggregateColumnDataToAccumulatorParams,
} from './constants/constants';
import { ScoringAdoptionReportCsvGenerator } from './csv-generators/adoption-report-csv-generator';
import { ScoringAdherenceReportCsvGenerator } from './csv-generators/adherence-report-csv-generator';
import { AdherenceReportMetadataCsvGenerator } from './csv-generators/adherence-report-metadata-csv-generator';
import { ApplicabilityMediaTypesEnum } from '../scoring-constants';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { CriteriaService } from '../criteria/criteria.service';
import { MediaObjectAndScoresDto } from './dtos/media-object-and-scores.dto';
import { InflightReportCsvGenerator } from './inflight-report-csv-generator';
import { PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP } from '../../constants/platform.constants';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';
import { ImpressionAdherenceReportV3ResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/impressionAdherenceReportV3ResponseDto';
import {
  AdherenceColumn,
  AdherenceReportV3ResponseDto,
} from './dtos/adherence-report-v3-response.dto';

@Injectable()
export class ScoringReportsService {
  private readonly logger = new Logger(ScoringReportsService.name);

  constructor(
    private readonly scoreService: ScoreService,
    private readonly reportService: RollupReportsService,
    private readonly workspaceService: WorkspaceService,
    private readonly brandService: BrandService,
    private readonly scoringCriteriaSetsService: CriteriaSetService,
    private readonly criteriaService: CriteriaService,
    private readonly inflightReportCsvService: InflightReportCsvGenerator,
    private readonly diversityReportService: DEIReportService,
    private readonly scoringAuthService: ScoringAuthService,
    private readonly adherenceReportMetadataCsvGenerator: AdherenceReportMetadataCsvGenerator,
    private readonly mediaServiceSDK: MediaService,
    @InjectDataSource()
    private readonly datasource: DataSource,
    @InjectRepository(Market)
    private readonly marketRepository: Repository<Market>,
  ) {}

  public async getAccessibleReportTypes(
    workspaceId: number,
  ): Promise<AccessibleReportTypesDto> {
    const workspace = await this.workspaceService.findOneAsPromise(workspaceId);
    const organizationId = workspace.result.organizationId;

    return {
      accessibleReportTypes:
        ORGANIZATIONS_WITH_DIVERSITY_REPORT_ACCESS.includes(organizationId)
          ? ALL_SUPPORTED_REPORTS
          : ALL_SUPPORTED_REPORTS_EXCEPT_DIVERSITY,
    };
  }

  /**
   * Wraps the scoring auth service method of the same name for ease of use and testing.
   * @param userId
   * @param workspaceId
   */

  public async getRelatedAccessiblePartners(
    userId: number,
    workspaceId: number,
  ) {
    return this.scoringAuthService.getRelatedAccessiblePartners(
      userId,
      workspaceId,
    );
  }

  public async getChannelIdentifiers(): Promise<string[]> {
    const identifiersResult = await this.datasource.query(
      'SELECT identifier FROM platform',
    );

    return identifiersResult.map((item: { identifier: string }) => {
      return item.identifier;
    });
  }

  private pickKeys(obj: Record<string, any>, keys: string[]) {
    return keys.reduce((acc, key) => {
      acc[key] = obj[key];
      return acc;
    }, {} as Record<string, any>);
  }

  private pickIds(obj: Record<string, any>, keys: string[]) {
    const onlyParent = keys.length == 1;
    return keys.reduce((acc, key, currentIndex) => {
      if (onlyParent) {
        acc['id'] = obj[key];
      } else {
        acc[currentIndex === 0 ? 'parentId' : 'id'] = obj[key];
      }
      return acc;
    }, {} as Record<string, string>);
  }

  public async generateDiversityReport(createReportDto: CreateReportDto) {
    try {
      return {
        data: await Promise.all(
          Object.values(DiversityIdentifier).map((identifier) =>
            this.diversityReportService
              .getDEIDemographicReportAsPromise(identifier, createReportDto)
              .then((response) => response.result),
          ),
        ),
      };
    } catch (err) {
      throw rethrowAxiosError(err);
    }
  }

  public async generateDiversityReportV2(createReportDto: CreateReportDto) {
    const batchTypes = this.getContainedBatchTypes(createReportDto);
    this.validateBatchTypesForV2(batchTypes);
    try {
      return {
        data: await Promise.all(
          Object.values(DiversityIdentifier).map((identifier) =>
            this.diversityReportService
              .getDEIDemographicReportV2AsPromise(identifier, createReportDto)
              .then((response) => response.result),
          ),
        ),
      };
    } catch (err) {
      throw rethrowAxiosError(err);
    }
  }

  /**
   * Generate an adoption report based on the provided input.
   * @param createReportDto - the report parameters.
   */
  private getAdoptionReportData(createReportDto: CreateReportDto) {
    return this.reportService.createAdoptionReport(createReportDto).pipe(
      map(
        (axiosResponse) =>
          axiosResponse.data.result as unknown as {
            data: AdoptionReportDataItem[];
          },
      ),
    );
  }

  private getAdoptionReportDataV2(createReportDto: CreateReportDto) {
    const { entityTypeFilter, filtersWithoutEntityType } =
      this.extractEntityTypeFilter(createReportDto.filters);
    createReportDto.filters = filtersWithoutEntityType;
    return this.reportService
      .createAdoptionReportV2(
        entityTypeFilter?.value.toString() ?? EntityType.AD_ASSET,
        createReportDto,
      )
      .pipe(
        map(
          (axiosResponse) =>
            axiosResponse.data.result as unknown as {
              data: AdoptionReportDataItem[];
              rowTotalData: AdoptionReportDataItem[];
              totalMediaCount: number;
            },
        ),
      );
  }

  generateAdoptionReport(createReportDto: CreateReportDto) {
    return this.getAdoptionReportData(createReportDto).pipe(
      map((result) => ({
        data: result.data.map((item) => ({
          row: this.pickKeys(item, createReportDto.groupBy.rows),
          column: this.pickKeys(item, createReportDto.groupBy.columns),
          data: { type: 'number', value: Number(item.totalScored) },
        })),
      })),
      map((result) => ({
        ...result,
        flags: this.getAdoptionReportFlags(createReportDto),
      })),
      catchError((err) => rethrowAxiosError(err)),
    );
  }

  generateAdoptionReportV2(createReportDto: CreateReportDto) {
    const batchTypes = this.getContainedBatchTypes(createReportDto);
    this.validateBatchTypesForV2(batchTypes);
    return this.getAdoptionReportDataV2(createReportDto).pipe(
      map((result) => ({
        data: result.data.map((item) => ({
          row: this.pickKeys(item, createReportDto.groupBy.rows),
          column: this.pickKeys(item, createReportDto.groupBy.columns),
          data: { type: 'number', value: Number(item.totalScored) },
        })),
        rowTotalData: result.rowTotalData.map((item) => ({
          row: this.pickKeys(item, createReportDto.groupBy.rows),
          column: 'row_total',
          data: { type: 'number', value: Number(item.totalScored) },
        })),
        totalMediaCount: result.totalMediaCount,
      })),
      map((result) => ({
        ...result,
        flags: this.getAdoptionReportFlags(createReportDto),
      })),
      catchError((err) => rethrowAxiosError(err)),
    );
  }

  getAdoptionReportFlags(createReportDto: CreateReportDto) {
    const groupings = [
      ...createReportDto.groupBy.rows,
      ...createReportDto.groupBy.columns,
    ];

    return groupings.reduce((acc, grouping) => {
      if (FlagsGroupReducer[grouping]) {
        return FlagsGroupReducer[grouping](acc, createReportDto);
      }

      return acc;
    }, [] as ReportFlagDto[]);
  }

  private async getAdherenceReportDataHelperAsPromise(
    createReportDto: CreateReportDto,
    batchType: 'PRE_FLIGHT' | 'IN_FLIGHT',
  ): Promise<ImpressionAdherenceReportDataItem[]> {
    const filters = createReportDto.filters.filter(
      (f) =>
        f.fieldName !== 'batchType' && f.fieldName !== 'normsConfiguration',
    );
    const batchFilter: ReportFilterEqualsDto = {
      fieldName: 'batchType',
      operator: ReportFilterOperator.Equals,
      value: batchType,
    };
    const modifiedCreateReportDto = {
      ...createReportDto,
      filters: [...filters, batchFilter],
    };
    const response =
      await this.reportService.createSnowflakeAdherenceReportAsPromise(
        modifiedCreateReportDto,
      );
    const data = response.result
      .data as unknown as ImpressionAdherenceReportDataItem[];

    return data.map((item) => {
      const column = item.column;
      return { ...item, column: { ...column, batchType } };
    });
  }

  private async getAdherenceReportDataHelperSupportsAdvancedFiltersAsPromise(
    initialCreateReportDto: CreateReportDto,
    batchType: 'PRE_FLIGHT' | 'IN_FLIGHT',
  ): Promise<ImpressionAdherenceReportDataItem[]> {
    const { modifiedCreateReportDto, entityType } =
      this.getFormatedRequestAndEntityType(initialCreateReportDto, batchType);

    const response =
      await this.reportService.createSnowflakeAdherenceReportV2AsPromise(
        entityType,
        modifiedCreateReportDto,
      );
    const data = response.result
      .data as unknown as ImpressionAdherenceReportDataItem[];

    return data.map((item) => {
      const column = item.column;
      return { ...item, column: { ...column, batchType } };
    });
  }

  private getFormatedRequestAndEntityType(
    initialCreateReportDto: CreateReportDto,
    optionalBatchType?: string,
  ): { modifiedCreateReportDto: CreateReportDto; entityType: EntityType } {
    const batchTypes = this.getContainedBatchTypes(initialCreateReportDto);
    if (!optionalBatchType && batchTypes.length !== 1) {
      throw new BadRequestException(
        `Invalid number of batch types: ${batchTypes.length}, can only be 1 batch type`,
      );
    }

    const { entityTypeFilter, filtersWithoutEntityType } =
      this.extractEntityTypeFilter(initialCreateReportDto.filters);
    initialCreateReportDto.filters = filtersWithoutEntityType;
    const filters = initialCreateReportDto.filters.filter(
      (f) =>
        f.fieldName !== 'batchType' && f.fieldName !== 'normsConfiguration',
    );

    const batchFilter: ReportFilterEqualsDto = {
      fieldName: 'batchType',
      operator: ReportFilterOperator.Equals,
      value: optionalBatchType ?? batchTypes[0],
    };
    const modifiedCreateReportDto = {
      ...initialCreateReportDto,
      filters: [...filters, batchFilter],
    };

    return {
      modifiedCreateReportDto,
      entityType:
        (entityTypeFilter?.value.toString() as EntityType) ??
        EntityType.AD_ASSET,
    };
  }

  private mapToLegacyAdherenceReportDataItem(
    item: ImpressionAdherenceReportDataItem,
  ) {
    return {
      row: {
        brand: item.row.brand,
        market: item.row.market,
        workspace: item.row.workspace,
      },
      column: {
        criteria: {
          criteriaIsOptional: Boolean(item.column.criteria.isOptional),
          criteriaIsBestPractice: Boolean(item.column.criteria.isBestPractice),
          criteriaIdentifier: item.column.criteria.criteriaIdentifier,
          criteriaParameters: item.column.criteria.criteriaParameters,
          criteriaPlatform: item.column.criteria.criteriaPlatform,
          criteriaName: item.column.criteria.criteriaName,
        },
        batchType: item.column.batchType,
      },
      data: {
        type: item.data.type,
        value: item.data.value,
      },
    };
  }

  private async getCriteriaSetsForReportOptions(
    organizationId: string,
    accessibleWorkspaceIds: number[],
  ): Promise<Option[]> {
    const criteriaSets =
      (await firstValueFrom(
        this.scoringCriteriaSetsService.getCriteriaSets(organizationId, {
          workspaceIds: accessibleWorkspaceIds,
        }),
      )) || [];

    const options = criteriaSets.map(
      (set: any): { label: string; value: string } => {
        return {
          label: set.name,
          value: set.id.toString(),
        };
      },
    );
    if (options.length > 0) {
      options.unshift({
        label: 'All Criteria',
        value: ALL_VALUE,
      });
    }
    return options;
  }

  private getContainedBatchTypes(createReportDto: CreateReportDto) {
    const batchType = createReportDto.filters.find(
      (f) => f.fieldName === 'batchType',
    );
    const bothBatchTypes = [IN_FLIGHT, PRE_FLIGHT] as const;
    if (batchType?.value) {
      if (Array.isArray(batchType.value)) {
        const batchTypes = batchType.value as (
          | 'IN_FLIGHT'
          | 'PRE_FLIGHT'
          | '*'
        )[];
        if (batchTypes.includes(ALL_VALUE)) {
          return bothBatchTypes;
        }
        return batchType.value as ('IN_FLIGHT' | 'PRE_FLIGHT')[];
      }
      if (batchType.value === ALL_VALUE) {
        return bothBatchTypes;
      }
      return [batchType.value as 'IN_FLIGHT' | 'PRE_FLIGHT'];
    }
    return bothBatchTypes;
  }

  private validateBatchTypesForV2(
    batchTypes:
      | readonly [typeof IN_FLIGHT, typeof PRE_FLIGHT]
      | (typeof IN_FLIGHT | typeof PRE_FLIGHT)[],
  ) {
    if (batchTypes.length > 1) {
      throw new Error(
        `Only one batch type is allowed, ${PRE_FLIGHT} or ${IN_FLIGHT}`,
      );
    }
  }

  private getReportSubRequests(
    createReportDto: CreateReportDto,
    withAnalyticsAdvancedFiltersSupport = false,
  ) {
    const batchTypes = this.getContainedBatchTypes(createReportDto);

    if (withAnalyticsAdvancedFiltersSupport) {
      this.validateBatchTypesForV2(batchTypes);

      return batchTypes.map((batchType) => {
        return this.getAdherenceReportDataHelperSupportsAdvancedFiltersAsPromise(
          createReportDto,
          batchType,
        );
      });
    }

    return batchTypes.map((batchType) => {
      return this.getAdherenceReportDataHelperAsPromise(
        createReportDto,
        batchType,
      );
    });
  }

  public async generateAdherenceReport(
    createReportDto: CreateReportDto,
    withAnalyticsAdvancedFiltersSupport: boolean,
  ) {
    const subRequests = this.getReportSubRequests(
      createReportDto,
      withAnalyticsAdvancedFiltersSupport,
    );
    const data = (await Promise.all(subRequests))
      .flat()
      .map((item) =>
        this.mapToLegacyAdherenceReportDataItem(
          item as ImpressionAdherenceReportDataItem,
        ),
      );

    return {
      data,
      aggregationColumns: this.getAdherenceReportCriteriaAggregationColumns(
        createReportDto,
        data,
      ),
    };
  }

  public async generateAdherenceNormsReport(
    createReportDto: CreateReportDto,
    withAnalyticsAdvancedFiltersSupport: boolean,
  ) {
    const subRequests = this.getReportSubRequests(
      createReportDto,
      withAnalyticsAdvancedFiltersSupport,
    );
    const data = (await Promise.all(subRequests)).flat();
    const countryIsoCodeNameMap = await this.getCountryIsoCodeNameMap();

    const normsRequest = this.scoreService.extractNormsRequest(createReportDto);
    // TODO Fetch both data sets concurrently
    return forkJoin({
      adherenceData: of(data),
      normsData: this.sendNormsRequest(normsRequest).pipe(
        map((response) => response?.result),
        catchError((err) => rethrowAxiosError(err)),
      ),
    }).pipe(
      map(({ adherenceData, normsData }) =>
        this.mergeResults(
          adherenceData as ImpressionAdherenceReportDataItem[],
          normsData,
          createReportDto,
          countryIsoCodeNameMap,
        ),
      ),
    );
  }

  public async generateAdherenceNormsReportV3(
    initialCreateReportDto: CreateReportDto,
  ) {
    const { modifiedCreateReportDto, entityType } =
      this.getFormatedRequestAndEntityType(initialCreateReportDto);
    const normsRequest = this.scoreService.extractNormsRequest(
      initialCreateReportDto,
    );

    return forkJoin({
      adherenceData: from(
        this.reportService.createSnowflakeAdherenceReportV3AsPromise(
          entityType,
          modifiedCreateReportDto,
        ),
      ).pipe(
        map((response) => response.result),
        catchError((err) => rethrowAxiosError(err)),
      ),
      normsData: this.sendNormsRequest(normsRequest).pipe(
        map((response) => response?.result),
        catchError((err) => rethrowAxiosError(err)),
      ),
      columnDisplayNameMap: from(
        this.getColumnDisplayNameMap(modifiedCreateReportDto.groupBy),
      ),
    }).pipe(
      map(({ adherenceData, normsData, columnDisplayNameMap }) =>
        this.mergeResultsReportV3(
          adherenceData,
          modifiedCreateReportDto.groupBy,
          columnDisplayNameMap,
        ),
      ),
    );
  }

  public sendNormsRequest(
    request: ScoringNormsRequest | undefined,
  ): Observable<GetScoringNorms200Response | null> {
    if (!request || request.isEnabled !== true) {
      return of(null);
    }
    return from(this.scoreService.buildNormsRequest(request)).pipe(
      switchMap((req) => {
        // Now handle the Observable from getScoringNorms
        return this.scoreService
          .getScoringNorms(req)
          .pipe(map((response) => response.data));
      }),
    );
  }

  private mergeResults(
    adherenceData: ImpressionAdherenceReportDataItem[],
    normsData: ScoringNormsResponseDto | undefined,
    createReportDto: CreateReportDto,
    countryIsoCodeNameMap: Map<string, string>,
  ) {
    // Implement merging logic here
    // Example: Combine data and calculate metrics
    const adherenceMap: Map<string, AdherenceRow> = new Map();
    adherenceData.forEach((item) => {
      const data = buildAdherenceColumn(item);
      const ids = this.pickIds(item.row, createReportDto.groupBy.rows);
      const rowId = `${ids['parentId']}-${ids['id']}`;
      let row = adherenceMap.get(rowId);
      if (row) {
        findAndReplaceWithMergedColumn(row, data);
      } else {
        row = {
          id: rowId,
          parentId: ids['parentId'],
          displayName: ids['id'],
          columns: [data],
        };
        adherenceMap.set(rowId, row);
      }
    });

    const rows = Array.from(adherenceMap.values());
    const parentRows = createParentRows(rows);
    const allRows = parentRows.concat(rows);

    calculateTotalColumn(allRows);
    const titleRow = createAdherenceNormsList(rows);
    calculateAdherence(allRows, titleRow);

    addNormsData(titleRow, normsData);

    return {
      columns: titleRow,
      rows: this.getDisplayNames(allRows, countryIsoCodeNameMap),
    };
  }

  private getDisplayNames(
    rows: AdherenceRow[],
    countryIsoCodeNameMap: Map<string, string>,
  ): AdherenceRow[] {
    return rows
      .filter((row) => !row.id?.toLowerCase().startsWith('ant')) // Filter out rows with 'ant' in the ID. TODO: Remove this when ANT is removed from the DB
      .map((row) => {
        if (row.displayName === NOT_SPECIFIED_VALUE) {
          return { ...row, displayName: NOT_SPECIFIED_LABEL };
        }

        if (countryIsoCodeNameMap.has(row.displayName.toLowerCase())) {
          const countryName = countryIsoCodeNameMap.get(
            row.displayName.toLowerCase(),
          );
          if (countryName) {
            return { ...row, displayName: countryName };
          }
        }
        return row;
      })
      .sort((a, b) => {
        if (a.displayName === NOT_SPECIFIED_LABEL) return 1;
        if (b.displayName === NOT_SPECIFIED_LABEL) return -1;
        return a.displayName.localeCompare(b.displayName);
      });
  }

  /**
   * Generate an impression-adherence report based on the provided input.
   * @param createReportDto - the report parameters.
   */
  private getImpressionAdherenceReportDataHelper(
    createReportDto: CreateReportDto,
  ) {
    return this.reportService
      .createImpressionAdherenceReport(createReportDto)
      .pipe(
        map(
          (axiosResponse) =>
            axiosResponse.data.result as unknown as {
              data: ImpressionAdherenceReportDataItem[];
              impressionCountData: ImpressionAdherenceReportTotalItem[];
            },
        ),
      );
  }

  private getImpressionAdherenceReportDataHelperV2(
    createReportDto: CreateReportDto,
  ) {
    const { entityTypeFilter, filtersWithoutEntityType } =
      this.extractEntityTypeFilter(createReportDto.filters);
    createReportDto.filters = filtersWithoutEntityType;
    return this.reportService
      .createImpressionAdherenceReportV2(
        entityTypeFilter?.value.toString() ?? EntityType.AD_ASSET,
        createReportDto,
      )
      .pipe(
        map(
          (axiosResponse) =>
            axiosResponse.data.result as unknown as {
              data: ImpressionAdherenceReportDataItem[];
              impressionCountData: ImpressionAdherenceReportTotalItem[];
            },
        ),
      );
  }

  public generateImpressionAdherenceReport(createReportDto: CreateReportDto) {
    return this.getImpressionAdherenceReportDataHelper(createReportDto).pipe(
      map((result) => ({
        data: result.data.map((item) => ({
          row: {
            brand: item.row.brand,
            market: item.row.market,
            workspace: item.row.workspace,
          },
          column: {
            criteria: {
              criteriaIsOptional: Boolean(item.column.criteria.isOptional),
              criteriaIsBestPractice: Boolean(
                item.column.criteria.isBestPractice,
              ),
              criteriaIdentifier: item.column.criteria.criteriaIdentifier,
              criteriaParameters: item.column.criteria.criteriaParameters,
              criteriaPlatform: item.column.criteria.criteriaPlatform,
              criteriaName: item.column.criteria.criteriaName,
            },
            batchType: 'IN_FLIGHT',
          },
          data: {
            type: item.data.type,
            value: item.data.value,
          },
        })),
        impressionCountData: result.impressionCountData.map((item) => ({
          row: {
            market: item.row.market,
            workspace: item.row.workspace,
            brand: item.row.brand,
          },
          column: item.column,
          data: {
            type: item.data.type,
            value: item.data.value,
          },
        })),
      })),
      catchError((err) => rethrowAxiosError(err)),
    );
  }

  public generateImpressionAdherenceReportV2(createReportDto: CreateReportDto) {
    return this.getImpressionAdherenceReportDataHelperV2(createReportDto).pipe(
      map((result) => ({
        data: result.data.map((item) => ({
          row: {
            brand: item.row.brand,
            market: item.row.market,
            workspace: item.row.workspace,
          },
          column: {
            criteria: {
              criteriaIsOptional: Boolean(item.column.criteria.isOptional),
              criteriaIsBestPractice: Boolean(
                item.column.criteria.isBestPractice,
              ),
              criteriaIdentifier: item.column.criteria.criteriaIdentifier,
              criteriaParameters: item.column.criteria.criteriaParameters,
              criteriaPlatform: item.column.criteria.criteriaPlatform,
              criteriaName: item.column.criteria.criteriaName,
            },
            batchType: BatchType.InFlight,
          },
          data: {
            type: item.data.type,
            value: item.data.value,
          },
        })),
        impressionCountData: result.impressionCountData.map((item) => ({
          row: {
            market: item.row.market,
            workspace: item.row.workspace,
            brand: item.row.brand,
          },
          column: item.column,
          data: {
            type: item.data.type,
            value: item.data.value,
          },
        })),
      })),
      catchError((err) => rethrowAxiosError(err)),
    );
  }

  public async generateImpressionAdherenceReportV3(
    initialCreateReportDto: CreateReportDto,
  ) {
    const { modifiedCreateReportDto, entityType } =
      this.getFormatedRequestAndEntityType(initialCreateReportDto);

    return forkJoin({
      impressionAdherenceData: from(
        this.reportService.createImpressionAdherenceReportV3AsPromise(
          entityType,
          modifiedCreateReportDto,
        ),
      ).pipe(
        map((response) => response.result),
        catchError((err) => rethrowAxiosError(err)),
      ),
      columnDisplayNameMap: from(
        this.getColumnDisplayNameMap(modifiedCreateReportDto.groupBy),
      ),
    }).pipe(
      map(({ impressionAdherenceData, columnDisplayNameMap }) =>
        this.mergeResultsReportV3(
          impressionAdherenceData,
          modifiedCreateReportDto.groupBy,
          columnDisplayNameMap,
        ),
      ),
    );
  }

  private async getColumnDisplayNameMap(groupBy: CreateReportGroupByDto) {
    if (
      groupBy.columns.includes(ADHERENCE_REPORT_GROUP_BY_COLUMNS_OPTIONS.MARKET)
    ) {
      return await this.getCountryIsoCodeNameMap();
    }

    return new Map<string, string>([
      [NOT_SPECIFIED.value, NOT_SPECIFIED.displayName],
    ]);
  }

  /**
   * Merges the results of the Adherence Report V3.
   * Reduces the data to aggregate columns and rows (along with columns for each row)
   * @param response
   * @param groupBy
   * @param columnDisplayNameMap
   */
  public mergeResultsReportV3(
    response: AdherenceReportV3Dto | ImpressionAdherenceReportV3ResponseDto,
    groupBy: CreateReportGroupByDto,
    columnDisplayNameMap: Map<string, string>,
  ): AdherenceReportV3ResponseDto {
    if (!response.data.length) {
      return {
        columns: [],
        rows: [],
      };
    }

    const parentRowKey = groupBy.rows[0] as AdherenceReportParentRowKey;
    const childRowKey = groupBy.rows[1] as AdherenceReportChildRowKey;
    const childColumnKey = groupBy.columns[1] as AdherenceReportColumnKey;
    const parentColumnKey = groupBy.columns[0] as AdherenceReportColumnKey;
    const impressionsOrCreativeCountKey =
      'impressionCountData' in response ? 'impressions' : 'creativeCount';
    const creativeOrImpressionCountData =
      'impressionCountData' in response
        ? response.impressionCountData
        : response.creativeCountData;

    const impressionsOrCreativeCountByItemId =
      this.getImpressionsOrCreativeCountByItemId(
        creativeOrImpressionCountData,
        groupBy,
      );

    const aggregatedRows = response.data.reduce(
      (accumulator: Record<string, any>, item) => {
        const parentRowId = item.row[parentRowKey]?.id ?? '';

        // add parent row data
        accumulator = this.addAggregateRowDataToAccumulator({
          accumulator,
          item,
          rowKey: parentRowKey,
          rowId: parentRowId,
          parentId: null,
          parentColumnKey,
          childColumnKey,
          displayName:
            PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP[
              item.row[parentRowKey]?.displayName?.toLowerCase() ?? ''
            ] ?? item.row[parentRowKey]?.displayName,
        });

        // add nested row data
        accumulator = this.addAggregateRowDataToAccumulator({
          accumulator,
          item,
          rowKey: childRowKey,
          rowId: `${item.row[parentRowKey]?.id}-${buildCriteriaIdString(
            item.row.criteria,
          )}`,
          parentId: parentRowId,
          parentColumnKey,
          childColumnKey,
          displayName: item.row.criteria.criteriaName as string,
        });

        return accumulator;
      },
      {},
    );

    // for each row - calculate column's adherence percentage and sort columns by id
    const rows = Object.values(aggregatedRows).map((singleRow) => {
      const { columns: rawDataColumns, ...restOfSingleRow } = singleRow;
      const columns = this.addAdherencePercentageToAllColumnsAndSort(
        Object.entries(rawDataColumns).map(
          ([id, value]: [string, [number, number]]) => ({ id, value }),
        ),
        groupBy,
        impressionsOrCreativeCountByItemId,
        impressionsOrCreativeCountKey,
        singleRow.id,
      );

      return {
        ...restOfSingleRow,
        columns,
      };
    });

    const columns = response.data.reduce(
      (
        accumulator: Record<string, any>,
        item: AdherenceReportV3DataItemDto,
      ) => {
        const parentId = item.column[parentColumnKey];
        // parent column
        accumulator = this.addAggregateColumnToAccumulator({
          accumulator,
          value: parentId,
          dataValueArray: item.data.value,
          columnDisplayNameMap,
          parentId: null,
          identifiers: {
            // e.g 'brandId': item.column['brandId']
            [`${parentColumnKey}Id`]: item.column[`${parentColumnKey}Id`],
          },
        });

        // nested column if more than one group by column in request
        if (groupBy.columns.length === 2) {
          accumulator = this.addAggregateColumnToAccumulator({
            accumulator,
            value: item.column[childColumnKey],
            dataValueArray: item.data.value,
            columnDisplayNameMap,
            parentId,
            identifiers: {
              // e.g 'brandId': item.column['brandId'], 'workspaceId': item.column['workspaceId']
              [`${parentColumnKey}Id`]: item.column[`${parentColumnKey}Id`],
              [`${childColumnKey}Id`]: item.column[`${childColumnKey}Id`],
            },
          });
        }

        return accumulator;
      },
      {},
    );

    // calculate adherence percentage and sort columns by id
    const finalColumns = this.addAdherencePercentageToAllColumnsAndSort(
      Object.values(columns),
      groupBy,
      impressionsOrCreativeCountByItemId,
      impressionsOrCreativeCountKey,
    );

    return {
      columns: finalColumns as AdherenceColumn[],
      rows,
    };
  }

  /**
   * Adds row data to the accumulator, including the rows columns for data item. Only add child column if applicable (i.e > 1 group by column in request)
   * If the rowId already exists in accumulator (parent row), sum up the denominator and numerator values
   */
  private addAggregateRowDataToAccumulator(
    params: AdherenceAggregateRowDataToAccumulatorParams,
  ) {
    const {
      accumulator,
      item,
      rowKey,
      rowId,
      parentId,
      parentColumnKey,
      childColumnKey,
      displayName,
    } = params;
    const parentColumnId = item.column[parentColumnKey];
    const childColumnId = `${parentColumnId}-${item.column[childColumnKey]}`;

    if (!accumulator[rowId]) {
      accumulator[rowId] = {
        ...item.row[rowKey],
        id: rowId,
        displayName,
        parentId,
        columns: {
          [parentColumnId]: item.data.value,
          ...(childColumnKey ? { [childColumnId]: item.data.value } : {}),
        },
      };
    } else {
      accumulator[rowId].columns = {
        ...accumulator[rowId].columns,
        [parentColumnId]: this.sumAdherenceValuesArrays(
          accumulator[rowId].columns[parentColumnId] ?? [0, 0],
          item.data.value,
        ),
        ...(childColumnKey
          ? {
              [childColumnId]: this.sumAdherenceValuesArrays(
                accumulator[rowId].columns[childColumnId] ?? [0, 0],
                item.data.value,
              ),
            }
          : {}),
      };
    }

    return accumulator;
  }

  /**
   * Adds column data to the accumulator.
   * If the columnId already exists in accumulator (parent column), sum up the denominator and numerator values
   */
  private addAggregateColumnToAccumulator(
    params: AdherenceAggregateColumnDataToAccumulatorParams,
  ) {
    const {
      accumulator,
      value,
      parentId,
      dataValueArray,
      columnDisplayNameMap,
      identifiers,
    } = params;
    const id = parentId ? `${parentId}-${value}` : value;
    if (!accumulator[id]) {
      accumulator[id] = {
        id,
        displayName: columnDisplayNameMap.get(value) ?? value,
        parentId,
        value: dataValueArray,
        identifiers,
      };
    } else {
      accumulator[id].value = this.sumAdherenceValuesArrays(
        accumulator[id].value,
        dataValueArray,
      );
    }

    return accumulator;
  }

  private sumAdherenceValuesArrays(
    valuesArrayOne: number[],
    valuesArrayTwo: number[],
  ) {
    if (valuesArrayOne.length !== 2 || valuesArrayTwo.length !== 2) {
      throw new Error(
        `Expects adherence percentage values to be array of two elements but got ${valuesArrayOne.length} and ${valuesArrayTwo.length} elements instead`,
      );
    }

    const [passingScoresOne, totalScoresOne] = valuesArrayOne;
    const [passingScoresTwo, totalScoresTwo] = valuesArrayTwo;

    return [
      passingScoresOne + passingScoresTwo,
      totalScoresOne + totalScoresTwo,
    ];
  }

  /**
   * Sums up all column values for the overall average column.
   * Also adds adherence percentage to all columns and sorts them by id, with overall column first.
   * Includes impressions data if applicable.
   */
  private addAdherencePercentageToAllColumnsAndSort(
    items: {
      value: [number, number];
      id: string;
      impressions?: number;
    }[],
    groupBy: CreateReportGroupByDto,
    impressionsOrCreativeCountByItemId: Record<string, number>,
    impressionsOrCreativeCountKey: 'impressions' | 'creativeCount',
    rowIdForRowColumns?: string,
  ) {
    const { calculatedItems, overall } = items.reduce(
      (
        acc: {
          calculatedItems: {
            id: string;
            adherencePercent: number | undefined;
          }[];
          overall: number[];
        },
        singleItem,
      ) => {
        const impressionItemKey = rowIdForRowColumns
          ? `${rowIdForRowColumns}-${singleItem.id}`
          : singleItem.id;

        acc.calculatedItems.push(
          this.addImpressionsAndAdherencePercentageToColumn(
            singleItem,
            impressionsOrCreativeCountByItemId,
            impressionItemKey,
            impressionsOrCreativeCountKey,
          ),
        );
        acc.overall = this.sumAdherenceValuesArrays(
          acc.overall,
          singleItem.value,
        );

        return acc;
      },
      { calculatedItems: [], overall: [0, 0] },
    );

    const overallColumnId = this.getOverallColumnId(groupBy);
    const overallColumnImpressionItemKey = rowIdForRowColumns
      ? `${rowIdForRowColumns}-${overallColumnId}`
      : overallColumnId;

    // sort columns alphabetically by id, with overall column first
    calculatedItems.sort((a, b) => a.id.localeCompare(b.id));
    calculatedItems.unshift(
      this.addImpressionsAndAdherencePercentageToColumn(
        {
          id: overallColumnId,
          value: overall,
          // don't add extra info for response.rows[i].columns item, only for response.columns
          ...(rowIdForRowColumns
            ? {}
            : {
                displayName: OVERALL_COLUMN_HEADER_DISPLAY_NAME,
                parentId: null,
              }),
        },
        impressionsOrCreativeCountByItemId,
        overallColumnImpressionItemKey,
        impressionsOrCreativeCountKey,
      ),
    );

    return calculatedItems;
  }

  private addImpressionsAndAdherencePercentageToColumn(
    singleItem: {
      value: number[];
      id: string;
      parentId?: string | null;
      displayName?: string;
      impressions?: number;
    },
    impressionsOrCreativeCountByItemId: Record<string, number>,
    itemId: string,
    impressionsOrCreativeCountKey: 'impressions' | 'creativeCount',
  ) {
    const {
      value: [passingScores, totalScores],
      ...restOfSingleItem
    } = singleItem;
    return {
      ...restOfSingleItem,
      [impressionsOrCreativeCountKey]:
        impressionsOrCreativeCountByItemId[itemId],
      adherencePercent: totalScores
        ? Number(
            ((passingScores * 100) / totalScores).toFixed(
              ADHERENCE_PERCENTAGE_DECIMAL_PLACES,
            ),
          )
        : undefined,
    };
  }

  /**
   * Returns unique impression count by item id. Only needed for parent rows and column headers.
   * Equivalent of adherence numerator (i.e. data[0]) for nested rows
   */
  private getImpressionsOrCreativeCountByItemId(
    creativeOrImpressionCountData: CreativeOrImpressionCountDataDto[],
    groupBy: CreateReportGroupByDto,
  ): Record<string, number> {
    return creativeOrImpressionCountData.reduce(
      (acc: Record<string, number>, curr: CreativeOrImpressionCountDataDto) => {
        const key = this.getImpressionOrCreativeCountItemId(groupBy, curr);
        acc[key] = curr.data.value;

        return acc;
      },
      {},
    );
  }

  /**
   * Generates a unique ID for the impression item based on the groupBy criteria.
   * @returns A unique impression item ID to match the id of the column/row's column in the report.
   * @example 'FACEBOOK-usa-Workspace 9' where groupBy - {columns: ['market', 'workspace'], rows: ['channel', 'criteria']}
   */
  private getImpressionOrCreativeCountItemId(
    groupBy: CreateReportGroupByDto,
    creativeOrImpressionCountItem: CreativeOrImpressionCountDataDto,
  ) {
    const [parentColKey, childColKey] =
      groupBy.columns as AdherenceReportColumnKey[];
    const parentRowKey = groupBy.rows[0] as AdherenceReportParentRowKey;
    const { column, row } = creativeOrImpressionCountItem;

    const columnId = column[childColKey]
      ? `${column[parentColKey]}-${column[childColKey]}`
      : `${column[parentColKey]}`;

    const rowId = row[parentRowKey] ? `${row[parentRowKey]?.id}-` : '';
    return `${rowId}${columnId}`;
  }

  private getOverallColumnId(groupBy: CreateReportGroupByDto): string {
    return Array(groupBy.columns.length)
      .fill(OVERALL_COLUMN_HEADER_DISPLAY_NAME)
      .join('-');
  }

  getAdherenceReportCriteriaAggregationColumns(
    createReportDto: CreateReportDto,
    datapoints: ImpressionAdherenceReportDataItem[],
  ) {
    const batchTypeFilter = createReportDto.filters.find(
      (filter) => filter.fieldName === 'batchType',
    );
    if (batchTypeFilter?.value !== '*') {
      return [];
    }

    const criteriaGroupAggregationColumns = datapoints.reduce(
      (acc, datapoint) => {
        const { criteria } = datapoint.column;
        const criteriaColumnGroupId = md5(JSON.stringify(criteria));

        if (acc[criteriaColumnGroupId]) {
          return acc;
        }

        return {
          ...acc,
          [criteriaColumnGroupId]: {
            group: criteriaColumnGroupId,
            name: 'Total',
            type: 'sum',
            matchers: { criteria },
          },
        };
      },
      {} as Record<string, AggregationColumnDto>,
    );

    return Object.values(criteriaGroupAggregationColumns);
  }

  private getCsvStreamFromImpressionAdherenceData(
    data: ImpressionAdherenceReportDataItem[],
    impressionCountData: ImpressionAdherenceReportTotalItem[],
  ) {
    const csvReadableStream = new Readable({
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      read() {},
    });

    const csvData = this.transformImpressionDataToCSVFormat(
      data,
      impressionCountData,
    );

    fastcsv
      .write(csvData, {
        headers: true,
        writeHeaders: true,
      })
      .on('data', (chunk) => csvReadableStream.push(chunk))
      .on('end', () => csvReadableStream.push(null));

    return csvReadableStream;
  }

  /**
   * Generate the adherence report and return it as a CSV.
   * @param createReportDto - the structure of the report
   * @param withAnalyticsAdvancedFiltersSupport - whether to use the new endpoint that supports analytics advanced filters
   */
  async generateAdherenceReportCsv(
    createReportDto: CreateReportDto,
    withAnalyticsAdvancedFiltersSupport: boolean,
  ) {
    const { data } = await this.generateAdherenceReport(
      createReportDto,
      withAnalyticsAdvancedFiltersSupport,
    );
    return this.getCsvStreamFromImpressionAdherenceData(data, []);
  }

  /**
   * Generate the impression-adherence report and return it as a CSV.
   * @param createReportDto - the structure of the report
   */
  async generateImpressionAdherenceReportCsv(createReportDto: CreateReportDto) {
    const { data, impressionCountData } = await firstValueFrom(
      this.getImpressionAdherenceReportDataHelper(createReportDto),
    );
    return this.getCsvStreamFromImpressionAdherenceData(
      data,
      impressionCountData,
    );
  }

  async generateAdherenceReportMetadataCsv(
    createReportDto: CreateReportDto,
  ): Promise<string> {
    const { entityTypeFilter, filtersWithoutEntityType } =
      this.extractEntityTypeFilter(createReportDto.filters);

    createReportDto.filters = filtersWithoutEntityType;
    const response =
      await this.reportService.getAdherenceReportRowMetadataAsPromise(
        entityTypeFilter?.value.toString() ?? EntityType.AD_ASSET,
        createReportDto,
      );
    if (!response.result.length) {
      throw new Error(
        'No data found for the given report settings and row selection',
      );
    }

    return await this.adherenceReportMetadataCsvGenerator.generateCsv(
      response.result,
    );
  }

  async getAccountsEarliestImpressionDate(
    earliestImpressionDateRequest: EarliestImpressionDateRequestDto,
  ): Promise<GetEarliestImpressionDate200Response> {
    return await this.reportService.getEarliestImpressionDateAsPromise(
      earliestImpressionDateRequest,
    );
  }

  async getInFlightReportGroupAggregate(
    inflightAggregateRequestDto: InflightAggregateRequestDto,
    organizationId: string,
  ): Promise<Observable<GetPreflightChannelAggregateScores200Response>> {
    return this.reportService
      .getGroupedAggregateScores(organizationId, inflightAggregateRequestDto)
      .pipe(
        map((response) => {
          const isGroupedByChannel =
            !inflightAggregateRequestDto.groupBy ||
            inflightAggregateRequestDto.groupBy ===
              InflightAggregateRequestDto.GroupByEnum.Channel;
          if (isGroupedByChannel) {
            return {
              ...response.data,
              result: {
                ...response.data.result,
                groupAggregates: response.data.result.groupAggregates.map(
                  (channelAgg) => ({
                    ...channelAgg,
                    displayName:
                      PLATFORM_ID_TO_CHANNEL_DISPLAY_NAME_MAP[
                        channelAgg.displayName.toLowerCase()
                      ],
                  }),
                ),
              },
            };
          }

          return response.data;
        }),
      );
  }

  async getInFlightReportCriteriaAggregate(
    inflightAggregateRequestDto: InflightAggregateRequestDto,
    paginationOptions: PaginationOptions,
    organizationId: string,
  ): Promise<GetCriteriaAggregateScoresV2200Response> {
    return await this.reportService.getCriteriaAggregateScoresV2AsPromise(
      organizationId,
      inflightAggregateRequestDto,
      paginationOptions.offset,
      paginationOptions.perPage,
    );
  }

  async getInflightReportMediaList(
    organizationId: string,
    inflightAggregateRequestDto: InflightAggregateRequestDto,
    paginationOptions: PaginationOptions,
    inflightReportSortOptions: InflightReportSortOptions,
    useDetailedScoreStatus: boolean,
  ): Promise<PaginatedResultArray<MediaObjectAndScoresDto>> {
    const mediaScoresResponse: GetMediaScoresForInflightReport200Response =
      await this.reportService.getMediaScoresForInflightReportAsPromise(
        organizationId,
        inflightReportSortOptions.sortOrder,
        inflightReportSortOptions.sortBy === 'passedPercentByChannel'
          ? 'passedPercentByGroup'
          : inflightReportSortOptions.sortBy,
        inflightReportSortOptions.sortByGroupId ||
          inflightReportSortOptions.sortByChannel,
        inflightAggregateRequestDto,
        paginationOptions.offset,
        paginationOptions.perPage,
      );

    const resultWithMediaObject: MediaObjectAndScoresDto[] = await Promise.all(
      mediaScoresResponse.result.map(async (mediaScore) => {
        const { result: mediaObject } =
          await this.mediaServiceSDK.getMediaByIdAsPromise(mediaScore.id);
        const scores = mediaScore.scores.map((score: CriteriaStatusDto) => ({
          ...score,
          status: useDetailedScoreStatus
            ? score.detailedStatus || score.status
            : score.status,
          detailedStatus: undefined, //Hide detailedStatus in the response
        }));
        return {
          ...mediaScore,
          scores,
          mediaObject,
        };
      }),
    );

    return new PaginatedResultArray(
      resultWithMediaObject,
      mediaScoresResponse.pagination?.totalSize ?? 0,
    );
  }

  async generateInflightReportCSV(
    userId: number,
    organizationId: string,
    inflightAggregateRequestDto: InflightAggregateRequestDto,
    inflightReportSortOptions: InflightReportSortOptions,
  ): Promise<string> {
    const criteriaListQueryParams =
      this.inflightReportCsvService.getCriteriaListQueryParamsForInflightReportCsv(
        inflightAggregateRequestDto,
      );

    const [mediaScoresResponse, channelAggregateScores, criteria] =
      await Promise.all([
        this.getInflightReportMediaList(
          organizationId,
          inflightAggregateRequestDto,
          {
            perPage: Number.MAX_SAFE_INTEGER,
            offset: 0,
          },
          inflightReportSortOptions,
          true,
        ),
        this.reportService.getChannelAggregateScoresAsPromise(
          inflightAggregateRequestDto,
        ),
        this.criteriaService.getWorkspaceCriteriaList(
          userId,
          // just one workspace is being supported for now
          inflightAggregateRequestDto.workspaceIds[0],
          { perPage: Number.MAX_SAFE_INTEGER, offset: 0 },
          criteriaListQueryParams,
        ),
      ]);

    return this.inflightReportCsvService.getInflightReportCSV(
      mediaScoresResponse,
      channelAggregateScores,
      criteria,
      inflightAggregateRequestDto,
    );
  }

  private transformImpressionDataToCSVFormat(
    data: ImpressionAdherenceReportDataItem[],
    impressionCountData: ImpressionAdherenceReportTotalItem[],
  ) {
    type LookupType = { [key: string]: string };
    const csvData: { [key: string]: string }[] = [];
    const totalAdherence: { [key: string]: [number, number] } = {};
    const lookup: LookupType = {}; // Used to track position of market-workspace in csvData

    const allCriteriaNames: Set<string> = new Set(); // Track all unique criteria encountered

    data.forEach((item: ImpressionAdherenceReportDataItem) => {
      const key = `${item.row.market}-${item.row.workspace}`;

      // If this market-workspace combination is not in lookup, add it
      if (!lookup[key]) {
        const newRecord: { [key: string]: string } = {};
        newRecord['Market'] = item.row.market;
        newRecord['Workspace'] = item.row.workspace;
        csvData.push(newRecord);
        totalAdherence[key] = [0, 0];
        lookup[key] = String(csvData.length - 1);
      }

      // dynamically update the criteria column for the specific market-workspace
      const criteriaName =
        ScoringAdherenceReportCsvGenerator.getCriteriaDisplayName({
          criteriaIsOptional: item.column.criteria.isOptional,
          criteriaIdentifier: item.column.criteria.criteriaIdentifier,
          criteriaParameters: item.column.criteria.criteriaParameters,
          criteriaPlatform: item.column.criteria.criteriaPlatform || '',
          criteriaName: item.column.criteria.criteriaName || '',
        });

      csvData[Number(lookup[key])][
        criteriaName
      ] = `= ${item.data.value[0]}/${item.data.value[1]}`;

      if (!item.column.criteria.isOptional) {
        let [totalAdheredImpressions, totalImpressions] = totalAdherence[key];
        totalAdheredImpressions = totalAdheredImpressions + item.data.value[0];
        totalImpressions = totalImpressions + item.data.value[1];
        totalAdherence[key] = [totalAdheredImpressions, totalImpressions];
      }
      // Add the criteria to the set of all encountered criteria
      allCriteriaNames.add(criteriaName);
    });

    Object.keys(totalAdherence).forEach((key) => {
      const criteriaName = 'Total Adherence';
      const adherencePercentage =
        totalAdherence[key][1] === 0
          ? 0
          : Math.round((totalAdherence[key][0] / totalAdherence[key][1]) * 100);
      csvData[Number(lookup[key])][criteriaName] = `${adherencePercentage}%`;
    });

    impressionCountData.forEach((item: ImpressionAdherenceReportTotalItem) => {
      const key = `${item.row.market}-${item.row.workspace}`;

      // dynamically update the criteria column for the specific market-workspace
      const criteriaName = 'Total Impressions';

      // If this market-workspace combination is not in lookup, skip it
      if (!lookup[key]) {
        //This is a bit of a hack, it's not clear to me why we have items with no data in the response here
        //but we only want to show rows with data for criteria
        this.logger.error(
          `Unexpected market and workspace for ${key}: ${item.row.market}, ${item.row.workspace}`,
        );
        return;
      }

      csvData[Number(lookup[key])][criteriaName] = `= ${item.data.value}`;

      // Add the criteria to the set of all encountered criteria
      allCriteriaNames.add(criteriaName);
    });

    // Now, ensure that every record has every criteria column
    const allCriteriaNamesArr = Array.from(allCriteriaNames); // Convert to array for easier access
    csvData.forEach((record) => {
      allCriteriaNamesArr.forEach((criteria) => {
        if (!record[criteria]) {
          record[criteria] = ''; // Set default empty string value for missing criteria
        }
      });
    });

    const columnOrder = [
      ...(impressionCountData.length ? ['Total Impressions'] : []), // Include this column only if impressions data is present
      'Total Adherence',
      ...allCriteriaNamesArr.filter(
        (criteria) =>
          criteria !== 'Total Impressions' && criteria !== 'Total Adherence',
      ),
    ];

    // Reorder the properties of each record, ensuring "Market" and "Workspace" come first
    const sortedCsvData = csvData.map((record) => {
      const sortedRecord: { [key: string]: string } = {
        Market: record.Market,
        Workspace: record.Workspace,
      };
      columnOrder.forEach((criteria) => {
        sortedRecord[criteria] = record[criteria];
      });
      return sortedRecord;
    });
    return sortedCsvData;
  }

  /**
   * Generate the adoption report and return it as a CSV.
   * @param createReportDto - the structure of the report
   */
  async generateAdoptionReportCsv(createReportDto: CreateReportDto) {
    const { data } = await firstValueFrom(
      this.getAdoptionReportData(createReportDto),
    );
    return new ScoringAdoptionReportCsvGenerator().generateCsv(
      createReportDto,
      data,
    );
  }

  /**
   * Get the report options for the given organization / user. This is mostly a placeholder to support continued
   * progress on the front end while the platform team makes the necessary changes to the organization service.
   * @param relatedWorkspaces - a collection of related workspaces that the user has access to.
   */
  async getReportOptions(
    relatedWorkspaces: ReadWorkspaceSearchDto[],
  ): Promise<ScoringReportOptionsDto> {
    const reportOptions = new ScoringReportOptionsDto();
    const organizationId: string = relatedWorkspaces[0]?.organizationId;

    if (!organizationId) {
      return reportOptions;
    }

    // Mapping workspaces to selection options
    reportOptions.workspaces = relatedWorkspaces.map((workspace) => ({
      label: workspace.name,
      value: workspace.id.toString(),
    }));

    // Fetching and mapping brands
    const brands = await this.brandService.getBrandsAsPromise(
      organizationId,
      undefined,
      undefined,
      1000,
    );
    reportOptions.brands = this.mergeDataLabelsAndValues(
      brands.result.map((brand) => ({
        label: brand.name,
        value: brand.id,
      })),
    );

    // Fetching and mapping markets
    const allMarkets = await this.marketRepository.find();
    reportOptions.markets = this.mergeDataLabelsAndValues(
      allMarkets.map((market) => ({
        label: market.name,
        value: market.isoCode,
      })),
    );

    // Setting up predefined creative types
    reportOptions.creativeType = [
      { label: 'Video', value: ApplicabilityMediaTypesEnum.VIDEO },
      { label: 'Image', value: ApplicabilityMediaTypesEnum.IMAGE },
      { label: 'Display', value: ApplicabilityMediaTypesEnum.HTML },
      { label: 'GIF', value: ApplicabilityMediaTypesEnum.ANIMATED_IMAGE },
    ];

    // Fetching criteria sets
    reportOptions.criteriaSets = await this.getCriteriaSetsForReportOptions(
      organizationId,
      relatedWorkspaces.map((workspace) => workspace.id),
    );

    return reportOptions;
  }

  private mergeDataLabelsAndValues(options: Option[]) {
    const includedOptions: Record<string, string> = {};
    const optionList = options.sort((a, b) => a.label.localeCompare(b.label));
    const mergedOptions = optionList.filter((option) => {
      if (option.value in includedOptions) {
        return false;
      }
      includedOptions[option.value] = option.value;
      return true;
    });
    if (mergedOptions.length > 0) {
      mergedOptions.push({
        label: NOT_SPECIFIED_LABEL,
        value: NOT_SPECIFIED_VALUE,
      });
    }
    return mergedOptions;
  }

  public async getMarkets(): Promise<Option[]> {
    const allMarkets = await this.marketRepository.find();

    return this.mergeDataLabelsAndValues(
      allMarkets.map((market) => ({
        label: market.name,
        value: market.isoCode,
      })),
    );
  }

  private async getCountryIsoCodeNameMap(): Promise<Map<string, string>> {
    const countryMap = new Map<string, string>();
    const countries: Market[] = await this.marketRepository.find();
    countries.forEach((market: Market) => {
      countryMap.set(market.isoCode.toLowerCase(), market.name);
    });
    countryMap.set(NOT_SPECIFIED.value, NOT_SPECIFIED.displayName);

    return countryMap;
  }

  private extractEntityTypeFilter(filters: CreateReportDtoFiltersInner[]): {
    entityTypeFilter: CreateReportDtoFiltersInner | undefined;
    filtersWithoutEntityType: CreateReportDtoFiltersInner[];
  } {
    const entityTypeFilter = filters.find(
      (filter) => filter.fieldName === ENTITY_TYPE_FILTER_KEY,
    );

    const filtersWithoutEntityType = filters.filter(
      (filter) => filter.fieldName !== ENTITY_TYPE_FILTER_KEY,
    );

    return { entityTypeFilter, filtersWithoutEntityType };
  }
}
