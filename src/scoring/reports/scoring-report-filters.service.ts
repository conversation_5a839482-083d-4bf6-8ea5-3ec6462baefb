import { Injectable, Logger } from '@nestjs/common';
import {
  ReportFilterEqualsDto,
  ReportFilterOperator,
} from '../../reports/model/report-filters.dto';
import { CreateReportDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { WorkspaceService } from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class ScoringReportFiltersService {
  private readonly logger = new Logger(ScoringReportFiltersService.name);

  private static readonly ORGANIZATION_ID_FIELD = 'organizationId';

  constructor(private readonly workspaceService: WorkspaceService) {}

  /**
   * Adds an organization ID filter to a scoring report. The organization ID is determined based on the provided workspace ID.
   *
   * This filter is intended to improve query performance by utilizing clustering fields in the scoring table. It does not impact
   * the accuracy of the results at the moment.
   *
   * If any issue occurs while adding this filter (e.g., missing workspace ID or organization ID), the request will not fail.
   * Instead, a log message will be recorded, and the query will proceed without the organization ID filter, since it is only
   * needed for performance optimization and does not affect the accuracy of the returned data.
   */
  async addOrganizationIdFilter(
    createReportDto: CreateReportDto,
    workspaceId: number,
  ): Promise<void> {
    if (!workspaceId) {
      this.logger.error(
        `Workspace ID is not set. Skipping add organization id filter.`,
      );
      return;
    }
    if (!createReportDto) {
      this.logger.error(
        `CreateReportDto is not set. Skipping add organization id filter.`,
      );
      return;
    }

    const organizationId = await this.getWorkspaceOrganizationId(workspaceId);

    if (!organizationId) {
      this.logger.error(
        `Organization ID not found for workspace ID ${workspaceId}. Skipping add organization id filter.`,
      );
      return;
    }

    if (!createReportDto.filters) {
      createReportDto.filters = [];
    }

    const isOrganizationIdFilterExists = createReportDto.filters.some(
      (filter) =>
        filter.fieldName === ScoringReportFiltersService.ORGANIZATION_ID_FIELD,
    );

    if (!isOrganizationIdFilterExists) {
      const organizationIdFilter =
        this.createOrganizationIdFilter(organizationId);
      createReportDto.filters.push(organizationIdFilter);
    }
  }

  private createOrganizationIdFilter(
    organizationId: string,
  ): ReportFilterEqualsDto {
    const filter = new ReportFilterEqualsDto();
    filter.fieldName = ScoringReportFiltersService.ORGANIZATION_ID_FIELD;
    filter.operator = ReportFilterOperator.Equals;
    filter.value = organizationId;
    return filter;
  }

  private async getWorkspaceOrganizationId(
    workspaceId: number,
  ): Promise<string | null> {
    const { result: workspace } = await this.workspaceService.findOneAsPromise(
      workspaceId,
    );
    return workspace?.organizationId ?? null;
  }
}
