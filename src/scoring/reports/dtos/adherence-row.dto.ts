export type ReportDataItemMatchers = Record<string, any>;

export interface AdherenceRow {
  id: string;
  parentId?: string;
  displayName: string;
  columns: Array<AdherenceColumn>;
}

export interface AdherenceColumn {
  id: string;
  displayName: string;
  criteriaIdentifier: string;
  criteriaPlatform: string;
  criteriaParameters: string;
  criteriaIsOptional: boolean;
  criteriaIsBestPractice: boolean;

  inFlight: number | null;
  preFlight: number | null;

  inFlightPassed: number;
  inFlightTotal: number;
  preFlightPassed: number;
  preFlightTotal: number;
}

export interface AdherenceColumnTitle {
  id: string;
  displayName: string;
  criteriaIdentifier: string;
  criteriaPlatform: string;
  criteriaParameters: string;
  criteriaIsOptional: boolean;
  criteriaIsBestPractice: boolean;

  inFlight: number | null;
  preFlight: number | null;
  totalNorms: number | null;

  totalNormsPassed: number;
  totalNormsTotal: number;

  inFlightPassed: number;
  inFlightTotal: number;
  preFlightPassed: number;
  preFlightTotal: number;
}
