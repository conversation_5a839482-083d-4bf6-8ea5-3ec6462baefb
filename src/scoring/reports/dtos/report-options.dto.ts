import { SortOrder } from '../../../reports/model/sort-order';

export interface Option {
  label: string;
  value: string;
}

export class UserInfoDto {
  userId: number;
  workspaceId: number;
}

/**
 * Options used to populate the scoring report filter dropdowns in ACS.
 */
export class ScoringReportOptionsDto {
  markets: Option[];
  brands: Option[];
  workspaces: Option[];
  criteriaSets: Option[];
  creativeType: Option[];
  channels: string[];
}

export interface InflightReportSortOptions {
  sortOrder: SortOrder;
  sortBy:
    | 'displayName'
    | 'fileType'
    | 'passedPercent'
    | 'passedPercentByChannel'
    | 'passedPercentByGroup';
  sortByChannel:
    | 'ALL_PLATFORMS'
    | 'TWITTER'
    | 'FACEBOOK'
    | 'TIKTOK'
    | 'PINTEREST'
    | 'LINKEDIN'
    | 'DV360'
    | 'ADWORDS'
    | 'SNAPCHAT'
    | 'AMAZON'
    | 'REDDIT';
  sortByGroupId?: string;
}
