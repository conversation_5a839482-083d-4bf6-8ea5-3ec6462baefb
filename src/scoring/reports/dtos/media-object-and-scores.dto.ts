import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsObject } from 'class-validator';
import { ReadMediaDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readMediaDto';
import { MediaWithScoresDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { CriteriaStatusDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/criteriaStatusDto';

/**
 * Encapsulates media object with scores.
 * Note: media object will be added by the BFF, querying from the organization service so the response
 * includes valid stream urls for preview on the FE.
 */
export class MediaObjectAndScoresDto {
  @ApiProperty({ description: 'The media object id', example: 123456 })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'The media recognition status',
    example: 'SUCCEEDED',
  })
  @IsEnum(MediaWithScoresDto.MediaRecognitionStatusEnum)
  mediaRecognitionStatus: MediaWithScoresDto.MediaRecognitionStatusEnum;

  @ApiProperty({
    description:
      'Percentage of criteria the media was scored on that it passed',
    example: 33,
  })
  @IsNumber()
  passedPercent: number;

  @ApiProperty({
    description:
      'Percentage of criteria the media was scored on that it passed by channel',
    example: { FACEBOOK: 33, SNAPCHAT: 25 },
  })
  @IsObject()
  passedPercentByChannel: object;

  @ApiProperty({
    description: 'The scores for each criteria the media was scored on',
    example: [{ criteriaId: 101, status: 'PASS' }],
  })
  scores: Array<
    Omit<CriteriaStatusDto, 'status'> & {
      status: CriteriaStatusDto.StatusEnum | string;
    }
  >;

  @ApiProperty({
    description: 'The media object',
  })
  mediaObject: ReadMediaDto;
}
