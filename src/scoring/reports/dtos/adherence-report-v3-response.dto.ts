import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>y, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AdherenceRowColumn {
  @ApiProperty({
    description: 'The unique identifier for the column',
    example: 'Main Workspace',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The percentage of media adherence for the row and column',
    example: 98.5882,
  })
  @IsNumber()
  adherencePercent?: number | undefined;

  @ApiProperty({
    description: 'The number of impressions for the row and column',
    example: 1000,
  })
  @IsNumber()
  impressions?: number | undefined;

  @ApiProperty({
    description: 'The norms percentage lift',
    example: -2.45,
  })
  @IsNumber()
  normsPercentLift?: number | undefined;
}

export class AdherenceColumnIdentifiers {
  @ApiProperty({
    description: 'The workspace id for the column',
    example: 1212,
  })
  @IsNumber()
  workspaceId?: number;

  @ApiProperty({
    description: 'The brand id for the column',
    example: 'q002-qr2uu28y94y024i-42i-u',
  })
  @IsString()
  brandId?: string;

  @ApiProperty({
    description: 'The market iso code for the column',
    example: 'fra',
  })
  @IsString()
  marketId?: string;
}

export class AdherenceColumn {
  @ApiProperty({
    description: 'The unique identifier for the column',
    example: 'Known Brand-Main Workspace',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The actual identifiers for the column entities',
    example: '{"workspaceId": 1212,"brandId": "q002-qr2uu28y94y024i-42i-u"}',
    type: AdherenceColumnIdentifiers,
  })
  identifiers: AdherenceColumnIdentifiers;

  @ApiProperty({
    description: 'The display name for the column',
    example: 'Main Workspace',
  })
  @IsString()
  displayName: string;

  @ApiProperty({
    description: 'The id of parent column this is nested under',
    example: 'Known Brand',
  })
  @IsString()
  parentId: string | null;

  @ApiProperty({
    description: 'The percentage of media adherence for the column',
    example: 98.5882,
  })
  @IsNumber()
  adherencePercent?: number | undefined;

  @ApiProperty({
    description: 'The number of impressions for the column',
    example: 1000,
  })
  @IsNumber()
  impressions?: number | undefined;
}

export class AdherenceRow {
  @ApiProperty({
    description: 'The unique identifier for the adherence row',
    example: 'LINKEDIN:HUMAN_PRESENCE_ANYTIME:{}:OPTIONAL:BEST_PRACTICE',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The display name for the adherence row',
    example: 'LinkedIn Human Presence Anytime',
  })
  @IsString()
  displayName: string;

  @ApiProperty({
    description: 'The unique identifier for the parent of the adherence row',
    example: '98UH42-HY9H2HRBRU-H2H2JH4J',
  })
  @IsString()
  parentId: string | null;

  @ApiProperty({
    description: 'The color associated with the adherence parent row',
    example: '#FF5733',
  })
  @IsString()
  color: string | null;

  @ApiProperty({
    description: 'The unique identifier for the criteria',
    example: 'HUMAN_PRESENCE_ANYTIME',
  })
  @IsString()
  criteriaIdentifier?: string;

  @ApiProperty({
    description: 'The name of the criteria',
    example: '{"minPercent":50}',
  })
  @IsString()
  criteriaParameters?: string | null;

  @ApiProperty({
    description: 'The platform associated with the criteria',
    example: 'LINKEDIN',
  })
  @IsString()
  criteriaPlatform?: string;

  @ApiProperty({
    description: 'If the criteria is optional',
    example: true,
  })
  @IsBoolean()
  isOptional?: boolean;

  @ApiProperty({
    description: 'If the criteria is a best practice',
    example: false,
  })
  @IsBoolean()
  isBestPractice?: boolean;

  @ApiProperty({
    description:
      'The unique identifier for the criteria rule used to create the criteria',
    example: '39924g7-994208y-804ry0y2',
  })
  @IsString()
  criteriaRuleId?: string | null;

  @ApiProperty({
    description: 'The URL for the custom icon associated with the criteria',
    example: 'https://example.com/icon.png',
  })
  @IsArray()
  @IsString({ each: true })
  customIconUrls?: string[];

  @ApiProperty({
    description: 'The adherence percentage for the criteria for every column',
    example: [
      { id: 'Main Workspace', adherencePercent: 89.1938, impressions: 1000 },
    ],
    type: [AdherenceRowColumn],
  })
  columns: AdherenceRowColumn[];
}

export class AdherenceReportV3ResponseDto {
  @ApiProperty({ type: [AdherenceColumn] })
  columns: AdherenceColumn[];

  @ApiProperty({ type: [AdherenceRow] })
  rows: AdherenceRow[];
}
