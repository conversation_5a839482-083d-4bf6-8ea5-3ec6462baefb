/**
 * This is temp data until the platform team provides has an API to get
 * market data for a specific workspace. It mirrors what's in the database
 * but I didn't want to add a database depenency for something temporary.
 */
export const countryDataMapByIsoCode = {
  afg: 'Afghanistan',
  ala: 'Åland Islands',
  alb: 'Albania',
  dza: 'Algeria',
  asm: 'American Samoa',
  and: 'Andorra',
  ago: 'Angola',
  aia: 'Anguilla',
  ata: 'Antarctica',
  atg: 'Antigua and Barbuda',
  arg: 'Argentina',
  arm: 'Armenia',
  abw: 'Aruba',
  aus: 'Australia',
  aut: 'Austria',
  aze: 'Azerbaijan',
  bhs: 'Bahamas',
  bhr: 'Bahrain',
  bgd: 'Bangladesh',
  brb: 'Barbados',
  blr: 'Belarus',
  bel: 'Belgium',
  blz: 'Belize',
  ben: 'Benin',
  bmu: 'Bermuda',
  btn: 'Bhutan',
  bol: 'Bolivia',
  bih: 'Bosnia and Herzegovina',
  bwa: 'Botswana',
  bvt: 'Bouvet Island',
  bra: 'Brazil',
  iot: 'British Indian Ocean Territory',
  vgb: 'British Virgin Islands',
  brn: 'Brunei Darussalam',
  bgr: 'Bulgaria',
  bfa: 'Burkina Faso',
  bdi: 'Burundi',
  khm: 'Cambodia',
  cmr: 'Cameroon',
  can: 'Canada',
  cpv: 'Cape Verde',
  cym: 'Cayman Islands',
  caf: 'Central African',
  tcd: 'Chad',
  chl: 'Chile',
  chn: 'China',
  cxr: 'Christmas Island',
  cck: 'Cocos (Keeling) Islands',
  col: 'Colombia',
  com: 'Comoros',
  cok: 'Cook Islands',
  cri: 'Costa Rica',
  civ: "Côte d'Ivoire",
  hrv: 'Croatia',
  cub: 'Cuba',
  cyp: 'Cyprus',
  cze: 'Czech Republic',
  prk: "Democratic People's Republic of Korea",
  dnk: 'Denmark',
  dji: 'Djibouti',
  dma: 'Dominica',
  dom: 'Dominican Republic',
  ecu: 'Ecuador',
  egy: 'Egypt',
  slv: 'El Salvador',
  gnq: 'Equatorial Guinea',
  eri: 'Eritrea',
  est: 'Estonia',
  eth: 'Ethiopia',
  flk: 'Falkland Islands',
  fro: 'Faroe Islands',
  fsm: 'Federated States of Micronesia',
  fji: 'Fiji',
  fin: 'Finland',
  fra: 'France',
  guf: 'French Guiana',
  pyf: 'French Polynesia',
  atf: 'French Southern Territories',
  gab: 'Gabon',
  gmb: 'Gambia',
  geo: 'Georgia',
  deu: 'Germany',
  gha: 'Ghana',
  gib: 'Gibraltar',
  grc: 'Greece',
  grl: 'Greenland',
  grd: 'Grenada',
  glp: 'Guadeloupe',
  gum: 'Guam',
  gtm: 'Guatemala',
  gin: 'Guinea',
  gnb: 'Guinea-Bissau',
  guy: 'Guyana',
  hti: 'Haiti',
  hmd: 'Heard Island and McDonald Islands',
  hnd: 'Honduras',
  hkg: 'Hong Kong',
  hun: 'Hungary',
  isl: 'Iceland',
  ind: 'India',
  idn: 'Indonesia',
  irq: 'Iraq',
  irl: 'Ireland',
  irn: 'Islamic Republic of Iran',
  imn: 'Isle of Man',
  isr: 'Israel',
  ita: 'Italy',
  jam: 'Jamaica',
  jpn: 'Japan',
  jor: 'Jordan',
  kaz: 'Kazakhstan',
  ken: 'Kenya',
  kir: 'Kiribati',
  kwt: 'Kuwait',
  kgz: 'Kyrgyzstan',
  lao: "Lao People's Democratic Republic",
  lva: 'Latvia',
  lbn: 'Lebanon',
  lso: 'Lesotho',
  lbr: 'Liberia',
  lby: 'Libyan Arab Jamahiriya',
  lie: 'Liechtenstein',
  ltu: 'Lithuania',
  lux: 'Luxembourg',
  mac: 'Macao',
  mdg: 'Madagascar',
  mwi: 'Malawi',
  mys: 'Malaysia',
  mdv: 'Maldives',
  mli: 'Mali',
  mlt: 'Malta',
  mhl: 'Marshall Islands',
  mtq: 'Martinique',
  mrt: 'Mauritania',
  mus: 'Mauritius',
  myt: 'Mayotte',
  mex: 'Mexico',
  mco: 'Monaco',
  mng: 'Mongolia',
  msr: 'Montserrat',
  mar: 'Morocco',
  moz: 'Mozambique',
  mmr: 'Myanmar',
  nam: 'Namibia',
  nru: 'Nauru',
  npl: 'Nepal',
  nld: 'Netherlands',
  ant: 'Netherlands Antilles',
  ncl: 'New Caledonia',
  nzl: 'New Zealand',
  nic: 'Nicaragua',
  ner: 'Niger',
  nga: 'Nigeria',
  niu: 'Niue',
  nfk: 'Norfolk Island',
  mnp: 'Northern Mariana Islands',
  nor: 'Norway',
  pse: 'Occupied Palestinian Territory',
  omn: 'Oman',
  pak: 'Pakistan',
  plw: 'Palau',
  pan: 'Panama',
  png: 'Papua New Guinea',
  pry: 'Paraguay',
  per: 'Peru',
  phl: 'Philippines',
  pcn: 'Pitcairn',
  pol: 'Poland',
  prt: 'Portugal',
  pri: 'Puerto Rico',
  qat: 'Qatar',
  kor: 'Republic of Korea',
  mda: 'Republic of Moldova',
  reu: 'Réunion',
  rou: 'Romania',
  rus: 'Russian Federation',
  rwa: 'Rwanda',
  blm: 'Saint Barthélemy',
  shn: 'Saint Helena',
  kna: 'Saint Kitts and Nevis',
  lca: 'Saint Lucia',
  maf: 'Saint Martin (French part)',
  spm: 'Saint Pierre and Miquelon',
  vct: 'Saint Vincent and the Grenadines',
  wsm: 'Samoa',
  smr: 'San Marino',
  stp: 'Sao Tome and Principe',
  sau: 'Saudi Arabia',
  sen: 'Senegal',
  srb: 'Serbia',
  syc: 'Seychelles',
  sle: 'Sierra Leone',
  sgp: 'Singapore',
  svk: 'Slovakia',
  svn: 'Slovenia',
  slb: 'Solomon Islands',
  som: 'Somalia',
  zaf: 'South Africa',
  sgs: 'South Georgia and the South Sandwich Islands',
  esp: 'Spain',
  lka: 'Sri Lanka',
  sdn: 'Sudan',
  sur: 'Suriname',
  sjm: 'Svalbard and Jan Mayen',
  swz: 'Swaziland',
  swe: 'Sweden',
  che: 'Switzerland',
  syr: 'Syrian Arab Republic',
  twn: 'Taiwan',
  tjk: 'Tajikistan',
  tha: 'Thailand',
  mkd: 'The former Yugoslav Republic of Macedonia',
  tls: 'Timor-Leste',
  tgo: 'Togo',
  tkl: 'Tokelau',
  ton: 'Tonga',
  tto: 'Trinidad and Tobago',
  tun: 'Tunisia',
  tur: 'Turkey',
  tkm: 'Turkmenistan',
  tca: 'Turks and Caicos Islands',
  tuv: 'Tuvalu',
  uga: 'Uganda',
  ukr: 'Ukraine',
  are: 'United Arab Emirates',
  gbr: 'United Kingdom',
  tza: 'United Republic of Tanzania',
  usa: 'United States',
  umi: 'United States Minor Outlying Islands',
  ury: 'Uruguay',
  uzb: 'Uzbekistan',
  vut: 'Vanuatu',
  ven: 'Venezuela',
  vnm: 'Vietnam',
  vir: 'Virgin Islands, US',
  wlf: 'Wallis and Futuna',
  esh: 'Western Sahara',
  yem: 'Yemen',
  zmb: 'Zambia',
  zwe: 'Zimbabwe',
};
