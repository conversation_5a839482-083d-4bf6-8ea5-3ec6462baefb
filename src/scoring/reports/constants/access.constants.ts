import { ScoringReportType } from '../../../reports/model/report';

export const ORGANIZATIONS_WITH_DIVERSITY_REPORT_ACCESS: string[] = [
  '27a7e882-43de-4bfa-8f53-3b62875b8432', // Things Are Fine, dev
  'cb90fd8c-4740-43c6-9def-98d9f8eb5a0b', // Things Are Fine, stage
  '421fb903-40bc-4e59-beb5-6b026813752c', // VidMob, prod,
  'ce480809-bafc-4acf-a7b9-693d08abacff', // Kenvue, prod
  '08710038-9d0c-4d38-a7dc-112f6404ce40', // <PERSON><PERSON> (Kellogg), prod
  '63dea39a-2f6d-4066-bb21-adae3365427d', // <PERSON>sberg, prod
  '29b01785-8094-4430-aa28-8c5c06d2a524', // MGM Resorts, prod
  '1472082c-dc0c-47b6-b9f7-fffab7809c98', // L<PERSON><PERSON><PERSON><PERSON>, prod
  '015fcd0e-3c9b-4d49-ace2-70b947bbf4f1', // Mars Petcare, prod
  '35480162-f257-45be-9ebb-0cb2f5e30996', // Ulta, prod
  'c6038b8d-8003-4875-88f8-9595817f857c', // Kroger, prod
  'd99e71bb-9e9e-4fac-9e65-dedff88b247e', // Mercado Libre, prod
  'e3c90955-babd-494d-b00c-c2d2cfe3c679', // Church & Dwight, prod
  '6288ace3-af0d-4359-86a4-b3abb9726b7a', // Coke, prod
  '7b0b7dcc-8e60-46d0-a637-bc8600ae6e9a', // P&G, prod
  '5c8184d8-6538-476d-8666-0bd9771fd1a6', // General Motors, prod
  '6dbf4285-fef7-4dde-bc11-0ab6a62d2c98', // Reckitt Benckiser, prod
  'aedaa4c1-c8c4-4648-b5f3-7eeb030b746d', // Kraft Heinz, prod
  '0b138591-3174-4d2d-b502-9543d527904f', // American Express, prod
  'a813410b-61e2-4e85-863b-0075055016d3', // Novartis, prod
];

export const ALL_SUPPORTED_REPORTS: Array<ScoringReportType> =
  Object.values(ScoringReportType);

export const ALL_SUPPORTED_REPORTS_EXCEPT_DIVERSITY: Array<ScoringReportType> =
  ALL_SUPPORTED_REPORTS.filter(
    (report) => report !== ScoringReportType.DIVERSITY,
  );
