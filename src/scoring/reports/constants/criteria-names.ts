import { isNil } from '@nestjs/common/utils/shared.utils';

const CUSTOM_AUDIO_IDENTIFIER = 'CUSTOM_AUDIO';
const CUSTOM_TEXT_IDENTIFIER = 'CUSTOM_TEXT';
const CUSTOM_COLOR_IDENTIFIER = 'CUSTOM_COLOR';

const adherenceColumnNames: Record<string, string> = {
  'ui.compliance.criteriaManagement.template.video.duration.description':
    'Video length ({minDuration} - {maxDuration}) seconds',
  'ui.compliance.criteriaManagement.template.human.presence.description':
    'Human presence, first {maxFirstHumanPresence} seconds',
  'ui.compliance.criteriaManagement.template.human.presence.anytime.description':
    'Human presence',
  'ui.compliance.criteriaManagement.template.positive.emotion.description':
    'Positive emotions, first {maxFirstPositiveEmotion} seconds',
  'ui.compliance.criteriaManagement.template.positive.emotion.anytime.description':
    'Positive emotions',
  'ui.compliance.criteriaManagement.template.max.frame.words.description':
    '<={maxWordsPerFrame} words per frame',
  'ui.compliance.criteriaManagement.template.brand.name.logo.description':
    'Brand name / logo, first {maxFirstBrandAppearance} seconds',
  'ui.compliance.criteriaManagement.template.brand.name.logo.anytime.description':
    'Brand name / logo',
  'ui.compliance.criteriaManagement.template.framed.for.mobile.description':
    'Aspect ratio {aspectRatios}',
  'ui.compliance.criteriaManagement.template.scene.change.description':
    '{maxSceneChanges} or more Scene Changes in First {lastSceneChangeTime} Seconds',
  'ui.compliance.criteriaManagement.template.product.presence.description':
    'Product in First {lastProductPresenceTime} Seconds',
  'ui.compliance.criteriaManagement.template.call.to.action.description':
    'Call to Action',
  'ui.compliance.criteriaManagement.template.jnj.sound.off.description':
    'Visible messaging',
  'ui.compliance.criteriaManagement.template.jnj.sound.on.description':
    'Audio track',
  'ui.compliance.criteriaManagement.template.jnj.product.description':
    'Product in First {lastProductPresenceTime} Seconds',
  'ui.compliance.criteriaManagement.template.jnj.highlight.description':
    'Highlight What Matters in the Entire Duration',
  'ui.compliance.criteriaManagement.template.abi.product.early.description':
    'Product Early, first {minProductPresenceTime} seconds',
  'ui.compliance.criteriaManagement.template.abi.supers.early.description':
    'Text and voice sync, first {supersPresenceTimeLimit} seconds',
  'ui.compliance.criteriaManagement.template.abi.brandLink.description':
    'Branded imagery in the first {maxFirstBrandOrProductAppearance} seconds',
  'ui.compliance.criteriaManagement.template.abi.responsibleDrink.description':
    'Messaging to Drink Responsibly',
  'ui.compliance.criteriaManagement.template.abi.frame.tightly.description':
    'Faces, branding, and/or product framed tightly in first {frameTightlyTimeLimit} seconds',
  'ui.compliance.criteriaManagement.template.lusa.brand.persist.description':
    'Indicates if the brand name or logo appears at least {minPercent}% of the time.',
  'ui.compliance.criteriaManagement.template.key.messaging.persists.description':
    '>={minPercent}% video shows text',
  'ui.compliance.criteriaManagement.template.sound.off.no.transcription.description':
    'Text and voice sync',
  'ui.compliance.criteriaManagement.template.loreal.vo.anytime.description':
    'Video has VO at any time',
  'ui.compliance.criteriaManagement.template.earlyCta.description':
    'CTA is mentioned in the first {maxFirstEarlyCTAPresence} seconds',
  'ui.compliance.criteriaManagement.template.jnj.visual.cta.description':
    'Text CTA Present Within The Last {durationBeforeEnd} Seconds',
  'ui.compliance.criteriaManagement.template.snap.celebrity.presence.description':
    'Includes famous person',
  'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.description':
    'Brand name mention in first {maxFirstAudioBrandMention} seconds',
  'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.anytime.description':
    'Brand name mention',
  'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.description':
    'Brand name mention in first {maxFirstAudioBrandMention} seconds',
  'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.anytime.description':
    'Brand name mention',
  'ui.compliance.criteriaManagement.template.abi.brandColor.description':
    'Includes specific color',
  'ui.compliance.criteriaManagement.template.snap.beauty.pc.product.description':
    'Beta: Beauty & PC Product Detected in the First {lastProductPresenceTime} seconds',
  'ui.compliance.criteriaManagement.template.snap.beauty.pc.product.anytime.description':
    'Beta: Beauty & PC Product Detected at any time',
  'ui.compliance.criteriaManagement.template.snap.food.beverage.product.description':
    'Beta: Food & Beverage Product Detected in the First {maxFirstFoodOrBeverageAppearance} seconds',
  'ui.compliance.criteriaManagement.template.snap.food.beverage.product.anytime.description':
    'Beta: Food & Beverage Product Detected at Any Time',
  'ui.compliance.criteriaManagement.template.snap.product.technology.presence.description':
    'Beta: Technology Product Detected in the First {maxFirstTechProductAppearance} Seconds',
  'ui.compliance.criteriaManagement.template.offer.message.description':
    'Promotional offer(s)',
  'ui.compliance.criteriaManagement.template.abi.retailer.logo.anytime.description':
    'Retailer logo shown anytime',
  'ui.compliance.criteriaManagement.template.abi.retailer.logo.description':
    'Retailer logo shown',
  'ui.compliance.criteriaManagement.template.abi.product.anytime.logo.description':
    'Product Present',
  'ui.compliance.criteriaManagement.template.abi.pace.quickly.description':
    'Pace quickly',
  'ui.compliance.criteriaManagement.template.abi.dynamic.start.description':
    'Dynamic Start, first {maxMotionPresenceTime} seconds',
  'ui.compliance.criteriaManagement.template.custom.audio.description':
    'Audio (specific)',
  'ui.compliance.criteriaManagement.template.custom.text.description':
    'Text (specific)',
  'ui.compliance.criteriaManagement.template.custom.color.description':
    'Color(s) (specific)',
  'ui.compliance.criteriaManagement.template.custom_params.dynamic.description':
    'Is Custom option',

  'ui.compliance.criteriaManagement.names.custom.text.specific.benefits':
    'Product benefit(s)',
  'ui.compliance.criteriaManagement.names.custom.text.calls.to.action':
    'CTA(s)',
  'ui.compliance.criteriaManagement.names.custom.text.written.disclaimers':
    'Disclaimer(s)',
  'ui.compliance.criteriaManagement.names.custom.text.legal.information':
    'Legal information',
  'ui.compliance.criteriaManagement.names.custom.text.terms.conditions':
    'Terms and conditions',
  'ui.compliance.criteriaManagement.names.custom.text.product.claims':
    'Product claim(s)',
  'ui.compliance.criteriaManagement.names.custom.text.product.features':
    'Product feature(s)',
  'ui.compliance.criteriaManagement.names.custom.text.promotional.offers':
    'Promotional offer(s)',
  'ui.compliance.criteriaManagement.names.custom.text.written.taglines':
    'Tagline(s)',

  'ui.compliance.criteriaManagement.names.custom.audio.specific.benefits':
    'Product benefits(s)',
  'ui.compliance.criteriaManagement.names.custom.audio.calls.to.action':
    'CTA(s)',
  'ui.compliance.criteriaManagement.names.custom.audio.disclaimers':
    'Disclaimer(s)',
  'ui.compliance.criteriaManagement.names.custom.audio.legal.information':
    'Legal information',
  'ui.compliance.criteriaManagement.names.custom.audio.terms.conditions':
    'Terms and conditions',
  'ui.compliance.criteriaManagement.names.custom.audio.product.claims':
    'Product claim(s)',
  'ui.compliance.criteriaManagement.names.custom.audio.product.features':
    'Product feature(s)',
  'ui.compliance.criteriaManagement.names.custom.audio.promotional.offers':
    'Promotional offer(s)',
  'ui.compliance.criteriaManagement.names.custom.audio.taglines': 'Tagline(s)',

  'ui.compliance.criteriaManagement.names.custom.color.brand.colors':
    'Color(s)',
};

// Template identifiers. They match identifiers in DB
const CRITERIA_TEMPLATE_IDENTIFIERS = {
  VIDEO_DURATION: 'VIDEO_DURATION',
  HUMAN_PRESENCE: 'HUMAN_PRESENCE',
  HUMAN_PRESENCE_ANYTIME: 'HUMAN_PRESENCE_ANYTIME',
  POSITIVE_EMOTION: 'POSITIVE_EMOTION',
  POSITIVE_EMOTION_ANYTIME: 'POSITIVE_EMOTION_ANYTIME',
  MAX_WORDS_PER_FRAME: 'MAX_WORDS_PER_FRAME',
  BRAND_NAME_LOGO: 'BRAND_NAME_OR_LOGO',
  BRAND_NAME_LOGO_ANYTIME: 'BRAND_NAME_OR_LOGO_ANYTIME',
  FRAMED_FOR_MOBILE: 'FRAMED_FOR_MOBILE',
  LOREAL_PRODUCT: 'LOREAL_PRODUCT',
  SCENE_CHANGE: 'SCENE_CHANGE',
  CTA_DETECTION: 'CTA_DETECTION',
  SOUND_OFF: 'SOUND_OFF',
  SOUND_ON: 'SOUND_ON',
  JNJ_PRODUCT: 'JNJ_PRODUCT',
  SNAP_PRODUCT: 'SNAP_PRODUCT',
  PERSON_WITH_PRODUCT: 'PERSON_WITH_PRODUCT',
  BRAND_NAME_OR_LOGO_PERSISTS: 'BRAND_NAME_OR_LOGO_PERSISTS',
  KEY_MESSAGING_PERSISTS: 'KEY_MESSAGING_PERSISTS',
  ABI_BRAND_LINK: 'ABI_BRAND_LINK',
  SOUND_OFF_NO_TRANSCRIPTION: 'SOUND_OFF_NO_TRANSCRIPTION',
  ABI_PRODUCT_EARLY: 'ABI_PRODUCT_EARLY',
  ABI_RESPONSIBLE_DRINK_MESSAGE: 'ABI_RESPONSIBLE_DRINK_MESSAGE',
  ABI_SUPERS_PRESENCE: 'ABI_SUPERS_PRESENCE',
  LOREAL_VOICE_OVER_ANYTIME: 'LOREAL_VOICE_OVER_ANYTIME',
  ABI_FRAME_TIGHTLY: 'ABI_FRAME_TIGHTLY',
  ABI_EARLY_CTA: 'ABI_EARLY_CTA',
  ABI_BRAND_COLOR: 'ABI_BRAND_COLOR',
  JNJ_VISUAL_CTA_PRESENCE: 'JNJ_VISUAL_CTA_PRESENCE',
  SNAP_CELEBRITY_PRESENCE: 'SNAP_CELEBRITY_PRESENCE',
  JNJ_BRAND_NAME_IN_AUDIO: 'JNJ_BRAND_NAME_IN_AUDIO',
  JNJ_BRAND_NAME_IN_AUDIO_ANYTIME: 'JNJ_BRAND_NAME_IN_AUDIO_ANYTIME',
  LOREAL_BRAND_NAME_IN_AUDIO: 'LOREAL_BRAND_NAME_IN_AUDIO',
  LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME: 'LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME',
  SNAP_FOOD_BEVERAGE_PRODUCT: 'SNAP_FOOD_BEVERAGE_PRODUCT',
  SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME: 'SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME',
  SNAP_TECHNOLOGY_PRODUCT: 'SNAP_TECHNOLOGY_PRODUCT',
  OFFER_MESSAGE: 'OFFER_MESSAGE',
  ABI_RETAILER_LOGO_ANYTIME: 'ABI_RETAILER_LOGO_ANYTIME',
  ABI_PRODUCT_ANYTIME: 'ABI_PRODUCT_ANYTIME',
  ABI_PACE_QUICKLY: 'ABI_PACE_QUICKLY',
  ABI_DYNAMIC_START: 'ABI_DYNAMIC_START',
  CUSTOM_PARAMS: 'CUSTOM_PARAMS',
  CUSTOM_AUDIO: CUSTOM_AUDIO_IDENTIFIER,
  CUSTOM_TEXT: CUSTOM_TEXT_IDENTIFIER,
  CUSTOM_COLOR: CUSTOM_COLOR_IDENTIFIER,
};

const CRITERIA_DEFINITIONS = {
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.FRAMED_FOR_MOBILE]:
    'ui.compliance.criteriaManagement.template.framed.for.mobile.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.beauty.pc.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PERSON_WITH_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.highlight.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF]:
    'ui.compliance.criteriaManagement.template.jnj.sound.off.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE]:
    'ui.compliance.criteriaManagement.template.human.presence.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE_ANYTIME]:
    'ui.compliance.criteriaManagement.template.human.presence.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION]:
    'ui.compliance.criteriaManagement.template.positive.emotion.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION_ANYTIME]:
    'ui.compliance.criteriaManagement.template.positive.emotion.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.VIDEO_DURATION]:
    'ui.compliance.criteriaManagement.template.video.duration.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.MAX_WORDS_PER_FRAME]:
    'ui.compliance.criteriaManagement.template.max.frame.words.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_VISUAL_CTA_PRESENCE]:
    'ui.compliance.criteriaManagement.template.jnj.visual.cta.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_ON]:
    'ui.compliance.criteriaManagement.template.jnj.sound.on.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CTA_DETECTION]:
    'ui.compliance.criteriaManagement.template.call.to.action.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_EARLY]:
    'ui.compliance.criteriaManagement.template.abi.product.early.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RESPONSIBLE_DRINK_MESSAGE]:
    'ui.compliance.criteriaManagement.template.abi.responsibleDrink.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_SUPERS_PRESENCE]:
    'ui.compliance.criteriaManagement.template.abi.supers.early.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_OR_LOGO_PERSISTS]:
    'ui.compliance.criteriaManagement.template.lusa.brand.persist.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.KEY_MESSAGING_PERSISTS]:
    'ui.compliance.criteriaManagement.template.key.messaging.persists.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_LINK]:
    'ui.compliance.criteriaManagement.template.abi.brandLink.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF_NO_TRANSCRIPTION]:
    'ui.compliance.criteriaManagement.template.sound.off.no.transcription.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_CELEBRITY_PRESENCE]:
    'ui.compliance.criteriaManagement.template.snap.celebrity.presence.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_COLOR]:
    'ui.compliance.criteriaManagement.template.abi.brandColor.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_TECHNOLOGY_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.product.technology.presence.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.OFFER_MESSAGE]:
    'ui.compliance.criteriaManagement.template.offer.message.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RETAILER_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.retailer.logo.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.product.anytime.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PACE_QUICKLY]:
    'ui.compliance.criteriaManagement.template.abi.pace.quickly.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_DYNAMIC_START]:
    'ui.compliance.criteriaManagement.template.abi.dynamic.start.definition',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_PARAMS]:
    'ui.compliance.criteriaManagement.template.custom_params.dynamic.description',
};

const CRITERIA_TEMPLATE_DESCRIPTION = {
  [CRITERIA_TEMPLATE_IDENTIFIERS.VIDEO_DURATION]:
    'ui.compliance.criteriaManagement.template.video.duration.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE]:
    'ui.compliance.criteriaManagement.template.human.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.HUMAN_PRESENCE_ANYTIME]:
    'ui.compliance.criteriaManagement.template.human.presence.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION]:
    'ui.compliance.criteriaManagement.template.positive.emotion.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.POSITIVE_EMOTION_ANYTIME]:
    'ui.compliance.criteriaManagement.template.positive.emotion.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.MAX_WORDS_PER_FRAME]:
    'ui.compliance.criteriaManagement.template.max.frame.words.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.brand.name.logo.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.FRAMED_FOR_MOBILE]:
    'ui.compliance.criteriaManagement.template.framed.for.mobile.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SCENE_CHANGE]:
    'ui.compliance.criteriaManagement.template.scene.change.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_PRODUCT]:
    'ui.compliance.criteriaManagement.template.product.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CTA_DETECTION]:
    'ui.compliance.criteriaManagement.template.call.to.action.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF]:
    'ui.compliance.criteriaManagement.template.jnj.sound.off.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_ON]:
    'ui.compliance.criteriaManagement.template.jnj.sound.on.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.product.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.beauty.pc.product.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.PERSON_WITH_PRODUCT]:
    'ui.compliance.criteriaManagement.template.jnj.highlight.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.BRAND_NAME_OR_LOGO_PERSISTS]:
    'ui.compliance.criteriaManagement.template.lusa.brand.persist.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.KEY_MESSAGING_PERSISTS]:
    'ui.compliance.criteriaManagement.template.key.messaging.persists.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_LINK]:
    'ui.compliance.criteriaManagement.template.abi.brandLink.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SOUND_OFF_NO_TRANSCRIPTION]:
    'ui.compliance.criteriaManagement.template.sound.off.no.transcription.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_EARLY]:
    'ui.compliance.criteriaManagement.template.abi.product.early.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RESPONSIBLE_DRINK_MESSAGE]:
    'ui.compliance.criteriaManagement.template.abi.responsibleDrink.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_SUPERS_PRESENCE]:
    'ui.compliance.criteriaManagement.template.abi.supers.early.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_VOICE_OVER_ANYTIME]:
    'ui.compliance.criteriaManagement.template.loreal.vo.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_FRAME_TIGHTLY]:
    'ui.compliance.criteriaManagement.template.abi.frame.tightly.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_EARLY_CTA]:
    'ui.compliance.criteriaManagement.template.earlyCta.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_BRAND_COLOR]:
    'ui.compliance.criteriaManagement.template.abi.brandColor.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_VISUAL_CTA_PRESENCE]:
    'ui.compliance.criteriaManagement.template.jnj.visual.cta.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_CELEBRITY_PRESENCE]:
    'ui.compliance.criteriaManagement.template.snap.celebrity.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.JNJ_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.jnj.brand.audio.mention.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.LOREAL_BRAND_NAME_IN_AUDIO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.loreal.brand.audio.mention.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_FOOD_BEVERAGE_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.snap.food.beverage.product.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.SNAP_TECHNOLOGY_PRODUCT]:
    'ui.compliance.criteriaManagement.template.snap.product.technology.presence.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.OFFER_MESSAGE]:
    'ui.compliance.criteriaManagement.template.offer.message.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_RETAILER_LOGO_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.retailer.logo.anytime.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PRODUCT_ANYTIME]:
    'ui.compliance.criteriaManagement.template.abi.product.anytime.logo.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_PACE_QUICKLY]:
    'ui.compliance.criteriaManagement.template.abi.pace.quickly.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.ABI_DYNAMIC_START]:
    'ui.compliance.criteriaManagement.template.abi.dynamic.start.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_AUDIO]:
    'ui.compliance.criteriaManagement.template.custom.audio.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_TEXT]:
    'ui.compliance.criteriaManagement.template.custom.text.description',
  [CRITERIA_TEMPLATE_IDENTIFIERS.CUSTOM_COLOR]:
    'ui.compliance.criteriaManagement.template.custom.color.description',
};

const CONTENT_AUDIT_CRITERIA_DESCRIPTION: Record<string, string> = {
  DURATION: 'ui.compliance.contentAudit.criterion.duration.description',
  TALENT: 'ui.compliance.contentAudit.criterion.talent.description',
  POSITIVE_EMOTION:
    'ui.compliance.contentAudit.criterion.positive.emotion.description',
  BRAND_APPEARS:
    'ui.compliance.contentAudit.criterion.brand.appears.description',
  BRAND_APPEARS_ENOUGH:
    'ui.compliance.contentAudit.criterion.brand.appears.enough.description',
  WORDS_PER_SECOND: 'ui.compliance.contentAudit.criterion.wps.description',
  FORMAT: 'ui.compliance.contentAudit.criterion.format.description',
};

export const getDisplayNameFor = (identifier: string, parameters: any) => {
  return getCriteriaDefinitionAndDescriptionWith(identifier, parameters)
    .description;
};

const getCriteriaDefinitionAndDescriptionWith = (
  identifier: string,
  parameters: any,
  custom: Record<string, string> = {},
) => {
  let definition = '';
  let description = '';
  const formattedParameters = { ...parameters };

  Object.keys(formattedParameters).forEach((param) => {
    if (typeof formattedParameters[param] === 'object') {
      formattedParameters[param] = formattedParameters[param].join(', ');
    }
  });

  if (parameters && !isNil(CRITERIA_TEMPLATE_DESCRIPTION[identifier])) {
    description = formatMessage(
      { id: CRITERIA_TEMPLATE_DESCRIPTION[identifier] },
      { ...formattedParameters },
    );
  } else {
    description = CONTENT_AUDIT_CRITERIA_DESCRIPTION[identifier];
  }

  if (!isNil(CRITERIA_DEFINITIONS[identifier])) {
    definition = formatMessage(
      { id: CRITERIA_DEFINITIONS[identifier] },
      { ...formattedParameters },
    );
  }

  return { definition, description };
};

function formatMessage(descriptor: { id: string }, values: any = {}) {
  // Retrieve the message using the locale and message ID.
  const message = adherenceColumnNames[descriptor.id] || 'Unknown';

  // If no message is found, return an error or fallback string.
  if (!message) {
    return 'Unknown';
  }

  // Replace placeholders in the message with provided values.
  return message.replace(/{(\w+)}/g, (_: any, key: any) => {
    const value = values[key]; //value could be false or zero
    return value != null ? value : `{${key}}`; // If value is not provided, keep the placeholder.
  });
}
