import { CriteriaStatusDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/criteriaStatusDto';
import { AdherenceReportV3DataItemDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/adherenceReportV3DataItemDto';

export const JOIN_STRING = ' | ';

export const IN_FLIGHT = 'IN_FLIGHT';

export const PRE_FLIGHT = 'PRE_FLIGHT';

export const ALL_VALUE = '*';

export const NOT_SPECIFIED_LABEL = 'Not Specified';

export const NOT_SPECIFIED_VALUE = 'NOT_SPECIFIED';

export const ENTITY_TYPE_FILTER_KEY = 'entityType';

export enum EntityType {
  AD = 'AD',
  AD_ASSET = 'AD_ASSET',
}

export enum DiversityIdentifier {
  Age = 'age',
  Gender = 'gender',
  SkinTone = 'skin-tone',
}

export enum BatchType {
  InFlight = 'IN_FLIGHT',
  PreFlight = 'PRE_FLIGHT',
}

export const ADHERENCE_METADATA_RESPONSE_KEYS = {
  platform: 'platform',
  brand: 'brand',
  market: 'market',
  workspace: 'workspace',
  workspaceId: 'workspaceId',
  adAccountName: 'adAccountName',
  adAccountId: 'adAccountId',
  campaignName: 'campaignName',
  campaignId: 'campaignId',
  campaignObjective: 'campaignObjective',
  adGroupOrSetId: 'adGroupOrSetId',
  adGroupOrSetName: 'adGroupOrSetName',
  mediaId: 'mediaId',
  platformMediaId: 'platformMediaId',
  mediaName: 'mediaName',
  mediaType: 'mediaType',
  assetDuration: 'assetDuration',
  date: 'date',
  impressions: 'impressions',
  adName: 'adName',
  adId: 'adId',
  adTypes: 'adTypes',
};

export const ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT = {
  linkToDownload: 'linkToDownload',
  linkExpirationDate: 'linkExpirationDate',
  overallScore: 'overallScore',
};

export const ADHERENCE_METADATA_KEY_TO_CSV_HEADER_MAP: Record<string, string> =
  {
    [ADHERENCE_METADATA_RESPONSE_KEYS.platform]: 'Platform',
    [ADHERENCE_METADATA_RESPONSE_KEYS.brand]: 'Brand',
    [ADHERENCE_METADATA_RESPONSE_KEYS.market]: 'Market',
    [ADHERENCE_METADATA_RESPONSE_KEYS.workspace]: 'Workspace Name',
    [ADHERENCE_METADATA_RESPONSE_KEYS.workspaceId]: 'Workspace ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adAccountName]: 'Ad Account Name',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adAccountId]: 'Ad Account ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.campaignName]: 'Campaign Name',
    [ADHERENCE_METADATA_RESPONSE_KEYS.campaignId]: 'Campaign ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.campaignObjective]: 'Campaign Objective',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adGroupOrSetId]: 'Ad Group/Set ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adGroupOrSetName]: 'Ad Group/Set Name',
    [ADHERENCE_METADATA_RESPONSE_KEYS.mediaId]: 'Media ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.platformMediaId]: 'Platform Media ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.mediaName]: 'Media Name',
    [ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.linkToDownload]:
      'Link to Download',
    [ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.linkExpirationDate]:
      'Link Expiration Date',
    [ADHERENCE_METADATA_RESPONSE_KEYS.mediaType]: 'Media Type',
    [ADHERENCE_METADATA_RESPONSE_KEYS.assetDuration]: 'Asset Duration',
    [ADHERENCE_METADATA_RESPONSE_KEYS.date]: 'Date',
    [ADHERENCE_METADATA_RESPONSE_KEYS.impressions]: 'Impressions',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adName]: 'Ad Name',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adId]: 'Ad ID',
    [ADHERENCE_METADATA_RESPONSE_KEYS.adTypes]: 'Ad Types',
    [ADHERENCE_METADATA_KEYS_NOT_IN_RESPONSE_OBJECT.overallScore]:
      'Overall Score',
  };

export const JWT_EXPIRATION_MILLI_SECONDS = 1000 * 60 * 60 * 24 * 15; // 15 days

export const JWT_REQUESTS_BATCH_SIZE = 1000;

export const CRITERIA_AND_SCORES_KEY = 'criteriaAndScores';

export const BLANK_CSV_VALUE = '-';

export const ALL_PLATFORMS = 'ALL_PLATFORMS';

export const STANDARD_CRITERIA = 'Standard Criteria';

export const INTERNAL_TO_CSV_OUTPUT_SCORE_MAP = {
  [CriteriaStatusDto.StatusEnum.Pass]: 'Yes',
  [CriteriaStatusDto.StatusEnum.Fail]: 'No',
  [CriteriaStatusDto.StatusEnum.NoData]: 'No Data',
};

export interface CriteriaAndScoresCSVAccumulator {
  platformAggregate: Record<
    string,
    { totalCount: number; passedCount: number }
  >;
  criteriaAndScores: string[];
}

export type AdherenceReportParentRowKey = 'channel' | 'criteriaGroup';

export type AdherenceReportChildRowKey = 'criteria';

export type AdherenceReportColumnKey = 'market' | 'workspace' | 'brand';

export const ADHERENCE_REPORT_GROUP_BY_COLUMNS_OPTIONS = {
  BRAND: 'brand',
  WORKSPACE: 'workspace',
  MARKET: 'market',
};

export const NOT_SPECIFIED = {
  value: 'NOT_SPECIFIED',
  displayName: 'Not Specified',
};

export const OVERALL_COLUMN_HEADER_DISPLAY_NAME = 'Overall average';

export const ADHERENCE_PERCENTAGE_DECIMAL_PLACES = 4;

export interface AdherenceAggregateRowDataToAccumulatorParams {
  accumulator: Record<
    string,
    {
      id: string;
      displayName: string;
      parentId: string | null;
      columns: Record<string, number[]>;
    }
  >;
  item: AdherenceReportV3DataItemDto;
  rowKey: AdherenceReportParentRowKey | AdherenceReportChildRowKey;
  rowId: string;
  parentId: string | null;
  parentColumnKey: AdherenceReportColumnKey;
  childColumnKey: AdherenceReportColumnKey;
  displayName: string;
}

export interface AdherenceAggregateColumnDataToAccumulatorParams {
  accumulator: Record<
    string,
    {
      id: string;
      displayName: string;
      parentId: string | null;
      value: number[];
      identifiers: Record<string, string | number>;
    }
  >;
  value: string;
  dataValueArray: number[];
  columnDisplayNameMap: Map<string, string>;
  parentId: string | null;
  identifiers: Record<string, string | number>;
}
