import { HttpException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ReadBrandIdentifierDto, UpdateBrandIdentifiersDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { BrandController } from './brand.controller';
import { BrandService } from './brand.service'

describe('BrandController', () => {
  let controller: BrandController;
  let brandService: BrandService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BrandController],
      providers: [
        {
          provide: BrandService,
          useValue: {
            getBrandIdentifiers: jest.fn(),
            updateBrandIdentifiers: jest.fn(),
          }
        }
      ],
    }).compile();

    controller = module.get<BrandController>(BrandController);
    brandService = module.get<BrandService>(BrandService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should retrieve brand identifiers for a partner', () => {
    const partnerId = 123;
    const testResponse: ReadBrandIdentifierDto[] = [
      {id: 1, partnerId, brandIdentifier: 'coke', dateCreated: "2023-06-21T02:06:31.577Z", lastUpdated: "2023-06-21T02:06:31.577Z"},
      {id: 2, partnerId, brandIdentifier: 'coca-cola', dateCreated: "2023-06-21T02:06:31.578Z", lastUpdated: "2023-06-21T02:06:31.578Z"},
    ];
    brandService.getBrandIdentifiers = jest.fn().mockReturnValue(testResponse);

    // Call the route
    const response = controller.getBrandIdentifiers(partnerId);

    // Expectations
    expect(brandService.getBrandIdentifiers).toHaveBeenCalledWith(partnerId);
    expect(response).toEqual(testResponse);
  });

  it('should update brand identifiers for a partner', () => {
    const partnerId = 123;
    const updateBrandIdentifiersDto: UpdateBrandIdentifiersDto = {brandIdentifiers: ['pepsi', 'mountian dew']};
    const testResponse: ReadBrandIdentifierDto[] = [
      {id: 1, partnerId, brandIdentifier: 'pepsi', dateCreated: "2023-06-21T02:06:31.577Z", lastUpdated: "2023-06-21T02:06:31.577Z"},
      {id: 2, partnerId, brandIdentifier: 'mountian dew', dateCreated: "2023-06-21T02:06:31.578Z", lastUpdated: "2023-06-21T02:06:31.578Z"},
    ];

    brandService.updateBrandIdentifiers = jest.fn().mockReturnValue(testResponse);

    // Call the route
    const response = controller.updateBrandIdentifiers(partnerId, updateBrandIdentifiersDto);

    // Expectations
    expect(brandService.updateBrandIdentifiers).toHaveBeenCalledWith(partnerId, updateBrandIdentifiersDto);
    expect(response).toEqual(testResponse);
  });

});
