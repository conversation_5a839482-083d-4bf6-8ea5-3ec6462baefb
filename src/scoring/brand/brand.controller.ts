import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
} from '@nestjs/common';
import { BrandService } from './brand.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { readConfiguration, updateConfiguration } from '../scoring.permissions';
import { UpdateBrandIdentifiersDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Brand')
@ApiSecurity('Bearer Token')
@Controller('brand')
export class BrandController {
  constructor(private brandService: BrandService) {}

  /**
   * Get all brand identifiers for a partner.
   * @param partnerId
   */
  @ApiParam({
    name: 'partnerId',
    description: 'The id of the partner to get all brand identifiers for',
  })
  @Permissions(readConfiguration)
  @Get('partner/:partnerId')
  getBrandIdentifiers(@Param('partnerId', ParseIntPipe) partnerId: number) {
    return this.brandService.getBrandIdentifiers(partnerId);
  }

  /**
   * Update brand identifiers for a partner.
   * @param partnerId
   * @param updateBrandIdentifiersDto - a collection of brand identifiers that will completely replace the existing set.
   */
  @ApiParam({
    name: 'partnerId',
    description: 'The id of the partner to update the brand identifiers for',
  })
  @Permissions(updateConfiguration)
  @Post('partner/:partnerId')
  updateBrandIdentifiers(
    @Param('partnerId', ParseIntPipe) partnerId: number,
    @Body() updateBrandIdentifiersDto: UpdateBrandIdentifiersDto,
  ) {
    return this.brandService.updateBrandIdentifiers(
      partnerId,
      updateBrandIdentifiersDto,
    );
  }
}
