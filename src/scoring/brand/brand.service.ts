import { Injectable } from '@nestjs/common';
import { ScoringBrandIdentifiersService, UpdateBrandIdentifiersDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { catchError, map } from 'rxjs';
import { rethrowAxiosError } from '../api.utils';

@Injectable()
export class BrandService {
  constructor(
    private readonly scoringBrandIdentifierService: ScoringBrandIdentifiersService,
  ) {}

  getBrandIdentifiers(partnerId: number) {
    return this.scoringBrandIdentifierService
      .getBrandIdentifiers(partnerId)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  updateBrandIdentifiers(partnerId: number, updateBrandIdentifiersDto: UpdateBrandIdentifiersDto) {
    return this.scoringBrandIdentifierService
      .updateBrandIdentifiers(partnerId, updateBrandIdentifiersDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }
}
