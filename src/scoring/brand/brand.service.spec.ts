import { HttpException, HttpStatus } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import {
  ReadBrandIdentifierDto,
  ScoringBrandIdentifiersService,
  UpdateBrandIdentifiersDto
} from "@vidmob/vidmob-soa-scoring-service-sdk";
import { AxiosError, AxiosResponse } from "axios";
import { of, throwError } from "rxjs";
import { BrandService } from "./brand.service";

describe("BrandService", () => {
  let service: BrandService;
  let remoteService: ScoringBrandIdentifiersService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [BrandService,
        {
          provide: ScoringBrandIdentifiersService,
          useValue: {
            getBrandIdentifiers: jest.fn(),
            updateBrandIdentifiers: jest.fn(),
          }
        }]
    }).compile();

    service = module.get<BrandService>(BrandService);
    remoteService = module.get<ScoringBrandIdentifiersService>(ScoringBrandIdentifiersService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  it("should call getBrandIdentifiers", async () => {
    const partnerId = 123;
    const testResponse: ReadBrandIdentifierDto[] = [
      {
        id: 1,
        partnerId,
        brandIdentifier: "pepsi",
        dateCreated: "2023-06-21T02:06:31.577Z",
        lastUpdated: "2023-06-21T02:06:31.577Z"
      },
      {
        id: 2,
        partnerId,
        brandIdentifier: "mountian dew",
        dateCreated: "2023-06-21T02:06:31.578Z",
        lastUpdated: "2023-06-21T02:06:31.578Z"
      }
    ];
    const axiosResponse = { data: { result: testResponse } };
    remoteService.getBrandIdentifiers = jest.fn().mockReturnValue(
      of(axiosResponse)
    );
    const response = await service.getBrandIdentifiers(partnerId).toPromise();
    expect(response).toEqual(testResponse);
  });

  it("test calling updateBrandIdentifiers", async () => {
    const partnerId = 123;
    const newBrandIdentifiers: UpdateBrandIdentifiersDto = {
      brandIdentifiers: ["one", "two", "three"]
    };
    const dummyResponse = "dummy response";
    const axiosResponse = { data: { result: dummyResponse } };
    remoteService.updateBrandIdentifiers = jest.fn().mockReturnValue(
      of(axiosResponse)
    );
    const response = await service.updateBrandIdentifiers(partnerId, newBrandIdentifiers).toPromise();
    expect(response).toEqual(dummyResponse);
    expect(remoteService.updateBrandIdentifiers).toHaveBeenCalledWith(partnerId, newBrandIdentifiers);
  });

  it("Forward error from remote call", async () => {
    const errorResponse = {
      status: HttpStatus.BAD_REQUEST,
      message: "Validation failed",
      errors: [
        { field: "username", message: "Username is required" },
        { field: "email", message: "Email is invalid" }
      ]
    };

    const axiosResponse: AxiosResponse = {
      data: errorResponse,
      status: HttpStatus.BAD_REQUEST
    } as AxiosResponse;
    const axiosError: AxiosError = new AxiosError(
      "not used",
      "code is not used",
      undefined,
      undefined,
      axiosResponse);

    remoteService.getBrandIdentifiers = jest.fn().mockReturnValue(throwError(axiosError));

    try {
      const response = await service.getBrandIdentifiers(123).toPromise();
      expect(true).toBe(false);
    } catch (e) {
      expect(e).toBeInstanceOf(HttpException);
      expect(e.message).toEqual(errorResponse.message);
      expect(e.status).toEqual(errorResponse.status);
    }
  });

});
