import { Test, TestingModule } from '@nestjs/testing';
import { lastValueFrom, of, throwError } from 'rxjs';
import { ScoreOverrideService } from './score-override.service';
import {
  ReadOverrideRequestDto,
  ScoringOverrideRequestService,
  UpdateScoreRequestsDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import {
  ScoreOverrideRequestReason,
  ScoreOverrideRequestStatus,
} from './score-override.constants';
import { ScoreOverrideGetResponseDto } from './dtos/score-override-get-response.dto';
import { MediaService } from '@vidmob/vidmob-organization-service-sdk/dist/api/media.service';

describe('ScoreOverrideService', () => {
  let service: ScoreOverrideService;
  let mockScoringService: any;

  beforeEach(async () => {
    mockScoringService = {
      updateScoreOverrideRequest: jest.fn(),
      getScoreOverrideRequest: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScoreOverrideService,
        {
          provide: ScoringOverrideRequestService,
          useValue: mockScoringService,
        },
        {
          provide: MediaService,
          useValue: {
            getMediaByIdAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ScoreOverrideService>(ScoreOverrideService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateScoreOverrideRequests', () => {
    it('should return the result when the service call is successful', async () => {
      const userId = 1;
      const dto: UpdateScoreRequestsDto = {} as any;
      const expectedResult = { success: true };

      mockScoringService.updateScoreOverrideRequest.mockReturnValue(
        of({ data: { result: expectedResult } }),
      );

      const response = await service.updateScoreOverrideRequests(userId, dto);

      const result = await lastValueFrom(response);

      expect(result).toEqual(expectedResult);
      expect(
        mockScoringService.updateScoreOverrideRequest,
      ).toHaveBeenCalledWith(userId, dto);
    });

    it('should throw an error when the service call fails', async () => {
      const userId = 1;
      const dto: UpdateScoreRequestsDto = {} as any;
      const error = new Error('Test error');

      mockScoringService.updateScoreOverrideRequest.mockReturnValue(
        throwError(error),
      );

      try {
        await service.updateScoreOverrideRequests(userId, dto);
      } catch (err) {
        expect(err).toBe(error);
      }

      expect(
        mockScoringService.updateScoreOverrideRequest,
      ).toHaveBeenCalledWith(userId, dto);
    });
  });

  describe('getScoreOverride', () => {
    const mockGetOverrideRequestResponse: ReadOverrideRequestDto[] = [
      {
        id: '61e5e3ba-91b0-4b0a-a652-e38006a61ce3',
        criteriaId: 3145,
        criteriaIdentifier: 'CTA_DETECTION',
        criteriaParameters: '{}',
        mediaId: 180576,
        workspaceId: 31869,
        workspaceName: 'test workspace',
        status: ScoreOverrideRequestStatus.OPEN,
        reason: ScoreOverrideRequestReason.INCORRECT,
        reasonText: 'fewer than 10 words on screen but score is no',
        originalScore: 0,
        scoreChanged: false,
        dateCreated: '2023-05-03',
        lastUpdated: '2023-05-03',
        reportId: 123,
        reportName: 'test report',
        criteriaDescription: 'CTA Detection',
        criteriaRule: 'CTA Detection Rule',
        person: {
          id: 432,
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          displayName: 'Test User',
          photoUrl: 'https://test.com/test.jpg',
        },
      },
      {
        id: '61e5e3ba-91b0-4b0a-a652-e38006a61ce1',
        criteriaId: 2390,
        criteriaIdentifier: 'FRAMED_FOR_MOBILE',
        criteriaParameters: '{"aspectRatios": ["16:9"]}',
        criteriaDescription: 'Framed for Mobile',
        criteriaRule: 'Framed for Mobile Rule',
        mediaId: 180576,
        workspaceId: 31732,
        workspaceName: 'test workspace',
        status: ScoreOverrideRequestStatus.OPEN,
        reason: ScoreOverrideRequestReason.INCORRECT,
        reasonText: 'human not present but score is yes',
        originalScore: 0,
        scoreChanged: false,
        dateCreated: '2023-05-03',
        lastUpdated: '2023-05-03',
        reportId: 123,
        reportName: 'test report',
        person: {
          id: 432,
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>',
          displayName: 'Test User',
          photoUrl: 'https://test.com/test.jpg',
        },
      },
    ];

    const mockGetOverrideResponse: ScoreOverrideGetResponseDto = {
      personId: 432,
      overrideRequests: mockGetOverrideRequestResponse,
    };

    it('should return a score override response', async () => {
      mockScoringService.getScoreOverrideRequest.mockReturnValue(
        of({ data: { result: mockGetOverrideRequestResponse } }),
      );

      const result = await service.getScoreOverride(
        432,
        ScoreOverrideRequestStatus.OPEN,
      );
      expect(result).toEqual(mockGetOverrideResponse);
    });

    it('should fail if the scoring service fails', async () => {
      const error = new Error('error');
      mockScoringService.getScoreOverrideRequest.mockReturnValue(
        throwError(error),
      );

      await expect(
        service.getScoreOverride(432, ScoreOverrideRequestStatus.OPEN),
      ).rejects.toThrow('error');
    });
  });
});
