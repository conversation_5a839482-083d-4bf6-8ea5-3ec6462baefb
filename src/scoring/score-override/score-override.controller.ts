import {
  Body,
  Controller,
  Get,
  Put,
  Request,
  Query,
  ValidationPipe,
  UsePipes,
  Param,
  Version,
} from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  ScoringOverrideRequestService,
  UpdateScoreRequestsDto,
  UpdateScoreResponseDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoreOverrideService } from './score-override.service';
import {
  ScoreOverrideGetResponseDto,
  ScoreOverrideGetResponseV2Dto,
} from './dtos/score-override-get-response.dto';
import { GetScoreOverridesDto } from './dtos/get-score-overrides.dto';
import { Observable } from 'rxjs';

@ApiTags('Score Override')
@ApiSecurity('Bearer Token')
@Controller('compliance/score-override')
export class ScoreOverrideController {
  constructor(
    private readonly scoringOverrideRequestService: ScoringOverrideRequestService,
    private readonly scoreOverrideService: ScoreOverrideService,
  ) {}

  /**
   * Updates and resolves one or more score override requests.
   * @param overrideRequests - A list of ScoreOverrideUpdateDto objects.
   */
  @ApiParam({
    name: 'overrideRequests',
    description: 'A list of resolutions to score override requests',
  })
  @Put()
  async updateScoreOverride(
    @Request() req: any,
    @Body(new ValidationPipe()) overrideRequests: UpdateScoreRequestsDto,
  ): Promise<Observable<UpdateScoreResponseDto[]>> {
    const { userId } = req;
    return this.scoreOverrideService.updateScoreOverrideRequests(
      userId,
      overrideRequests,
    );
  }

  /**
   * Gets a list of score override requests that the caller has permission to see.
   * @param req
   * @param getScoreOverridesParams
   */
  @UsePipes(new ValidationPipe())
  @Get()
  async getScoreOverride(
    @Request() req: any,
    @Query() getScoreOverridesParams: GetScoreOverridesDto,
  ): Promise<ScoreOverrideGetResponseDto> {
    const { userId } = req;
    const { status, canUpdate, offset, perPage } = getScoreOverridesParams;
    return this.scoreOverrideService.getScoreOverride(
      userId,
      status,
      offset,
      perPage,
      canUpdate,
    );
  }

  @UsePipes(new ValidationPipe())
  @Version('2')
  @Get('/organization/:organizationId')
  async getScoreOverrideV2(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @Query() getScoreOverridesDto: GetScoreOverridesDto,
  ): Promise<ScoreOverrideGetResponseV2Dto> {
    const { userId } = req;
    return this.scoreOverrideService.getScoreOverrideV2(
      userId,
      organizationId,
      getScoreOverridesDto,
    );
  }
}
