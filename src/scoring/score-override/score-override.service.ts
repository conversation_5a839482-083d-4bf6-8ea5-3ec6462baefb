import { Injectable } from '@nestjs/common';
import {
  ScoringOverrideRequestService,
  UpdateScoreRequestsDto,
  ReadOverrideRequestDto,
  UpdateScoreResponseDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { catchError, map, Observable } from 'rxjs';
import {
  ScoreOverrideGetResponseDto,
  ScoreOverrideGetResponseV2Dto,
} from './dtos/score-override-get-response.dto';
import { MediaService } from '@vidmob/vidmob-organization-service-sdk/dist/api/media.service';
import { ScoreOverrideRequestStatus } from './score-override.constants';
import { lastValueFrom } from 'rxjs';
import { rethrowAxiosError } from '../api.utils';
import { GetScoreOverridesDto } from './dtos/get-score-overrides.dto';

@Injectable()
export class ScoreOverrideService {
  constructor(
    private readonly scoringOverrideRequestService: ScoringOverrideRequestService,
    private readonly mediaService: MediaService,
  ) {}

  async updateScoreOverrideRequests(
    userId: number,
    overrideRequests: UpdateScoreRequestsDto,
  ): Promise<Observable<UpdateScoreResponseDto[]>> {
    return this.scoringOverrideRequestService
      .updateScoreOverrideRequest(userId, overrideRequests)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  async getScoreOverride(
    personId: number,
    status: ScoreOverrideRequestStatus,
    offset: number | undefined = undefined,
    perPage: number | undefined = undefined,
    canUpdate: boolean | undefined = undefined,
  ): Promise<ScoreOverrideGetResponseDto> {
    try {
      const response =
        this.scoringOverrideRequestService.getScoreOverrideRequest(
          personId,
          canUpdate ?? false,
          status,
          offset,
          perPage,
        );
      const overrideRequestsResult = await lastValueFrom(response);

      const overrideRequests: ReadOverrideRequestDto[] =
        overrideRequestsResult.data.result || [];
      const scoreOverrideResponse: ScoreOverrideGetResponseDto = {
        personId,
        overrideRequests,
      };
      return scoreOverrideResponse;
    } catch (err) {
      return rethrowAxiosError(err);
    }
  }

  async getScoreOverrideV2(
    personId: number,
    organizationId: string,
    getScoreOverridesDto: GetScoreOverridesDto,
  ): Promise<ScoreOverrideGetResponseV2Dto> {
    const { status, canUpdate, offset, perPage } = getScoreOverridesDto;

    try {
      const requests =
        await this.scoringOverrideRequestService.getScoreOverrideRequestV2AsPromise(
          personId,
          organizationId,
          canUpdate ?? false,
          status,
          offset,
          perPage,
        );
      const overrideRequests = requests.result;

      const decoratedRequests = await Promise.all(
        overrideRequests.map(async (req) => {
          const { result: media } =
            await this.mediaService.getMediaByIdAsPromise(req.mediaId);
          return {
            id: req.id,
            status: req.status,
            reason: req.reason,
            reasonText: req.reasonText,
            resolver: req.resolver,
            requester: req.person,
            resolution: req.resolution,
            resolutionText: req.resolutionText,
            originalScore: req.originalScore,
            scoreChanged: req.scoreChanged,
            dateCreated: req.dateCreated,
            resolutionDate: req.resolutionDate,
            lastUpdated: req.lastUpdated,
            workspaceId: req.workspaceId,
            workspaceName: req.workspaceName,
            mediaId: req.mediaId,
            scorecardId: req.reportId,
            scorecardName: req.reportName,
            criteriaId: req.criteriaId,
            criteriaIdentifier: req.criteriaIdentifier,
            criteriaParameters: req.criteriaParameters,
            criteriaDescription: req.criteriaDescription,
            criteriaDefaultDisplayName: req.criteriaDefaultDisplayName,
            criteriaName: req.criteriaName,
            criteriaRule: req.criteriaRule,
            media,
          };
        }),
      );

      return { personId, overrideRequests: decoratedRequests };
    } catch (err) {
      return rethrowAxiosError(err);
    }
  }
}
