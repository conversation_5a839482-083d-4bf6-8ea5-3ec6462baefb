export enum Status {
  RESOLVED_NEEDS_OVERRIDE = 'RESOLVED_NEEDS_OVERRIDE',
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  RESOLVED = 'RESOLVED',
}

export enum Resolution {
  SCORE_CHANGED = 'SCORE_CHANGED',
  SCORE_NOT_CHANGED = 'SCORE_NOT_CHANGED',
  OTHER = 'OTHER',
}

export enum ScoreOverrideRequestStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  RESOLVED = 'RESOLVED',
}

export enum ScoreOverrideRequestResolution {
  SCORE_CHANGED = 'SCORE_CHANGED',
  SCORE_NOT_CHANGED = 'SCORE_NOT_CHANGED',
  OTHER = 'OTHER',
}

export enum ScoreOverrideRequestReason {
  INCORRECT = 'INCORRECT',
  DOES_NOT_APPLY = 'DOES_NOT_APPLY',
  OTHER = 'OTHER',
}
