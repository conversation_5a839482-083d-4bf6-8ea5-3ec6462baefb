import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsEnum,
  <PERSON>Date,
  IsOptional,
  IsBoolean,
} from 'class-validator';
import { Resolution, Status } from '../score-override.constants';

/*
 * DTO representing a single score override request. A score override request is a request that a single criteria be
 * overridden on a particular media after it has been scored.
 */
export class ScoreOverrideDto {
  /*
   * The ID of the score override request.
   */
  @IsUUID()
  id: string;

  /*
   * The ID of the criteria associated with this override request.
   */
  @IsNumber()
  criteriaId: number;

  /*
   * The ID of the media associated with this override request.
   */
  @IsNumber()
  mediaId: number;

  /*
   * The ID of the workspace associated with this override request.
   */
  @IsNumber()
  workspaceId: number;

  /*
   * The status of this override request. (OPEN, CLOSED, RESOLVED)
   */
  @IsEnum(Status)
  status: Status;

  /*
   * The comment provided when the override request was created.
   */
  @IsOptional()
  @IsString()
  reasonComment: string | null;

  /*
   * The ID of the person who resolved the override request.
   */
  @IsOptional()
  @IsNumber()
  resolverId: number | null;

  /*
   * How the override request was resolved. (SCORE_CHANGED, SCORE_NOT_CHANGED, OTHER)
   */
  @IsOptional()
  @IsEnum(Resolution)
  resolution: Resolution | null;

  /*
   * The comment provided when the override request was resolved.
   */
  @IsOptional()
  @IsString()
  resolutionComment: string | null;

  /*
   * Whether or not the score was changed as a result of this override request.
   */
  @IsBoolean()
  scoreChanged: boolean;

  /*
   * The date the override request was created.
   */
  @IsDate()
  dateCreated: Date;

  /*
   * The date the override request was resolved.
   */
  @IsOptional()
  @IsDate()
  dateResolved: Date | null;

  /*
   * The date the override request was last updated.
   */
  @IsDate()
  lastUpdated: Date;
}
