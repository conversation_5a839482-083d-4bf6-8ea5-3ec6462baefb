import { IsBoolean, IsEnum, IsOptional } from 'class-validator';
import { ScoreOverrideRequestStatus } from '../score-override.constants';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Transform } from 'class-transformer';

export class GetScoreOverridesDto extends PaginationOptions {
  /**
   * Optionally filter by request status.
   */
  @IsEnum(ScoreOverrideRequestStatus)
  @IsOptional()
  status: ScoreOverrideRequestStatus;

  /**
   * Optionally filter by whether the user can update the request. If true, only return requests that the user can update.
   * If false, return all requests the user can read.
   */
  @IsOptional()
  @IsBoolean()
  @Transform((value: any) => Boolean(value))
  canUpdate?: boolean;
}
