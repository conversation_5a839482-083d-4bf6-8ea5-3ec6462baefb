import { IsArray } from 'class-validator';
import {
  ReadOverrideRequestDto,
  ReadOverrideRequestDtoPerson,
  ReadOverrideRequestDtoResolver,
} from '@vidmob/vidmob-soa-scoring-service-sdk';

export class ScoreOverrideGetResponseDto {
  /**
   * The unique identifier for the person who requested the score override.
   */
  personId: number;

  /**
   * The array of score override requests.
   */
  overrideRequests: ReadOverrideRequestDto[];
}

export class ScoreOverrideGetResponseV2Dto {
  /**
   * The unique identifier for the person who requested the score override.
   */
  personId: number;

  @IsArray()
  overrideRequests: ReadOverrideRequestV2Dto[];
}

export interface ReadOverrideRequestV2Dto {
  id: string;
  status: ReadOverrideRequestDto.StatusEnum;
  reason: ReadOverrideRequestDto.ReasonEnum;
  reasonText: string;
  resolver?: ReadOverrideRequestDtoResolver;
  requester: ReadOverrideRequestDtoPerson;
  resolution?: ReadOverrideRequestDto.ResolutionEnum;
  resolutionText?: string;
  originalScore: number;
  scoreChanged: boolean;
  dateCreated: string;
  resolutionDate?: string;
  lastUpdated: string;
  workspaceId: number;
  workspaceName: string;
  mediaId: number;
  scorecardId: number;
  scorecardName: string;
  criteriaId: number;
  criteriaIdentifier: ReadOverrideRequestDto.CriteriaIdentifierEnum;
  criteriaParameters: string;
  criteriaDescription: string;
  criteriaRule: string;
  criteriaDefaultDisplayName?: string;
  criteriaName?: string;
  media: object;
}
