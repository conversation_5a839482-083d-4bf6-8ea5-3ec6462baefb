import { Test, TestingModule } from '@nestjs/testing';
import { ScoreOverrideController } from './score-override.controller';
import { ScoreOverrideService } from './score-override.service';
import {
  ScoringOverrideRequestService,
  UpdateScoreRequestsDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { throwError } from 'rxjs';
import { ScoreOverrideGetResponseDto } from './dtos/score-override-get-response.dto';
import {
  ScoreOverrideRequestReason,
  ScoreOverrideRequestStatus,
} from './score-override.constants';

describe('ScoreOverrideController', () => {
  let controller: ScoreOverrideController;
  let mockGetOverrideResponse: ScoreOverrideGetResponseDto;
  let mockGetScoreOverride: jest.Mock;
  let mockUpdateScoreOverride: jest.Mock;

  beforeEach(async () => {
    mockGetOverrideResponse = {
      personId: 432,
      overrideRequests: [
        {
          id: '61e5e3ba-91b0-4b0a-a652-e38006a61ce3',
          criteriaId: 3145,
          criteriaIdentifier: 'CTA_DETECTION',
          criteriaParameters: '{}',
          criteriaDescription: 'CTA_DETECTION',
          criteriaRule: 'CTA_DETECTION_RULE',
          mediaId: 180576,
          workspaceId: 31869,
          workspaceName: 'test workspace',
          status: ScoreOverrideRequestStatus.OPEN,
          reason: ScoreOverrideRequestReason.INCORRECT,
          reasonText: 'fewer than 10 words on screen but score is no',
          originalScore: 0,
          scoreChanged: false,
          dateCreated: '2023-05-03',
          lastUpdated: '2023-05-03',
          reportId: 123,
          reportName: 'test report',
          person: {
            id: 432,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            displayName: 'Test User',
            photoUrl: 'https://test.com/test.jpg',
          },
        },
        {
          id: '61e5e3ba-91b0-4b0a-a652-e38006a61ce1',
          criteriaId: 2390,
          criteriaIdentifier: 'FRAMED_FOR_MOBILE',
          criteriaParameters: '{"aspectRatios": ["16:9"]}',
          criteriaDescription: 'Framed for Mobile',
          criteriaRule: 'Framed for Mobile Rule',
          mediaId: 180576,
          workspaceId: 31732,
          workspaceName: 'test workspace',
          status: ScoreOverrideRequestStatus.OPEN,
          reason: ScoreOverrideRequestReason.INCORRECT,
          reasonText: 'human not present but score is yes',
          originalScore: 0,
          scoreChanged: false,
          dateCreated: '2023-05-03',
          lastUpdated: '2023-05-03',
          reportId: 123,
          reportName: 'test report',
          person: {
            id: 432,
            firstName: 'Test',
            lastName: 'User',
            email: '<EMAIL>',
            displayName: 'Test User',
            photoUrl: 'https://test.com/test.jpg',
          },
        },
      ],
    };

    mockGetScoreOverride = jest.fn().mockResolvedValue(mockGetOverrideResponse);
    mockUpdateScoreOverride = jest.fn().mockResolvedValue({ success: true });

    const module: TestingModule = await Test.createTestingModule({
      controllers: [ScoreOverrideController],
      providers: [
        { provide: ScoringOverrideRequestService, useValue: {} },
        {
          provide: ScoreOverrideService,
          useValue: {
            getScoreOverride: mockGetScoreOverride,
            updateScoreOverrideRequests: mockUpdateScoreOverride,
          },
        },
      ],
    }).compile();

    controller = module.get<ScoreOverrideController>(ScoreOverrideController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getScoreOverride', () => {
    it('should return the correct score override response', async () => {
      const response: ScoreOverrideGetResponseDto =
        await controller.getScoreOverride(
          { userId: 432 },
          {
            status: ScoreOverrideRequestStatus.OPEN,
            perPage: 10,
            offset: 0,
          },
        );
      expect(response).toEqual(mockGetOverrideResponse);
    });

    it('should fail if the service call fails', async () => {
      const mockError = new Error('test error');
      mockGetScoreOverride.mockRejectedValueOnce(mockError);
      await expect(
        controller.getScoreOverride(
          { userId: 432 },
          {
            status: ScoreOverrideRequestStatus.OPEN,
            perPage: 10,
            offset: 0,
          },
        ),
      ).rejects.toThrow(mockError);
    });
  });

  describe('updateScoreOverride', () => {
    it('should return the result when the service call is successful', async () => {
      const userId = 1;
      const dto: UpdateScoreRequestsDto = {} as any;
      const expectedResult = { success: true };

      const result = await controller.updateScoreOverride({ userId }, dto);

      expect(result).toEqual(expectedResult);
      expect(mockUpdateScoreOverride).toHaveBeenCalledWith(userId, dto);
    });

    it('should throw an error when the service call fails', async () => {
      const userId = 1;
      const dto: UpdateScoreRequestsDto = {} as any;

      mockGetScoreOverride.mockRejectedValueOnce(
        throwError(new Error('Service call failed')),
      );

      try {
        await controller.updateScoreOverride({ userId }, dto);
      } catch (error) {
        expect(error.message).toBe('Service call failed');
      }

      expect(mockUpdateScoreOverride).toHaveBeenCalledWith(userId, dto);
    });
  });
});
