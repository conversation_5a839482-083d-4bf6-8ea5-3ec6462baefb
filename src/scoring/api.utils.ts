import { AxiosError } from 'axios/index';
import { HttpException } from '@nestjs/common';
import { ErrorResponse } from '@vidmob/vidmob-nestjs-common';

export const rethrowAxiosError = (err: any): never => {
  if (err.isAxiosError && err.response) {
    const axiosError: AxiosError = err;
    const { data, status } = axiosError.response!;
    if (data && typeof data === 'object' && 'status' in data) {
      throw new HttpException(data, status);
    }
  }

  const { identifier, type, system, message } = err.response?.error || {};
  if (identifier && type && system && message) {
    throw new ErrorResponse({
      httpStatusCode: err.status,
      traceId: err.response.traceId,
      error: err.response.error,
    });
  }

  throw err;
};
