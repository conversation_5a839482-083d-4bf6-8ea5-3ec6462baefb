import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import // Import necessary DTOs and services here
'@vidmob/vidmob-soa-scoring-service-sdk';
import { PersonService } from '@vidmob/vidmob-authorization-service-sdk';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { PermissionSubResource } from '../../auth/enums/permission.subresource.enum';
import { PermissionAction } from '../../auth/enums/permission.action.enum';
import { ReadWorkspaceSearchDto } from '../reports/internal-scoring-reports.interfaces';
import { catchError, firstValueFrom, map } from 'rxjs';
import { rethrowAxiosError } from '../api.utils';
import {
  OrganizationService,
  ScopeFilterService,
  WorkspaceService,
} from '@vidmob/vidmob-organization-service-sdk';
import {
  EarliestImpressionDateRequestDto,
  InflightAggregateRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { UserDetailsDto } from '../../analytics/dto/user-details.dto';
import { ScopeFilterRequestDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/scopeFilterRequestDto';
import { ScopeFilterResponseDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/scopeFilterResponseDto';
import { AnalyticsUserService } from '../../analytics/analytics-user-service/analytics-user-service';

@Injectable()
export class ScoringAuthService {
  private readonly logger = new Logger(ScoringAuthService.name);

  constructor(
    @InjectDataSource()
    private readonly datasource: DataSource,
    private readonly personService: PersonService,
    private readonly organizationService: OrganizationService,
    private readonly workspaceService: WorkspaceService,
    private readonly scopeFilterServiceSDK: ScopeFilterService,
    private readonly analyticsUserService: AnalyticsUserService,
  ) {}

  public async checkPermissions(
    userId: number,
    partnerId: number,
    subResource: PermissionSubResource,
    action: PermissionAction,
    resource = 'partner',
  ) {
    const sql = `SELECT pe.id
                 FROM permission pe
                          JOIN role_permission rp ON rp.permission_id = pe.id
                          JOIN partner_person pp ON pp.role_id = rp.role_id
                          JOIN partner pa ON pp.partner_id = pa.id
                 WHERE pe.resource = ?
                   AND pe.subresource = ?
                   AND pe.type = ?
                   AND pp.person_id = ?
                   AND pa.id = ?
                   AND pp.active = 1`;

    return await this.datasource.query(sql, [
      resource,
      subResource,
      action,
      userId,
      partnerId,
    ]);
  }

  public async getIsUserOrgAdmin(userId: number, workspaceId: number) {
    const subResource = 'workspace_all';
    const action = 'update';
    const resource = 'organization';

    const sql = `SELECT pe.id
                 FROM permission pe
                          JOIN role_permission rp ON rp.permission_id = pe.id
                          JOIN organization_person_role opr ON opr.role_id = rp.role_id
                          JOIN organization org ON org.id = opr.organization_id
                          JOIN partner pa ON pa.organization_id = org.id
                 WHERE pe.resource = ?
                   AND pe.subresource = ?
                   AND pe.type = ?
                   AND opr.person_id = ?
                   AND pa.id = ?`;

    const result = await this.datasource.query(sql, [
      resource,
      subResource,
      action,
      userId,
      workspaceId,
    ]);

    return result.length > 0;
  }

  public async isUserPartnerManager(
    userId: number,
    partnerId: number,
  ): Promise<boolean> {
    const sql = `SELECT pm.id FROM partner_manager pm WHERE (pm.role in ('ACCOUNT_MANAGER', 'PROJECT_MANAGER')) AND pm.manager_id = ? AND pm.partner_id = ?`;
    const result = await this.datasource.query(sql, [userId, partnerId]);
    return result.length > 0;
  }

  public async getWorkspaceIdForScorecard(scorecardId: number) {
    const sql = `SELECT partner_id FROM compliance_batch WHERE id = ?`;
    const result = await this.datasource.query(sql, [scorecardId]);
    return result[0].partner_id;
  }

  /**
   * Get a list of the partners that the user has an active role on and are related to the current partner.
   * @param personId - the id of the user.
   * @param partnerId - the id of the current partner.
   */
  async getRelatedAccessiblePartners(personId: number, partnerId: number) {
    const workspace = await this.workspaceService.findOneAsPromise(partnerId);
    const organizationId = workspace.result.organizationId;

    const [relatedPartners, isUserOrgAdmin] = await Promise.all([
      this.getRelatedPartners(organizationId),
      this.getIsUserOrgAdmin(personId, partnerId),
    ]);

    if (isUserOrgAdmin) {
      return relatedPartners;
    }

    const accessiblePartners = await this.getAccessiblePartners(personId);

    const accessiblePartnersIds = new Set(
      accessiblePartners.roles.map((x) => x.partnerId),
    );
    return relatedPartners.filter((p) => accessiblePartnersIds.has(p.id));
  }

  /**
   * Get the partners related to the current partner.
   * Technically this should live in the organization service but this is currently
   * a hacky implementation to meet a scoring need. I want to keep it private to
   * avoid other people introducing dependencies on it until the real version is
   * available.
   * @param organizationId - id of the current organization.
   * @private
   */
  private async getRelatedPartners(
    organizationId: string,
  ): Promise<ReadWorkspaceSearchDto[]> {
    const perPage = 10000;

    const relatedWorkspaces =
      await this.organizationService.findWorkspacesByOrganizationAsPromise(
        organizationId,
        undefined,
        undefined,
        undefined,

        0,
        perPage,
      );
    return relatedWorkspaces.result;
  }
  /**
   * Get a list of the partners that the user has an active role on.
   * This could live elsewhere but I want to keep it local until we're ready to share it.
   * @param userId - the id of the user.
   * @private
   */
  private getAccessiblePartners(userId: number) {
    const accessiblePartners = this.personService
      .getAllRolesForUser(userId)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
    return firstValueFrom(accessiblePartners);
  }

  private canAccessPartner(
    partnerId: number,
    accessiblePartners: ReadWorkspaceSearchDto[],
  ) {
    const canAccessPartner = accessiblePartners.some(
      (workspace) => `${workspace.id}` === `${partnerId}`,
    );
    if (!canAccessPartner) {
      throw new UnauthorizedException(
        `The user does not have access to workspace ${partnerId}`,
      );
    }
  }

  /**
   * Check if the user has access to the partner for the relevant scorecard.
   * @param userId - the id of the user.
   * @param scorecardId - the id of the scorecard.
   */

  /**
   * Checks if the user has access to the partner associated with the given scorecard.
   * @param userId - The ID of the user.
   * @param scorecardId - The ID of the scorecard.
   * @param currentWorkspaceId - The ID of the workspace the user is currently on.
   * @returns A boolean indicating whether the user has access to the workspace associated with the relevant scorecard.
   */
  public async canUserAccessPartnerForScorecard(
    userId: number,
    scorecardId: number,
    currentWorkspaceId: number,
  ) {
    // This may or may not be the same workspace that the user is currently on.
    const workspaceIdForScorecard = await this.getWorkspaceIdForScorecard(
      scorecardId,
    );
    // Here we use the ID of the workspace the user is currently on to determine which org they are on.
    // We need to scope our check for accessible workspaces to the current org.
    const accessiblePartners = await this.getRelatedAccessiblePartners(
      userId,
      currentWorkspaceId,
    );
    // Check if workspace associated with the scorecard is included in list of accessible workspaces on the org.
    this.canAccessPartner(workspaceIdForScorecard, accessiblePartners);
    return workspaceIdForScorecard;
  }

  public async validateAndReturnPlatformAccountIdsForInflightReport(
    inflightAggregateRequestDto:
      | InflightAggregateRequestDto
      | EarliestImpressionDateRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<string[]> {
    const {
      channel,
      workspaceIds,
      platformAccountIds: platformAccountIdsInRequest,
      brandIds,
      markets,
    } = inflightAggregateRequestDto;
    let platformAccountIds = platformAccountIdsInRequest;

    if (!platformAccountIds?.length) {
      const adAccounts =
        await this.scopeFilterServiceSDK.filterAdAccountsByScopeAsPromise(
          {
            platform: channel,
            workspaceIds,
            brandIds,
            marketIds: markets,
            type: ScopeFilterRequestDto.TypeEnum.AdAccounts,
          },
          0,
          Number.MAX_SAFE_INTEGER,
        );

      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore // return type is incorrectly defined in sdk, this works
      platformAccountIds = adAccounts.result.map(
        (adAccount: ScopeFilterResponseDto) => adAccount.id,
      );
    }

    if (platformAccountIds?.length) {
      await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
        userDetails,
        inflightAggregateRequestDto.workspaceIds,
        platformAccountIds,
      );
    }

    return platformAccountIds || [];
  }
}
