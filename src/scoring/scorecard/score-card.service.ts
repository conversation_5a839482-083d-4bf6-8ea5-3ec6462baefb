import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import {
  CreateScorecardRequestDto,
  CreateAdAccountScorecardRequestDto,
  ScorecardService,
  MediaService,
  UpdateScorecardsRequestDto,
  GetScorecardsRequestDto,
  GetScorecardsQueryparamsDto,
  GetMediaIdsForBatch200Response,
  GetScorecardsResponseDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { MediaService as MediaServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';
import { GetOutputVideoScorecardsDto } from '@vidmob/vidmob-studio-service-sdk/dist/model/getOutputVideoScorecardsDto';
import {
  OFFSET_DEFAULT,
  PER_PAGE_DEFAULT,
  PaginationOptions,
} from '@vidmob/vidmob-nestjs-common';
import { PermissionAction } from '../../auth/enums/permission.action.enum';
import { PermissionSubResource } from '../../auth/enums/permission.subresource.enum';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { GetAssetVersionsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getAssetVersionsResponseDto';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';

@Injectable()
export class ScoreCardService {
  private readonly logger = new Logger(ScoreCardService.name);
  constructor(
    @InjectDataSource()
    private readonly dataSource: DataSource,
    private readonly organizationMediaServiceSDK: MediaServiceSDK,
    private readonly scorecardSDKService: ScorecardService,
    private readonly scoringMediaServiceSDK: MediaService,
    private readonly scoringAuthService: ScoringAuthService,
    private readonly projectService: ProjectService,
  ) {}

  async getScorecardAssetVersions(
    baseBatchId: string,
    currentBatchId: string,
    currentMediaId: string,
  ): Promise<{
    status: string;
    result: GetAssetVersionsResponseDto;
  }> {
    const response =
      this.scorecardSDKService.getScorecardAssetVersionsAsPromise(
        baseBatchId,
        currentBatchId,
        currentMediaId,
      );

    // For some reason the SDK will not build the getScorecardAssetVersions 200 response DTO.
    // We need to assert the response as unknown and then cast it to the expected response type for now.
    return response as unknown as {
      status: string;
      result: GetAssetVersionsResponseDto;
    };
  }

  async getScorecardsPostHandler(
    userId: number,
    workspaceId: number,
    paginationOptions: PaginationOptions,
    getScorecardsRequestDto: GetScorecardsRequestDto,
  ) {
    const { offset, perPage } = paginationOptions;
    const isAllowedToRead = await this.isUserAllowedToReadCreate(
      userId,
      workspaceId,
    );
    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of scorecards for workspace ${workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    if (!getScorecardsRequestDto.offset) {
      getScorecardsRequestDto.offset = offset;
    }
    if (!getScorecardsRequestDto.perPage) {
      getScorecardsRequestDto.perPage = perPage;
    }

    return await this.scorecardSDKService.getScorecardsPostAsPromise(
      workspaceId,
      getScorecardsRequestDto,
    );
  }

  async getScorecardsGetHandler(
    userId: number,
    paginationOptions: PaginationOptions,
    getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
  ) {
    const { offset, perPage } = paginationOptions;
    const {
      workspaceId,
      markets,
      platforms,
      types,
      creators,
      statuses,
      sortBy,
      sortOrder,
      startDate,
      endDate,
      searchText,
      brands,
      platformAdAccounts,
    } = getScorecardsQueryParamDto;

    const isAllowedToRead = await this.isUserAllowedToReadCreate(
      userId,
      workspaceId,
    );
    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of scorecards for workspace ${workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    const platformsAsString: string = platforms as unknown as string;

    return await this.scorecardSDKService.getScorecardsAsPromise(
      workspaceId,
      sortOrder,
      sortBy,
      types,
      platformsAsString,
      startDate,
      endDate,
      markets,
      creators,
      statuses,
      searchText,
      brands,
      platformAdAccounts,
      offset,
      perPage,
    );
  }

  async getPlatformMediaId(
    mediaId: number,
    userId: number,
  ): Promise<{ platformMediaId: string | null }> {
    try {
      const { result } =
        await this.organizationMediaServiceSDK.validatePersonCanViewMediaAsPromise(
          mediaId,
          userId,
        );

      if (!result?.canView) {
        this.logger.error(
          `UserId - ${userId} does not have permission to view mediaId - ${mediaId}`,
        );
        return { platformMediaId: null };
      }

      const sql = `SELECT platform_media_id FROM platform_media WHERE media_id = ?`;
      const response = await this.dataSource.query(sql, [mediaId]);
      const platformMediaId = response[0]
        ? response[0].platform_media_id
        : null;

      return { platformMediaId };
    } catch (error) {
      this.logger.error(
        `Error while trying to get platformMediaId for mediaId - ${mediaId} and userId - ${userId} ${error}`,
      );
      return { platformMediaId: null };
    }
  }

  async getScorecardOptions(workspaceId: number, platforms?: string) {
    return this.scorecardSDKService.getScorecardOptionsAsPromise(
      workspaceId,
      platforms,
    );
  }

  async getOutputVideoScorecardCounts(
    projectId: number,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<GetOutputVideoScorecardsDto>> {
    const outputVideoScorecardCountsResponse: any =
      await this.projectService.projectControllerGetOutputVideoScorecardsCountAsPromise(
        projectId,
        paginationOptions?.offset,
        paginationOptions?.perPage,
        paginationOptions?.queryId,
      );

    return new PaginatedResultArray<GetOutputVideoScorecardsDto>(
      outputVideoScorecardCountsResponse.result ?? [],
      outputVideoScorecardCountsResponse.pagination?.totalSize ?? 0,
    );
  }

  async getScorecardWithProjectDetails(
    userId: number,
    workspaceId: number,
    getScorecardsRequestDto: GetScorecardsRequestDto,
    projectIdsFilter: (number | string)[],
  ) {
    const allScorecards = await this.fetchScorecardsInPaginationLoop(
      userId,
      workspaceId,
      getScorecardsRequestDto,
    );

    // Enhance each scorecard with project details from studioService
    const enhancedScorecards = await Promise.all(
      allScorecards.map(async (scorecard) => {
        const scorecardId = Number(scorecard.id);
        const mediaId = await this.getMediaIdByBatch(scorecardId);

        const projectResponse: any =
          await this.projectService.projectControllerGetProjectFromMediaAsPromise(
            mediaId,
          );

        const projectDetails = projectResponse.result;

        // If projects filter is provided, check if the project's id matches
        if (projectIdsFilter && projectIdsFilter.length > 0) {
          projectIdsFilter = projectIdsFilter.map((id) => Number(id));
          if (
            !projectDetails ||
            !projectDetails.projectId ||
            !projectIdsFilter.includes(projectDetails.projectId)
          ) {
            // Return null if the projectId does not match the filter
            return null;
          }
        }

        const media =
          await this.organizationMediaServiceSDK.getMediaByIdAsPromise(mediaId);
        const deviceIdentifier = media?.result?.deviceIdentifier;

        return {
          ...scorecard,
          ...projectDetails,
          deviceIdentifier: deviceIdentifier,
        };
      }),
    );

    // Remove any null values from the enhancedScorecards array
    const filteredScorecards = enhancedScorecards.filter(
      (scorecard) => scorecard !== null,
    );

    const totalSize = filteredScorecards.length;
    const perPage = getScorecardsRequestDto.perPage || PER_PAGE_DEFAULT;
    const offset = getScorecardsRequestDto.offset || OFFSET_DEFAULT;
    const nextOffset =
      offset + perPage >= totalSize ? OFFSET_DEFAULT : offset + perPage;

    const pagination = {
      offset,
      perPage,
      nextOffset,
      totalSize,
    };

    const paginatedScorecards = filteredScorecards.slice(
      offset,
      offset + perPage,
    );

    const response = {
      status: 'OK',
      result: paginatedScorecards,
      pagination: pagination,
    };

    return response;
  }

  async fetchScorecardsInPaginationLoop(
    userId: number,
    workspaceId: number,
    getScorecardsRequestDto: GetScorecardsRequestDto,
  ): Promise<any[]> {
    const allScorecards: any[] = [];

    const copyOfGetScorecardsRequestDto = { ...getScorecardsRequestDto };
    copyOfGetScorecardsRequestDto.perPage = undefined;
    copyOfGetScorecardsRequestDto.offset = undefined;

    let offset = 0;
    const perPage = 100;
    let totalSize;
    let moreRecords = true;

    while (moreRecords) {
      const scorecardsResponse = await this.getScorecardsPostHandler(
        userId,
        workspaceId,
        { perPage, offset },
        copyOfGetScorecardsRequestDto,
      );

      if (!totalSize) {
        totalSize = scorecardsResponse.pagination?.totalSize || 0;
      }

      allScorecards.push(...scorecardsResponse.result);

      offset += perPage;

      moreRecords = allScorecards.length < totalSize;
    }

    return allScorecards;
  }

  async getMediaIdByBatch(batchId: number) {
    const response: GetMediaIdsForBatch200Response =
      await this.scoringMediaServiceSDK.getMediaIdsForBatchAsPromise(batchId);

    return Number(response.result[0]);
  }

  async getScorecard(userId: number, scorecardId: number) {
    const getScorecard200Response =
      await this.scorecardSDKService.getScorecardAsPromise(scorecardId);
    const isAllowedToRead = await this.isUserAllowedToReadCreate(
      userId,
      getScorecard200Response.result.partnerId,
    );

    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to read the scorecard with id ${scorecardId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    return getScorecard200Response;
  }

  async updateScorecard(
    userId: number,
    scorecardId: number,
    updateScorecardsRequestDto: UpdateScorecardsRequestDto,
  ) {
    const getScorecard200Response =
      await this.scorecardSDKService.getScorecardAsPromise(scorecardId);

    const isAllowed = await this.isUserAllowedToUpdate(
      userId,
      getScorecard200Response.result,
    );

    if (!isAllowed) {
      const errorMessage = `User does not have the correct permissions to update the scorecard with id ${scorecardId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    return await this.scorecardSDKService.updateScorecardAsPromise(
      scorecardId,
      updateScorecardsRequestDto,
    );
  }

  async createScorecard(
    userId: number,
    createScorecardRequestDto: CreateScorecardRequestDto,
  ) {
    const isAllowed = await this.isUserAllowedToReadCreate(
      userId,
      createScorecardRequestDto.workspaceId,
    );

    if (!isAllowed) {
      const errorMessage = `User does not have the correct permissions to create the scorecard for workspace ${createScorecardRequestDto.workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    return await this.scorecardSDKService.createScorecardAsPromise(
      userId,
      createScorecardRequestDto,
    );
  }

  async createAdAccountScorecard(
    userId: number,
    createAdAccountScorecardRequestDto: CreateAdAccountScorecardRequestDto,
  ) {
    const { workspaceId } = createAdAccountScorecardRequestDto;
    const isAllowed = await this.isUserAllowedToReadCreate(userId, workspaceId);

    if (!isAllowed) {
      const errorMessage = `User does not have permission to create scorecard on workspace ${workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    return await this.scorecardSDKService.createAdAccountScorecardAsPromise(
      userId,
      createAdAccountScorecardRequestDto,
    );
  }

  /**
   * TODO This should ideally be done by the auth guards but since multiple permissions checks are not supported, we have to execute the query manually.
   * At some point, when the guards can do multiple checks, this logic needs to be removed from here.
   * @param userId
   * @param getScorecard200Response
   * @private
   */
  private async isUserAllowedToUpdate(
    userId: number,
    scorecardResponse: GetScorecardsResponseDto,
  ): Promise<boolean> {
    const { person, partnerId } = scorecardResponse;
    const isOwner = userId ? userId == person?.id : false; // Can't use === because userId is a number and person.id is a string

    if (isOwner) {
      return true;
    }

    const isUserOrgAdmin = await this.scoringAuthService.getIsUserOrgAdmin(
      userId,
      partnerId,
    );

    if (isUserOrgAdmin) {
      return true;
    }

    const permissionCheckResult =
      await this.scoringAuthService.checkPermissions(
        userId,
        partnerId,
        PermissionSubResource.CONFIGURATION,
        PermissionAction.UPDATE,
      );

    return permissionCheckResult.length > 0;
  }

  /**
   * TODO This should ideally be done by the auth guards but since multiple permissions checks are not supported, we have to execute the query manually.
   * At some point, when the guards can do multiple checks, this logic needs to be removed from here.
   * 1. Checks if the user has READ DETAILS permission for the partner
   * 2. Checks if the user is a partner manager for the partner
   * @param userId
   * @param createScorecardRequestDto
   * @private
   */
  async isUserAllowedToReadCreate(
    userId: number,
    workspaceId: number,
  ): Promise<boolean> {
    const isUserOrgAdmin = await this.scoringAuthService.getIsUserOrgAdmin(
      userId,
      workspaceId,
    );

    if (isUserOrgAdmin) {
      return true;
    }

    const permissionCheckResult =
      await this.scoringAuthService.checkPermissions(
        userId,
        workspaceId,
        PermissionSubResource.DETAILS,
        PermissionAction.READ,
      );

    if (permissionCheckResult.length > 0) {
      return true;
    }

    const isPartnerManager = await this.scoringAuthService.isUserPartnerManager(
      userId,
      workspaceId,
    );

    return isPartnerManager;
  }

  async getMediaForScorecard(
    scorecardId: number,
    workspaceId: number,
    mediaId: number,
    userId: number,
  ): Promise<any> {
    await this.scoringAuthService.canUserAccessPartnerForScorecard(
      userId,
      scorecardId,
      workspaceId,
    );

    return await this.organizationMediaServiceSDK.getMediaByIdAsPromise(
      mediaId,
    );
  }

  async getEarliestAdAccountImpressionDate(scorecardId: number) {
    return await this.scorecardSDKService.getEarliestImpressionDateAsPromise(
      scorecardId,
    );
  }

  async syncMediaAndScoreBatch(
    userId: number,
    scorecardId: number,
    mediaIds: number[] = [],
  ) {
    const scorecard = (
      await this.scorecardSDKService.getScorecardAsPromise(scorecardId)
    ).result;

    const isAllowed = await this.isUserAllowedToUpdate(userId, scorecard);

    if (!isAllowed) {
      const errorMessage = `User does not have the correct permissions to score the batch with id ${scorecardId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    if (scorecard.batchType === 'PRE_FLIGHT') {
      // will either map media provided in the request OR——if none provided——will sync from partner asset folder
      await this.scorecardSDKService.addMediaToBatchAsPromise({
        scorecardId,
        mediaIds,
      });
    }

    // send batch for scoring——RESUBMITTED for existing, SUBMITTED for new
    return await this.scorecardSDKService.scoreBatchAsPromise({ scorecardId });
  }
}
