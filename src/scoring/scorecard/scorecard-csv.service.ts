import { Response } from 'express';
import * as fastCsv from 'fast-csv';
import { Stream } from 'stream';
import { Injectable, Logger } from '@nestjs/common';
import {
  ReadScoreRequestDto,
  ScoreService as ScoreSDKService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { GetMediaScoreDetails200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getMediaScoreDetails200Response';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { PermissionSubResource } from '../../auth/enums/permission.subresource.enum';
import { PermissionAction } from '../../auth/enums/permission.action.enum';
import { MediaScoreDetailsResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/mediaScoreDetailsResponseDto';
import { CriteriaStatusDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/criteriaStatusDto';
import { CHANNEL_ID_TO_NAME_MAP } from '../../reports/constants/constants';
import { InflightAggregateRequestDto } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/inflightAggregateRequestDto';

type ScorecardAggregateCriteriaResult = {
  criteriaId: string;
  criteriaDetails: {
    identifier: string;
    name: string;
    rule: string;
    platform: string;
    isOptional: number;
    applicability: string;
  };
};

type ScorecardAggregateScore200ResponseResult = {
  channel: string;
  aggregateScore: number;
  criteriaResults: ScorecardAggregateCriteriaResult[];
};

type ScorecardAggregateScore200Response = {
  status: string;
  result: ScorecardAggregateScore200ResponseResult[];
};

@Injectable()
export class ScorecardCsvService {
  private readonly logger = new Logger(ScorecardCsvService.name);
  private readonly COMMON_CSV_HEADERS = [
    'Platform',
    'Platform Compliance (%)',
    'Media Name',
    'Media ID',
    'Media Compliance (%)',
  ];
  private readonly BLANK_CELL = 'Intentionally left blank';
  private readonly INTERNAL_TO_CSV_OUTPUT_SCORE_MAP = {
    [CriteriaStatusDto.StatusEnum.Pass]: 'Yes',
    [CriteriaStatusDto.StatusEnum.Fail]: 'No',
    [CriteriaStatusDto.StatusEnum.NoData]: 'No Data',
  };

  constructor(
    private readonly scoreService: ScoreSDKService,
    private readonly scoringAuthService: ScoringAuthService,
  ) {}

  async createScorecardCsv(
    userId: number,
    res: Response,
    readScoreRequestDto: ReadScoreRequestDto,
  ): Promise<Stream | undefined> {
    const { scorecardId, workspaceId } = readScoreRequestDto;
    const isUserAuthorized = await this.checkScorecardPermissions(
      userId,
      workspaceId,
    );

    if (!isUserAuthorized) {
      res
        .status(403)
        .send(`User is not authorized to access scorecard: ${scorecardId}`);
      return;
    }

    try {
      const scorecardAggregateScores: ScorecardAggregateScore200Response =
        await this.getScorecardAggregateScores(readScoreRequestDto);
      const scorecardMediaScoreDetails: GetMediaScoreDetails200Response =
        await this.getScorecardMediaScoreDetails(readScoreRequestDto);

      const csvRows = this.buildScorecardCsvRows(
        scorecardAggregateScores,
        scorecardMediaScoreDetails,
      );

      const csvStream = new Stream.PassThrough();
      const filename = this.generateScorecardCsvFileName(scorecardId);

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader(
        'Content-Disposition',
        `attachment; filename="${filename}"`,
      );

      const csvHeaderRow = csvRows[0];
      const csvDataRows = csvRows.slice(1);

      const csvWriter = fastCsv.format({ headers: csvHeaderRow });
      csvWriter.pipe(csvStream);

      csvDataRows.forEach((dataRow) => {
        csvWriter.write(dataRow);
      });

      csvWriter.end();

      return csvStream.pipe(res);
    } catch (exc) {
      const status = exc.status ?? 500;
      const exceptionMessage = exc.response?.error?.message ?? String(exc);
      res
        .status(status)
        .send(
          `Error generating CSV file for scorecard: ${scorecardId}. Error: ${exceptionMessage}`,
        );
    }
  }

  private async checkScorecardPermissions(
    userId: number,
    workspaceId: number,
  ): Promise<boolean> {
    const isUserOrgAdmin = await this.scoringAuthService.getIsUserOrgAdmin(
      userId,
      workspaceId,
    );

    const permissionCheckResult =
      await this.scoringAuthService.checkPermissions(
        userId,
        workspaceId,
        PermissionSubResource.DETAILS,
        PermissionAction.READ,
      );

    const isUserPartnerMember = permissionCheckResult.length > 0;

    const isUserPartnerManager =
      await this.scoringAuthService.isUserPartnerManager(userId, workspaceId);

    return isUserOrgAdmin || isUserPartnerMember || isUserPartnerManager;
  }

  private buildScorecardCsvRows(
    scorecardAggregateScores: ScorecardAggregateScore200Response,
    scorecardMediaScoreDetails: GetMediaScoreDetails200Response,
  ): string[][] {
    const csvRows: string[][] = [[...this.COMMON_CSV_HEADERS]];
    const { result: aggregateScores } = scorecardAggregateScores;
    const { result: mediaScores } = scorecardMediaScoreDetails;

    this.buildMediaPlatformCsvRows(aggregateScores, mediaScores, csvRows);
    this.addCriteriaScores(csvRows, aggregateScores, mediaScores);
    this.appendPlatformComplianceRows(aggregateScores, csvRows);

    return csvRows;
  }

  private appendPlatformComplianceRows(
    aggregateScores: ScorecardAggregateScore200ResponseResult[],
    csvRows: string[][],
  ) {
    aggregateScores.forEach((aggregateScore) => {
      csvRows.push([
        this.getChannelName(aggregateScore.channel),
        String(aggregateScore.aggregateScore),
      ]);
    });
  }

  private toChannelEnum = (
    value: string,
  ): InflightAggregateRequestDto.ChannelEnum | undefined => {
    return Object.values(InflightAggregateRequestDto.ChannelEnum).includes(
      value.toLowerCase() as InflightAggregateRequestDto.ChannelEnum,
    )
      ? (value as InflightAggregateRequestDto.ChannelEnum)
      : undefined;
  };

  private getChannelName(channelId: string) {
    const enumValue = this.toChannelEnum(channelId);
    return enumValue ? CHANNEL_ID_TO_NAME_MAP[enumValue] : channelId;
  }

  private buildMediaPlatformCsvRows(
    aggregateScores: ScorecardAggregateScore200ResponseResult[],
    mediaScores: Array<MediaScoreDetailsResponseDto>,
    csvRows: string[][],
  ) {
    aggregateScores.forEach((aggregateScore) => {
      mediaScores.forEach((mediaScore) => {
        mediaScore.mediaDetailsWithScores.forEach((mediaDetailWithScores) => {
          const mediaScoreRow = [
            this.getChannelName(aggregateScore.channel),
            '',
            mediaDetailWithScores?.mediaObject?.displayName ?? '-', // display name can be undefined
            mediaDetailWithScores.mediaId.toString(),
            'calculate compliance', // filler which will be replaced with percentage
          ];

          csvRows.push(mediaScoreRow);
        });
      });
    });
  }

  private addCriteriaScores(
    csvRows: string[][],
    aggregateScores: ScorecardAggregateScore200ResponseResult[],
    mediaScores: MediaScoreDetailsResponseDto[],
  ) {
    const includedChannelIds = new Set(
      aggregateScores.map((aggScore) => aggScore.channel),
    );
    csvRows.forEach((csvRow, csvRowIndex) => {
      let passCount = 0;
      let totalScoreCount = 0;
      aggregateScores.forEach((aggregateScore) => {
        aggregateScore.criteriaResults.forEach((criteriaResult) => {
          if (
            !includedChannelIds.has(criteriaResult.criteriaDetails.platform)
          ) {
            return; // skip criteria unrelated to the channels assigned to the batch
          }

          const isOptional = criteriaResult.criteriaDetails.isOptional === 1;
          if (csvRowIndex === 0) {
            csvRow.push(
              this.getCriteriaColumnHeader(
                criteriaResult.criteriaDetails.name,
                criteriaResult.criteriaDetails.rule,
                isOptional,
              ),
            );
          } else {
            const rowChannelName = csvRow[0];
            const mediaId = csvRow[3];

            const criteriaChannelName = this.getChannelName(
              criteriaResult.criteriaDetails.platform,
            );
            if (criteriaChannelName === rowChannelName) {
              // find score
              let score = '';
              mediaScores.forEach((mediaScore) => {
                mediaScore.mediaDetailsWithScores.forEach(
                  (mediaDetailWithScores) => {
                    if (mediaDetailWithScores.mediaId.toString() === mediaId) {
                      const criteriaScore = mediaDetailWithScores.scores?.find(
                        (score) =>
                          score.criteriaId.toString() ==
                          criteriaResult.criteriaId,
                      );

                      //It would be nice if only applicable scores came back but that's not always happening currently
                      const mediaType = (
                        mediaDetailWithScores.mediaObject?.applicabilityType ||
                        'UNKNOWN'
                      ).toUpperCase();

                      const criteriaApplicability =
                        criteriaResult.criteriaDetails.applicability;
                      const isApplicable =
                        criteriaApplicability
                          .toUpperCase()
                          ?.split(',')
                          .includes(mediaType) ||
                        criteriaApplicability.trim().length === 0;

                      if (criteriaScore && isApplicable) {
                        score =
                          this.INTERNAL_TO_CSV_OUTPUT_SCORE_MAP[
                            criteriaScore.status
                          ] ?? '';
                        if (!isOptional) {
                          if (criteriaScore.status === 'PASS') {
                            passCount++;
                            totalScoreCount++;
                          }

                          if (criteriaScore.status == 'FAIL') {
                            totalScoreCount++;
                          }
                        }
                      }
                    }
                  },
                );
              });

              csvRow[4] = this.calculateOverallMediaScorePercentage(
                passCount,
                totalScoreCount,
              );

              csvRow.push(score);
            } else {
              csvRow.push(this.BLANK_CELL);
            }
          }
        });
      });
    });
  }

  private calculateOverallMediaScorePercentage(
    passCount: number,
    total: number,
  ): string {
    if (total) {
      const percentage = (passCount / total) * 100;
      const roundedPercentage = percentage.toFixed(2);

      return roundedPercentage.toString();
    }

    return '0';
  }

  private generateScorecardCsvFileName(scorecardId: number): string {
    const timestamp = new Date().toISOString();
    return `${scorecardId}_${timestamp}.csv`;
  }

  private async getScorecardAggregateScores(
    readScoreRequestDto: ReadScoreRequestDto,
  ): Promise<ScorecardAggregateScore200Response> {
    const { scorecardId } = readScoreRequestDto;
    try {
      return this.scoreService.getAggregateScoresAsPromise(
        readScoreRequestDto,
        0,
        Number.MAX_SAFE_INTEGER,
      );
    } catch (exc) {
      this.logger.error(
        `Failed to get aggregate scores when building CSV for scorecard: ${scorecardId}`,
      );
      throw exc;
    }
  }

  private async getScorecardMediaScoreDetails(
    readScoreRequestDto: ReadScoreRequestDto,
  ): Promise<GetMediaScoreDetails200Response> {
    const { scorecardId } = readScoreRequestDto;
    try {
      return await this.scoreService.getMediaScoreDetailsAsPromise(
        readScoreRequestDto,
        0,
        Number.MAX_SAFE_INTEGER,
      );
    } catch (exc) {
      this.logger.error(
        `Failed to get media score details when building CSV for scorecard: ${scorecardId}`,
      );
      throw exc;
    }
  }

  public getCriteriaColumnHeader(
    criteriaName: string,
    criteriaRule: string,
    isOptional: boolean,
  ): string {
    const sanitizeHeaderRegex = /,/gm;
    const sanitizeCriteriaColumnReplacementChar = '_';
    const optionalText = isOptional ? ' (Optional)' : '';
    const columnHeader = `"${criteriaName} / ${criteriaRule}${optionalText}"`;

    return columnHeader.replace(
      sanitizeHeaderRegex,
      sanitizeCriteriaColumnReplacementChar,
    );
  }
}
