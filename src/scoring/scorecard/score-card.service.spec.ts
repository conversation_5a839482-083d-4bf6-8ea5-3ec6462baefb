import { Test, TestingModule } from '@nestjs/testing';
import { ScoreCardService } from './score-card.service';
import { MediaService as MediaServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/media.service';
import {
  MediaService,
  ScorecardService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import { DataSource } from 'typeorm';
import { ProjectService } from '@vidmob/vidmob-studio-service-sdk';

describe('ScoreCardService', () => {
  let scoreCardService: ScoreCardService;
  const mockCanUserAccessPartnerForScorecard = jest.fn();
  const mockGetMediaByIdAsPromise = jest.fn();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScoreCardService,
        {
          provide: DataSource,
          useValue: {
            query: jest.fn(),
          },
        },
        {
          provide: MediaServiceSDK,
          useValue: { getMediaByIdAsPromise: mockGetMediaByIdAsPromise },
        },
        {
          provide: ScoringAuthService,
          useValue: {
            canUserAccessPartnerForScorecard:
              mockCanUserAccessPartnerForScorecard,
          },
        },
        {
          provide: MediaService,
          useValue: {},
        },
        {
          provide: ScorecardService,
          useValue: {},
        },
        {
          provide: ProjectService,
          useValue: {},
        },
      ],
    }).compile();

    scoreCardService = module.get<ScoreCardService>(ScoreCardService);
  });

  it('should fail to get media for scorecard if media is not in scorecard or workspace', async () => {
    mockCanUserAccessPartnerForScorecard.mockImplementationOnce(() => {
      throw new Error('The user does not have access to workspace 2222');
    });

    await expect(
      scoreCardService.getMediaForScorecard(1111, 2222, 3333, 44444),
    ).rejects.toThrow('The user does not have access to workspace 2222');
  });

  it('should return media if validation passes', async () => {
    const mockMediaResponse = {
      response: {
        id: 1111,
        name: 'test',
      },
    };
    mockCanUserAccessPartnerForScorecard.mockResolvedValue(2222);
    mockGetMediaByIdAsPromise.mockResolvedValue(mockMediaResponse);

    const response = await scoreCardService.getMediaForScorecard(
      1111,
      2222,
      3333,
      44444,
    );
    expect(response).toEqual(mockMediaResponse);
  });
});
