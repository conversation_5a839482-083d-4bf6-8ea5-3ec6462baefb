import { Test, TestingModule } from '@nestjs/testing';
import { ScorecardCsvService } from './scorecard-csv.service';
import { ScoreService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';

describe('Scorecard CSV Service', () => {
  let scorecardCsvService: ScorecardCsvService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ScorecardCsvService,
        {
          provide: ScoringAuthService,
          useValue: {
            getIsUserOrgAdmin: jest.fn(),
            checkPermissions: jest.fn(),
            isUserPartnerManager: jest.fn(),
          },
        },
        { provide: ScoreService, useValue: {} },
      ],
    }).compile();

    scorecardCsvService = module.get<ScorecardCsvService>(ScorecardCsvService);
  });

  it('should be defined', () => {
    expect(scorecardCsvService).toBeDefined();
  });
});
