import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  Res,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import {
  readDetails,
  readPartnerAssetFolderMedia,
} from '../scoring.permissions';
import { ApiParam, ApiSecurity, ApiTags, ApiResponse } from '@nestjs/swagger';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  CreateAdAccountScorecardRequestDto,
  CreateScorecardRequestDto,
  GetScorecardsQueryparamsDto,
  UpdateScorecardsRequestDto,
  GetScorecardsRequestDto,
  ReadScoreRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ScoreCardService } from './score-card.service';
import { ScorecardCsvService } from './scorecard-csv.service';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { GetOutputVideoScorecardsDto } from '@vidmob/vidmob-studio-service-sdk/dist/model/getOutputVideoScorecardsDto';

@ApiTags('Scorecard')
@ApiSecurity('Bearer Token')
@Controller('scorecard')
export class ScorecardController {
  constructor(
    private readonly scorecardService: ScoreCardService,
    private readonly scorecardCsvService: ScorecardCsvService,
  ) {}
  private readonly logger = new Logger(ScorecardController.name);

  /**
   * This endpoint gets a list of scorecards for a given workspace. It supports a series of filters through query params.
   */
  @Permissions(readDetails)
  @Get()
  async getScorecardsGet(
    @GetPagination() paginationOptions: PaginationOptions,
    @Query() getScorecardsQueryParamDto: GetScorecardsQueryparamsDto,
    @Request() req: any,
  ) {
    this.logger.debug('Input - getScorecards: ', {
      GetScorecardsQueryparamsDto,
      PaginationOptions,
    });
    const userId: number = req['userId'] as number;

    return await this.scorecardService.getScorecardsGetHandler(
      userId,
      paginationOptions,
      getScorecardsQueryParamDto,
    );
  }
  /**
   * Convenience endpoint to rescore batches (with media if provided)
   */
  @Post(':scorecardId/score')
  async scoreBatch(
    @Param('scorecardId', ParseIntPipe) scorecardId: number,
    @Request() req: any,
    @Body('mediaIds') mediaIds: number[],
  ) {
    return this.scorecardService.syncMediaAndScoreBatch(
      req['userId'] as number,
      scorecardId,
      mediaIds,
    );
  }

  /**
   * This endpoint gets a list of scorecard filter options for a given workspace.
   */
  @Get('/scorecard-options')
  async getScorecardOptions(
    @Query('workspaceId', ParseIntPipe) workspaceId: number,
    @Request() req: any,
    @Query('platforms') platforms?: string,
  ) {
    return await this.scorecardService.getScorecardOptions(
      workspaceId,
      platforms,
    );
  }

  /**
   * This endpoint allows the user to get details of a scorecard.
   */
  @ApiParam({
    name: 'scorecardId',
    type: 'number',
    required: true,
    description: 'System assigned id of the scorecard',
  })
  @Get('/:scorecardId')
  async getScorecard(
    @Param('scorecardId', ParseIntPipe) scorecardId: number,
    @Request() req: any,
  ) {
    this.logger.debug('Input - getScorecard: ', {
      scorecardId,
    });
    const userId: number = req['userId'] as number;

    return await this.scorecardService.getScorecard(userId, scorecardId);
  }

  /**
   * This endpoint allows the user to update scorecard details like name, markets, platforms etc.
   */
  @ApiParam({
    name: 'scorecardId',
    type: 'number',
    required: true,
    description: 'System assigned id of the scorecard',
  })
  @Patch('/:scorecardId')
  async updateScorecard(
    @Param('scorecardId', ParseIntPipe) scorecardId: number,
    @Body() updateScorecardsRequestDto: UpdateScorecardsRequestDto,
    @Request() req: any,
  ) {
    this.logger.debug('Input - updateScorecard: ', {
      updateScorecardsRequestDto,
      scorecardId,
    });
    const userId: number = req['userId'] as number;

    return await this.scorecardService.updateScorecard(
      userId,
      scorecardId,
      updateScorecardsRequestDto,
    );
  }

  /**
   * This endpoint creates a CSV file with scorecard details.
   * @param scorecardId
   * @param readScoreRequestDto
   * @param req
   * @param res
   */
  @Post('/:scorecardId/csv')
  async downloadScorecardCsv(
    @Param('scorecardId', ParseIntPipe) scorecardId: number,
    @Body(new ValidationPipe()) readScoreRequestDto: ReadScoreRequestDto,
    @Request() req: any,
    @Res() res: Response,
  ) {
    const userId: number = req['userId'] as number;
    return await this.scorecardCsvService.createScorecardCsv(
      userId,
      res,
      readScoreRequestDto,
    );
  }

  @Post('/in-flight')
  async createInFlightScorecard(
    @Request() req: any,
    @Body()
    createAdAccountScorecardRequestDto: CreateAdAccountScorecardRequestDto,
  ) {
    this.logger.debug('Input - createInFlightScorecard: ', {
      createAdAccountScorecardRequestDto,
    });
    const userId: number = req['userId'] as number;
    return await this.scorecardService.createAdAccountScorecard(
      userId,
      createAdAccountScorecardRequestDto,
    );
  }

  /**
   * This endpoint gets a list of scorecards for a given workspace. It supports a series of filters through query params.
   */
  @Permissions(readDetails)
  @Post('/:workspaceId')
  async getScorecardsPost(
    @Param('workspaceId') workspaceId: number,
    @Body() getScorecardsRequestDto: GetScorecardsRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
    @Request() req: any,
  ) {
    this.logger.debug('Input - getScorecards: ', {
      GetScorecardsQueryparamsDto,
      PaginationOptions,
    });
    const userId: number = req['userId'] as number;

    return await this.scorecardService.getScorecardsPostHandler(
      userId,
      workspaceId,
      paginationOptions,
      getScorecardsRequestDto,
    );
  }

  /**
   * This endpoint gets a list of scorecards with project details for a given workspace. It supports a series of filters through body params
   *
   */
  @Permissions(readDetails)
  @Post('/:workspaceId/project-detail')
  async getScorecardsWithProjectDetails(
    @Param('workspaceId') workspaceId: number,
    @Body() body: any,
    @Request() req: any,
  ) {
    const userId: number = req['userId'] as number;
    const getScorecardsRequestDto: GetScorecardsRequestDto = body;
    const projectIdsFilter: (number | string)[] = body.projects || [];

    this.logger.debug('Input - getScorecardsWithProjectDetails: ', {
      getScorecardsRequestDto,
      projectIdsFilter,
    });

    return await this.scorecardService.getScorecardWithProjectDetails(
      userId,
      workspaceId,
      getScorecardsRequestDto,
      projectIdsFilter,
    );
  }

  /**
   * This endpoint allows the user to create a scorecard.
   */
  @Post()
  async createScoreCard(
    @Body() createScorecardRequestDto: CreateScorecardRequestDto,
    @Request() req: any,
  ) {
    this.logger.debug('Input - createScoreCard: ', {
      createScorecardRequestDto,
    });
    const userId: number = req['userId'] as number;

    return await this.scorecardService.createScorecard(
      userId,
      createScorecardRequestDto,
    );
  }

  /**
   * This endpoint returns a paginated list of scorecards for each output video in a project.
   *
   * @param projectId - The ID of the project
   * @param paginationOptions - Pagination options including offset and limit
   * @returns A paginated list of scorecards per output video
   */
  @Get('/:projectId/outputVideo')
  @ApiParam({
    name: 'projectId',
    type: 'number',
    description: 'ID of the project',
  })
  @ApiResponse({
    status: 200,
    description: 'Paginated list of scorecards per output video',
  })
  async getOutputVideoScorecardsCount(
    @Param('projectId') projectId: number,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<GetOutputVideoScorecardsDto>> {
    return this.scorecardService.getOutputVideoScorecardCounts(
      projectId,
      paginationOptions,
    );
  }

  /**
   * This endpoint return platformMediaId based on mediaId.
   */
  @Get('platform-media/:mediaId')
  async getPlatformMediaId(
    @Request() req: any,
    @Param('mediaId') mediaId: number,
  ): Promise<{ platformMediaId: string | null }> {
    const { userId } = req;
    return await this.scorecardService.getPlatformMediaId(mediaId, userId);
  }

  /**
   * Endpoint to fetch media for a scorecard
   */
  @Get('workspace/:workspaceId/scorecard/:scorecardId/media/:mediaId')
  @Permissions(readPartnerAssetFolderMedia)
  async getMediaForScorecard(
    @Param('scorecardId', ParseIntPipe) scorecardId: number,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param('mediaId', ParseIntPipe) mediaId: number,
    @Request() request: any,
  ) {
    return await this.scorecardService.getMediaForScorecard(
      scorecardId,
      workspaceId,
      mediaId,
      request.userId,
    );
  }

  /**
   * Endpoint to get earliest media impression date for ad-account scorecards
   */
  @Get(':scorecardId/earliest-impression-date')
  async getEarliestAdAccountImpressionDate(
    @Param('scorecardId', ParseIntPipe) scorecardId: number,
  ) {
    return await this.scorecardService.getEarliestAdAccountImpressionDate(
      scorecardId,
    );
  }
}
