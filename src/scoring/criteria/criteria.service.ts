import {
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  AttachIconsToCriteriaDto,
  CriteriaAttributesDTO,
  CriteriaCreateDto,
  CriteriaSetService,
  DetachIconsFromCriteriaDto,
  GetCriteriaOptions200Response,
  GetCriteriaQueryParamsDto,
  ReadCriteriaDto,
  ReadCriteriaSetDto,
  ScoringCriteriaService,
  ScoringCriteriaTemplatesService,
  SearchCriteriaDto,
  UpdateCriteriaDto,
  UploadCustomIconDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { catchError, firstValueFrom, map } from 'rxjs';
import { rethrowAxiosError } from '../api.utils';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { GetWorkspaceCriteriaSets200Response } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/getWorkspaceCriteriaSets200Response';
import { ScoreCardService } from '../scorecard/score-card.service';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import * as fastcsv from 'fast-csv';
import { PassThrough } from 'stream';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import dayjs from 'dayjs';
import {
  transformMediaTypes,
  transformPlatform,
} from '../../utils/string-transformations';
import GlobalStatusesEnum = GetCriteriaQueryParamsDto.GlobalStatusesEnum;

@Injectable()
export class CriteriaService {
  private readonly logger = new Logger(CriteriaService.name);

  constructor(
    private readonly scoringCriteriaService: ScoringCriteriaService,
    private readonly scoringCriteriaTemplatesService: ScoringCriteriaTemplatesService,
    private readonly scoringCriteriaSetsService: CriteriaSetService,
    private readonly scorecardService: ScoreCardService,
    private readonly scoringCriteriaServiceV2: ScoringCriteriaService,
    private readonly scoringAuthService: ScoringAuthService,
    private readonly workspaceService: WorkspaceService,
  ) {}

  async deleteCriteria(
    workspaceId: number,
    criteriaId: number,
    userId: number,
  ) {
    const canDeleteGlobal = await this.scoringAuthService.getIsUserOrgAdmin(
      userId,
      workspaceId,
    );
    return this.scoringCriteriaService.deleteCriteriaAsPromise(
      workspaceId,
      criteriaId,
      canDeleteGlobal,
    );
  }

  async createCriteria(
    personId: number,
    workspaceId: number,
    createCriteriaDto: CriteriaCreateDto,
  ) {
    const canCreateGlobal = await this.scoringAuthService.getIsUserOrgAdmin(
      personId,
      workspaceId,
    );

    if (createCriteriaDto.isGlobal && !canCreateGlobal) {
      // Only org admins can create global criteria
      throw new ForbiddenException(
        `User ${personId} does not have permission to create global criteria`,
      );
    }

    createCriteriaDto.personId = personId;

    // Users type in spaces before/after custom name and then complain of criteria duplication on reports
    // See VID-3019
    if (createCriteriaDto.name) {
      createCriteriaDto.name = createCriteriaDto.name.trim();
    }

    return this.scoringCriteriaService
      .createCriteria(workspaceId, createCriteriaDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  getPartnerCriteriaTemplates(
    partnerId: number,
    excludeDeprecatedCriteriaTemplates = false,
  ) {
    return this.scoringCriteriaTemplatesService
      .getPartnerCriteriaTemplates(
        partnerId,
        excludeDeprecatedCriteriaTemplates,
      )
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  findCriteria(organizationId: string, searchQueryDto: SearchCriteriaDto) {
    this.scoringCriteriaService
      .searchCriteria(organizationId, searchQueryDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  public async getCriteriaForListOfWorkspaces(
    workspaceIds: string[],
    sortOrder: 'ASC' | 'DESC' = 'ASC',
    sortBy: string,
    platformsString: string,
    applicabilityTypesString = '',
  ) {
    const identifier = undefined;
    const isOptional = undefined;
    const ownerIds = undefined;
    const searchText = undefined;
    const globalStatuses = undefined;
    const categories = undefined;
    const startDate = undefined;
    const endDate = undefined;
    const criteriaGroupIds = undefined;
    const offset = 0;
    const perPage = Number.MAX_SAFE_INTEGER;
    return await this.scoringCriteriaService.getCriteriaForListOfWorkspacesAsPromise(
      workspaceIds,
      sortOrder,
      identifier,
      sortBy,
      platformsString,
      applicabilityTypesString,
      isOptional,
      ownerIds,
      searchText,
      globalStatuses,
      categories,
      startDate,
      endDate,
      criteriaGroupIds,
      offset,
      perPage,
    );
  }

  async getCriteriaAsCsv(
    userId: number,
    workspaceIds: number[],
    criteriaParams: GetCriteriaQueryParamsDto,
  ): Promise<string> {
    const BATCH_SIZE = 1000;
    let allCriteria: ReadCriteriaDto[] = [];

    for (const workspaceId of workspaceIds) {
      const workspace = await this.workspaceService.getWorkspaceById(
        workspaceId,
      );
      const workspaceName = workspace?.name || 'Unknown Workspace';

      let criteriaForThisWorkspace: ReadCriteriaDto[] = [];
      const criteriaSetIds = await this.getDefaultAndGlobalCriteriaSetIds(
        userId,
        workspaceId,
      );

      let offset = 0;
      let totalSize = Infinity;

      do {
        const workspaceCriteria =
          await this.scoringCriteriaSetsService.getCriteriaInCriteriaSetsAsPromise(
            criteriaSetIds,
            criteriaParams.sortOrder,
            undefined, // identifier
            criteriaParams.sortBy,
            criteriaParams.platforms as unknown as string,
            criteriaParams.mediaTypes as unknown as string,
            criteriaParams.isOptional as unknown as string,
            criteriaParams.ownerIds,
            criteriaParams.searchText,
            undefined,
            criteriaParams.categories,
            criteriaParams.startDate,
            criteriaParams.endDate,
            criteriaParams.criteriaGroupIds,
            offset,
            BATCH_SIZE,
          );

        const { result, pagination } = workspaceCriteria;

        criteriaForThisWorkspace = criteriaForThisWorkspace.concat(result);

        if (pagination) {
          offset = pagination.nextOffset || offset + pagination.perPage;
          totalSize = pagination.totalSize;
        } else {
          break;
        }
      } while (offset < totalSize);

      const criteriaWithWorkspaceName = criteriaForThisWorkspace.map(
        (item) => ({
          ...item,
          workspaceName,
        }),
      );

      allCriteria = allCriteria.concat(criteriaWithWorkspaceName);
    }

    return this.generateCsv(allCriteria);
  }

  private async getDefaultAndGlobalCriteriaSetIds(
    userId: number,
    workspaceId: number,
  ): Promise<string[]> {
    const isAllowedToRead =
      await this.scorecardService.isUserAllowedToReadCreate(
        userId,
        workspaceId,
      );

    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of criteria for workspace ${workspaceId}`;
      throw new ForbiddenException(errorMessage);
    }

    const criteriaSets =
      await this.scoringCriteriaSetsService.getWorkspaceCriteriaSetsAsPromise(
        workspaceId,
      );

    return criteriaSets.result.map((set) => set.id.toString());
  }

  private async generateCsv(records: any[]): Promise<string> {
    const getRuleText = (record: any) => {
      if (!record.rule) {
        return 'N/A';
      }
      return record.isBestPractice
        ? `Channel best practice: ${record.rule}`
        : record.rule;
    };

    return new Promise((resolve, reject) => {
      const csvStream = fastcsv.format({
        headers: [
          'Workspace',
          'Name',
          'Rule',
          'Channel',
          'Creative Type',
          'Consideration',
          'Organization Criteria',
          'Category',
          'Criteria Groups',
          'Date Added',
          'Added By',
        ],
      });

      const output = new PassThrough();
      let csvString = '';

      output.on('data', (chunk) => (csvString += chunk.toString()));
      output.on('end', () => resolve(csvString));
      output.on('error', (error) => reject(error));

      records.forEach((record) =>
        csvStream.write({
          Workspace: record.workspaceName,
          Name: record.name,
          Rule: getRuleText(record),
          Channel: record.platform ? transformPlatform(record.platform) : 'N/A',
          'Creative Type': Array.isArray(record.mediaTypes)
            ? transformMediaTypes(record.mediaTypes).join(', ')
            : 'N/A',
          Consideration: record.isOptional ? 'Optional' : 'Mandatory',
          'Organization Criteria': record.criteriaSet?.isGlobal ? 'Yes' : 'No',
          Category: record.category || 'N/A',
          'Criteria Groups': record.criteriaGroups.length
            ? record.criteriaGroups.map((cg: any) => cg.name).join(', ')
            : 'Unassigned',
          'Date Added': record.dateCreated
            ? dayjs(record.dateCreated).format('MMM DD, YYYY')
            : 'N/A',
          'Added By': `${record.owner?.firstName || 'Unknown'} ${
            record.owner?.lastName || 'User'
          }`,
        }),
      );

      csvStream.end();
      csvStream.pipe(output);
    });
  }

  async updateCriteria(
    criteriaId: number,
    updateCriteriaDto: UpdateCriteriaDto,
    userId: number,
    workspaceId: number,
  ) {
    const canUpdateGlobal = await this.scoringAuthService.getIsUserOrgAdmin(
      userId,
      workspaceId,
    );

    updateCriteriaDto.workspaceId = workspaceId;

    return this.scoringCriteriaService.updateCriteriaAsPromise(
      criteriaId,
      canUpdateGlobal,
      updateCriteriaDto,
    );
  }

  deleteCriterion(id: number) {
    return this.scoringCriteriaService.deleteCriterion(id).pipe(
      map((axiosResponse) => axiosResponse.data.result),
      catchError((err) => rethrowAxiosError(err)),
    );
  }

  async getWorkspaceCriteriaOptions(
    userId: number,
    workspaceId: number,
    includeGlobalOptions?: boolean,
  ): Promise<GetCriteriaOptions200Response> {
    const isAllowedToRead =
      await this.scorecardService.isUserAllowedToReadCreate(
        userId,
        workspaceId,
      );

    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of criteria for workspace ${workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    return this.scoringCriteriaServiceV2.getCriteriaOptionsAsPromise(
      workspaceId,
      Boolean(includeGlobalOptions),
    );
  }

  async decorateCriteriaWithSignedUrls(criteria: ReadCriteriaDto[]) {
    const { criteriaRequestPayload, originalCriteriaById } = criteria.reduce(
      (acc, item) => {
        acc.criteriaRequestPayload.push({
          id: item.id,
          customIconId: item.customIconId ?? '',
        });
        acc.originalCriteriaById[item.id] = item;
        return acc;
      },
      {
        criteriaRequestPayload: [] as { id: number; customIconId: string }[],
        originalCriteriaById: {} as Record<number, ReadCriteriaDto>,
      },
    );

    // Call the service with simplified payload
    const criteriaWithSignedUrls =
      await this.scoringCriteriaService.appendSignedUrlsToCriteriaAsPromise({
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        criteria: criteriaRequestPayload,
      });

    // Merge signed URLs back into the full criteria
    return criteriaWithSignedUrls.result.map(
      (item: { id: number; customIconUrl: string }) => ({
        ...originalCriteriaById[item.id],
        customIconUrl: item.customIconUrl,
      }),
    );
  }

  async getWorkspaceCriteriaList(
    userId: number,
    workspaceId: number,
    paginationOptions: PaginationOptions,
    queryParams: GetCriteriaQueryParamsDto,
  ) {
    const { globalStatuses } = queryParams;
    const isAllowedToRead =
      await this.scorecardService.isUserAllowedToReadCreate(
        userId,
        workspaceId,
      );
    if (!isAllowedToRead) {
      const errorMessage = `User does not have the correct permissions to get list of criteria for workspace ${workspaceId}`;
      this.logger.error(errorMessage);
      throw new ForbiddenException(errorMessage);
    }

    const criteriaSets: GetWorkspaceCriteriaSets200Response =
      await this.scoringCriteriaSetsService.getWorkspaceCriteriaSetsAsPromise(
        workspaceId,
      );

    const defaultCriteriaSet: ReadCriteriaSetDto | undefined =
      criteriaSets.result.find(
        (value) => value.name === GlobalStatusesEnum.Default,
      );

    if (!defaultCriteriaSet) {
      const message =
        'No default criteria set found for workspace id: ' + workspaceId;
      this.logger.error(message);
      throw new NotFoundException(message);
    }

    const criteriaSetIds = [];

    if (
      globalStatuses?.includes(GlobalStatusesEnum.Default) ||
      !globalStatuses
    ) {
      criteriaSetIds.push(defaultCriteriaSet.id?.toString());
    }

    const globalCriteriaSet: ReadCriteriaSetDto | undefined =
      criteriaSets.result.find(
        (value) =>
          value.workspaceId === null &&
          value.name === GlobalStatusesEnum.Global,
      );

    if (
      globalCriteriaSet &&
      (globalStatuses?.includes(GlobalStatusesEnum.Global) || !globalStatuses)
    ) {
      criteriaSetIds.push(globalCriteriaSet.id?.toString());
    }

    const {
      mediaTypes,
      platforms,
      sortBy,
      sortOrder,
      isOptional,
      ownerIds,
      searchText,
      categories,
      startDate,
      endDate,
      criteriaGroupIds,
    } = queryParams;

    // TS incorrectly thinks these values should have been transformed into arrays by the DTO.
    // But those transforms only happen in the scoring service. Which is why the below assertions are needed for now.

    const platformsStr: string = platforms as unknown as string;
    const mediaTypesStr: string = mediaTypes as unknown as string;
    const isOptionalStr: string = isOptional as unknown as string;

    const workspaceCriteria =
      await this.scoringCriteriaSetsService.getCriteriaInCriteriaSetsAsPromise(
        criteriaSetIds,
        sortOrder,
        undefined, // a default for 'identifier'
        sortBy,
        platformsStr,
        mediaTypesStr,
        isOptionalStr,
        ownerIds,
        searchText,
        undefined,
        categories,
        startDate,
        endDate,
        criteriaGroupIds,
        paginationOptions!.offset,
        paginationOptions.perPage,
      );

    const { result } = workspaceCriteria;

    // Fetch signed URLs and merge back into criteria objects
    const fullCriteriaWithSignedUrls =
      await this.decorateCriteriaWithSignedUrls(result);

    return { ...workspaceCriteria, result: fullCriteriaWithSignedUrls };
  }

  async getMultipleWorkspaceCriteriaList(
    userId: number,
    workspaceIds: number[],
    pagination: PaginationOptions,
    queryParams: GetCriteriaQueryParamsDto,
  ) {
    for (const workspaceId of workspaceIds) {
      const allowed = await this.scorecardService.isUserAllowedToReadCreate(
        userId,
        workspaceId,
      );
      if (!allowed) {
        throw new ForbiddenException(
          `User ${userId} lacks permissions for workspace ${workspaceId}`,
        );
      }
    }

    const criteriaSetIds = new Set<string>();
    let globalAdded = false;

    for (const workspaceId of workspaceIds) {
      const setsResp =
        await this.scoringCriteriaSetsService.getWorkspaceCriteriaSetsAsPromise(
          workspaceId,
        );

      const defaultSet = setsResp.result.find(
        (s) => s.name === GlobalStatusesEnum.Default,
      );
      if (
        defaultSet &&
        (!queryParams.globalStatuses ||
          queryParams.globalStatuses.includes(GlobalStatusesEnum.Default))
      ) {
        criteriaSetIds.add(defaultSet.id!.toString());
      }

      if (!globalAdded) {
        const globalSet = setsResp.result.find(
          (s) => s.workspaceId === null && s.name === GlobalStatusesEnum.Global,
        );
        if (
          globalSet &&
          (!queryParams.globalStatuses ||
            queryParams.globalStatuses.includes(GlobalStatusesEnum.Global))
        ) {
          criteriaSetIds.add(globalSet.id!.toString());
          globalAdded = true;
        }
      }
    }

    const platformsStr = queryParams.platforms as unknown as string;
    const mediaTypesStr = queryParams.mediaTypes as unknown as string;
    const optionalStr = queryParams.isOptional as unknown as string;

    const criteriaResp =
      await this.scoringCriteriaSetsService.getCriteriaInCriteriaSetsAsPromise(
        Array.from(criteriaSetIds),
        queryParams.sortOrder,
        undefined, // default identifier
        queryParams.sortBy,
        platformsStr,
        mediaTypesStr,
        optionalStr,
        queryParams.ownerIds,
        queryParams.searchText,
        undefined,
        queryParams.categories,
        queryParams.startDate,
        queryParams.endDate,
        queryParams.criteriaGroupIds,
        pagination.offset,
        pagination.perPage,
      );

    const decorated = await this.decorateCriteriaWithSignedUrls(
      criteriaResp.result,
    );
    return { ...criteriaResp, result: decorated };
  }

  getCriteriaDetails(
    organizationId: string,
    criteriaAttributesDto: CriteriaAttributesDTO,
  ) {
    return this.scoringCriteriaService
      .getCriteriaDetails(organizationId, criteriaAttributesDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  async getCriteriaDetailsAsPromise(
    organizationId: string,
    criteriaAttributesDto: CriteriaAttributesDTO,
  ) {
    return await firstValueFrom(
      this.getCriteriaDetails(organizationId, criteriaAttributesDto),
    );
  }

  async getBestPracticesSummaryForWorkspace(
    workspaceId: number,
    channels = '',
  ) {
    return await this.scoringCriteriaServiceV2.getCurrentWorkspaceBestPracticesAsPromise(
      workspaceId,
      channels,
    );
  }

  async getOutdatedBestPracticesForWorkspace(
    workspaceId: number,
    channels = '',
  ) {
    return await this.scoringCriteriaService.getOutdatedWorkspaceBestPracticesAsPromise(
      workspaceId,
      channels,
    );
  }

  async createWorkspaceBestPractices(
    userId: number,
    workspaceId: number,
    bestPracticeDefinitionIds: number[],
  ) {
    return await this.scoringCriteriaService.createWorkspaceBestPracticesAsPromise(
      userId,
      {
        workspaceId,
        bestPracticeDefinitionIds,
      },
    );
  }

  async deleteOutdatedWorkspaceBestPractices(
    workspaceId: number,
    bestPracticeDefinitionIds: number[],
  ) {
    return await this.scoringCriteriaService.deleteOutdatedWorkspaceBestPracticesAsPromise(
      {
        workspaceId,
        bestPracticeDefinitionIds,
      },
    );
  }

  async getOrgCustomIcons(organizationId: string) {
    return await this.scoringCriteriaService.getCustomIconsForOrganizationAsPromise(
      organizationId,
    );
  }

  async uploadCustomIcon(
    organizationId: string,
    uploadDetails: UploadCustomIconDto,
  ) {
    return await this.scoringCriteriaService.uploadCustomIconAsPromise(
      organizationId,
      uploadDetails,
    );
  }

  async attachIconsToCriteria(iconMappings: AttachIconsToCriteriaDto) {
    return await this.scoringCriteriaService.attachIconsToCriteriaAsPromise(
      iconMappings,
    );
  }

  async detachIconsFromCriteria(idsToDetach: DetachIconsFromCriteriaDto) {
    return await this.scoringCriteriaService.detachIconsFromCriteriaAsPromise(
      idsToDetach,
    );
  }
}
