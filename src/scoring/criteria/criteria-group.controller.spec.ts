import { Test, TestingModule } from '@nestjs/testing';
import { CriteriaGroupController } from './criteria-group.controller';
import { CriteriaGroupService } from './criteria-group.service';
import { OrganizationService as OrganizationServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/organization.service';
import { CreateCriteriaGroup } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/createCriteriaGroup';
import { ReadCriteriaGroup } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/readCriteriaGroup';

describe('CriteriaGroupController', () => {
  let controller: CriteriaGroupController;
  const mockValidateWorkspacesWithinOrganizationAsPromise = jest.fn();
  const mockCreateCriteriaGroup = jest.fn();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CriteriaGroupController],
      providers: [
        {
          provide: CriteriaGroupService,
          useValue: {
            getCriteriaGroups: jest.fn(),
            getSingleCriteriaGroup: jest.fn(),
            createCriteriaGroup: mockCreateCriteriaGroup,
            editCriteriaGroup: jest.fn(),
            deleteCriteriaGroup: jest.fn(),
          },
        },
        {
          provide: OrganizationServiceSDK,
          useValue: {
            validateWorkspacesWithinOrganizationAsPromise:
              mockValidateWorkspacesWithinOrganizationAsPromise,
          },
        },
      ],
    }).compile();

    controller = module.get<CriteriaGroupController>(CriteriaGroupController);
  });

  it('should fail to createCriteriaGroup if workspace is not in organization', async () => {
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: { success: false },
    });

    await expect(
      controller.createCriteriaGroup(
        'mockOrgId',
        11111,
        {} as CreateCriteriaGroup,
        {} as any,
      ),
    ).rejects.toThrowError(
      'Workspace ids are not valid for organization mockOrgId',
    );
  });

  it('should call service createCriteriaGroup if workspace is valid for organization', async () => {
    const mockReadCriteriaGroup: ReadCriteriaGroup = {
      id: 'mockCriteriaGroupId',
      name: 'mockCriteriaGroupName',
      color: '#000000',
      organizationId: 'mockOrgId',
      dateCreated: new Date().toDateString(),
      lastUpdated: new Date().toDateString(),
      createdBy: {
        id: 2222,
        displayName: 'mockUserName',
        firstName: 'mockFirstName',
        lastName: 'mockLastName',
        photoUrl: 'http://mockUrl.com',
      },
    };
    const mockResponse = {
      status: 'OK',
      result: mockReadCriteriaGroup,
    };
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: { success: true },
    });
    mockCreateCriteriaGroup.mockResolvedValue(mockResponse);

    const response = await controller.createCriteriaGroup(
      'mockOrgId',
      11111,
      {} as CreateCriteriaGroup,
      {} as any,
    );

    expect(response).toEqual(mockResponse);
  });

  it('should fail to updateCriteriaGroup if workspace is not in organization', async () => {
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: { success: false },
    });

    await expect(
      controller.updateCriteriaGroup(
        'mockOrgId',
        'mockCriteriaGroupId',
        11111,
        {} as CreateCriteriaGroup,
        {} as any,
      ),
    ).rejects.toThrowError(
      'Workspace ids are not valid for organization mockOrgId',
    );
  });

  it('should fail to deleteCriteriaGroup if workspace is not in organization', async () => {
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: { success: false },
    });

    await expect(
      controller.deleteCriteriaGroup(
        'mockOrgId',
        'mockCriteriaGroupId',
        11111,
        {} as any,
      ),
    ).rejects.toThrowError(
      'Workspace ids are not valid for organization mockOrgId',
    );
  });
});
