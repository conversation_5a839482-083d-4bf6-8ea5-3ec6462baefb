import { Injectable, Logger } from '@nestjs/common';
import {
  CreateCriteriaSetDto,
  SearchCriteriaSetDto,
  UpdateCriteriaSetDto,
  CriteriaSetService as ScoringCriteriaSetService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { catchError, map } from 'rxjs';
import { rethrowAxiosError } from '../api.utils';

@Injectable()
export class CriteriaSetService {
  private readonly logger = new Logger(CriteriaSetService.name);

  constructor(
    private readonly scoringCriteriaSetsService: ScoringCriteriaSetService,
  ) {}

  createCriteriaSet(createCriteriaSetDto: CreateCriteriaSetDto) {
    return this.scoringCriteriaSetsService
      .createCriteriaSet(createCriteriaSetDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }
  getCriteriaSets(
    organizationId: string,
    searchCriteriaSetDto: SearchCriteriaSetDto,
  ) {
    return this.scoringCriteriaSetsService
      .searchCriteriaSets(organizationId, searchCriteriaSetDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  updateCriteriaSet(id: number, updateCriteriaSetDto: UpdateCriteriaSetDto) {
    return this.scoringCriteriaSetsService
      .updateCriteriaSet(id, updateCriteriaSetDto)
      .pipe(
        map((axiosResponse) => axiosResponse.data.result),
        catchError((err) => rethrowAxiosError(err)),
      );
  }

  deleteCriteriaSet(id: number) {
    return this.scoringCriteriaSetsService.deleteCriteriaSet(id).pipe(
      map((axiosResponse) => axiosResponse.data.result),
      catchError((err) => rethrowAxiosError(err)),
    );
  }
}
