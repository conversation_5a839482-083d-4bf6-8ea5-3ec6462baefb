import { Test, TestingModule } from '@nestjs/testing';
import {
  CreateCriteriaSetDto,
  CriteriaSetService as ScoringCriteriaSetsService,
  SearchCriteriaSetDto,
  UpdateCriteriaSetDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { of } from 'rxjs';
import { CriteriaSetService } from './criteria-set.service';

describe('CriteriaSetService', () => {
  let service: CriteriaSetService;
  let scoringCriteriaSetsService: ScoringCriteriaSetsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CriteriaSetService,
        {
          provide: ScoringCriteriaSetsService,
          useValue: {
            createCriteriaSet: jest.fn(),
            getCriteriaSets: jest.fn(),
            updateCriteriaSet: jest.fn(),
            deleteCriteriaSet: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CriteriaSetService>(CriteriaSetService);
    scoringCriteriaSetsService = module.get<ScoringCriteriaSetsService>(
      ScoringCriteriaSetsService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  const criteriaId = 456;
  const dummyResponse = 'dummy response';
  const axiosResponse = { data: { result: dummyResponse } };

  it('should call ScoringCriteriaSetService.createCriteriaSet', async () => {
    const testDto: CreateCriteriaSetDto = {
      name: 'test',
      associations: [],
    };
    scoringCriteriaSetsService.createCriteriaSet = jest
      .fn()
      .mockReturnValue(of(axiosResponse));
    const result = await service.createCriteriaSet(testDto).toPromise();
    expect(result).toEqual(dummyResponse);
    expect(scoringCriteriaSetsService.createCriteriaSet).toHaveBeenCalledWith(
      testDto,
    );
  });

  it('should call ScoringCriteriaSetService.getCriteriaSets', async () => {
    const testDto: SearchCriteriaSetDto = {
      criteriaSetIds: [1, 2, 3],
      workspaceIds: [1, 2, 3],
      isMandatory: true,
    };
    const organizationId = '1';
    scoringCriteriaSetsService.searchCriteriaSets = jest
      .fn()
      .mockReturnValue(of(axiosResponse));
    const result = await service
      .getCriteriaSets(organizationId, testDto)
      .toPromise();
    expect(result).toEqual(dummyResponse);
    expect(scoringCriteriaSetsService.searchCriteriaSets).toHaveBeenCalledWith(
      organizationId,
      testDto,
    );
  });

  it('should call ScoringCriteriaSetService.updateCriteriaSet', async () => {
    const testDto: UpdateCriteriaSetDto = {
      name: 'test',
      associations: [],
    };
    scoringCriteriaSetsService.updateCriteriaSet = jest
      .fn()
      .mockReturnValue(of(axiosResponse));
    const result = await service
      .updateCriteriaSet(criteriaId, testDto)
      .toPromise();
    expect(result).toEqual(dummyResponse);
    expect(scoringCriteriaSetsService.updateCriteriaSet).toHaveBeenCalledWith(
      criteriaId,
      testDto,
    );
  });

  it('should call ScoringCriteriaSetService.deleteCriteriaSet', async () => {
    const criteriaId = 123;
    scoringCriteriaSetsService.deleteCriteriaSet = jest
      .fn()
      .mockReturnValue(of(axiosResponse));
    const result = await service.deleteCriteriaSet(criteriaId).toPromise();
    expect(result).toEqual(dummyResponse);
    expect(scoringCriteriaSetsService.deleteCriteriaSet).toHaveBeenCalledWith(
      criteriaId,
    );
  });
});
