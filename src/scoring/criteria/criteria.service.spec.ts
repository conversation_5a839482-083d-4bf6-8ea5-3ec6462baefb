import { Test, TestingModule } from '@nestjs/testing';
import {
  CriteriaCreateDto,
  ScoringCriteriaService,
  CriteriaSetService as ScoringCriteriaSetsService,
  ScoringCriteriaTemplatesService,
  ReadOverrideRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { of } from 'rxjs';
import { CriteriaService } from './criteria.service';
import { ScoreCardService } from '../scorecard/score-card.service';
import { ScoringAuthService } from '../scoring-auth/scoring-auth.service';
import CriteriaIdentifierEnum = ReadOverrideRequestDto.CriteriaIdentifierEnum;
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { ApplicabilityMediaTypesEnum } from '../scoring-constants';

describe('CriteriaService', () => {
  let service: CriteriaService;
  let scoringCriteriaService: ScoringCriteriaService;
  let scoringAuthService: ScoringAuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CriteriaService,
        {
          provide: ScoringCriteriaService,
          useValue: {
            createCriteria: jest.fn(),
            deleteCriteriaAsPromise: jest.fn(),
            getCriteriaForListOfWorkspacesAsPromise: jest.fn(),
          },
        },
        {
          provide: ScoringCriteriaTemplatesService,
          useValue: {
            getPartnerCriteriaTemplates: jest.fn(),
          },
        },
        {
          provide: ScoringCriteriaSetsService,
          useValue: {
            getPartnerCriteriaSet: jest.fn(),
          },
        },
        {
          provide: ScoreCardService,
          useValue: {
            isUserAllowedToReadCreate: jest.fn(),
          },
        },
        {
          provide: ScoringAuthService,
          useValue: {
            getIsUserOrgAdmin: jest.fn(),
          },
        },
        {
          provide: WorkspaceService,
          useValue: {
            getWorkspaceById: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CriteriaService>(CriteriaService);
    scoringCriteriaService = module.get<ScoringCriteriaService>(
      ScoringCriteriaService,
    );
    scoringAuthService = module.get<ScoringAuthService>(ScoringAuthService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  const partnerId = 123;
  const criteriaId = 456;
  const userId = 789;
  const dummyResponse = 'dummy response';
  const axiosResponse = { data: { result: dummyResponse } };

  it('should call ScoringCriteriaService.createCriteria', async () => {
    const personId = 1;
    const testRequest: CriteriaCreateDto = {
      applicabilityMediaTypes: [ApplicabilityMediaTypesEnum.VIDEO],
      platforms: ['ALL_PLATFORMS'],
      isGlobal: false,
      isOptional: false,
      personId,
      templateIdentifier: CriteriaIdentifierEnum.FramedForMobile,
      name: 'Framed for Mobile',
    };
    (scoringCriteriaService.createCriteria as jest.Mock).mockReturnValue(
      of(axiosResponse),
    );

    const result = await service.createCriteria(
      personId,
      partnerId,
      testRequest,
    );
    expect(result).toBeDefined();
    expect(scoringCriteriaService.createCriteria).toHaveBeenCalledWith(
      partnerId,
      testRequest,
    );
  });

  it('should call ScoringCriteriaService.deleteCriteria', async () => {
    (scoringAuthService.getIsUserOrgAdmin as jest.Mock).mockResolvedValue(
      false,
    );
    (
      scoringCriteriaService.deleteCriteriaAsPromise as jest.Mock
    ).mockResolvedValue(axiosResponse.data.result);

    const result = await service.deleteCriteria(partnerId, criteriaId, userId);
    expect(result).toEqual(dummyResponse);
    expect(scoringCriteriaService.deleteCriteriaAsPromise).toHaveBeenCalledWith(
      partnerId,
      criteriaId,
      false,
    );
  });

  it('should call ScoringCriteriaService.deleteCriteria with global flag', async () => {
    (scoringAuthService.getIsUserOrgAdmin as jest.Mock).mockResolvedValue(true);
    (
      scoringCriteriaService.deleteCriteriaAsPromise as jest.Mock
    ).mockResolvedValue(axiosResponse.data.result);

    const result = await service.deleteCriteria(partnerId, criteriaId, userId);
    expect(result).toEqual(dummyResponse);
    expect(scoringCriteriaService.deleteCriteriaAsPromise).toHaveBeenCalledWith(
      partnerId,
      criteriaId,
      true,
    );
  });

  it('should call getCriteriaForListOfWorkspacesAsPromise with correct defaults', async () => {
    const workspaceIds = ['w1', 'w2'];
    const sortOrder: 'ASC' | 'DESC' = 'DESC';
    const sortBy = 'someField';
    const platformsString = 'p1,p2';
    const applicabilityTypesString = 'app1,app2';

    const fakeResponse = {
      result: [{ dummy: true }],
      pagination: { offset: 0, perPage: 2, nextOffset: 2, totalSize: 2 },
    } as unknown as any;

    (
      scoringCriteriaService.getCriteriaForListOfWorkspacesAsPromise as jest.Mock
    ).mockResolvedValue(fakeResponse);

    const response = await service.getCriteriaForListOfWorkspaces(
      workspaceIds,
      sortOrder,
      sortBy,
      platformsString,
      applicabilityTypesString,
    );

    expect(
      scoringCriteriaService.getCriteriaForListOfWorkspacesAsPromise,
    ).toHaveBeenCalledWith(
      workspaceIds,
      sortOrder,
      undefined,
      sortBy,
      platformsString,
      applicabilityTypesString,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      0,
      Number.MAX_SAFE_INTEGER,
    );
    expect(response).toBe(fakeResponse);
  });
});
