import { Injectable, Logger } from '@nestjs/common';
import {
  GetSingleCriteriaGroup200Response,
  ScoringCriteriaGroupsService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { CreateCriteriaGroup } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/createCriteriaGroup';
import { ManageCriteriaGroupCriteriaRequest } from '@vidmob/vidmob-soa-scoring-service-sdk/dist/model/manageCriteriaGroupCriteriaRequest';

@Injectable()
export class CriteriaGroupService {
  logger = new Logger(CriteriaGroupService.name);

  constructor(
    private readonly scoringCriteriaGroupsService: ScoringCriteriaGroupsService,
  ) {}

  async getCriteriaGroups(
    organizationId: string,
    workspaceId: number,
    paginationOptions: PaginationOptions,
    searchText: string | null,
    includeCriteriaDetails: string | null,
  ) {
    try {
      return await this.scoringCriteriaGroupsService.getCriteriaGroupsAsPromise(
        organizationId,
        workspaceId,
        paginationOptions.offset,
        paginationOptions.perPage,
        undefined,
        includeCriteriaDetails,
        searchText,
      );
    } catch (error) {
      this.logger.error(
        `Error getting all criteria groups. Error: ${error.response.error.message}`,
      );
      throw error;
    }
  }

  async getSingleCriteriaGroup(
    organizationId: string,
    workspaceId: number,
    criteriaGroupId: string,
  ): Promise<GetSingleCriteriaGroup200Response> {
    try {
      return await this.scoringCriteriaGroupsService.getSingleCriteriaGroupAsPromise(
        organizationId,
        workspaceId,
        criteriaGroupId,
      );
    } catch (error) {
      this.logger.error(
        `Error getting single criteria group. Error: ${error.response.error.message}`,
      );
      throw error;
    }
  }

  async createCriteriaGroup(
    organizationId: string,
    workspaceId: number,
    userId: number,
    createCriteriaGroupRequest: CreateCriteriaGroup,
  ) {
    try {
      return await this.scoringCriteriaGroupsService.createCriteriaGroupAsPromise(
        organizationId,
        workspaceId,
        userId,
        createCriteriaGroupRequest,
      );
    } catch (error) {
      this.logger.error(
        `Error creating criteria group. Error: ${error.response.error.message}`,
      );
      throw error;
    }
  }

  async editCriteriaGroup(
    organizationId: string,
    workspaceId: number,
    criteriaGroupId: string,
    createCriteriaGroupRequest: CreateCriteriaGroup,
  ) {
    try {
      return await this.scoringCriteriaGroupsService.editCriteriaGroupAsPromise(
        organizationId,
        workspaceId,
        criteriaGroupId,
        createCriteriaGroupRequest,
      );
    } catch (error) {
      this.logger.error(
        `Error editing criteria group. Error: ${error.response.error.message}`,
      );
      throw error;
    }
  }

  async deleteCriteriaGroup(organizationId: string, criteriaGroupId: string) {
    try {
      return await this.scoringCriteriaGroupsService.deleteCriteriaGroupAsPromise(
        organizationId,
        criteriaGroupId,
      );
    } catch (error) {
      this.logger.error(
        `Error deleting criteria group. Error: ${error.response.error.message}`,
      );
      throw error;
    }
  }

  async manageCriteriaInCriteriaGroups(
    organizationId: string,
    manageCriteriaGroupCriteriaRequest: ManageCriteriaGroupCriteriaRequest,
  ) {
    try {
      return await this.scoringCriteriaGroupsService.manageCriteriaInCriteriaGroupAsPromise(
        organizationId,
        manageCriteriaGroupCriteriaRequest,
      );
    } catch (error) {
      this.logger.error(
        `Error adding or removing criteria from criteria group. Error: ${
          error.response.error.message
        }. Request: ${JSON.stringify(manageCriteriaGroupCriteriaRequest)}`,
      );
      throw error;
    }
  }
}
