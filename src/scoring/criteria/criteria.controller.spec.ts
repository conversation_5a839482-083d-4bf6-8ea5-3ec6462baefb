import { Test, TestingModule } from '@nestjs/testing';
import {
  CriteriaCreateDto,
  DeleteCriteriaDto,
  ReadCriteriaDto,
  ReadCriteriaSetDto,
  ReadTranslatedCriteriaTemplateDto,
  GetCriteriaQueryParamsDto,
  ReadOverrideRequestDto,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { of } from 'rxjs';
import { CriteriaController } from './criteria.controller';
import { CriteriaService } from './criteria.service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import CriteriaIdentifierEnum = ReadOverrideRequestDto.CriteriaIdentifierEnum;
import { ApplicabilityMediaTypesEnum } from '../scoring-constants';

describe('CriteriaController', () => {
  let controller: CriteriaController;
  let criteriaService: CriteriaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CriteriaController],
      providers: [
        {
          provide: CriteriaService,
          useValue: {
            deleteCriteria: jest.fn(),
            createCriteria: jest.fn(),
            getPartnerCriteriaSets: jest.fn(),
            getPartnerCriteriaTemplates: jest.fn(),
            getWorkspaceCriteriaOptions: jest.fn(),
            getMultipleWorkspaceCriteriaList: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<CriteriaController>(CriteriaController);
    criteriaService = module.get<CriteriaService>(CriteriaService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  const partnerId = 123;
  const criteriaId = 456;
  const request = { userId: 789 };

  const mockGetCriteriaQueryParamsDto: GetCriteriaQueryParamsDto = {
    sortOrder: GetCriteriaQueryParamsDto.SortOrderEnum.Asc, // or Descending
    sortBy: 'dateCreated',
    platforms: [
      GetCriteriaQueryParamsDto.PlatformsEnum.Facebook,
      GetCriteriaQueryParamsDto.PlatformsEnum.Tiktok,
    ],
    mediaTypes: [],
    isOptional: [GetCriteriaQueryParamsDto.IsOptionalEnum.Mandatory],
    ownerIds: '123456,789012',
    searchText: 'sample search text',
  };

  const testCriteriaResponse: ReadCriteriaDto = {
    id: 1,
    name: 'Video Duration',
    criteriaSetId: 2,
    criteriaSet: {
      id: 2,
      isGlobal: 1,
    },
    category: '',
    identifier: 'VIDEO_DURATION',
    platform: 'ALL_PLATFORMS',
    parameters: { minDuration: 6, maxDuration: 12 },
    isBestPractice: false,
    dateCreated: '2023-06-21T02:06:31.578Z',
    mediaTypes: ['VIDEO', 'IMAGE'],
    isOptional: false,
    criteriaGroups: [],
  };

  it('delete a criteria', async () => {
    const testResponse: DeleteCriteriaDto = {
      id: 1,
    };
    criteriaService.deleteCriteria = jest
      .fn()
      .mockReturnValue(of(testResponse));
    const response = await controller.deleteCriteria(
      request,
      partnerId,
      criteriaId,
    );
    expect(criteriaService.deleteCriteria).toHaveBeenCalledWith(
      partnerId,
      criteriaId,
      request.userId,
    );
    expect(response).toEqual(testResponse);
  });

  it('create criteria - default criteria set', async () => {
    const personId = 1;
    const testRequest: CriteriaCreateDto = {
      applicabilityMediaTypes: [ApplicabilityMediaTypesEnum.VIDEO],
      platforms: ['ALL_PLATFORMS'],
      isGlobal: false,
      isOptional: false,
      personId,
      templateIdentifier: CriteriaIdentifierEnum.FramedForMobile,
      name: 'Framed for Mobile',
    };
    criteriaService.createCriteria = jest
      .fn()
      .mockReturnValue(of(testCriteriaResponse));
    const response = await controller.createCriteria(partnerId, testRequest, {
      userId: personId,
    });
    expect(criteriaService.createCriteria).toHaveBeenCalledWith(
      partnerId,
      testRequest,
    );
    expect(response).toEqual(testCriteriaResponse);
  });

  it('get partner criteria sets - no detail', async () => {
    const testResponse: ReadCriteriaSetDto[] = [
      {
        id: 1,
        name: 'DEFAULT',
        workspaceId: 1,
        dateCreated: '2023-06-21T02:06:31.578Z',
        lastUpdated: '2023-06-21T02:06:31.578Z',
      },
    ];
    criteriaService.getWorkspaceCriteriaList = jest
      .fn()
      .mockReturnValue(testResponse);

    const mockPaginationOptions = new PaginationOptions();

    const response = await controller.getWorkspaceCriteriaList(
      mockPaginationOptions,
      partnerId,
      mockGetCriteriaQueryParamsDto,
      {
        userId: 134,
      },
    );
    expect(criteriaService.getWorkspaceCriteriaList).toHaveBeenCalledWith(
      134,
      partnerId,
      mockPaginationOptions,
      mockGetCriteriaQueryParamsDto,
    );
    expect(response).toEqual(testResponse);
  });

  it('get partner criteria sets - detail', async () => {
    const testResponse: ReadCriteriaSetDto[] = [
      {
        id: 1,
        name: 'DEFAULT',
        workspaceId: 1,
        dateCreated: '2023-06-21T02:06:31.578Z',
        lastUpdated: '2023-06-21T02:06:31.578Z',
      },
    ];

    criteriaService.getWorkspaceCriteriaList = jest
      .fn()
      .mockReturnValue(testResponse);

    const mockPaginationOptions = new PaginationOptions();

    const response = await controller.getWorkspaceCriteriaList(
      mockPaginationOptions,
      partnerId,
      mockGetCriteriaQueryParamsDto,
      {
        userId: 134,
      },
    );
    expect(criteriaService.getWorkspaceCriteriaList).toHaveBeenCalledWith(
      134,
      partnerId,
      mockPaginationOptions,
      mockGetCriteriaQueryParamsDto,
    );
    expect(response).toEqual(testResponse);
  });

  it('get partner criteria templates', async () => {
    const testResponse: ReadTranslatedCriteriaTemplateDto[] = [
      {
        id: 1,
        identifier:
          ReadTranslatedCriteriaTemplateDto.IdentifierEnum
            .BrandNameOrLogoAnytime,
        criteriaType:
          ReadTranslatedCriteriaTemplateDto.CriteriaTypeEnum.VidmobOptional,
        platformIdentifier:
          ReadTranslatedCriteriaTemplateDto.PlatformIdentifierEnum.Facebook,
        usesBrandIdentifier: true,
        parameters: [],
        defaultInstanceParameters: {},
        isBestPractice: false,
        applicability: ReadTranslatedCriteriaTemplateDto.ApplicabilityEnum.All,
        rule: '',
        description: '',
        defaultDisplayName: '',
        category: '',
        custom: false,
      },
      {
        id: 2,
        identifier:
          ReadTranslatedCriteriaTemplateDto.IdentifierEnum.VideoDuration,
        criteriaType:
          ReadTranslatedCriteriaTemplateDto.CriteriaTypeEnum.VidmobDefault,
        platformIdentifier:
          ReadTranslatedCriteriaTemplateDto.PlatformIdentifierEnum.Tiktok,
        usesBrandIdentifier: false,
        parameters: [
          {
            identifier: 'minDuration',
            dataType: 'INTEGER',
            units: 'SECONDS',
            multiselect: false,
            values: [1, 2, 3],
          },
          {
            identifier: 'maxDuration',
            dataType: 'INTEGER',
            units: 'SECONDS',
            multiselect: false,
            values: [1, 2, 3],
          },
        ],
        defaultInstanceParameters: {
          minDuration: 3,
          maxDuration: 5,
        },
        isBestPractice: false,
        applicability:
          ReadTranslatedCriteriaTemplateDto.ApplicabilityEnum.Video,
        rule: '',
        description: '',
        defaultDisplayName: '',
        category: '',
        custom: false,
      },
    ];
    criteriaService.getPartnerCriteriaTemplates = jest
      .fn()
      .mockReturnValue(of(testResponse));

    const response = await controller
      .getPartnerCriteriaTemplates(partnerId, false)
      .toPromise();
    expect(criteriaService.getPartnerCriteriaTemplates).toHaveBeenCalledWith(
      partnerId,
      false,
    );
  });

  it.only('gets partner criteria options', async () => {
    const testResponse: any = {
      // updating this to any to work around an issue with the SDK not updating the response type in the gitlab pipeline.
      status: 'success',
      result: {
        owners: [
          {
            label: 'Jack VidMob',
            value: '12345',
          },
          {
            label: 'Carl VidMob',
            value: '54321',
          },
        ],
      },
    };

    jest
      .spyOn(criteriaService, 'getWorkspaceCriteriaOptions')
      .mockResolvedValue(testResponse);

    const response = await controller.getCriteriaOptions(
      { userId: 134 },
      123,
      false,
    );

    expect(criteriaService.getWorkspaceCriteriaOptions).toHaveBeenCalledWith(
      134,
      123,
      false,
    );
    expect(response).toEqual(testResponse);
  });

  it.only('getCriteriaAcrossWorkspacesV2 should call service.getMultipleWorkspaceCriteriaList and return its value', async () => {
    const fakeUserId = 555;
    const fakePagination = { offset: 0, perPage: 2 } as PaginationOptions;
    const fakeWorkspaceIds = [10, 20];
    const fakeQuery: GetCriteriaQueryParamsDto = {
      sortOrder: GetCriteriaQueryParamsDto.SortOrderEnum.Asc,
      sortBy: 'dateCreated',
      platforms: [GetCriteriaQueryParamsDto.PlatformsEnum.Facebook],
      mediaTypes: [GetCriteriaQueryParamsDto.MediaTypesEnum.Image],
      isOptional: [GetCriteriaQueryParamsDto.IsOptionalEnum.Optional],
      ownerIds: '123,456',
      searchText: 'foo',
      globalStatuses: [GetCriteriaQueryParamsDto.GlobalStatusesEnum.Global],
      categories: 'bar',
      startDate: '2025-01-01',
      endDate: '2025-12-31',
      criteriaGroupIds: 'g1,g2',
    };

    const fakeResponse = {
      status: 'OK',
      result: [{ id: 1, name: 'x' }],
      pagination: { offset: 0, perPage: 2, nextOffset: 2, totalSize: 1 },
    };

    (
      criteriaService.getMultipleWorkspaceCriteriaList as jest.Mock
    ).mockResolvedValue(fakeResponse);

    const req = { userId: fakeUserId };
    const response = await controller.getCriteriaAcrossWorkspacesV2(
      req,
      fakePagination,
      fakeWorkspaceIds,
      fakeQuery,
    );

    expect(
      criteriaService.getMultipleWorkspaceCriteriaList,
    ).toHaveBeenCalledWith(
      fakeUserId,
      fakeWorkspaceIds,
      fakePagination,
      fakeQuery,
    );
    expect(response).toBe(fakeResponse);
  });
});
