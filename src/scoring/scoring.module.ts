import { Module } from '@nestjs/common';
import { CriteriaController } from './criteria/criteria.controller';
import { CriteriaService } from './criteria/criteria.service';
import { BrandController } from './brand/brand.controller';
import { BrandService } from './brand/brand.service';
import { ScoreOverrideController } from './score-override/score-override.controller';
import { ScoreOverrideService } from './score-override/score-override.service';
import { ReportsController } from './reports/reports.controller';
import { ScoringReportsService } from './reports/scoring-reports.service';
import { ScoringReportFiltersService } from './reports/scoring-report-filters.service';
import { HttpModule } from '@nestjs/axios';
import { CriteriaSetController } from './criteria/criteria-set.controller';
import { CriteriaSetService } from './criteria/criteria-set.service';
import { ScoreController } from './score/score.controller';
import { ScoreService } from './score/score.service';
import { ScorecardController } from './scorecard/score-card.controller';
import { ScoreCardService } from './scorecard/score-card.service';
import { ScoringAuthService } from './scoring-auth/scoring-auth.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Market } from '../entities/market.entity';
import { ScoringNormsController } from './score/norms-score.controller';
import { Industry } from '../platform/entities/industry.entity';
import { Country } from '../platform/entities/market.entity';
import { CountryRegionMap } from '../platform/entities/country-region-map.entity';
import { Region } from '../platform/entities/region.entity';
import { ScoringNormsVersion } from './score/entities/scoring-norms-version.entity';
import { MediaAnnotationService } from '../media-annotation/media-annotation.service';
import { AdherenceReportMetadataCsvGenerator } from './reports/csv-generators/adherence-report-metadata-csv-generator';
import { ConfigService } from '@nestjs/config';
import { CriteriaGroupService } from './criteria/criteria-group.service';
import { CriteriaGroupController } from './criteria/criteria-group.controller';
import { RekognitionTag } from '../entities/rekognition-tag.entity';
import { CustomCriteriaController } from './custom-criteria/custom-criteria.controller';
import { CustomCriteriaService } from './custom-criteria/custom-criteria.service';
import { WorkspaceService } from '../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../auth/services/auth.service';
import { ScorecardCsvService } from './scorecard/scorecard-csv.service';
import { AnalyticsUserService } from '../analytics/analytics-user-service/analytics-user-service';
import { InflightReportCsvGenerator } from './reports/inflight-report-csv-generator';

@Module({
  controllers: [
    CriteriaController,
    CriteriaSetController,
    CriteriaGroupController,
    BrandController,
    ScoreController,
    ScoringNormsController,
    ScoreOverrideController,
    ReportsController,
    ScorecardController,
    CustomCriteriaController,
  ],
  providers: [
    CriteriaService,
    CriteriaSetService,
    CriteriaGroupService,
    BrandService,
    ScoreService,
    ScoreOverrideService,
    ScoringReportsService,
    ScoringReportFiltersService,
    AdherenceReportMetadataCsvGenerator,
    InflightReportCsvGenerator,
    ScoreCardService,
    ScorecardCsvService,
    MediaAnnotationService,
    ScoringAuthService,
    ConfigService,
    CustomCriteriaService,
    WorkspaceService,
    AnalyticsUserService,
    AuthService,
  ],
  imports: [
    TypeOrmModule.forFeature([
      Country,
      Industry,
      CountryRegionMap,
      Region,
      ScoringNormsVersion,
      RekognitionTag,
    ]),
    HttpModule,
    TypeOrmModule.forFeature([Market]),
  ],
  exports: [ScoringReportsService, CriteriaGroupService, CriteriaService],
})
export class ScoringModule {}
