import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AutomapperModule } from '@automapper/nestjs';
import { classes } from '@automapper/classes';
import {
  configuration,
  databaseProvider,
  HealthModule,
  RookoutModule,
  VidmobCommonModule,
  VidmobResponseInterceptorFactory,
} from '@vidmob/vidmob-nestjs-common';
import { APP_INTERCEPTOR } from '@nestjs/core';

import {
  ApiModule as DashboardApiModule,
  Configuration as DashboardConfiguration,
  ConfigurationParameters as DashboardConfigurationParameters,
  WidgetQueryService as DashboardDefaultService,
} from '@vidmob/vidmob-dashboard-service-sdk';

import {
  ApiModule as AuthorizationApiModule,
  Configuration as AuthorizationConfiguration,
  ConfigurationParameters as AuthorizationConfigurationParameters,
  DefaultService as AuthorizationDefaultService,
} from '@vidmob/vidmob-authorization-service-sdk';
import {
  ApiModule as AnalyticsApiModule,
  Configuration as AnalyticsConfiguration,
  ConfigurationParameters as AnalyticsConfigurationParameters,
  DefaultService as AnalyticsDefaultService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  ApiModule as OrganizationApiModule,
  Configuration as OrganizationConfiguration,
  ConfigurationParameters as OrganizationConfigurationParameters,
  DefaultService as OrganizationDefaultService,
} from '@vidmob/vidmob-organization-service-sdk';
import {
  ApiModule as ScoringApiModule,
  Configuration as ScoringConfiguration,
  ConfigurationParameters as ScoringConfigurationParameters,
  HealthService as ScoringDefaultService,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import {
  ApiModule as MediaConversionApiModule,
  Configuration as MediaConversionConfiguration,
  ConfigurationParameters as MediaConversionConfigurationParameters,
  DefaultService as MediaConversionDefaultService,
} from '@vidmob/vidmob-media-conversion-service-sdk';
import {
  ApiModule as NotificationApiModule,
  Configuration as NotificationConfiguration,
  ConfigurationParameters as NotificationConfigurationParameters,
  DefaultService as NotificationDefaultService,
} from '@vidmob/vidmob-soa-notification-service-sdk';
import {
  ApiModule as StudioApiModule,
  Configuration as StudioConfiguration,
  ConfigurationParameters as StudioConfigurationParameters,
  DefaultService as StudioDefaultService,
} from '@vidmob/vidmob-studio-service-sdk';
import {
  API_TAG_NAME,
  DEFAULT_RESPONSE_DESCRIPTION,
  DEFAULT_RESPONSE_ERROR_TYPE,
} from './constants/api.constants';
import { ServiceStatusModule } from './service-status/service-status.module';
import { AuthModule } from './auth/auth.module';
import { ScoringModule } from './scoring/scoring.module';
import { AccountManagementModule } from './account-management/account-management.module';
import { ReportsModule } from './reports/reports.module';
import { LeaderboardModule } from './analytics/leaderboard/leaderboard.module';
import { CustomAudienceModule } from './custom-audience/custom-audience.module';
import { StudioModule } from './studio/studio.module';
import { AnalyticsReportsModule } from './analytics/saved-report/analytics-reports.module';
import { NotificationsModule } from './notifications/notifications.module';
import { AnalyticsCopilotModule } from './analytics/analytics-copilot/analytics-copilot.module';
import { CustomElementSetModule } from './analytics/custom-element-set/custom-element-set.module';
import { ElementPresenceModule } from './analytics/element-presence/element-presence.module';
import { ElementFlagModule } from './analytics/element-flag/element-flag.module';
import { PlatformMediaGroupModule } from './analytics/platform-media-group/platform-media-group.module';
import { FeConstantsModule } from './platform/fe-constants/fe-constants.module';
import { NormativePerformanceModule } from './analytics/normative-performance/normative-performance.module';
import { PlatformMetadataModule } from './platform/metadata/platform-metadata.module';
import { AnalyticsConfigModule } from './analytics/analytics-config/analytics-config.module';
import { ApiKeyManagementModule } from './platform/api-key-management/api-key-management.module';
import { CriteriaPerformanceReportModule } from './analytics/criteria-performance-report/criteria-performance-report.module';
import { DataExportsModule } from './platform/data-exports/data-exports.module';
import { OAuth2Module } from './oauth/oauth2.module';
import { InsightModule } from './analytics/insight/insight.module';
import { ConversationModule } from './analytics/analytics-copilot/conversation.module';

import {
  ApiModule as MediaAnnotationApiModule,
  Configuration as MediaAnnotationConfiguration,
  ConfigurationParameters as MediaAnnotationConfigurationParameters,
  DefaultService as MediaAnnotationDefaultService,
} from '@vidmob/vidmob-soa-media-annotation-service-sdk';
import { MediaAnnotationModule } from './media-annotation/media-annotation.module';
import { DimensionsModule } from './analytics/dimensions/dimensions.module';
import { CreativeLifecycleModule } from './creative-lifecycle/creative-lifecycle.module';
import { CreativeManagerModule } from './analytics/creative-manager/creative-manager.module';
import { ConnectorsModule } from './platform/connectors/connectors.module';
import { RealEyesModule } from './real-eyes/real-eyes.module';
import { BrandsCopilotModule } from './analytics/brands-copilot/brands-copilot.module';
import { CurrencyModule } from './analytics/currency/currency.module';
import { AnalyticsMediaModule } from './analytics/analytics-media/analytics-media.module';
import { AudienceModule } from './analytics/audience/audience.module';
import { InsightFilterModule } from './analytics/insight/insight-filters/insight-filter.module';
import { MediaImpactModule } from './analytics/media-impact/media-impact.module';
import { ExecutiveDashboardModule } from './executive-dashboard/executive-dashboard.module';
import { WidgetQueryService } from '@vidmob/vidmob-dashboard-service-sdk';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [configuration],
      isGlobal: true,
    }),
    AutomapperModule.forRoot({ strategyInitializer: classes() }),
    VidmobCommonModule,
    TypeOrmModule.forRootAsync(databaseProvider()),
    ApiKeyManagementModule,
    HealthModule,
    AuthModule,
    ScoringModule,
    StudioModule,
    AccountManagementModule,
    LeaderboardModule,
    CriteriaPerformanceReportModule,
    ElementPresenceModule,
    ElementFlagModule,
    CustomAudienceModule,
    NormativePerformanceModule,
    PlatformMediaGroupModule,
    DimensionsModule,
    CustomElementSetModule,
    NotificationsModule,
    FeConstantsModule,
    PlatformMetadataModule,
    InsightModule,
    AuthorizationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<AuthorizationConfigurationParameters>(
            'authorizationService',
          );
        return new AuthorizationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    DashboardApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceCfg =
          configService.get<DashboardConfigurationParameters>(
            'dashboardService',
          );
        return new DashboardConfiguration(serviceCfg);
      },
      [ConfigService],
    ),
    ScoringApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<ScoringConfigurationParameters>('scoringService');
        return new ScoringConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    AnalyticsApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<AnalyticsConfigurationParameters>(
            'analyticsService',
          );
        return new AnalyticsConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    OrganizationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<OrganizationConfigurationParameters>(
            'organizationService',
          );
        return new OrganizationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    MediaConversionApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<MediaConversionConfigurationParameters>(
            'mediaConversionService',
          );
        return new MediaConversionConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    MediaAnnotationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<MediaAnnotationConfigurationParameters>(
            'mediaAnnotationService',
          );
        return new MediaAnnotationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    NotificationApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<NotificationConfigurationParameters>(
            'notificationService',
          );
        return new NotificationConfiguration(serviceConfig);
      },
      [ConfigService],
    ),
    StudioApiModule.forRoot(
      (configService: ConfigService) => {
        const serviceConfig =
          configService.get<StudioConfigurationParameters>('studioService');
        return new StudioConfiguration(serviceConfig);
      },
      [ConfigService],
    ),

    ServiceStatusModule.forRoot({
      services: {
        'vidmob-authorization-service': AuthorizationDefaultService,
        'vidmob-soa-analytics-service': AnalyticsDefaultService,
        'vidmob-organization-service': OrganizationDefaultService,
        'vidmob-soa-scoring-service': ScoringDefaultService,
        'vidmob-media-conversion-service': MediaConversionDefaultService,
        'vidmob-soa-notification-service': NotificationDefaultService,
        'vidmob-studio-service': StudioDefaultService,
        'vidmob-soa-media-annotation-service': MediaAnnotationDefaultService,
        'vidmob-dashboard-service': DashboardDefaultService,
      },
    }),
    ReportsModule,
    AnalyticsReportsModule,
    AnalyticsCopilotModule,
    BrandsCopilotModule,
    MediaAnnotationModule,
    RookoutModule,
    AnalyticsConfigModule,
    DataExportsModule,
    OAuth2Module,
    CreativeLifecycleModule,
    CreativeManagerModule,
    ConnectorsModule,
    ConversationModule,
    CurrencyModule,
    RealEyesModule,
    AnalyticsMediaModule,
    AudienceModule,
    InsightFilterModule,
    MediaImpactModule,
    ExecutiveDashboardModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_INTERCEPTOR,
      useValue: new VidmobResponseInterceptorFactory().create({
        errorConfiguration: {
          identifierPrefix: API_TAG_NAME,
          serviceSystem: API_TAG_NAME,
          defaultErrorMessage: DEFAULT_RESPONSE_DESCRIPTION,
          defaultErrorType: DEFAULT_RESPONSE_ERROR_TYPE,
        },
      }),
    },
  ],
})
export class AppModule {}
