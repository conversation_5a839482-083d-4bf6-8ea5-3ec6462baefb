import { PermissionDomain } from '../auth/enums/permission.domain.enum';
import {
  organizationFromBodyHandler,
  organizationFromParamsHandler,
} from '../auth/decorators/permission.decorator';
import { PermissionAction } from '../auth/enums/permission.action.enum';
import { PermissionSubResource } from '../auth/enums/permission.subresource.enum';

export const readOrganizationWorkspaceAdAccountReportsBodyHandler = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromBodyHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const anyOrganizationRole = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const readOrganizationWorkspaceAdAccountReports = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const createOrganizationWorkspaceAdAccountReports = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};

export const updateOrganizationAdAccountsDetails = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const readOrganizationAdAccountsDetails = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
  ],
};

export const readOrganizationAdAccountsAndCriteriaDetails = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.AD_ACCOUNT,
    },
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CRITERIA,
    },
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.CRITERIA_SET,
    },
  ],
};

export const readOrganizationDashboard = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.DASHBOARD,
    },
  ],
};

export const readOrganizationWorkspaceElementFlagDetails = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.WORKSPACE_ASSIGNED,
    },
  ],
};
