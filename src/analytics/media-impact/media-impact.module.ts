import { Modu<PERSON> } from '@nestjs/common';

import { HttpModule } from '@nestjs/axios';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { MediaImpactService } from './media-impact.service';
import { MediaImpactController } from './media-impact.controller';

@Module({
  imports: [HttpModule],
  controllers: [MediaImpactController],
  providers: [
    MediaImpactService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
  ],
})
export class MediaImpactModule {}
