import { <PERSON>du<PERSON> } from '@nestjs/common';

import { HttpModule } from '@nestjs/axios';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { MediaImpactService } from './media-impact.service';
import { MediaImpactController } from './media-impact.controller';
import { OrganizationUserModule } from '../../account-management/organization/organization-user/organization-user.module';
import { AccountManagementModule } from '../../account-management/account-management.module';

@Module({
  imports: [HttpModule, AccountManagementModule],
  controllers: [MediaImpactController],
  providers: [MediaImpactService, AnalyticsUserService, AuthService],
})
export class MediaImpactModule {}
