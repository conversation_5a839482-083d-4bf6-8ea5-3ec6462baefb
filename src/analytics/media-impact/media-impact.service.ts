import {
  DimensionPerformanceService,
  MediaImpactCreativeRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

@Injectable()
export class MediaImpactService {
  constructor(
    private readonly dimensionPerformanceService: DimensionPerformanceService,
  ) {}

  async getCreativesForKpi(
    creativeRequestDto: MediaImpactCreativeRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    return this.dimensionPerformanceService.getMediaImpactCreativeLevelPerformanceAsPromise(
      creativeRequestDto,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }
}
