import { Body, Controller, Param, Post, Request } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { readOrganizationWorkspaceAdAccountReports } from '../analytics.permissions';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { MediaImpactCreativeRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { MediaImpactService } from './media-impact.service';
import { ApiSecurity } from '@nestjs/swagger';

@ApiTags('Media Impact')
@ApiSecurity('Bearer Token')
@Controller('media-impact/organization/:organizationId')
export class MediaImpactController {
  constructor(
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly mediaImpactService: MediaImpactService,
  ) {}

  @ApiParam({
    name: 'organizationId',
    description: 'The organization we get creatives for',
    required: true,
  })
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Post('/creative')
  async getMediaImpactCreativePerformanceForKpi(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() creativeRequestDto: MediaImpactCreativeRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      {
        organizationId,
        userId: req.userId,
        authorization: req.headers.authorization,
      },
      creativeRequestDto.workspaceIds,
      creativeRequestDto.adAccountIds,
    );

    return await this.mediaImpactService.getCreativesForKpi(
      creativeRequestDto,
      paginationOptions,
    );
  }
}
