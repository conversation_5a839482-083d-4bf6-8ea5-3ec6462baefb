import { UserDetailsDto } from '../dto/user-details.dto';
import { UserAdAccountsAndWorkspacesDto } from '../saved-report/model/analytics-report';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { DEFAULT_PAGINATION_OPTIONS_INTERNAL_REQUESTS } from '../constants/constants';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/workspaceAdAccount.service';

@Injectable()
export class AnalyticsUserService {
  constructor(
    private readonly workspaceAdAccountServiceSDK: WorkspaceAdAccountServiceSDK,
    private readonly workspaceService: WorkspaceService,
  ) {}

  async fetchUserAdAccountsByWorkspaces(
    userDetails: UserDetailsDto,
    workspaceIds: number[],
    platform?: string,
  ): Promise<ReadPlatformAdAccountDto[]> {
    const workspaces = await this.fetchAllOrganizationWorkspacesForUser(
      userDetails,
    );

    // Filter the workspaces user has access by the workspaces user provided
    const workspaceIdsForOrganization = workspaces.reduce(
      (acc: number[], { id }) => {
        if (workspaceIds.includes(id)) {
          acc.push(id);
        }
        return acc;
      },
      [],
    );

    if (workspaceIdsForOrganization.length === 0) {
      return [];
    }

    const adAccounts = await this.fetchAllAdAccountsForWorkspaces(
      workspaceIdsForOrganization,
      platform,
    );

    return adAccounts;
  }

  async fetchUserAdAccountsAndWorkspaces(
    userDetails: UserDetailsDto,
    platform?: string,
  ): Promise<UserAdAccountsAndWorkspacesDto> {
    const workspaces = await this.fetchAllOrganizationWorkspacesForUser(
      userDetails,
    );
    const workspaceIdsForOrganization = workspaces.map(
      (workspace) => workspace.id,
    );
    const adAccounts = await this.fetchAllAdAccountsForWorkspaces(
      workspaceIdsForOrganization,
      platform,
    );

    return {
      adAccounts,
      workspaces,
    };
  }

  async fetchAllOrganizationWorkspacesForUser(
    userDetails: UserDetailsDto,
  ): Promise<{ id: number; name: string }[]> {
    const { userId, organizationId, authorization } = userDetails;
    const { perPage } = DEFAULT_PAGINATION_OPTIONS_INTERNAL_REQUESTS;
    let hasMore = true;
    let offset = 0;
    const workspaces = [];

    while (hasMore) {
      const { items, totalCount } =
        await this.workspaceService.getWorkspacesByOrganizationId(
          organizationId,
          userId,
          authorization,
          {
            offset,
            perPage: perPage,
          },
          {},
        );
      workspaces.push(...items);

      hasMore = Boolean(totalCount && workspaces.length < totalCount);
      offset = workspaces.length;
    }

    return workspaces;
  }

  async fetchAllAdAccountsForWorkspaces(
    workspaces: number[],
    platform?: string,
  ): Promise<ReadPlatformAdAccountDto[]> {
    const { perPage } = DEFAULT_PAGINATION_OPTIONS_INTERNAL_REQUESTS;
    let hasMore = true;
    let offset = 0;
    const adAccounts = [];

    while (hasMore) {
      const { result, pagination } =
        await this.workspaceAdAccountServiceSDK.getConnectedAdAccountsForWorkspacesAndPlatformAsPromise(
          { workspaces, platform },
          offset,
          perPage,
        );
      adAccounts.push(...result);

      hasMore = Boolean(
        pagination?.totalSize && adAccounts.length < pagination?.totalSize,
      );
      offset = adAccounts.length;
    }

    return adAccounts;
  }

  async validateUserAccessToAdAccountsAndWorkspaces(
    userDetails: UserDetailsDto,
    workspaceIds: number[],
    adAccountIds?: string[],
  ) {
    const { adAccounts, workspaces } =
      await this.fetchUserAdAccountsAndWorkspaces(userDetails);

    const userWorkspaceIds = workspaces.map((workspace) => workspace.id);
    const allExistInUserWorkspaces = workspaceIds.every((workspaceId) =>
      userWorkspaceIds.includes(workspaceId),
    );

    if (!allExistInUserWorkspaces) {
      throw new ForbiddenException(
        `User ${userDetails.userId} does not have access to all the submitted workspaces`,
      );
    }

    if (adAccountIds) {
      const userAdAccountIds = adAccounts.map(
        (adAccount) => adAccount.platformAccountId,
      );

      const allExistInUserAccounts = adAccountIds.every((adAccountId) =>
        userAdAccountIds.includes(adAccountId),
      );

      if (!allExistInUserAccounts) {
        throw new ForbiddenException(
          `User ${userDetails.userId} does not have access to all the submitted ad accounts`,
        );
      }
    }
  }
}
