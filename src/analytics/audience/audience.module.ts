import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { AudienceController } from './audience.controller';
import { AudienceService } from './audience.service';

@Module({
  imports: [HttpModule, ConfigModule.forRoot()],
  providers: [AudienceService],

  controllers: [AudienceController],
})
export class AudienceModule {}
