import { Injectable } from '@nestjs/common';
import { AudienceService as AudienceServiceSdk } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Platform } from '@vidmob/vidmob-nestjs-common';
@Injectable()
export class AudienceService {
  constructor(private readonly audienceServiceSdk: AudienceServiceSdk) {}

  getAudiences(platform: Platform) {
    return this.audienceServiceSdk.getAudiencesAsPromise(platform);
  }
}
