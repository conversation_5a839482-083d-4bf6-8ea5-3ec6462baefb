import { AudienceService } from './audience.service';
import { ApiTags } from '@nestjs/swagger';
import { Controller, Get, Query } from '@nestjs/common';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { readOrganizationWorkspaceAdAccountReports } from '../analytics.permissions';

@ApiTags('audience')
@Controller('organization/:organizationId/audience')
export class AudienceController {
  constructor(private readonly audienceService: AudienceService) {}

  @Get()
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  getAudiences(@Query('platform') platform: Platform) {
    return this.audienceService.getAudiences(platform);
  }
}
