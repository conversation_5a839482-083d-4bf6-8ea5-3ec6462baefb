import { ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Post,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { readOrganizationWorkspaceAdAccountReportsBodyHandler } from '../analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  ElementPresenceRequestDto,
  ElementPresenceResponseDto,
  MediaByElementTagRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ElementPresenceService } from './element-presence.service';

@ApiTags('Element Presence')
@Controller({
  path: 'analytics',
})
export class ElementPresenceController {
  constructor(
    private readonly elementPresenceService: ElementPresenceService,
  ) {}

  @Post('platform-media/search')
  @Permissions(readOrganizationWorkspaceAdAccountReportsBodyHandler)
  @UsePipes(new ValidationPipe({ transform: true }))
  async searchElementPresence(
    @Request() req: any,
    @Body() elementPresenceRequestDto: ElementPresenceRequestDto,
  ): Promise<ElementPresenceResponseDto> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId: elementPresenceRequestDto.organizationId,
      authorization: headers.authorization,
    };

    return this.elementPresenceService.getElementPresence(
      userDetails,
      elementPresenceRequestDto,
    );
  }

  @Post('element-presence/media')
  @Permissions(readOrganizationWorkspaceAdAccountReportsBodyHandler)
  @UsePipes(new ValidationPipe({ transform: true }))
  async searchMediaByElementTag(
    @Request() req: any,
    @Body() mediaByElementTagRequestDto: MediaByElementTagRequestDto,
  ) {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId: mediaByElementTagRequestDto.organizationId,
      authorization: headers.authorization,
    };

    return this.elementPresenceService.getMediaByElementTag(
      userDetails,
      mediaByElementTagRequestDto,
    );
  }
}
