import { UserDetailsDto } from '../dto/user-details.dto';
import {
  ElementPresenceService as ElementPresenceServiceSDK,
  ElementPresenceRequestDto,
  ElementPresenceResponseDto,
  MediaByElementTagRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { OrganizationService as OrganizationServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/organization.service';

@Injectable()
export class ElementPresenceService {
  private readonly logger = new Logger(ElementPresenceService.name);

  constructor(
    private readonly elementPresenceService: ElementPresenceServiceSDK,
    private readonly organizationServiceSDK: OrganizationServiceSDK,
  ) {}

  async getElementPresence(
    userDetails: UserDetailsDto,
    elementPresenceRequestDto: ElementPresenceRequestDto,
  ): Promise<ElementPresenceResponseDto> {
    const { result } =
      await this.organizationServiceSDK.validateAdAccountsWithinOrganizationAsPromise(
        elementPresenceRequestDto.organizationId,
        userDetails.userId,
        {
          adAccountIds: elementPresenceRequestDto.adAccountIds,
          workspaceIds: elementPresenceRequestDto.workspacesIds,
        },
      );
    if (!result.success) {
      throw new BadRequestException(
        `Ad account ids are not valid for organization ${elementPresenceRequestDto.organizationId}`,
      );
    }

    this.logger.log(
      `Sending EPR request to analytics-service Request: ${JSON.stringify(
        elementPresenceRequestDto,
      )}`,
    );

    return this.elementPresenceService
      .searchMediaAsPromise(elementPresenceRequestDto)
      .then((resp) => resp.result)
      .catch((error) => {
        const baseErrorMessage = `Failed to fetch element presence for user ${userDetails.userId} in organization ${userDetails.organizationId}`;
        this.logger.error(
          `${baseErrorMessage}. ${error}. \nRequest: ${JSON.stringify(
            elementPresenceRequestDto,
          )}`,
        );
        throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
      });
  }

  async getMediaByElementTag(
    userDetails: UserDetailsDto,
    mediaByElementTagRequestDto: MediaByElementTagRequestDto,
  ) {
    const { result } =
      await this.organizationServiceSDK.validateAdAccountsWithinOrganizationAsPromise(
        mediaByElementTagRequestDto.organizationId,
        userDetails.userId,
        {
          adAccountIds: mediaByElementTagRequestDto.adAccountIds,
          workspaceIds: mediaByElementTagRequestDto.workspaceIds,
        },
      );

    if (!result.success) {
      throw new BadRequestException(
        `Ad account ids are not valid for organization ${mediaByElementTagRequestDto.organizationId}`,
      );
    }

    return this.elementPresenceService.searchMediaByElementTagAsPromise(
      mediaByElementTagRequestDto,
    );
  }
}
