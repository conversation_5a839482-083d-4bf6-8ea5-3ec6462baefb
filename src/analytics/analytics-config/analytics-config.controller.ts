import { ApiTags } from '@nestjs/swagger';
import { Controller, Get } from '@nestjs/common';
import { AnalyticsConfigService } from './analytics-config.service';

@ApiTags('Analytics Config')
@Controller({
  path: 'analytics-config',
})
export class AnalyticsConfigController {
  constructor(
    private readonly analyticsConfigService: AnalyticsConfigService,
  ) {}

  @Get('default-durations')
  async getDefaultDurationBuckets() {
    return this.analyticsConfigService.getDefaultDurationBuckets();
  }
}
