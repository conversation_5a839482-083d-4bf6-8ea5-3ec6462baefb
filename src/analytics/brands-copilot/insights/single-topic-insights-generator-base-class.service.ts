import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>Report, ElementsLift } from '../report/copilot-report.model';
import {
  createSystemPromptMessage,
  createUserPromptMessage,
} from '../llm/llm-utils';
import { Insight } from '../report/insights.model';
import { toReportForLlmWithSortElementsByKpiValue } from '../report/copilot-report.utils';
import {
  InsightGeneratorSource,
  LLM_INSIGHTS_SCHEMA,
  LLM_INSIGHTS_SCHEMA_TYPE,
  parseLlmInsightResult,
  toStringReport,
} from './insight-utils.utils';
import { BrandInformation } from '../brand-data/brand-information.service';
import { UUID } from 'crypto';
import { CURRENCY_CODES_DETAILS_MAP } from '../../currency/currency.constants';
import { Llm<PERSON>lientRouter } from '../llm/llm-client-router.service';
import { LLMModel } from '../model/llm-model.enum';

const generateInsightsPrompt = `
    You are an insights generator for brand managers. 
    $tagExplanationPrompt
    User will supply KPI average of all ads of all elements (and not just for those elements that are supplied in the user report) 
    
    "input brand";
      $brandInformationPlaceholder
      
    Conversation context:
      $conversationContextSummary
    Conversation context may supply additional information or instructions about the brand/campaign/creatives/target audience etc.
    Make sure to take it into account while generating insights. 

    isStatSig=true is a statistically significant element, isStatSig=false is not. if isStatSig=false inform the user you can't rely on the findings of this element.
    
    $insightsInstructionsPrompt
    
    Your insight response should include the following sections:
    Title - A 2-3 word summary that captures the core insight.
    Findings - A brief description of what the data reveals about the creative elements and their performance.
    Recommendations - Actionable recommendation based on the findings
    Recommendations should tell a story, that connects performance data with the brand in an interesting way.
      Recommendations should contain 2 sentences explaining the why of element performance: 
      Think how are those creative elements relate to input brand audience, values, vision etc. Think in which kind of ads could this element appear? Why will this element perform well for this specific brand?  
      And 2 sentences of insightful action for brand managers based on the explanation
    
    Responses findings/recommendations should sound natural. 
    The user is unfamiliar with technical terms like elements or types.
    $naturalLanguageExamplePrompt
    
    Provide recommendation/findings in markdown format. Text that mentions report creative elements should be bold.
    For $kpiName values/lift/change use the following format ![$value](#388E3C) for $kpiName values/changes associated with elements with positive performance impact or ![$value](#D32F2F) for $kpiName values/changes associated with elements with negative performance impact.
    Kpi name is: $kpiName and its values represent a $kpiFormat.
    Add % when writing percentage and $currencySymbol to currency.
    Use it in your answer.
    
    Maintain a professional and confident tone throughout your response.
  `;

@Injectable()
export abstract class SingleTopicInsightsGeneratorBaseClassService {
  protected constructor(private readonly llmProviderService: LlmClientRouter) {}

  async generateInsights(
    conversationId: UUID,
    conversationContextSummary: string,
    report: CopilotReport,
    brandInformation: BrandInformation[],
    llmModel: LLMModel,
  ): Promise<Insight[]> {
    try {
      const tags = await this.filterTags(report);
      if (tags.length == 0) {
        return [];
      }

      const reportForLlm = toReportForLlmWithSortElementsByKpiValue(
        tags,
        report.isInverseHealthKpi,
        report.kpiAverage,
      );
      const reportToString = toStringReport(
        reportForLlm,
        report.kpiName,
        report.kpiFormat,
        report.isInverseHealthKpi,
      );
      const systemMessage = this.getSystemPrompt(
        brandInformation,
        conversationContextSummary,
        report.kpiName,
        report.kpiFormat,
        report.currency,
      );
      const llmMessages = [
        createSystemPromptMessage(systemMessage),
        createUserPromptMessage(reportToString),
      ];
      const llmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse<LLM_INSIGHTS_SCHEMA_TYPE>(
          LLM_INSIGHTS_SCHEMA,
          llmMessages,
          llmModel,
        );

      // FIXME: Add a step to verify LLM didn't hallucinated new tags?

      return parseLlmInsightResult(
        llmResult,
        report,
        conversationId,
        this.getInsightGeneratorType(),
      );
    } catch (error) {
      throw new Error(
        `Failed to generateInsights for conversation id: ${conversationId}. Error message: ${error.message}`,
      );
    }
  }

  protected abstract filterTags(report: CopilotReport): Promise<ElementsLift[]>;

  protected abstract getTagExplanationPrompt(): string;

  protected abstract getInsightsInstructionsPrompt(): string;

  protected abstract getNaturalLanguageExamplePrompt(): string;

  protected abstract getInsightGeneratorType(): InsightGeneratorSource;

  private getSystemPrompt(
    brandInformation: BrandInformation[],
    conversationContextSummary: string,
    kpiName: string,
    kpiFormat: string,
    currency = 'USD',
  ): string {
    return generateInsightsPrompt
      .replace(/\$kpiName/g, kpiName)
      .replace(/\$kpiFormat/g, kpiFormat)
      .replace(/\$currencySymbol/g, CURRENCY_CODES_DETAILS_MAP[currency].symbol)
      .replace(
        '$brandInformationPlaceholder',
        JSON.stringify(brandInformation.slice(0, 3)),
      )
      .replace('$tagExplanationPrompt', this.getTagExplanationPrompt())
      .replace(
        '$insightsInstructionsPrompt',
        this.getInsightsInstructionsPrompt(),
      )
      .replace(
        '$naturalLanguageExamplePrompt',
        this.getNaturalLanguageExamplePrompt(),
      )
      .replace('$conversationContextSummary', conversationContextSummary);
  }
}
