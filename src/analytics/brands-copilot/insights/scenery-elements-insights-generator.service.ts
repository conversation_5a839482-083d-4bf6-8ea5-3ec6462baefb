import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class SceneryElementInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allSceneryGroupElements = filterElementsByParent(
      report.elementsLift,
      ['City', 'Outdoors', 'Indoors', 'Shop', 'Building', 'Architecture'],
    );
    return filterElementsByMinNumberOfCreatives(allSceneryGroupElements, 5);
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's scenery creative element historical performance data.
      For each scenery element that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight that will include all report element. 
      Insight should be able to answer the question what kind of scenery elements affect performance the most (positively or negatively) outdoors, indoors or city.
      Insight should explain that those elements were selected since they affect performance the most (so the brand manager will understand that we didn't selected random elements for the insight)
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad: “creatives with the element ‘snow’” Good: “Ads that are showing snow.”
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.SceneryElementInsightsGenerator;
  }
}
