import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterByTypeAndExcludeParents,
  filterElementsByMinNumberOfCreatives,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class GazeAndEmotionsInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allCallToActionElements = filterByTypeAndExcludeParents(
      report.elementsLift,
      ['gaze and emotion'],
      [],
    );

    const callToActionElements = filterElementsByMinNumberOfCreatives(
      allCallToActionElements,
      5,
    );

    if (callToActionElements.length > 40) {
      return selectBestAndWorstElementsByKpiLift(callToActionElements, 20, 20);
    } else {
      return callToActionElements;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's gaze and emotion keywords creative element historical performance data.
      For each model gaze direction or model facial expression emotion that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce up to 2 insights: one about emotions and one about gaze direction. (emotions should come first always)  
      Insight should be able to answer the question what colors emotions and gaze direction affect performance the most (positively or negatively).
      Feel free to use multiple elements in order to tell a bigger more interesting story that connected to the brand.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad: “creatives with the element ‘Gaze Left’” Good: “Ads where models gazing towards left"
      Bad: “creatives with the element ‘Surprised’” Good: “Ads with surprised models"
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.GazeAndEmotionsInsightsGenerator;
  }
}
