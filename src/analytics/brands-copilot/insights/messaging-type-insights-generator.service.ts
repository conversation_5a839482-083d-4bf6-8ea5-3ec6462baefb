import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class MessagingTypeInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allCallToActionElements = filterElementsByParent(
      report.elementsLift,
      ['Benefit Messaging'],
    );

    const callToActionElements = filterElementsByMinNumberOfCreatives(
      allCallToActionElements,
      3,
    );

    if (callToActionElements.length > 40) {
      return selectBestAndWorstElementsByKpiLift(callToActionElements, 20, 20);
    } else {
      return callToActionElements;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's messaging type historical performance data.
      For each messaging type that Vidmob detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
      Insight should answer the question which messaging type affect performance the most (positively or negatively).
      Insight title should make it obvious that we are dealing with messaging type elements.
      Insight should explain that those elements affect performance the most (so the brand manager will understand that we didn't selected random elements for the insight)
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `Bad: “creatives with the messaging element ‘Functional’” Good: “Ads with Functional messaging`;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.MessagingInsightsGenerator;
  }
}
