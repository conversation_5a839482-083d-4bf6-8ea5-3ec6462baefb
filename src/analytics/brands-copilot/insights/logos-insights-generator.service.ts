import { Injectable } from '@nestjs/common';
import { Co<PERSON>lotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class LogoInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const logoGroupElements = filterElementsByParent(report.elementsLift, [
      'branding',
    ]);
    return filterElementsByMinNumberOfCreatives(logoGroupElements, 10);
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's logos creative element historical performance data.
      For each logo that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
      Insight should be able to answer the question which logos has the biggest effect (positive or negative) on performance.
      Logo that performs better/worse may indicate 2 different things:
        - that this logo brand ads are more effective in comparison to average KPI
        - that showing the logo itself at ads start affect the KPI performance
      Multiple logos can belong to the same bigger brand.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `Bad: “creatives with the element ‘LOGO: Pringles’” Good: “Ads that showing pringles logg.”`;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.LogoInsightsGenerator;
  }
}
