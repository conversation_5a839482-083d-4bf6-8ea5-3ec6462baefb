import { z } from 'zod';
import { <PERSON><PERSON><PERSON><PERSON>eport, ElementsLift } from '../report/copilot-report.model';
import { ReportForLlm } from '../report/copilot-report.utils';
import { AnalyticsKpiResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/analyticsKpiResponseDto';
import { Result } from '../model/Result';
import { Insight } from '../report/insights.model';
import { randomUUID } from 'crypto';

export const INVERSE_HEALTH_REPORT_GUIDE =
  'Data is sorted from best to worst. Since this KPI measures $kpiName, a lower value and a negative percentage change both indicate an element with a better effect on performance.';
export const NO_INVERSE_HEALTH_REPORT_GUIDE =
  'Data is sorted from best to worst. KPI measures $kpiName, a higher value and a positive KPI lift both indicate an element with a better effect on performance.';

export const LLM_INSIGHTS_SCHEMA = z.object({
  insights: z.array(
    z.object({
      title: z.string(),
      finding: z.string(),
      recommendations: z.string(),
      elements: z.array(
        z.object({
          id: z.string(),
          type: z.string(),
        }),
      ),
    }),
  ),
});

export type LLM_INSIGHTS_SCHEMA_TYPE = z.infer<typeof LLM_INSIGHTS_SCHEMA>;

export function filterElementsById(
  elementsLift: ElementsLift[],
  inputIds: string[],
): ElementsLift[] {
  const inputIdsLowerCase = new Set(inputIds.map((id) => id.toLowerCase()));
  return elementsLift.filter((element) =>
    inputIdsLowerCase.has(element.id.toLowerCase()),
  );
}

export function filterElementsByIdAndParent(
  elementsLift: ElementsLift[],
  inputIds: string[],
  inputParents: string[],
): ElementsLift[] {
  const filteredById = filterElementsById(elementsLift, inputIds);
  const inputParentsLowerCase = new Set(
    inputParents.map((parent) => parent.toLowerCase()),
  );
  return filteredById.filter((element) =>
    element.parentElements.some((parent) =>
      inputParentsLowerCase.has(parent.toLowerCase()),
    ),
  );
}

export function filterElementsByParent(
  elements: ElementsLift[],
  parents: string[],
): ElementsLift[] {
  return filterElementsByParentWithExclude(elements, parents, []);
}

export function filterByTypeAndExcludeParents(
  elements: ElementsLift[],
  types: string[],
  excludeList: string[],
): ElementsLift[] {
  const typesLowerCase = new Set(types.map((parent) => parent.toLowerCase()));
  const excludeLowerCaseParents = new Set(
    excludeList.map((parent) => parent.toLowerCase()),
  );
  const includeListElements = elements
    .filter((element) => typesLowerCase.has(element.type))
    .filter((element) => !typesLowerCase.has(element.id.toLowerCase()));
  if (excludeList.length > 0) {
    return includeListElements
      .filter((element) =>
        element.parentElements.every(
          (parent) => !excludeLowerCaseParents.has(parent.toLowerCase()),
        ),
      )
      .filter(
        (element) => !excludeLowerCaseParents.has(element.id.toLowerCase()),
      );
  } else {
    return includeListElements;
  }
}

export function filterOutNotStatisticallySignificantElements(
  elements: ElementsLift[],
): ElementsLift[] {
  return elements.filter(
    (element) => element.statisticalSignificance.isStatisticallySignificant,
  );
}

export function filterElementsByParentWithExclude(
  elements: ElementsLift[],
  includeList: string[],
  excludeList: string[],
): ElementsLift[] {
  const includeLowerCaseParents = new Set(
    includeList.map((parent) => parent.toLowerCase()),
  );
  const excludeLowerCaseParents = new Set(
    excludeList.map((parent) => parent.toLowerCase()),
  );
  const includeListElements = elements
    .filter((element) =>
      element.parentElements.some((parent) =>
        includeLowerCaseParents.has(parent.toLowerCase()),
      ),
    )
    .filter(
      (element) => !includeLowerCaseParents.has(element.id.toLowerCase()),
    );
  if (excludeList.length > 0) {
    return includeListElements.filter((element) =>
      element.parentElements.every(
        (parent) => !excludeLowerCaseParents.has(parent.toLowerCase()),
      ),
    );
  } else {
    return includeListElements;
  }
}

export function selectBestAndWorstElementsByKpiLift(
  elements: ElementsLift[],
  numberOfBestElements: number,
  numberOfWorstElements: number,
): ElementsLift[] {
  const totalElements = elements.length;
  if (totalElements <= numberOfBestElements + numberOfWorstElements) {
    return elements;
  } else {
    const sortedElements = [...elements].sort((a, b) => a.kpiLift - b.kpiLift);
    const worstElements = sortedElements.slice(0, numberOfWorstElements);
    const bestElements = sortedElements.slice(-numberOfBestElements);
    return worstElements.concat(bestElements);
  }
}

export function filterElementsByMinNumberOfCreatives(
  elements: ElementsLift[],
  numberOfCreatives: number,
): ElementsLift[] {
  return elements.filter(
    (element) => element.numberOfCreatives >= numberOfCreatives,
  );
}

export function getReportDataExplanationPrompt(
  kpiName: string,
  kpiFormat: string,
  isInverseHealthKpi: boolean,
): string {
  if (isInverseHealthKpi) {
    return INVERSE_HEALTH_REPORT_GUIDE.replace(/\$kpiName/g, kpiName).replace(
      /\$kpiFormat/g,
      kpiFormat,
    );
  } else {
    return NO_INVERSE_HEALTH_REPORT_GUIDE.replace(
      /\$kpiName/g,
      kpiName,
    ).replace(/\$kpiFormat/g, kpiFormat);
  }
}

export function toStringReport(
  report: ReportForLlm,
  kpiName: string,
  kpiFormat: AnalyticsKpiResponseDto.FormatEnum,
  isInverseHealthKpi: boolean,
): string {
  const reportDataExplanation = getReportDataExplanationPrompt(
    kpiName,
    kpiFormat,
    isInverseHealthKpi,
  );
  const reportDataPlaceholder = JSON.stringify(report);
  return `
    Report data: 
      ${reportDataExplanation}
      ${reportDataPlaceholder}
    `;
}

function filterByTags(
  report: CopilotReport,
  tags: { id: string; type: string }[],
): CopilotReport {
  const filteredElementsLift = report.elementsLift.filter((elementLift) => {
    return tags.some(
      (element) =>
        element.id === elementLift.id && element.type === elementLift.type,
    );
  });

  return {
    kpiAverage: report.kpiAverage,
    kpiName: report.kpiName,
    kpiFormat: report.kpiFormat,
    isInverseHealthKpi: report.isInverseHealthKpi,
    impressions: report.impressions,
    numberOfCreatives: report.numberOfCreatives,
    elementsLift: filteredElementsLift,
  };
}

export function parseLlmInsightResult(
  llmResult: Result<LLM_INSIGHTS_SCHEMA_TYPE>,
  report: CopilotReport,
  conversationId: string,
  source?: InsightGeneratorSource,
): Insight[] {
  if (!llmResult.isSuccessful()) {
    throw new Error(
      `Failed to generateInsights for conversation id: ${conversationId}. Error message: ${llmResult.error}`,
    );
  }

  const llmInsights = llmResult.value.insights;
  return llmInsights.map((llmInsight) => {
    const reportDataForInsight = filterByTags(report, llmInsight.elements);
    return {
      id: randomUUID(),
      title: llmInsight.title,
      findings: llmInsight.finding,
      recommendation: llmInsight.recommendations,
      report: reportDataForInsight,
      version: 1,
      source: source,
    };
  });
}

export enum InsightGeneratorSource {
  DemographicInsightsGenerator = 'DemographicInsightsGenerator',
  BestCallToActionInsightsGenerator = 'BestCallToActionInsightsGenerator',
  BodyPartFocusInsightsGenerator = 'BodyPartFocusInsightsGenerator',
  SceneryGroupInsightsGenerator = 'SceneryGroupInsightsGenerator',
  HumanPresenceInsightsGenerator = 'HumanPresenceInsightsGenerator',
  LogoInsightsGenerator = 'LogoInsightsGenerator',
  MessagingInsightsGenerator = 'MessagingInsightsGenerator',
  ColorInsightsGenerator = 'ColorInsightsGenerator',
  GazeAndEmotionsInsightsGenerator = 'GazeAndEmotionsInsightsGenerator',
  SceneryElementInsightsGenerator = 'SceneryElementInsightsGenerator',
  ObjectInsightsGenerator = 'ObjectInsightsGenerator',
}
