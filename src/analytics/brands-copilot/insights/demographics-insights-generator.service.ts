import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import {
  filterElementsByIdAndParent,
  filterElementsByMinNumberOfCreatives,
  InsightGeneratorSource,
} from './insight-utils.utils';
import { TagsMetadataService } from '../tag-metadata/tag-metadata.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class DemographicInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(
    llmProviderService: LlmClientRouter,
    private readonly tagsMetadataService: TagsMetadataService,
  ) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const demographicTags = await this.tagsMetadataService.getDemographicTags();
    const demographicTagsFromReport = filterElementsByIdAndParent(
      report.elementsLift,
      demographicTags,
      ['person'],
    );
    return filterElementsByMinNumberOfCreatives(demographicTagsFromReport, 5);
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's demographics creative element historical performance data.
      An element represent a type of person that was detected by Vidmob computer vision in brand manager ads.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. Insight should produce a recommendation based on gender/age groups affect performance the most (positively or negatively).
      When producing an insight about an 
      Note: Elements represent what kind of people were detected in the ad creatives. 
      'Woman' KPI means the KPI for ads featuring woman, it is not the KPI of the woman target audience.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad: “creatives with the element ‘Boy’ and 'Girl' perform better.” Good: “Ads featuring kids perform better.”
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.DemographicInsightsGenerator;
  }
}
