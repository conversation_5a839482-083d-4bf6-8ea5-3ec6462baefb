import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterByTypeAndExcludeParents,
  filterElementsByMinNumberOfCreatives,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class ObjectInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allObjectsThatDoNotHaveTheirOwnInsightsClassTags =
      filterByTypeAndExcludeParents(
        report.elementsLift,
        ['object'],
        [
          'City',
          'Outdoors',
          'Indoors',
          'Person',
          'Shop',
          'Building',
          'Architecture',
          'Body Part',
          'Urban',
        ],
      );
    const elementsWithMinNumberOfCreatives =
      filterElementsByMinNumberOfCreatives(
        allObjectsThatDoNotHaveTheirOwnInsightsClassTags,
        5,
      );
    if (elementsWithMinNumberOfCreatives.length > 40) {
      return selectBestAndWorstElementsByKpiLift(
        elementsWithMinNumberOfCreatives,
        20,
        20,
      );
    } else {
      return elementsWithMinNumberOfCreatives;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's element historical performance data.
      For each element that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
      Insight should be able to answer the question what kind of things presence in my ads affect performance the most: positively or negatively, you can include elements of both directions in the same insight. 
      Insight should explain that those elements were selected since they affect performance the most (so the brand manager will understand that we didn't selected random elements for the insight)
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad: “creatives with the element ‘snow’” Good: “Ads that are showing snow.”
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.ObjectInsightsGenerator;
  }
}
