import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class CallToActionTypeInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allCallToActionElements = filterElementsByParent(
      report.elementsLift,
      ['Call-to-action Type'],
    );

    const callToActionElements = filterElementsByMinNumberOfCreatives(
      allCallToActionElements,
      3,
    );

    if (callToActionElements.length > 40) {
      return selectBestAndWorstElementsByKpiLift(callToActionElements, 20, 20);
    } else {
      return callToActionElements;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's call to action types historical performance data.
      For each call to action type that Vidmob's computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
      Insight should answer the question which call to action types affect performance the most (positively or negatively).
      Insight title should make it obvious that we are dealing with call to action type elements.
      Insight should explain that those elements affect performance the most (so the brand manager will understand that we didn't selected random elements for the insight)
      Other CTA means a CTA which is not Conversion CTA, Engagement CTA, Information CTA. Explain it to the user if this tag is part of the output.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `Bad: “creatives with the element ‘Conversion’” Good: “Ads with Conversion CTA`;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.MessagingInsightsGenerator;
  }
}
