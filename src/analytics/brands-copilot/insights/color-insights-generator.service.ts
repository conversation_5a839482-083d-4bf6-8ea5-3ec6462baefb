import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class ColorInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allCallToActionElements = filterElementsByParent(
      report.elementsLift,
      ['color'],
    );

    const callToActionElements = filterElementsByMinNumberOfCreatives(
      allCallToActionElements,
      10,
    );

    if (callToActionElements.length > 40) {
      return selectBestAndWorstElementsByKpiLift(callToActionElements, 20, 20);
    } else {
      return callToActionElements;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's color keywords creative element historical performance data.
      For each color that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
      Insight should be able to answer the question what colors, color contrast (high - stark, low - muted), color saturation (high - vibrant, low - dull), text contrast, affect performance the most (positively or negatively).
      In findings/recommendation use professional language: stark, muted, vibrant, dull.
      Feel free to use multiple elements in order to tell a bigger more interesting story that connected to the brand.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `Bad: “creatives with the element ‘Your’” Good: “Ads that showing the text "Your"`;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.ColorInsightsGenerator;
  }
}
