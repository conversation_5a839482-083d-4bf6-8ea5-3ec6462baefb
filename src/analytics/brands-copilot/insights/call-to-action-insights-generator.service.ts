import { Injectable } from '@nestjs/common';
import { CopilotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class BestCallToActionInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allCallToActionElements = filterElementsByParent(
      report.elementsLift,
      ['Call-to-Action'],
    );

    const callToActionElements = filterElementsByMinNumberOfCreatives(
      allCallToActionElements,
      5,
    );

    const filterOutShortElements = callToActionElements.filter(
      (element) => element.id.length > 1,
    );

    if (filterOutShortElements.length > 40) {
      return selectBestAndWorstElementsByKpiLift(
        filterOutShortElements,
        20,
        20,
      );
    } else {
      return filterOutShortElements;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's call to action creative element historical performance data.
      For each call to action that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
    
      Do not use elements, which value doesn't look like an actual call to action.
      Non English call to action success usually comes from campaigns that targeted non english audience and occasionally 
      from a foreign language success for English audience.   
      - Don't suggest change all call to actions to foreign language. 
        But rather you may suggest to try the english translation of the same call to action or raise ideas why this specific call to action perform well in that language.
      Insight should explain that those elements affect performance the most (so the brand manager will understand that we our showing the best/worst CTA and not just some random one) 
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return ``;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.BestCallToActionInsightsGenerator;
  }
}
