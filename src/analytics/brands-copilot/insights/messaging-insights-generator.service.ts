import { Injectable } from '@nestjs/common';
import { Co<PERSON>lotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
  selectBestAndWorstElementsByKpiLift,
} from './insight-utils.utils';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class MessagingInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(llmProviderService: LlmClientRouter) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const allCallToActionElements = filterElementsByParent(
      report.elementsLift,
      ['Keyword'],
    );

    const callToActionElements = filterElementsByMinNumberOfCreatives(
      allCallToActionElements,
      10,
    );

    if (callToActionElements.length > 40) {
      return selectBestAndWorstElementsByKpiLift(callToActionElements, 20, 20);
    } else {
      return callToActionElements;
    }
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's messaging keywords creative element historical performance data.
      For each messaging keyword that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
    
      Do not use elements, which value doesn't look like an actual messaging keyword. Skip linking/filler words that 
      can't be connected to the brand values.

      Insight should answer the question which message keywords affect performance the most (positively or negatively).
      Only include relevant elements in the elements array, elements that are mentioned in findings/recommendations.
      Choose extremely positive or extremely negative elements for the insights.
      
      Insight title should make it obvious understand that we are dealing with messaging element.
      Insight should explain that those elements affect performance the most (so the brand manager will understand that we didn't selected random elements for the insight)
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `Bad: “creatives with the element ‘Your’” Good: “Ads that showing the text "Your"`;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.MessagingInsightsGenerator;
  }
}
