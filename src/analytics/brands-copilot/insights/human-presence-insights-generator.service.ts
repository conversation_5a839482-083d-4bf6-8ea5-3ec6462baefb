import { Injectable } from '@nestjs/common';
import { Co<PERSON>lotReport, ElementsLift } from '../report/copilot-report.model';
import { TagsMetadataService } from '../tag-metadata/tag-metadata.service';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import {
  filterElementsById,
  filterElementsByMinNumberOfCreatives,
  InsightGeneratorSource,
} from './insight-utils.utils';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class HumanPresenceInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(
    llmProviderService: LlmClientRouter,
    private readonly tagsMetadataService: TagsMetadataService,
  ) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const humanPresenceTags =
      await this.tagsMetadataService.getHumanPresenceGroupsTags();
    const allHumanPresenceElements = filterElementsById(
      report.elementsLift,
      humanPresenceTags,
    );
    return filterElementsByMinNumberOfCreatives(allHumanPresenceElements, 5);
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's human presence creative element historical performance data.
      For each human presence that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight that will include all report element. 
      Insight should be able to answer the questions: 
      Which kind of human presence perform the best? Should we show a single human (person) or multiple (team, people, crowd)?
      If all human presence have a negative effect on performance emphasize that any human presence deteriorate performance, this should be the main insight in such case. Explain that while in general human presence have a negative impact it may worth exploring specific human age/gender that work well.  
      Include at least 2 elements in the insight to show how single person compares to small/big groups and not just to KPI average.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad: “creatives with the element ‘Family’” Good: “Ads that showing a family.”
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.HumanPresenceInsightsGenerator;
  }
}
