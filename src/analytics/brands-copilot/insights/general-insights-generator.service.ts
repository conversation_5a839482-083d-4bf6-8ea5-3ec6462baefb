import { Injectable } from '@nestjs/common';
import { z } from 'zod';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { CopilotReport } from '../report/copilot-report.model';
import {
  convertToLlmMessagesWithSystemPrompt,
  createAssistantPromptMessage,
  createUserPromptMessage,
} from '../llm/llm-utils';
import { Insight, InsightAttributes } from '../report/insights.model';
import { vidmobTypeLabelExplanationPrompt } from '../llm/prompts';
import { Result } from '../model/Result';
import {
  generateUserPromptFirstLineBasedOnFilters,
  getReportForLlm,
  ReportForLlm,
} from '../report/copilot-report.utils';
import { AnalyticsKpiResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/analyticsKpiResponseDto';
import { randomUUID } from 'crypto';
import { getReportDataExplanationPrompt } from './insight-utils.utils';
import { BrandInformation } from '../brand-data/brand-information.service';
import { CURRENCY_CODES_DETAILS_MAP } from '../../currency/currency.constants';
import { LlmClientRouter } from '../llm/llm-client-router.service';
import { LLMModel } from '../model/llm-model.enum';

const generateInsightsPrompt = `
    You are an insights generator for brand managers. 
    Analyze Vidmob's creative element historical performance data and generate up to 5 insights for the user question.
    Report data: 
    $reportDataExplanation
    $reportDataPlaceholder
    
    Try to use as many elements as possible. When makes sense, group related elements together into a single insight which will tell a single bigger story.
    ${vidmobTypeLabelExplanationPrompt}
    
    If the last message of the conversation supplying context (usually start with 'context:', 'additional context:' 'ctx:') you should answer the question from previous messages and take this additional context during insight generation.
    'input brand':
      $brandInformationPlaceholder
    Also use all previous context messages (if exists) in this conversation to get additional brand information. Context instructions in previous messages are more important the brand information supplied here. 
      
    Prioritize Insights of statistically significant elements from report json.
    If asked about a specific element use it whether it is significant or not
    If all elements in a general question are not significant return an insight with all elements and the findings : none of the elements in significant.
    Every element with isStatSig=true is significant never discuss element with isStatSig=true as not significant
    
    Your insight response should include the following sections:
    Title - A 2-3 word summary that captures the core insight.
    Findings - A brief description of what the data reveals about the creative elements and their performance.
    Recommendations - Actionable recommendation based on the findings
    Recommendations should tell a story, that connects performance data with the brand in an interesting way.
      Recommendations should contain 2 sentences explaining the why of element performance: 
      think how are those creative elements relate to: input brand: audience, vision etc, Why will this element perform well in the context of this brand. 
      Think in which kind of ads could this element appear?
      2 sentences of insightful action for brand managers based on the explanation
    elements should contain information about the elements based on which this insight was created
    
    Provide recommendation/findings in markdown format. Text that mentions report creative elements should be bold.
    For $kpiName values/lift/change use the following format ![$value](#388E3C) for $kpiName values/changes associated with elements with positive performance impact or ![$value](#D32F2F) for $kpiName values/changes associated with elements with negative performance impact.
    Kpi name is: $kpiName and its values represent a $kpiFormat.
    Add % when writing percentage and $currencySymbol to currency.
    Use it in your answer.
    promptResponse should contain a 1 sentence answer to the supplied creative question.
    
    Maintain a professional and confident tone throughout your response.
    
    Responses should sound natural, as the user is unfamiliar with technical terms like elements or types. 
    Bad “creatives with the element ‘Lebron James’ perform better.” Good “Ads featuring Lebron James performed better.”
    Bad “Type emotion: HAPPY.” Instead, say, “Ads featuring happy models.
    
    Do not use report data from previous messages, it may have had other filters applied to it. Only use current report data.
  `;

const improveInsightsPrompt = `
Improve insights
- Analyze the provided insights and enhance them by finding deeper connections between the data and the brand's audience, vision, and values.
  - Think why would this insight perform well within the given brand and build a compelling story around it.
- Ensure that all insights are relevant to the brand and its context.
- Remove any insights that are not related to the brand or do not make sense in the context.
- Ensure that the insights are safe and do not include or promote any disallowed content such as smoking, alcohol, adult content, racism, anti-gender sentiments, offensive language, or any inappropriate topics.
  - Remove all not safe insights
- Verify that there are no empty elements array in any insight.
  - If any insight has empty elements array remove it. 
`;

const insightsSchema = z.object({
  promptResponse: z.string(),
  insights: z.array(
    z.object({
      title: z.string(),
      finding: z.string(),
      recommendations: z.string(),
      elements: z.array(
        z.object({
          id: z.string(),
          type: z.string(),
        }),
      ),
    }),
  ),
});

type InsightsSchemaType = z.infer<typeof insightsSchema>;

export class InsightsGenerationResponse {
  promptResponse: string;
  insights: Insight[];
}

@Injectable()
export class GeneralInsightsGeneratorService {
  constructor(private readonly llmProviderService: LlmClientRouter) {}

  async generateInsights(
    conversation: CopilotConversation,
    report: CopilotReport,
    insightAttributes: InsightAttributes,
    brandInformation: BrandInformation[],
    llmModel: LLMModel,
  ): Promise<InsightsGenerationResponse> {
    try {
      const reportForLlm = getReportForLlm(report);
      const initialInsightsGenerationPrompt =
        this.getInitialInsightGenerationSystemPrompt(
          reportForLlm,
          brandInformation,
          report.kpiName,
          report.kpiFormat,
          report.isInverseHealthKpi,
          report.currency,
        );
      const llmMessagesInitialInsightsGenerations =
        convertToLlmMessagesWithSystemPrompt(
          initialInsightsGenerationPrompt,
          conversation,
        );
      const originalInsightsLlmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse<InsightsSchemaType>(
          insightsSchema,
          llmMessagesInitialInsightsGenerations,
          llmModel,
        );

      const llmMessagesForInsightsImprovement = [
        ...llmMessagesInitialInsightsGenerations,
        createAssistantPromptMessage(JSON.stringify(originalInsightsLlmResult)),
        createUserPromptMessage(improveInsightsPrompt),
      ];

      const improveInsightsLlmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse<InsightsSchemaType>(
          insightsSchema,
          llmMessagesForInsightsImprovement,
          llmModel,
        );

      return this.parseLlmResult(
        improveInsightsLlmResult,
        report,
        conversation.id,
        generateUserPromptFirstLineBasedOnFilters(insightAttributes),
      );
    } catch (error) {
      throw new Error(
        `Failed to generateInsights for conversation id: ${conversation.id}. Error message: ${error.message}`,
      );
    }
  }

  private parseLlmResult(
    llmResult: Result<InsightsSchemaType>,
    report: CopilotReport,
    conversationId: string,
    userPromptAttributesPrefixString: string,
  ): InsightsGenerationResponse {
    if (!llmResult.isSuccessful()) {
      throw new Error(
        `Failed to generateInsights for conversation id: ${conversationId}. Error message: ${llmResult.error}`,
      );
    }

    const llmInsights = llmResult.value.insights;
    const insights: Insight[] = llmInsights.map((llmInsight) => {
      const reportDataForInsight = this.filterByTags(
        report,
        llmInsight.elements,
      );
      return {
        id: randomUUID(),
        title: llmInsight.title,
        findings: llmInsight.finding,
        recommendation: llmInsight.recommendations,
        report: reportDataForInsight,
        version: 1,
      };
    });

    // In some scenarios LLM continues to bring insight data from previous messages in the conversation rather than
    // from current message report data. I filtered out elements that are not part of the report in the previous statement,
    // and use this statement to eliminate all together an insight that relies solely on previous messages elements
    const filterOutInsightsWithoutElements = insights.filter(
      (insight) => insight.report?.elementsLift?.length > 0,
    );

    return {
      insights: filterOutInsightsWithoutElements,
      promptResponse:
        userPromptAttributesPrefixString + ' ' + llmResult.value.promptResponse,
    };
  }

  private getInitialInsightGenerationSystemPrompt(
    report: ReportForLlm,
    brandInformation: BrandInformation[],
    kpiName: string,
    kpiFormat: AnalyticsKpiResponseDto.FormatEnum,
    isInverseHealthKpi: boolean,
    currency = 'USD',
  ): string {
    return generateInsightsPrompt
      .replace('$reportDataPlaceholder', JSON.stringify(report))
      .replace(/\$kpiName/g, kpiName)
      .replace(/\$kpiFormat/g, kpiFormat)
      .replace(/\$currencySymbol/g, CURRENCY_CODES_DETAILS_MAP[currency].symbol)
      .replace(
        '$reportDataExplanation',
        getReportDataExplanationPrompt(kpiName, kpiFormat, isInverseHealthKpi),
      )
      .replace(
        '$brandInformationPlaceholder',
        JSON.stringify(brandInformation.slice(0, 3)),
      );
  }

  private filterByTags(
    report: CopilotReport,
    tags: { id: string; type: string }[],
  ): CopilotReport {
    const filteredElementsLift = report.elementsLift.filter((elementLift) => {
      return tags.some(
        (element) =>
          element.id === elementLift.id && element.type === elementLift.type,
      );
    });

    return {
      kpiAverage: report.kpiAverage,
      kpiName: report.kpiName,
      kpiFormat: report.kpiFormat,
      isInverseHealthKpi: report.isInverseHealthKpi,
      impressions: report.impressions,
      numberOfCreatives: report.numberOfCreatives,
      elementsLift: filteredElementsLift,
    };
  }
}
