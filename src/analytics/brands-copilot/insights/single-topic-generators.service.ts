import { Injectable } from '@nestjs/common';
import { HumanPresenceInsightsGenerator } from '../insights/human-presence-insights-generator.service';
import { SceneryGroupInsightsGenerator } from '../insights/scenery-groups-insights-generator.service';
import { DemographicInsightsGenerator } from '../insights/demographics-insights-generator.service';
import { BestCallToActionInsightsGenerator } from './call-to-action-insights-generator.service';
import { BodyPartFocusInsightsGenerator } from '../insights/body-part-focus-insights-generator.service';
import { LogoInsightsGenerator } from '../insights/logos-insights-generator.service';
import { MessagingInsightsGenerator } from '../insights/messaging-insights-generator.service';
import { ColorInsightsGenerator } from '../insights/color-insights-generator.service';
import { GazeAndEmotionsInsightsGenerator } from '../insights/gaze-and-emotions-generator.service';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { SceneryElementInsightsGenerator } from './scenery-elements-insights-generator.service';
import { ObjectInsightsGenerator } from './object-insights-generator.service';
import { MessagingTypeInsightsGenerator } from './messaging-type-insights-generator.service';
import { CallToActionTypeInsightsGenerator } from './call-to-action-type-insights-generator.service';

@Injectable()
export class SingleTopicGeneratorsService {
  constructor(
    private readonly demographicInsightsService: DemographicInsightsGenerator,
    private readonly bestCallToActionInsightsService: BestCallToActionInsightsGenerator,
    private readonly callToActionTypeInsightsGenerator: CallToActionTypeInsightsGenerator,
    private readonly bodyPartsFocusInsightsService: BodyPartFocusInsightsGenerator,
    private readonly sceneryGroupInsightsService: SceneryGroupInsightsGenerator,
    private readonly humanPresenceInsightsService: HumanPresenceInsightsGenerator,
    private readonly logoInsightsGenerator: LogoInsightsGenerator,
    private readonly messagingInsightsGenerator: MessagingInsightsGenerator,
    private readonly messagingTypeInsightsGenerator: MessagingTypeInsightsGenerator,
    private readonly colorInsightsGenerator: ColorInsightsGenerator,
    private readonly gazeAndEmotionsInsightsGenerator: GazeAndEmotionsInsightsGenerator,
    private readonly sceneryElementInsightsGenerator: SceneryElementInsightsGenerator,
    private readonly objectInsightsGenerator: ObjectInsightsGenerator,
  ) {}

  getAllInsightsGenerators(): SingleTopicInsightsGeneratorBaseClassService[] {
    return [
      this.messagingInsightsGenerator,
      this.messagingTypeInsightsGenerator,
      this.humanPresenceInsightsService,
      this.sceneryGroupInsightsService,
      this.bestCallToActionInsightsService,
      this.callToActionTypeInsightsGenerator,
      this.colorInsightsGenerator,
      this.logoInsightsGenerator,
      this.demographicInsightsService,
      this.sceneryElementInsightsGenerator,
      this.bodyPartsFocusInsightsService,
      this.objectInsightsGenerator,
      this.gazeAndEmotionsInsightsGenerator,
    ];
  }
}
