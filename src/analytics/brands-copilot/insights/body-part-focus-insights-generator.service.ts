import { Injectable } from '@nestjs/common';
import { Co<PERSON>lotReport, ElementsLift } from '../report/copilot-report.model';
import {
  filterElementsByIdAndParent,
  filterElementsByMinNumberOfCreatives,
  InsightGeneratorSource,
} from './insight-utils.utils';
import { TagsMetadataService } from '../tag-metadata/tag-metadata.service';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class BodyPartFocusInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(
    llmProviderService: LlmClientRouter,
    private readonly tagsMetadataService: TagsMetadataService,
  ) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const bodyPartsFocusTags =
      await this.tagsMetadataService.getBodyPartsFocusTags();
    const personBodyPartsTags = filterElementsByIdAndParent(
      report.elementsLift,
      bodyPartsFocusTags,
      ['person'],
    );
    return filterElementsByMinNumberOfCreatives(personBodyPartsTags, 5);
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's body part focus creative element historical performance data.
      For each element that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight. 
      Insight should focus on which body parts presence in ads affect performance (negatively or positively)  
      You can focus the insight on a single element, or multiple elements that tell a bigger story
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad “creatives with the element ‘Wrist’ and 'Ankle' perform better.” Good: “Ads that focusing on model wrist/ankle.”
      Bad "Ads featuring element 'face' perform worse" Good: "Ads showing faces perform worst"
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.BodyPartFocusInsightsGenerator;
  }
}
