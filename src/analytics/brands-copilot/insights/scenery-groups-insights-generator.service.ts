import { Injectable } from '@nestjs/common';
import { LlmOpenAiClient } from '../llm/llm-open-ai-client.service';
import { CopilotReport } from '../report/copilot-report.model';
import {
  filterElementsById,
  filterElementsByIdAndParent,
  filterElementsByMinNumberOfCreatives,
  filterElementsByParent,
  InsightGeneratorSource,
} from './insight-utils.utils';
import { TagsMetadataService } from '../tag-metadata/tag-metadata.service';
import { SingleTopicInsightsGeneratorBaseClassService } from './single-topic-insights-generator-base-class.service';
import { ElementsLift } from '../report/copilot-report.model';
import { undefined } from 'zod';
import { LlmClientRouter } from "../llm/llm-client-router.service";

@Injectable()
export class SceneryGroupInsightsGenerator extends SingleTopicInsightsGeneratorBaseClassService {
  constructor(
    llmProviderService: LlmClientRouter,
    private readonly tagsMetadataService: TagsMetadataService,
  ) {
    super(llmProviderService);
  }

  protected async filterTags(report: CopilotReport): Promise<ElementsLift[]> {
    const sceneryGroupTags =
      await this.tagsMetadataService.getSceneryGroupsTags();
    const allSceneryGroupElements = filterElementsByIdAndParent(
      report.elementsLift,
      sceneryGroupTags,
      ['object'],
    );
    return filterElementsByMinNumberOfCreatives(allSceneryGroupElements, 10);
  }

  protected getTagExplanationPrompt(): string {
    return `
      User will supply Vidmob's scenery creative element historical performance data.
      For each scenery that Vidmob computer vision detected in brand ads, you will get a KPI.
    `;
  }

  protected getInsightsInstructionsPrompt(): string {
    return `
      Produce a single insight that will include all report element. 
      Insight should be able to answer the question what kind of scenery affect performance the most (positively or negatively) outdoors, indoors or city.
      Each of the report elements needs to be present in insight elements field.
      If all elements a negative effect on performance emphasize that outdoor/indoor/city environment deteriorate performance, this should be the main insight in such case. 
      Suggest to try more abstract environments or look for a specific elements in outdoor/indoor/city that pushed performance up.
    `;
  }

  protected getNaturalLanguageExamplePrompt(): string {
    return `
      Bad: “creatives with the element ‘Indoors’” Good: “Ads that happening indoors.”
    `;
  }

  protected getInsightGeneratorType(): InsightGeneratorSource {
    return InsightGeneratorSource.SceneryGroupInsightsGenerator;
  }
}
