import { AnalyticsKpiResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/analyticsKpiResponseDto';

export interface StatisticalSignificance {
  pValue: number;
  isStatisticallySignificant: boolean;
}

export interface ElementsLift {
  id: string;
  type: string;
  typeValue: string;
  kpiValue: number;
  kpiLift: number;
  parentElements: string[];
  impressions: number;
  numberOfCreatives: number;
  statisticalSignificance: StatisticalSignificance;
  isMatchedByParentElement: boolean;
}

export interface CreativeElementReport {
  kpiAverage: number;
  kpiName: string;
  kpiFormat: AnalyticsKpiResponseDto.FormatEnum;
  isInverseHealthKpi: boolean;
  impressions: number;
  numberOfCreatives: number;
  elementsLift: ElementsLift[];
  currency?: string;
}

export type CopilotReport = CreativeElementReport;
