import { Injectable } from '@nestjs/common';
import {
  CreativeElement,
  CreativeElementEntityType,
} from '../tag-identification/creative-elements.model';
import { CreativeElementReport, ElementsLift } from './copilot-report.model';
import { CompareReportClient } from '../compare-report/compare-report.client';
import { CompareReportPayload } from '../compare-report/compare-report.interfaces';
import { BrandCopilotScope } from '../model/request.model';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { CopilotKpiService } from '../kpi/copilot-kpi.service';
import { KpiInfo } from '../kpi/kpi.model';
import { KpiUtils } from '../kpi/kpi.utils';
import { buildCustomTagTimeRange } from './copilot-report.utils';
import { AdvancedFilterTransformerService } from './advanced-filter-transformer.service';

enum QueryType {
  TYPE_BREAKDOWN = 'TYPE_BREAKDOWN',
  VALUE_LIST = 'VALUE_LIST',
}

@Injectable()
export class BrandCopilotElementImpactReportService {
  private readonly MAX_ELEMENTS_TO_RETURN = 40;

  private readonly TAGS_TO_FILTER_OUT: Set<string> = new Set(
    [
      'Keyword',
      'Call-to-Action',
      'Call-to-action Type',
      'Benefit Messaging',
    ].map((tag) => tag.toLowerCase()),
  );

  private readonly typeToTypeLabelMap: { [key: string]: string } = {
    'COLOR:DOMINANT_COLORS:CATEGORY': 'color',
    LABEL: 'object',
    'LABEL:CUSTOM': 'custom element',
    'FACE:GAZE_DIRECTION': 'gaze and emotion',
    'COLOR:TEMPERATURE:CATEGORY': 'color',
    'FACE:EMOTION': 'gaze and emotion',
    'COLOR:TEXT_CONTRAST:CATEGORY': 'color',
    LOGO: 'branding',
    'LABEL:CUSTOM:GROUP': 'element set',
    'COLOR:VIBRANCY:CATEGORY': 'color',
    'COLOR:CONTRAST:CATEGORY': 'color',
    'FACE:SMILE': 'gaze and emotion',
    'TEXT:WORD_EMPHASIS': 'messaging',
    'CELEBRITY:NAME': 'celebrity',
    'CTA:PRESENT': 'messaging',
    'CTA:BY_LINE': 'messaging',
    'APERTURE:MESSAGING': 'messaging',
    'APERTURE:PRODUCTION_LEVEL': 'Production Type',
    'APERTURE:PRODUCTION_TYPE': 'Production Level',
  };

  constructor(
    private readonly compareReportClient: CompareReportClient,
    private readonly kpiService: CopilotKpiService,
    private readonly advancedFilterTransformer: AdvancedFilterTransformerService,
  ) {}

  async getReportWithTagsFilter(
    scope: BrandCopilotScope,
    tags: CreativeElement[],
    authToken: string,
  ): Promise<CreativeElementReport> {
    try {
      const originalScope = JSON.parse(JSON.stringify(scope));

      const queryType = this.determineQueryType(tags);
      // const tagFilter = this.buildTagFilter(tags, queryType);
      const mediaTypes =
        BrandCopilotElementImpactReportService.getMediaTypes(scope);
      const kpiInfo = await this.getKpiInfo(scope);

      const scopeForPayload = JSON.parse(JSON.stringify(scope));

      const payload = this.buildCompareReportPayload(
        scopeForPayload,
        mediaTypes,
        kpiInfo,
        // tagFilter,
      );

      const creativeElementReport = await this.fetchAndParseReport(
        scopeForPayload,
        payload,
        authToken,
      );

      const filteredElements = this.filterElementsByTags(
        creativeElementReport.elementsLift,
        tags,
        queryType,
      ).filter(
        (element) => !this.TAGS_TO_FILTER_OUT.has(element.id.toLowerCase()),
      );

      // TODO: Add step to validate actual relevancy of tags to the question
      creativeElementReport.elementsLift = this.extractBestAndWorstKpiElements(
        filteredElements,
        this.MAX_ELEMENTS_TO_RETURN / 2,
        this.MAX_ELEMENTS_TO_RETURN / 2,
      );

      // Restore the original filters in scope for saving in the DB.
      scope.filters = originalScope.filters;

      return creativeElementReport;
    } catch (error) {
      throw new Error(
        `Error during element impact report retrieval: ${error.message}`,
      );
    }
  }

  async getReportForAllTags(
    scope: BrandCopilotScope,
    authToken: string,
  ): Promise<CreativeElementReport> {
    try {
      const mediaTypes =
        BrandCopilotElementImpactReportService.getMediaTypes(scope);
      const kpiInfo = await this.getKpiInfo(scope);

      const payload = this.buildCompareReportPayload(
        scope,
        mediaTypes,
        kpiInfo,
      );
      // creativeElementReport.elementsLift =
      //   this.selectBestAndWorstElementsByKpiLift(
      //     creativeElementReport.elementsLift,
      //     this.MAX_ELEMENTS_TO_RETURN / 2,
      //     this.MAX_ELEMENTS_TO_RETURN / 2,
      //   );

      return await this.fetchAndParseReport(scope, payload, authToken);
    } catch (error) {
      throw new Error(
        `Error during element impact report retrieval: ${error.message}`,
      );
    }
  }

  private async getKpiInfo(scope: BrandCopilotScope) {
    const kpiId = scope.kpiOptions.kpiIds[0];
    const adAccountIds = scope.adAccounts.map(
      (account) => account.platformAccountId,
    );
    return await this.kpiService
      .getKpiInfo(scope.platform, [kpiId], adAccountIds)
      .then((kpiMap) => kpiMap[kpiId]);
  }

  private async fetchAndParseReport(
    scope: BrandCopilotScope,
    payload: CompareReportPayload,
    authToken: string,
  ) {
    const reportData = await this.compareReportClient.fetchReport(
      payload,
      authToken,
    );
    const adAccountIds = scope.adAccounts.map(
      (account) => account.platformAccountId,
    );
    const kpiId = scope.kpiOptions.kpiIds[0];
    const kpiInfo: KpiInfo = await this.kpiService
      .getKpiInfo(scope.platform, [kpiId], adAccountIds)
      .then((kpiMap) => kpiMap[kpiId]);

    return this.parseReportOutput(reportData, kpiInfo, scope.platform);
  }

  private determineQueryType(tags: CreativeElement[]): QueryType {
    const entityTypeSet = new Set(tags.map((tag) => tag.entityType));

    if (entityTypeSet.size > 1) {
      throw new Error(
        'Multiple entity types detected. Only one entity type is supported.',
      );
    }

    const entityType = tags[0].entityType;
    return entityType === CreativeElementEntityType.CREATIVE_ELEMENT
      ? QueryType.VALUE_LIST
      : QueryType.TYPE_BREAKDOWN;
  }

  static getMediaTypes(scope: BrandCopilotScope): string[] {
    return scope.filters.mediaTypes && scope.filters.mediaTypes.length > 0
      ? scope.filters.mediaTypes
      : ['VIDEO', 'IMAGE'];
  }

  private getTypeLabelFromType(type: string): string {
    return this.typeToTypeLabelMap[type];
  }

  private buildCompareReportPayload(
    scope: BrandCopilotScope,
    mediaTypes: string[],
    kpiInfo: KpiInfo,
    tagFilter?: any,
  ): CompareReportPayload {
    const normalizedPlatform = scope.platform.toLowerCase();
    // Get the base transformed filters
    let transformedFilters = this.advancedFilterTransformer.transformFilters(
      scope.filters,
      normalizedPlatform,
    );

    if (normalizedPlatform === Platform.INSTAGRAM_PAGE) {
      const startTimestamp = new Date(scope.startDate).getTime();
      const endTimestamp = new Date(scope.endDate).getTime();
      transformedFilters = {
        'post[timestamp]': [`$gte(${startTimestamp})`, `$lte(${endTimestamp})`],
        page: scope.adAccounts.map((ad) => ad.platformAccountId),
        ...(transformedFilters.ad ? { ad: transformedFilters.ad } : {}),
      };
    } else {
      transformedFilters.account = scope.adAccounts.map(
        (adAccount) => adAccount.platformAccountId,
      );
    }

    const dimensions =
      this.advancedFilterTransformer.getDimensions(normalizedPlatform);

    const customTagTimeRange = buildCustomTagTimeRange(kpiInfo);
    const kpiOptionsWithCustomTagTimeRange = {
      kpiIds: [...scope.kpiOptions?.kpiIds],
      ...(scope.kpiOptions?.currencyObject?.id && {
        currency: scope.kpiOptions.currencyObject.id,
      }),
      customTagTimeRange: customTagTimeRange,
    };
    const groupBy = {
      type: 'filter',
      values: [
        {
          title: '__average',
          filter: { 'analytics_media[duration]': [] },
        },
      ],
    };

    const options: any = {
      includeTracker: true,
      includeMissingGroups: false,
      includeMergedStats: false,
      includeApertureTags: true,
    };

    if (
      this.advancedFilterTransformer.isCreatedByVidmobAdvancedFilterSet(scope)
    ) {
      this.advancedFilterTransformer.addCreatedByVidmobAdvancedFilterToCompareReportOptions(
        options,
        scope,
      );
    }

    return {
      platform: normalizedPlatform,
      startDate: scope.startDate,
      endDate: scope.endDate,
      filter: {
        ...transformedFilters,
        ...(tagFilter || {}),
      },
      metrics: this.advancedFilterTransformer.fetchMetric(normalizedPlatform),
      fileTypes: mediaTypes,
      options: options,
      kpiOptions: kpiOptionsWithCustomTagTimeRange,
      dimensions: dimensions,
      elementBy: {
        type: 'tagAndType',
        average: 'element',
        minTagConfidence: 75,
        minTagConfidences: { defaultTo: 75, 'CELEBRITY:NAME': 90 },
      },
      groupBy: groupBy,
      averageBy: { type: 'custom' },
      aggregateAllAdTypes: true,
      organizationId: scope.organizationId,
      workspaceIds: scope.workspaces.map((w) => w.id),
    };
  }

  private getNumberOfCreativesDimensionName(platform: Platform) {
    if (
      platform === Platform.FACEBOOK_PAGE ||
      platform === Platform.INSTAGRAM_PAGE
    ) {
      return '$count(media)';
    } else {
      return '$count(advideo)';
    }
  }

  private readonly CONFIDENCE_LEVEL_95 = 0.05;

  private parseReportOutput(
    httpResponseData: any,
    kpi: KpiInfo,
    platform: Platform,
  ): CreativeElementReport {
    const data = httpResponseData.data;
    const numberOfCreativesDimension =
      this.getNumberOfCreativesDimensionName(platform);

    const kpiAverage = data.groups?.__average?.stat?.[kpi.id] ?? 0;
    const totalImpressions = data.groups?.__average?.impressions ?? 0;
    const totalNumberOfCreatives =
      data.groups?.__average?.metadata?.dimensions?.[
        numberOfCreativesDimension
      ] ?? 0;
    const elementsLift: ElementsLift[] = Object.entries(
      data.elements ?? [],
    ).map(([, elementValue]: [string, any]) => ({
      id: elementValue.title,
      type: elementValue.genericType,
      typeValue: elementValue.tagType,
      kpiValue: elementValue.groups.__average.stat[kpi.id],
      kpiLift: elementValue.groups.__average.statLift[kpi.id],
      parentElements: elementValue.parents || [],
      impressions: elementValue.groups.__average.impressions,
      numberOfCreatives:
        elementValue.groups.__average.metadata.dimensions[
          numberOfCreativesDimension
        ],
      statisticalSignificance: {
        pValue: elementValue.groups.__average.ztest[kpi.id].group.pValue,
        isStatisticallySignificant:
          KpiUtils.isAlwaysStatisticalSignificantType(kpi.format) ||
          elementValue.groups.__average.ztest[kpi.id].group.pValue <
            this.CONFIDENCE_LEVEL_95,
      },
      isMatchedByParentElement: false,
    }));

    return {
      kpiAverage: kpiAverage,
      kpiName: kpi.name,
      kpiFormat: kpi.format,
      isInverseHealthKpi: kpi.inverseHealth,
      impressions: totalImpressions,
      numberOfCreatives: totalNumberOfCreatives,
      elementsLift: elementsLift,
      ...(data.currency ? { currency: data.currency } : {}),
    };
  }

  // Originally I was using media_tag[value_label] / media_tag[type_label] to get only relevant tags from compare report
  // Yet, compare report return different total KPIs (kpi average, number of creatives) when tag filtered are supplied,
  // probably because some data is being filtered out by the joins. In order to have consistent numbers to element
  // impact, I won't use media tag filters on compare report request, but rather get data for all tags and filter
  // relevant tags in memory.
  // In order to support hierarchy (for example: if Animal tag was passed, return dog, cat tags as well, based on
  // element impact element hierarchy) we are matching against element parents as well.
  private filterElementsByTags(
    elementsLift: ElementsLift[],
    tags: CreativeElement[],
    queryType: QueryType,
  ): ElementsLift[] {
    if (queryType === QueryType.VALUE_LIST) {
      const tagValuesSet = new Set(tags.map((tag) => tag.value.toUpperCase()));

      elementsLift.forEach((element) => {
        const isMatchedByTagId = tagValuesSet.has(element.id.toUpperCase());
        const isMatchedByParent = element.parentElements.some((parent) =>
          tagValuesSet.has(parent.toUpperCase()),
        );
        if (isMatchedByParent && !isMatchedByTagId) {
          element.isMatchedByParentElement = true;
        }
      });

      return elementsLift.filter(
        (element) =>
          tagValuesSet.has(element.id.toUpperCase()) ||
          element.isMatchedByParentElement,
      );
    } else if (queryType === QueryType.TYPE_BREAKDOWN) {
      const inputTypes = new Set(tags.map((tag) => tag.type));
      return elementsLift.filter((element) =>
        inputTypes.has(element.typeValue),
      );
    } else {
      return elementsLift;
    }
  }

  private selectBestAndWorstElementsByKpiLift(
    elements: ElementsLift[],
    numberOfBestElements: number,
    numberOfWorstElements: number,
  ): ElementsLift[] {
    const totalElements = elements.length;
    if (totalElements <= numberOfBestElements + numberOfWorstElements) {
      return elements;
    } else {
      const sortedElements = [...elements].sort(
        (a, b) => a.kpiLift - b.kpiLift,
      );
      const worstElements = sortedElements.slice(0, numberOfWorstElements);
      const bestElements = sortedElements.slice(-numberOfBestElements);
      return worstElements.concat(bestElements);
    }
  }

  // Passing too many elements to an LLM may increase hallucinations and irrelevant responses.
  // This function selects the most relevant elements from a report output by prioritizing elements with the best and worst KPI values.
  // It also gives priority to tags that were directly mentioned over tags matched via their parent tags.
  // For example, if a user asks: "Should I show bananas in my ads or other food items?"
  // There may be many food items, but regardless of their KPI values, we want to return the 'banana' element.
  // In this case, the 'banana' tag has precedence over other food items that were matched only because 'food' is a parent tag.
  // This avoids situations where directly mentioned tags are overshadowed by numerous related tags.
  private extractBestAndWorstKpiElements(
    elements: ElementsLift[],
    numberOfBestElements: number,
    numberOfWorstElements: number,
  ): ElementsLift[] {
    const numberInputTags = elements.length;
    const numberOfTagsToReturn = numberOfBestElements + numberOfWorstElements;
    if (numberInputTags <= numberOfTagsToReturn) {
      return elements;
    }

    const matchByTagId = elements.filter(
      (element) => !element.isMatchedByParentElement,
    );
    const matchedByTagParent = elements.filter(
      (element) => element.isMatchedByParentElement,
    );

    const selectedMatchByTagId = this.selectBestAndWorstElementsByKpiLift(
      matchByTagId,
      numberOfBestElements,
      numberOfWorstElements,
    );

    if (selectedMatchByTagId.length >= numberOfTagsToReturn) {
      return selectedMatchByTagId;
    }

    const numberOfParentTagsToSelect =
      numberOfTagsToReturn - selectedMatchByTagId.length;
    const selectedMatchByTagParent = this.selectBestAndWorstElementsByKpiLift(
      matchedByTagParent,
      Math.ceil(numberOfParentTagsToSelect / 2),
      Math.ceil(numberOfParentTagsToSelect / 2),
    );

    return selectedMatchByTagId.concat(selectedMatchByTagParent);
  }
}
