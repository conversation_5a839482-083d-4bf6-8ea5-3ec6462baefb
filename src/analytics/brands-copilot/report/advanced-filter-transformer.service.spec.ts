import { AdvancedFilterTransformerService } from './advanced-filter-transformer.service';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { BrandCopilotScope } from '../brand-copilot-next.dto';

describe('AdvancedFilterTransformerService', () => {
  let service: AdvancedFilterTransformerService;

  beforeEach(() => {
    service = new AdvancedFilterTransformerService();
  });

  describe('transformFilters', () => {
    it('merges objectives into the proper objective key for non-Twitter channels', () => {
      const input = { objectives: ['BRAND_AWARENESS', 'REACH'] };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result['campaign[objective]']).toEqual([
        'BRAND_AWARENESS',
        'REACH',
      ]);
      expect(result.objectives).toBeUndefined();
    });

    it('merges objectives into lineitem[objective] for Twitter', () => {
      const input = { objectives: ['BRAND_AWARENESS', 'REACH'] };
      const result = service.transformFilters({ ...input }, Platform.TWITTER);
      expect(result['lineitem[objective]']).toEqual([
        'BRAND_AWARENESS',
        'REACH',
      ]);
      expect(result.objectives).toBeUndefined();
    });

    it('converts mediaTypes to uppercase and then removes mediaTypes and fileTypes', () => {
      const input = { mediaTypes: ['video', 'image'] };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.mediaTypes).toBeUndefined();
      expect(result.fileTypes).toBeUndefined();
    });

    describe('adTypes transformation', () => {
      it('for FACEBOOK, if placements provided, final placement equals placements', () => {
        const input = { adTypes: ['VIDEO', 'CAROUSEL'], placements: ['TOP'] };
        const result = service.transformFilters(
          { ...input },
          Platform.FACEBOOK,
        );
        expect(result.placement).toEqual(['TOP']);
        expect(result['ad[type]']).toBeUndefined();
      });

      it('for FACEBOOK, if placements are not provided, final placement equals adTypes', () => {
        const input = { adTypes: ['VIDEO', 'CAROUSEL'] };
        const result = service.transformFilters(
          { ...input },
          Platform.FACEBOOK,
        );
        expect(result.placement).toEqual(['VIDEO', 'CAROUSEL']);
        expect(result['ad[type]']).toBeUndefined();
      });

      it('for ADWORDS, assigns adTypes to ad[type]', () => {
        const input = { adTypes: ['APP_AD', 'OTHER'] };
        const result = service.transformFilters({ ...input }, Platform.ADWORDS);
        expect(result['ad[type]']).toEqual(['APP_AD', 'OTHER']);
      });

      it('for DV360, assigns adTypes to adgroup[type]', () => {
        const input = { adTypes: ['Non Youtube', 'SOMETHING'] };
        const result = service.transformFilters({ ...input }, Platform.DV360);
        expect(result['adgroup[type]']).toEqual(['Non Youtube', 'SOMETHING']);
      });

      it('for other platforms (e.g., INSTAGRAM), defaults adTypes to ad[type]', () => {
        const input = { adTypes: ['STANDARD'] };
        const result = service.transformFilters(
          { ...input },
          Platform.INSTAGRAM_PAGE,
        );
        expect(result['ad[type]']).toEqual(['STANDARD']);
      });
    });

    it('transforms adIdentifiers into ad by extracting id', () => {
      const input = { adIdentifiers: [{ id: 'ad1' }, { id: 'ad2' }] };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.ad).toEqual(['ad1', 'ad2']);
      expect(result.adIdentifiers).toBeUndefined();
    });

    it('merges placements into placement when adTypes is not present', () => {
      const input = { placements: ['TOP'] };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.placement).toEqual(['TOP']);
    });

    it('leaves adsetIdentifiers unchanged', () => {
      const input = { adsetIdentifiers: ['adset1', 'adset2'] };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.adsetIdentifiers).toEqual(['adset1', 'adset2']);
    });

    it('leaves campaignIdentifiers unchanged', () => {
      const input = { campaignIdentifiers: ['camp1', 'camp2'] };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.campaignIdentifiers).toEqual(['camp1', 'camp2']);
    });

    it('removes createdByVidmob after transformation', () => {
      const input = { createdByVidmob: true };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.createdByVidmob).toBeUndefined();
    });

    it('converts adImpressionsRange into metric strings under ad', () => {
      const input = { adImpressionsRange: { minimum: 100, maximum: 500 } };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.ad).toEqual([
        '$metric(impressions, gte, 100)',
        '$metric(impressions, lte, 500)',
      ]);
      expect(result.adImpressionsRange).toBeUndefined();
    });

    it('converts creativeImpressionsRange into metric strings under advideo', () => {
      const input = { creativeImpressionsRange: { minimum: 50, maximum: 250 } };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.advideo).toEqual([
        '$metric(impressions, gte, 50)',
        '$metric(impressions, lte, 250)',
      ]);
      expect(result.creativeImpressionsRange).toBeUndefined();
    });

    it('leaves unknown keys intact', () => {
      const input = { unknownKey: 'unknownValue' };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      expect(result.unknownKey).toEqual('unknownValue');
    });

    it('transforms campaignIds to campaign', () => {
      const inputFilters = {
        campaignIds: ['123456789'],
        mediaTypes: ['video'],
        objectives: ['objective1'],
      };
      const transformedFilters = service.transformFilters(
        inputFilters,
        Platform.FACEBOOK,
      );
      expect(transformedFilters.campaign).toEqual(['123456789']);
      expect(transformedFilters.campaignIds).toBeUndefined();
    });

    it('removes all keys that are in the cleanup list', () => {
      const input = {
        objectives: ['A'],
        campaignObjectives: ['B'],
        mediaTypes: ['video'],
        adTypes: ['X'],
        adImpressionsRange: { minimum: 100 },
        creativeImpressionsRange: { minimum: 50 },
        placements: ['P'],
        adIdentifiers: [{ id: 'ad1' }],
      };
      const result = service.transformFilters({ ...input }, Platform.FACEBOOK);
      const keysToRemove = [
        'objectives',
        'campaignObjectives',
        'mediaTypes',
        'adTypes',
        'adImpressionsRange',
        'creativeImpressionsRange',
        'placements',
        'adIdentifiers',
        'campaignIds',
        'fileTypes',
      ];
      keysToRemove.forEach((key) => {
        expect(result[key]).toBeUndefined();
      });
    });
  });

  describe('fetchCreatedByVidmobFilter', () => {
    it('returns true if createdByVidmob is true', () => {
      const scope: any = {
        filters: { createdByVidmob: true },
      };
      expect(service.fetchCreatedByVidmobFilter(scope)).toBe(true);
    });

    it('returns false if createdByVidmob is false', () => {
      const scope: any = {
        filters: { createdByVidmob: false },
      };
      expect(service.fetchCreatedByVidmobFilter(scope)).toBe(false);
    });

    it('returns undefined if createdByVidmob is not defined', () => {
      const scope: BrandCopilotScope = { filters: {} } as BrandCopilotScope;
      expect(service.fetchCreatedByVidmobFilter(scope)).toBe(undefined);
    });

    it('returns undefined if createdByVidmob is not a boolean', () => {
      const scope: any = { filters: { createdByVidmob: 'true' } };
      expect(service.fetchCreatedByVidmobFilter(scope)).toBe(undefined);
    });
  });

  describe('isCreatedByVidmobAdvancedFilterSet', () => {
    it('should return true when createdByVidmob is a boolean true', () => {
      const scope: any = { filters: { createdByVidmob: true } };
      expect(
        service.isCreatedByVidmobAdvancedFilterSet(scope as BrandCopilotScope),
      ).toBe(true);
    });

    it('should return true when createdByVidmob is a boolean false', () => {
      const scope: any = { filters: { createdByVidmob: false } };
      // Since the filter is defined as a boolean, we return true (indicating that the advanced filter is set)
      expect(
        service.isCreatedByVidmobAdvancedFilterSet(scope as BrandCopilotScope),
      ).toBe(true);
    });

    it('should return false when createdByVidmob is not defined', () => {
      const scope: any = { filters: {} };
      expect(
        service.isCreatedByVidmobAdvancedFilterSet(scope as BrandCopilotScope),
      ).toBe(false);
    });

    it('should return false when createdByVidmob is not a boolean', () => {
      const scope: any = { filters: { createdByVidmob: 'true' } };
      expect(
        service.isCreatedByVidmobAdvancedFilterSet(scope as BrandCopilotScope),
      ).toBe(false);
    });
  });

  describe('addCreatedByVidmobAdvancedFilterToCompareReportOptions', () => {
    it('should add createdByVidmob to options when defined in scope', () => {
      const scope: any = { filters: { createdByVidmob: true } };
      const options: any = {};
      service.addCreatedByVidmobAdvancedFilterToCompareReportOptions(
        options,
        scope as BrandCopilotScope,
      );
      expect(options.createdByVidmob).toBe(true);
    });

    it('should leave options unchanged when createdByVidmob is not defined', () => {
      const scope: any = { filters: {} };
      const options: any = {};
      service.addCreatedByVidmobAdvancedFilterToCompareReportOptions(
        options,
        scope as BrandCopilotScope,
      );
      expect(options.createdByVidmob).toBeUndefined();
    });
  });

  describe('getDimensions', () => {
    it('returns correct dimensions for Twitter', () => {
      const dims = service.getDimensions(Platform.TWITTER);
      expect(dims).toEqual([
        '$count(tweet)',
        '$count(advideo)',
        '$count(lineitem)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for InstagramPage', () => {
      const dims = service.getDimensions(Platform.INSTAGRAM_PAGE);
      expect(dims).toEqual([
        '$count(post)',
        '$count(media)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for Facebook', () => {
      const dims = service.getDimensions(Platform.FACEBOOK);
      expect(dims).toEqual([
        '$count(ad)',
        '$count(advideo)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for FacebookPage', () => {
      const dims = service.getDimensions(Platform.FACEBOOK_PAGE);
      expect(dims).toEqual([
        '$count(post)',
        '$count(media)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for DV360', () => {
      const dims = service.getDimensions(Platform.DV360);
      expect(dims).toEqual([
        '$count(ad)',
        '$count(advideo)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for Snapchat', () => {
      const dims = service.getDimensions(Platform.SNAPCHAT);
      expect(dims).toEqual([
        '$count(ad)',
        '$count(advideo)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for Reddit', () => {
      const dims = service.getDimensions(Platform.REDDIT);
      expect(dims).toEqual([
        '$count(ad)',
        '$count(advideo)',
        '$count(campaign)',
        '$sum(media_tag[vrs])',
      ]);
    });

    it('returns correct dimensions for Amazon Advertising DSP', () => {
      const dims = service.getDimensions(Platform.AMAZON_ADVERTISING_DSP);
      expect(dims).toEqual(['$count(campaign)', '$sum(media_tag[vrs])']);
    });
  });

  describe('fetchMetric', () => {
    it('returns post_engaged_users for FACEBOOK and FACEBOOK_PAGE', () => {
      expect(service.fetchMetric(Platform.FACEBOOK)).toEqual([]);
      expect(service.fetchMetric(Platform.FACEBOOK_PAGE)).toEqual([
        'post_engaged_users',
      ]);
    });

    it('returns engagement for INSTAGRAM_PAGE', () => {
      expect(service.fetchMetric(Platform.INSTAGRAM_PAGE)).toEqual([
        'engagement',
      ]);
    });

    it('returns empty array for unknown platform', () => {
      expect(service.fetchMetric('unknown')).toEqual([]);
    });
  });
});
