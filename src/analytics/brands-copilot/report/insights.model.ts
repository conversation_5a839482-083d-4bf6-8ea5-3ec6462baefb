import { CreativeKey } from '../brand-copilot-next.dto';
import { CopilotReport } from './copilot-report.model';
import { AnalyticsKpiResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/analyticsKpiResponseDto';
import { UUID } from 'crypto';
import { InsightGeneratorSource } from "../insights/insight-utils.utils";

export interface InsightAttributes {
  platform: string;
  brands: string[];
  startDate: string;
  endDate: string;
  kpiName: string;
  kpiFormat: AnalyticsKpiResponseDto.FormatEnum;
  isInverseHealthKpi: boolean;
}

export interface Insight {
  id: UUID;
  title: string;
  findings: string;
  recommendation: string;
  report: CopilotReport;
  version: number;
  insightLibraryId?: string;
  source?: InsightGeneratorSource;
}

export interface InsightWithCreativesKeys extends Insight {
  attributes: InsightAttributes;
  creatives: <PERSON>Key[];
}
