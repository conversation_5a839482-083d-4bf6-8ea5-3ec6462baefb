import { Injectable } from '@nestjs/common';
import { BrandCopilotScope } from '../brand-copilot-next.dto';
import { Platform } from '@vidmob/vidmob-nestjs-common';

@Injectable()
export class AdvancedFilterTransformerService {
  transformFilters(filters: any, platform: string): any {
    // Clone filters to avoid mutating the original object
    let modFilters = { ...filters };
    const payload: any = {};
    const normalizedPlatform = platform.toLowerCase();

    // Convert mediaTypes to fileTypes (uppercase)
    if (modFilters.mediaTypes) {
      payload.fileTypes = modFilters.mediaTypes.map((type: string) =>
        type.toUpperCase(),
      );
    }

    // Merge objectives and campaignObjectives into a single key
    if (modFilters.objectives || modFilters.campaignObjectives) {
      const objectiveKey =
        normalizedPlatform === Platform.TWITTER
          ? 'lineitem[objective]'
          : 'campaign[objective]';
      const objectives = [
        ...(modFilters.objectives || []),
        ...(modFilters.campaignObjectives || []),
      ];
      modFilters[objectiveKey] = objectives;
    }

    // Process adTypes based on platform
    if (modFilters.adTypes) {
      // For Facebook pages, merge placements and adTypes into "placement"
      if (
        normalizedPlatform === Platform.FACEBOOK ||
        normalizedPlatform === Platform.FACEBOOK_PAGE
      ) {
        modFilters.placement = [
          ...(modFilters.placements || []),
          ...modFilters.adTypes,
        ];
      } else if (normalizedPlatform === Platform.ADWORDS) {
        modFilters['ad[type]'] = modFilters.adTypes;
      } else if (normalizedPlatform === Platform.DV360) {
        modFilters['adgroup[type]'] = modFilters.adTypes;
      } else {
        modFilters['ad[type]'] = modFilters.adTypes;
      }
    }

    // Process adImpressionsRange using pushMetric helper
    if (modFilters.adImpressionsRange) {
      modFilters.ad = modFilters.ad || [];
      pushMetric(
        modFilters.ad,
        'impressions',
        'gte',
        modFilters.adImpressionsRange.minimum,
      );
      pushMetric(
        modFilters.ad,
        'impressions',
        'lte',
        modFilters.adImpressionsRange.maximum,
      );
    }

    // Process creativeImpressionsRange similarly
    if (modFilters.creativeImpressionsRange) {
      modFilters.advideo = modFilters.advideo || [];
      pushMetric(
        modFilters.advideo,
        'impressions',
        'gte',
        modFilters.creativeImpressionsRange.minimum,
      );
      pushMetric(
        modFilters.advideo,
        'impressions',
        'lte',
        modFilters.creativeImpressionsRange.maximum,
      );
    }

    // Process adIdentifiers
    if (modFilters.adIdentifiers) {
      modFilters.ad = (modFilters.ad || []).concat(
        modFilters.adIdentifiers.map((item: any) =>
          typeof item === 'object' ? item.id : item,
        ),
      );
    }

    // Direct assignments for campaign and ad IDs
    if (modFilters.campaignIds) {
      modFilters.campaign = modFilters.campaignIds;
    }
    if (modFilters.adsetIds) {
      modFilters.adset = modFilters.adsetIds;
    }
    if (modFilters.adIds) {
      modFilters.ad = modFilters.adIds;
    }
    if (modFilters.placements) {
      modFilters.placement = modFilters.placements;
    }

    // Merge in any additional payload adjustments (like fileTypes)
    modFilters = { ...modFilters, ...payload };

    // Remove keys that are not needed in the final payload
    const keysToRemove = [
      'objectives',
      'campaignObjectives',
      'mediaTypes',
      'fileTypes',
      'adTypes',
      'adImpressionsRange',
      'creativeImpressionsRange',
      'placements',
      'adIdentifiers',
      'campaignIds',
      'adsetIds',
      'adIds',
      'createdByVidmob',
    ];
    keysToRemove.forEach((key) => delete modFilters[key]);

    return modFilters;
  }

  fetchCreatedByVidmobFilter(scope: BrandCopilotScope): boolean | undefined {
    if (typeof scope.filters.createdByVidmob === 'boolean') {
      return scope.filters.createdByVidmob;
    }
    return undefined;
  }

  isCreatedByVidmobAdvancedFilterSet(scope: BrandCopilotScope): boolean {
    // Returns true if the 'createdByVidmob' filter is defined (as a boolean)
    return typeof scope.filters.createdByVidmob === 'boolean';
  }

  addCreatedByVidmobAdvancedFilterToCompareReportOptions(
    options: any,
    scope: BrandCopilotScope,
  ): void {
    // Add the createdByVidmob filter value to options.
    options.createdByVidmob = scope.filters.createdByVidmob;
  }

  fetchMetric(platform: string): string[] {
    const normalizedPlatform = platform.toLowerCase();
    if (normalizedPlatform === Platform.FACEBOOK_PAGE) {
      return ['post_engaged_users'];
    } else if (normalizedPlatform === Platform.INSTAGRAM_PAGE) {
      return ['engagement'];
    } else {
      return [];
    }
  }

  getDimensions(platform: string): string[] {
    const normalizedPlatform = platform.toLowerCase();
    switch (normalizedPlatform) {
      case Platform.TWITTER:
        return [
          '$count(tweet)',
          '$count(advideo)',
          '$count(lineitem)',
          '$sum(media_tag[vrs])',
        ];
      case Platform.INSTAGRAM_PAGE:
        return ['$count(post)', '$count(media)', '$sum(media_tag[vrs])'];
      case Platform.FACEBOOK:
        return ['$count(ad)', '$count(advideo)', '$sum(media_tag[vrs])'];
      case Platform.FACEBOOK_PAGE:
        return ['$count(post)', '$count(media)', '$sum(media_tag[vrs])'];
      case Platform.DV360:
        return ['$count(ad)', '$count(advideo)', '$sum(media_tag[vrs])'];
      case Platform.SNAPCHAT:
        return ['$count(ad)', '$count(advideo)', '$sum(media_tag[vrs])'];
      case Platform.PINTEREST:
        return ['$count(ad)', '$count(advideo)', '$sum(media_tag[vrs])'];
      case Platform.LINKEDIN:
        return ['$count(ad)', '$count(advideo)', '$sum(media_tag[vrs])'];
      case Platform.TIKTOK:
        return ['$count(ad)', '$count(advideo)', '$sum(media_tag[vrs])'];
      case Platform.REDDIT:
        return [
          '$count(ad)',
          '$count(advideo)',
          '$count(campaign)',
          '$sum(media_tag[vrs])',
        ];
      case Platform.AMAZON_ADVERTISING_DSP:
        return ['$count(campaign)', '$sum(media_tag[vrs])'];
      default:
        return ['$count(advideo)', '$sum(media_tag[vrs])'];
    }
  }
}

// Helper function to push metric filters
function pushMetric(
  targetArray: string[],
  metric: string,
  operator: 'gte' | 'lte',
  value: number | any,
): void {
  if (value !== undefined && value !== null) {
    targetArray.push(`$metric(${metric}, ${operator}, ${value})`);
  }
}
