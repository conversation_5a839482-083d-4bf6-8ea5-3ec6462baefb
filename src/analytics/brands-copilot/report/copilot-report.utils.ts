import { CopilotReport, ElementsLift } from './copilot-report.model';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { BrandCopilotNextResponse } from '../model/response.model';
import { randomUUID } from 'crypto';
import { InsightAttributes } from './insights.model';
import { KpiInfo } from '../kpi/kpi.model';
import { mapPlatformToDisplayName } from 'src/analytics/analytics-copilot/utils/platform.utils';
import { formatDate } from 'src/analytics/analytics-copilot/utils/date.utils';

export type ElementKpiForLlm =
  | NoInverseElementKpiForLlm
  | InverseElementKpiForLlm;

export interface NoInverseElementKpiForLlm {
  id: string;
  type: string;
  kpi: number;
  kpiLift: number;
  isStatSig: boolean;
}

export interface InverseElementKpiForLlm {
  id: string;
  type: string;
  kpi: number;
  change: number;
  isStatSig: boolean;
}

export interface ReportForLlm {
  kpiAverage: number;
  elements: ElementKpiForLlm[];
}

export function getReportForLlm(report: CopilotReport) {
  return toReportForLlmWithSortElementsByKpiValue(
    report.elementsLift,
    report.isInverseHealthKpi,
    report.kpiAverage,
  );
}

export function toReportForLlmWithSortElementsByKpiValue(
  elementsLift: ElementsLift[],
  isInverseHealthKpi: boolean,
  kpiAverage: number,
): ReportForLlm {
  const elementsForLlm: ElementKpiForLlm[] = elementsLift
    .sort((a, b) =>
      isInverseHealthKpi ? a.kpiValue - b.kpiValue : b.kpiValue - a.kpiValue,
    )
    .map((element) => ({
      id: element.id,
      type: element.type,
      kpi: parseFloat(element.kpiValue.toFixed(2)),
      ...(isInverseHealthKpi
        ? { change: parseFloat(element.kpiLift.toFixed(2)) }
        : { kpiLift: parseFloat(element.kpiLift.toFixed(2)) }),
      isStatSig: element.statisticalSignificance.isStatisticallySignificant,
    }));

  return {
    kpiAverage: parseFloat(kpiAverage.toFixed(2)),
    elements: elementsForLlm,
  };
}

// TODO: Improve no tags found, and instruct the user about what he can do
export function createNoTagsFoundResponse(
  conversation: CopilotConversation,
): BrandCopilotNextResponse {
  return {
    chatId: conversation.id,
    answerPrompt: 'No creative element found for the given filter and question',
    messageId: randomUUID().toString(),
    role: 'VIDMOB',
  };
}

export function createNoPerformanceDataResponse(
  conversation: CopilotConversation,
): BrandCopilotNextResponse {
  return {
    chatId: conversation.id,
    answerPrompt:
      'We couldn’t find performance data for the given question. Please try adjusting the filters or ask a question about other creative elements.',
    messageId: randomUUID().toString(),
    role: 'VIDMOB',
    insights: [],
  };
}

export function createNoInsightsResponse(
  conversation: CopilotConversation,
): BrandCopilotNextResponse {
  return {
    chatId: conversation.id,
    answerPrompt:
      'We couldn’t find insights the given question. Please try adjusting the filters or ask a question about other creative elements.',
    messageId: randomUUID().toString(),
    role: 'VIDMOB',
    insights: [],
  };
}

export function generateUserPromptFirstLineBasedOnFilters(
  attributes: InsightAttributes,
): string {
  const { platform, brands, startDate, endDate, kpiName } = attributes;
  const displayPlatform = mapPlatformToDisplayName(platform);
  const dateRange = `from ${formatDate(startDate)} to ${formatDate(endDate)}`;

  let brandsString = '';
  if (brands.length === 0) {
    brandsString = '';
  } else if (brands.length === 1) {
    brandsString = `for brand ${brands[0]}`;
  } else {
    const firstTwoBrands = brands.slice(0, 2);
    const remainingBrands = brands.length - 2;
    if (remainingBrands > 0) {
      brandsString = `for brands like ${firstTwoBrands.join(
        ', ',
      )} +${remainingBrands} more`;
    } else {
      brandsString = `for brands ${firstTwoBrands.join(' and ')}`;
    }
  }

  return `Based on: KPI: ${kpiName}, ${dateRange}, on ${displayPlatform}${
    brandsString ? ', ' + brandsString : ''
  }.`;
}

// The `kpiOptions.customTagTimeRange` filter determines which portion of the ad video we focus on when searching for tags.
// - If `type` is 'duration', the `value` represents the number of seconds from the start of the video.
// - If `type` is 'percentage', the `value` represents a fraction of the video duration (e.g., 1 for the full video, 0.1 for the first 10%).
// This logic is adapted from the ACS UI project used in element impact analysis.
export function buildCustomTagTimeRange(kpiInfo: KpiInfo): {
  value: number;
  type: string;
} {
  const { tagTimeRange, tagTimeRangeType } = kpiInfo;

  const isTimeRangeTypeDefined =
    tagTimeRangeType !== null && tagTimeRangeType !== undefined;
  const isTimeRangeDefined =
    tagTimeRange !== undefined && tagTimeRange !== null;
  const isTimeConstraintKpiSelected =
    isTimeRangeTypeDefined && (isTimeRangeDefined || tagTimeRange === 0);

  // If the KPI associated with a time range use it, otherwise use first 3 seconds as the default
  if (isTimeConstraintKpiSelected) {
    return { value: tagTimeRange, type: tagTimeRangeType.toLowerCase() };
  } else {
    return { value: 3, type: 'duration' };
  }
}
