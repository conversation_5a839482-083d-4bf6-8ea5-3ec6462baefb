import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { SecretsManagerModule } from '@vidmob/vidmob-nestjs-common';
import { BrandCopilotService } from './brand-copilot.service';
import { BrandCopilotService as BrandCopilotServiceV2 } from './v2/brand-copilot.service';
import { LlmOpenAiClient } from './llm/llm-open-ai-client.service';
import { TagsExtractor } from './tag-identification/tag-extractor.service';
import { PineconeService } from './pinecone/pinecone.service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../../auth/services/auth.service';
import { BrandCopilotPromptClassifier } from './brand-copilot-user-prompt-classifier.service';
import { FlowStrategy } from './flow-type/flow-strategy-manager';
import { CreativeElementsQuestionFlow } from './flow-type/creative-element-question-flow';
import { NonIdentifiedFlow } from './flow-type/non-identified-flow';
import { GeneralInsightsGeneratorService } from './insights/general-insights-generator.service';
import { TagsTypeExtractor } from './tag-identification/tag-type-extractor.service';
import { VectorSearchTagsExtractor } from './tag-identification/vector-search-tag-extractor.service';
import { BrandCopilotController } from './brand-copilot.controller';
import { BrandCopilotController as BrandCopilotControllerV2 } from './v2/brand-copilot.controller';
import { CopilotElasticSearchRepository } from './repository/copilot-elastic-search-repository.service';
import { CopilotMongoDbRepository } from './repository/copilot-mongo-db-repository.service';
import { QuestionTagRelevancyService } from './tag-identification/question-tag-relevancy.service';
import { ConversationSubjectExtractor } from './tag-identification/conversation-subject-extractor';
import { BrandElasticsearchService } from './repository/elasticsearch.service';
import { MongoDbService } from './repository/mongo-db.service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { ScoringAuthService } from '../../scoring/scoring-auth/scoring-auth.service';
import { BrandCopilotAuthorizationService } from './authorization/brand-copilot-authorization.service';
import { BrandCopilotElementImpactReportService } from './report/brand-copilot-element-impact-report.service';
import { CompareReportClient } from './compare-report/compare-report.client';
import { GetMediaByTagsService } from './compare-report/get-media-by-tags.service';
import { CopilotKpiService } from './kpi/copilot-kpi.service';
import { BrandInformationService } from './brand-data/brand-information.service';
import { AccountManagementModule } from '../../account-management/account-management.module';
import { PerformanceQuestionWithoutCreativeElementsFlow } from './flow-type/performance-question-without-creative-elements-flow.service';
import { TagsMetadataService } from './tag-metadata/tag-metadata.service';
import { HumanPresenceInsightsGenerator } from './insights/human-presence-insights-generator.service';
import { SceneryGroupInsightsGenerator } from './insights/scenery-groups-insights-generator.service';
import { DemographicInsightsGenerator } from './insights/demographics-insights-generator.service';
import { BodyPartFocusInsightsGenerator } from './insights/body-part-focus-insights-generator.service';
import { BestCallToActionInsightsGenerator } from './insights/call-to-action-insights-generator.service';
import { LogoInsightsGenerator } from './insights/logos-insights-generator.service';
import { MessagingInsightsGenerator } from './insights/messaging-insights-generator.service';
import { ColorInsightsGenerator } from './insights/color-insights-generator.service';
import { GazeAndEmotionsInsightsGenerator } from './insights/gaze-and-emotions-generator.service';
import { SingleTopicGeneratorsService } from './insights/single-topic-generators.service';
import { SceneryElementInsightsGenerator } from './insights/scenery-elements-insights-generator.service';
import { ObjectInsightsGenerator } from './insights/object-insights-generator.service';
import { BrandService } from '../../account-management/organization/brand/services/brand.service';
import { ConversationCurrentContextSummaryService } from './llm/conversation-summary.service';
import { IndexSchedulerService } from './repository/mongo-db-index-scheduler.service';
import { ScheduleModule } from '@nestjs/schedule';
import { EMBEDDING_SERVICE } from './embeddings/embedding.service';
import { MicrosoftMultilingualE5LargeThroughPineconeEmbeddingService } from './embeddings/microsoft-multilingual-e5-large-through-pinecone-embedding.service';
import { MessagingTypeInsightsGenerator } from './insights/messaging-type-insights-generator.service';
import { CallToActionTypeInsightsGenerator } from './insights/call-to-action-type-insights-generator.service';
import { AdvancedFilterTransformerService } from './report/advanced-filter-transformer.service';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Brand } from '../../account-management/organization/brand/entities/brand.entity';
import { WorkspaceBrandMap } from '../../account-management/workspace/brand/entities/workspace-brand-map.entity';
import { LlmClientRouter } from './llm/llm-client-router.service';
// import { LlmGeminiClient } from './llm/llm-gemini-client.service';
import { LlmVertexAiClient } from './llm/llm-vertex-ai-client.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([Brand, WorkspaceBrandMap]),
    ScheduleModule.forRoot(),
    HttpModule,
    SecretsManagerModule,
    AccountManagementModule,
  ],
  providers: [
    BrandCopilotService,
    BrandCopilotServiceV2,
    BrandCopilotPromptClassifier,
    LlmOpenAiClient,
    FlowStrategy,
    CreativeElementsQuestionFlow,
    NonIdentifiedFlow,
    PerformanceQuestionWithoutCreativeElementsFlow,
    TagsExtractor,
    GeneralInsightsGeneratorService,
    TagsTypeExtractor,
    PineconeService,
    VectorSearchTagsExtractor,
    WorkspaceService,
    ConfigService,
    AuthService,
    CopilotElasticSearchRepository,
    CopilotMongoDbRepository,
    QuestionTagRelevancyService,
    ConversationSubjectExtractor,
    BrandElasticsearchService,
    MongoDbService,
    OrganizationUserService,
    ScoringAuthService,
    AnalyticsUserService,
    BrandCopilotAuthorizationService,
    BrandCopilotElementImpactReportService,
    CompareReportClient,
    GetMediaByTagsService,
    CopilotKpiService,
    BrandInformationService,
    TagsMetadataService,
    DemographicInsightsGenerator,
    BodyPartFocusInsightsGenerator,
    BestCallToActionInsightsGenerator,
    CallToActionTypeInsightsGenerator,
    HumanPresenceInsightsGenerator,
    SceneryGroupInsightsGenerator,
    LogoInsightsGenerator,
    MessagingInsightsGenerator,
    MessagingTypeInsightsGenerator,
    ColorInsightsGenerator,
    GazeAndEmotionsInsightsGenerator,
    SingleTopicGeneratorsService,
    SceneryElementInsightsGenerator,
    ObjectInsightsGenerator,
    BrandService,
    ConversationCurrentContextSummaryService,
    IndexSchedulerService,
    AdvancedFilterTransformerService,
    LlmClientRouter,
    // LlmGeminiClient,
    LlmVertexAiClient,
    {
      provide: EMBEDDING_SERVICE,
      useClass: MicrosoftMultilingualE5LargeThroughPineconeEmbeddingService,
    },
  ],
  controllers: [BrandCopilotController, BrandCopilotControllerV2],
})
export class BrandsCopilotModule {}
