import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import fetch from 'node-fetch';
import { EmbeddingService } from './embedding.service';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';

@Injectable()
export class MicrosoftMultilingualE5LargeThroughPineconeEmbeddingService
  implements OnModuleInit, EmbeddingService
{
  private readonly logger = new Logger(
    MicrosoftMultilingualE5LargeThroughPineconeEmbeddingService.name,
  );
  private apiKey: string;
  private apiUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly secretsConfigurationService: SecretsConfigurationService,
  ) {}

  async onModuleInit() {
    this.apiKey = 'b3c5c1e9-2f9d-41e1-b24b-e1319095df07';
    // const pineconeConfig = await this.secretsConfigurationService.get<any>('pinecone');
    // this.apiKey = pineconeConfig?.api_key;
    if (!this.apiKey) {
      throw new Error('Pinecone API key not found in configuration');
    }
    this.apiUrl =
      this.configService.get<string>('pinecone.embedApiUrl') ||
      'https://api.pinecone.io/embed';
  }

  async getEmbeddings(text: string): Promise<number[]> {
    const requestBody = {
      model: 'multilingual-e5-large',
      parameters: {
        input_type: 'query',
        truncate: 'END',
      },
      inputs: [
        {
          text: text,
        },
      ],
    };

    try {
      const response = await fetch(this.apiUrl, {
        method: 'POST',
        headers: {
          'Api-Key': this.apiKey,
          'Content-Type': 'application/json',
          'X-Pinecone-API-Version': '2024-10',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        throw new Error(
          `Error: Unable to get embedding. HTTP response code: ${response.status}`,
        );
      }

      const jsonResponse = await response.json();
      return jsonResponse.data[0].values;
    } catch (error) {
      this.logger.error(
        'Error when trying to get vector search embeddings:',
        error,
      );
      throw new Error('Error when trying to get vector search embeddings');
    }
  }
}
