import { google, slides_v1 } from 'googleapis';
import { OAuth2Client } from 'google-auth-library';
import * as fs from 'fs';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { Insight, InsightElement } from './slides.types';
import {
  deletePresentationFromDrive,
  downloadPresentation,
} from './google-slides';
import {
  addReplaceTextRequest,
  deleteExtraElements,
  deleteTemplateSlides,
  duplicateSlide,
  findImageByAltText,
  findShapeByAltText,
  getImageObjectId,
  hexToRgb,
  populateInsightText,
} from './slides.utils';
import { blueCircleAltText } from './slides.constants';

type Slide = slides_v1.Schema$Page;
type Request = slides_v1.Schema$Request;

const replaceBlueCircleWithPlatformIcon = async (
  requests: Request[],
  slides: Slide[],
  platformIcon: string | null,
): Promise<void> => {
  slides.forEach((slide) => {
    const blueCircleObjectId = getImageObjectId(slide, blueCircleAltText);
    if (blueCircleObjectId && platformIcon) {
      requests.push({
        replaceImage: {
          imageObjectId: blueCircleObjectId,
          url: platformIcon,
        },
      });
    }
  });
};

const handleIndexedSlides = (
  requests: Request[],
  insights: Insight[],
  slide: Slide,
  indexedPlaceholderSlideIds: string[],
): void => {
  if (slide.objectId && indexedPlaceholderSlideIds.includes(slide.objectId)) {
    populateIndexedSlides(requests, insights, slide);
  } else {
    duplicateAndPopulateSlides(requests, slide?.objectId || '', insights);
  }
};

const handleTemplateSlideDeletion = (
  requests: Request[],
  templateSlideIds: string[],
): void => {
  if (templateSlideIds) {
    deleteTemplateSlides(requests, templateSlideIds);
  }
};

// Main function to populate the Google Slides presentation
export const populatePresentation = async (
  auth: OAuth2Client,
  presentationId: string,
  insights: Insight[],
  platform: Platform,
): Promise<void> => {
  try {
    const slidesService = google.slides({ version: 'v1', auth });

    const requests: Request[] = [];

    // Detect which slides have indexed placeholders
    const { indexedPlaceholderSlideIds, templateSlideIds, slides } =
      await detectIndexedPlaceholders(auth, presentationId);

    if (!slides) {
      throw new Error('Slides not found in the presentation');
    }

    const platformEnumValue =
      Platform[platform.toUpperCase() as keyof typeof Platform] ||
      Platform.FACEBOOK;
    const platformIcon = findImageByAltText(slides, platformEnumValue);

    await replaceBlueCircleWithPlatformIcon(requests, slides, platformIcon);

    slides.forEach((slide) => {
      handleIndexedSlides(
        requests,
        insights,
        slide,
        indexedPlaceholderSlideIds,
      );
    });

    handleTemplateSlideDeletion(requests, templateSlideIds);

    await slidesService.presentations.batchUpdate({
      presentationId,
      requestBody: {
        requests,
      },
    });
  } catch (error) {
    throw error;
  }
};

export const detectIndexedPlaceholders = async (
  auth: OAuth2Client,
  presentationId: string,
): Promise<{
  indexedPlaceholderSlideIds: string[];
  templateSlideIds: string[];
  slides: Slide[];
}> => {
  const slidesService = google.slides({ version: 'v1', auth });

  const presentation = await slidesService.presentations.get({
    presentationId,
  });

  if (!presentation?.data?.slides) {
    throw new Error('No slides found in the presentation');
  }

  const indexedPlaceholderSlideIds = new Set<string>();

  const indexedPlaceholderRegex = /\{\{insight\d+\}\}/;

  presentation.data.slides.forEach((slide) => {
    const slideId = slide.objectId;
    slide.pageElements?.forEach((element) => {
      if (element.shape?.text) {
        const textContent = element.shape.text.textElements
          ?.map((te) => te.textRun?.content || '')
          .join('');
        if (
          textContent &&
          indexedPlaceholderRegex.test(textContent) &&
          slideId
        ) {
          indexedPlaceholderSlideIds.add(slideId);
        }
      }
    });
  });

  const templateSlideIds = presentation.data.slides
    .filter((slide): slide is Slide => slide.objectId != null)
    .filter((slide) => slide.objectId && !indexedPlaceholderSlideIds.has(slide.objectId!))
    .map((slide) => slide.objectId!);

  return {
    indexedPlaceholderSlideIds: Array.from(indexedPlaceholderSlideIds),
    templateSlideIds,
    slides: presentation.data.slides,
  };
};

// Function to handle element replacement and shape color updates
const handleElementUpdates = (
  requests: Request[],
  slide: Slide,
  insightNum: number,
  element: InsightElement,
  elementIndex: number,
) => {
  const elementNum = elementIndex + 1;
  const indexedPlaceholder = `${insightNum}${elementNum}`;
  const indexedElementPlaceholder = `{{element${indexedPlaceholder}}}`;
  const indexedKpiPlaceholder = `{{kpi${indexedPlaceholder}}}`;

  addReplaceTextRequest(requests, indexedElementPlaceholder, element.element);
  addReplaceTextRequest(requests, indexedKpiPlaceholder, element.kpi_lift);

  // Update the background color of the shape based on the element's tagColor
  const rgbColor = hexToRgb(element.tagColor);
  const objectId = findShapeByAltText([slide], `element${indexedPlaceholder}`);
  if (rgbColor && objectId) {
    requests.push({
      updateShapeProperties: {
        objectId: objectId,
        shapeProperties: {
          shapeBackgroundFill: {
            solidFill: { color: { rgbColor: rgbColor } },
          },
        },
        fields: 'shapeBackgroundFill.solidFill.color',
      },
    });
  }
};

// Main function to populate indexed slides
export function populateIndexedSlides(
  requests: Request[],
  insights: Insight[],
  slide: Slide,
) {
  const insight = insights[0];
  addReplaceTextRequest(
    requests,
    `{{kpiAvg}}`,
    insight.overallKPIAvg?.toFixed(2) || '',
  );
  addReplaceTextRequest(
    requests,
    `{{creatives}}`,
    insight.numCreatives?.toString() || '',
  );
  addReplaceTextRequest(
    requests,
    `{{impressions}}`,
    insight.impressions?.toString() || '',
  );
  addReplaceTextRequest(requests, `{{kpiName}}`, insight.kpiName || '');

  insights.forEach((insight, insightIndex) => {
    const insightNum = insightIndex + 1;

    populateInsightText(requests, insight, insightNum);

    insight.elements.forEach((element, elementIndex) => {
      handleElementUpdates(requests, slide, insightNum, element, elementIndex);
    });

    deleteExtraElements(requests, slide, insightNum, insight.elements.length);
  });
}

// Function to populate the slide with insights
export function duplicateAndPopulateSlides(
  requests: Request[],
  templateSlideId: string,
  insights: Insight[],
) {
  insights.forEach((insight, index) => {
    const newSlideId = `slide_${index}_${templateSlideId}`;

    duplicateSlide(requests, templateSlideId, newSlideId);

    addReplaceTextRequest(requests, '{{title}}', insight.title, newSlideId);
    addReplaceTextRequest(requests, '{{insight}}', insight.finding, newSlideId);
    addReplaceTextRequest(
      requests,
      '{{recommendation}}',
      insight.recommendation,
      newSlideId,
    );

    if (insight.elements.length > 0) {
      const element = insight.elements[0];
      addReplaceTextRequest(
        requests,
        '{{element}}',
        element.element,
        newSlideId,
      );
      addReplaceTextRequest(requests, '{{kpi}}', element.kpi_lift, newSlideId);
    }
  });
}

export async function downloadAndDeletePresentation(
  auth: OAuth2Client,
  presentationId: string,
): Promise<fs.PathLike> {
  try {
    const filePath = await downloadPresentation(auth, presentationId);

    if (!filePath) {
      throw new Error('Download failed: File path is null or undefined.');
    }
    await deletePresentationFromDrive(auth, presentationId);

    return filePath;
  } catch (error) {
    throw error;
  }
}
