import { Platform } from '@vidmob/vidmob-nestjs-common';

export interface InsightElement {
  element: string;
  element_avg_kpi: number;
  kpi_lift: string;
  tag_type: string;
  element_kpi_value: number;
  element_impressions: number;
  element_creatives: number;
  element_is_significant: boolean;
  tagColor: string;
}

export interface Insight {
  title: string;
  recommendation: string;
  finding: string;
  elements: InsightElement[];
  numCreatives: number;
  impressions: string;
  overallKPIAvg: number;
  kpiName: string;
}

export interface PlatformInsights {
  platform: Platform;
  insights: Insight[];
}
