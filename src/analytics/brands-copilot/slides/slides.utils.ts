import { slides_v1 } from 'googleapis';
import { Insight } from './slides.types';

type Slide = slides_v1.Schema$Page;
type Shape = slides_v1.Schema$PageElement;
type Request = slides_v1.Schema$Request;

export const findElementByAltText = (
  slides: Slide[],
  altText: string,
  elementType: 'image' | 'shape',
) => {
  for (const slide of slides) {
    const matchingElement = slide.pageElements?.find(
      (element: Shape) =>
        ((elementType === 'image' && element.image) ||
          (elementType === 'shape' && element.shape)) &&
        (element.title === altText || element.description === altText),
    );
    if (matchingElement) {
      return elementType === 'image'
        ? matchingElement.image?.contentUrl || null
        : matchingElement.objectId || null;
    }
  }
  return null;
};

export const findImageByAltText = (slides: Slide[], altText: string) => {
  return findElementByAltText(slides, altText, 'image');
};

export const findShapeByAltText = (slides: Slide[], altText: string) => {
  return findElementByAltText(slides, altText, 'shape');
};

export const addReplaceTextRequest = (
  requests: Request[],
  placeholder: string,
  replacement: string,
  newSlideId?: string,
) => {
  requests.push({
    replaceAllText: {
      containsText: { text: placeholder, matchCase: true },
      replaceText: replacement,
      pageObjectIds: newSlideId ? [newSlideId] : undefined,
    },
  });
};

export const hexToRgb = (
  hex?: string,
): { red: number; green: number; blue: number } | null => {
  if (!hex) return null;
  const match = hex.match(/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i);
  if (!match) return null;
  const [, r, g, b] = match;
  return {
    red: parseInt(r, 16) / 255,
    green: parseInt(g, 16) / 255,
    blue: parseInt(b, 16) / 255,
  };
};

export const duplicateSlide = (
  requests: Request[],
  templateSlideId: string,
  newSlideId: string,
) => {
  requests.push({
    duplicateObject: {
      objectId: templateSlideId,
      objectIds: {
        [templateSlideId]: newSlideId,
      },
    },
  });
};

export const deleteTemplateSlides = (
  requests: Request[],
  templateSlideIds: string[],
) => {
  templateSlideIds.forEach((templateSlideId) => {
    requests.push({
      deleteObject: {
        objectId: templateSlideId,
      },
    });
  });
};

export const populateInsightText = (
  requests: Request[],
  insight: Insight,
  insightNum: number,
) => {
  addReplaceTextRequest(requests, `{{title${insightNum}}}`, insight.title);
  addReplaceTextRequest(requests, `{{insight${insightNum}}}`, insight.finding);
  addReplaceTextRequest(
    requests,
    `{{recommendation${insightNum}}}`,
    insight.recommendation,
  );
};

export const deleteExtraElements = (
  requests: Request[],
  slide: Slide,
  insightNum: number,
  elementCount: number,
) => {
  for (let i = elementCount + 1; i <= 6; i++) {
    const altText = `element${insightNum}${i}`;
    const shapeObjectId = findShapeByAltText([slide], altText);
    if (shapeObjectId) {
      requests.push({
        deleteObject: { objectId: shapeObjectId },
      });
    }
  }
};

export const getImageObjectId = (
  slide: Slide,
  altTextToFind: string,
): string | null => {
  for (const element of slide.pageElements || []) {
    if (element.image) {
      const altText = element.title || element.description || '';
      if (altText.includes(altTextToFind)) {
        return element.objectId || null;
      }
    }
  }
  return null;
};
