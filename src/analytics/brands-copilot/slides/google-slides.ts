import { google } from 'googleapis';
import { CredentialBody, OAuth2Client } from 'google-auth-library';
import * as fs from 'fs';
import * as path from 'path';
import { SCOPES, templateID } from './slides.constants';

export async function authenticate(credentials?: CredentialBody): Promise<any> {
  if (!credentials) {
    throw new Error('No credentials provided');
  }
  const auth = new google.auth.GoogleAuth({
    credentials,
    scopes: SCOPES,
  });
  const authClient = (await auth.getClient()) as OAuth2Client;
  return authClient;
}

export async function copyTemplate(auth: OAuth2Client): Promise<string> {
  const drive = google.drive({ version: 'v3', auth });

  const response = await drive.files.copy({
    fileId: templateID,
    requestBody: {
      name: 'New Presentation from Template',
    },
  });
  return response.data.id || '';
}

export async function downloadPresentation(
  auth: OAuth2Client,
  presentationId: string,
): Promise<fs.PathLike> {
  const drive = google.drive({ version: 'v3', auth });

  const response = await drive.files.export(
    {
      fileId: presentationId,
      mimeType:
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    },
    { responseType: 'stream' },
  );

  const filePath = path.join(__dirname, 'downloaded-presentation.pptx');
  const dest = fs.createWriteStream(filePath);

  return new Promise((resolve, reject) => {
    // Start piping the stream
    response.data.pipe(dest);

    // Catch any errors in the stream
    response.data.on('error', (err) => {
      reject(err);
    });

    // Ensure the stream finishes properly
    dest.on('finish', () => {
      fs.stat(filePath, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve(filePath);
        }
      });
    });

    dest.on('error', (err) => {
      reject(err);
    });
  });
}

export const deletePresentationFromDrive = async (
  auth: OAuth2Client,
  presentationId: string,
) => {
  const drive = google.drive({ version: 'v3', auth });
  try {
    await drive.files.delete({ fileId: presentationId });
  } catch (error) {
    console.error(`Failed to delete file from Google Drive: ${error}`);
  }
};
