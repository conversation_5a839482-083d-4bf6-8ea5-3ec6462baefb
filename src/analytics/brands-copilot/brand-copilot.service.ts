import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { BrandCopilotNextRequest } from './model/request.model';
import { BrandCopilotNextResponse } from './model/response.model';
import {
  Context,
  CopilotConversation,
  CopilotConversationMessage,
  CopilotConversationMessageRequest,
  CopilotConversationMessageResponse,
} from './model/copilot-conversation.model';
import { randomUUID, UUID } from 'crypto';
import { BrandCopilotPromptClassifier } from './brand-copilot-user-prompt-classifier.service';
import { FlowStrategy } from './flow-type/flow-strategy-manager';
import { CopilotElasticSearchRepository } from './repository/copilot-elastic-search-repository.service';
import { CopilotRole } from './model/llm-constants.model';
import { AnalyticsCopilotConversationListItem } from '../analytics-copilot/dto/analytics-copilot-conversation.dto';
import { UpdateMessageFeedbackDto } from '../analytics-copilot/dto/update-message-feedback.dto';
import {
  downloadAndDeletePresentation,
  populatePresentation,
} from './slides/slides';
import {
  Platform,
  SecretsConfigurationService,
} from '@vidmob/vidmob-nestjs-common';
import { CredentialBody } from 'google-auth-library';
import { Insight } from './slides/slides.types';
import * as fs from 'fs';
import { authenticate, copyTemplate } from './slides/google-slides';
import { LLMModel } from './model/llm-model.enum';
import { LlmClientRouter } from './llm/llm-client-router.service';

@Injectable()
export class BrandCopilotService {
  private readonly logger = new Logger(BrandCopilotService.name);

  constructor(
    private readonly promptClassifier: BrandCopilotPromptClassifier,
    private readonly flowStrategy: FlowStrategy,
    private readonly copilotRepository: CopilotElasticSearchRepository,
    private readonly secretsConfigService: SecretsConfigurationService,
    private readonly llmProviderService: LlmClientRouter,
  ) {}

  async getNextResponse(
    context: Context,
    request: BrandCopilotNextRequest,
    authToken: string,
  ): Promise<BrandCopilotNextResponse> {
    try {
      const conversation = await this.getConversation(context, request);
      // Save early, so even if response fail, we will have partial model in repository
      await this.copilotRepository.save(conversation);

      const selectedLlmModel =
        request.model ?? this.llmProviderService.getDefaultModel();

      const response = await this.executeSuitableFlow(
        context,
        conversation,
        request,
        authToken,
        selectedLlmModel,
      );

      response.model = selectedLlmModel;
      this.addMessage(conversation, this.createMessageResponse(response));

      await this.copilotRepository.save(conversation);
      return response;
    } catch (e) {
      this.logError(e, request);
      return this.createTryAgainResponse();
    }
  }

  async getConversationList(
    organizationId: string,
    userId: number,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    return this.copilotRepository.getList(organizationId, userId);
  }

  async getConversationMessages(
    userId: number,
    chatId: UUID,
  ): Promise<CopilotConversationMessage[]> {
    const conversation = await this.getFromDb(userId, chatId);
    return conversation.messages;
  }

  async updateConversationFeedback(
    userId: number,
    chatId: UUID,
    messageId: UUID,
    requestDto: UpdateMessageFeedbackDto,
  ): Promise<boolean> {
    const conversation = await this.getFromDb(userId, chatId);
    const message: CopilotConversationMessage = this.getResponseMessage(
      conversation,
      messageId,
    );
    const responseMessage: CopilotConversationMessageResponse =
      this.getAsResponseMessage(message);
    responseMessage.feedback = {
      score: requestDto.feedback,
      reason: requestDto.reasonType,
      comments: requestDto.additionalComments,
    };
    await this.copilotRepository.save(conversation);
    return true;
  }

  private getResponseMessage(
    conversation: CopilotConversation,
    messageId: UUID,
  ): CopilotConversationMessage {
    if (conversation.messages.length === 0) {
      throw new Error(
        `Message with id ${messageId} not found in conversation: ${conversation.id}`,
      );
    }

    const message = conversation.messages.find((msg) => msg.id === messageId);

    if (!message) {
      throw new Error(
        `Message with id ${messageId} not found in conversation: ${conversation.id}`,
      );
    }

    return message;
  }

  private getAsResponseMessage(
    message: CopilotConversationMessage,
  ): CopilotConversationMessageResponse {
    if (message.role !== CopilotRole.ASSISTANT) {
      throw new Error(
        `Message with id ${message.id} is not a response message.`,
      );
    }

    return message as CopilotConversationMessageResponse;
  }

  private logError(e: Error, request: BrandCopilotNextRequest) {
    if (e instanceof NotFoundException) {
      this.logger.warn(`Conversation not found for chatId: ${request.chatId}`);
    } else {
      this.logger.error(
        `Failed to generate LLM response for message: ${request.userInput} with chat id: ${request.chatId}. Error: ${e.message}`,
      );
    }
  }

  private async executeSuitableFlow(
    context: Context,
    conversation: CopilotConversation,
    nextRequest: BrandCopilotNextRequest,
    authToken: string,
    llmModel: LLMModel,
  ): Promise<BrandCopilotNextResponse> {
    const flowType = await this.promptClassifier.classify(
      conversation,
      llmModel,
    );
    const flowHandler = this.flowStrategy.getFlowHandler(flowType);
    return flowHandler.executeFlow(
      context,
      conversation,
      nextRequest.scope,
      authToken,
      llmModel,
    );
  }

  private async getConversation(
    context: Context,
    nextRequest: BrandCopilotNextRequest,
  ): Promise<CopilotConversation> {
    const now = Date.now();
    let conversation: CopilotConversation;
    if (nextRequest.chatId) {
      conversation = await this.getFromDb(context.user.id, nextRequest.chatId);
    } else {
      conversation = this.createNewCopilotConversation(context, now);
    }

    const message = this.createMessageRequest(now, nextRequest);
    this.addMessage(conversation, message);

    return conversation;
  }

  private addMessage(
    conversation: CopilotConversation,
    message:
      | CopilotConversationMessageRequest
      | CopilotConversationMessageResponse,
  ) {
    conversation.messages.push(message);
    conversation.lastInteractionDate = message.timestamp;
    if (conversation.messages.length === 1) {
      conversation.title = message.text;
    }
  }

  private createMessageRequest(
    timestamp: number,
    nextRequest: BrandCopilotNextRequest,
  ): CopilotConversationMessageRequest {
    return {
      id: randomUUID(),
      timestamp: timestamp,
      role: CopilotRole.USER,
      text: nextRequest.userInput,
      filters: nextRequest.scope,
    } as CopilotConversationMessageRequest;
  }

  // FIXME: Add report
  private createMessageResponse(
    response: BrandCopilotNextResponse,
  ): CopilotConversationMessageResponse {
    return {
      id: response.messageId,
      timestamp: Date.now(),
      text: response.answerPrompt,
      role: CopilotRole.ASSISTANT,
      insights: response.insights,
      model: response.model,
    } as CopilotConversationMessageResponse;
  }

  private createTryAgainResponse(): BrandCopilotNextResponse {
    return {
      chatId: randomUUID(),
      messageId: randomUUID(),
      role: 'VIDMOB',
      answerPrompt: `Oops! Something went wrong while processing your request. Please try again in a few moments. We’re working on it!`,
    } as BrandCopilotNextResponse;
  }

  private async getFromDb(
    userId: number,
    chatId: UUID,
  ): Promise<CopilotConversation> {
    const dbConversationModel = await this.copilotRepository.get(
      userId,
      chatId,
    );
    if (!dbConversationModel) {
      throw new NotFoundException(`chat id: ${chatId} not found`);
    }
    return dbConversationModel;
  }

  private createNewCopilotConversation(
    context: Context,
    timestamp: number,
  ): CopilotConversation {
    return {
      id: randomUUID(),
      userId: context.user.id,
      organizationId: context.organization.id,
      messages: [],
      timestamp,
      lastInteractionDate: timestamp,
      userContext: undefined,
      title: '',
    } as CopilotConversation;
  }

  async deleteConversation(userId: number, chatId: UUID): Promise<boolean> {
    const exists = await this.copilotRepository.existsByUserIdAndChatId(
      userId,
      chatId,
    );
    if (!exists) {
      this.logger.warn(
        `Conversation with id ${chatId} not found for user ${userId}`,
      );
      return false;
    } else {
      try {
        await this.copilotRepository.delete(chatId);
        this.logger.log(`Deleted conversation: ${chatId}`);
        return true;
      } catch (e) {
        this.logger.error(
          `Failed to delete conversation with id: ${chatId}`,
          e,
        );
        throw new Error(
          `Error while deleting conversation ${chatId}: ${e.message}`,
        );
      }
    }
  }

  async renameConversation(
    userId: number,
    chatId: UUID,
    newTitle: string,
  ): Promise<CopilotConversation> {
    const conversation = await this.getFromDb(userId, chatId);

    conversation.title = newTitle;

    await this.copilotRepository.save(conversation);
    return conversation;
  }

  async createPresentation(
    insights: Insight[],
    platform: Platform,
  ): Promise<fs.PathLike> {
    try {
      // Step 1: Fetch credentials
      const credentials = await this.secretsConfigService.get<CredentialBody>(
        'google-credentials',
      );
      const auth = await authenticate(credentials);

      // Step 2: Copy template
      const presentationId = await copyTemplate(auth);
      if (!presentationId) {
        throw new Error('Failed to copy template');
      }

      // Step 3: Populate the presentation
      await populatePresentation(auth, presentationId, insights, platform);

      // Step 4: Download and delete the presentation
      const filePath = await downloadAndDeletePresentation(
        auth,
        presentationId,
      );
      return filePath;
    } catch (error) {
      this.logger.error('Error creating presentation:', error);
      throw error;
    }
  }
}
