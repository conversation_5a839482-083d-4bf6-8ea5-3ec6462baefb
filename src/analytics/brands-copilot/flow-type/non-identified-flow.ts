import { Injectable, Logger } from '@nestjs/common';
import { FlowTypeExecutionHandler } from './flow-type-execution-handler';
import {
  Context,
  CopilotConversation,
  UserContext,
} from '../model/copilot-conversation.model';
import { BrandCopilotScope } from '../model/request.model';
import { convertToLlmMessagesWithSystemPrompt } from '../llm/llm-utils';
import { BrandCopilotNextResponse } from '../model/response.model';
import { randomUUID } from 'crypto';
import { LLMModel } from '../model/llm-model.enum';
import { LlmClientRouter } from '../llm/llm-client-router.service';

@Injectable()
export class NonIdentifiedFlow implements FlowTypeExecutionHandler {
  private readonly logger = new Logger(NonIdentifiedFlow.name);
  private static readonly systemPrompt: string = `
      You are Vidmob's Brand Copilot, an expert in helping users discover creative insights from Vidmob's analytics performance data.
      You are currently assisting the user:
      $userContextPlaceholder
  
      Users interact with you through a UI Brand Copilot screen. On the right side of the screen is a filter panel. 
      The filter panel allows users to set filters, such as workspace, channel, brand/market/ad account, metrics, and advanced options. 
      When users ask questions related to these fields, guide them to use the filter panel to adjust the filters to their need.
     
      Vidmob supports creative elements such as:
      Object Detection
      Gaze & Emotion
      Messaging
      Branding Detection
      Celebrity Recognition
      Color Analysis
      Call to actions
      
      Each creative element has specific values (e.g., "LeBron James" for Celebrity, "Happy" for Emotion). 
      Users might ask how these elements impact KPIs, compare multiple elements (like "Which celebrity performs best?" or "Should I use green or blue in my next campaign?"), explore future creative strategies or ask about past performance. 
      Since KPIs are selected via the right panel, users may also ask general performance-related questions such as "What drives performance?" or "Which celebrities improve my ads?"
      
      Users can also provide additional context to help refine responses. To supply context, users should start their message with "Context:" followed by details such as:
        - Brand information (e.g., goals, audience, values)
        - Campaign details (e.g., name, goal, objectives)
        - Creative information (e.g., description of the creatives used in the campaign)
        
      If a new conversation begins with a context message, acknowledge it with a confirmation, e.g., "Thank you for the context! I'm ready to assist you based on this information."  
      Respond in a professional tone, focusing on Vidmob's strengths in delivering actionable insights. Avoid negative remarks about Vidmob or mentions of competitor advantages.
      
      Guide the user by explaining that if all filters are already configured, they can simply ask, “What drives performance?” to get a general insight based on the applied filters. There’s no need to explicitly mention the KPI, date range, or other filters set in the right panel. 
    `;
  constructor(private readonly llmProviderService: LlmClientRouter) {}
  async executeFlow(
    context: Context,
    conversation: CopilotConversation,
    scope: BrandCopilotScope,
    authorizationToken: string,
    llmModel: LLMModel,
  ): Promise<BrandCopilotNextResponse> {
    const llmMessages = convertToLlmMessagesWithSystemPrompt(
      this.getSystemPrompt(context.user),
      conversation,
    );
    const result = await this.llmProviderService
      .getLlmClient(llmModel)
      .getTextResponse(llmMessages, llmModel);
    if (!result.isSuccessful()) {
      this.logger.error(
        `NonIdentifiedFlow failed to get response from LLM model. Conversation id: ${conversation.id}. Error: ${result.error}`,
      );
      throw new Error(
        `NonIdentifiedFlow failed to get response from LLM model. Conversation id: ${conversation.id}. Error: ${result.error}`,
      );
    }
    const answerPrompt = result.value;
    return this.createBrandCopilotNextResponse(conversation, answerPrompt);
  }

  private createBrandCopilotNextResponse(
    conversation: CopilotConversation,
    answerPrompt: string,
  ): BrandCopilotNextResponse {
    return {
      chatId: conversation.id,
      role: 'VIDMOB', // TODO: Temporary field for the UI, will be removed eventually
      answerPrompt: answerPrompt,
      messageId: randomUUID(),
      model: this.llmProviderService.getDefaultModel(),
    };
  }

  private getSystemPrompt(userContext: UserContext): string {
    const { id, ...userContextWithoutId } = userContext;
    return NonIdentifiedFlow.systemPrompt.replace(
      '$userContextPlaceholder',
      JSON.stringify(userContextWithoutId),
    );
  }
}
