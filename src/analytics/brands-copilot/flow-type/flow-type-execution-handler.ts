import {
  Context,
  CopilotConversation,
} from '../model/copilot-conversation.model';
import { BrandCopilotNextResponse } from '../model/response.model';
import { BrandCopilotScope } from '../model/request.model';
import { LLMModel } from '../model/llm-model.enum';

export interface FlowTypeExecutionHandler {
  executeFlow(
    context: Context,
    conversation: CopilotConversation,
    scope: BrandCopilotScope,
    authorizationToken: string,
    llmModel: LLMModel,
  ): Promise<BrandCopilotNextResponse>;
}
