import { BrandCopilotNextResponse } from '../model/response.model';
import { BrandCopilotScope } from '../model/request.model';
import { Injectable } from '@nestjs/common';
import { FlowTypeExecutionHandler } from './flow-type-execution-handler';
import {
  Context,
  CopilotConversation,
} from '../model/copilot-conversation.model';
import {
  Insight,
  InsightAttributes,
  InsightWithCreativesKeys,
} from '../report/insights.model';
import { randomUUID } from 'crypto';
import { BrandCopilotElementImpactReportService } from '../report/brand-copilot-element-impact-report.service';
import { GetMediaByTagsService } from '../compare-report/get-media-by-tags.service';
import { BrandInformationService } from '../brand-data/brand-information.service';
import { CreativeKey } from '../brand-copilot-next.dto';
import { SingleTopicGeneratorsService } from '../insights/single-topic-generators.service';
import {
  createNoPerformanceDataResponse,
  generateUserPromptFirstLineBasedOnFilters,
} from '../report/copilot-report.utils';
import {
  filterOutNotStatisticallySignificantElements,
  InsightGeneratorSource,
} from '../insights/insight-utils.utils';
import { ConversationCurrentContextSummaryService } from '../llm/conversation-summary.service';
import { LLMModel } from '../model/llm-model.enum';

@Injectable()
export class ModifyInsightsFlow implements FlowTypeExecutionHandler {
  constructor(
    private readonly elementImpactReportService: BrandCopilotElementImpactReportService,
    private readonly getMediaByTagsService: GetMediaByTagsService,
    private readonly brandInformationService: BrandInformationService,
    private readonly singleTopicGeneratorsService: SingleTopicGeneratorsService,
    private readonly contextSummaryService: ConversationCurrentContextSummaryService,
  ) {}

  async executeFlow(
    context: Context,
    conversation: CopilotConversation,
    scope: BrandCopilotScope,
    authorizationToken: string,
    llmModel: LLMModel,
  ): Promise<BrandCopilotNextResponse> {
    const report = await this.elementImpactReportService.getReportForAllTags(
      scope,
      authorizationToken,
    );

    if (report.elementsLift.length === 0) {
      return createNoPerformanceDataResponse(conversation);
    }

    report.elementsLift = filterOutNotStatisticallySignificantElements(
      report.elementsLift,
    );

    const brands = await this.brandInformationService.getByAdAccountId(
      conversation.organizationId,
      scope.workspaces.map((w) => w.id),
      scope.adAccounts.map((adAccount) => adAccount.platformAccountId),
    );
    const brandNames = brands.map((brand) => brand.name);
    const insightAttributes: InsightAttributes = {
      platform: scope.platform,
      brands: brandNames,
      startDate: scope.startDate,
      endDate: scope.endDate,
      kpiName: report.kpiName,
      kpiFormat: report.kpiFormat,
      isInverseHealthKpi: report.isInverseHealthKpi,
    };

    const insightGenerators =
      this.singleTopicGeneratorsService.getAllInsightsGenerators();

    const conversationContextSummary =
      await this.contextSummaryService.getCurrentContextSummary(
        conversation,
        llmModel,
      );

    const insightPromisesArray = insightGenerators.map((insightGenerator) =>
      insightGenerator.generateInsights(
        conversation.id,
        conversationContextSummary,
        report,
        brands,
        llmModel,
      ),
    );

    const resolvedInsightPromises = await Promise.all(insightPromisesArray);
    const responseInsights = resolvedInsightPromises.flatMap(
      (resolved) => resolved,
    );

    if (responseInsights.length === 0) {
      return createNoPerformanceDataResponse(conversation);
    }

    const sortedResponseInsights =
      this.sortInsightsByMaxKpiLift(responseInsights);

    const reorderedInsightsBySource = this.reorderInsights(
      sortedResponseInsights,
    );

    const responseInsightsWithCreatives =
      await this.getInsightsWithCreativesKeys(
        reorderedInsightsBySource,
        scope,
        authorizationToken,
        insightAttributes,
      );

    const userPrompt =
      generateUserPromptFirstLineBasedOnFilters(insightAttributes) +
      ' I found the following insights:';

    return {
      chatId: conversation.id,
      answerPrompt: userPrompt,
      messageId: randomUUID().toString(),
      role: 'VIDMOB',
      model: llmModel,
      insights: responseInsightsWithCreatives,
    };
  }

  // TODO: Currently, the creative retrieval process is inefficient.
  //       Our immediate focus is to ensure 100% functionality.
  //       The Plan is to: Optimize the code to use a single dedicated query for
  //       Copilot, instead of multiple calls. Specifically:
  //       1. Retrieve report KPI.
  //       2. For each insight, get creatives by tag and then retrieve the creative.
  private async getInsightsWithCreativesKeys(
    inputInsights: Insight[],
    scope: BrandCopilotScope,
    authorizationToken: string,
    insightAttributes: InsightAttributes,
  ): Promise<Insight[]> {
    if (inputInsights.length === 0) {
      return [];
    }
    return await Promise.all(
      inputInsights.map((insight) =>
        this.getInsightWithCreativesKeys(
          insight,
          scope,
          authorizationToken,
          insightAttributes,
        ),
      ),
    );
  }

  private async getInsightWithCreativesKeys(
    insight: Insight,
    scope: BrandCopilotScope,
    authorizationToken: string,
    insightAttributes: InsightAttributes,
  ): Promise<InsightWithCreativesKeys> {
    const insightElements = insight.report.elementsLift.map((element) => {
      return {
        value: element.id,
        type: element.typeValue,
        typeLabel: element.type,
      };
    });

    let creatives: CreativeKey[] = [];

    if (insightElements.length > 0) {
      const platformMediaIds =
        await this.getMediaByTagsService.getPlatformMediaIds(
          scope,
          insightElements,
          authorizationToken,
        );
      const workspaceIds: number[] = scope.workspaces.map((w) => w.id);
      creatives = platformMediaIds.slice(0, 15).map((platformMediaId) => {
        return {
          platformMediaId: platformMediaId,
          workspaceIds: workspaceIds,
        };
      });
    }

    return {
      ...insight,
      attributes: insightAttributes,
      creatives: creatives,
    };
  }

  private sortInsightsByMaxKpiLift(insights: Insight[]): Insight[] {
    return insights.sort((a, b) => {
      const maxKpiLiftA = Math.max(
        ...a.report.elementsLift.map((el) => Math.abs(el.kpiLift)),
      );
      const maxKpiLiftB = Math.max(
        ...b.report.elementsLift.map((el) => Math.abs(el.kpiLift)),
      );
      return maxKpiLiftB - maxKpiLiftA;
    });
  }

  private reorderInsights(insights: Insight[]): Insight[] {
    const gazeAndEmotionsInsights = [];
    const objectInsights = [];
    const logoInsights = [];
    const otherInsights = [];

    for (const insight of insights) {
      switch (insight.source) {
        case InsightGeneratorSource.GazeAndEmotionsInsightsGenerator:
          gazeAndEmotionsInsights.push(insight);
          break;
        case InsightGeneratorSource.ObjectInsightsGenerator:
          objectInsights.push(insight);
          break;
        case InsightGeneratorSource.LogoInsightsGenerator:
          logoInsights.push(insight);
          break;
        default:
          otherInsights.push(insight);
          break;
      }
    }

    return [
      ...otherInsights,
      ...logoInsights,
      ...objectInsights,
      ...gazeAndEmotionsInsights,
    ];
  }
}
