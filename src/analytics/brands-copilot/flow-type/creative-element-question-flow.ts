import { BrandCopilotNextResponse } from '../model/response.model';
import { BrandCopilotScope } from '../model/request.model';
import { Injectable } from '@nestjs/common';
import { FlowTypeExecutionHandler } from './flow-type-execution-handler';
import {
  Context,
  CopilotConversation,
} from '../model/copilot-conversation.model';
import { TagsExtractor } from '../tag-identification/tag-extractor.service';
import {
  Insight,
  InsightAttributes,
  InsightWithCreativesKeys,
} from '../report/insights.model';
import { randomUUID } from 'crypto';
import { BrandCopilotElementImpactReportService } from '../report/brand-copilot-element-impact-report.service';
import { GetMediaByTagsService } from '../compare-report/get-media-by-tags.service';
import { BrandInformationService } from '../brand-data/brand-information.service';
import { CreativeKey } from '../brand-copilot-next.dto';
import { GeneralInsightsGeneratorService } from '../insights/general-insights-generator.service';
import {
  createNoInsightsResponse,
  createNoPerformanceDataResponse,
  createNoTagsFoundResponse,
} from '../report/copilot-report.utils';
import { LLMModel } from '../model/llm-model.enum';

@Injectable()
export class CreativeElementsQuestionFlow implements FlowTypeExecutionHandler {
  constructor(
    private readonly tagsExtractor: TagsExtractor,
    private readonly elementImpactReportService: BrandCopilotElementImpactReportService,
    private readonly insightsGenerator: GeneralInsightsGeneratorService,
    private readonly getMediaByTagsService: GetMediaByTagsService,
    private readonly brandInformationService: BrandInformationService,
  ) {}

  async executeFlow(
    context: Context,
    conversation: CopilotConversation,
    scope: BrandCopilotScope,
    authorizationToken: string,
    llmModel: LLMModel,
  ): Promise<BrandCopilotNextResponse> {
    const tags = await this.tagsExtractor.getTags(conversation, llmModel);
    if (tags.length == 0) {
      return createNoTagsFoundResponse(conversation);
    }

    const report =
      await this.elementImpactReportService.getReportWithTagsFilter(
        scope,
        tags,
        authorizationToken,
      );

    if (report.elementsLift.length === 0) {
      return createNoPerformanceDataResponse(conversation);
    }

    const brands = await this.brandInformationService.getByAdAccountId(
      conversation.organizationId,
      scope.workspaces.map((w) => w.id),
      scope.adAccounts.map((adAccount) => adAccount.platformAccountId),
    );

    const brandNames = brands.map((brand) => brand.name);
    const insightAttributes: InsightAttributes = {
      platform: scope.platform,
      brands: brandNames,
      startDate: scope.startDate,
      endDate: scope.endDate,
      kpiName: report.kpiName,
      kpiFormat: report.kpiFormat,
      isInverseHealthKpi: report.isInverseHealthKpi,
    };

    const insightsGenerationResponse =
      await this.insightsGenerator.generateInsights(
        conversation,
        report,
        insightAttributes,
        brands,
        llmModel,
      );

    if (insightsGenerationResponse.insights?.length === 0) {
      return createNoInsightsResponse(conversation);
    }

    const responseInsights = await this.getInsightsWithCreativesKeys(
      insightsGenerationResponse.insights,
      scope,
      authorizationToken,
      insightAttributes,
    );

    return {
      chatId: conversation.id,
      answerPrompt: insightsGenerationResponse.promptResponse,
      messageId: randomUUID().toString(),
      role: 'VIDMOB',
      model: llmModel,
      insights: responseInsights,
    };
  }

  // TODO: Currently, the creative retrieval process is inefficient.
  //       Our immediate focus is to ensure 100% functionality.
  //       The Plan is to: Optimize the code to use a single dedicated query for
  //       Copilot, instead of multiple calls. Specifically:
  //       1. Retrieve report KPI life.
  //       2. For each insight, get creatives by tag and then retrieve the creative.
  private async getInsightsWithCreativesKeys(
    inputInsights: Insight[],
    scope: BrandCopilotScope,
    authorizationToken: string,
    insightAttributes: InsightAttributes,
  ): Promise<InsightWithCreativesKeys[]> {
    if (inputInsights.length === 0) {
      return [];
    }
    return await Promise.all(
      inputInsights.map((insight) =>
        this.getInsightDtoWithCreatives(
          insight,
          scope,
          authorizationToken,
          insightAttributes,
        ),
      ),
    );
  }

  private async getInsightDtoWithCreatives(
    insight: Insight,
    scope: BrandCopilotScope,
    authorizationToken: string,
    insightAttributes: InsightAttributes,
  ): Promise<InsightWithCreativesKeys> {
    const insightElements = insight.report.elementsLift.map((element) => {
      return {
        value: element.id,
        type: element.typeValue,
        typeLabel: element.type,
      };
    });

    let creatives: CreativeKey[] = [];

    if (insightElements.length > 0) {
      const platformMediaIds =
        await this.getMediaByTagsService.getPlatformMediaIds(
          scope,
          insightElements,
          authorizationToken,
        );
      const workspaceIds: number[] = scope.workspaces.map((w) => w.id);
      creatives = platformMediaIds.slice(0, 15).map((platformMediaId) => {
        return {
          platformMediaId: platformMediaId,
          workspaceIds: workspaceIds,
        };
      });
    }

    return {
      ...insight,
      attributes: insightAttributes,
      creatives: creatives,
    };
  }
}
