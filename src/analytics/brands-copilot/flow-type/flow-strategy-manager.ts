import { Injectable } from '@nestjs/common';
import { FlowTypeExecutionHandler } from './flow-type-execution-handler';
import { CreativeElementsQuestionFlow } from './creative-element-question-flow';
import { NonIdentifiedFlow } from './non-identified-flow';
import { BrandCopilotFlowType } from './brand-copilot-flow-type';
import { PerformanceQuestionWithoutCreativeElementsFlow } from './performance-question-without-creative-elements-flow.service';

// TODO : Check how to implement strategy in nestjs without explicitly matching enum to a newly created object
@Injectable()
export class FlowStrategy {
  private readonly flowHandlers: Map<
    BrandCopilotFlowType,
    FlowTypeExecutionHandler
  >;

  constructor(
    private readonly creativeElementsQuestionFlow: CreativeElementsQuestionFlow,
    private readonly nonIdentifiedFlow: NonIdentifiedFlow,
    private readonly openQuestionFlow: PerformanceQuestionWithoutCreativeElementsFlow,
  ) {
    this.flowHandlers = new Map<BrandCopilotFlowType, FlowTypeExecutionHandler>(
      [
        [
          BrandCopilotFlowType.CREATIVE_ELEMENTS_QUESTION,
          this.creativeElementsQuestionFlow,
        ],
        [
          BrandCopilotFlowType.PERFORMANCE_QUESTION_WITHOUT_CREATIVE_ELEMENTS,
          this.openQuestionFlow,
        ],
        [BrandCopilotFlowType.OTHER, this.nonIdentifiedFlow],
      ],
    );
  }

  getFlowHandler(flowType: BrandCopilotFlowType): FlowTypeExecutionHandler {
    return this.flowHandlers.get(flowType) ?? this.nonIdentifiedFlow;
  }
}
