// compare-report.client.ts
import { AxiosInstance } from 'axios';
import { Injectable, Logger } from '@nestjs/common';
import { CompareReportClient } from './compare-report.client';
import { BrandCopilotScope } from '../model/request.model';
import { CompareReportPayload } from './compare-report.interfaces';
import { BrandCopilotElementImpactReportService } from '../report/brand-copilot-element-impact-report.service';
import { KpiInfo } from '../kpi/kpi.model';
import { buildCustomTagTimeRange } from '../report/copilot-report.utils';
import { CopilotKpiService } from '../kpi/copilot-kpi.service';
import { AdvancedFilterTransformerService } from '../report/advanced-filter-transformer.service';

// Very inefficient solution, currently I mimic element impact report UI behavior.
// Once we create our own queries for brand copilot, we could unify most brand
// copilot needs in a single query
// FIXME: Add types to the filter it is required for correct data
@Injectable()
export class GetMediaByTagsService {
  private readonly logger = new Logger(GetMediaByTagsService.name);
  private readonly axiosInstance: AxiosInstance;

  constructor(
    private readonly compareReportClient: CompareReportClient,
    private readonly kpiService: CopilotKpiService,
    private readonly advancedFilterTransformer: AdvancedFilterTransformerService,
  ) {}

  async getPlatformMediaIds(
    scope: BrandCopilotScope,
    tags: { value: string; type: string; typeLabel: string }[],
    authToken: string,
  ): Promise<string[]> {
    const adAccountIds = scope.adAccounts.map(
      (account) => account.platformAccountId,
    );
    const kpiInfo = await this.getKpiInfo(scope, adAccountIds);
    const payload = this.buildCompareReportPayload(scope, kpiInfo, tags);
    const response = await this.compareReportClient.fetchReport(
      payload,
      authToken,
    );
    return this.parsePlatformMediaIds(response);
  }

  private buildCompareReportPayload(
    scope: BrandCopilotScope,
    kpiInfo: KpiInfo,
    tags: { value: string; type: string; typeLabel: string }[],
  ): CompareReportPayload {
    const normalizedPlatform = scope.platform.toLowerCase();
    const mediaTypes =
      BrandCopilotElementImpactReportService.getMediaTypes(scope);
    const valueLabels = tags.map((tag) => {
      if (tag.type === 'FACE:EMOTION') {
        return tag.value.toUpperCase();
      } else if (tag.type === 'COLOR:DOMINANT_COLORS:CATEGORY') {
        return tag.value.toLowerCase();
      } else {
        return tag.value;
      }
    });

    const transformedFilters = this.advancedFilterTransformer.transformFilters(
      scope.filters,
      normalizedPlatform,
    );

    const payloadFilters = {
      ...transformedFilters,
      account: scope.adAccounts.map((adAccount) => adAccount.platformAccountId),
      'media_tag[value_label]': valueLabels,
    };

    payloadFilters.account = scope.adAccounts.map(
      (adAccount) => adAccount.platformAccountId,
    );

    const customTagTimeRange = buildCustomTagTimeRange(kpiInfo);
    const kpiOptionsWithCustomTagTimeRange = {
      kpiIds: [...scope.kpiOptions?.kpiIds],
      ...(scope.kpiOptions?.currencyObject?.id && {
        currency: scope.kpiOptions.currencyObject.id,
      }),
      customTagTimeRange: customTagTimeRange,
    };

    const options: any = {
      includeTracker: true,
      includeMissingGroups: false,
      includeMergedStats: false,
      includeApertureTags: true,
    };

    if (
      this.advancedFilterTransformer.isCreatedByVidmobAdvancedFilterSet(scope)
    ) {
      this.advancedFilterTransformer.addCreatedByVidmobAdvancedFilterToCompareReportOptions(
        options,
        scope,
      );
    }

    return {
      platform: normalizedPlatform,
      startDate: scope.startDate,
      endDate: scope.endDate,
      filter: payloadFilters,
      metrics: [],
      fileTypes: mediaTypes,
      options: options,
      kpiOptions: kpiOptionsWithCustomTagTimeRange,
      dimensions: [],
      elementBy: {},
      groupBy: {
        type: 'creative',
      },
      averageBy: {
        type: 'custom',
      },
      aggregateAllAdTypes: true,
      organizationId: scope.organizationId,
      workspaceIds: scope.workspaces.map((w) => w.id),
    };
  }

  private parsePlatformMediaIds(httpResponseData: any): string[] {
    const groups = httpResponseData.data.groups;
    return Object.keys(groups).filter((key) => key !== '__average');
  }

  private async getKpiInfo(scope: BrandCopilotScope, adAccountIds: string[]) {
    const kpiId = scope.kpiOptions.kpiIds[0];
    return await this.kpiService
      .getKpiInfo(scope.platform, [kpiId], adAccountIds)
      .then((kpiMap) => kpiMap[kpiId]);
  }
}
