// compare-report.interfaces.ts
export interface CompareReportFilters {
  [key: string]: any;
}

export interface CompareReportPayload {
  platform: string;
  startDate: string;
  endDate: string;
  filter: CompareReportFilters;
  metrics: string[];
  fileTypes: string[];
  options: {
    includeTracker: boolean;
    includeMissingGroups: boolean;
    includeMergedStats: boolean;
    includeApertureTags: boolean;
    createdByVidmob: boolean;
  };
  kpiOptions: any;
  dimensions: string[];
  elementBy: any;
  groupBy: any;
  averageBy: any;
  aggregateAllAdTypes: boolean;
  organizationId: string;
  workspaceIds: number[];
}
