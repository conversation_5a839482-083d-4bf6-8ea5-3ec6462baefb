// compare-report.client.ts
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CompareReportPayload } from './compare-report.interfaces';

@Injectable()
export class CompareReportClient {
  private readonly logger = new Logger(CompareReportClient.name);
  private readonly axiosInstance: AxiosInstance;
  private readonly apiUrl: string;

  constructor(private readonly configService: ConfigService) {
    const baseVidMobApiUrl = this.configService.get<string>(
      'baseVidMobApiUrl',
      '',
    );
    this.apiUrl = `${baseVidMobApiUrl.replace(
      'VidMob',
      '',
    )}as-gw/api/v2/compare-report`;

    this.axiosInstance = axios.create({
      baseURL: this.apiUrl,
    });
  }

  async fetchReport(
    payload: CompareReportPayload,
    authToken: string,
  ): Promise<any> {
    const headers = {
      Authorization: `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };

    try {
      const response: AxiosResponse<any> = await this.axiosInstance.post(
        '',
        payload,
        { headers },
      );
      return response.data;
    } catch (error) {
      throw new Error(
        `Error fetching compare report through HTTP request: ${error.message}`,
      );
    }
  }
}
