import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { Context } from '../model/copilot-conversation.model';
import { OrganizationUserService } from '../../../account-management/organization/organization-user/organization-user.service';
import { ScoringAuthService } from '../../../scoring/scoring-auth/scoring-auth.service';

@Injectable()
export class BrandCopilotAuthorizationService {
  private readonly logger = new Logger(BrandCopilotAuthorizationService.name);

  constructor(
    private readonly organizationService: OrganizationUserService,
    private readonly scoringAuthService: ScoringAuthService,
  ) {}

  // I didn't find a standard implementation that receives user id and validates
  // whether it has access to organization and workspace id. My current solution
  // based on the requests I found in scoring reports. Yet, these queries are not
  // efficient and should be standardized in a single location. I'll discuss it
  // with the team to see how we make sure we have this logic once in a central
  // place
  async validateOrganizationAndWorkspaceAccess(
    userId: number,
    organizationId: string,
    workspaceIds: number[],
  ): Promise<Context> {
    const getUserResponse =
      await this.organizationService.getUserInOrganization(
        organizationId,
        userId,
      );
    const user = getUserResponse.result;

    if (!user.roles || user.roles.length === 0) {
      throw new UnauthorizedException(
        `User: ${userId} does not have access to organization: ${organizationId}`,
      );
    }

    const userWorkspaces =
      await this.scoringAuthService.getRelatedAccessiblePartners(
        userId,
        workspaceIds[0], // Assuming there's at least one workspace to check
      );

    const userWorkspacesSet = new Set(
      userWorkspaces.map((workspaceDto) => workspaceDto.id),
    );
    workspaceIds.forEach((id) => {
      if (!userWorkspacesSet.has(id)) {
        throw new UnauthorizedException(
          `User: ${userId} does not have access to workspace: ${id} in organization: ${organizationId}`,
        );
      }
    });

    return {
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
      },
      organization: {
        id: userWorkspaces[0].organizationId,
        name:
          (userWorkspaces[0] as { [key: string]: any }).organizationName ||
          userWorkspaces[0].name, // Temporary solution, will change once we decide where to get brand name from
      },
    };
  }
}
