import { Injectable, Logger } from '@nestjs/common';

// This class encapsulated the logic of dealing with tags metadata. e.g if we have an insight step about demographics, it
// may want to know which tags are related to demographic data. For now, this class is going to bring this information
// hardcoded, in the future we will decide whether we should bring this data from static config files or from another
// service that is going to manage this logic.
@Injectable()
export class TagsMetadataService {
  async getDemographicTags(): Promise<string[]> {
    return [
      'Adult',
      'Baby',
      'Boy',
      'Child',
      'Female',
      'Girl',
      'Lady',
      'Male',
      'Man',
      'Newborn',
      'Senior Citizen',
      'Teen',
      'Woman',
    ];
  }

  async getSceneryGroupsTags(): Promise<string[]> {
    return ['City', 'Outdoors', 'Indoors'];
  }

  async getHumanPresenceGroupsTags(): Promise<string[]> {
    return ['Family', 'Jury', 'People', 'Person', 'Team', 'Tribe'];
  }

  async getBodyPartsFocusTags(): Promise<string[]> {
    return [
      'Ankle',
      '<PERSON>',
      'Back',
      '<PERSON>',
      'Dimple<PERSON>',
      'Ear',
      'Face',
      '<PERSON>ger',
      'Fist',
      '<PERSON>eck<PERSON>',
      '<PERSON>',
      'Head',
      'Heel',
      'Hip',
      'Jaw',
      '<PERSON>nee',
      'Mouth',
      'Mustache',
      'Navel',
      'Neck',
      'Shoulder',
      'Skin',
      'Stomach',
      'Teeth',
      'Thigh',
      'Throat',
      'Toe',
      'Tongue',
      'Torso',
      'Wrist',
    ];
  }
}
