import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { BrandElasticsearchService } from './elasticsearch.service';
import {
  CopilotConversation,
  CopilotConversationMessageResponse,
} from '../model/copilot-conversation.model';
import { QueryDslQueryContainer } from '@elastic/elasticsearch/lib/api/types';
import { CopilotRepository } from './copilot-repository.model';
import { AnalyticsCopilotConversationListItem } from '../../analytics-copilot/dto/analytics-copilot-conversation.dto';
import { UUID } from 'crypto';
import { error } from 'console';

@Injectable()
export class CopilotElasticSearchRepository implements CopilotRepository {
  private readonly logger = new Logger(CopilotElasticSearchRepository.name);
  private static readonly BRAND_COPILOT_CONVERSATIONS_INDEX =
    'brand_copilot_conversations';

  constructor(private readonly es: BrandElasticsearchService) {}

  async getList(
    organizationId: string,
    userId: number,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    const query = this.buildSearchQuery({
      user: userId,
      organization: organizationId,
    });
    const projectionFields = ['id', 'title', 'timestamp'];
    return await this.es.search<AnalyticsCopilotConversationListItem>(
      CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
      query,
      200,
      [{ timestamp: { order: 'desc' } }],
      projectionFields,
    );
  }

  async get(
    userId: number,
    chatId: string,
  ): Promise<CopilotConversation | null> {
    const getResponse = await this.es.get<CopilotConversation>(
      CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
      chatId,
    );

    if (!getResponse) {
      return null;
    }

    if (getResponse.userId !== userId) {
      this.logger.warn(`getConversation chat: ${chatId} was called with user: 
      ${userId} while db user set to: ${getResponse.userId}`);
      return null;
    }

    return getResponse;
  }

  async save(conversation: CopilotConversation): Promise<CopilotConversation> {
    try {
      // Use index API directly to either create or overwrite the document
      await this.es.index(
        CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
        conversation.id,
        conversation,
      );

      return conversation;
    } catch (e) {
      this.logger.error(
        `Failed to index copilot conversation with id: ${conversation.id}`,
        e,
      );
      throw new Error(
        `Error while indexing conversation ${conversation.id}: ${e.message}`,
      );
    }
  }

  private buildSearchQuery({
    user,
    chat,
    organization,
  }: {
    user?: number;
    chat?: string;
    organization?: string;
  }): QueryDslQueryContainer {
    const mustQueries: any[] = [];

    if (user) mustQueries.push({ match: { userId: user } });
    if (chat) mustQueries.push({ term: { 'id.keyword': chat } });
    if (organization)
      mustQueries.push({ term: { 'organizationId.keyword': organization } });
    return { bool: { must: mustQueries } };
  }

  async delete(chatId: string): Promise<void> {
    await this.es.delete(
      CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
      chatId,
    );
  }

  async existsByUserIdAndChatId(
    userId: number,
    chatId: UUID,
  ): Promise<boolean> {
    const query = this.buildSearchQuery({ user: userId, chat: chatId });
    try {
      return await this.es.exists(
        CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
        query,
      );
    } catch (e) {
      this.logger.error(
        `Error while checking if conversation exists ${e} with chatId: ${chatId} and userId: ${userId} errorstack: ${e.stack}`,
      );
      throw error;
    }
  }

  async updateInsightWithLibraryId(
    chatId: UUID,
    messageId: UUID,
    insightId: UUID,
    insightLibraryId: string,
  ): Promise<void> {
    try {
      const conversation = await this.es.get<CopilotConversation>(
        CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
        chatId,
      );

      if (!conversation) {
        throw new NotFoundException(
          `Conversation with id ${chatId} not found.`,
        );
      }

      const message = conversation.messages.find(
        (msg) =>
          'id' in msg &&
          (msg as CopilotConversationMessageResponse).id === messageId,
      ) as CopilotConversationMessageResponse | undefined;

      if (!message || !message.insights) {
        throw new NotFoundException(
          `Message or insights not found for messageId: ${messageId}`,
        );
      }

      const insight = message.insights.find((ins) => ins.id === insightId);

      if (!insight) {
        throw new NotFoundException(
          `Insight with id ${insightId} not found in message ${messageId}`,
        );
      }

      insight.insightLibraryId = insightLibraryId;

      await this.es.index(
        CopilotElasticSearchRepository.BRAND_COPILOT_CONVERSATIONS_INDEX,
        chatId,
        conversation,
        true,
      );

      this.logger.log(
        `Updated insight ${insightId} in conversation ${chatId} with insightLibraryId ${insightLibraryId}.`,
      );
    } catch (error) {
      this.logger.error(
        `Error updating insight with id ${insightId} in chat ${chatId}: ${error.message}`,
      );
      throw error;
    }
  }
}
