import {
  Injectable,
  Logger,
  OnM<PERSON>ule<PERSON><PERSON>roy,
  OnModuleInit,
} from '@nestjs/common';
import {
  MongoClient,
  Db,
  Collection,
  Document,
  Filter,
  Sort,
  ServerApiVersion,
  MongoClientOptions,
  WithId,
} from 'mongodb';
import { ConfigService } from '@nestjs/config';
import {
  BRAND_INSIGHTS_COLLECTION_PREFIX,
  LOCALHOST_MONGODB_URI,
  MONGODB_REGION,
  MONGODB_SEARCH_MAX_SIZE,
  MONGODB_SECRET_NAME,
  MONGODB_VERSION_STAGE,
  NORMATIVE_INSIGHTS_COLLECTION_PREFIX,
} from '../../analytics-copilot/constants/analytics-copilot.constants';
import * as tls from 'tls';
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from '@aws-sdk/client-secrets-manager';

// Define a type for transformed documents
type TransformedDocument<T extends Document> = Omit<WithId<T>, '_id'> & {
  id: string;
};

@Injectable()
export class MongoDbService<T extends Document>
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(MongoDbService.name);
  private client: MongoClient;
  private db: Db;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
  }

  private async connect() {
    try {
      const uri =
        this.configService.get<string>('MONGODB_URI') || LOCALHOST_MONGODB_URI;
      const dbName = this.configService.get<string>('MONGODB_DB_NAME');

      let certBase64 = process.env.MONGODB_X509_CERT_BASE64;

      if (!certBase64) {
        certBase64 = await this.getMongoDbCert();
      }

      // Decode the Base64-encoded certificate
      const certPem = Buffer.from(certBase64, 'base64');

      // Create a secure context using the certificate content
      const secureContext = tls.createSecureContext({
        cert: certPem,
        key: certPem,
      });

      const options: MongoClientOptions = {
        tls: true,
        tlsInsecure: false,
        serverApi: ServerApiVersion.v1,
        secureContext: secureContext, // Attach the custom secure context
      };

      this.client = new MongoClient(uri, options);

      await this.client.connect();
      this.db = this.client.db(dbName);
      this.logger.log('Successfully connected to MongoDB.');
    } catch (error) {
      this.logger.error(`Error connecting to MongoDB: ${error.message}`);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      if (this.client) {
        await this.client.close();
        this.logger.log('MongoDB client connection closed');
      }
    } catch (error) {
      this.logger.error(
        `Error during MongoDB client cleanup: ${error.message}`,
      );
    }
  }

  /**
   * Retrieves a MongoDB collection by name.
   */
  getCollection(collectionName: string): Collection<T> {
    if (!this.db) {
      throw new Error('Database connection not initialized.');
    }
    return this.db.collection<T>(collectionName);
  }

  /**
   * Dynamically sets indexes based on the collection prefix.
   */
  async setIndexes(collectionName: string) {
    try {
      const collection = this.getCollection(collectionName);

      if (collectionName.startsWith(BRAND_INSIGHTS_COLLECTION_PREFIX)) {
        // Brand Insights collections
        await collection.createIndex(
          { userId: 1, organizationId: 1 },
          { background: true },
        );
        await collection.createIndex(
          { organizationId: 1, timestamp: -1 },
          { background: true },
        );
      } else if (
        collectionName.startsWith(NORMATIVE_INSIGHTS_COLLECTION_PREFIX)
      ) {
        // Normative Insights collections
        await collection.createIndex(
          { userId: 1, organizationId: 1 },
          { background: true },
        );
        await collection.createIndex(
          { organizationId: 1, timestamp: -1 },
          { background: true },
        );
        // If you have messageId field used frequently for search:
        await collection.createIndex({ messageId: 1 }, { background: true });
      } else {
        this.logger.debug(
          `No specific indexes defined for collection: ${collectionName}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error ensuring indexes for collection ${collectionName}: ${error.message}`,
      );
    }
  }

  /**
   * Inserts or updates a document in a collection.
   */
  async index(
    collectionName: string,
    id: string,
    document: Partial<T>,
  ): Promise<{ id: string }> {
    try {
      const collection = this.getCollection(collectionName);
      const { id: _, ...documentWithoutId } = document;

      const result = await collection.findOneAndUpdate(
        { _id: id } as Filter<T>,
        { $set: documentWithoutId as any },
        { upsert: true, returnDocument: 'after' },
      );

      return { id: result.value?._id.toString() || id };
    } catch (error) {
      this.logger.error(
        `Error indexing document with id ${id} in collection ${collectionName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Searches for documents in a collection.
   */
  async search(
    collectionName: string,
    filters: Filter<T>,
    size = 1,
    sort: Sort = {},
    projection?: Partial<Record<keyof T, 1 | 0>>,
  ): Promise<TransformedDocument<T>[]> {
    if (size > MONGODB_SEARCH_MAX_SIZE) {
      this.logger.warn(
        `Search size ${size} is greater than the maximum allowed size ${MONGODB_SEARCH_MAX_SIZE}. Truncating to ${MONGODB_SEARCH_MAX_SIZE}.`,
      );
      size = MONGODB_SEARCH_MAX_SIZE;
    }

    try {
      const collection = this.getCollection(collectionName);
      let cursor = collection.find(filters).limit(size).sort(sort);
      if (projection) cursor = cursor.project(projection);

      const results = await cursor.toArray();

      return results.map((doc) => this.transformDocument(doc));
    } catch (error) {
      this.logger.error(
        `Error searching documents in collection ${collectionName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Retrieves a single document by ID.
   */
  async get(
    collectionName: string,
    id: string,
  ): Promise<TransformedDocument<T> | null> {
    try {
      const collection = this.getCollection(collectionName);
      const result = await collection.findOne({ _id: id } as Filter<T>);
      if (!result) return null;

      return this.transformDocument(result);
    } catch (error) {
      this.logger.error(
        `Error getting document with id ${id} in collection ${collectionName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Deletes a document by ID.
   */
  async delete(collectionName: string, id: string): Promise<void> {
    try {
      const collection = this.getCollection(collectionName);
      const result = await collection.deleteOne({ _id: id } as Filter<T>);
      if (result.deletedCount === 0) {
        this.logger.warn(
          `Document with id ${id} not found in collection ${collectionName}.`,
        );
        throw new Error(`Document deletion failed for id: ${id}`);
      }
      this.logger.log(
        `Successfully deleted document with id: ${id} in collection ${collectionName}`,
      );
    } catch (error) {
      this.logger.error(
        `Error deleting document with id: ${id} in collection ${collectionName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Checks if a document exists in a collection.
   */
  async exists(collectionName: string, filters: Filter<T>): Promise<boolean> {
    try {
      const collection = this.getCollection(collectionName);
      const exists = await collection.findOne(filters, {
        projection: { _id: 1 },
      });
      return exists !== null;
    } catch (error) {
      this.logger.error(
        `Error checking document existence in collection ${collectionName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Retrieves dynamic collection names.
   */
  async getDynamicCollections(): Promise<string[]> {
    try {
      const collections = await this.db.listCollections().toArray();
      return collections.map((col) => col.name);
    } catch (error) {
      this.logger.error(`Error fetching dynamic collections: ${error.message}`);
      throw error;
    }
  }

  /**
   * Updates a single document that matches the provided filter.
   * Applies partial updates (e.g., $set) to the matched document.
   *
   * @param collectionName - The name of the collection
   * @param filter - The filter used to select the document to update
   * @param update - The update operations to apply (e.g. { $set: { title: "newTitle" } })
   * @returns An object containing matchedCount and modifiedCount
   */
  async updateOne(
    collectionName: string,
    filter: Filter<T>,
    update: Document,
    options?: { arrayFilters?: any[] },
  ): Promise<{ matchedCount: number; modifiedCount: number }> {
    try {
      const collection = this.getCollection(collectionName);
      const result = await collection.updateOne(filter, update, options);
      return {
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
      };
    } catch (error) {
      this.logger.error(
        `Error updating document in ${collectionName} with filter ${JSON.stringify(
          filter,
        )}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Transforms a MongoDB document by replacing _id with id.
   */
  transformDocument(doc: WithId<T>): TransformedDocument<T> {
    const { _id, ...rest } = doc;
    return { id: _id.toString(), ...rest };
  }

  /**
   * Retrieves the MongoDB certificate from AWS Secrets Manager.
   */
  async getMongoDbCert(): Promise<string> {
    const client = new SecretsManagerClient({
      region: MONGODB_REGION,
    });
    let response;

    try {
      response = await client.send(
        new GetSecretValueCommand({
          SecretId: MONGODB_SECRET_NAME,
          VersionStage: MONGODB_VERSION_STAGE,
        }),
      );
    } catch (error) {
      this.logger.error('Error fetching MongoDB certificate:', error.message);
      throw error;
    }

    const cert = response.SecretString;
    return cert || '';
  }
}
