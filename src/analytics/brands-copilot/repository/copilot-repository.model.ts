import { CopilotConversation } from '../model/copilot-conversation.model';
import { AnalyticsCopilotConversationListItem } from '../../analytics-copilot/dto/analytics-copilot-conversation.dto';
import { UUID } from 'crypto';

// Interface for repositories without organizationId, used for Elasticsearch
export interface CopilotRepository {
  getList(
    organizationId: string,
    userId: number,
  ): Promise<AnalyticsCopilotConversationListItem[]>;

  get(userId: number, chatId: string): Promise<CopilotConversation | null>;

  save(conversation: CopilotConversation): Promise<CopilotConversation>;

  delete(chatId: string): Promise<void>;

  existsByUserIdAndChatId(userId: number, chatId: UUID): Promise<boolean>;
}

// Interface for repositories with organizationId, used for MongoDB
export interface CopilotRepositoryWithOrganization {
  getList(
    organizationId: string,
    userId: number,
  ): Promise<AnalyticsCopilotConversationListItem[]>;

  get(
    organizationId: string,
    userId: number,
    chatId: string,
  ): Promise<CopilotConversation | null>;

  save(
    organizationId: string,
    conversation: CopilotConversation,
  ): Promise<CopilotConversation>;

  delete(organizationId: string, chatId: string): Promise<void>;

  existsByUserIdAndChatId(
    organizationId: string,
    userId: number,
    chatId: UUID,
  ): Promise<boolean>;
}
