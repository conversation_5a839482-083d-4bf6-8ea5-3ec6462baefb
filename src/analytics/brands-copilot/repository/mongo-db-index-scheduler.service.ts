import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { MongoDbService } from './mongo-db.service';

@Injectable()
export class IndexSchedulerService {
  private readonly logger = new Logger(IndexSchedulerService.name);

  constructor(private readonly mongoDbService: MongoDbService<any>) {}

  @Cron(CronExpression.EVERY_12_HOURS)
  async ensureIndexesForDynamicCollections() {
    this.logger.log(
      'Starting the ensureIndexes process for dynamic collections.',
    );

    try {
      const collections = await this.mongoDbService.getDynamicCollections();
      const concurrencyLimit = 5;

      await this.processWithLimitedConcurrency(
        collections,
        concurrencyLimit,
        async (collectionName) => {
          try {
            await this.mongoDbService.setIndexes(collectionName);
            this.logger.log(
              `Indexes created successfully for collection: ${collectionName}`,
            );
          } catch (error) {
            this.logger.error(
              `Error creating indexes for collection: ${collectionName}. Error: ${error.message}`,
            );
          }
        },
      );

      this.logger.log(
        'Completed the ensureIndexes process for all collections.',
      );
    } catch (error) {
      this.logger.error('Failed to fetch dynamic collections.', error.message);
    }
  }

  private async processWithLimitedConcurrency<T>(
    items: T[],
    concurrencyLimit: number,
    task: (item: T) => Promise<void>,
  ): Promise<void> {
    const activeTasks: Promise<void>[] = [];
    for (const item of items) {
      const taskPromise = task(item);
      activeTasks.push(taskPromise);

      // If we reach the concurrency limit, wait for the first promise to settle
      if (activeTasks.length >= concurrencyLimit) {
        await Promise.race(activeTasks).then(() => {
          // Remove settled tasks
          const settledIndex = activeTasks.findIndex(
            (promise) => promise === taskPromise,
          );
          if (settledIndex >= 0) activeTasks.splice(settledIndex, 1);
        });
      }
    }

    await Promise.all(activeTasks);
  }
}
