import { Injectable, Logger, OnM<PERSON>ule<PERSON><PERSON>roy } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import {
  QueryDslQueryContainer,
  Sort,
} from '@elastic/elasticsearch/lib/api/types';
import {
  ELASTIC_SEARCH_API_KEY,
  ELASTIC_SEARCH_NODE,
} from '../../analytics-copilot/constants/analytics-copilot.constants';
import { UUID } from 'crypto';

@Injectable()
export class BrandElasticsearchService implements OnModuleDestroy {
  private readonly logger = new Logger(BrandElasticsearchService.name);
  private esClient: Client;

  constructor() {
    this.esClient = new Client({
      node: ELASTIC_SEARCH_NODE,
      auth: {
        apiKey: ELASTIC_SEARCH_API_KEY,
      },
    });
  }

  async index<T>(
    index: string,
    id: string,
    document: T,
    refresh = false,
  ): Promise<{ id: string }> {
    try {
      const response = await this.esClient.index({
        index: index,
        id: id,
        document: document,
        refresh: refresh ? 'true' : 'false',
      });
      return { id: response._id };
    } catch (error) {
      this.logger.error(
        `Error indexing document in ${index} with id ${id}: ${error.stack}`,
      );
      throw error;
    }
  }

  async search<T>(
    index: string,
    query: QueryDslQueryContainer,
    size = 1,
    sort: Sort = [],
    projection?: string[],
  ): Promise<T[]> {
    try {
      const response = await this.esClient.search({
        index: index,
        query: query,
        sort: sort,
        size: size,
        _source: projection || true,
      });
      return response.hits.hits.map((hit) => hit._source as T);
    } catch (error) {
      this.logger.error(
        `Error searching documents in ${index} with query ${JSON.stringify(
          query,
        )}: ${error.stack}`,
      );
      throw error;
    }
  }

  async get<T>(index: string, id: string): Promise<T | null> {
    try {
      const response = await this.esClient.get({
        index: index,
        id: id,
      });

      if (!response.found) {
        this.logger.warn(`Document with id ${id} not found in index ${index}.`);
        return null;
      }

      return response._source as T;
    } catch (error) {
      this.logger.error(
        `Error getting document in ${index} with id ${id}: ${error.stack}`,
      );
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await this.esClient.close();
      this.logger.log('Elasticsearch client has been closed successfully.');
    } catch (error) {
      this.logger.warn('Error shutting down Elasticsearch client', error.stack);
    }
  }

  async delete(index: string, id: string): Promise<void> {
    try {
      const response = await this.esClient.delete({
        index,
        id,
      });

      if (response.result !== 'deleted') {
        this.logger.warn(`Failed to delete document with id: ${id}`);
        throw new Error(`Document deletion failed for id: ${id}`);
      }

      this.logger.log(`Successfully deleted document with id: ${id}`);
    } catch (error) {
      this.logger.error(`Error deleting document with id: ${id}`, error);
      throw new Error(
        `Error deleting document with id: ${id}: ${error.message}`,
      );
    }
  }

  async exists(index: string, query: QueryDslQueryContainer): Promise<boolean> {
    try {
      const response = await this.esClient.count({
        index: index,
        body: {
          query: query,
        },
      });

      return response.count > 0;
    } catch (error) {
      throw error;
    }
  }
}
