import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { MongoDbService } from './mongo-db.service';
import {
  CopilotConversationDocument,
  CopilotConversation,
} from '../model/copilot-conversation.model';
import { AnalyticsCopilotConversationListItem } from '../../analytics-copilot/dto/analytics-copilot-conversation.dto';
import { randomUUID, UUID } from 'crypto';
import { CopilotRepositoryWithOrganization } from './copilot-repository.model';
import { BRAND_INSIGHTS_COLLECTION_PREFIX } from '../../analytics-copilot/constants/analytics-copilot.constants';

@Injectable()
export class CopilotMongoDbRepository
  implements CopilotRepositoryWithOrganization
{
  private readonly logger = new Logger(CopilotMongoDbRepository.name);

  constructor(
    private readonly mongoService: MongoDbService<CopilotConversationDocument>,
  ) {}

  /**
   * Constructs the collection name dynamically for Copilot conversations.
   *
   * @param organizationId - The ID of the organization.
   * @returns The name of the collection for Copilot conversations.
   */
  private getCollectionName(organizationId: string): string {
    return `${BRAND_INSIGHTS_COLLECTION_PREFIX}-${organizationId}`;
  }

  /**
   * Retrieves a list of conversations for a specific organization and user.
   * Converts the `timestamp` field from `Date` to numeric timestamp (`number`).
   *
   * @param organizationId - The ID of the organization.
   * @param userId - The ID of the user.
   * @returns An array of conversation list items with numeric timestamps.
   */
  async getList(
    organizationId: string,
    userId: number,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    try {
      const collectionName = this.getCollectionName(organizationId);

      const results = await this.mongoService.search(
        collectionName,
        { userId },
        200, // Limit to 200 items
        { timestamp: -1 }, // Sort by timestamp descending
        { _id: 1, title: 1, timestamp: 1 },
      );

      return results.map((doc) => ({
        id: String(doc.id),
        title: doc.title,
        timestamp: doc.timestamp?.getTime(),
      }));
    } catch (error) {
      this.logger.error(
        `Error retrieving conversation list for org ${organizationId} and userId ${userId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Retrieves a specific conversation by chatId for a given organization and user.
   * Converts `timestamp` and `lastInteractionDate` from `Date` to numeric timestamp (`number`).
   *
   * @param organizationId - The ID of the organization.
   * @param userId - The ID of the user.
   * @param chatId - The ID of the chat/conversation.
   * @returns The conversation object with numeric timestamps or `null` if not found.
   */
  async get(
    organizationId: string,
    userId: number,
    chatId: UUID,
  ): Promise<CopilotConversation | null> {
    try {
      const collectionName = this.getCollectionName(organizationId);

      const document = await this.mongoService.get(collectionName, chatId);

      if (!document || document.userId !== userId) {
        this.logger.warn(
          `Conversation not found for chatId: ${chatId} and userId: ${userId} in org ${organizationId}`,
        );
        return null;
      }

      const { timestamp, lastInteractionDate, ...conversationData } = document;

      return {
        ...conversationData,
        timestamp: timestamp?.getTime(),
        lastInteractionDate: lastInteractionDate?.getTime(),
      } as CopilotConversation;
    } catch (error) {
      this.logger.error(
        `Error retrieving conversation with chatId: ${chatId} for userId: ${userId} in org ${organizationId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Saves a conversation to the database.
   *
   * @param organizationId - The ID of the organization.
   * @param conversation - The conversation object to save.
   * @returns The saved conversation object.
   */
  async save(
    organizationId: string,
    conversation: CopilotConversation,
  ): Promise<CopilotConversation> {
    try {
      conversation.id = conversation.id || randomUUID();

      const collectionName = this.getCollectionName(organizationId);
      const convertedConversation = {
        ...conversation,
        timestamp: new Date(conversation.timestamp),
        lastInteractionDate: new Date(conversation.lastInteractionDate),
      };

      await this.mongoService.index(
        collectionName,
        convertedConversation.id,
        convertedConversation as unknown as Partial<CopilotConversationDocument>,
      );

      return conversation;
    } catch (error) {
      this.logger.error(
        `Error saving conversation with id ${conversation.id} in org ${organizationId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * Deletes a conversation by chatId for a given organization.
   *
   * @param organizationId - The ID of the organization.
   * @param chatId - The ID of the chat/conversation to delete.
   */
  async delete(organizationId: string, chatId: UUID): Promise<void> {
    try {
      const collectionName = this.getCollectionName(organizationId);
      await this.mongoService.delete(collectionName, chatId);

      this.logger.log(
        `Successfully deleted conversation with id ${chatId} in org ${organizationId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error deleting conversation with id: ${chatId} in org ${organizationId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Checks if a conversation exists by userId and chatId for a given organization.
   *
   * @param organizationId - The ID of the organization.
   * @param userId - The ID of the user.
   * @param chatId - The ID of the chat/conversation.
   * @returns `true` if the conversation exists, otherwise `false`.
   */
  async existsByUserIdAndChatId(
    organizationId: string,
    userId: number,
    chatId: UUID,
  ): Promise<boolean> {
    try {
      const collectionName = this.getCollectionName(organizationId);

      return await this.mongoService.exists(collectionName, {
        userId,
        _id: chatId,
      });
    } catch (error) {
      this.logger.error(
        `Error checking existence of conversation with id ${chatId} for userId ${userId} in org ${organizationId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Updates the title of a conversation.
   *
   * @param organizationId - The ID of the organization.
   * @param userId - The ID of the user.
   * @param chatId - The ID of the conversation.
   * @param newTitle - The new title for the conversation.
   * @returns The updated conversation object.
   */
  /**
   * Updates the title of a conversation for a given user and org.
   * Returns `true` if a document was updated, or `false` if none matched.
   */
  async updateConversationTitle(
    organizationId: string,
    userId: number,
    chatId: UUID,
    newTitle: string,
  ): Promise<boolean> {
    try {
      const collectionName = this.getCollectionName(organizationId);
      const filter = { userId, _id: chatId };
      const update = { $set: { title: newTitle } };

      const result = await this.mongoService.updateOne(
        collectionName,
        filter,
        update,
      );

      if (result.matchedCount === 0) {
        this.logger.warn(
          `No conversation matched chatId: ${chatId} and userId: ${userId} in org ${organizationId}`,
        );
        return false;
      }

      // If matchedCount > 0, document is found.
      // Even if modifiedCount is 0, it means the title was already the same.
      if (result.modifiedCount === 0) {
        this.logger.debug(
          `Conversation title for chatId: ${chatId} in org ${organizationId} was already '${newTitle}'. No changes made.`,
        );
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error updating title for chatId: ${chatId} in org ${organizationId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Updates an insight within a conversation's message with a new libraryId.
   */
  async updateInsightWithLibraryId(
    organizationId: string,
    chatId: UUID,
    messageId: UUID,
    insightId: UUID,
    insightLibraryId: string,
  ): Promise<void> {
    try {
      const collectionName = this.getCollectionName(organizationId);
      // Define the filter to locate the specific document and nested array elements
      const filter = {
        _id: chatId,
        'messages.id': messageId,
        'messages.insights.id': insightId,
      };

      // Define the update operation to set the `insightLibraryId` field
      const update = {
        $set: {
          'messages.$[message].insights.$[insight].insightLibraryId':
            insightLibraryId,
        },
      };

      // Specify array filters to target the correct message and insight
      const options = {
        arrayFilters: [
          { 'message.id': messageId },
          { 'insight.id': insightId },
        ],
      };

      // Execute the update operation
      const result = await this.mongoService.updateOne(
        collectionName,
        filter,
        update,
        options,
      );

      // Handle cases where no matching document was found
      if (result.matchedCount === 0) {
        throw new NotFoundException(
          `Conversation, message, or insight not found. chatId=${chatId}, messageId=${messageId}, insightId=${insightId}`,
        );
      }

      // Log success for debugging or tracking
      this.logger.log(
        `Successfully updated insight ${insightId} in conversation ${chatId} with insightLibraryId ${insightLibraryId}.`,
      );
    } catch (error) {
      // Log and rethrow the error for proper error handling
      this.logger.error(
        `Failed to update insight ${insightId} in conversation ${chatId}: ${error.message}`,
      );
      throw error;
    }
  }

  transformDocument(document: CopilotConversationDocument) {
    return this.mongoService.transformDocument(document);
  }
}
