import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { CopilotConversation } from '../model/copilot-conversation.model';
import {
  CreativeElement,
  CreativeElementEntityType,
} from './creative-elements.model';
import { vidmobTypesExplanationPrompt } from '../llm/prompts';
import { convertToLlmMessagesWithSystemPrompt } from '../llm/llm-utils';
import { LlmClientRouter } from '../llm/llm-client-router.service';
import { LLMModel } from '../model/llm-model.enum';

const IdentifyCreativeElementTypeBreakdownPrompt = `
You are the VidMob Copilot creative element analyzer. Based on the last message identify whether a user want to perform a breakdown query on a creative element types. If needed, Use previous messages in the conversation to understand last message context. 
For each creative element type, assign a confidence score between 0 (very low confidence) to 5 (100% confident) indicating the likelihood that the user is referring to that specific element type.

${vidmobTypesExplanationPrompt}

Type breakdown query required when the question require information regarding all values of a specific type.
Note, if the user ask a question about a specific value or a specific group in a type, return NO_CREATIVE_ELEMENT_TYPE.

e.g type breakdown question
Which celebrities drive performance? - return CELEBRITY:NAME
How does different emotions affect performance? - return FACE:EMOTION
Which objects should I show in my ads? - return LABEL
Which call to actions are the best? - return CTA:BY_LINE
What messaging drove the best performance? - return TEXT:WORD_EMPHASIS
How does branding impact performance? - return LOGO
What CTA affect performance the most? - return CTA:BY_LINE
What should I show? - return LABEL
What CTA type perform the best? - return APERTURE:MESSAGING
What production type should I use - return APERTURE:PRODUCTION_TYPE
What production level performs the best - return APERTURE:PRODUCTION_LEVEL

non type breakdown question
Which kitchen objects should I show in my ads? - return NO_CREATIVE_ELEMENT_TYPE (since we asking specifically about kitchen and not about any object) 
Which music instruments affect performance? - NO_CREATIVE_ELEMENT_TYPE (music instruments is not a type)
Does featuring lebron james drive performance? - NO_CREATIVE_ELEMENT_TYPE (question about specific value in CELEBRITY:NAME type)
Should I show angry, happy, or confused people in my ads? - NO_CREATIVE_ELEMENT_TYPE  (question about specific values in FACE:EMOTION type)
Should I show ads at the outdoors? - NO_CREATIVE_ELEMENT_TYPE (outdoor is just a single group in LABEL type)_
Do you have information about specific foods performance? like fruits, vegetables, dairy products etc - NO_CREATIVE_ELEMENT_TYPE  (food is not a type, and explicit values of interest are provided)
Which Animals should I show in my ads? - NO_CREATIVE_ELEMENT_TYPE (animals are not a type)
Is it better to show a single person or multiple people in creative? - NO_CREATIVE_ELEMENT_TYPE (we are asking a question about specific labels and not general question about all labels)
Which Ads perform better? those with conversion or informational CTA? - NO_CREATIVE_ELEMENT_TYPE (we are asking a question about specific CTA types and not general question about all CTA types)

If none of the types are mentioned or if the message is unrelated to creative elements, 
return NO_CREATIVE_ELEMENT_TYPE with a confidence score indicating how certain you are that the question does not involve creative elements.

For each creative element type detected, your response should include:
The creative element type (as an enum).
A confidence score (0 to 5).
    `;

const IdentifyCreativeTypesForElementsFilterPrompt = `
You are the VidMob Copilot creative element analyzer. Based on the last message identify creative element of which types could answer the question. 
If needed, Use previous messages in the conversation to understand last message context.
For each creative element type, assign a confidence score from 0 (very low confidence) to 5 (100% sure) to indicate the likelihood that creative elements of this type could answer the user prompt.

${vidmobTypesExplanationPrompt}

e.g
Which Ads perform better? those with conversion or informational CTA? - APERTURE:MESSAGING
Who should I use in my campaigns Meryl Streep or Tom Hanks - return CELEBRITY:NAME
Which of those emotions perform the best happy,sad,surprised,angry? - return FACE:EMOTION
Should I use brownies or cakes in my ads? - return LABEL
What production type works the best: Animation, Live-Action or CGI? - return APERTURE:PRODUCTION_TYPE
Should I use production level UGC or Commercial? - return APERTURE:PRODUCTION_LEVEL
What types of messaging works the best? - APERTURE:MESSAGING
Which call to action type perform the best? - APERTURE:MESSAGING
Which call to actions should I use: 'buy now' or 'order'? - return CTA:BY_LINE
What messaging drove the best performance, feeling related messages or brand related messages? - return TEXT:WORD_EMPHASIS
Does Pringles logo works better than Lays logo? - return LOGO 
Should I show more indoor or outdoor scenery? - return LABEL
What scenery works the best? - return LABEL
Does showing Santa drive performance? - return LABEL

General question about which items/objects/things should be classified as LABEL.
Actual real-life human celebrities should be classified as CELEBRITY:NAME
Fictional characters should be classified as LABEL

If none of the types are mentioned in the question, return an empty array
For each relevant creative element type, your response should include:
The creative element type (as an enum).
A confidence score (0 to 5).

Provide output as JSON array with objects with type and confidence
`;

const tagTypeArraySchema = z.object({
  elementTypes: z.array(
    z.object({
      type: z.enum([
        'COLOR:DOMINANT_COLORS:CATEGORY',
        'COLOR:TEMPERATURE:CATEGORY',
        'LABEL',
        'FACE:GAZE_DIRECTION',
        'FACE:EMOTION',
        'COLOR:TEXT_CONTRAST:CATEGORY',
        'LOGO',
        'COLOR:VIBRANCY:CATEGORY',
        'COLOR:CONTRAST:CATEGORY',
        'FACE:SMILE',
        'TEXT:WORD_EMPHASIS',
        'CELEBRITY:NAME',
        'CTA:PRESENT',
        'CTA:BY_LINE',
        'APERTURE:MESSAGING',
        'APERTURE:PRODUCTION_TYPE',
        'APERTURE:PRODUCTION_LEVEL',
        'NO_CREATIVE_ELEMENT_TYPE',
      ]),
      confidence: z.number(),
    }),
  ),
});

@Injectable()
export class TagsTypeExtractor {
  private readonly logger = new Logger(TagsTypeExtractor.name);

  constructor(private readonly llmProviderService: LlmClientRouter) {}

  // Returns the creative element types discussed in the last message of the input conversation
  // Used to identify questions related to type breakdown
  async getConversationTagTypesBreakdown(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<CreativeElement[]> {
    try {
      const llmMessages = convertToLlmMessagesWithSystemPrompt(
        IdentifyCreativeElementTypeBreakdownPrompt,
        conversation,
      );
      const llmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse(tagTypeArraySchema, llmMessages, llmModel);
      if (!llmResult.isSuccessful()) {
        throw new Error(
          `Failed to getConversationTagTypesBreakdown for conversation id: ${conversation.id}. Error message: ${llmResult.error}`,
        );
      }

      const responseData = llmResult.value.elementTypes;
      const typesWithConfidence5 =
        this.filterTypesByConfidenceScore(responseData);

      return this.toTypeCreativeElements(typesWithConfidence5);
    } catch (error) {
      throw new Error(
        `Error during getConversationTagTypesBreakdown for conversation id: ${conversation.id}. Error: ${error.message}`,
      );
    }
  }

  private filterTypesByConfidenceScore(
    llmResponse: { confidence: number; type: string }[],
  ): string[] {
    const typesWithScore5 = llmResponse
      .filter(
        (creativeType) =>
          creativeType.confidence === 5 &&
          creativeType.type !== 'NO_CREATIVE_ELEMENT_TYPE',
      )
      .map((creativeType) => creativeType.type);

    if (typesWithScore5.length > 0) {
      return typesWithScore5;
    } else {
      return llmResponse
        .filter(
          (creativeType) =>
            creativeType.confidence === 4 &&
            creativeType.type !== 'NO_CREATIVE_ELEMENT_TYPE' &&
            creativeType.type !== 'LABEL',
        )
        .map((creativeType) => creativeType.type);
    }
  }

  private toTypeCreativeElements(types: string[]): CreativeElement[] {
    return types.map(this.toTypeCreativeElement);
  }

  private toTypeCreativeElement(type: string): CreativeElement {
    return {
      entityType: CreativeElementEntityType.CREATIVE_ELEMENT_TYPE,
      value: type,
      type: type,
      breakdownByChildElements: true,
    } as CreativeElement;
  }

  // Returns the creative element types which may be relevant for user question
  // It is used to make sure we only query tag values of relevant types from the
  // vector DB
  async getTagTypesToFilter(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<string[]> {
    try {
      const llmMessages = convertToLlmMessagesWithSystemPrompt(
        IdentifyCreativeTypesForElementsFilterPrompt,
        conversation,
      );
      const llmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse(tagTypeArraySchema, llmMessages, llmModel);

      if (!llmResult.isSuccessful()) {
        throw new Error(
          `Failed to getTagTypesToFilter for conversation id: ${conversation.id}. Error message: ${llmResult.error}`,
        );
      }

      const responseData = llmResult.value.elementTypes;
      return this.filterTypesByConfidenceScore(responseData);
    } catch (error) {
      throw new Error(
        `Unexpected error during getTagTypesToFilter for conversation id: ${conversation.id}. Error message: ${error.messages}`,
      );
    }
  }
}
