import { Injectable, Logger } from '@nestjs/common';
import { CreativeElement } from './creative-elements.model';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { TagsTypeExtractor } from './tag-type-extractor.service';
import { VectorSearchTagsExtractor } from './vector-search-tag-extractor.service';
import { LLMModel } from '../model/llm-model.enum';

@Injectable()
export class TagsExtractor {
  private readonly logger = new Logger(TagsExtractor.name);

  constructor(
    private readonly tagsTypeExtractor: TagsTypeExtractor,
    private readonly vectorSearchTagsExtractor: VectorSearchTagsExtractor,
  ) {}

  async getTags(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<CreativeElement[]> {
    const typeTagsFuture =
      this.tagsTypeExtractor.getConversationTagTypesBreakdown(
        conversation,
        llmModel,
      );
    const vectorSearchTagsPromise = this.vectorSearchTagsExtractor.getTags(
      conversation,
      llmModel,
    );
    const typeTags = await typeTagsFuture;
    if (typeTags.length > 0) {
      return typeTags;
    } else {
      const vectorSearchTags = await vectorSearchTagsPromise;
      if (vectorSearchTags.length > 0) {
        return vectorSearchTags;
      } else {
        return [];
      }
    }
  }
}
