import { Injectable, Logger } from '@nestjs/common';
import {
  PineconeService,
  PineconeTagScore,
} from '../pinecone/pinecone.service';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { TagsTypeExtractor } from './tag-type-extractor.service';
import { ConversationSubjectExtractor } from './conversation-subject-extractor';
import { QuestionTagRelevancyService } from './question-tag-relevancy.service';
import { CreativeElement } from './creative-elements.model';
import {
  EMBEDDING_SERVICE,
  EmbeddingService,
} from '../embeddings/embedding.service';
import { Inject } from '@nestjs/common';
import { LLMModel } from '../model/llm-model.enum';

@Injectable()
export class VectorSearchTagsExtractor {
  private readonly logger = new Logger(VectorSearchTagsExtractor.name);
  private static readonly MINIMUM_SCORE = 0.75;

  constructor(
    @Inject(EMBEDDING_SERVICE)
    private readonly embeddingService: EmbeddingService,
    private readonly questionTagRelevancyService: QuestionTagRelevancyService,
    private readonly pineconeService: PineconeService,
    private readonly tagsTypeExtractor: TagsTypeExtractor,
    private readonly subjectExtractor: ConversationSubjectExtractor,
  ) {}

  async getTags(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<CreativeElement[]> {
    try {
      const promptSubjects = await this.subjectExtractor.getSubjects(
        conversation,
        llmModel,
      );
      if (promptSubjects.length === 0) {
        return [];
      }

      const filterByTypesArray =
        await this.tagsTypeExtractor.getTagTypesToFilter(
          conversation,
          llmModel,
        );

      const vectorSearchTags = await this.getTagsFromVectorSearch(
        conversation.organizationId,
        promptSubjects,
        filterByTypesArray,
      );
      if (vectorSearchTags.length === 0) {
        return [];
      }
      return this.questionTagRelevancyService.getQuestionMostRelevantTags(
        conversation,
        vectorSearchTags,
        llmModel,
      );
    } catch (e) {
      throw new Error(
        `Error during getSubjects for conversation id: ${conversation.id}. Error: ${e.message}`,
      );
    }
  }

  private async getTagsFromVectorSearch(
    organizationId: string,
    promptSubjects: string[],
    filterByTypes: string[],
  ): Promise<PineconeTagScore[]> {
    try {
      const numberOfTagsToSelectPerSubject =
        this.getNumberOfTagsToSelectPerSubject(promptSubjects);
      const tagResults = await Promise.all(
        promptSubjects.map((subject) =>
          this.getTagFromVectorSearch(
            organizationId,
            subject,
            filterByTypes,
            numberOfTagsToSelectPerSubject,
          ),
        ),
      );

      const allTags = this.getUniqueTags(tagResults);
      return this.filterTagsByMinimumScore(allTags);
    } catch (error) {
      throw new Error(
        `Error during getTagsFromVectorSearch. Error: ${error.message}`,
      );
    }
  }

  private getUniqueTags(tagResults: PineconeTagScore[][]): PineconeTagScore[] {
    const tagMap = new Map<string, PineconeTagScore>();

    tagResults.flat().forEach((tag) => {
      const key = `${tag.tag}-${tag.type}`;
      const existingTag = tagMap.get(key);
      // If the tag already exists, compare the scores and keep the one with the higher score
      if (!existingTag || tag.score > existingTag.score) {
        tagMap.set(key, tag);
      }
    });

    return Array.from(tagMap.values());
  }

  // The more tags we select the more probable it is that we may have LLM errors
  // in the following stages. And on the other side, if the number of tags
  // we select is too small, we may miss interesting tags with interesting insights.
  // I choose to select up to ~40 tags in a single call, this is an arbitrary
  // number that may change after some trial and error
  private getNumberOfTagsToSelectPerSubject(promptSubjects: string[]): number {
    switch (promptSubjects.length) {
      case 1:
        return 30;
      case 2:
        return 15;
      case 3:
        return 10;
      case 4:
        return 8;
      default:
        return 6;
    }
  }

  private filterTagsByMinimumScore(
    tags: PineconeTagScore[],
  ): PineconeTagScore[] {
    return tags.filter(
      (tag) => tag.score >= VectorSearchTagsExtractor.MINIMUM_SCORE,
    );
  }

  private async getTagFromVectorSearch(
    organizationId: string,
    subject: string,
    tagTypes: string[],
    topKtoSelect: number,
  ): Promise<PineconeTagScore[]> {
    const embeddings = await this.embeddingService.getEmbeddings(subject);
    return this.pineconeService.queryPinecone(
      organizationId,
      embeddings,
      tagTypes,
      topKtoSelect,
    );
  }
}
