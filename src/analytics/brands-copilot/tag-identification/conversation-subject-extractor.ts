import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { convertToLlmMessagesWithSystemPrompt } from '../llm/llm-utils';
import { LlmClientRouter } from '../llm/llm-client-router.service';
import { LLMModel } from '../model/llm-model.enum';

const extractSubjectsSystemPrompt = `
You are a Vidmob Creative Element Assistant. Your task is to analyze user questions and identify the primary creative elements of interest. 
Start by extracting the main subject(s) from the user's last question. If needed, Use previous messages in the conversation to understand last message context. 
These subjects will then be used for a similarity search against Vidmob's creative elements.

Provide your response in JSON format with a 'subjects' field that contains an array of strings.

Example:
Question: "Should I show tea or coffee in my ad?"
Subjects: ["tea", "coffee"]

Question: "Should I display the word 'tea' on screen?"
Subjects: ["tea"]

Question: "How do I add a celebrity to my ad?"
Subjects: ["celebrity"]
`;

const stringArraySchema = z.object({
  subjects: z.array(z.string()),
});

@Injectable()
export class ConversationSubjectExtractor {
  private readonly logger = new Logger(ConversationSubjectExtractor.name);

  constructor(private readonly llmProviderService: LlmClientRouter) {}

  async getSubjects(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<string[]> {
    try {
      const llmMessages = convertToLlmMessagesWithSystemPrompt(
        extractSubjectsSystemPrompt,
        conversation,
      );
      const llmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse(stringArraySchema, llmMessages, llmModel);

      if (!llmResult.isSuccessful()) {
        throw new Error(
          `Failed to getSubjects for conversation id: ${conversation.id}. Error message: ${llmResult.error}`,
        );
      }

      return llmResult.value.subjects;
    } catch (error) {
      throw new Error(
        `Error during getSubjects for conversation id: ${conversation.id}. Error: ${error.message}`,
      );
    }
  }
}
