import { Injectable, Logger } from '@nestjs/common';
import { z } from 'zod';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { convertToLlmMessagesWithSystemPrompt } from '../llm/llm-utils';
import { PineconeTagScore } from '../pinecone/pinecone.service';
import {
  CreativeElement,
  CreativeElementEntityType,
} from './creative-elements.model';
import { LlmClientRouter } from '../llm/llm-client-router.service';
import { LLMModel } from '../model/llm-model.enum';

const tagRelevancePromptTemplate = `
You are an AI system designed to help Vidmob generate creative performance insights based on user questions and a list of tag objects.

Input:
You will receive a conversation with a user's question about creative performance. 
If needed, Use previous messages in the conversation to understand last message context.

You are given a tag context: 
$tagsPlaceholder

Output:
For each tag, return the tag, type, and a new relevance score from 0 to 5, where:
5: Highly relevant to answering the user's question.
0: Not relevant to the question.

Task:
Identify which tags are most relevant to the user’s question.
If the question focuses on a specific tag, return 5 only for that tag.
If the question asks about multiple tags or a general category, assign good scores to all tags which are relevant to the question.

Examples:
Question: How does scenery affect performance? Tags: scenery, ball, indoor, outdoor, garden, child, forest Response: scenery-5, ball-0, indoor-5, outdoor-5, garden-5, forest-5, child-1 Explanation: All scenery-related tags are relevant to the question.
Question: Does the color blue perform better than green? Tags: blue, red, green, yellow, white, black Response: blue-5, green-5, red-0, yellow-0, white-0, black-0 Explanation: The question is a direct comparison of two specific colors, so only blue and green are relevant.
`;

const tagsScoreSchema = z.object({
  tags: z.array(
    z.object({
      value: z.string(),
      type: z.string(),
      score: z.number(),
    }),
  ),
});

@Injectable()
export class QuestionTagRelevancyService {
  private readonly logger = new Logger(QuestionTagRelevancyService.name);

  constructor(private readonly llmProviderService: LlmClientRouter) {}

  async getQuestionMostRelevantTags(
    conversation: CopilotConversation,
    tags: PineconeTagScore[],
    llmModel: LLMModel,
  ): Promise<CreativeElement[]> {
    try {
      const systemPrompt = this.getSystemPrompt(tags);
      const llmMessages = convertToLlmMessagesWithSystemPrompt(
        systemPrompt,
        conversation,
      );
      const llmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse(tagsScoreSchema, llmMessages, llmModel);

      if (!llmResult.isSuccessful()) {
        throw new Error(
          `Failed to getQuestionMostRelevantTags for conversation id: ${conversation.id}. Error message: ${llmResult.error}`,
        );
      }

      const responseData = this.filterTagsWithScore5(llmResult.value.tags);
      return responseData.map(this.createCreativeElement);
    } catch (error) {
      throw new Error(
        `Error during getSubjects for conversation id: ${conversation.id}. Error: ${error.message}`,
      );
    }
  }

  private filterTagsWithScore5(
    tags: { score: number; type: string; value: string }[],
  ) {
    return tags.filter((tag) => tag.score === 5);
  }

  private createCreativeElement(tag: {
    type: string;
    value: string;
  }): CreativeElement {
    return {
      entityType: CreativeElementEntityType.CREATIVE_ELEMENT,
      value: tag.value,
      type: tag.type,
      breakdownByChildElements: false,
    };
  }

  private getSystemPrompt(tags: PineconeTagScore[]): string {
    return tagRelevancePromptTemplate.replace(
      '$tagsPlaceholder',
      JSON.stringify(tags),
    );
  }
}
