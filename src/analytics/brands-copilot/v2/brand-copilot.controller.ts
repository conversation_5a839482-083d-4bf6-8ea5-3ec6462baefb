import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  Res,
  UnauthorizedException,
} from '@nestjs/common';
import { BrandCopilotService } from './brand-copilot.service';
import { BrandCopilotNextRequest } from '../model/request.model';
import { BrandCopilotNextResponse } from '../model/response.model';
import { BRAND_COPILOT_CONVERSATION_STARTERS } from '../llm/prompts';
import {
  Context,
  CopilotConversationMessage,
} from '../model/copilot-conversation.model';
import { UUID } from 'crypto';
import { BrandCopilotAuthorizationService } from '../authorization/brand-copilot-authorization.service';
import { AnalyticsCopilotConversationListItem } from '../../analytics-copilot/dto/analytics-copilot-conversation.dto';
import { UpdateMessageFeedbackDto } from '../../analytics-copilot/dto/update-message-feedback.dto';
import { PlatformInsights } from '../slides/slides.types';
import { validateQueryParam } from '../../analytics-copilot/utils/validate-query-param.utils';
import { LLMModel, LLM_MODEL_METADATA } from '../model/llm-model.enum';

@ApiTags('Copilot')
@ApiSecurity('Bearer Token')
@Controller({
  path: 'brand-copilot',
  version: '2',
})
export class BrandCopilotController {
  constructor(
    private readonly copilotService: BrandCopilotService,
    private readonly authorizationService: BrandCopilotAuthorizationService,
  ) {}

  @Post('next')
  async getLLMResponse(
    @Req() request: any,
    @Body()
    nextRequest: BrandCopilotNextRequest,
  ): Promise<BrandCopilotNextResponse> {
    // Temporary retrieval of authorization token, required since we are currently using a web call to get creatives data.
    // As soon as we switch to a normal API it will no longer be needed.
    const authToken = this.extractAuthToken(request);
    const context: Context =
      await this.authorizationService.validateOrganizationAndWorkspaceAccess(
        request.userId,
        nextRequest.scope.organizationId,
        nextRequest.scope.workspaces.map((w) => w.id),
      );

    return this.copilotService.getNextResponse(context, nextRequest, authToken);
  }

  @Get('conversations')
  async getConversationList(
    @Req() request: any,
    @Query('organizationId') organizationId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    validateQueryParam(organizationId, 'organizationId', true);
    return await this.copilotService.getConversationList(
      organizationId,
      request.userId,
    );
  }

  @Get('conversations/:chatId')
  async getConversationHistory(
    @Req() request: any,
    @Param('chatId') chatId: UUID,
    @Query('organizationId') organizationId: string,
  ): Promise<CopilotConversationMessage[]> {
    validateQueryParam(organizationId, 'organizationId', true);

    return await this.copilotService.getConversationMessages(
      organizationId,
      request.userId,
      chatId,
    );
  }

  @Post('conversations/:chatId/messages/:messageId/feedback')
  async updateMessageFeedback(
    @Req() request: any,
    @Param('chatId') chatId: UUID,
    @Param('messageId') messageId: UUID,
    @Body() requestDto: UpdateMessageFeedbackDto,
    @Query('organizationId') organizationId: string,
  ): Promise<boolean> {
    validateQueryParam(organizationId, 'organizationId', true);

    return await this.copilotService.updateConversationFeedback(
      organizationId,
      request.userId,
      chatId,
      messageId,
      requestDto,
    );
  }

  @Get('conversation-starters')
  async getConversationStarters(): Promise<string[]> {
    return BRAND_COPILOT_CONVERSATION_STARTERS;
  }

  private extractAuthToken(request: any): string {
    const authHeader = request.headers['authorization'];
    if (!authHeader) {
      throw new UnauthorizedException('Authorization header is missing');
    }
    return authHeader.split(' ')[1];
  }

  @Patch('conversations/:chatId/rename')
  async renameConversation(
    @Req() request: any,
    @Param('chatId') chatId: UUID,
    @Body('newTitle') newTitle: string,
    @Query('organizationId') organizationId: string,
  ): Promise<boolean> {
    validateQueryParam(organizationId, 'organizationId', true);
    return await this.copilotService.renameConversation(
      organizationId,
      request.userId,
      chatId,
      newTitle,
    );
  }

  @Delete('conversations/:chatId')
  async deleteConversation(
    @Req() request: any,
    @Param('chatId') chatId: UUID,
    @Query('organizationId') organizationId: string,
  ): Promise<boolean> {
    validateQueryParam(organizationId, 'organizationId', true);
    return await this.copilotService.deleteConversation(
      organizationId,
      request.userId,
      chatId,
    );
  }

  @Post('create-presentation')
  async createPresentation(
    @Req() req: any,
    @Body() body: PlatformInsights,
    @Res() res: any,
  ) {
    const insights = body.insights;
    const platform = body.platform;
    const filePath = await this.copilotService.createPresentation(
      insights,
      platform,
    );
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    );
    res.download(filePath, 'presentation.pptx', (err: Error) => {
      if (err) {
        console.error('Error sending file:', err);
        res.status(500).send('Error downloading file');
      }
    });
    return res;
  }

  /*
   * Return the list of available LLM models with the provider's modelId.
   */
  @Get('models')
  async getAvailableModels() {
    return Object.values(LLMModel).map((model) => ({
      model: model,
      modelId: LLM_MODEL_METADATA[model].modelId,
    }));
  }
}
