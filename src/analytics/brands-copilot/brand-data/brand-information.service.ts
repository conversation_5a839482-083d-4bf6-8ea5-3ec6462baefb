import { Injectable } from '@nestjs/common';
import { AdAccountService } from '../../../account-management/organization/ad-account/services/ad-account.service';
import { BrandService } from '../../../account-management/organization/brand/services/brand.service';

export interface BrandInformation {
  id: string;
  name: string;
  description?: string;
}

@Injectable()
export class BrandInformationService {
  constructor(
    private readonly adAccountService: AdAccountService,
    private readonly brandService: BrandService,
  ) {}

  async getByAdAccountId(
    organizationId: string,
    workspaceIds: number[],
    adAccountIds: string[],
  ): Promise<BrandInformation[]> {
    const platformAdAccountBrandsResponse =
      await this.adAccountService.getBrands(
        organizationId,
        workspaceIds,
        adAccountIds,
      );

    const brands: BrandInformation[] = platformAdAccountBrandsResponse.result;

    const getBrandInformationByNameResponse =
      await this.brandService.getBrandsByName(
        organizationId,
        brands.map((brand) => brand.name),
      );

    const brandWithDescription: BrandInformation[] = Object.values(
      getBrandInformationByNameResponse.result,
    );

    const brandNamesWithDescription = new Set(
      brandWithDescription.map((brand) => brand.name),
    );

    // Filter out brands that already have descriptions
    const brandsWithoutDescription = brands.filter(
      (brand) => !brandNamesWithDescription.has(brand.name),
    );

    return [...brandWithDescription, ...brandsWithoutDescription];
  }
}
