import { Injectable } from '@nestjs/common';
import { KPIService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { KpiInfo } from './kpi.model';
import { PLATFORMS_SUPPORT_CONVERSION_KPIS } from '../../../constants/platform.constants';
import { AnalyticsKpiResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/analyticsKpiResponseDto';

@Injectable()
export class CopilotKpiService {
  constructor(private readonly kpiService: KPIService) {}

  // FIXME: Inefficient implementation to use existing function, replace with dedicate get name by id call
  async getKpiInfo(
    platform: string,
    kpiIds: string[],
    adAccountIds: string[],
  ): Promise<{ [key: string]: KpiInfo }> {
    try {
      const kpiList = await this.getKPIs(platform, adAccountIds);
      return kpiList
        .filter((kpi) => kpiIds.includes(kpi.id))
        .reduce((acc: { [key: string]: KpiInfo }, kpi) => {
          acc[kpi.id] = {
            id: kpi.id,
            name: kpi.name,
            format: kpi.format,
            inverseHealth: kpi.inverseHealth,
            tagTimeRange: kpi.tagTimeRange,
            tagTimeRangeType: kpi.tagTimeRangeType,
          };
          return acc;
        }, {} as { [key: string]: KpiInfo });
    } catch (error) {
      throw new Error(`Failed to get KPI information. Error: ${error.message}`);
    }
  }

  private async getKPIs(
    platform: string,
    adAccountIds: string[],
  ): Promise<AnalyticsKpiResponseDto[]> {
    if (
      PLATFORMS_SUPPORT_CONVERSION_KPIS.map((platform) =>
        platform.toLowerCase(),
      ).includes(platform.toLowerCase()) &&
      adAccountIds?.length
    ) {
      const [platformKpis, customConversionKpis] = await Promise.all([
        this.kpiService.getPlatformKpisAsPromise(platform),
        this.kpiService.getCustomConversionKpisForAdAccountsAsPromise(
          platform,
          { adAccountIds: adAccountIds as string[] },
        ),
      ]);
      return [...platformKpis.result, ...customConversionKpis.result];
    }

    const platformKpis = await this.kpiService.getPlatformKpisAsPromise(
      platform,
    );
    return platformKpis.result;
  }
}
