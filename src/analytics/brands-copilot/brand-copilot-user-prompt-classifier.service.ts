import { Injectable, Logger } from '@nestjs/common';
import { BrandCopilotFlowType } from './flow-type/brand-copilot-flow-type';
import { z } from 'zod';
import { CopilotConversation } from './model/copilot-conversation.model';
import { CopilotRole } from './model/llm-constants.model';
import { convertToLlmMessagesWithSystemPrompt } from './llm/llm-utils';
import { LLMModel } from './model/llm-model.enum';
import { LlmClientRouter } from './llm/llm-client-router.service';

@Injectable()
export class BrandCopilotPromptClassifier {
  private readonly logger = new Logger(BrandCopilotPromptClassifier.name);
  constructor(private readonly llmProviderService: LlmClientRouter) {}

  private static readonly FlowToClassificationDescriptionMap: Record<
    BrandCopilotFlowType,
    string
  > = {
    [BrandCopilotFlowType.CREATIVE_ELEMENTS_QUESTION]:
      'Classify to this category if the messages involves questions about the use, impact, or comparison of creative elements (such as colors, people, objects, emotions, messaging, text, etc.) on a specific KPI, the overall performance of creatives, or general inquiries about what works best or what should be used.',
    [BrandCopilotFlowType.PERFORMANCE_QUESTION_WITHOUT_CREATIVE_ELEMENTS]:
      'Classify to this category if the messages involves open question about performance without mentioning specific creative elements. ' +
      'For example: What drive performance? Which elements should I use? What should I add to my adds?. ' +
      'Do not classify general non performance related questions such as: what is the meaning of life, who are the NBA champions, what is the goal of Vidmob? ' +
      'Do not classify question about specific specific types: which objects should I include? Which messaging works best? What colors drive performance? Those question should be classified as creative element questions ' +
      'Production type and Production level are separate type, should be classified as CREATIVE_ELEMENTS_QUESTION',
    [BrandCopilotFlowType.OTHER]:
      'Classify to this category for any other type of question',
  };

  private static readonly ClassificationSystemPromptPrefix = `
      You are the Vidmob chatbot assistant. Your task is to classify the user's last message based on the following rules.
      Instruction for handling context message:
        Context message is a message that provide additional brand/audience/performance/context data. It usually starts with 'context:'/'additional context:'/'ctx:'.
        If this is the first message in the conversation classify as OTHER.
        If this is not the first message, classify conversation based on previous message.
        If we have previous messages, that context just add more data for other flows, and you should classify this conversation based on the previous messages.
    `;

  private static readonly FullSystemPrompt =
    BrandCopilotPromptClassifier.buildFullSystemPrompt();

  async classify(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<BrandCopilotFlowType> {
    this.validateConversation(conversation);

    const classificationSchema = z.object({
      type: z.nativeEnum(BrandCopilotFlowType),
    });

    const llmMessages = convertToLlmMessagesWithSystemPrompt(
      BrandCopilotPromptClassifier.FullSystemPrompt,
      conversation,
    );

    try {
      const result = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse(classificationSchema, llmMessages, llmModel);

      if (result.isSuccessful()) {
        return result.value.type as BrandCopilotFlowType;
      } else {
        throw new Error(
          `Unexpected error during conversation classification. Classification result is not successful for Conversation id: ${conversation.id}. Error: ${result.error}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Unexpected error during conversation classification. Conversation id: ${conversation.id}. Error: ${error.message}`,
      );
      throw error;
    }
  }

  private validateConversation(conversation: CopilotConversation): void {
    if (!conversation.messages || conversation.messages.length === 0) {
      this.logger.error(
        `classify was called with an invalid conversation: No messages available. Conversation id: ${conversation.id}`,
      );
      throw new Error(
        'classify was called with an invalid conversation: No messages available',
      );
    }

    const lastMessage = conversation.messages[conversation.messages.length - 1];
    if (lastMessage.role !== CopilotRole.USER) {
      this.logger.error(
        `classify was called with an invalid conversation: Last message is not a user request. Conversation id: ${conversation.id}`,
      );
      throw new Error(
        'classify was called with an invalid conversation: Last message is not a user request',
      );
    }
  }

  private static buildFullSystemPrompt(): string {
    // Build the full system prompt by concatenating the prefix and descriptions
    const classificationsDescription = Object.entries(
      BrandCopilotPromptClassifier.FlowToClassificationDescriptionMap,
    )
      .map(([flowType, description]) => `- **${flowType}**: ${description}`)
      .join('\n');

    return `${BrandCopilotPromptClassifier.ClassificationSystemPromptPrefix}\n\n${classificationsDescription}`;
  }
}
