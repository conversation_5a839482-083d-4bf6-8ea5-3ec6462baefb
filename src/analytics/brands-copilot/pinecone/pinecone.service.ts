import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Pinecone } from '@pinecone-database/pinecone';
import { ConfigService } from '@nestjs/config';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';

type Metadata = {
  accounts: string[];
  networks: string[];
  type: string;
  valueLabel: string;
};

export type PineconeTagScore = {
  tag: string;
  type: string;
  score: number;
};

@Injectable()
export class PineconeService implements OnModuleInit {
  private readonly logger = new Logger(PineconeService.name);
  private client: Pinecone;
  private apiKey: string;
  private indexName: string;

  private vectorSearchIndexedOrganizations: Set<string>;
  private defaultOrganizationId: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly secretsConfigurationService: SecretsConfigurationService,
  ) {}

  async onModuleInit() {
    try {
      // const pineconeConfig = await this.secretsConfigurationService.get<any>(
      //   'pinecone',
      // );
      // this.apiKey = pineconeConfig?.api_key;
      this.apiKey = 'b3c5c1e9-2f9d-41e1-b24b-e1319095df07';
      if (!this.apiKey) {
        throw new Error('Pinecone API key not found in configuration');
      }
      this.client = new Pinecone({ apiKey: this.apiKey });

      const indexName = this.configService.get<string>('pinecone.indexName');
      if (!indexName) {
        throw new Error('Pinecone index name not found in configuration');
      }
      this.indexName = indexName;

      const orgs = this.configService.get<string[]>(
        'pinecone.vectorSearchIndexedOrganizations',
      );
      if (!orgs) {
        throw new Error(
          'Vector search indexed organizations not found in configuration',
        );
      }
      this.vectorSearchIndexedOrganizations = new Set(orgs);

      const defaultOrgId = this.configService.get<string>(
        'pinecone.defaultOrganizationId',
      );
      if (!defaultOrgId) {
        throw new Error('Default organization ID not found in configuration');
      }
      this.defaultOrganizationId = defaultOrgId;
    } catch (error) {
      this.logger.error(
        'Error initializing PineconeService:' + error?.message,
        error,
      );
      throw error;
    }
  }

  async queryPinecone(
    organizationId: string,
    embedding: number[],
    tagTypes: string[],
    topK: number,
  ): Promise<PineconeTagScore[]> {
    try {
      const index = this.client.index<Metadata>(this.indexName);

      const metadataFilter: any = {
        organizationId: this.getOrganizationIdForVectorSearch(organizationId),
      };
      if (tagTypes && tagTypes.length > 0) {
        metadataFilter.type = { $in: tagTypes };
      }

      const result = await index.query({
        topK: topK,
        vector: embedding,
        includeMetadata: true,
        filter: metadataFilter,
      });

      return result.matches.map((match) => ({
        tag: match.metadata?.valueLabel ?? 'not found',
        type: match.metadata?.type ?? 'unknown',
        score: match.score ?? 0,
      }));
    } catch (error) {
      this.logger.error('Error querying Pinecone:', error);
      throw error;
    }
  }

  private getOrganizationIdForVectorSearch(organizationId: string): string {
    return this.vectorSearchIndexedOrganizations.has(organizationId)
      ? organizationId
      : this.defaultOrganizationId;
  }
}
