import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { UUID } from 'crypto';
import { CurrencyDto } from '../../currency/dto/currency.dto';
import { LLMModel } from './llm-model.enum';

// Class it taken from element impact report, I didn't change the model to have
// a consistent model
export class KpiOptions {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  kpiIds: string[];

  @Type(() => CurrencyDto)
  @IsOptional()
  currencyObject?: CurrencyDto;
}

export class BrandCopilotScope {
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  @IsObject()
  @IsOptional()
  filters: { [key: string]: string[] } = {};

  @ValidateNested()
  @Type(() => KpiOptions)
  @IsNotEmpty()
  kpiOptions: KpiOptions;

  @IsNotEmpty()
  @IsString()
  platform: Platform;

  @IsString()
  @IsNotEmpty()
  organizationId: string;

  @IsArray()
  @IsNotEmpty()
  workspaces: Workspace[];

  @IsArray()
  @IsNotEmpty()
  adAccounts: AdAccount[];

  @IsOptional()
  advancedAdAccounts?: Record<string, any>;

  @IsOptional()
  advancedFilters?: { [key: string]: string[] };
}

export class BrandCopilotNextRequest {
  @IsNotEmpty()
  @IsString()
  userInput: string;

  @IsString()
  @IsOptional()
  chatId?: UUID;

  @ValidateNested()
  @Type(() => BrandCopilotScope)
  scope: BrandCopilotScope;

  @IsEnum(LLMModel)
  @IsOptional()
  model?: LLMModel;
}

export class AdAccount {
  @IsString()
  platformAccountName: string;

  @IsNotEmpty()
  @IsString()
  platformAccountId: string;
}

export class Workspace {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsString()
  industry: string | null;

  @IsBoolean()
  isEnterprise: boolean;

  @IsString()
  publicAccountTypeName: string;

  @IsString()
  accountTypeIdentifier: string;

  @IsBoolean()
  isPersonal: boolean;

  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  logoUrl?: string | null;

  @IsString()
  color: string;

  @IsNotEmpty()
  isFindMyTeamEnabled: boolean;

  @IsString()
  organizationId: string;

  @IsString()
  organizationName: string;
}
