export class Result<T> {
  private constructor(
    private readonly _data: T | null,
    private readonly _error: string | null,
  ) {}

  static success<T>(data: T): Result<T> {
    return new Result<T>(data, null);
  }

  static failure<T>(error: string): Result<T> {
    return new Result<T>(null, error);
  }

  isSuccessful(): boolean {
    return this._error === null;
  }

  isFailure(): boolean {
    return this._error !== null;
  }

  get value(): T {
    if (this.isFailure()) {
      throw new Error('Cannot access value on a failed result.');
    }
    return this._data as T;
  }

  get error(): string {
    if (this.isSuccessful()) {
      throw new Error('Cannot access error on a successful result.');
    }
    return this._error as string;
  }
}
