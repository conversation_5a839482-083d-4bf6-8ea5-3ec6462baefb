import { UUID } from 'crypto';
import { Document } from 'mongodb';
import { CopilotRole } from './llm-constants.model';
import { CopilotReport } from '../report/copilot-report.model';
import { Feedback } from './feedback.model';
import { InsightWithCreativesKeys } from '../report/insights.model';

export interface CopilotConversationMessage {
  id: UUID;
  role: CopilotRole;
  timestamp: number;
  text: string;
}

export interface CopilotConversationMessageV2 {
  id: UUID;
  role: CopilotRole;
  timestamp: Date;
  text: string;
}

export interface CopilotConversationMessageRequest
  extends CopilotConversationMessage {
  role: CopilotRole.USER;
  filters?: any;
}

export interface CopilotConversationMessageRequestV2
  extends CopilotConversationMessageV2 {
  role: CopilotRole.USER;
  filters?: any;
}

export interface CopilotConversationMessageResponse
  extends CopilotConversationMessage {
  role: CopilotRole.ASSISTANT;
  feedback?: Feedback;
  insights?: InsightWithCreativesKeys[];
  report?: CopilotReport;
}

export interface UserContext {
  id: number;
  firstName: string;
  lastName: string;
}

interface OrganizationContext {
  id: string;
  name: string;
}

export interface Context {
  user: UserContext;
  organization: OrganizationContext;
}

// Base interface for shared fields across versions
interface BaseCopilotConversation {
  id: UUID;
  userId: number;
  organizationId: string;
  messages: (
    | CopilotConversationMessageRequest
    | CopilotConversationMessageResponse
  )[];
  userContext?: Context;
  title: string;
  deleted?: boolean;
}

// Original version with number timestamps
export interface CopilotConversation extends BaseCopilotConversation {
  timestamp: number;
  lastInteractionDate: number;
}

// Version with Date timestamps
export interface CopilotConversationV2 extends BaseCopilotConversation {
  timestamp: Date;
  lastInteractionDate: Date;
}

// MongoDB document version, extending V2 with MongoDB's `_id` requirement
export interface CopilotConversationDocument
  extends CopilotConversationV2,
    Document {
  _id: UUID;
}
