import { IsNotEmpty, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { BrandCopilotInsight } from '../brand-copilot-next.dto';
import { Insight } from '../report/insights.model';

export class BrandCopilotNextResponse {
  @IsNotEmpty()
  @IsString()
  chatId: string;

  @IsNotEmpty()
  @IsString()
  answerPrompt: string;

  @IsNotEmpty()
  @IsString()
  messageId: string;

  @IsNotEmpty()
  @IsString()
  role: string;

  @IsString()
  model?: string;

  @ValidateNested({ each: true })
  @Type(() => BrandCopilotInsight)
  insights?: Insight[];
}
