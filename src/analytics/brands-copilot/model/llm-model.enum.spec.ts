import {
  LLMModel,
  LLM_MODEL_METADATA,
  LLMModelIdentifierMap,
  getProviderForModel,
  getModelId,
  isValidLLMModelValue,
  isValidLLMModelKey,
  getLLMModelFromKey,
} from './llm-model.enum';

describe('LLMModel Enum & Utilities', () => {
  // Ensure metadata covers all enum models
  it('should have metadata for every LLMModel value', () => {
    const missingModels = Object.values(LLMModel).filter(
      (model) => !(model in LLM_MODEL_METADATA),
    );
    if (missingModels.length > 0) {
      throw new Error(
        `Missing metadata entries for models: ${missingModels.join(', ')}. ` +
          `Please add an entry to LLM_MODEL_METADATA for each LLMModel enum value.`,
      );
    }
    expect(missingModels).toHaveLength(0);
  });

  describe('getProviderForModel', () => {
    it('should return correct providers for each model', () => {
      for (const model of Object.values(LLMModel)) {
        const metadata = LLM_MODEL_METADATA[model];
        const provider = getProviderForModel(model);
        expect(provider).toBe(metadata.provider);
      }
    });

    it('should throw descriptive error for invalid model', () => {
      const invalidModel = 'INVALID_MODEL' as LLMModel;
      expect(() => getProviderForModel(invalidModel)).toThrow(
        /Model not found: 'INVALID_MODEL'. Available models:/
      );
    });
  });

  describe('getModelId', () => {
    it('should return correct model IDs for each model', () => {
      for (const model of Object.values(LLMModel)) {
        const metadata = LLM_MODEL_METADATA[model];
        const modelId = getModelId(model);
        expect(modelId).toBe(metadata.modelId);
      }
    });

    it('should throw descriptive error for invalid model', () => {
      const invalidModel = 'INVALID_MODEL' as LLMModel;
      expect(() => getModelId(invalidModel)).toThrow(
        /Model not found: 'INVALID_MODEL'. Available models:/
      );
    });
  });

  describe('isValidLLMModelValue', () => {
    it('should return true for valid model strings', () => {
      for (const model of Object.values(LLMModel)) {
        expect(isValidLLMModelValue(model)).toBe(true);
      }
    });

    it('should return false for invalid strings', () => {
      expect(isValidLLMModelValue('')).toBe(false);
      expect(isValidLLMModelValue('gpt-unknown')).toBe(false);
      expect(isValidLLMModelValue('GPT-4O')).toBe(false);
    });
  });

  describe('LLMModelIdentifierMap & isValidLLMModelKey', () => {
    it('should map each enum key to its corresponding value', () => {
      for (const key of Object.keys(LLMModel)) {
        expect(LLMModelIdentifierMap).toHaveProperty(key);
        expect(LLMModelIdentifierMap[key]).toBe((LLMModel as any)[key]);
      }
    });

    it('isValidLLMModelKey should return true for valid enum keys', () => {
      for (const key of Object.keys(LLMModel)) {
        expect(isValidLLMModelKey(key)).toBe(true);
      }
    });

    it('isValidLLMModelKey should return false for invalid keys', () => {
      expect(isValidLLMModelKey('')).toBe(false);
      expect(isValidLLMModelKey('INVALID_KEY')).toBe(false);
      expect(isValidLLMModelKey('open_ai_4_o')).toBe(false);
    });
  });

  describe('getLLMModelFromKey', () => {
    it('should return correct model for valid keys', () => {
      for (const [key, value] of Object.entries(LLMModelIdentifierMap)) {
        const result = getLLMModelFromKey(key);
        expect(result).toBe(value);
      }
    });

    it('should return undefined for invalid keys', () => {
      expect(getLLMModelFromKey('')).toBeUndefined();
      expect(getLLMModelFromKey('UNKNOWN')).toBeUndefined();
    });
  });
});
