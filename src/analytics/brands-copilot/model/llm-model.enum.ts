import { <PERSON><PERSON><PERSON><PERSON><PERSON>rovider } from './llm-provider.enum';
import { LLMModelMetadata } from './llm-model-metadata.enum';

export enum LLMModel {
  OPEN_AI_4_O = 'OPEN_AI_4_O',
  OPEN_AI_4_1 = 'OPEN_AI_4_1',

  GEMINI_1_5_PRO = 'GEMINI_1_5_PRO',
  GEMINI_2_0_FLASH = 'GEMINI_2_0_FLASH',
  GEMINI_2_5_PRO_PREVIEW = 'GEMINI_2_5_PRO_PREVIEW',
  GEMINI_2_5_FLASH_PREVIEW = 'GEMINI_2_5_FLASH_PREVIEW',

  VERTEX_AI_GEMINI_2_5_PRO_PREVIEW = 'VERTEX_AI_GEMINI_2_5_PRO_PREVIEW',
  VERTEX_AI_GEMINI_2_5_FLASH_PREVIEW = 'VERTEX_AI_GEMINI_2_5_FLASH_PREVIEW',
  VERTEX_AI_GEMINI_2_0_FLASH = 'VERTEX_AI_GEMINI_2_0_FLASH',
  VERTEX_AI_GEMINI_2_0_FLASH_LITE = 'VERTEX_AI_GEMINI_2_0_FLASH_LITE',
  VERTEX_AI_CLAUDE_SONNET_4 = 'VERTEX_AI_CLAUDE_SONNET_4',
  VERTEX_AI_CLAUDE_3_7_SONNET = 'VERTEX_AI_CLAUDE_3_7_SONNET',

  CLAUDE_3_5_SONNET = 'CLAUDE_3_5_SONNET',
  CLAUDE_3_OPUS = 'CLAUDE_3_OPUS',
  CLAUDE_3_HAIKU = 'CLAUDE_3_HAIKU',
}

export const LLM_MODEL_METADATA: Record<LLMModel, LLMModelMetadata> = {
  // OPEN_AI models
  [LLMModel.OPEN_AI_4_O]: {
    provider: LlmClientProvider.OPEN_AI,
    modelId: 'gpt-4o',
    displayName: 'GPT-4o',
  },
  [LLMModel.OPEN_AI_4_1]: {
    provider: LlmClientProvider.OPEN_AI,
    modelId: 'gpt-4.1',
    displayName: 'GPT-4.1',
  },
  // GEMINI models
  [LLMModel.GEMINI_1_5_PRO]: {
    provider: LlmClientProvider.GEMINI,
    modelId: 'gemini-1.5-pro',
    displayName: 'Gemini 1.5 Pro',
  },
  [LLMModel.GEMINI_2_0_FLASH]: {
    provider: LlmClientProvider.GEMINI,
    modelId: 'gemini-2.0-flash',
    displayName: 'Gemini 2.0 Flash',
  },
  [LLMModel.GEMINI_2_5_PRO_PREVIEW]: {
    provider: LlmClientProvider.GEMINI,
    modelId: 'gemini-2.5-pro-preview-03-25',
    displayName: 'Gemini 2.5 Pro Preview',
  },
  [LLMModel.GEMINI_2_5_FLASH_PREVIEW]: {
    provider: LlmClientProvider.GEMINI,
    modelId: 'gemini-2.5-flash-preview-04-17',
    displayName: 'Gemini 2.5 Flash Preview',
  },
  // VERTEX_AI models
  [LLMModel.VERTEX_AI_GEMINI_2_5_PRO_PREVIEW]: {
    provider: LlmClientProvider.VERTEX_AI,
    modelId: 'gemini-2.5-pro-preview-05-06',
    displayName: 'Gemini 2.5 Pro Preview',
  },
  [LLMModel.VERTEX_AI_GEMINI_2_5_FLASH_PREVIEW]: {
    provider: LlmClientProvider.VERTEX_AI,
    modelId: 'gemini-2.5-flash-preview-04-17',
    displayName: 'Vertex Gemini 2.5 Flash Preview',
  },
  [LLMModel.VERTEX_AI_GEMINI_2_0_FLASH]: {
    provider: LlmClientProvider.VERTEX_AI,
    modelId: 'gemini-2.0-flash-001',
    displayName: 'Vertex Gemini 2.0 Flash',
  },
  [LLMModel.VERTEX_AI_GEMINI_2_0_FLASH_LITE]: {
    provider: LlmClientProvider.VERTEX_AI,
    modelId: 'gemini-2.0-flash-lite-001',
    displayName: 'Vertex Gemini 2.0 Flash Lite',
  },
  [LLMModel.VERTEX_AI_CLAUDE_SONNET_4]: {
    provider: LlmClientProvider.VERTEX_AI,
    modelId: 'claude-sonnet-4',
    displayName: 'Vertex Claude Sonnet 4',
  },
  [LLMModel.VERTEX_AI_CLAUDE_3_7_SONNET]: {
    provider: LlmClientProvider.VERTEX_AI,
    modelId: 'claude-3-7-sonnet',
    displayName: 'Vertex Claude 3.7 Sonnet',
  },
  // CLAUDE models
  [LLMModel.CLAUDE_3_5_SONNET]: {
    provider: LlmClientProvider.CLAUDE,
    modelId: 'claude-3-5-sonnet-latest',
    displayName: 'Claude 3.5 Sonnet',
  },
  [LLMModel.CLAUDE_3_OPUS]: {
    provider: LlmClientProvider.CLAUDE,
    modelId: 'claude-3-opus-latest',
    displayName: 'Claude 3 Opus',
  },
  [LLMModel.CLAUDE_3_HAIKU]: {
    provider: LlmClientProvider.CLAUDE,
    modelId: 'claude-3-haiku-20240307',
    displayName: 'Claude 3 Haiku',
  },
};

export function getProviderForModel(model: LLMModel): LlmClientProvider {
  const metadata = LLM_MODEL_METADATA[model];
  if (!metadata) {
    throw new Error(
      `Model not found: '${model}'. Available models: ${Object.keys(
        LLM_MODEL_METADATA,
      ).join(', ')}`,
    );
  }
  return metadata.provider;
}

export function getModelId(model: LLMModel): string {
  const metadata = LLM_MODEL_METADATA[model];
  if (!metadata) {
    throw new Error(
      `Model not found: '${model}'. Available models: ${Object.keys(
        LLM_MODEL_METADATA,
      ).join(', ')}`,
    );
  }
  return metadata.modelId;
}

export function isValidLLMModelValue(value: string): value is LLMModel {
  return Object.values(LLMModel).includes(value as LLMModel);
}

// Helper type to map enum keys back to enum values (for config validation/parsing)
export const LLMModelIdentifierMap: { [key: string]: LLMModel } =
  Object.entries(LLMModel).reduce((acc, [key, value]) => {
    acc[key] = value;
    return acc;
  }, {} as { [key: string]: LLMModel });

export function isValidLLMModelKey(key: string): key is keyof typeof LLMModel {
  return key in LLMModelIdentifierMap;
}

export function getLLMModelFromKey(key: string): LLMModel | undefined {
  if (isValidLLMModelKey(key)) {
    return LLMModelIdentifierMap[key];
  }
  return undefined;
}
