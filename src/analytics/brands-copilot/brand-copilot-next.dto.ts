import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { AdAccount, Workspace } from './model/request.model';
//FIXME: Split to multiple classes
//FIXME: Check existing classes, especially for media
export class InsightAttributes {
  @IsNotEmpty()
  @IsString()
  platform: string;

  @IsNotEmpty()
  @IsString()
  brand: string;

  @IsNotEmpty()
  startDate: string;

  @IsNotEmpty()
  endDate: string;

  @IsNotEmpty()
  @IsString()
  kpiName: string;

  @IsNotEmpty()
  @IsString()
  kpiId: string;
}

export class ElementsLift {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  type: string;

  @IsNumber()
  kpiValue: number;

  @IsNumber()
  kpiLift: number;

  @IsNotEmpty()
  @IsString()
  elementGroup: string;

  @IsArray()
  adVideosSample: string[];
}

export class Thumbnail {
  @IsNotEmpty()
  width: number;

  @IsNotEmpty()
  height: number;

  @IsNotEmpty()
  @IsString()
  url: string;
}

export class Streams {
  @IsNotEmpty()
  @IsString()
  wifi: string;

  @IsNotEmpty()
  @IsString()
  cellular: string;
}

export class BrandAnalysisData {
  @IsNumber()
  kpiAverage: number;

  @IsNotEmpty()
  @IsString()
  kpiName: string;

  @ValidateNested({ each: true })
  @Type(() => ElementsLift)
  elementsLift: ElementsLift[];
}

export class CreativeMedia {
  @IsNotEmpty()
  id: number;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  displayName: string;

  @IsNotEmpty()
  @IsString()
  downloadUrl: string;

  @IsNotEmpty()
  @IsString()
  // FIXME: Do we have an enum here?
  mediaType: string;

  @IsNotEmpty()
  @IsString()
  // FIXME: Do we have an enum here?
  fileType: string;

  @IsNotEmpty()
  @IsString()
  // FIXME: Do we have an enum here?
  processingState: string;

  @IsNotEmpty()
  width: number;

  @IsNotEmpty()
  height: number;

  @IsNotEmpty()
  duration: number;

  @ValidateNested({ each: true })
  @Type(() => Thumbnail)
  thumbnails: Thumbnail[];

  @ValidateNested()
  @Type(() => Streams)
  streams: Streams;

  @IsNotEmpty()
  @IsString()
  format: string;
}

export class CreativeKey {
  platformMediaId: string;
  workspaceIds: number[];
}

export class BrandCopilotInsight {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  findings: string;

  @IsNotEmpty()
  @IsString()
  recommendation: string;

  // Move to filter
  @ValidateNested()
  @Type(() => InsightAttributes)
  attributes: InsightAttributes;

  @ValidateNested()
  @Type(() => BrandAnalysisData)
  analysis: BrandAnalysisData;

  @ValidateNested({ each: true })
  @Type(() => CreativeKey)
  creativeExamples: CreativeKey[];

  @IsNotEmpty()
  version: number;
}

export class BrandCopilotNextResponse {
  @IsNotEmpty()
  @IsString()
  chatId: string;

  @IsNotEmpty()
  @IsString()
  answerPrompt: string;

  @IsNotEmpty()
  @IsString()
  messageId: string;

  @IsNotEmpty()
  @IsString()
  role: string;

  @IsString()
  model: string;

  @ValidateNested({ each: true })
  @Type(() => BrandCopilotInsight)
  insights?: BrandCopilotInsight[];
}

export class CompareReportRequestKpiOptions {
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  kpiIds: string[];
}

// Options class for report requests
export class CompareReportRequestOptions {
  @IsBoolean()
  @IsOptional()
  includeOnlyVidMobCreative = false;

  @IsString()
  @IsOptional()
  createdByVidmob?: string | undefined;
}

export class BrandCopilotScope {
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  @IsObject()
  @IsOptional()
  filters: { [key: string]: string[] } = {};

  @ValidateNested()
  @Type(() => CompareReportRequestKpiOptions)
  @IsOptional()
  kpiOptions: CompareReportRequestKpiOptions;

  @IsNotEmpty()
  @IsString()
  platform: Platform;

  @IsString()
  @IsOptional()
  organizationId?: string;

  @IsArray()
  @IsOptional()
  workspaces?: Workspace[];

  @IsArray()
  adAccounts: AdAccount[];
}

export class BrandCopilotNextPayload {
  @IsNotEmpty()
  @IsString()
  userInput: string;

  @IsString()
  @IsOptional()
  chatId?: string;

  @ValidateNested()
  @Type(() => BrandCopilotScope)
  scope: BrandCopilotScope;
}
