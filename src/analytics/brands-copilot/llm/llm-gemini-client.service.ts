// // src/brands-copilot/llm/gemini.service.ts
// import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
// // import {
// //   Content,
// //   GenerationConfig,
// //   GoogleGenerativeAI,
// //   HarmBlockThreshold,
// //   HarmCategory,
// // } from '@google/generative-ai';
// import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';
// import { ZodSchema } from 'zod';
// import { CopilotRole, LLMMessage } from '../model/llm-constants.model';
// import { Result } from '../model/Result';
// import { LlmClient } from './llm-client';
// import { getModelId, LLMModel } from '../model/llm-model.enum';
// import { zodToJsonSchema } from 'zod-to-json-schema';

// @Injectable()
// export class LlmGeminiClient extends LlmClient implements OnModuleInit {
//   private readonly logger = new Logger(LlmGeminiClient.name);
//   // private genAI!: GoogleGenerativeAI;

//   // private readonly safetySettings = [
//   //   {
//   //     category: HarmCategory.HARM_CATEGORY_HARASSMENT,
//   //     threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
//   //   },
//   //   {
//   //     category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
//   //     threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
//   //   },
//   //   {
//   //     category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
//   //     threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
//   //   },
//   //   {
//   //     category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
//   //     threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
//   //   },
//   // ];

//   // Base generation config, can be overridden or extended for specific calls
//   // private readonly baseGenerationConfig: GenerationConfig = { temperature: 0 };

//   constructor(
//     private readonly secretsConfigurationService: SecretsConfigurationService,
//   ) {
//     super();
//   }

//   /** Initialize Gemini client; throw on failure to fail fast at startup */
//   async onModuleInit(): Promise<void> {
//     // const config = await this.secretsConfigurationService.get<any>('google-generative-ai');
//     // if (!config?.apiKey) {
//     //   throw new Error('Gemini API key missing – aborting startup');
//     // }
//     // this.genAI = new GoogleGenerativeAI(config.apiKey);
//     // this.genAI = new GoogleGenerativeAI('aaa');
//     this.logger.log(
//       'Google Generative AI (Gemini) client initialized successfully.',
//     );
//   }

//   /**
//    * Converts LLMMessage to the message format expected by gemini
//    * Build history array and extract the final user prompt + optional system instruction */
//   private buildChat(messages: LLMMessage[]): {
//     history: Content[];
//     prompt: string;
//     systemInstruction?: string;
//   } {
//     const systemInstruction = messages.find(
//       (m) => m.role === CopilotRole.SYSTEM,
//     )?.content;
//     const chat = messages.filter((m) => m.role !== CopilotRole.SYSTEM);
//     const last = chat.pop();
//     const history = chat.map((m) => ({
//       role: m.role === CopilotRole.USER ? 'user' : 'model',
//       parts: [{ text: m.content }],
//     }));
//     return { history, prompt: last?.content ?? '', systemInstruction };
//   }

//   /** Central helper to get a configured Gemini model (without response schema/mime type) */
//   private getGeminiModel(model: LLMModel, systemInstruction?: string) {
//     // return this.genAI.getGenerativeModel({
//     //   model: getModelId(model),
//     //   safetySettings: this.safetySettings,
//     //   generationConfig: this.baseGenerationConfig, // Use base config
//     //   systemInstruction,
//     // });
//     return null; // Placeholder for actual model
//   }

//   async getTextResponse(
//     messages: LLMMessage[],
//     model: LLMModel,
//   ): Promise<Result<string>> {
//     const { history, prompt, systemInstruction } = this.buildChat(messages);
//     if (!prompt) {
//       return Result.failure('No user prompt provided');
//     }

//     try {
//       const gemini = this.getGeminiModel(model, systemInstruction);
//       const chat = gemini.startChat({ history });
//       const result = await chat.sendMessage(prompt);
//       return Result.success(result.response.text());
//     } catch (error: any) {
//       this.logger.error(
//         `Error getting text response from Gemini: ${error.message}`,
//         error.stack,
//       );
//       if (error.message.includes('SAFETY')) {
//         return Result.failure('Gemini request blocked due to safety settings.');
//       }
//       return Result.failure(
//         `Error getting text response from Gemini: ${error.message}`,
//       );
//     }
//   }

//   /** Get a JSON response from Gemini, validating against a Zod schema using structured output */
//   async getJsonResponse<T>(
//     schema: ZodSchema<T>,
//     messages: LLMMessage[],
//     model: LLMModel,
//   ): Promise<Result<T>> {
//     const { history, prompt, systemInstruction } = this.buildChat(messages);
//     if (!prompt) {
//       return Result.failure('No user prompt provided');
//     }

//     // Convert Zod schema to JSON schema format expected by Gemini API
//     // $refStrategy: 'none' is used to inline schemas, which is generally preferred
//     // for the responseSchema to keep it self-contained.
//     // The output of zodToJsonSchema is compatible with the Gemini API's Schema object.
//     const fullSchema = zodToJsonSchema(schema, {
//       $refStrategy: 'none', // Good choice for self-contained schemas
//       target: 'jsonSchema7', // Explicitly target JSON Schema 7 if needed, though it's the default
//     });

//     // Gemini API's responseSchema doesn't support top-level '$schema' or 'additionalProperties'.
//     // Create a mutable copy or use type assertion to modify.
//     const cleanedSchema: any = { ...fullSchema }; // Create a shallow copy to modify

//     if (cleanedSchema.$schema) {
//       delete cleanedSchema.$schema;
//     }
//     if (
//       cleanedSchema.type === 'object' &&
//       cleanedSchema.hasOwnProperty('additionalProperties')
//     ) {
//       delete cleanedSchema.additionalProperties;
//     }

//     // Define generation config for JSON output with the specified schema
//     // const jsonGenerationConfig: GenerationConfig = {
//     //   ...this.baseGenerationConfig,
//     //   responseMimeType: 'application/json',
//     //   responseSchema: cleanedSchema as any,
//     // };

//     try {
//       // Get the model configured for JSON output with the specific schema
//       const modelId = getModelId(model);
//       // const gemini = this.genAI.getGenerativeModel({
//       //   model: modelId,
//       //   safetySettings: this.safetySettings,
//       //   generationConfig: jsonGenerationConfig,
//       //   systemInstruction,
//       // });

//       // const chat = gemini.startChat({ history });

//       // Send the user message. The model is configured to respond with JSON based on the schema.
//       // const result = await chat.sendMessage(prompt);

//       // The response.text() should contain the raw JSON string directly due to responseMimeType
//       // const rawJsonResponse = result.response.text().trim();
//       const rawJsonResponse = '[]'; // Placeholder for actual response

//       // Parse the JSON response string
//       let data: T;
//       try {
//         data = JSON.parse(rawJsonResponse);
//       } catch (parseError: any) {
//         this.logger.error(
//           `Failed to parse Gemini JSON response (API configured for JSON output): ${parseError.message}`,
//           { raw: rawJsonResponse },
//         );
//         return Result.failure(
//           `Failed to parse Gemini JSON response: ${parseError.message}`,
//         );
//       }

//       // Validate the parsed data against the original Zod schema as a safeguard.
//       // Although the API is configured for a schema, this adds an extra layer
//       // of validation and provides detailed Zod error messages.
//       const validation = schema.safeParse(data);
//       if (validation.success) {
//         return Result.success(validation.data);
//       } else {
//         // Log detailed validation errors if the parsed JSON doesn't match the Zod schema
//         this.logger.error(
//           `Gemini JSON validation failed after successful API call: ${validation.error.message}`,
//           { errors: validation.error.errors, raw: rawJsonResponse },
//         );
//         return Result.failure(
//           `Gemini response JSON validation failed: ${validation.error.issues
//             .map((i) => `${i.path.join('.')}: ${i.message}`)
//             .join(', ')}`,
//         );
//       }
//     } catch (error: any) {
//       this.logger.error(
//         `Error during Gemini JSON generation with schema: ${error.message}`,
//         error.stack,
//       );
//       if (error.message.includes('SAFETY')) {
//         return Result.failure(
//           'Gemini JSON request blocked due to safety settings.',
//         );
//       }
//       // Catch potential API errors related to schema or mime type configuration
//       if (
//         error.message.includes('responseSchema') ||
//         error.message.includes('responseMimeType') ||
//         error.message.includes('GenerationConfig')
//       ) {
//         this.logger.error(
//           `Gemini API GenerationConfig error: ${error.message}`,
//           error.stack,
//         );
//         return Result.failure(
//           `Gemini API configuration error: ${error.message}`,
//         );
//       }
//       // Catch JSON parsing errors that might not be caught by the inner try/catch if error structure is different
//       if (
//         error.message.includes('JSON Parse error') ||
//         error.message.includes('Unexpected token')
//       ) {
//         this.logger.error(
//           `Gemini response JSON parsing error: ${error.message}`,
//           error.stack,
//         );
//         return Result.failure(
//           `Gemini response JSON parsing error: ${error.message}`,
//         );
//       }

//       return Result.failure(
//         `Error getting JSON response from Gemini: ${error.message}`,
//       );
//     }
//   }
// }
