export const vidmobTypeLabelExplanationPrompt = `
Vidmob Creative Element Types:
- Color: Refers to dominant colors or other attributes like contrast (low, medium, high), temperature (cool, neutral, warm), or vibrancy (least colorful, somewhat colorful, most colorful).
- Celebrity: Indicates the presence of a celebrity in the creative.
- Messaging: Identifies on-screen text, which can be general messaging or a call to action (CTA).
- Object: Labels identified by <PERSON><PERSON><PERSON><PERSON>'s computer vision within the creative, including people, animals, scenery, or objects.
- Branding: Identifies the presence of logos, with the brand name specified in the "value" field as LOGO: $brandName
- Gaze and emotions - Highlights emotions detected by Vidmob's computer vision (e.g., happy, sad, angry) or the gaze direction of the main subject in the creative (e.g., right, left, up-right).
- Production Level - Indicates the production quality of the advertisement. Element values are Commercial (high) or UGC (low)
- Production Type - Specifies the production style of the AD. Element values: Live-Action, Animation, CGI 
`;

export const vidmobTypesExplanationPrompt = `
Below are the available creative element types along with brief explanations::
- COLOR:DOMINANT_COLORS:CATEGORY: Refers to the dominant colors in the creative, for example, gray, brown, dark red.
- COLOR:TEXT_CONTRAST:CATEGORY: Describes the contrast between text and its background, for example, low, medium, high contrast.
- COLOR:TEMPERATURE:CATEGORY: Refers to the warmth or coolness of the colors, for example, warm, neutral, cool.
- COLOR:VIBRANCY:CATEGORY: Measures the color intensity, for example, least colorful, most colorful.
- COLOR:CONTRAST:CATEGORY: Describes the contrast between different colors, for example, low, medium, high contrast.
- CELEBRITY:NAME: Detects the presence of real life human celebrities in the creative. for example: Lenny Kravitz, Dua Lipa, Zendaya.
- CTA:BY_LINE, APERTURE:MESSAGING: Refers to specific CTA text, for example, shop now, buy, sign up. CTA specific text is present in CTA:BY_LINE, CTA types (engagement, information, conversion) are present APERTURE:MESSAGING 
- APERTURE:MESSAGING: Refers to messaging benefit type (functional, emotional, educational, promotional, pricing) and CTA types (engagement, information, conversion)
- APERTURE:PRODUCTION_LEVEL: Indicates the production quality of the advertisement. Element values are Commercial (high) or UGC (low)
- APERTURE:PRODUCTION_TYPE: Specifies the production type of the AD. Element values: Live-Action, Animation, CGI
- TEXT:WORD_EMPHASIS: Refers to the most emphasized words in text, for example, brand names or key terms like 'biotherm'. Also the suitable type to answer regarding general 'messaging' questions like: What messaging drove the best performance?
- LABEL: Labels that are identified by AI, for example: person, perfume, face, lipstick, objects, outdoors, forest, night, characters. generic items that recognized by Vidmob's computer vision. Everything that was find by computer vision and doesn't have its own type, is marked by LABEL.
- LOGO: Detects branding elements in the creative, for example, L'Oréal, Maybelline.
- FACE:GAZE_DIRECTION: Indicates the direction a face is looking in the creative, for example, middle, left, right.
- FACE:EMOTION: Refers to detected emotions in faces, for example, calm, happy, surprised, angry.
- FACE:SMILE: Indicates whether a smile is detected on a face, for example, true.
`;

export const BRAND_COPILOT_CONVERSATION_STARTERS = [
  'Which emotions should I highlight in my creatives?',
  'How do different colors impact campaign performance?',
  'What messaging drove the best performance?',
  'What drives performance?',
];
