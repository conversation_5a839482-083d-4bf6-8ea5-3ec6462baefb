import {
  CopilotConversation,
  CopilotConversationMessageRequest,
  CopilotConversationMessageResponse,
} from '../model/copilot-conversation.model';
import { CopilotRole, LLMMessage } from '../model/llm-constants.model';

export function createSystemPromptMessage(systemPrompt: string): LLMMessage {
  return {
    role: CopilotRole.SYSTEM,
    content: systemPrompt,
  } as LLMMessage;
}

export function createUserPromptMessage(userPrompt: string): LLMMessage {
  return {
    role: CopilotRole.USER,
    content: userPrompt,
  } as LLMMessage;
}

export function createAssistantPromptMessage(userPrompt: string): LLMMessage {
  return {
    role: CopilotRole.ASSISTANT,
    content: userPrompt,
  } as LLMMessage;
}

const convertToLLMMessage = (
  message:
    | CopilotConversationMessageRequest
    | CopilotConversationMessageResponse,
): LLMMessage => {
  let content = message.text;

  if ('insights' in message && message.insights) {
    content += `\nInsights:\n${JSON.stringify(message.insights)}`;
  }

  return {
    role: message.role,
    content: content || '',
  };
};

export const convertToLlmMessagesWithSystemPrompt = (
  systemPrompt: string,
  conversation: CopilotConversation,
  lastMessagesCount = 10,
): LLMMessage[] => {
  const systemPromptMessage = createSystemPromptMessage(systemPrompt);
  const llmMessages = convertToLlmMessages(conversation, lastMessagesCount);
  return [systemPromptMessage, ...llmMessages];
};

export const convertToLlmMessages = (
  conversation: CopilotConversation,
  lastMessagesCount = 10,
): LLMMessage[] => {
  const conversationMessages = conversation.messages.slice(-lastMessagesCount);
  return conversationMessages.map(convertToLLMMessage);
};
