// src/brands-copilot/llm/llm-provider.service.ts
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  getProviderForModel,
  isValidLLMModelValue,
  LLM_MODEL_METADATA,
  LLMModel,
} from '../model/llm-model.enum';
import { LlmClient } from './llm-client';
import { LlmClientProvider } from '../model/llm-provider.enum';
import { LlmOpenAiClient } from './llm-open-ai-client.service';
// import { LlmGeminiClient } from './llm-gemini-client.service';
import { LlmVertexAiClient } from './llm-vertex-ai-client.service';

@Injectable()
export class LlmClientRouter {
  private readonly logger = new Logger(LlmClientRouter.name);
  private readonly _defaultModel: LLMModel;

  // Map to store provider services
  private readonly providerServiceMap: Map<LlmClientProvider, LlmClient>;

  constructor(
    private readonly configService: ConfigService,
    private readonly openAiService: LlmOpenAiClient,
    // private readonly geminiService: LlmGeminiClient,
    private readonly vertexAiService: LlmVertexAiClient,
  ) {
    // Initialize provider service map
    this.providerServiceMap = new Map<LlmClientProvider, LlmClient>();
    this.providerServiceMap.set(LlmClientProvider.OPEN_AI, this.openAiService);
    // this.providerServiceMap.set(LlmClientProvider.GEMINI, this.geminiService);
    this.providerServiceMap.set(LlmClientProvider.VERTEX_AI, this.vertexAiService);

    // Determine default model from config
    const defaultModelFromConfig =
      this.configService.get<string>('llm.defaultModel');
    let resolvedDefaultModel: LLMModel | undefined;

    if (defaultModelFromConfig) {
      if (isValidLLMModelValue(defaultModelFromConfig)) {
        resolvedDefaultModel = defaultModelFromConfig;
      }
    }

    if (!resolvedDefaultModel) {
      this.logger.warn(
        `Invalid or missing 'llm.defaultModel' in configuration: "${defaultModelFromConfig}". Falling back to ${LLMModel.OPEN_AI_4_1}.`,
      );
      this._defaultModel = LLMModel.OPEN_AI_4_1;
    } else {
      this._defaultModel = resolvedDefaultModel;
      this.logger.log(
        `Default LLM Model configured to: ${this._defaultModel} (${
          LLM_MODEL_METADATA[this._defaultModel].displayName
        })`,
      );
    }
  }

  public getLlmClient(model?: LLMModel): LlmClient {
    const targetModel = model ?? this._defaultModel;
    const provider = getProviderForModel(targetModel);
    const service = this.providerServiceMap.get(provider);

    if (!service) {
      throw new Error(
        `Provider ${provider} required for model ${targetModel} is not available.`,
      );
    }

    return service;
  }

  public getDefaultModel(): LLMModel {
    return this._defaultModel;
  }
}
