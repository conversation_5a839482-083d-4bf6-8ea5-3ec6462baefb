import { ZodSchema } from 'zod';
import { LLMMessage, CopilotRole } from '../model/llm-constants.model';
import { Result } from '../model/Result';
import { LLMModel } from '../model/llm-model.enum';

export abstract class LlmClient {
  abstract getTextResponse(
    messages: LLMMessage[],
    model: LLMModel,
  ): Promise<Result<string>>;

  abstract getJsonResponse<T>(
    schema: ZodSchema<T>,
    messages: LLMMessage[],
    model: LLMModel,
  ): Promise<Result<T>>;

  async getUserPromptTextResponse(
    userPrompt: string,
    model: LLMModel,
  ): Promise<Result<string>> {
    const messages: LLMMessage[] = [
      {
        role: CopilotRole.USER,
        content: userPrompt,
      },
    ];
    return this.getTextResponse(messages, model);
  }

  async getSystemUserPromptTextResponse(
    systemPrompt: string,
    userPrompt: string,
    model: LLMModel,
  ): Promise<Result<string>> {
    const messages: LLMMessage[] = [
      {
        role: CopilotRole.SYSTEM,
        content: systemPrompt,
      },
      {
        role: CopilotRole.USER,
        content: userPrompt,
      },
    ];
    return this.getTextResponse(messages, model);
  }

  async getUserPromptJsonResponse<T>(
    schema: ZodSchema<T>,
    userPrompt: string,
    model: LLMModel,
  ): Promise<Result<T>> {
    const messages: LLMMessage[] = [
      {
        role: CopilotRole.USER,
        content: userPrompt,
      },
    ];
    return this.getJsonResponse(schema, messages, model);
  }

  async getSystemUserPromptJsonResponse<T>(
    schema: ZodSchema<T>,
    systemPrompt: string,
    userPrompt: string,
    model: LLMModel,
  ): Promise<Result<T>> {
    const messages: LLMMessage[] = [
      {
        role: CopilotRole.SYSTEM,
        content: systemPrompt,
      },
      {
        role: CopilotRole.USER,
        content: userPrompt,
      },
    ];
    return this.getJsonResponse(schema, messages, model);
  }
}
