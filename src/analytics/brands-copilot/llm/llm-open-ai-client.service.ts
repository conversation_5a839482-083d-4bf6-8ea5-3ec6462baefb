import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import OpenAI from 'openai';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';
import { ZodSchema } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';
import { LLMMessage } from '../model/llm-constants.model';
import { Result } from '../model/Result';
import { LlmClient } from './llm-client';
import { ParsedChatCompletion } from 'openai/resources/beta/chat/completions';
import { ChatCompletionCreateParamsNonStreaming } from 'openai/src/resources/chat/completions';
import { getModelId, LLMModel } from '../model/llm-model.enum';

@Injectable()
export class LlmOpenAiClient extends LlmClient implements OnModuleInit {
  private readonly logger = new Logger(LlmOpenAiClient.name);
  private client: OpenAI;

  constructor(
    private readonly secretsConfigurationService: SecretsConfigurationService,
  ) {
    super();
  }

  /**
   * Method called once the host module has been initialized.
   * Initializes the OpenAI client using a secret configuration service.
   */
  async onModuleInit(): Promise<void> {
    try {
      const config = await this.secretsConfigurationService.get<any>('openai');
      this.client = new OpenAI({
        apiKey: config?.apiKey,
      });
      this.logger.log(
        'OpenAI client initialized successfully for Brands Copilot.',
      );
    } catch (e) {
      this.logger.error(
        `Error while trying to initialize OpenAI client for Brands Copilot: ${e.message}`,
      );
    }
  }

  async getTextResponse(
    messages: LLMMessage[],
    model: LLMModel,
  ): Promise<Result<string>> {
    try {
      if (!this.client) {
        return Result.failure('Unexpected error. Client is not initialized');
      }

      // Construct the request payload for plain text response
      const openAiRequest = this.createOpenAiRequestPlain(messages, model);
      const response = await this.client.chat.completions.create(openAiRequest);

      return this.parsePlainResponseToResultObject(response);
    } catch (e) {
      this.logger.error(
        `Error while trying to get plain text response from OpenAI: ${e.message}`,
      );
      return Result.failure(
        'Error while trying to get plain text response from OpenAI.',
      );
    }
  }

  private parsePlainResponseToResultObject(response: any): Result<string> {
    try {
      if (!response || !response.choices || response.choices.length === 0) {
        return Result.failure('Invalid response from OpenAI');
      }

      // Extracting the text from the first choice
      const textResponse = response.choices[0].message?.content;
      if (!textResponse) {
        return Result.failure('No text response found in OpenAI response');
      }

      return Result.success(textResponse);
    } catch (e) {
      this.logger.error(`Error while parsing OpenAI response: ${e.message}`);
      return Result.failure('Error while parsing OpenAI response');
    }
  }

  private createOpenAiRequestPlain(
    messages: LLMMessage[],
    model: LLMModel,
  ): ChatCompletionCreateParamsNonStreaming {
    return {
      model: getModelId(model),
      messages,
      temperature: 0,
    };
  }

  async getJsonResponse<T>(
    schema: ZodSchema<T>,
    messages: LLMMessage[],
    model: LLMModel,
  ): Promise<Result<T>> {
    try {
      // FIXME: Feels bad, check if we can handle it on constructor level
      if (!this.client) {
        return Result.failure('Unexpected error. Client is not initialized');
      }

      // Construct the request payload using the Zod schema
      const openAiRequest = this.createOpenAiRequest(messages, schema, model);
      const response = await this.client.beta.chat.completions.parse(
        openAiRequest,
      );

      return this.parseResponseToResultObject(response);
    } catch (e) {
      return Result.failure(
        `Error while trying to get response from OpenAI. Error: ${e.message}`,
      );
    }
  }

  private parseResponseToResultObject<T>(
    openAiResponse: ParsedChatCompletion<T>,
  ): Result<T> {
    const responseMessage = openAiResponse.choices[0].message;
    if (responseMessage.parsed) {
      return Result.success(responseMessage.parsed);
    } else if (responseMessage.refusal) {
      this.logger.error(
        `OpenAi refused getMessagesResponseWithSchema with: ${responseMessage.refusal}`,
      );
      return Result.failure(
        'OpenAi refused getMessagesResponseWithSchema with:' +
          responseMessage.refusal,
      );
    } else {
      this.logger.error(
        'Unexpected state in getMessagesResponseWithSchema, OpenAi result returned without parsed data or refusal message',
      );
      return Result.failure(
        'Unexpected state in getMessagesResponseWithSchema, OpenAi result returned without parsed data or refusal message',
      );
    }
  }

  private createOpenAiRequest<T>(
    messages: LLMMessage[],
    schema: ZodSchema<T>,
    model: LLMModel,
  ) {
    return {
      model: getModelId(model),
      messages,
      response_format: zodResponseFormat(schema, 'response'),
      temperature: 0,
    };
  }
}
