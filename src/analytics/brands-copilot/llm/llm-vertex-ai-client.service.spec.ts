// Set required process.env variables to ensure config is found by the service
process.env['VERTEXAI_PROJECTID'] = 'test-project';
process.env['VERTEXAI_LOCATION'] = 'test-location';
process.env['VERTEXAI_AWSREGION'] = 'us-east-1';
process.env['VERTEXAI_AUDIENCE'] = 'test-audience';
process.env['VERTEXAI_SUBJECT_TOKEN_TYPE'] = 'urn:ietf:params:aws:token-type:aws4_request';

process.env['VERTEXAI_SERVICE_ACCOUNT_IMPERSONATION_URL'] = 'https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken';

// Patch ConfigService prototype before all imports
import { ConfigService } from '@nestjs/config';
// Patch ConfigService prototype after import
(ConfigService.prototype as any).get = function (key: string) {
  switch (key) {
    case 'vertexai.projectId':
      return 'test-project';
    case 'vertexai.location':
      return 'test-location';
    case 'vertexai.awsRegion':
      return 'us-east-1';
    case 'AWS_REGION':
      return 'us-east-1';
    case 'vertexai.audience':
      return 'test-audience';
    case 'vertexai.subject_token_type':
      return 'urn:ietf:params:aws:token-type:aws4_request';
    case 'vertexai.service_account_impersonation_url':
      return 'https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken';
    default:
      return undefined;
  }
};

import { Test, TestingModule } from '@nestjs/testing';
import { LlmVertexAiClient } from './llm-vertex-ai-client.service';
import { CopilotRole } from '../model/llm-constants.model';
import { LLMModel } from '../model/llm-model.enum';
import { z } from 'zod';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';

// Mock VertexAI module
jest.mock('@google-cloud/vertexai', () => {
  const mockResponseFactory = (isJson: boolean) => {
    const candidate = {
      content: {
        parts: [
          {
            text: isJson ? '{"mockKey":"mockValue"}' : 'mock response text',
          },
        ],
        role: 'model',
      },
      finishReason: 'STOP',
      safetyRatings: [],
    };
    const item = { candidates: [candidate] };
    return {
      stream: [item],
      response: item, // Add this so await result.response works
    };
  };

  return {
    VertexAI: jest.fn().mockImplementation(() => ({
      preview: {
        getGenerativeModel: jest.fn().mockImplementation(() => ({
          startChat: jest.fn().mockImplementation(() => ({
            sendMessageStream: jest.fn().mockImplementation(() => {
              // Always treat as JSON request for getJsonResponse tests
              // This ensures all getJsonResponse tests get the expected mock JSON
              const stack = new Error().stack || '';
              const isJsonRequest = stack.includes('getJsonResponse');
              return Promise.resolve(mockResponseFactory(isJsonRequest));
            }),
          })),
        })),
      },
    })),
    HarmCategory: {
      HARM_CATEGORY_HATE_SPEECH: 'HARM_CATEGORY_HATE_SPEECH',
      HARM_CATEGORY_DANGEROUS_CONTENT: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      HARM_CATEGORY_SEXUALLY_EXPLICIT: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      HARM_CATEGORY_HARASSMENT: 'HARM_CATEGORY_HARASSMENT',
    },
    HarmBlockThreshold: {
      BLOCK_ONLY_HIGH: 'BLOCK_ONLY_HIGH',
    },
  };
});

describe('LlmVertexAiClient', () => {
  let service: LlmVertexAiClient;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LlmVertexAiClient,
        ConfigService,
        {
          provide: SecretsConfigurationService,
          useValue: {
            getSecret: jest.fn().mockResolvedValue('mock-secret'), // Mock implementation
          },
        },
      ],
    }).compile();

    service = module.get<LlmVertexAiClient>(LlmVertexAiClient);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('onModuleInit', () => {
    it('should initialize successfully with valid project and location', async () => {
      // Act
      await service.onModuleInit();

      // Assert
      expect(service).toBeDefined();
    });

    it('should throw error when project info is missing', async () => {
      // Arrange
      jest.spyOn(configService, 'get').mockReturnValue(undefined);

      // Act & Assert
      try {
        await service.onModuleInit();
        fail('Should have thrown an error');
      } catch (e) {
        expect(e.message).toBe(
          'Vertex AI project ID and location must be configured',
        );
      }
    });
  });

  describe('getTextResponse', () => {
    beforeEach(async () => {
      // Setup successful initialization
      await service.onModuleInit();
    });

    it('should return success result with response text', async () => {
      // Arrange
      const messages = [{ role: CopilotRole.USER, content: 'test message' }];

      // Act
      const result = await service.getTextResponse(
        messages,
        LLMModel.VERTEX_AI_GEMINI_2_0_FLASH,
      );

      // Assert
      expect(result.isSuccessful()).toBe(true);
      expect(result.value).toBe('mock response text');
    });

    it('should return failure result when no messages provided', async () => {
      // Act
      const result = await service.getTextResponse(
        [],
        LLMModel.VERTEX_AI_GEMINI_2_0_FLASH,
      );

      // Assert
      expect(result.isSuccessful()).toBe(false);
      expect(result.error).toBe('No messages provided');
    });
  });

  describe('getJsonResponse', () => {
    const testSchema = z.object({
      mockKey: z.string(),
    });

    beforeEach(async () => {
      // Setup successful initialization
      await service.onModuleInit();
    });

    it('should return success result with parsed JSON', async () => {
      // Arrange
      const messages = [
        {
          role: CopilotRole.USER,
          content: 'test message with json',
        },
      ];

      // Act
      const result = await service.getJsonResponse(
        testSchema,
        messages,
        LLMModel.VERTEX_AI_GEMINI_2_0_FLASH,
      );

      // Assert
      expect(result.isSuccessful()).toBe(true);
      expect(result.value).toEqual({ mockKey: 'mockValue' });
    });

    it('should return failure result when no messages provided', async () => {
      // Act
      const result = await service.getJsonResponse(
        testSchema,
        [],
        LLMModel.VERTEX_AI_GEMINI_2_0_FLASH,
      );

      // Assert
      expect(result.isSuccessful()).toBe(false);
      expect(result.error).toBe('No messages provided');
    });

    it('should handle system prompts correctly', async () => {
      // Arrange
      const messages = [
        { role: CopilotRole.SYSTEM, content: 'system instruction' },
        { role: CopilotRole.USER, content: 'test message' },
      ];

      // Act
      const result = await service.getJsonResponse(
        testSchema,
        messages,
        LLMModel.VERTEX_AI_GEMINI_2_0_FLASH,
      );

      // Assert
      expect(result.isSuccessful()).toBe(true);
      expect(result.value).toEqual({ mockKey: 'mockValue' });
    });
  });
});
