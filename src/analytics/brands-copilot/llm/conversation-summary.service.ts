import { Injectable } from '@nestjs/common';
import { z } from 'zod';
import { CopilotConversation } from '../model/copilot-conversation.model';
import { convertToLlmMessagesWithSystemPrompt } from './llm-utils';
import { LlmClientRouter } from './llm-client-router.service';
import { LLMModel } from '../model/llm-model.enum';

const summariseConversationPrompt = `
You are a helper agent for an AI insights generator for brand managers.
Given the user conversation, your task is to summarize the current context of the conversation.
You need to output the latest user question and all of the instructions that are relevant to this conversation.
If the user supplies additional instructions or information about brand, campaign, KPI, or analysis, make sure to include it.
Your output will be provided to the insights generation step that will require this data to produce relevant insights.
`;

const summarySchema = z.object({
  summary: z.string(),
});

type SummarySchemaType = z.infer<typeof summarySchema>;

@Injectable()
export class ConversationCurrentContextSummaryService {
  constructor(private readonly llmProviderService: LlmClientRouter) {}

  async getCurrentContextSummary(
    conversation: CopilotConversation,
    llmModel: LLMModel,
  ): Promise<string> {
    try {
      const llmMessages = convertToLlmMessagesWithSystemPrompt(
        summariseConversationPrompt,
        conversation,
      );

      const llmResult = await this.llmProviderService
        .getLlmClient(llmModel)
        .getJsonResponse<SummarySchemaType>(
          summarySchema,
          llmMessages,
          llmModel,
        );

      return llmResult.value.summary;
    } catch (error: unknown) {
      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      throw new Error(
        `Failed to summarize conversation id: ${conversation.id}. Error message: ${errorMessage}`,
      );
    }
  }
}
