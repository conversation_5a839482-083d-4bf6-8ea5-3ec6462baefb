import { Logger } from '@nestjs/common';
import { LlmClientRouter } from './llm-client-router.service';
import { ConfigService } from '@nestjs/config';
import { LlmOpenAiClient } from './llm-open-ai-client.service';
// import { LlmGeminiClient } from './llm-gemini-client.service';
import { LLMModel, LLM_MODEL_METADATA } from '../model/llm-model.enum';
import { LlmVertexAiClient } from './llm-vertex-ai-client.service';

describe('LlmProviderService', () => {
  let configService: ConfigService;
  let openAiService: LlmOpenAiClient;
  // let geminiService: LlmGeminiClient;
  let vertexAiService: LlmVertexAiClient;

  beforeEach(() => {
    // Create simple stubs for the dependent services
    configService = { get: jest.fn() } as any;
    openAiService = {} as any;
    // geminiService = {} as any;
    vertexAiService = {} as any;
  });

  describe('defaultModel initialization', () => {
    it('should use provided valid defaultModel and log the configuration', () => {
      // Arrange: stub config to return a valid model
      const validModel = LLMModel.OPEN_AI_4_O;
      (configService.get as jest.Mock).mockReturnValue(validModel);

      // Spy on logger.log before instantiation
      const logSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();

      // Act
      const service = new LlmClientRouter(
        configService,
        openAiService,
        // geminiService,
        vertexAiService,
      );

      // Assert: defaultModel resolves to the valid model
      const returned = service.getLlmClient();
      expect(returned).toBe(openAiService);
      expect(logSpy).toHaveBeenCalledWith(
        `Default LLM Model configured to: ${validModel} (${LLM_MODEL_METADATA[validModel].displayName})`,
      );

      logSpy.mockRestore();
    });

    it('should fallback to OPEN_AI_4_1 and warn on missing or invalid defaultModel', () => {
      // Arrange: stub config to return invalid
      (configService.get as jest.Mock).mockReturnValue('INVALID_MODEL');

      // Spy on warn
      const warnSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation();

      // Act
      const service = new LlmClientRouter(
        configService,
        openAiService,
        // geminiService,
        vertexAiService,
      );

      // Assert: getLlmProvider() returns OpenAI service by default
      expect(service.getLlmClient()).toBe(openAiService);
      expect(warnSpy).toHaveBeenCalledWith(
        `Invalid or missing 'llm.defaultModel' in configuration: "INVALID_MODEL". Falling back to ${LLMModel.OPEN_AI_4_1}.`,
      );

      warnSpy.mockRestore();
    });

    it('should fallback and warn when defaultModelFromConfig is undefined', () => {
      // Arrange: config.get returns undefined by default
      const warnSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation();

      // Act
      const service = new LlmClientRouter(
        configService,
        openAiService,
        // geminiService,
        vertexAiService,
      );

      // Assert
      expect(service.getLlmClient()).toBe(openAiService);
      expect(warnSpy).toHaveBeenCalledWith(
        `Invalid or missing 'llm.defaultModel' in configuration: "undefined". Falling back to ${LLMModel.OPEN_AI_4_1}.`,
      );

      warnSpy.mockRestore();
    });
  });

  describe('getLlmProvider', () => {
    let service: LlmClientRouter;

    beforeEach(() => {
      (configService.get as jest.Mock).mockReturnValue(undefined);
      service = new LlmClientRouter(
        configService,
        openAiService,
        // geminiService,
        vertexAiService,
      );
    });

    it('should return the OpenAI service for OPEN_AI models', () => {
      const result = service.getLlmClient(LLMModel.OPEN_AI_4_1);
      expect(result).toBe(openAiService);
    });

    it('should return the Vertex AI service for Vertex AI models', () => {
      const result = service.getLlmClient(LLMModel.VERTEX_AI_GEMINI_2_0_FLASH);
      expect(result).toBe(vertexAiService);
    });
  });
});
