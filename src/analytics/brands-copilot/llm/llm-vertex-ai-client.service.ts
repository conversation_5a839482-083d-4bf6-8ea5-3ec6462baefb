import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';
import { ZodSchema } from 'zod';
import {
  VertexAI,
  HarmCategory,
  HarmBlockThreshold,
} from '@google-cloud/vertexai';
import {
  AwsClient,
  AwsSecurityCredentials,
  AwsSecurityCredentialsSupplier,
  ExternalAccountSupplierContext,
} from 'google-auth-library';
import { fromNodeProviderChain } from '@aws-sdk/credential-providers';

import { CopilotRole, LLMMessage } from '../model/llm-constants.model';
import { Result } from '../model/Result';
import { LlmClient } from './llm-client';
import { getModelId, LLMModel } from '../model/llm-model.enum';
import { zodToJsonSchema } from 'zod-to-json-schema';

/**
 * Custom AWS Supplier for Google Auth Library
 * Implements the AwsSecurityCredentialsSupplier interface to provide AWS credentials
 * for Workload Identity Federation (WIF) authentication
 */
class AwsSupplier implements AwsSecurityCredentialsSupplier {
  private readonly region: string;
  constructor(region: string) {
    this.region = region;
  }
  async getAwsRegion(
    _context: ExternalAccountSupplierContext,
  ): Promise<string> {
    return this.region;
  }
  async getAwsSecurityCredentials(
    _context: ExternalAccountSupplierContext,
  ): Promise<AwsSecurityCredentials> {
    const awsCredentialsProvider = fromNodeProviderChain();
    const awsCredentials = await awsCredentialsProvider();
    // Parse the AWS credentials into a AWS security credentials instance and return them.
    const awsSecurityCredentials = {
      accessKeyId: awsCredentials.accessKeyId,
      secretAccessKey: awsCredentials.secretAccessKey,
      token: awsCredentials.sessionToken,
    };
    return awsSecurityCredentials;
  }
}

/**
 * Ensures every insight in the LLM result has an `elements` array (defaulting to []).
 */
function sanitizeLlmInsightsResult(raw: any): any {
  if (!raw || !Array.isArray(raw.insights)) return raw;
  return {
    ...raw,
    insights: raw.insights.map((insight: any) => ({
      ...insight,
      elements: Array.isArray(insight.elements) ? insight.elements : [],
    })),
  };
}

/**
 * LlmVertexAiClient is a service that interacts with the Vertex AI API
 * Example usage:
 * https://cloud.google.com/vertex-ai/generative-ai/docs/reference/nodejs/0.2.1#:~:text=The%20Vertex%20AI%20Node.,js%20SDK.
 */
@Injectable()
export class LlmVertexAiClient extends LlmClient implements OnModuleInit {
  private readonly logger = new Logger(LlmVertexAiClient.name);
  private projectId?: string;
  private location?: string;
  private vertexAI!: any;
  private authClient!: AwsClient;

  constructor(
    private readonly configService: ConfigService,
    private readonly secretsConfigService: SecretsConfigurationService,
  ) {
    super();
  }

  /** Initialize Vertex AI client */
  async onModuleInit(): Promise<void> {
    try {
      this.projectId = this.configService.get<string>('vertexai.projectId');
      this.location = this.configService.get<string>('vertexai.location');

      if (!this.projectId || !this.location) {
        throw new Error('Vertex AI project ID and location must be configured');
      }

      // Setup Workload Identity Federation with AWS credentials
      const awsRegion =
        process.env.AWS_REGION || this.configService.get<string>('AWS_REGION');
      const audience = this.configService.get<string>('vertexai.audience');
      const subject_token_type = this.configService.get<string>(
        'vertexai.subject_token_type',
      );
      const service_account_impersonation_url = this.configService.get<string>(
        'vertexai.service_account_impersonation_url',
      );
      if (
        !awsRegion ||
        !audience ||
        !subject_token_type ||
        !service_account_impersonation_url
      ) {
        throw new Error(
          'Workload Identity Federation configuration missing. Required: awsRegion, audience, subject_token_type, service_account_impersonation_url',
        );
      }
      const clientOptions = {
        audience,
        subject_token_type: String(subject_token_type),
        aws_security_credentials_supplier: new AwsSupplier(String(awsRegion)),
        service_account_impersonation_url,
      };
      this.authClient = new AwsClient(clientOptions);
      // Initialize VertexAI with authClient
      this.vertexAI = new VertexAI({
        project: this.projectId,
        location: this.location,
        googleAuthOptions: {
          authClient: this.authClient as any, // Type workaround for AwsClient compatibility
        },
      });
      this.logger.log(
        [
          'Vertex AI client initialized successfully for Brands Copilot.',
          `Project: ${this.projectId}`,
          `Location: ${this.location}`,
          `AuthClient config: ${JSON.stringify(clientOptions, null, 2)}`,
        ].join('\n'),
      );
    } catch (error) {
      this.logger.error(
        `Error while trying to initialize Vertex AI client for Brands Copilot: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Checks if a model is a Claude/Anthropic model
   */
  private isClaudeModel(modelId: string): boolean {
    return modelId.toLowerCase().includes('claude');
  }

  /**
   * Converts LLMMessages to the format expected by Vertex AI client
   */
  private formatMessagesForChat(messages: LLMMessage[]): {
    systemPrompt: string;
    contents: Array<{
      role: string;
      parts: Array<{ text: string }>;
    }>;
  } {
    let systemPrompt = '';

    // Handle system prompt separately
    if (messages[0]?.role === CopilotRole.SYSTEM) {
      systemPrompt = messages[0].content;
      // messages = messages.slice(1); // Temporarily include system messages in contents
    }

    // Format remaining messages for Vertex AI client
    const contents = messages.map((message) => ({
      role: message.role === CopilotRole.USER ? 'user' : 'model',
      parts: [{ text: message.content }],
    }));

    return { systemPrompt, contents };
  }

  /**
   * Converts LLMMessages to the format expected by Anthropic Claude API
   */
  private formatMessagesForClaude(messages: LLMMessage[]): Array<{
    role: string;
    content: Array<{ type: string; text: string }>;
  }> {
    // Format messages for Claude API
    const formattedMessages = messages.map((message) => ({
      role: message.role === CopilotRole.USER ? 'user' : 'assistant',
      content: [{ type: 'text', text: message.content }],
    }));

    return formattedMessages;
  }

  /**
   * Makes HTTP request to Claude Vertex AI endpoint using rawPredict
   */
  private async makeClaudeRequest(modelId: string, payload: any): Promise<any> {
    const claudeLocation = 'us-east5'; // Anthropic models are only available in us-east5
    const endpoint = `${claudeLocation}-aiplatform.googleapis.com`;
    const url = `https://${endpoint}/v1/projects/${this.projectId}/locations/${claudeLocation}/publishers/anthropic/models/${modelId}:rawPredict`;

    // Get access token from auth client
    const accessToken = await this.authClient.getAccessToken();
    if (!accessToken.token) {
      throw new Error('Failed to get access token for Claude request');
    }

    // Make HTTP request
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${accessToken.token}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        `Claude API request failed: ${response.status} ${
          response.statusText
        }. ${JSON.stringify(errorData)}`,
      );
    }

    return response.json();
  }

  /**
   * Makes Claude API call for text response - handles only prompt/response exchange
   */
  private async getClaudeTextResponse(
    modelId: string,
    messages: LLMMessage[],
  ): Promise<string> {
    const formattedMessages = this.formatMessagesForClaude(messages);

    const payload = {
      anthropic_version: 'vertex-2023-10-16',
      stream: false,
      max_tokens: 8192,
      temperature: 0.2,
      top_p: 0.8,
      top_k: 40,
      messages: formattedMessages,
    };

    const response = await this.makeClaudeRequest(modelId, payload);

    // Extract text from Claude response
    const responseText = response?.content?.find?.(
      (c: any) => c.type === 'text',
    )?.text;
    if (!responseText) {
      throw new Error('No text content in Claude response');
    }
    return responseText;
  }

  /**
   * Makes Claude API call for JSON response - handles only prompt/response exchange
   */
  private async getClaudeJsonResponse(
    modelId: string,
    schema: string,
    systemPrompt: string,
    userPrompt: string,
  ): Promise<string> {
    const payload = {
      anthropic_version: 'vertex-2023-10-16',
      stream: false,
      max_tokens: 8192,
      temperature: 0.2,
      top_p: 0.8,
      top_k: 40,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `${systemPrompt}
${userPrompt}
Your response must be a valid JSON object that conforms to this schema:
${schema}`,
            },
          ],
        },
      ],
    };

    const response = await this.makeClaudeRequest(modelId, payload);

    // Extract text from Claude response
    const rawJsonResponse = response?.content?.find?.(
      (c: any) => c.type === 'text',
    )?.text;
    if (!rawJsonResponse) {
      throw new Error('No text content in Claude response');
    }

    return rawJsonResponse;
  }

  async getTextResponse(
    messages: LLMMessage[],
    model: LLMModel,
  ): Promise<Result<string>> {
    try {
      if (messages.length === 0) {
        return Result.failure('No messages provided');
      }
      const modelId = getModelId(model);
      const { contents } = this.formatMessagesForChat(messages);
      const last = messages[messages.length - 1];
      let responseText = '';
      if (this.isClaudeModel(modelId)) {
        // Claude models
        responseText = await this.getClaudeTextResponse(modelId, messages);
      } else {
        // Gemini models
        const generativeModel = this.vertexAI.preview.getGenerativeModel({
          generationConfig: {
            maxOutputTokens: 8192, // Enough for structured responses and fits *lite* models
            temperature: 0.2, // Low randomness = more stable structure
            topP: 1.0, // Use full probability distribution
            topK: 0, // No hard cap on tokens ranked
            candidateCount: 1, // Only care about top response
            stopSequences: [], // Don't stop early unless specified
            seed: 42, // Optional: deterministic output for same input
          },
          model: modelId,
          safetySettings: [
            {
              category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
              threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            },
            {
              category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
              threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            },
            {
              category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
              threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            },
            {
              category: HarmCategory.HARM_CATEGORY_HARASSMENT,
              threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
            },
          ],
        });
        const chat = generativeModel.startChat({
          history: contents,
        });
        const result = await chat.sendMessageStream(last.content);
        // Extract text from result
        for await (const item of result.stream) {
          responseText += item?.candidates[0]?.content?.parts[0]?.text;
        }
      }

      return Result.success(responseText);
    } catch (error: any) {
      // Enhanced authentication error logging
      if (
        error.message?.includes('GoogleAuthError') ||
        error.message?.includes('authentication') ||
        error.message?.includes('authenticate')
      ) {
        // AWS environment details
        const awsEnv = {
          AWS_REGION: process.env.AWS_REGION,
          AWS_EXECUTION_ENV: process.env.AWS_EXECUTION_ENV,
          AWS_LAMBDA_FUNCTION_NAME: process.env.AWS_LAMBDA_FUNCTION_NAME,
          ECS_CONTAINER_METADATA_URI_V4:
            process.env.ECS_CONTAINER_METADATA_URI_V4 ?? 'undefined',
        };
        // GCP environment details
        const gcpEnv = {
          GOOGLE_APPLICATION_CREDENTIALS:
            process.env.GOOGLE_APPLICATION_CREDENTIALS ?? 'undefined',
          GCLOUD_PROJECT: process.env.GCLOUD_PROJECT ?? 'undefined',
          GOOGLE_CLOUD_PROJECT: process.env.GOOGLE_CLOUD_PROJECT ?? 'undefined',
        };
        // Single comprehensive authentication error log
        this.logger.error(`=== Vertex AI Authentication Error Details ===
Error Details:
- Name: ${error.name || 'unknown'}
- Code: ${error.code || 'unknown'}
- Status: ${error.status || 'unknown'}
- Message: ${error.message}
- Stack: ${error.stack || 'none'}

Environment Context:
- Current working directory: ${process.cwd()}
- NODE_ENV: ${process.env.NODE_ENV}
- Project ID: ${this.projectId}
- Location: ${this.location}

AWS Environment:
${JSON.stringify(awsEnv, null, 2)}

GCP Environment:
${JSON.stringify(gcpEnv, null, 2)}

Full Error Object:
${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}`);
      }
      if (
        error.message.includes('safety') ||
        error.message.includes('SAFETY')
      ) {
        return Result.failure(
          'Vertex AI request blocked due to safety settings',
        );
      }
      return Result.failure(
        `Error getting text response from Vertex AI: ${error.message}`,
      );
    }
  }

  async getJsonResponse<T>(
    schema: ZodSchema<T>,
    messages: LLMMessage[],
    model: LLMModel,
  ): Promise<Result<T>> {
    try {
      if (messages.length === 0) {
        return Result.failure('No messages provided');
      }
      const { systemPrompt, contents } = this.formatMessagesForChat(messages);
      // Convert the Zod schema to JSON schema
      const jsonSchema = zodToJsonSchema(schema, {
        $refStrategy: 'none',
        target: 'jsonSchema7',
      });
      // Clean up schema properties that might cause issues
      const cleanedSchema: any = { ...jsonSchema };
      if (cleanedSchema.$schema) delete cleanedSchema.$schema;
      if (cleanedSchema.additionalProperties)
        delete cleanedSchema.additionalProperties;
      try {
        const modelId = getModelId(model);
        const last = messages[messages.length - 1];
        const messageWithJsonInstructions = `${last.content}
          Use the \`response\` function call defined in the tools to return your result.
          Return only the args object, as a valid JSON, matching this schema:
          {
            "summary": "..."
          }
          Do not print, explain, or wrap your response. Just return the json object.
          Do not return internal errors like \`malformed function call\`.
          `;

        let rawJsonResponse = '';
        let data: T = {} as T;
        if (this.isClaudeModel(modelId)) {
          // Claude models
          rawJsonResponse = await this.getClaudeJsonResponse(
            modelId,
            JSON.stringify(cleanedSchema, null, 2),
            systemPrompt,
            messageWithJsonInstructions,
          );
        } else {
          // Gemini models
          const generativeModel = this.vertexAI.preview.getGenerativeModel({
            generationConfig: {
              maxOutputTokens: 8192, // Enough for structured responses and fits *lite* models
              temperature: 0.2, // Low randomness = more stable structure
              topP: 1.0, // Use full probability distribution
              topK: 0, // No hard cap on tokens ranked
              candidateCount: 1, // Only care about top response
              stopSequences: [], // Don't stop early unless specified
              seed: 42, // Optional: deterministic output for same input
            },
            model: modelId,
            safetySettings: [
              {
                category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
              },
              {
                category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
              },
              {
                category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
              },
              {
                category: HarmCategory.HARM_CATEGORY_HARASSMENT,
                threshold: HarmBlockThreshold.BLOCK_ONLY_HIGH,
              },
            ],
            tools: [
              {
                functionDeclarations: [
                  {
                    name: 'response',
                    description:
                      'The response function that returns JSON data according to the schema',
                    parameters: cleanedSchema,
                  },
                ],
              },
            ],
          });
          const chat = generativeModel.startChat({
            history: contents,
          });
          // Send the message with tool calling enabled
          const result = await chat.sendMessageStream(
            messageWithJsonInstructions,
          );
          for await (const item of result.stream) {
            // Check for function call response first
            const functionCall =
              item.candidates[0]?.content?.parts?.[0]?.functionCall;
            if (functionCall && functionCall.name === 'response') {
              // Extract JSON from function call args
              rawJsonResponse += functionCall.args
                ? JSON.stringify(functionCall.args)
                : '';
            } else {
              // Fallback to regular text response if no function call
              rawJsonResponse +=
                item.candidates[0]?.content?.parts?.[0]?.text || '';
            }
          }
          // Wait for complete response and check for function call in final response
          const finalResponse = await result.response;
          const finalFunctionCall =
            finalResponse.candidates[0]?.content?.parts?.[0]?.functionCall;
          if (
            finalFunctionCall &&
            finalFunctionCall.name === 'response' &&
            !rawJsonResponse
          ) {
            rawJsonResponse = finalFunctionCall.args
              ? JSON.stringify(finalFunctionCall.args)
              : '';
          }
        }
        try {
          // Try to extract JSON if it's embedded in markdown or other text
          const jsonMatches = rawJsonResponse.match(
            /```(?:json)?\s*({[\s\S]*?})\s*```/,
          ) ||
            rawJsonResponse.match(/({[\s\S]*})/) || [null, rawJsonResponse];
          const jsonContent = jsonMatches[1]?.trim() || rawJsonResponse;

          this.logger.log(`
Maddie prompt to model ${modelId}:
Schema:
${JSON.stringify(cleanedSchema, null, 2)}
System Prompt:
${systemPrompt}
User Prompt:
${last.content}
Maddie json response from model:
${JSON.stringify(JSON.parse(jsonContent), null, 2)}`);
          data = JSON.parse(jsonContent);

          data = sanitizeLlmInsightsResult(data);
        } catch (error: any) {
          return Result.failure(
            `Failed to parse Vertex AI JSON response: ${error.message}
            Maddie raw response from model:\n${JSON.stringify(
              rawJsonResponse,
              null,
              2,
            )}`,
          );
        }

        // Validate the parsed data against the original Zod schema as a safeguard.
        // Although the API is configured for a schema, this adds an extra layer
        // of validation and provides detailed Zod error messages.
        const validation = schema.safeParse(data);
        if (validation.success) {
          return Result.success(validation.data);
        } else {
          // Log detailed validation errors if the parsed JSON doesn't match the Zod schema
          return Result.failure(
            `Gemini response JSON validation failed: ${validation.error.issues
              .map((i) => `${i.path.join('.')}: ${i.message}`)
              .join(', ')}`,
          );
        }
      } catch (error: any) {
        // Enhanced authentication error logging
        if (
          error.message?.includes('GoogleAuthError') ||
          error.message?.includes('authentication') ||
          error.message?.includes('authenticate')
        ) {
          // AWS environment details
          const awsEnv = {
            AWS_REGION: process.env.AWS_REGION,
            AWS_EXECUTION_ENV: process.env.AWS_EXECUTION_ENV,
            AWS_LAMBDA_FUNCTION_NAME: process.env.AWS_LAMBDA_FUNCTION_NAME,
            ECS_CONTAINER_METADATA_URI_V4:
              process.env.ECS_CONTAINER_METADATA_URI_V4 ?? 'undefined',
          };

          // Single comprehensive authentication error log
          this.logger.error(`=== Vertex AI Authentication Error Details ===
Environment Context:
  - Current working directory: ${process.cwd()}
  - NODE_ENV: ${process.env.NODE_ENV}
  - Project ID: ${this.projectId}
  - Location: ${this.location}

AWS Environment:
${JSON.stringify(awsEnv, null, 2)}

Full Error Object:
${JSON.stringify(error, Object.getOwnPropertyNames(error), 2)}`);
        }
        return Result.failure(
          `Error in LlmVertexAiClient.getJsonResponse: ${error.message}`,
        );
      }
    } catch (error: any) {
      if (
        error.message.includes('safety') ||
        error.message.includes('SAFETY')
      ) {
        return Result.failure(
          'Vertex AI JSON request blocked due to safety settings',
        );
      }
      if (
        error.message.includes('responseSchema') ||
        error.message.includes('schema')
      ) {
        return Result.failure(
          `Vertex AI schema configuration error: ${error.message}`,
        );
      }
      return Result.failure(
        `Error getting JSON response from Vertex AI: ${error.message}`,
      );
    }
  }
}
