import { AutoMap } from '@automapper/classes';
import { IsArray, IsBoolean, IsNotEmpty, IsOptional } from 'class-validator';
import { CriteriaPerformanceRequestDtoFilters } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/criteriaPerformanceRequestDtoFilters';
import {
  CreativeLeaderboardRequestDto,
  CriteriaPerformanceRequestDtoAdvancedFilters,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import PlatformEnum = CreativeLeaderboardRequestDto.PlatformEnum;

class DateRangeDto {
  @AutoMap()
  startDate: string;

  @AutoMap()
  endDate: string;
}

export class GroupedCriteriaDetailsRequestDto {
  @AutoMap()
  id: string;

  @AutoMap()
  @IsOptional()
  @IsArray()
  criteriaIds: number[];

  @AutoMap()
  platform: PlatformEnum;

  @AutoMap()
  @IsArray()
  adAccountIds: string[];

  @AutoMap()
  dateRange: DateRangeDto;

  @AutoMap()
  @IsArray()
  @IsOptional()
  mediaTypes?: CriteriaPerformanceRequestDtoFilters.CreativeMediaTypeEnum[];

  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  workspaceIds: number[];

  @AutoMap()
  @IsOptional()
  advancedFilters?: CriteriaPerformanceRequestDtoAdvancedFilters;

  @AutoMap()
  @IsOptional()
  @IsBoolean()
  shouldIncludeStandardCriteria?: boolean;
}
