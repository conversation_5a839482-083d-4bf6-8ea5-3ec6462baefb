import { AutoMap } from '@automapper/classes';
import { CriteriaPerformanceDtoMediaCount } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/criteriaPerformanceDtoMediaCount';
import { CriteriaPerformanceDtoImpressions } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/criteriaPerformanceDtoImpressions';
import { ApiProperty } from '@nestjs/swagger';

export class CriteriaPerformanceDto {
  @ApiProperty({
    description:
      'unique identifier for the criteria group, generated from the groupBy attributes',
    example: 'BRAND_NAME_IN_AUDIO_NEAR_END:{maxFromEndAudioBrandMention:5}',
  })
  @AutoMap()
  id: string;

  @ApiProperty({
    description: 'unique criteria identifier',
    example: 'BRAND_NAME_IN_AUDIO_NEAR_END',
  })
  @AutoMap()
  identifier: string;

  @ApiProperty({
    description: 'criteria parameters',
    example: '{maxFromEndAudioBrandMention:5}',
  })
  @AutoMap()
  parameters: Record<string, any>;

  @ApiProperty({
    description: 'ad account media count values for the criteria group',
    example: { met: 749484, failed: 9484, percentageMet: 98.******** },
  })
  @AutoMap()
  mediaCount: CriteriaPerformanceDtoMediaCount;

  @ApiProperty({
    description: 'impressions count values for the criteria group',
    example: { met: 749484, failed: 9484, total: 758968 },
  })
  @AutoMap()
  impressions: CriteriaPerformanceDtoImpressions;

  @ApiProperty({
    description: 'kpi values for the criteria group',
    example: {
      '103': {
        met: 1.************,
        failed: 1.************,
        percentLift: 2.***************,
        isStatisticallySignificant: false,
      },
      '**************:1': {
        met: 14.*********,
        failed: 17.*********,
        percentLift: -16.***************,
        isStatisticallySignificant: true,
      },
    },
  })
  @AutoMap()
  performanceByKpiId: Record<
    string,
    {
      met: number;
      failed: number;
      percentLift: number;
      isStatisticallySignificant: boolean;
    }
  >;
}
