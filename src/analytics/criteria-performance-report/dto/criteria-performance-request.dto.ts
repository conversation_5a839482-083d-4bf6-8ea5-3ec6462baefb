import {
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { Platform } from '../../../constants/platform.constants';
import { CriteriaPerformanceRequestDtoFilters } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/criteriaPerformanceRequestDtoFilters';
import { CriteriaPerformanceRequestDto as CriteriaPerformanceRequestDtoSDK } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/criteriaPerformanceRequestDto';
import { ApiProperty } from '@nestjs/swagger';
import { AdvancedFiltersRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk';

export class CriteriaPerformanceRequestDto {
  @ApiProperty({
    description: 'workspace ids to query leaderboard data for',
    example: [100, 200, 300],
  })
  @AutoMap()
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  workspaceIds: number[];

  @ApiProperty({
    description: 'start date to query leaderboard data from',
    example: '2023-08-22',
  })
  @AutoMap()
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: 'last date to query leaderboard data to',
    example: '2023-08-15',
  })
  @AutoMap()
  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  @ApiProperty({
    description: 'platform ad account ids to query creative performance for',
    example: ['***************', '8340nhhohr30h098', '****************'],
  })
  @AutoMap()
  @IsNotEmpty()
  @IsString({ each: true })
  adAccountIds: string[];

  @ApiProperty({
    description: 'id of platform KPI to query performance for',
    example: ['103', '**************:1'],
  })
  @AutoMap()
  @IsNotEmpty()
  @IsString({ each: true })
  kpiIds: string[];

  @ApiProperty({
    description: 'platform (aka channel) to filter by',
    example: 'FACEBOOK',
  })
  @AutoMap()
  @IsNotEmpty()
  @IsEnum(Platform)
  platform: Platform;

  @ApiProperty({
    description:
      'the percentage statistical confidence level to use for determining if a criteria performance is statistically significant. Defaults to 95',
    example: 95,
  })
  @AutoMap()
  @IsOptional()
  @IsNumber()
  statisticalConfidence?: number;

  @ApiProperty({
    description: 'criteria attributes to roll up performance data by',
    example: ['identifier', 'parameters'],
  })
  @AutoMap()
  @IsOptional()
  @IsEnum(CriteriaPerformanceRequestDtoSDK.GroupByEnum, { each: true })
  groupBy?: CriteriaPerformanceRequestDtoSDK.GroupByEnum[];

  @ApiProperty({
    description: 'filters to apply to the criteria performance data',
    example: {
      criteriaGroupIds: [
        'BRAND_NAME_OR_LOGO_PERSISTS:{minPercent:25}',
        'JNJ_VISUAL_CTA_PRESENCE:{durationBeforeEnd:3}',
      ],
      creativeMediaType: ['video', 'image'],
    },
  })
  @AutoMap()
  @IsOptional()
  @ValidateNested({ each: true })
  filters?: CriteriaPerformanceRequestDtoFilters;

  @ApiProperty({
    description: 'advanced filters to apply to the criteria performance data',
    example: {
      adsetIdentifiers: ['adset1', 'adset2'],
      campaignObjectives: ['BRAND_AWARENESS', 'CONVERSIONS'],
    },
  })
  @AutoMap()
  @IsOptional()
  advancedFilters?: AdvancedFiltersRequestDto;

  @IsOptional()
  @IsBoolean()
  @AutoMap()
  isSpendKpiEnabled?: boolean = false;
}
