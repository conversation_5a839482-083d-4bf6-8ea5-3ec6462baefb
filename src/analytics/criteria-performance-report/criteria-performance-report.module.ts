import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { CriteriaPerformanceReportController } from './criteria-performance-report.controller';
import { CriteriaPerformanceReportService } from './criteria-performance-report.service';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';

@Module({
  controllers: [CriteriaPerformanceReportController],
  providers: [
    CriteriaPerformanceReportService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
    ReportSpendPermissionService,
    OrganizationUserService,
  ],
})
export class CriteriaPerformanceReportModule {}
