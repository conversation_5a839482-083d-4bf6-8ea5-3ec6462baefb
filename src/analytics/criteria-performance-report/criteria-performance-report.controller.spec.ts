import { CriteriaPerformanceReportController } from './criteria-performance-report.controller';
import { CriteriaPerformanceReportService } from './criteria-performance-report.service';
import { Test, TestingModule } from '@nestjs/testing';
import {
  mockCriteriaGroupReportRequest,
  mockOrganizationId,
} from './mock-data/mock-data';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

describe('CriteriaGroupReportController', () => {
  let criteriaGroupReportController: CriteriaPerformanceReportController;
  const mockGetCriteriaGroupReport = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CriteriaPerformanceReportController],
      providers: [
        {
          provide: CriteriaPerformanceReportService,
          useValue: {
            getCriteriaGroupReport: mockGetCriteriaGroupReport,
          },
        },
      ],
    }).compile();

    criteriaGroupReportController =
      module.get<CriteriaPerformanceReportController>(
        CriteriaPerformanceReportController,
      );
  });

  it('should be defined', () => {
    expect(criteriaGroupReportController).toBeDefined();
  });

  it('should call getCriteriaGroupReport', async () => {
    await criteriaGroupReportController.getCriteriaGroupReport(
      {
        userId: 'mock-user-id',
        headers: { authorization: 'mock-authorization' },
      },
      mockOrganizationId,
      mockCriteriaGroupReportRequest,
    );

    expect(mockGetCriteriaGroupReport).toHaveBeenCalledWith(
      mockCriteriaGroupReportRequest,
      {
        userId: 'mock-user-id',
        organizationId: mockOrganizationId,
        authorization: 'mock-authorization',
      },
    );
  });
});
