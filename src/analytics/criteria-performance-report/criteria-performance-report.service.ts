import { Injectable } from '@nestjs/common';
import {
  GetCriteriaPerformance200Response,
  CriteriaPerformanceService as CriteriaPerformanceServiceSDK,
  ExchangeRateRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { CriteriaPerformanceRequestDto } from './dto/criteria-performance-request.dto';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { UserDetailsDto } from '../dto/user-details.dto';
import {
  GroupedCriteriaDetailsDto,
  ScoringCriteriaService as ScoringCriteriaServiceSDK,
} from '@vidmob/vidmob-soa-scoring-service-sdk';
import { GroupedCriteriaDetailsRequestDto } from './dto/grouped-criteria-details-request.dto';
import { CriteriaPerformanceRequestDto as CriteriaPerformanceRequestDtoSDK } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/criteriaPerformanceRequestDto';
import CurrencyCodeEnum = ExchangeRateRequestDto.CurrencyCodeEnum;
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

@Injectable()
export class CriteriaPerformanceReportService {
  constructor(
    private readonly criteriaPerformanceServiceSDK: CriteriaPerformanceServiceSDK,
    private readonly scoringCriteriaServiceSDK: ScoringCriteriaServiceSDK,
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly reportSpendPermissionService: ReportSpendPermissionService,
  ) {}

  async getCriteriaGroupReport(
    request: CriteriaPerformanceRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<GetCriteriaPerformance200Response> {
    const { workspaceIds, adAccountIds } = request;
    const organizationId = userDetails.organizationId;

    const isSpendKpiEnabled =
      await this.reportSpendPermissionService.getOrganizationAccessToSpendKpi(
        organizationId,
      );

    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      userDetails,
      workspaceIds,
      adAccountIds,
    );
    return this.criteriaPerformanceServiceSDK.getCriteriaPerformanceAsPromise(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore // platform enum is the same but nestjs is not smart enough to tell
      { ...request, isSpendKpiEnabled } as CriteriaPerformanceRequestDtoSDK,
    );
  }

  async getCriteriaGroupDetails(
    request: GroupedCriteriaDetailsRequestDto,
    organizationId: string,
  ) {
    const {
      dateRange,
      adAccountIds,
      id,
      platform,
      workspaceIds,
      mediaTypes,
      advancedFilters,
      shouldIncludeStandardCriteria,
      criteriaIds,
    } = request;
    const criteriaPerformanceRequestDto: CriteriaPerformanceRequestDtoSDK = {
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      adAccountIds,
      kpiIds: [],
      platform,
      filters: {
        creativeMediaType: mediaTypes ?? [],
        criteriaGroupedRules: [{ id, criteriaIds }],
      },
      advancedFilters,
      currency: CurrencyCodeEnum.Usd,
      shouldIncludeStandardCriteria,
    };

    const criteriaGroupDetailsDto: GroupedCriteriaDetailsDto = {
      workspaceIds,
      adAccountIds,
      platform,
      id,
      organizationId,
      shouldIncludeStandardCriteria,
    };

    const exampleMedia =
      await this.criteriaPerformanceServiceSDK.getCriteriaGroupExampleMediaAsPromise(
        criteriaPerformanceRequestDto,
      );
    const detailsResponse =
      await this.scoringCriteriaServiceSDK.fetchGroupedCriteriaDetailsAsPromise(
        criteriaGroupDetailsDto,
      );

    const { examplePlatformMediasPass, examplePlatformMediasFail } =
      exampleMedia.result;

    return {
      ...detailsResponse.result,
      examplePlatformMediasPass,
      examplePlatformMediasFail,
    };
  }
}
