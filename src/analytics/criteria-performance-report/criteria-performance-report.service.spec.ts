import { CriteriaPerformanceReportService } from './criteria-performance-report.service';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { CriteriaPerformanceService as CriteriaPerformanceServiceSDK } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Test, TestingModule } from '@nestjs/testing';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK } from '@vidmob/vidmob-organization-service-sdk/dist/api/workspaceAdAccount.service';
import {
  mockCriteriaGroupReportRequest,
  mockUserDetails,
} from './mock-data/mock-data';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

describe('CriteriaGroupReportService', () => {
  let service: CriteriaPerformanceReportService;
  const mockGetCriteriaPerformanceAsPromise = jest.fn();
  const mockGetWorkspacesByOrganizationId = jest.fn();
  const mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CriteriaPerformanceReportService,
        AnalyticsUserService,
        {
          provide: CriteriaPerformanceServiceSDK,
          useValue: {
            getCriteriaPerformanceAsPromise:
              mockGetCriteriaPerformanceAsPromise,
          },
        },
        {
          provide: WorkspaceService,
          useValue: {
            getWorkspacesByOrganizationId: mockGetWorkspacesByOrganizationId,
          },
        },
        {
          provide: WorkspaceAdAccountServiceSDK,
          useValue: {
            getConnectedAdAccountsForWorkspacesAndPlatformAsPromise:
              mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise,
          },
        },
        {
          provide: ScoringCriteriaService,
          useValue: {},
        },
        {
          provide: ReportSpendPermissionService,
          useValue: {
            getOrganizationAccessToSpendKpi: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    }).compile();

    service = module.get<CriteriaPerformanceReportService>(
      CriteriaPerformanceReportService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should throw ForbiddenException if user does not have access to ad accounts and workspaces', async () => {
    mockGetWorkspacesByOrganizationId.mockResolvedValue({
      items: [{ id: 1 }],
      totalCount: 1,
    });
    mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise.mockResolvedValue(
      {
        result: [{ platformAccountId: '1' }, { platformAccountId: '2' }],
        pagination: {
          totalSize: 2,
        },
      },
    );

    await expect(
      service.getCriteriaGroupReport(
        mockCriteriaGroupReportRequest,
        mockUserDetails,
      ),
    ).rejects.toThrowError(
      'User 2222 does not have access to all the submitted workspaces',
    );
  });

  it('should call analytics api getCriteriaPerformanceAsPromise', async () => {
    mockGetWorkspacesByOrganizationId.mockResolvedValue({
      items: [{ id: 100 }, { id: 200 }, { id: 300 }],
      totalCount: 1,
    });
    mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise.mockResolvedValue(
      {
        result: [
          { platformAccountId: 'mock-ad-account-id-one' },
          { platformAccountId: 'mock-ad-account-id-two' },
        ],
        pagination: {
          totalSize: 2,
        },
      },
    );

    await service.getCriteriaGroupReport(
      mockCriteriaGroupReportRequest,
      mockUserDetails,
    );

    expect(mockGetCriteriaPerformanceAsPromise).toHaveBeenCalledWith(
      mockCriteriaGroupReportRequest,
    );
  });

  it('should forward criteriaGroupIds when present', async () => {
    mockGetWorkspacesByOrganizationId.mockResolvedValue({
      items: [{ id: 100 }, { id: 200 }, { id: 300 }],
      totalCount: 1,
    });
    mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise.mockResolvedValue(
      {
        result: [
          { platformAccountId: 'mock-ad-account-id-one' },
          { platformAccountId: 'mock-ad-account-id-two' },
        ],
        pagination: {
          totalSize: 2,
        },
      },
    );

    const requestWithFilter = {
      ...mockCriteriaGroupReportRequest,
      filters: {
        ...mockCriteriaGroupReportRequest.filters,
        criteriaGroupIds: ['keep-this'],
      },
    };

    await service.getCriteriaGroupReport(requestWithFilter, mockUserDetails);

    expect(mockGetCriteriaPerformanceAsPromise).toHaveBeenCalledWith(
      expect.objectContaining({
        filters: expect.objectContaining({
          criteriaGroupIds: ['keep-this'],
        }),
      }),
    );
  });

  it('should not include criteriaGroupIds when none are passed', async () => {
    mockGetWorkspacesByOrganizationId.mockResolvedValue({
      items: [{ id: 100 }, { id: 200 }, { id: 300 }],
      totalCount: 1,
    });
    mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise.mockResolvedValue(
      {
        result: [
          { platformAccountId: 'mock-ad-account-id-one' },
          { platformAccountId: 'mock-ad-account-id-two' },
        ],
        pagination: {
          totalSize: 2,
        },
      },
    );

    const requestWithoutFilter = {
      ...mockCriteriaGroupReportRequest,
      filters: {
        ...mockCriteriaGroupReportRequest.filters,
        criteriaGroupIds: [],
      },
    };

    await service.getCriteriaGroupReport(requestWithoutFilter, mockUserDetails);

    expect(mockGetCriteriaPerformanceAsPromise).toHaveBeenCalledWith(
      expect.objectContaining({
        filters: expect.objectContaining({
          criteriaGroupIds: [],
        }),
      }),
    );
  });
});
