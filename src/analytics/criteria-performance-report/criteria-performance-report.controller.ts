import { CriteriaPerformanceReportService } from './criteria-performance-report.service';
import { Body, Controller, Param, Post, Request } from '@nestjs/common';
import { GetCriteriaPerformance200Response } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ApiParam } from '@nestjs/swagger';
import { CriteriaPerformanceRequestDto } from './dto/criteria-performance-request.dto';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { readOrganizationAdAccountsAndCriteriaDetails } from '../analytics.permissions';
import { VmApiOkUnPaginatedArrayResponse } from '@vidmob/vidmob-nestjs-common';
import { CriteriaPerformanceDto } from './dto/criteria-performance.dto';
import { GroupedCriteriaDetailsRequestDto } from './dto/grouped-criteria-details-request.dto';

/**
 * Controller for criteria performance report
 * Note: "criteria groups" referred to here are just criteria grouped by chosen attributes, meant to support analytics criteria performance report.
 * This is not related to the criteria group feature where user can create a group for organization and assign individual criteria to it.
 */
@Controller('criteria-group-report/organization/:organizationId')
export class CriteriaPerformanceReportController {
  constructor(
    private readonly criteriaGroupReportService: CriteriaPerformanceReportService,
  ) {}

  /**
   * Get details for criteria, grouped by criteria attributes
   *
   * @param req
   * @param organizationId
   * @param requestBody
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization for workspaces in request to fetch criteria performance for',
    required: true,
  })
  @Post('details')
  @Permissions(readOrganizationAdAccountsAndCriteriaDetails)
  async getCriteriaGroupDetailsReport(
    @Param('organizationId') organizationId: string,
    @Body() requestBody: GroupedCriteriaDetailsRequestDto,
  ) {
    return await this.criteriaGroupReportService.getCriteriaGroupDetails(
      requestBody,
      organizationId,
    );
  }

  /**
   * Get performance for criteria, grouped by criteria attributes, in workspaces, ad accounts, and for KPIs
   *
   * @param req
   * @param organizationId
   * @param requestBody
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization for workspaces in request to fetch criteria performance for',
    required: true,
  })
  @VmApiOkUnPaginatedArrayResponse({ type: CriteriaPerformanceDto })
  @Post()
  @Permissions(readOrganizationAdAccountsAndCriteriaDetails)
  async getCriteriaGroupReport(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() requestBody: CriteriaPerformanceRequestDto,
  ): Promise<GetCriteriaPerformance200Response> {
    const { userId, headers } = req;

    const userDetails = {
      userId,
      organizationId,
      authorization: headers?.authorization,
    };

    return this.criteriaGroupReportService.getCriteriaGroupReport(
      requestBody,
      userDetails,
    );
  }
}
