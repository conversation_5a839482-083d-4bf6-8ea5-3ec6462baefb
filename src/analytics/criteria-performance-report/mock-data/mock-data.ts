import { CriteriaPerformanceRequestDto } from '../dto/criteria-performance-request.dto';
import { Platform } from '../../../constants/platform.constants';
import { UserDetailsDto } from '../../dto/user-details.dto';

export const mockOrganizationId = 'mock-organization-id';

export const mockCriteriaGroupReportRequest: CriteriaPerformanceRequestDto = {
  workspaceIds: [100, 200, 300],
  adAccountIds: ['mock-ad-account-id-one', 'mock-ad-account-id-two'],
  statisticalConfidence: 95,
  kpiIds: ['11', '22', '33'],
  platform: Platform.FACEBOOK,
  startDate: '2023-08-22',
  endDate: '2023-08-15',
  filters: {
    creativeMediaType: ['VIDEO', 'IMAGE'],
    criteriaGroupIds: [],
  },
  isSpendKpiEnabled: true,
};

export const mockUserDetails: UserDetailsDto = {
  organizationId: mockOrganizationId,
  userId: 2222,
  authorization: 'mock-authorization',
};
