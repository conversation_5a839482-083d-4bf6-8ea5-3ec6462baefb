import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { MediaService as MediaServiceSDK } from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class ElementFlagAuthorizationService {
  private readonly logger = new Logger(ElementFlagAuthorizationService.name);

  constructor(
    private readonly organizationUserService: OrganizationUserService,
    private readonly mediaService: MediaServiceSDK,
  ) {}

  async validateUserOrganizationAccess(
    userId: number,
    organizationId: string,
  ): Promise<void> {
    const getUserResponse =
      await this.organizationUserService.getUserInOrganization(
        organizationId,
        userId,
      );
    const user = getUserResponse.result;

    if (!user) {
      this.logger.error(
        `User ${userId} not found in organization ${organizationId}`,
      );
      throw new UnauthorizedException(
        `User ${userId} not found in organization ${organizationId}`,
      );
    }
    if (!user.roles || user.roles.length === 0) {
      this.logger.error(
        `User ${userId} does not have access to organization ${organizationId}`,
      );
      throw new UnauthorizedException(
        `User ${userId} does not have access to organization ${organizationId}`,
      );
    }
  }
}
