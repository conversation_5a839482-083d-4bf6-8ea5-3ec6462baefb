import { Test, TestingModule } from '@nestjs/testing';
import { ElementFlagController } from './element-flag.controller';
import { ElementFlagService } from './element-flag.service';
import { CreateElementFlagDto } from './dto/create-element-flag.dto';
import { UserOrganizationDetailsDto } from './dto/user-organization-details.dto';

describe('ElementFlagController', () => {
  let controller: ElementFlagController;
  let service: ElementFlagService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ElementFlagController],
      providers: [
        {
          provide: ElementFlagService,
          useValue: {
            createElemementFlag: jest.fn(),
            getFlagsByMedia: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<ElementFlagController>(ElementFlagController);
    service = module.get<ElementFlagService>(ElementFlagService);
  });

  describe('createFlag', () => {
    it('should call createElemementFlag with correct parameters and return result', async () => {
      const req = { userId: 1234 };
      const organizationId = 'org-123';
      const createFlagDto: CreateElementFlagDto = {
        mediaId: 5678,
        tagValue: 'BLUE',
        tagType: 'COLOR',
        flagReason: 'MISLEADING',
        flagSource: 'ANALYTICS',
        timestampsFlagged: [5, 8],
        additionalNotes: 'Tag should be RED instead of BLUE.',
      };

      const userDetails: UserOrganizationDetailsDto = {
        userId: req.userId,
        organizationId,
      };

      const expectedResult = { status: 'success', data: 'flag created' };

      (service.createElemementFlag as jest.Mock).mockResolvedValue(
        expectedResult,
      );

      const result = await controller.createFlag(
        req,
        organizationId,
        createFlagDto,
      );

      expect(service.createElemementFlag).toHaveBeenCalledWith(
        createFlagDto,
        userDetails,
      );
      expect(result).toEqual(expectedResult);
    });
  });
});
