import { ApiProperty } from '@nestjs/swagger';
import { AutoMap } from '@automapper/classes';
import { IsNumber, IsString, IsOptional, IsArray } from 'class-validator';

export class ReadElementFlagResponseDto {
  @ApiProperty()
  @AutoMap()
  @IsString()
  id: string;

  @ApiProperty()
  @AutoMap()
  @IsNumber()
  mediaId: number;

  @ApiProperty()
  @AutoMap()
  @IsString()
  organizationId: string;

  @ApiProperty()
  @AutoMap()
  @IsNumber()
  userId: number;

  @ApiProperty()
  @AutoMap()
  @IsString()
  status: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  tagValue: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  tagType: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  flagSource: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  flagReason: string;

  @ApiProperty({ required: false, type: [Number] })
  @AutoMap()
  @IsArray()
  @IsOptional()
  timestampsFlagged?: number[];

  @ApiProperty({ required: false })
  @AutoMap()
  @IsString()
  @IsOptional()
  additionalNotes?: string;

  @ApiProperty()
  @AutoMap()
  dateCreated: Date;

  @ApiProperty()
  @AutoMap()
  lastUpdated: Date;

  /**
   * If you want to include the account ID derived from the nested `platformMedia`,
   * but not the full platformMedia object, put it here:
   */
  @ApiProperty({ required: false })
  @AutoMap()
  @IsString()
  @IsOptional()
  platformAccountId?: string;

  /**
   * If you want the platform string (e.g. 'FACEBOOKPAGE') in the response,
   * include this too.
   */
  @ApiProperty({ required: false })
  @AutoMap()
  @IsString()
  @IsOptional()
  platform?: string;
}
