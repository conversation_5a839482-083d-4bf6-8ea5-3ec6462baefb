import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsNumber, IsOptional, IsString } from 'class-validator';

export class ReadElementFlagFromSdkDto {
  @ApiProperty()
  @AutoMap()
  @IsString()
  id: string;

  @ApiProperty()
  @AutoMap()
  @IsNumber()
  mediaId: number;

  @ApiProperty()
  @AutoMap()
  @IsString()
  organizationId: string;

  @ApiProperty()
  @AutoMap()
  @IsNumber()
  userId: number;

  @ApiProperty()
  @AutoMap()
  @IsString()
  status: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  tagValue: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  tagType: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  flagSource: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  flagReason: string;

  @ApiProperty({ required: false, type: [Number] })
  @AutoMap()
  @IsArray()
  @IsOptional()
  timestampsFlagged?: number[];

  @ApiProperty({ required: false })
  @AutoMap()
  @IsString()
  @IsOptional()
  additionalNotes?: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  platformAccountId: string;

  @ApiProperty()
  @AutoMap()
  @IsString()
  platform: string;

  @ApiProperty()
  @AutoMap()
  dateCreated: Date;

  @ApiProperty()
  @AutoMap()
  lastUpdated: Date;
}
