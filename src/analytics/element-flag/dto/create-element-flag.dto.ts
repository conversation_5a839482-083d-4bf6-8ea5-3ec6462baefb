import { Type } from 'class-transformer';
import { IsOptional, IsString, IsA<PERSON>y, IsNumber, IsIn } from 'class-validator';
import { FLAG_REASONS, FLAG_SOURCE } from '../constants';

export type FlagReasonType = (typeof FLAG_REASONS)[number];
export type FlagSourceType = (typeof FLAG_SOURCE)[number];

export class CreateElementFlagDto {
  @Type(() => Number)
  @IsNumber()
  mediaId: number;

  @IsString()
  tagValue: string;

  @IsString()
  tagType: string;

  @IsOptional()
  @IsString()
  @IsIn(FLAG_REASONS)
  flagReason?: FlagReasonType;

  @IsOptional()
  @IsString()
  @IsIn(FLAG_SOURCE)
  flagSource?: FlagSourceType;

  @IsOptional()
  @IsArray()
  timestampsFlagged?: number[];

  @IsOptional()
  @IsString()
  additionalNotes?: string;
}
