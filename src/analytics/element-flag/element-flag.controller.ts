import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Request,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CreateElementFlagDto } from './dto/create-element-flag.dto';
import { ElementFlagService } from './element-flag.service';
import { ApiTags } from '@nestjs/swagger';
import { UserOrganizationDetailsDto } from './dto/user-organization-details.dto';
import { readOrganizationWorkspaceElementFlagDetails } from '../analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';

@ApiTags('Element Flag')
@Controller({
  path: 'analytics',
})
export class ElementFlagController {
  constructor(private readonly elementFlagService: ElementFlagService) {}

  /**
   * Creates a new element flag for the specified media.
   *
   * @param req - The incoming request containing user context.
   * @param organizationId - The unique identifier of the organization.
   * @param createElementFlagDto - The DTO containing the element flag data.
   * @returns The newly created element flag.
   */
  @Permissions(readOrganizationWorkspaceElementFlagDetails)
  @UsePipes(new ValidationPipe({ transform: true }))
  @Post('organization/:organizationId/element-flag')
  async createFlag(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() createElementFlagDto: CreateElementFlagDto,
  ) {
    const { userId } = req;

    const userDetails: UserOrganizationDetailsDto = {
      userId,
      organizationId,
    };

    return this.elementFlagService.createElemementFlag(
      createElementFlagDto,
      userDetails,
    );
  }

  /**
   * Retrieves all element flags for a given mediaId within a specified organization.
   *
   * @param req - The incoming request containing user context.
   * @param organizationId - The unique identifier of the organization.
   * @param mediaId - The media identifier used to filter element flags.
   * @returns An array of element flags that match the provided mediaId and organization.
   */
  @Permissions(readOrganizationWorkspaceElementFlagDetails)
  @Get('organization/:organizationId/element-flag/media/:mediaId')
  getFlagsByMedia(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('mediaId') mediaId: number,
  ) {
    const { userId } = req;
    const userDetails: UserOrganizationDetailsDto = {
      userId,
      organizationId,
    };
    return this.elementFlagService.getFlagsByMedia(mediaId, userDetails);
  }
}
