import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { ElementFlagService as ElementFlagServiceSDK } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { MediaService as MediaServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { UserOrganizationDetailsDto } from './dto/user-organization-details.dto';
import { InjectMapper } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { ReadElementFlagResponseDto } from './dto/read-element-flag-response.dto';
import { ReadElementFlagFromSdkDto } from './dto/read-element-flag-from-sdk.dto';

@Injectable()
export class ElementFlagService {
  private readonly logger = new Logger(ElementFlagService.name);

  constructor(
    private readonly elementFlagService: ElementFlagServiceSDK,
    private readonly mediaService: MediaServiceSDK,
    @InjectMapper() private readonly classMapper: Mapper,
  ) {}

  /**
   * Checks if the user is allowed to view the media.
   * Throws ForbiddenException if not authorized.
   */
  private async ensureMediaViewPermission(
    mediaId: number,
    userId: number,
  ): Promise<void> {
    const { result } =
      await this.mediaService.validatePersonCanViewMediaAsPromise(
        mediaId,
        userId,
      );
    if (!result?.canView) {
      this.logger.error(
        `User ${userId} is not allowed to view media ${mediaId}`,
      );
      throw new ForbiddenException(
        `User ${userId} is not allowed to view media ${mediaId}`,
      );
    }
  }

  /**
   * Creates a new element flag for the specified media.
   */
  async createElemementFlag(
    createElementFlagDto: any,
    userDetails: UserOrganizationDetailsDto,
  ) {
    const { userId, organizationId } = userDetails;
    const mediaId = createElementFlagDto.mediaId;

    await this.ensureMediaViewPermission(mediaId, userId);

    const serviceDto = {
      ...createElementFlagDto,
      organizationId,
      userId,
    };

    return this.elementFlagService.createAsPromise(serviceDto);
  }

  /**
   * Retrieves a list of element flags from the database that have the specified mediaId,
   * then maps them into ReadElementFlagResponseDto, removing platformMedia.
   */
  async getFlagsByMedia(
    mediaId: number,
    userDetails: UserOrganizationDetailsDto,
  ): Promise<ReadElementFlagResponseDto[]> {
    try {
      const { userId, organizationId } = userDetails;

      await this.ensureMediaViewPermission(mediaId, userId);

      const response = await this.elementFlagService.getByMediaIdAsPromise(
        organizationId,
        mediaId,
      );

      const rawFlags =
        response.result as unknown as ReadElementFlagFromSdkDto[];

      return this.classMapper.mapArray(
        rawFlags,
        ReadElementFlagFromSdkDto,
        ReadElementFlagResponseDto,
      );
    } catch (error) {
      this.logger.error(`Error fetching flags for media ${mediaId}: `, error);
      throw error;
    }
  }
}
