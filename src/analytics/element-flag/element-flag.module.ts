import { Modu<PERSON> } from '@nestjs/common';
import { ElementFlagController } from './element-flag.controller';
import { ElementFlagService } from './element-flag.service';
import { OrganizationUserModule } from '../../account-management/organization/organization-user/organization-user.module';
import { ElementFlagAuthorizationService } from './element-flag-auth.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PlatformAdAccount } from '../../entities/platform-ad-account.entity';
import { ElementFlagProfile } from './mappers/element-flag.profile';

@Module({
  imports: [
    TypeOrmModule.forFeature([PlatformAdAccount]),
    OrganizationUserModule,
  ],
  controllers: [ElementFlagController],
  providers: [
    ElementFlagService,
    ElementFlagAuthorizationService,
    ElementFlagProfile,
  ],
  exports: [ElementFlagService, ElementFlagAuthorizationService],
})
export class ElementFlagModule {}
