// file: element-flag.profile.ts

import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Mapper, createMap, forMember, mapFrom } from '@automapper/core';

import { ReadElementFlagResponseDto } from '../dto/read-element-flag-response.dto';
import { ReadElementFlagFromSdkDto } from '../dto/read-element-flag-from-sdk.dto';

/**
 * ElementFlagProfile maps ElementFlagSdkDto (SDK shape)
 * into ReadElementFlagResponseDto (our internal shape).
 */
@Injectable()
export class ElementFlagProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        ReadElementFlagFromSdkDto, // Source type
        ReadElementFlagResponseDto, // Destination type

        // Convert string -> number
        forMember(
          (dest) => dest.mediaId,
          mapFrom((src) => Number(src.mediaId)),
        ),
        forMember(
          (dest) => dest.userId,
          mapFrom((src) => Number(src.userId)),
        ),

        forMember(
          (dest) => dest.dateCreated,
          mapFrom((src) => new Date(src.dateCreated)),
        ),
        forMember(
          (dest) => dest.lastUpdated,
          mapFrom((src) => new Date(src.lastUpdated)),
        ),

        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.id),
        ),
        forMember(
          (dest) => dest.organizationId,
          mapFrom((src) => src.organizationId),
        ),
        forMember(
          (dest) => dest.status,
          mapFrom((src) => src.status),
        ),
        forMember(
          (dest) => dest.tagValue,
          mapFrom((src) => src.tagValue),
        ),
        forMember(
          (dest) => dest.tagType,
          mapFrom((src) => src.tagType),
        ),
        forMember(
          (dest) => dest.flagSource,
          mapFrom((src) => src.flagSource),
        ),
        forMember(
          (dest) => dest.flagReason,
          mapFrom((src) => src.flagReason),
        ),
        forMember(
          (dest) => dest.timestampsFlagged,
          mapFrom((src) => src.timestampsFlagged ?? []),
        ),
        forMember(
          (dest) => dest.additionalNotes,
          mapFrom((src) => src.additionalNotes),
        ),
        forMember(
          (dest) => dest.platformAccountId,
          mapFrom((src) => src.platformAccountId),
        ),
        forMember(
          (dest) => dest.platform,
          mapFrom((src) => src.platform),
        ),
      );
    };
  }
}
