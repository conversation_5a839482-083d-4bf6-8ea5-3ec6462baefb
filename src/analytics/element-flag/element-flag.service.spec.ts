import { Test, TestingModule } from '@nestjs/testing';
import { ElementFlagService } from './element-flag.service';
import { ElementFlagService as ElementFlagServiceSDK } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ElementFlagAuthorizationService } from './element-flag-auth.service';
import { MediaService as MediaServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { CreateElementFlagDto } from './dto/create-element-flag.dto';
import { UserOrganizationDetailsDto } from './dto/user-organization-details.dto';
import { getMapperToken } from '@automapper/nestjs';

describe('ElementFlagService', () => {
  let service: ElementFlagService;
  let sdkService: ElementFlagServiceSDK;
  let mediaService: MediaServiceSDK;

  const fakeMapper = {
    mapArray: jest.fn().mockImplementation((source: any[]) => source),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ElementFlagService,
        {
          provide: ElementFlagServiceSDK,
          useValue: {
            createAsPromise: jest.fn(),
            getByMediaIdAsPromise: jest.fn(),
          },
        },
        {
          provide: MediaServiceSDK,
          useValue: {
            validatePersonCanViewMediaAsPromise: jest.fn().mockResolvedValue({
              result: { canView: true },
            }),
          },
        },
        {
          provide: ElementFlagAuthorizationService,
          useValue: {
            validateUserOrganizationAccess: jest
              .fn()
              .mockResolvedValue(undefined),
          },
        },
        {
          provide: getMapperToken(),
          useValue: fakeMapper,
        },
      ],
    }).compile();

    service = module.get<ElementFlagService>(ElementFlagService);
    sdkService = module.get<ElementFlagServiceSDK>(ElementFlagServiceSDK);
    mediaService = module.get<MediaServiceSDK>(MediaServiceSDK);
  });

  describe('createElemementFlag', () => {
    it('should validate media access and call sdk.createAsPromise with the correct payload', async () => {
      const flagCreationData: CreateElementFlagDto = {
        mediaId: 1234,
        tagValue: 'BLUE',
        tagType: 'COLOR',
        flagReason: 'UNCLEAR',
        flagSource: 'ANALYTICS',
        timestampsFlagged: [5, 8],
        additionalNotes: 'Test note.',
      };

      const userDetails: UserOrganizationDetailsDto = {
        organizationId: 'org-123',
        userId: 1234,
      };

      const expectedPayload = {
        ...flagCreationData,
        organizationId: userDetails.organizationId,
        userId: userDetails.userId,
        flagSource: flagCreationData.flagSource,
      };

      (
        mediaService.validatePersonCanViewMediaAsPromise as jest.Mock
      ).mockResolvedValue({
        result: { canView: true },
      });

      (sdkService.createAsPromise as jest.Mock).mockResolvedValue({
        id: 'new-flag-id',
      });

      const result = await service.createElemementFlag(
        flagCreationData,
        userDetails,
      );

      expect(
        mediaService.validatePersonCanViewMediaAsPromise,
      ).toHaveBeenCalledWith(flagCreationData.mediaId, userDetails.userId);
      expect(sdkService.createAsPromise).toHaveBeenCalledWith(expectedPayload);
      expect(result).toEqual({ id: 'new-flag-id' });
    });
  });
});
