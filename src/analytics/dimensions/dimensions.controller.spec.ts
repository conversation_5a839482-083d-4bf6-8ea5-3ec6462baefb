import { DimensionsController } from './dimensions.controller';
import { DimensionsService } from './dimensions.service';
import { Test, TestingModule } from '@nestjs/testing';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { mockRequest } from '../saved-report/mocks/analytics-report.mock';
import { GetDimensionsRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

describe('DimensionsController', () => {
  let dimensionsController: DimensionsController;
  let reportSpendPermissionService: ReportSpendPermissionService;
  const mockGetDimensions = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DimensionsController],
      providers: [
        {
          provide: DimensionsService,
          useValue: {
            getDimensionsForUser: mockGetDimensions,
          },
        },
        {
          provide: ReportSpendPermissionService,
          useValue: {
            validateOrganizationAccessToSpendKpi: jest.fn(),
          },
        },
      ],
    }).compile();

    dimensionsController =
      module.get<DimensionsController>(DimensionsController);
    reportSpendPermissionService = module.get<ReportSpendPermissionService>(
      ReportSpendPermissionService,
    );
  });

  it('should call getDimensions with basic request', async () => {
    const requestDto: GetDimensionsRequestDto = {
      kpiId: '7',
      startDate: '2023-01-01',
      endDate: '2024-01-01',
      adAccountIds: ['***************'],
      filters: {
        platformMediaIds: ['***************:f2e28cceadc17f43fa95c06f4505c1a8'],
      },
      platform: Platform.FACEBOOK,
    };
    await dimensionsController.getDimensions(
      mockRequest,
      'organizationId',
      requestDto,
    );
    expect(mockGetDimensions).toBeCalled();
    expect(
      reportSpendPermissionService.validateOrganizationAccessToSpendKpi,
    ).toBeCalledWith(requestDto.kpiId, requestDto.platform, 'organizationId');
  });
});
