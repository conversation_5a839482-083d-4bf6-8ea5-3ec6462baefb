import {
  GetDimensionsRequestDto,
  GetMediaDecayRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Injectable, Logger } from '@nestjs/common';
import { DimensionsService as DimensionsServiceSDK } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { UserDetailsDto } from '../dto/user-details.dto';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';

@Injectable()
export class DimensionsService {
  private readonly logger = new Logger(DimensionsService.name);

  constructor(
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly dimensionServiceSDK: DimensionsServiceSDK,
  ) {}

  async getDimensionsForUser(
    getDimensionsRequestDto: GetDimensionsRequestDto,
    userDetails: UserDetailsDto,
  ) {
    const { adAccountIds, workspaceIds, offset, perPage } =
      getDimensionsRequestDto || {};
    if (!adAccountIds?.length || !workspaceIds?.length) {
      throw new Error(
        'adAccountIds and workspaceIds are required to get dimensions',
      );
    }

    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      userDetails,
      workspaceIds,
      adAccountIds,
    );

    return this.dimensionServiceSDK.getDimensionsAsPromise(
      getDimensionsRequestDto,
      offset,
      perPage,
    );
  }

  async getMediaDecayAndValidateUserAccess(
    getMediaDecayRequestDto: GetMediaDecayRequestDto,
    userDetails: UserDetailsDto,
  ) {
    const { adAccountIds, workspaceIds } = getMediaDecayRequestDto || {};
    if (!adAccountIds?.length || !workspaceIds?.length) {
      throw new Error(
        'adAccountIds and workspaceIds are required to get media decay data',
      );
    }

    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      userDetails,
      workspaceIds,
      adAccountIds,
    );

    return this.dimensionServiceSDK.getMediaDecayAsPromise(
      getMediaDecayRequestDto,
    );
  }
}
