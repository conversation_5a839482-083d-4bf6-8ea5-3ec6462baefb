import { DimensionsService } from './dimensions.service';
import { Test, TestingModule } from '@nestjs/testing';
import { GetDimensionsRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { mockUserDetails } from '../saved-report/mocks/analytics-report.mock';
import { DimensionsService as DimensionsServiceSDK } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';

describe('DimensionsService', () => {
  let dimensionsService: DimensionsService;
  let analyticsUserService: AnalyticsUserService;
  let dimensionServiceSDK: DimensionsServiceSDK;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DimensionsService,
        {
          provide: AnalyticsUserService,
          useValue: {
            validateUserAccessToAdAccountsAndWorkspaces: jest.fn(),
          },
        },
        {
          provide: DimensionsServiceSDK,
          useValue: {
            getDimensionsAsPromise: jest
              .fn()
              .mockResolvedValue(['dimension1', 'dimension2']),
          },
        },
      ],
    }).compile();

    dimensionsService = module.get<DimensionsService>(DimensionsService);
    analyticsUserService =
      module.get<AnalyticsUserService>(AnalyticsUserService);
    dimensionServiceSDK =
      module.get<DimensionsServiceSDK>(DimensionsServiceSDK);
  });

  it('should call getDimensionsForUser', async () => {
    const requestDto: GetDimensionsRequestDto = {
      kpiId: '7',
      startDate: '2023-01-01',
      endDate: '2024-01-01',
      adAccountIds: ['***************'],
      filters: {
        platformMediaIds: ['***************:f2e28cceadc17f43fa95c06f4505c1a8'],
      },
      platform: Platform.FACEBOOK,
      workspaceIds: [23142],
      perPage: 8,
      offset: 0,
    };
    const validateUserAccessSpy = jest.spyOn(
      analyticsUserService,
      'validateUserAccessToAdAccountsAndWorkspaces',
    );
    const getDimensionsSpy = jest.spyOn(
      dimensionServiceSDK,
      'getDimensionsAsPromise',
    );

    await dimensionsService.getDimensionsForUser(requestDto, mockUserDetails);

    expect(validateUserAccessSpy).toHaveBeenCalledWith(
      mockUserDetails,
      requestDto.workspaceIds,
      requestDto.adAccountIds,
    );
    expect(getDimensionsSpy).toHaveBeenCalledWith(requestDto, 0, 8);
  });
});
