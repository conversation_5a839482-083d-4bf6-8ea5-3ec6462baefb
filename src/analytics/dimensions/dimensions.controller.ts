import { Body, Controller, Param, Post, Request } from '@nestjs/common';
import { DimensionsService } from './dimensions.service';
import {
  GetDimensionsRequestDto,
  GetMediaDecayRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

@ApiTags('Dimensions')
@ApiSecurity('Bearer Token')
@Controller('dimensions/organization/:organizationId')
export class DimensionsController {
  constructor(
    private readonly dimensionService: DimensionsService,
    private readonly reportSpendPermissionService: ReportSpendPermissionService,
  ) {}

  /**
   * Endpoint to query for advideo or ad dimensions and metrics data
   * @param req
   * @param organizationId
   * @param getDimensionsRequestDto
   */
  @ApiParam({
    name: 'organizationId',
    description: 'Organization ID to query dimensions for',
    required: true,
  })
  @Post()
  async getDimensions(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() getDimensionsRequestDto: GetDimensionsRequestDto,
  ) {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      getDimensionsRequestDto.kpiId,
      getDimensionsRequestDto.platform,
      organizationId,
    );

    return this.dimensionService.getDimensionsForUser(
      getDimensionsRequestDto,
      userDetails,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'Organization ID to query dimensions for',
    required: true,
  })
  @Post('media-decay')
  async getMediaDecay(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() getMediaDecayRequestDto: GetMediaDecayRequestDto,
  ) {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      getMediaDecayRequestDto.kpiId,
      getMediaDecayRequestDto.platform,
      organizationId,
    );

    return this.dimensionService.getMediaDecayAndValidateUserAccess(
      getMediaDecayRequestDto,
      userDetails,
    );
  }
}
