import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { DimensionsService } from './dimensions.service';
import { DimensionsController } from './dimensions.controller';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

@Module({
  imports: [HttpModule, TypeOrmModule.forFeature([])],
  providers: [
    AnalyticsUserService,
    AuthService,
    DimensionsService,
    WorkspaceService,
    ReportSpendPermissionService,
  ],
  controllers: [DimensionsController],
})
export class DimensionsModule {}
