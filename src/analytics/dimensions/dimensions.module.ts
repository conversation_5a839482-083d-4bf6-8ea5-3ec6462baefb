import { Module } from '@nestjs/common';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { DimensionsService } from './dimensions.service';
import { DimensionsController } from './dimensions.controller';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from '../../auth/services/auth.service';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';
import { AccountManagementModule } from '../../account-management/account-management.module';

@Module({
  imports: [HttpModule, AccountManagementModule, TypeOrmModule.forFeature([])],
  providers: [
    AnalyticsUserService,
    AuthService,
    DimensionsService,
    ReportSpendPermissionService,
  ],
  controllers: [DimensionsController],
})
export class DimensionsModule {}
