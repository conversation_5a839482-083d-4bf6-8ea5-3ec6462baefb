import { Test, TestingModule } from '@nestjs/testing';
import { NormativePerformanceService } from './normative-performance.service';
import { PlatformMetadataService } from '../../platform/metadata/platform-metadata.service';
import {
  NormativePerformanceService as NormativePerformanceServiceSDK,
  ReportFiltersService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Platform } from '../../constants/platform.constants';
import {
  expectedEmptyResponse,
  expectedMockGetNormativePerformanceAsPromiseArg,
  expectedNormativeScopes,
  expectGetObjectivesByNormativeObjectiveGroupIds,
  mockCountryNames,
  mockGetIndustryAndSubIndustryNamesByIdsResult,
  mockNormativePerformanceRequest,
} from './mocks/mock-normative-performance';

describe('NormativePerformanceService', () => {
  let service: NormativePerformanceService;
  const mockGetNormativePerformanceAsPromise = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NormativePerformanceService,
        {
          provide: NormativePerformanceServiceSDK,
          useValue: {
            getNormativePerformanceAsPromise:
              mockGetNormativePerformanceAsPromise,
          },
        },
        {
          provide: PlatformMetadataService,
          useValue: {
            getCountryNamesForMarketCodes: jest
              .fn()
              .mockResolvedValue(mockCountryNames),
            getIndustryAndSubIndustryNamesByIds: jest
              .fn()
              .mockResolvedValue(mockGetIndustryAndSubIndustryNamesByIdsResult),
          },
        },
        {
          provide: ReportFiltersService,
          useValue: {
            getObjectiveValuesByNormativeObjectiveGroupIdsAsPromise: jest
              .fn()
              .mockResolvedValue({
                result: expectGetObjectivesByNormativeObjectiveGroupIds,
              }),
          },
        },
      ],
    }).compile();

    service = module.get<NormativePerformanceService>(
      NormativePerformanceService,
    );
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getNormativeScopes', () => {
    it('should return correct array of NormativeScopeDto during the middle of month', () => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      jest.useFakeTimers('modern');
      jest.setSystemTime(new Date(2024, 4, 10));

      const result = service.getNormativeScopes();
      expect(result).toEqual(expectedNormativeScopes);
    });

    it('should return correct array of NormativeScopeDto during last day of the month', () => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      jest.useFakeTimers('modern');
      jest.setSystemTime(new Date(2024, 4, 31));

      const result = service.getNormativeScopes();
      expect(result).toEqual(expectedNormativeScopes);
    });
  });

  describe('getNormativePerformance', () => {
    it('should return empty response if platform does not support normative performance', async () => {
      const request = {
        ...mockNormativePerformanceRequest,
        platform: Platform.REDDIT,
      };
      const response = await service.getNormativePerformance(request, {
        perPage: 10,
        offset: 0,
      });

      expect(mockGetNormativePerformanceAsPromise).not.toHaveBeenCalled();
      expect(response).toEqual(expectedEmptyResponse);
    });

    it('should throw bad request error if api call made with objectives for platform without objectives', async () => {
      const request = {
        ...mockNormativePerformanceRequest,
        platform: Platform.ADWORDS,
        objectiveGroupIds: [1, 2, 3],
      };

      await expect(
        service.getNormativePerformance(request, {
          perPage: 10,
          offset: 0,
        }),
      ).rejects.toThrow('Platform does not support objectives');
    });

    it('should call sdk service with correct parameters', async () => {
      await service.getNormativePerformance(mockNormativePerformanceRequest, {
        perPage: 10,
        offset: 0,
      });

      expect(mockGetNormativePerformanceAsPromise).toHaveBeenCalledWith(
        expectedMockGetNormativePerformanceAsPromiseArg,
        0,
        10,
        undefined,
      );
    });
  });
});
