import { Injectable } from '@nestjs/common';
import { NormativeScopeDto } from '../../dto/normative-scope.dto';
import { getDateString } from '../../utils/date-utils';
import { NormativePerformanceRequestDto } from './dto/normative-performance-request.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { NormativePerformanceForKpiDto } from './dto/normative-performance.dto';
import {
  NormativePerformanceRequestDto as NormativePerformanceRequestDtoSdk,
  NormativePerformanceService as NormativePerformanceServiceSDK,
  ReportFiltersService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PlatformMetadataService } from '../../platform/metadata/platform-metadata.service';
import {
  PLATFORMS_SUPPORT_CAMPAIGN_OBJECTIVE,
  PLATFORMS_SUPPORT_NORMATIVE_PERFORMANCE,
} from '../../constants/platform.constants';

@Injectable()
export class NormativePerformanceService {
  constructor(
    private readonly normativePerformanceServiceSDK: NormativePerformanceServiceSDK,
    private readonly platformMetadataService: PlatformMetadataService,
    private readonly reportFiltersService: ReportFiltersService,
  ) {}

  async getNormativePerformance(
    normativePerformanceRequestDto: NormativePerformanceRequestDto,
    paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<NormativePerformanceForKpiDto>> {
    const { kpiIds, platform, objectiveGroupIds } =
      normativePerformanceRequestDto;

    if (!PLATFORMS_SUPPORT_NORMATIVE_PERFORMANCE.includes(platform)) {
      const emptyResponse: NormativePerformanceForKpiDto[] = kpiIds.map(
        (kpiId) => ({
          kpiId,
          normativePerformance: null,
          impressions: null,
          mediaCount: null,
        }),
      );
      return Promise.resolve(
        new PaginatedResultArray(emptyResponse, emptyResponse.length),
      );
    }

    const isPlatformSupportObjectives =
      PLATFORMS_SUPPORT_CAMPAIGN_OBJECTIVE.includes(platform);
    if (objectiveGroupIds?.length && !isPlatformSupportObjectives) {
      throw new Error('Platform does not support objectives');
    }

    return this.parseAndQueryNormativePerformance(
      normativePerformanceRequestDto,
      paginationOptions,
    );
  }

  private async parseAndQueryNormativePerformance(
    normativePerformanceRequestDto: NormativePerformanceRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    const {
      kpiIds,
      markets,
      platform,
      objectiveGroupIds,
      endDate,
      startDate,
      industryIds,
      currency,
    } = normativePerformanceRequestDto;

    let countryNames = undefined;
    if (markets?.length) {
      countryNames =
        await this.platformMetadataService.getCountryNamesForMarketCodes(
          markets,
        );
    }

    const { industries, subIndustries } =
      await this.platformMetadataService.getIndustryAndSubIndustryNamesByIds(
        industryIds,
      );

    const { result } = objectiveGroupIds
      ? await this.reportFiltersService.getObjectiveValuesByNormativeObjectiveGroupIdsAsPromise(
          platform,
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          objectiveGroupIds,
        )
      : { result: undefined };

    const request: NormativePerformanceRequestDtoSdk = {
      kpiIds,
      countryNames,
      platform:
        platform.toLowerCase() as NormativePerformanceRequestDtoSdk.PlatformEnum,
      objectives: result as string[] | undefined,
      endDate,
      startDate,
      industries,
      subIndustries,
      currency,
    };

    return this.normativePerformanceServiceSDK.getNormativePerformanceAsPromise(
      request,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  getNormativeScopes(): NormativeScopeDto[] {
    const endDate = this.getLastDayOfLastMonth();

    return [
      {
        id: 1,
        scope: 'Last 6 Months',
        scopeKey: 'last_six_months',
        startDate: this.getFirstDateMonthsAgo(endDate, 6),
        endDate,
      },
      {
        id: 2,
        scope: 'Last Year',
        scopeKey: 'last_year',
        startDate: this.getFirstDateMonthsAgo(endDate, 12),
        endDate,
      },
      {
        id: 3,
        scope: 'Last Two Years',
        scopeKey: 'last_two_years',
        startDate: this.getFirstDateMonthsAgo(endDate, 24),
        endDate,
      },
    ];
  }

  private getLastDayOfLastMonth(): string {
    const today = new Date();
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const lastDayOfLastMonth = new Date(
      lastMonth.getFullYear(),
      lastMonth.getMonth() + 1,
      0,
    );

    return getDateString(lastDayOfLastMonth);
  }

  private getFirstDateMonthsAgo(date: string, monthsAgo: number): string {
    // parse as a UTC date
    const inputDate = new Date(date + 'T00:00:00Z');

    let year = inputDate.getUTCFullYear();
    let month = inputDate.getUTCMonth();
    month -= monthsAgo - 1;

    year += Math.floor(month / 12);
    month = ((month % 12) + 12) % 12;

    const dateMonthsAgo = new Date(Date.UTC(year, month, 1));
    return getDateString(dateMonthsAgo);
  }
}
