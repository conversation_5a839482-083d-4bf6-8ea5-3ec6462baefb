import {
  Controller,
  Post,
  Body,
  Get,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import {
  GetPagination,
  PaginationOptions,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { NormativePerformanceRequestDto } from './dto/normative-performance-request.dto';
import { NormativePerformanceForKpiDto } from './dto/normative-performance.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { NormativePerformanceService } from './normative-performance.service';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Normative Performance')
@ApiSecurity('Bearer Token')
@Controller('normative-performance')
export class NormativePerformanceController {
  constructor(
    private readonly normativePerformanceService: NormativePerformanceService,
  ) {}

  @VmApiOkResponse({
    description: 'Return a list of normative performance for given kpis',
  })
  @Post()
  @UsePipes(new ValidationPipe({ transform: true }))
  async getNormativePerformance(
    @Body() normativePerformanceRequestDto: NormativePerformanceRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<PaginatedResultArray<NormativePerformanceForKpiDto>> {
    return this.normativePerformanceService.getNormativePerformance(
      normativePerformanceRequestDto,
      paginationOptions,
    );
  }

  @VmApiOkResponse({
    description:
      'Return the date range options for analytics normative performance',
  })
  @Get('scopes')
  getAnalyticsNormativeScopes() {
    return this.normativePerformanceService.getNormativeScopes();
  }
}
