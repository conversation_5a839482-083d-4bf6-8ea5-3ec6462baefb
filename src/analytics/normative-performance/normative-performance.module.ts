import { Modu<PERSON> } from '@nestjs/common';
import { NormativePerformanceController } from './normative-performance.controller';
import { NormativePerformanceService } from './normative-performance.service';
import { PlatformMetadataService } from '../../platform/metadata/platform-metadata.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Country } from '../../platform/entities/market.entity';
import { Industry } from '../../platform/entities/industry.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Country, Industry])],
  controllers: [NormativePerformanceController],
  providers: [NormativePerformanceService, PlatformMetadataService],
})
export class NormativePerformanceModule {}
