import { NormativePerformanceRequestDto } from '../dto/normative-performance-request.dto';
import { Platform } from '../../../constants/platform.constants';
import { NormativeScopeDto } from '../../../dto/normative-scope.dto';

export const mockNormativePerformanceRequest: NormativePerformanceRequestDto = {
  kpiIds: ['12', '32', '45'],
  platform: Platform.TIKTOK,
  startDate: '2020-01-01',
  endDate: '2021-12-31',
  markets: ['US', 'CA', 'GB', 'AU'],
  industryIds: [1, 2],
  objectiveGroupIds: [1, 2, 3],
};

export const mockCountryNames = [
  'United States',
  'Canada',
  'United Kingdom',
  'Australia',
];

export const mockGetIndustryAndSubIndustryNamesByIdsResult = {
  industries: ['Automotive', 'CPG'],
  subIndustries: ['Beer', 'Soda'],
};

export const expectGetObjectivesByNormativeObjectiveGroupIds = [
  'APP_INSTALL',
  'REACH',
  'VIDEO_VIEWS',
];

export const expectedNormativeScopes: NormativeScopeDto[] = [
  {
    id: 1,
    scope: 'Last 6 Months',
    scopeKey: 'last_six_months',
    startDate: '2023-11-01',
    endDate: '2024-04-30',
  },
  {
    id: 2,
    scope: 'Last Year',
    scopeKey: 'last_year',
    startDate: '2023-05-01',
    endDate: '2024-04-30',
  },
  {
    id: 3,
    scope: 'Last Two Years',
    scopeKey: 'last_two_years',
    startDate: '2022-05-01',
    endDate: '2024-04-30',
  },
];

export const expectedEmptyResponse = {
  items: [
    {
      kpiId: '12',
      normativePerformance: null,
      impressions: null,
      mediaCount: null,
    },
    {
      kpiId: '32',
      normativePerformance: null,
      impressions: null,
      mediaCount: null,
    },
    {
      kpiId: '45',
      normativePerformance: null,
      impressions: null,
      mediaCount: null,
    },
  ],
  totalCount: 3,
};

export const expectedMockGetNormativePerformanceAsPromiseArg = {
  kpiIds: ['12', '32', '45'],
  platform: Platform.TIKTOK,
  countryNames: ['United States', 'Canada', 'United Kingdom', 'Australia'],
  objectives: ['APP_INSTALL', 'REACH', 'VIDEO_VIEWS'],
  startDate: '2020-01-01',
  endDate: '2021-12-31',
  industries: ['Automotive', 'CPG'],
  subIndustries: ['Beer', 'Soda'],
};
