import { NormativePerformanceController } from './normative-performance.controller';
import { NormativePerformanceService } from './normative-performance.service';
import { Test, TestingModule } from '@nestjs/testing';
import { mockNormativePerformanceRequest } from './mocks/mock-normative-performance';

describe('NormativePerformanceController', () => {
  let controller: NormativePerformanceController;
  const mockGetNormativePerformance = jest.fn();
  const mockGetAnalyticsNormativeScopes = jest.fn();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NormativePerformanceController],
      providers: [
        {
          provide: NormativePerformanceService,
          useValue: {
            getNormativePerformance: mockGetNormativePerformance,
            getNormativeScopes: mockGetAnalyticsNormativeScopes,
          },
        },
      ],
    }).compile();

    controller = module.get<NormativePerformanceController>(
      NormativePerformanceController,
    );
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should call getNormativePerformance', async () => {
    await controller.getNormativePerformance(mockNormativePerformanceRequest, {
      perPage: 10,
      offset: 10,
    });

    expect(mockGetNormativePerformance).toHaveBeenCalledWith(
      mockNormativePerformanceRequest,
      { perPage: 10, offset: 10 },
    );
  });

  it('should return call getAnalyticsNormativeScopes', () => {
    controller.getAnalyticsNormativeScopes();

    expect(mockGetAnalyticsNormativeScopes).toHaveBeenCalled();
  });
});
