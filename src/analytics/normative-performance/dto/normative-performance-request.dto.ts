import { Platform } from '../../../constants/platform.constants';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { NormativePerformanceRequestDto as NormativePerformanceRequestSDK } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Transform } from 'class-transformer';
import CurrencyCodeEnum = NormativePerformanceRequestSDK.CurrencyEnum;

export class NormativePerformanceRequestDto {
  /**
   * Platform to fetch the normative performance for
   * @example 'facebook'
   */
  @IsEnum(Platform)
  @IsNotEmpty()
  @Transform(({ value }) => value.toLowerCase())
  platform: Platform;

  /**
   * Start date to filter the normative performance data
   * Should be start of a month
   * @example '2020-01-01'
   */
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  /**
   * End date to filter the normative performance data
   * Should be end of a month
   * @example '2021-12-31'
   */
  @IsDateString()
  @IsNotEmpty()
  endDate: string;

  /**
   * Kpi ids to fetch normative performance for
   */
  @IsString({ each: true })
  @IsNotEmpty()
  kpiIds: string[];

  /**
   * Industry ids to filter the normative performance data
   * @example [1, 21, 33]
   */
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  industryIds: number[];

  /**
   * Market codes to filter the normative performance data
   * @example ['usa', 'deu', 'gbr]
   */
  @IsString({ each: true })
  @IsOptional()
  markets?: string[];

  /**
   * Objective group ids to filter the normative performance data. Must be one of [1, 2, 3]
   * @example [1, 2, 3]
   */
  @IsNumber(
    { allowNaN: false, allowInfinity: false, maxDecimalPlaces: 0 },
    { each: true },
  )
  @IsOptional()
  objectiveGroupIds?: number[];

  /**
   * Currency to convert the normative performance data to
   */
  @IsOptional()
  @IsEnum(CurrencyCodeEnum)
  currency?: CurrencyCodeEnum;
}
