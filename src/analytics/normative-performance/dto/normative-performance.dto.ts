import { <PERSON><PERSON>otE<PERSON>y, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';

import { AnalyticsKpiFormat } from '../../kpi/analytics-kpi.enum';

export class NormativePerformanceForKpiDto {
  /**
   * KPI id
   * @example "20"
   */
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  kpiId: string;

  /**
   * KPI value
   * @example 7.28377489929
   */
  @AutoMap()
  @IsNumber()
  normativePerformance: number | null;

  /**
   * total impressions for the normative data
   * @example 188092
   */
  @AutoMap()
  @IsNumber()
  impressions: number | null;

  /**
   * total media count for the normative data
   * @example 100
   */
  @AutoMap()
  @IsNumber()
  mediaCount: number | null;

  @IsOptional()
  format?: AnalyticsKpiFormat;
}
