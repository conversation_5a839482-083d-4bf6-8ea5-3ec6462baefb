import { Document } from 'mongodb';
import { AnalyticsCopilotConversationRole } from '../enums/analytics-copilot-conversation-role.enum';
import { Feedback } from '../../brands-copilot/model/feedback.model';
import { AnalyticsCopilotInsightFunctionResponse } from '../dto/analytics-copilot-insight.dto';

/**
 * Represents a single message in a normative conversation.
 */
export interface NormativeConversationMessage {
  /** Unique identifier for the message */
  id: string;

  /** Role of the message sender (e.g., USER, ASSISTANT) */
  role: AnalyticsCopilotConversationRole;

  /** Content of the message */
  content: string;

  /** Timestamp of the message */
  timestamp: Date;

  /** Optional feedback associated with the message */
  feedback?: Feedback;

  /** Optional insight linked to the message */
  insight?: AnalyticsCopilotInsightFunctionResponse;

  /** Additional comments provided by the sender */
  additionalComments?: string;

  /** Optional reason type for feedback */
  reasonType?: string;
}

/**
 * Represents a normative conversation, including metadata and messages.
 */
export interface NormativeConversation {
  /** Unique identifier for the conversation */
  id: string;

  /** ID of the user participating in the conversation */
  userId: string;

  /** ID of the organization associated with the conversation */
  organizationId: string;

  /** Title of the conversation */
  title: string;

  /** List of messages in the conversation */
  messages: NormativeConversationMessage[];

  /** Creation timestamp of the conversation */
  timestamp: Date;

  /** Timestamp of the last interaction in the conversation */
  lastInteractionDate: Date;

  /** Flag indicating whether the conversation is deleted */
  deleted?: boolean;
}

/**
 * Represents a MongoDB document for storing a normative conversation.
 * Maps the `id` field of `NormativeConversation` to MongoDB's `_id` field.
 */
export interface NormativeConversationDocument
  extends Omit<NormativeConversation, 'id'>, // Replace `id` with `_id` for MongoDB compatibility
    Document {
  /** MongoDB document ID */
  _id: string;
}
