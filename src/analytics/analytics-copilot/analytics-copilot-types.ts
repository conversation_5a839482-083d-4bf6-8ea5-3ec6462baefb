import { KPI_CATEGORIES } from './enums/analytics-copilot-kpi.enum';

type KPI_TYPE = string;

export type KPI_TYPES = {
  [K in KPI_CATEGORIES]?: KPI_TYPE | undefined;
};

export type InsightType = {
  title: string;
  finding: string;
  recommendation: string;
  elements: ElementType[];
  id: string;
};

type ElementType = {
  element: string;
  kpi_lift: string;
  element_avg_kpi: string;
  tag_type: string;
};

export interface MissingFieldsInputs {
  platformEnum?: string;
  industryName?: string;
  kpiName?: string;
  kpiInfo?: {
    bigQueryFormula?: string;
    inverseHealth?: boolean;
  };
  startDate?: string;
  endDate?: string;
}
