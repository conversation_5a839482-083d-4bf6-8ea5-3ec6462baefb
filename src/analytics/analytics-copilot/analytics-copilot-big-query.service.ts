import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  VidMobBigQueryProvider,
  bigQueryProviderName,
} from '@vidmob/vidmob-nestjs-common';
import { BIGQUERY_READ_ONLY_PROVIDER_NAME } from './constants/analytics-copilot.constants';
import {
  AnalyticsCopilotInsightBigQueryItem,
  AnalyticsCopilotInsightBigQueryItemV2,
  AnalyticsCopilotTagInsightBigQueryItem,
} from './dto/analytics-copilot-insight.dto';
import {
  getInsightQueryByVersion,
  getTagInsightsQuery,
} from './utils/query.utils';

@Injectable()
export class CopilotBigQueryService {
  private readonly logger = new Logger(CopilotBigQueryService.name);

  constructor(
    @Inject(bigQueryProviderName(BIGQUERY_READ_ONLY_PROVIDER_NAME))
    private readonly bigQueryService: VidMobBigQueryProvider,
  ) {}

  async queryInsights(parameters: {
    platform: string;
    industry: string;
    startDate: string;
    endDate: string;
    formula: string;
    inverseHealth: boolean;
    version: number;
  }): Promise<
    | AnalyticsCopilotInsightBigQueryItem[]
    | AnalyticsCopilotInsightBigQueryItemV2[]
  > {
    const { query, params } = getInsightQueryByVersion(parameters);

    this.logger.log(`Analytics-copilot-get-insights-final-query: ${query}`);

    try {
      const [job] = await this.bigQueryService.createQueryJob({
        query,
        params,
      });

      const [rows] = await job.getQueryResults();
      return rows
        ? (rows as
            | AnalyticsCopilotInsightBigQueryItem[]
            | AnalyticsCopilotInsightBigQueryItemV2[])
        : [];
    } catch (error) {
      const errorMsg = `Failed to query big query. Query sql: ${query}. ${error}`;
      this.logger.error(errorMsg);
      return [];
    }
  }

  async queryInsightsByTag(parameters: {
    platform: string;
    industry: string;
    startDate: string;
    endDate: string;
    formula: string;
    tag_hierarchy_level_3: string;
    tag_hierarchy_level_4?: string;
  }): Promise<AnalyticsCopilotTagInsightBigQueryItem[]> {
    const { query, params } = getTagInsightsQuery(parameters);

    this.logger.log(
      `Analytics-copilot-get-insights-by-tag-final-query: ${query}`,
    );

    try {
      const [job] = await this.bigQueryService.createQueryJob({
        query,
        params,
      });

      const [rows] = await job.getQueryResults();
      return rows ? (rows as AnalyticsCopilotTagInsightBigQueryItem[]) : [];
    } catch (error) {
      const errorMsg = `Failed to query big query. Query sql: ${query}. ${error}`;
      this.logger.error(errorMsg);
      return [];
    }
  }
}
