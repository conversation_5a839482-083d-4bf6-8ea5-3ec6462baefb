import { Injectable } from '@nestjs/common';
import { ElasticsearchService } from './analytics-copilot-elastic-search.service';
import {
  AnalyticsCopilotConversationListItem,
  AnalyticsCopilotConversationHistory,
} from './dto/analytics-copilot-conversation.dto';
import {
  FeedbackReasonType,
  FeedbackScore,
} from './enums/analytics-copilot-conversation-message-feedback.enum';

@Injectable()
export class ConversationService {
  constructor(private readonly elasticsearchService: ElasticsearchService) {}
  async getConversationList(
    userId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    return this.elasticsearchService.getConversationList(userId);
  }

  async renameChat({
    userId,
    chatId,
    newTitle,
  }: {
    userId: string;
    chatId: string;
    newTitle: string;
  }) {
    return this.elasticsearchService.renameChatTitle({
      userId,
      chatId,
      newTitle,
    });
  }

  async deleteChat({ userId, chatId }: { userId: string; chatId: string }) {
    return this.elasticsearchService.deleteChat({ userId, chatId });
  }

  async getConversationHistory(props: {
    userId: string;
    chatId: string;
  }): Promise<AnalyticsCopilotConversationHistory[]> {
    const { userId, chatId } = props;

    const conversation = await this.elasticsearchService.getConversationHistory(
      userId,
      chatId,
    );
    const enhancedConversation = await Promise.all(
      conversation.map(async (message) => {
        let insightData;
        if (message.id) {
          insightData = await this.elasticsearchService.getInsightsByMessageId(
            message.id,
          );
        }

        return insightData && insightData.length > 0
          ? { ...message, content: insightData }
          : message;
      }),
    );

    return enhancedConversation;
  }

  async updateMessageFeedback(props: {
    userId: string;
    chatId: string;
    messageId: string;
    feedback: FeedbackScore;
    additionalComments?: string;
    reasonType?: FeedbackReasonType;
  }): Promise<boolean> {
    return this.elasticsearchService.updateMessageFeedback(props);
  }
}
