export enum KPI_CATEGORIES {
  click = 'click',
  view = 'view',
  conversion = 'conversion',
  engagement = 'engagement rate, total interactions',
  cpm = 'CPM, cost per thousand impressions',
  cpc = 'CPC, cost per click',
  cpp = 'CPP, cost per purchase',
  earlyVTR = '2 or 3 second view through rate',
  VTR25 = 'View Through to 25%, View Through Rate, VTR',
  VTR50 = 'View Through to 50%',
  VTR75 = 'View Through to 75%',
  VTR95 = 'View Through to 95%',
  VTR100 = 'View Through to 100%',
  purchase = 'Purchase rate, conversion purchase rate',
  landing = 'Landing page rate, page visit rate, web conversion: visits',
  VVR = 'trueview view rate, video view rate',
  purchaseROAS = 'Purchase ROAS, purchase return on ad spend',
}
