export const fetchTagInsightsQueryAndParams = (params: {
  formula: string;
  tableName: string;
  startDate: string;
  endDate: string;
  impressionColumn: string;
  industry: string;
  tag_hierarchy_level_3: string;
  tag_hierarchy_level_4?: string;
}) => {
  const {
    formula,
    tableName,
    startDate,
    endDate,
    impressionColumn,
    industry,
    tag_hierarchy_level_3: hierarchyLevel3,
    tag_hierarchy_level_4: hierarchyLevel4,
  } = params;

  const hierarchy4 = hierarchyLevel4
    ? `tag_hierarchy_level_4 = '${hierarchyLevel4}'`
    : 'tag_hierarchy_level_4 IS NULL';

  const tableVersion = '_v2';

  return {
    query: `
WITH
  kpi_calculations AS (
      SELECT
        advideo,
        platform_account_id,
        sum(${impressionColumn}) as advideo_impressions,
        -- Expression is retrieved and plugged in
        ${formula} AS kpi
      FROM
        norms_creative_trends.${tableName}_norms_creative_trends${tableVersion}
      WHERE
      -- Filters from user
        week_date between @startDate and @endDate
        AND industry = @industry
        -- Ensure divisor is at least 1000 to exclude weird performance data
        AND ${impressionColumn} >= 1000
      GROUP BY 1,2  
    ),

-- Filter advideos with KPIs within 95th percentile to remove outliers
-- and reduce extreme lift values
kpi_filtered AS (
SELECT
    *
FROM
    kpi_calculations
WHERE
    kpi <= (SELECT APPROX_QUANTILES(kpi, 100)[OFFSET(95)] FROM kpi_calculations)
),


-- Compute average of filtered advideos (excludes outliers)
-- This is a single value - it's the average for the sample 
avg_kpi_cte AS (
SELECT
    AVG(kpi) AS avg_kpi_all_records,
    count(distinct advideo) as sample_advideo_count,
    sum(advideo_impressions) as sample_impressions,
    count(distinct platform_account_id) as sample_num_accounts
FROM
    kpi_filtered
),

-- From the filtered advideos, get advideos that contain the particular item in the hierarchy
advideos_with_hierarchy_item AS (
SELECT
    pnct.advideo,
    pnct.tag_hierarchy_level_1 as creative_aperture_category,
    pnct.tag_hierarchy_level_3,
    IFNULL(pnct.tag_hierarchy_level_4, '') as tag_hierarchy_level_4,
    value,
    type,
    avg_kpi_cte.avg_kpi_all_records,
    (kf.kpi - avg_kpi_cte.avg_kpi_all_records) / avg_kpi_cte.avg_kpi_all_records * 100 as advideo_with_element_lift,
    kf.kpi,
    kf.advideo_impressions
FROM
    kpi_filtered kf,
    avg_kpi_cte
INNER JOIN
    norms_creative_trends.${tableName}_norms_creative_trends${tableVersion} pnct on kf.advideo = pnct.advideo
WHERE
-- User's element category choice
    tag_hierarchy_level_3 = '${hierarchyLevel3}'
    and ${hierarchy4}
GROUP BY all
),

avg_lift_for_category as (
SELECT
    creative_aperture_category, 
    tag_hierarchy_level_3,
    tag_hierarchy_level_4,
    avg(advideo_with_element_lift) as category_lift,
    avg(kpi) as avg_kpi_for_category
FROM
    advideos_with_hierarchy_item
GROUP BY 1,2,3
)

SELECT 
ahi.creative_aperture_category,
ahi.tag_hierarchy_level_3,
ahi.tag_hierarchy_level_4,
akc.sample_advideo_count,
akc.sample_impressions,
akc.sample_num_accounts,
ahi.avg_kpi_all_records,
ac.category_lift,
ac.avg_kpi_for_category,
ahi.value,
ahi.type,
avg(ahi.advideo_with_element_lift) as avg_tag_lift,
avg(ahi.kpi) as avg_kpi_for_tag,
count(distinct ahi.advideo) as num_advideos_contain_element,
sum(advideo_impressions) as num_impressions_for_element
FROM
advideos_with_hierarchy_item ahi,
avg_kpi_cte akc
INNER JOIN avg_lift_for_category ac on ac.creative_aperture_category = ahi.creative_aperture_category
and ac.tag_hierarchy_level_3 = ahi.tag_hierarchy_level_3 and ac.tag_hierarchy_level_4 = ahi.tag_hierarchy_level_4
GROUP BY 1,2,3,4,5,6,7,8,9,10,11
;`,
    params: { startDate, endDate, industry },
  };
};
