export const getInsightQueryAndParamsV1 = (params: {
  formula: string;
  tableName: string;
  startDate: string;
  endDate: string;
  impressionColumn: string;
  industry: string;
  inverseHealth: boolean;
}) => {
  const {
    formula,
    tableName,
    startDate,
    endDate,
    impressionColumn,
    industry,
    inverseHealth,
  } = params;
  return {
    query: `
  -- Calculate the KPI for all advideos, all individual dates
  WITH kpi_calculations AS (
    SELECT
      advideo,
      ${formula} AS kpi
    FROM
      norms_creative_trends.${tableName}_norms_creative_trends
    WHERE 
    -- User specifications
      week_date between @startDate and @endDate
    AND industry = @industry
      -- Ensure divisor is at least 100 to exclude weird performance data
    AND ${impressionColumn} >= 1000
  ),

  -- Filter advideos with KPIs within 95th percentile to remove outliers
  -- and reduce extreme lift values
  kpi_filtered AS (
    SELECT
      advideo,
      kpi
    FROM
      kpi_calculations
    WHERE
      kpi <= (SELECT APPROX_QUANTILES(kpi, 100)[OFFSET(95)] FROM kpi_calculations)
  ),

  -- Compute average of filtered advideos (excludes outliers)
  avg_kpi_cte AS (
    SELECT
      AVG(kpi) AS avg_kpi_all_records
    FROM
      kpi_filtered
  ),

  -- Retrieve top 300 advideos and calculate their average KPI
  -- for the dates active
  top_advideos AS (
    SELECT
      advideo,
      avg(kpi) as avg_advideo_kpi_through_time
    FROM
      kpi_filtered
    GROUP BY
      advideo
    ORDER BY
      avg_advideo_kpi_through_time  ${inverseHealth ? 'ASC' : 'DESC'}
    LIMIT 300
  ), 

  -- Combine top advideos and average KPI
  -- Filter to ensure advideos' KPIs are greater than average if sample is small
  filtered_top_advideos AS (
    SELECT
      top_advideos.advideo,
      top_advideos.avg_advideo_kpi_through_time,
      avg_kpi_cte.avg_kpi_all_records
    FROM
      top_advideos,
      avg_kpi_cte
    WHERE
      top_advideos.avg_advideo_kpi_through_time  ${
        inverseHealth ? '<' : '>'
      } avg_kpi_cte.avg_kpi_all_records
  )

  -- Select the corresponding elements and hierarchy for the top-performing advideos
  SELECT  
      pnct.tag,
      CONCAT(
          pnct.tag_hierarchy_level_1, ",",
          pnct.tag_hierarchy_level_2, ",",
          pnct.tag_hierarchy_level_3, ",",
          pnct.tag_hierarchy_level_4
      ) AS tag_hierarchy,
      filtered_top_advideos.avg_kpi_all_records,
      COUNT(DISTINCT filtered_top_advideos.advideo) AS number_of_advideos_contain_element,
      AVG(filtered_top_advideos.avg_advideo_kpi_through_time) AS avg_kpi_advideos_contain_element,
      ((AVG(filtered_top_advideos.avg_advideo_kpi_through_time) - filtered_top_advideos.avg_kpi_all_records) / filtered_top_advideos.avg_kpi_all_records) * 100 AS kpi_lift_compared_to_sample_average
  FROM
      norms_creative_trends.${tableName}_norms_creative_trends pnct 
  INNER JOIN 
      filtered_top_advideos ON pnct.advideo = filtered_top_advideos.advideo
  GROUP BY 
      pnct.tag, tag_hierarchy, filtered_top_advideos.avg_kpi_all_records
  HAVING 
      number_of_advideos_contain_element >= 5;
  `,
    params: { startDate, endDate, industry },
  };
};
