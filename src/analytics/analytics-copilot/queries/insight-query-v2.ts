export const getInsightQueryAndParamsV2 = (params: {
  formula: string;
  tableName: string;
  startDate: string;
  endDate: string;
  impressionColumn: string;
  industry: string;
  inverseHealth: boolean;
}) => {
  const {
    formula,
    tableName,
    startDate,
    endDate,
    impressionColumn,
    industry,
    inverseHealth,
  } = params;

  const tableVersion = '_v2';

  return {
    query: `
 WITH
  kpi_calculations AS (
      SELECT
        advideo,
        platform_account_id,
        sum(${impressionColumn}) as advideo_impressions,
        -- Expression is retrieved and plugged in
        ${formula} AS kpi,
      FROM
        norms_creative_trends.${tableName}_norms_creative_trends${tableVersion}
      WHERE 
      -- Filters from user
        week_date between @startDate and @endDate
        AND industry = @industry
        -- Ensure divisor is at least 1000 to exclude weird performance data
        AND ${impressionColumn} >= 1000
      GROUP BY 1,2
    ),

-- Filter advideos with KPIs within 95th percentile to remove outliers
-- and reduce extreme lift values
kpi_filtered AS (
 SELECT
    *
  FROM
    kpi_calculations
  WHERE
    kpi <= (SELECT APPROX_QUANTILES(kpi, 100)[OFFSET(95)] FROM kpi_calculations)
),

-- Compute average of filtered advideos (excludes outliers)
-- This is a single value - it's the average for the sample 
avg_kpi_cte AS (
  SELECT
    AVG(kpi) AS avg_kpi_all_records,
    count(distinct advideo) as sample_advideo_count,
    sum(advideo_impressions) as sample_impressions,
    count(distinct platform_account_id) as sample_num_accounts
  FROM
    kpi_filtered
),

-- Select the 300 top-performing advideos
top_advideos AS (
  SELECT
    *
  FROM
    kpi_filtered
  ORDER BY
    kpi ${inverseHealth ? 'ASC' : 'DESC'}
    LIMIT 300
),

-- Get the tag/hierarchy data for all advideos within sample
low_level_creative_elements AS 
(
  SELECT  
        pnct.advideo,
        pnct.tag_hierarchy_level_1 as creative_aperture_category,
        pnct.tag_hierarchy_level_3,
        -- Replace null with empty string so it's not dropped in downstream joins
        IFNULL(pnct.tag_hierarchy_level_4, '') as tag_hierarchy_level_4,
        min(pnct.type) as type,
        pnct.value,
        avg_kpi_cte.avg_kpi_all_records,
        -- Calculate lift for the tag/advideo against the sample average
        (top_advideos.kpi - avg_kpi_cte.avg_kpi_all_records) / avg_kpi_cte.avg_kpi_all_records * 100 as tag_lift,
        -- Retrieve the KPI for the tag/advideo
        top_advideos.kpi as kpi_for_element,
        top_advideos.advideo_impressions
    FROM
        norms_creative_trends.${tableName}_norms_creative_trends${tableVersion} pnct, 
        avg_kpi_cte
    INNER JOIN 
        top_advideos ON pnct.advideo = top_advideos.advideo and pnct.platform_account_id = top_advideos.platform_account_id
    WHERE pnct.type != 'LOGO'
    -- Group by all hierarchy cols except type to deduplicate values
    GROUP BY
        pnct.advideo, pnct.value, pnct.tag_hierarchy_level_1, pnct.tag_hierarchy_level_2, pnct.tag_hierarchy_level_3,
        pnct.tag_hierarchy_level_4, pnct.tag_hierarchy_level_5, pnct.tag_hierarchy_level_6, avg_kpi_cte.avg_kpi_all_records,
        tag_lift, kpi_for_element, top_advideos.advideo_impressions
        ),

category_level_elements AS
(
  SELECT
    creative_aperture_category,
    tag_hierarchy_level_3,
    tag_hierarchy_level_4,
    avg(tag_lift) as category_lift,
    avg(kpi_for_element) as avg_kpi_for_category,
    ROW_NUMBER() OVER (PARTITION BY creative_aperture_category ORDER BY avg(tag_lift) ${
      inverseHealth ? 'ASC' : 'DESC'
    }) AS category_rank
  FROM low_level_creative_elements
  GROUP BY
    1,2,3
  -- Ensure the category has at least 10 advideos to be included
  HAVING
    count(distinct advideo) >= 10

),

-- Within each aperture, rank the top-performing categories
-- Within each category, rank the top-performing tags
aggregated_categories AS (
  SELECT 
      lle.creative_aperture_category,
      lle.tag_hierarchy_level_3,
      lle.tag_hierarchy_level_4,
      -- Average KPI of sample
      lle.avg_kpi_all_records,
      -- Lift for all advideos in category vs. sample average
      cle.category_lift,
      -- Rank of performance for each category within the an aperture group
      cle.category_rank,
      -- Average KPI for all the advideos within the category (may not be needed, here for convenience)
      cle.avg_kpi_for_category,
      lle.value,
      lle.type,
      -- Rank of performance for the tag within each category
      ROW_NUMBER() OVER (PARTITION BY lle.creative_aperture_category, lle.tag_hierarchy_level_3, lle.tag_hierarchy_level_4  ORDER BY avg(lle.tag_lift)
      ${inverseHealth ? 'ASC' : 'DESC'}) AS tag_rank,
      -- Lift for all advideos in that contain the tag vs. sample average
      avg(lle.tag_lift) as avg_tag_lift,
      -- Avg kpi for advideos that contain the tag
      avg(lle.kpi_for_element) as avg_kpi_for_tag,
      count(distinct advideo) as num_advideos_contain_element,
      sum(advideo_impressions) as num_impressions_for_element
  FROM 
      low_level_creative_elements lle
  INNER JOIN category_level_elements cle on lle.creative_aperture_category = cle.creative_aperture_category
        AND lle.tag_hierarchy_level_3 = cle.tag_hierarchy_level_3
        AND lle.tag_hierarchy_level_4 = cle.tag_hierarchy_level_4
  GROUP BY
    1,2,3,4,5,6,7,8,9
  -- Ensure the tag is present in at least 5 advideos to be included
  HAVING
    num_advideos_contain_element >= 5
)
  
-- Final output: for each aperture category, get the top 3 categories
-- Within those top 3 categories, get the top 3 tags  
-- Note: all aperture categories may not be present
-- Fewer than 3 top categories may be present since they might not have least 10 advideos
-- Fewer than 3 top tags may be present if they're not present in at least 5 advideos
-- For now, ensure lift is positive only. This can be configured to get low performing elements
-- or high performing elements for spend KPIs.
SELECT 
  creative_aperture_category, 
  tag_hierarchy_level_3,
  tag_hierarchy_level_4, 
  sample_advideo_count,
  sample_impressions,
  sample_num_accounts,
  aggregated_categories.avg_kpi_all_records,
  category_lift, 
  value,
  type,
  avg_tag_lift,
  avg_kpi_for_tag,
  num_advideos_contain_element,
  num_impressions_for_element,
  category_rank,
  tag_rank
FROM 
  aggregated_categories,
  avg_kpi_cte
WHERE category_rank <= 2
  AND tag_rank <= 2
  AND avg_tag_lift ${inverseHealth ? '<' : '>'} 0
;
`,
    params: { startDate, endDate, industry },
  };
};
