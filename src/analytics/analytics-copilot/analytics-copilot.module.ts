import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AnalyticsCopilotController } from './analytics-copilot.controller';
import { AnalyticsCopilotController as AnalyticsCopilotControllerV2 } from './v2/analytics-copilot.controller';
import { AnalyticsCopilotService } from './analytics-copilot.service';
import { AnalyticsCopilotService as AnalyticsCopilotServiceV2 } from './v2/analytics-copilot.service';
import { NormativePerformanceService } from '../normative-performance/normative-performance.service';
import { PlatformMetadataService } from 'src/platform/metadata/platform-metadata.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Country } from '../../platform/entities/market.entity';
import { Industry } from '../../platform/entities/industry.entity';
import { ElasticsearchService } from './analytics-copilot-elastic-search.service';
import { OpenAiService } from './analytics-copilot-open-ai.service';
import {
  createBigQueryProvider,
  SecretsManagerModule,
} from '@vidmob/vidmob-nestjs-common';
import { CopilotBigQueryService } from './analytics-copilot-big-query.service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../../auth/services/auth.service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { ScoringAuthService } from '../../scoring/scoring-auth/scoring-auth.service';
import {
  BIGQUERY_LOCATION,
  BIGQUERY_READ_ONLY_PROVIDER_NAME,
} from './constants/analytics-copilot.constants';
import { ConversationService } from './conversation.service';
import { ConversationService as ConversationServiceV2 } from './v2/conversation.service';
import { NormativeInsightsMongoDbRepository } from './repository/normative-insights-mongo-db-repository.service';
import { MongoDbService } from '../brands-copilot/repository/mongo-db.service';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([Country, Industry]),
    SecretsManagerModule,
  ],
  controllers: [AnalyticsCopilotController, AnalyticsCopilotControllerV2],
  providers: [
    createBigQueryProvider(BIGQUERY_READ_ONLY_PROVIDER_NAME, BIGQUERY_LOCATION),
    CopilotBigQueryService,
    ElasticsearchService,
    NormativeInsightsMongoDbRepository,
    MongoDbService,
    OpenAiService,
    AnalyticsCopilotService,
    AnalyticsCopilotServiceV2,
    NormativePerformanceService,
    PlatformMetadataService,
    WorkspaceService,
    ConfigService,
    AuthService,
    OrganizationUserService,
    ScoringAuthService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
    ConversationService,
    ConversationServiceV2,
  ],
})
export class AnalyticsCopilotModule {}
