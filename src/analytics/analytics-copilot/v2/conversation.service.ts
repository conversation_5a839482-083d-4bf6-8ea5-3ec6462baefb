import { Injectable, Logger } from '@nestjs/common';
import { NormativeInsightsMongoDbRepository } from '../repository/normative-insights-mongo-db-repository.service';
import {
  AnalyticsCopilotConversationListItem,
  AnalyticsCopilotConversationHistory,
} from '../dto/analytics-copilot-conversation.dto';
import {
  FeedbackReasonType,
  FeedbackScore,
} from '../enums/analytics-copilot-conversation-message-feedback.enum';
import { InsightType } from '../analytics-copilot-types';

@Injectable()
export class ConversationService {
  private readonly logger = new Logger(ConversationService.name);

  constructor(
    private readonly normativeInsightsRepository: NormativeInsightsMongoDbRepository,
  ) {}

  /**
   * Retrieve a list of conversations for a specific user and organization.
   */
  async getConversationList(
    organizationId: string,
    userId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    try {
      return await this.normativeInsightsRepository.getConversationList(
        organizationId,
        userId,
      );
    } catch (error) {
      this.logger.error(
        `Error fetching conversation list for user ${userId} in organization ${organizationId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Rename a chat by updating its title.
   */
  async renameChat({
    organizationId,
    userId,
    chatId,
    newTitle,
  }: {
    organizationId: string;
    userId: string;
    chatId: string;
    newTitle: string;
  }): Promise<boolean> {
    try {
      const success = await this.normativeInsightsRepository.renameChatTitle({
        organizationId,
        userId,
        chatId,
        newTitle,
      });

      if (!success) {
        this.logger.warn(
          `Chat with ID ${chatId} not found for user ${userId} in organization ${organizationId}`,
        );
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error renaming chat: ${error.message}`);
      return false;
    }
  }

  /**
   * Delete a chat by removing it from the repository.
   */
  async deleteChat({
    organizationId,
    userId,
    chatId,
  }: {
    organizationId: string;
    userId: string;
    chatId: string;
  }): Promise<boolean> {
    try {
      const documentExists =
        await this.normativeInsightsRepository.getConversationHistory(
          organizationId,
          userId,
          chatId,
        );

      if (!documentExists.length) {
        this.logger.warn(`Chat with ID ${chatId} not found.`);
        return false;
      }

      await this.normativeInsightsRepository.deleteChat({
        organizationId,
        userId,
        chatId,
      });
      return true;
    } catch (error) {
      this.logger.error(`Error deleting chat: ${error.message}`);
      return false;
    }
  }

  /**
   * Retrieve the conversation history for a specific chat.
   */
  async getConversationHistory(props: {
    organizationId: string;
    userId: string;
    chatId: string;
  }): Promise<AnalyticsCopilotConversationHistory[]> {
    const { organizationId, userId, chatId } = props;

    try {
      const conversationHistory =
        await this.normativeInsightsRepository.getConversationHistory(
          organizationId,
          userId,
          chatId,
        );

      if (!conversationHistory.length) {
        this.logger.warn(
          `No conversation history found for chat ID ${chatId} in organization ${organizationId}`,
        );
        return [];
      }

      return await Promise.all(
        conversationHistory.map(async (message) => {
          const insightData = await this.getMessageInsights(
            organizationId,
            message.id || '',
          );

          return insightData.length
            ? { ...message, content: insightData }
            : message;
        }),
      );
    } catch (error) {
      this.logger.error(
        `Error fetching conversation history for chat ID ${chatId} in organization ${organizationId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Fetch insights for a message.
   */
  private async getMessageInsights(
    organizationId: string,
    messageId: string,
  ): Promise<InsightType[]> {
    try {
      return await this.normativeInsightsRepository.getInsightsByMessageId(
        organizationId,
        messageId,
      );
    } catch (error) {
      this.logger.error(
        `Error fetching insights for message ID ${messageId} in organization ${organizationId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Update feedback for a specific message in a conversation.
   */
  async updateMessageFeedback(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    messageId: string;
    feedback: FeedbackScore;
    additionalComments?: string;
    reasonType?: FeedbackReasonType;
  }): Promise<boolean> {
    try {
      return await this.normativeInsightsRepository.updateMessageFeedback(
        props,
      );
    } catch (error) {
      this.logger.error(
        `Error updating feedback for message ${props.messageId} in organization ${props.organizationId}: ${error.message}`,
      );
      return false;
    }
  }
}
