import { ApiSecurity, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Request,
} from '@nestjs/common';
import { AnalyticsCopilotService } from '../v2/analytics-copilot.service';
import { ConversationService } from '../v2/conversation.service';
import {
  AnalyticsCopilotNextPayload,
  AnalyticsCopilotNextResponse,
} from '../dto/analytics-copilot-next.dto';
import {
  AnalyticsCopilotConversationHistory,
  AnalyticsCopilotConversationListItem,
} from '../dto/analytics-copilot-conversation.dto';
import { CONVERSATION_STARTERS } from '../constants/analytics-copilot.constants';
import { UpdateMessageFeedbackDto } from '../dto/update-message-feedback.dto';
import { validateQueryParam } from '../utils/validate-query-param.utils';

@ApiTags('Copilot')
@ApiSecurity('Bearer Token')
@Controller({
  path: 'analytics-copilot',
  version: '2',
})
export class AnalyticsCopilotController {
  constructor(
    private readonly analyticsCopilotService: AnalyticsCopilotService,
    private readonly conversationService: ConversationService,
  ) {}

  @Post('next')
  async getLLMResponseV2(
    @Request() request: any,
    @Body()
    body: AnalyticsCopilotNextPayload & { organizationId: string },
  ): Promise<AnalyticsCopilotNextResponse> {
    const { userId } = request;
    const { userInput, chatId, organizationId } = body;
    const host = request.get('host');
    return await this.analyticsCopilotService.getLLMResponse({
      userInput,
      userId,
      chatId,
      organizationId,
      host,
      version: 2,
    });
  }

  @Get('conversation-list')
  async getConversationList(
    @Request() request: any,
    @Query('organizationId') organizationId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    validateQueryParam(organizationId, 'organizationId', true);
    const { userId } = request;
    return await this.conversationService.getConversationList(
      organizationId,
      userId,
    );
  }

  @Patch('conversation/:chatId/rename')
  async renameConversation(
    @Request() request: any,
    @Param('chatId') chatId: string,
    @Body() body: { newTitle: string },
    @Query('organizationId') organizationId: string,
  ): Promise<boolean> {
    validateQueryParam(organizationId, 'organizationId', true);
    const { userId } = request;
    const { newTitle } = body;
    return await this.conversationService.renameChat({
      userId,
      chatId,
      newTitle,
      organizationId,
    });
  }

  @Delete('conversation/:chatId')
  async deleteConversation(
    @Request() request: any,
    @Param('chatId') chatId: string,
    @Query('organizationId') organizationId: string,
  ): Promise<boolean> {
    validateQueryParam(organizationId, 'organizationId', true);
    const { userId } = request;
    return await this.conversationService.deleteChat({
      userId,
      chatId,
      organizationId,
    });
  }

  @Get('conversation-history/:chatId')
  async getConversationHistory(
    @Request() request: any,
    @Param('chatId') chatId: string,
    @Query('organizationId') organizationId: string,
  ): Promise<AnalyticsCopilotConversationHistory[]> {
    validateQueryParam(organizationId, 'organizationId', true);
    const { userId } = request;
    return await this.conversationService.getConversationHistory({
      userId,
      chatId,
      organizationId,
    });
  }

  @Post('update-message-feedback/:chatId/:messageId')
  async updateMessageFeedback(
    @Request() request: any,
    @Param('chatId') chatId: string,
    @Param('messageId') messageId: string,
    @Body() body: UpdateMessageFeedbackDto,
    @Query('organizationId') organizationId: string,
  ): Promise<boolean> {
    validateQueryParam(organizationId, 'organizationId', true);
    const { userId } = request;
    const { feedback, additionalComments, reasonType } = body;
    return await this.conversationService.updateMessageFeedback({
      userId,
      chatId,
      messageId,
      feedback,
      additionalComments,
      reasonType,
      organizationId,
    });
  }

  @Get('conversation-starters')
  async getConversationStarters(): Promise<string[]> {
    return CONVERSATION_STARTERS;
  }
}
