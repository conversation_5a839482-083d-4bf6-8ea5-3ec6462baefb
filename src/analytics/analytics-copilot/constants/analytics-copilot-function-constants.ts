import {
  PLATFORMS,
  INDUSTRIES,
  KPI_CATEGORY_NAMES,
  TAG_HIERARCHY,
} from './analytics-copilot.constants';
import { AnalyticsCopilotFunction } from '../enums/analytics-copilot-function.enum';

const FUNCTIONS_PROPERTIES = {
  platform: {
    type: 'string',
    enum: PLATFORMS,
    description: 'the platform e.g Facebook, TikTok, Snapchat',
  },
  industry_name: {
    type: 'string',
    enum: INDUSTRIES,
    description:
      'the industry of the benchmark e.g Automotive, Education, Electronics',
  },
  kpi_category: {
    type: 'string',
    enum: KPI_CATEGORY_NAMES,
    description:
      'the name of the KPI e.g Click Through Rate, View Through Rate',
  },
  tag_hierarchy: {
    type: 'string',
    enum: TAG_HIERARCHY,
    description:
      'the KPI tag category hierarchy e.g Things, Beauty and Personal Care',
  },
  start_date: {
    type: 'string',
    description:
      'this is the start date for the query in the format YYYY-MM-DD e.g 2021-01-01',
  },
  end_date: {
    type: 'string',
    description:
      'this is the end date for the query in the format YYYY-MM-DD e.g 2021-01-01',
  },
};

export const GET_INSIGHTS_FUNCTION = {
  name: AnalyticsCopilotFunction.GET_INSIGHTS,
  description:
    'This function returns a list of the top performing elements in a video ad for the provided platform, industry_name and kpi_name',
  parameters: {
    type: 'object',
    properties: FUNCTIONS_PROPERTIES,
    required: [], // 'platform, industry_name, kpi_name'
  },
};

export const GET_INSIGHTS_BY_TAG_FUNCTION = {
  name: AnalyticsCopilotFunction.GET_INSIGHTS_BY_TAG,
  description:
    'This function returns a list of creative elements and their performance for a creative category (tag_hierarchy) the user specifies. These elements and performance values are for the given platform, industry_name, and kpi_name',
  parameters: {
    type: 'object',
    properties: FUNCTIONS_PROPERTIES,
    required: [], // 'platform, industry_name, kpi_name'
  },
};

export const GET_BENCHMARKS_FUNCTION = {
  name: AnalyticsCopilotFunction.GET_BENCHMARKS,
  description:
    'this function returns the benchmarks/norms/normative data for this platform, kpi_name and industry_name',
  parameters: {
    type: 'object',
    properties: FUNCTIONS_PROPERTIES,
    required: [], // 'platform, industry_name, kpi_name'
  },
};
