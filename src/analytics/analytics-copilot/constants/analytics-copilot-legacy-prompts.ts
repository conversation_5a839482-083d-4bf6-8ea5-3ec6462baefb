// export const USER_PROMPT = `Based on user query,
// try to identify which function the user needs 'get_insights' or 'get_benchmarks,
// ONCE you can identify what function the user needs
// EXTRACT the required properties from user query OR USE THIS DEFAULTS { platform: 'Snapchat', industry_name: '<PERSON>G', kpi_name: 'Swipe Through Rate', start_date: '{start_date}', end_date: '{end_date}'}
// and call the specific function (ALWAYS USE DEFAULTS IF YOU CAN'T IDENTIFY THE PROPERTIES),
// ONLY if you CANT identify what function the user needs,
// answer his question in friendly tone and try to explain that you can provide insights or benchmarks and instruct him how to provide the required properties (DON'T mention JSON).
// process the following user query and respond appropriately: "user_query".
// your response must always be a valid JSON object in this format: { instructions: string }`;

// Provide insights about the creative elements for video ads based on the parameters that represent KPI lift
// by categorizing and grouping the creative elements and how they contribute to the overall effectiveness
// and impact of the advertisement. Use the format of category description and title in one line, then insights,
// and then example elements
// up to 5 insights

// export const INSIGHT_FUNCTION_PROMPT = `Analyze the provided advertising data and answer the user query,
// Provide insights about the creative elements for video ads based on the parameters that represent KPI lift
// by categorizing and grouping the creative elements and how they contribute to the overall effectiveness and impact of the advertisement.

// The "inverseHealth" parameter indicates whether KPI lift and element_avg_kpi values are performing poorly or well:
// - If inverseHealth is true, KPI lift and element_avg_kpi values under 0 indicate better performance (e.g., more negative values suggest elements are more effective).
// - If inverseHealth is false, KPI lift and element_avg_kpi values over 0 indicate better performance (e.g., more positive values suggest elements are more effective).

// Do not include "inverseHealth" in your response.

// Use the format of "category description and title in one condense line without writing category", then insights about why the elements performed well, and then example elements with the kpi_lift.
// Generate up to 5 insights in Markdown format NOT JSON format.
// Your response must always be a valid JSON object in this format: { answer: string }
// Process the following data and respond appropriately -
// user query: "{user_query}".
// inverseHealth: {inverseHealth}.
// advertising data: {insights}.`;

// export const INSIGHT_FUNCTION_PROMPT_V2 = `Analyze the provided advertising data and answer the user query.
// Provide insights about the creative elements using KPI lift for video ads based on the parameters that represent KPI lift
// by categorizing and grouping the creative elements and how they contribute to the overall effectiveness and impact of the advertisement.

// In the data provided, "kpi_lift" refers to the lift for the category and "element_avg_kpi" refers to the lift of the elements within that category.

// The "inverseHealth" parameter indicates whether KPI lift and element_avg_kpi values are performing poorly or well:
// - If inverseHealth is true, KPI lift and element_avg_kpi values under 0 indicate better performance (e.g., more negative values suggest elements are more effective).
// - If inverseHealth is false, KPI lift and element_avg_kpi values over 0 indicate better performance (e.g., more positive values suggest elements are more effective).

// Use the format of "category description title and KPI lift in one condensed line without writing category" then, provide insights about why the elements performed well, and then list example elements with their corresponding element_avg_kpi values.

// Do not include "kpi_lift", "element_avg_kpi" or "inverseHealth" in your response.

// Generate up to 5 insights in Markdown format, NOT JSON format. Your response must always be a valid JSON object in this format: { answer: string }.

// Process the following data and respond appropriately:
// user query: "{user_query}".
// inverseHealth: {inverseHealth}.
// advertising data: {insights}.`;

// export const INSIGHT_FUNCTION_PROMPT = `Analyze the provided advertising data and answer the user query,
// Use this kind of structure for your answer: "[WHAT] may be a key driver of [kpi_name], as seen in the [kpi_lift]% lift for [HOW] which could associate [WHY]".
// WHAT = rephrase the [element_hierarchy] into more meaningful, professional advertising terms.
// HOW = redefine the [element] with an example in a scene, using professional advertising language.
// WHY = create a reason based on the [HOW] that explains its effectiveness or relevance.
// Your insight must always take into consideration creative elements categories in ad videos like: Visual, Audio, Script, Character etc...
// Your response must always be a valid JSON object in this format: { answer: string }
// Process the following data and respond appropriately -
// user query: "{user_query}".
// advertising data: {insights}.`;

// export const INSIGHT_FUNCTION_PROMPT = `Based on the advertising data you need to create one best insight.
// INSIGHT = [What] may be a key driver of {kpi_name}, as seen in the [x% KPI lift] for [the How] which could associate [the Why]
// Use the tag_hierarchy column to help with generalizing your insights - the information contains categories for each element separated by commas.
// Include your strongest and most creative insight.
// Avoid making the insight about elements that are common in the "{industry_name}" industry - for example, if the industry is 'Automotive', avoid considering elements related to cars or car parts.
// This is the general format for an insight summary and finding:
// 'insight_title: [Trend] may be a [leading driver or consequence] of [high or low] {kpi_name}.
// insight_finding: Lean into using [2-3 relevant creative elements to trend] as it [deeper reason why trend is successful/unsuccessful with viewers].
// supporting_data: list(dict(tag: supporting element 1, avg_kpi_advideos_contain_element: element kpi value, kpi_lift_compared_to_sample_average: lift value), dict(tag: supporting element 2, avg_kpi_advideos_contain_element: element kpi value, kpi_lift_compared_to_sample_average: lift value), dict(tag: supporting element 3, avg_kpi_advideos_contain_element: element kpi value, kpi_lift_compared_to_sample_average: lift value))' Add variation in the language used for the insight_finding. Do not include more than 3 supporting data points.
// YOUR RESPONSE MUST always be a valid JSON object in this format: { insight_title: string, insight_finding: string, supporting_data: list(dict) }.
// Process the following advertising data and respond appropriately: "{insights}".`;

// export const SYSTEM_PROMPT = `You are an professional agent designed to provide insights/trends/analyses on advertising videos always answer in a professional advertising language.`;

// export const BENCHMARK_FUNCTION_PROMPT = `Based on function content,
// answer the user query and include the following properties:
// platform: "{platform}", industry_name: "{industry_name}", kpi_name: "{kpi_name}", normativePerformance: "{normativePerformance}%".
// YOU MUST MENTION the time frame for this query start_date: "{start_date}", end_date: "{end_date}".
// Format the dates appropriately so 2021-01-01 is January 1, 2021.
// your response must always be a valid JSON object in this format: { answer: string },
// with markdown formatting, percentage return ONLY in this format ![number%](positive ? #009f00 : #9f0000) and only mention number once.
// If the number has more than two decimal places round to the nearest hundredth.
// Do not include any undefined properties in the response.
// Also bold the properties in markdown.`;
