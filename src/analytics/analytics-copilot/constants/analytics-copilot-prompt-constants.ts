/* System prompt */
export const SYSTEM_PROMPT = `You are a professional agent designed to provide insights on creative  elements of video. answer using creative element categories in the video ads.`;

/* User prompt */
export const USER_PROMPT = `Based on user query,
Try to identify if the user is interested in insights/trends/analyses of ad videos.
If the user seems to be looking for insights on a specific tag_hierarchy element category call 'get_insights_by_tag' function. Else you should call 'get_insights' function.
Else YOU MUST answer to user query in professional advertising tone.
Process the following user query and respond appropriately: "user_query".
Your response must always be a valid JSON object in this format: { answer: string }.

Today's date is ${new Date().toISOString().split('T')[0]}`;

/* Benchmark function prompt */
export const BENCHMARK_FUNCTION_PROMPT = `Based on the function content answer the user query strictly using the following template using markdown format: 
Channel, Date range and Industry should be on a bullet list with a bullet before each of the property names. Format the dates appropriately so 2021-01-01 is Jan 1, 2021.
"For the KPI: **{kpi_name}**, and within the following scope:
* **Channel**: {platform}
* **Date range**: {start_date} - {end_date}
* **Industry**: {industry_name}
/n/n
The average performance for your KPI conversion is **{normativePerformance}**."
Your response must always be a valid JSON object in this format: { answer: string }.
`;

/* Base prompt */

export const BASE_PROMPT = `Analyze the provided advertising data and answer the user query,
Provide insights about the creative elements for video ads based on the parameters that represent KPI performance
by categorizing and grouping the creative elements and how they contribute to the overall effectiveness and impact of the advertisement.
{inverseHealth}
{replace}
Generate a maximum of {numberOfInsights} insights in Markdown format NOT JSON format.
Your response must always be a valid JSON object in this format: {format}
Process the following data and respond appropriately -
user query: "{user_query}".
advertising data: {insights}.
`;

/* Insight prompts */

export const INVERSE_HEALTH_DIRECTIONS = `This is important to remember: "category_lift" and "kpi_lift" values under 0 indicate better performance (e.g., more negative values suggest elements or categories are more effective).
Keep in mind that some categories may have positive values with corresponding elements having negative values. This is normal and indicates that the category is under performing, but some elements within the category are performing well.`;
export const NO_INVERSE_HEALTH_DIRECTIONS = `Values over 0 indicate better performance (e.g., more positive values suggest elements or categories are more effective).
Keep in mind that some categories may have negative values with corresponding elements having positive values. This is normal and indicates that the category is under performing, but some elements within the category are performing well.`;

export const V1_INSIGHTS_DIRECTIONS = `Use the format of "category description and title in one condense line without writing category", then insights about why the elements performed well, and then example elements with the KPI lift`;
export const V2_INSIGHTS_DIRECTIONS = `
In the data provided, "category_lift" refers to the overall performance of the category and "kpi_lift" refers to the performance impact of the elements within that category.
Do not use "kpi_lift" or "category_lift" in your insights.

The title should be the variable associated with element_hierarchy.
The finding should be a sentence indicating what is driving the kpi impact for only the category include the category percentage and a short interpretation.
The "category_lift" should be formatted like this: ![number%](performing well ? #388E3C : #D32F2F)
The recommendation should be two sentences. One sentence interpreting why the elements performed well.
The second sentence should be an insightful action based on that interpretation.
Finding and recommendation should be in valid markdown format -- bold the elements.
Elements should be an array of the exact objects you used to drive these insights including "element", "kpi_lift", "element_avg_kpi" and "tag_type" properties from the advertising data.
`;

/* Tag insight prompts */

export const NO_INVERSE_HEALTH_TAG_DIRECTIONS = `Group the elements where the "kpi_lift" is greater than the "category_lift" these elements are performing well, provide one insight about all these elements combined, if there are more than 5 elements select 5, if no elements meet this criteria do not provide an insight.
Group the elements where the "category_lift" is greater than the "kpi_lift" these elements are under performing within the category, provide one insight about all these elements combined, if there are more than 5 elements select 5, if no elements meet this criteria do not provide an insight.
`;

export const INVERSE_HEALTH_TAG_DIRECTIONS = `Group the elements where the "category_lift" is greater than the "kpi_lift", these elements are performing well, provide one insight about all these elements combined, if there are more than 5 elements select 5, if no elements meet this criteria do not provide an insight.
Group the elements where the "kpi_lift" is greater than the "category_lift", these elements are under performing within the category, provide one insight about all these elements combined, if there are more than 5 elements select 5, if no elements meet this criteria do not provide an insight.
Remember that positive "category_lift" values indicate worse performance, while negative values indicate better performance. Remember that a negative "category_lift" indicates that the category is performing well.`;

export const V2_INSIGHTS_BY_TAG_DIRECTIONS = `
In the data provided, "category_lift" refers to the overall performance of the category and "kpi_lift" refers to the performance impact of the elements within that category. 
Do not use "kpi_lift" or "category_lift" in your insights.

{inverseHealthTagDirections}
Insights should be returned in the following format:
The title should be the variable associated with element_hierarchy.
The finding should be two sentences one indicating what is driving the kpi impact for only the category include the category percentage ("category_lift") and a short interpretation.
The "category_lift" should be formatted like this: ![number%](performing well ? #388E3C : #D32F2F)
The second sentence should be evaluating the kpi impact of the element within the category. Do not include the "element_avg_kpi" in this sentence.
The recommendation should be two sentences. One sentence interpreting why the elements performed the way they did. 
The second sentence should be an insightful action based on that interpretation.
Finding and recommendation should be in valid markdown format -- bold the elements.
Elements should be an array of the exact objects you used to drive these insights including "element", "kpi_lift", "element_avg_kpi" and "tag_type" properties from the advertising data. 
`;

/* Constants */
export const V1_NUMBER_OF_INSIGHTS = '5';
export const V2_NUMBER_OF_INSIGHTS = '3';
export const V2_NUMBER_OF_INSIGHTS_BY_TAG = '2';

/* Format */
export const V1_FORMAT = `{answer: string}`;
export const V2_FORMAT = `{answer: [title: string, finding: string, recommendation: string, elements: []] }`;
