import { KPI_CATEGORIES } from '../enums/analytics-copilot-kpi.enum';

export const INDUSTRIES = [
  'CPG',
  'Restaurants',
  'Health & Wellness',
  'Real Estate',
  'Utility',
  'Other',
  'Transportation',
  'Entertainment',
  'Financial services',
  'Retail',
  'Pharmaceutical',
  'Technology',
  'Education',
  'Non-Profit',
  'Travel',
  'Government',
  'Alcohol',
  'Home Improvement',
  'Automotive',
  'Electronics',
  'B2B',
];

export const PLATFORMS = [
  'Snapchat',
  'Facebook',
  'TikTok',
  'Pinterest',
  'DV360',
  'LinkedIn',
  'Twitter',
  'Reddit',
  'Adwords',
  'X', // will be mapped to twitter
  'YouTube', // will be mapped to dv360
];

export const KPI_CATEGORY_NAMES: string[] = Object.keys(KPI_CATEGORIES).map(
  (key) => KPI_CATEGORIES[key as keyof typeof KPI_CATEGORIES],
) as string[];

export const CHAT_HISTORY_DB_INDEX = 'chat_history';
export const INSIGHTS_DB_INDEX = 'copilot_insights';

export enum TagHierarchy {
  Things_BeautyAndPersonalCare = 'Things: Beauty and Personal Care',
  Things_KitchenAndDining = 'Things: Kitchen and Dining',
  Things_ToolsAndMachinery = 'Things: Tools and Machinery',
  Things_Education = 'Things: Education',
  Things_EventsAndAttractions = 'Things: Events and Attractions',
  Things_EverydayObjects = 'Things: Everyday Objects',
  Things_Religion = 'Things: Religion',
  Things_ToysAndGaming = 'Things: Toys and Gaming',
  Things_ApparelAndAccessories = 'Things: Apparel and Accessories',
  Things_FurnitureAndFurnishings = 'Things: Furniture and Furnishings',
  Things_HomeAppliances = 'Things: Home Appliances',
  Things_Medical = 'Things: Medical',
  Things_NatureAndOutdoors = 'Things: Nature and Outdoors',
  Things_SymbolsAndFlags = 'Things: Symbols and Flags',
  Specs_TextSize = 'Specs: Text Size',
  Type_Empty = 'Type: ',
  AudioProduction_BeatsPerMinute = 'Audio Production: Beats Per Minute',
  Things_TechnologyAndComputing = 'Things: Technology and Computing',
  Specs_TextVisibility = 'Specs: Text Visibility',
  Things_ExpressionsAndEmotions = 'Things: Expressions and Emotions',
  Things_PublicSafety = 'Things: Public Safety',
  Things_TextAndDocuments = 'Things: Text and Documents',
  Things_Materials = 'Things: Materials',
  Things_PerformingArts = 'Things: Performing Arts',
  Things_PersonDescription = 'Things: Person Description',
  Logo_Logos = 'Logo: Logos',
  Things_HealthAndFitness = 'Things: Health and Fitness',
  Things_PatternsAndShapes = 'Things: Patterns and Shapes',
  Things_TransportAndLogistics = 'Things: Transport and Logistics',
  Hue_Empty = 'Hue: ',
  Transitions_Cut = 'Transitions: Cut',
  AudioProduction_AudioFrequency = 'Audio Production: Audio Frequency',
  Things_DamageDetection = 'Things: Damage Detection',
  Things_Depreciated = 'Things: Depreciated',
  Things_TravelAndAdventure = 'Things: Travel and Adventure',
  Things_WeaponsAndMilitary = 'Things: Weapons and Military',
  SpeechSpecs_Empty = 'Speech Specs: ',
  Things_ColorsAndVisualComposition = 'Things: Colors and Visual Composition',
  Things_HobbiesAndInterests = 'Things: Hobbies and Interests',
  Things_PlantsAndFlowers = 'Things: Plants and Flowers',
  Specs_WordsPerSecond = 'Specs: Words Per Second',
  Things_Products = 'Things: Products',
  Things_FoodAndBeverage = 'Things: Food and Beverage',
  Things_Sports = 'Things: Sports',
  Things_VehiclesAndAutomotive = 'Things: Vehicles and Automotive',
  Temperature_Empty = 'Temperature: ',
  Things_People = 'Things: People',
  Things_BuildingsAndArchitecture = 'Things: Buildings and Architecture',
  Hierarchy_KeyMessaging = 'Hierarchy: Key Messaging',
  Things_AnimalsAndPets = 'Things: Animals and Pets',
  Specs_TextColor = 'Specs: Text Color',
  CTA_CTAPresence = 'CTA: CTA Presence',
  Things_PopularLandmarks = 'Things: Popular Landmarks',
  Saturation_Empty = 'Saturation: ',
  Specs_TextLanguage = 'Specs: Text Language',
  Things_HomeAndIndoors = 'Things: Home and Indoors',
  Things_OfficesAndWorkspaces = 'Things: Offices and Workspaces',
  Contrast_Empty = 'Contrast: ',
  Specs_TextPresence = 'Specs: Text Presence',
}

export const TAG_HIERARCHY = Object.keys(TagHierarchy).map(
  (key) => TagHierarchy[key as keyof typeof TagHierarchy],
);

export const ELASTIC_SEARCH_NODE =
  'https://c3f9c44794c440f8862a5d6cdb895667.us-east-1.aws.found.io:443';

export const ELASTIC_SEARCH_API_KEY =
  'cWZIdmZvMEJhR0hWOFU2MzJCaFE6Z1BJeXhaRjdSUFNiUzliSU9GekpaZw==';

export const ERROR_MESSAGE =
  'Oops! Something went wrong{replace}. Please try again.';

export const NO_RESULTS_MESSAGE =
  'We were unable to provide an answer to your question. Please try asking a different question.';

export const CHAT_HISTORY_LIMIT = 20;

export const MIN_DATE = '2022-01-01';

export const CONVERSATION_STARTERS = [
  'What creative elements boost CTR on Meta for health and wellness campaigns?',
  'What factors influence 3-second VTR on Pinterest for CPG brands?',
  'How does color affect view-through rates on DV360 for retail?',
  'What is the average View Through to 100% on TikTok for the CPG category?',
];

export const BIGQUERY_READ_ONLY_PROVIDER_NAME = 'NORMS_READ_ONLY';

export const BIGQUERY_LOCATION = 'US';

export const INSIGHT_TEXT_TEMPLATE = `**{TITLE}**\n\n\n\n**Finding**\n\n\n\n{FINDING}`;

export const BRAND_INSIGHTS_COLLECTION_PREFIX = 'brandInsights';

export const NORMATIVE_INSIGHTS_COLLECTION_PREFIX = 'normativeInsights';

export const LOCALHOST_MONGODB_URI = 'mongodb://localhost:27017';

export const MONGODB_SEARCH_MAX_SIZE = 1000;

export const MONGODB_REGION = 'us-east-1';
export const MONGODB_SECRET_NAME = 'mongodbCert';
export const MONGODB_VERSION_STAGE = 'AWSCURRENT';
