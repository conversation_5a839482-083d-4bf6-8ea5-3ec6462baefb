import {
  AnalyticsCopilotConversationInsight,
  AnalyticsCopilotConversationTagInsight,
} from '../dto/analytics-copilot-conversation.dto';
import {
  AnalyticsCopilotInsightBigQueryItem,
  AnalyticsCopilotInsightBigQueryItemV2,
  AnalyticsCopilotInsightFunctionResponse,
} from '../dto/analytics-copilot-insight.dto';

import { AnalyticsKpiFormat } from '../../kpi/analytics-kpi.enum';
import { toFixed } from './formatPercentage.utils';

export function getFinalInsights(
  insightObj: AnalyticsCopilotConversationInsight,
): AnalyticsCopilotInsightFunctionResponse {
  if (insightObj.version === 1) {
    return getFinalInsightsForV1(insightObj);
  } else {
    return getFinalInsightsForV2(insightObj);
  }
}

export const getFinalTagInsights = (
  insightObj: AnalyticsCopilotConversationTagInsight,
): AnalyticsCopilotInsightFunctionResponse => {
  return getFinalInsightsForV2(insightObj);
};

const getFinalInsightsForV1 = (
  insightObj: AnalyticsCopilotConversationInsight,
): AnalyticsCopilotInsightFunctionResponse => {
  const { insights, ...others } = insightObj;
  const advertisingData = (
    insights as AnalyticsCopilotInsightBigQueryItem[]
  ).sort(
    (a, b) =>
      b.kpi_lift_compared_to_sample_average -
      a.kpi_lift_compared_to_sample_average,
  );

  const firstItem = advertisingData[0];
  const filtered = advertisingData.filter((data) =>
    Boolean(data.tag_hierarchy && data.tag),
  );

  const result = {
    ...others,
    overall_avg: firstItem?.avg_kpi_all_records,
    advertisingData: filtered
      .map((adData) => {
        return {
          element: adData.tag,
          element_avg_kpi: adData.avg_kpi_advideos_contain_element,
          kpi_lift: `${toFixed(adData.kpi_lift_compared_to_sample_average)}%`,
        };
      })
      .slice(0, 50),
  };

  return result;
};

const getFinalInsightsForV2 = (
  insightObj:
    | AnalyticsCopilotConversationInsight
    | AnalyticsCopilotConversationTagInsight,
): AnalyticsCopilotInsightFunctionResponse => {
  const { insights, ...others } = insightObj;
  const advertisingData = (
    insights as AnalyticsCopilotInsightBigQueryItemV2[]
  ).sort((a, b) => b.category_lift - a.category_lift);

  const filtered = advertisingData.filter((data) =>
    Boolean(data.tag_hierarchy_level_3 && data.value),
  );
  const firstItem = advertisingData[0];

  const result = {
    ...others,
    overall_avg: firstItem?.avg_kpi_all_records,
    level_ad_video_count: firstItem.sample_advideo_count,
    level_impressions: firstItem.sample_impressions,
    account_counts: firstItem.sample_num_accounts,
    level_kpi_value: firstItem.avg_kpi_all_records,
    advertisingData: filtered.map(
      (adData: AnalyticsCopilotInsightBigQueryItemV2) => {
        return {
          element: adData.value,
          element_hierarchy:
            adData.tag_hierarchy_level_4 || adData.tag_hierarchy_level_3,
          category_lift: `${toFixed(adData.category_lift)}%`,
          element_avg_kpi: adData.avg_kpi_for_tag,
          kpi_lift: `${toFixed(adData.avg_tag_lift)}%`,
          tag_type: adData.type,
          element_ad_video_count: adData.num_advideos_contain_element,
          element_impressions: adData.num_impressions_for_element,
          element_percent_lift: adData.avg_tag_lift,
          element_kpi_value: adData.avg_kpi_for_tag,
        };
      },
    ),
  };

  return result;
};

export const formatNormativeInsightByKpiFormat = (
  normativePerformance: number,
  kpiFormat?: AnalyticsKpiFormat,
) => {
  const roundedNormativePerformance = toFixed(normativePerformance);
  switch (kpiFormat) {
    case AnalyticsKpiFormat.PERCENTAGE:
      return `${roundedNormativePerformance}%`;
    case AnalyticsKpiFormat.SPEND:
      return `$${roundedNormativePerformance}`;
    case AnalyticsKpiFormat.SECOND:
      return `${roundedNormativePerformance}s`;
    default:
      return `${roundedNormativePerformance}`;
  }
};
