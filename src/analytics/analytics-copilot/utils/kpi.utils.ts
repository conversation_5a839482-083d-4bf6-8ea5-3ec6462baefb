import { KPI_TYPES } from '../analytics-copilot-types';
import { KPI_CATEGORIES } from '../enums/analytics-copilot-kpi.enum';

const KPI_NAMES: { [key: string]: KPI_TYPES } = {
  facebook: {
    [KPI_CATEGORIES.click]: 'Click Through Rate',
    [KPI_CATEGORIES.view]: '3-second View Through Rate',
    [KPI_CATEGORIES.engagement]: 'Post Engagement Rate',
    [KPI_CATEGORIES.cpm]: 'CPM',
    [KPI_CATEGORIES.cpc]: 'CPC',
    [KPI_CATEGORIES.earlyVTR]: '3-second View Through Rate',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 95%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.cpp]: 'Cost per Purchase',
    [KPI_CATEGORIES.VVR]: 'Video Completion Rate',
    [KPI_CATEGORIES.purchase]: 'Purchase Rate',
    [KPI_CATEGORIES.landing]: 'Landing Page Rate',
    [KPI_CATEGORIES.purchaseROAS]: 'Purchase ROAS',
  },
  tiktok: {
    [KPI_CATEGORIES.click]: 'Click Through Rate',
    [KPI_CATEGORIES.view]: '2-second View Through Rate',
    [KPI_CATEGORIES.conversion]: 'Conversion Rate',
    [KPI_CATEGORIES.cpc]: 'CPC',
    [KPI_CATEGORIES.earlyVTR]: '2-second View Through Rate',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 100%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.cpp]: 'Cost per Purchase',
    [KPI_CATEGORIES.VVR]: 'View Through to 100%',
    [KPI_CATEGORIES.purchase]: 'Purchase Rate',
    [KPI_CATEGORIES.landing]: 'Conversion Rate',
  },
  snapchat: {
    [KPI_CATEGORIES.click]: 'Swipe Through Rate',
    [KPI_CATEGORIES.view]: 'View Through to 25%',
    [KPI_CATEGORIES.conversion]: 'Conversion Purchase Rate',
    [KPI_CATEGORIES.engagement]: 'Total Interactions',
    [KPI_CATEGORIES.cpm]: 'Cost Per Thousand Impressions',
    [KPI_CATEGORIES.cpc]: 'Effective Cost per Swipe',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 100%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.VVR]: 'Video View Rate',
    [KPI_CATEGORIES.purchase]: 'Conversion Purchase Rate',
    [KPI_CATEGORIES.landing]: 'Conversion Page View Rate',
    [KPI_CATEGORIES.purchaseROAS]: 'Purchase ROAS',
  },
  pinterest: {
    [KPI_CATEGORIES.click]: 'Pin Click Rate',
    [KPI_CATEGORIES.view]: '3-second View Through Rate',
    [KPI_CATEGORIES.conversion]: 'Conversion Rate',
    [KPI_CATEGORIES.engagement]: 'Engagement Rate',
    [KPI_CATEGORIES.cpm]: 'CPM',
    [KPI_CATEGORIES.cpc]: 'Cost per Click',
    [KPI_CATEGORIES.earlyVTR]: '3-second View Through Rate',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 95%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.VVR]: 'Video View Rate',
    [KPI_CATEGORIES.purchase]: 'Check Out Rate',
    [KPI_CATEGORIES.landing]: 'Page Visit Rate',
  },
  dv360: {
    [KPI_CATEGORIES.click]: 'Click Through Rate',
    [KPI_CATEGORIES.view]: 'View Through to 25%',
    [KPI_CATEGORIES.engagement]: 'Engagement Rate',
    [KPI_CATEGORIES.cpm]: 'CPM',
    [KPI_CATEGORIES.cpc]: 'CPC',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 100%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.VVR]: 'Trueview View Rate',
    [KPI_CATEGORIES.landing]: 'Click Through Rate',
  },
  linkedin: {
    [KPI_CATEGORIES.click]: 'Click Through Rate',
    [KPI_CATEGORIES.view]: '2-second View Through Rate',
    [KPI_CATEGORIES.conversion]: 'Conversion Rate',
    [KPI_CATEGORIES.engagement]: 'Engagement Rate',
    [KPI_CATEGORIES.cpm]: 'CPM',
    [KPI_CATEGORIES.cpc]: 'CPC',
    [KPI_CATEGORIES.earlyVTR]: '2-second View Through Rate',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 100%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.VVR]: 'Video Completion Rate',
    [KPI_CATEGORIES.landing]: 'Landing Page Click Rate',
  },
  twitter: {
    [KPI_CATEGORIES.click]: 'Clicks Rate',
    [KPI_CATEGORIES.view]: '3-second View Through Rate',
    [KPI_CATEGORIES.engagement]: 'Engagement Rate',
    [KPI_CATEGORIES.cpm]: 'CPM',
    [KPI_CATEGORIES.earlyVTR]: '3-second View Through Rate',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 100%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.VVR]: 'View Through to 6sec',
    [KPI_CATEGORIES.purchase]: 'Web Conversion: Purchases',
    [KPI_CATEGORIES.landing]: 'Web Conversion: Visits',
  },
  reddit: {
    [KPI_CATEGORIES.click]: 'Click Through Rate',
    [KPI_CATEGORIES.view]: '3-second View Through Rate',
    [KPI_CATEGORIES.cpm]: 'eCPM',
    [KPI_CATEGORIES.cpc]: 'CPC',
    [KPI_CATEGORIES.earlyVTR]: '3-second View Through Rate',
  },
  adwords: {
    [KPI_CATEGORIES.click]: 'Click Through Rate',
    [KPI_CATEGORIES.view]: 'View Through to 25%',
    [KPI_CATEGORIES.conversion]: 'Conversion Rate',
    [KPI_CATEGORIES.engagement]: 'Engagement Rate',
    [KPI_CATEGORIES.cpm]: 'Average CPM',
    [KPI_CATEGORIES.cpc]: 'Average CPC',
    [KPI_CATEGORIES.VTR25]: 'View Through to 25%',
    [KPI_CATEGORIES.VTR50]: 'View Through to 50%',
    [KPI_CATEGORIES.VTR75]: 'View Through to 75%',
    [KPI_CATEGORIES.VTR95]: 'View Through to 100%',
    [KPI_CATEGORIES.VTR100]: 'View Through to 100%',
    [KPI_CATEGORIES.VVR]: 'Video View Rate',
    [KPI_CATEGORIES.landing]: 'Active View CTR',
  },
};

export const getKpiNameFromCategoryAndPlatform = (
  category?: KPI_CATEGORIES,
  platformName?: string,
): string | undefined => {
  if (!category || !platformName) return;
  const platformKey = platformName.toLowerCase();
  const platform = KPI_NAMES[platformKey];

  if (platform) {
    const kpiName = platform[category];

    if (!kpiName) {
      throw new Error(
        `I'm sorry. KPI not found corresponding to ${category} on ${platformName} platform`,
      );
    }

    return kpiName;
  }

  return undefined;
};
