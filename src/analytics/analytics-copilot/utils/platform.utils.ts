import { Platform } from 'src/constants/platform.constants';

const platformMap: { [key: string]: string } = {
  x: 'twitter',
  youtube: 'dv360',
};

const platformDisplayNames: { [key: string]: string } = {
  snapchat: 'Snapchat',
  tiktok: 'TikTok',
  pinterest: 'Pinterest',
  linkedin: 'LinkedIn',
  twitter: 'X',
  youtube: 'DV360',
  facebook: 'Meta',
  adwords: 'Adwords',
  reddit: 'Reddit',
  dv360: 'DV360',
  x: 'X',
};

export function mapPlatformToDisplayName(platformString: string): string {
  const platform = platformString.toLowerCase();

  return platformDisplayNames[platform] || platformString;
}

export function getPlatform(platformString?: string): Platform | undefined {
  if (!platformString) {
    return undefined;
  }

  const mappedPlatformString =
    platformMap[platformString.toLowerCase()] || platformString.toLowerCase();

  const platformEnumValues = Object.values(Platform);
  if (platformEnumValues.includes(mappedPlatformString as Platform)) {
    return Platform[
      mappedPlatformString.toUpperCase() as keyof typeof Platform
    ];
  }

  return undefined;
}

export function getPlatformNameForDBTable(platform: string): string {
  if (platform === 'snapchat') {
    return 'snap';
  }

  return platform;
}

export function getImpressionColumnForPlatform(platform: string): string {
  if (platform === 'tiktok') {
    return 'show_cnt';
  } else if (platform === 'pinterest') {
    return 'impression_1';
  }

  return 'impressions';
}
