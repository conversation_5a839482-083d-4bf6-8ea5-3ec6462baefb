import { BadRequestException } from '@nestjs/common';

/**
 * Utility to check if a query parameter is present and valid.
 * @param value - The value to validate.
 * @param paramName - The name of the parameter for error messages.
 */
function validatePresence(value: any, paramName: string): void {
  // Handle null, undefined, or "null" string explicitly
  if (!value || value === 'null') {
    throw new BadRequestException(
      `The query parameter "${paramName}" is required and cannot be null or empty.`,
    );
  }
}

/**
 * Utility to validate if a value is a valid UUID.
 * @param value - The value to validate.
 * @param paramName - The name of the parameter for error messages.
 */
function validateUUID(value: string, paramName: string): void {
  const uuidRegex =
    /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

  if (!uuidRegex.test(value)) {
    throw new BadRequestException(
      `The query parameter "${paramName}" must be a valid UUID.`,
    );
  }
}

/**
 * Combined utility for validating presence and optionally validating UUID format.
 * @param value - The value to validate.
 * @param paramName - The name of the parameter for error messages.
 * @param validateAsUUID - Whether to validate the value as a UUID.
 */
export function validateQueryParam(
  value: any,
  paramName: string,
  validateAsUUID = false,
): void {
  validatePresence(value, paramName); // Always validate presence
  if (validateAsUUID) {
    validateUUID(value, paramName); // Validate UUID only if specified
  }
}
