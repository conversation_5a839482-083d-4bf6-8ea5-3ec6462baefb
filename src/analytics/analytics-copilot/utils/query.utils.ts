import { fetchTagInsightsQueryAndParams } from '../queries/tag-insight-query';
import {
  getImpressionColumnForPlatform,
  getPlatformNameForDBTable,
} from './platform.utils';
import { getInsightQueryAndParamsV1 } from '../queries/insight-query-v1';
import { getInsightQueryAndParamsV2 } from '../queries/insight-query-v2';

export const getTagInsightsQuery = (params: {
  platform: string;
  industry: string;
  startDate: string;
  endDate: string;
  formula: string;
  tag_hierarchy_level_3: string;
  tag_hierarchy_level_4?: string;
}): {
  query: string;
  params: { startDate: string; endDate: string; industry: string };
} => {
  const {
    platform,
    industry,
    startDate,
    endDate,
    formula,
    tag_hierarchy_level_3,
    tag_hierarchy_level_4,
  } = params;
  const tableName = getPlatformNameForDBTable(platform);
  const impressionColumn = getImpressionColumnForPlatform(platform);

  const queryResult = fetchTagInsightsQueryAndParams({
    formula,
    tableName,
    startDate,
    endDate,
    impressionColumn,
    industry,
    tag_hierarchy_level_3,
    tag_hierarchy_level_4,
  });

  return queryResult;
};

export const getInsightQueryByVersion = (params: {
  platform: string;
  industry: string;
  startDate: string;
  endDate: string;
  formula: string;
  inverseHealth: boolean;
  version: number;
}): {
  query: string;
  params: { startDate: string; endDate: string; industry: string };
} => {
  const {
    platform,
    industry,
    startDate,
    endDate,
    formula,
    version,
    inverseHealth,
  } = params;
  const tableName = getPlatformNameForDBTable(platform);
  const impressionColumn = getImpressionColumnForPlatform(platform);
  let queryResult = { query: '', params: { startDate, endDate, industry } };

  if (version === 1) {
    queryResult = getInsightQueryAndParamsV1({
      formula,
      tableName,
      startDate,
      endDate,
      impressionColumn,
      industry,
      inverseHealth,
    });
  } else if (version === 2) {
    queryResult = getInsightQueryAndParamsV2({
      formula,
      tableName,
      startDate,
      endDate,
      impressionColumn,
      industry,
      inverseHealth,
    });
  }

  return queryResult;
};
