import { MIN_DATE } from 'class-validator';

export function getLastYear(): { startDate: string; endDate: string } {
  const endDate = new Date();
  const startDate = new Date();

  startDate.setMonth(endDate.getMonth() - 12);

  const format = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  return {
    startDate: format(startDate),
    endDate: format(endDate),
  };
}

export function getFormattedStartDate(value?: string): string {
  const { startDate } = getLastYear();
  if (!value) {
    return startDate;
  }

  try {
    const minDate = new Date(MIN_DATE);
    const date = new Date(value);

    if (date.getTime() < minDate.getTime()) {
      // if the date is before the minimum date, return the minimum date as default for start date
      return startDate;
    }

    return date.toISOString().split('T')[0];
  } catch (e) {
    // on any error return the last year as default for start date
    return startDate;
  }
}

export function getFormattedEndDate(value?: string): string {
  const { endDate } = getLastYear();
  if (!value) {
    return endDate;
  }

  try {
    const today = new Date();
    const date = new Date(value);

    if (date.getTime() > today.getTime()) {
      // if the date is in the future, return today as default for end date
      return endDate;
    }

    return date.toISOString().split('T')[0];
  } catch (e) {
    // on any error return today as default for end date
    return endDate;
  }
}

export function formatDate(value?: string) {
  const date = new Date(value || '');
  const monthNames = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sept',
    'Oct',
    'Nov',
    'Dec',
  ];
  const day = date.getUTCDate();
  const monthIndex = date.getUTCMonth();
  const year = date.getUTCFullYear();

  return `${monthNames[monthIndex]} ${day}, ${year}`;
}
