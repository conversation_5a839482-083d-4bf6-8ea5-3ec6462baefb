import {
  Is<PERSON>num,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { KPI_CATEGORIES } from '../enums/analytics-copilot-kpi.enum';
import { AnalyticsKpiFormat } from '../../kpi/analytics-kpi.enum';

export class AnalyticsCopilotBenchmark {
  @IsNotEmpty()
  @IsString()
  platform: string;

  @IsNotEmpty()
  @IsString()
  industry_name: string;

  @IsNotEmpty()
  @IsString()
  kpi_category: KPI_CATEGORIES;

  @IsNotEmpty()
  @IsString()
  start_date: string;

  @IsNotEmpty()
  @IsString()
  end_date: string;
}

export class AnalyticsCopilotBenchmarkFunctionResponse {
  @IsString()
  platform: string;

  @IsString()
  industry_name: string;

  @IsString()
  kpi_name: string;

  @IsString()
  start_date: string;

  @IsString()
  end_date: string;

  @IsNumber()
  normativePerformance: number;

  @IsOptional()
  @IsEnum(AnalyticsKpiFormat)
  format?: AnalyticsKpiFormat;
}
