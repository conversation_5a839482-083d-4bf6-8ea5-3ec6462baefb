import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AnalyticsCopilotInsightFunctionResponse } from './analytics-copilot-insight.dto';
import { InsightType } from '../analytics-copilot-types';

export class AnalyticsCopilotNextResponse {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  answer: string | InsightType[];

  @IsOptional()
  @IsString()
  log?: string;

  @IsOptional()
  @IsString()
  messageId?: string;

  @IsOptional()
  @Type(() => AnalyticsCopilotInsightFunctionResponse)
  insight?: AnalyticsCopilotInsightFunctionResponse;
}

export class AnalyticsCopilotNextPayload {
  @IsNotEmpty()
  @IsString()
  userInput: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsString()
  @IsOptional()
  chatId?: string;
}
