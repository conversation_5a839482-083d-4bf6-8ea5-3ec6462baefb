import { Type } from 'class-transformer';
import {
  <PERSON><PERSON><PERSON>y,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { KPI_CATEGORIES } from '../enums/analytics-copilot-kpi.enum';

export class AnalyticsCopilotInsightSupportingDataPoint {
  @IsNotEmpty()
  @IsString()
  tag: string;

  @IsNotEmpty()
  @IsNumber()
  avg_kpi_advideos_contain_element: number;

  @IsNotEmpty()
  @IsNumber()
  kpi_lift_compared_to_sample_average: number;
}

class AnalyticsCopilotInsightSupportingData {
  @IsOptional()
  @IsNumber()
  avg_kpi_all_records?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AnalyticsCopilotInsightSupportingDataPoint)
  elements?: AnalyticsCopilotInsightSupportingDataPoint[];
}

export class AnalyticsCopilotInsight {
  @IsNotEmpty()
  @IsString()
  platform: string;

  @IsNotEmpty()
  @IsString()
  industry_name: string;

  @IsOptional()
  @IsString()
  industry_id?: number;

  @IsOptional()
  @IsString()
  kpi_category?: KPI_CATEGORIES;

  @IsNotEmpty()
  @IsString()
  kpi_name: string;

  @IsOptional()
  @IsString()
  kpi_id?: string;

  @IsNotEmpty()
  @IsString()
  start_date: string;

  @IsNotEmpty()
  @IsString()
  end_date: string;

  @IsOptional()
  @IsNotEmpty()
  @Type(() => AnalyticsCopilotInsightSupportingData)
  supporting_data?: AnalyticsCopilotInsightSupportingData;

  @IsOptional()
  @IsString()
  tag_hierarchy?: string;
}

export class AnalyticsCopilotInsightBigQueryItem {
  @IsNotEmpty()
  @IsString()
  tag: string;

  @IsNotEmpty()
  @IsString()
  tag_hierarchy: string;

  @IsNotEmpty()
  @IsNumber()
  avg_kpi_all_records: number;

  @IsNotEmpty()
  @IsNumber()
  number_of_advideos_contain_element: number;

  @IsNotEmpty()
  @IsNumber()
  avg_kpi_advideos_contain_element: number;

  @IsNotEmpty()
  @IsNumber()
  kpi_lift_compared_to_sample_average: number;
}

export class AnalyticsCopilotInsightBigQueryBase {
  @IsNotEmpty()
  @IsString()
  creative_aperture_category: string;

  @IsNotEmpty()
  @IsString()
  tag_hierarchy_level_3: string;

  @IsOptional()
  @IsString()
  tag_hierarchy_level_4?: string;

  @IsNotEmpty()
  @IsNumber()
  avg_kpi_all_records: number;

  @IsNotEmpty()
  @IsNumber()
  category_lift: number;

  @IsNotEmpty()
  @IsString()
  value: string;

  @IsNotEmpty()
  @IsString()
  type: string;

  @IsNotEmpty()
  @IsNumber()
  avg_tag_lift: number;

  @IsNotEmpty()
  @IsNumber()
  avg_kpi_for_tag: number;
}

export class AnalyticsCopilotInsightBigQueryItemV2 extends AnalyticsCopilotInsightBigQueryBase {
  @IsNotEmpty()
  @IsString()
  category_rank: string;

  @IsNotEmpty()
  @IsString()
  tag_rank: string;

  @IsOptional()
  @IsNumber()
  sample_advideo_count?: number;

  @IsOptional()
  @IsNumber()
  sample_impressions?: number;

  @IsOptional()
  @IsNumber()
  sample_num_accounts?: number;

  @IsOptional()
  @IsNumber()
  num_advideos_contain_element?: number;

  @IsOptional()
  @IsNumber()
  num_impressions_for_element?: number;
}

export class AnalyticsCopilotTagInsightBigQueryItem extends AnalyticsCopilotInsightBigQueryBase {
  @IsNotEmpty()
  @IsNumber()
  avg_kpi_for_category: number;
}

export class AnalyticsCopilotInsightBigQueryResponseItem {
  @IsNotEmpty()
  @IsString()
  element: string;

  @IsNotEmpty()
  @IsString()
  kpi_lift: string;

  @IsNotEmpty()
  @IsNumber()
  element_avg_kpi: number;

  @IsOptional()
  @IsString()
  element_hierarchy?: string;

  @IsOptional()
  @IsString()
  category_lift?: string;

  @IsOptional()
  @IsString()
  tag_type?: string;

  @IsOptional()
  @IsNumber()
  element_ad_video_count?: number;

  @IsOptional()
  @IsNumber()
  element_impressions?: number;

  @IsOptional()
  @IsNumber()
  element_percent_lift?: number;

  @IsOptional()
  @IsNumber()
  element_kpi_value?: number;
}

export class AnalyticsCopilotInsightFunctionResponse {
  @IsNotEmpty()
  @IsString()
  platform: string;

  @IsNotEmpty()
  @IsString()
  industry_name: string;

  @IsNotEmpty()
  @IsString()
  kpi_name: string;

  @IsString()
  @IsNotEmpty()
  start_date: string;

  @IsNumber()
  @IsNotEmpty()
  overall_avg: number;

  @IsString()
  @IsNotEmpty()
  end_date: string;

  @IsNumber()
  @IsOptional()
  level_ad_video_count?: number;

  @IsNumber()
  @IsOptional()
  level_impressions?: number;

  @IsNumber()
  @IsOptional()
  account_counts?: number;

  @IsNumber()
  @IsOptional()
  level_kpi_value?: number;

  @IsBoolean()
  @IsOptional()
  inverse_health?: boolean;

  @IsString()
  @IsOptional()
  kpi_format?: string;

  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AnalyticsCopilotInsightBigQueryResponseItem)
  advertisingData: AnalyticsCopilotInsightBigQueryResponseItem[];
}
