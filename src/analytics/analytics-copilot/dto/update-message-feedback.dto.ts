import { IsDefined, IsEnum, IsOptional, IsString } from 'class-validator';
import {
  FeedbackScore,
  FeedbackReasonType,
} from '../enums/analytics-copilot-conversation-message-feedback.enum';

export class UpdateMessageFeedbackDto {
  @IsDefined({ message: 'Feedback is required.' })
  @IsEnum(FeedbackScore, {
    message: 'Invalid feedback type.',
  })
  feedback: FeedbackScore;

  @IsOptional()
  @IsString({ message: 'Additional comments must be a string.' })
  additionalComments?: string;

  @IsOptional()
  @IsEnum(FeedbackReasonType, {
    message: 'Invalid reason type.',
  })
  reasonType?: FeedbackReasonType;
}
