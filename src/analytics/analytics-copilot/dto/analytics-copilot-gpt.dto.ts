import { <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON>Optional, IsString } from 'class-validator';
import { AnalyticsCopilotConversationRole } from '../enums/analytics-copilot-conversation-role.enum';
import { Type } from 'class-transformer';

export class AnalyticsCopilotGptFunction {
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsString()
  arguments: string;
}

export class AnalyticsCopilotGptResponse {
  @IsNotEmpty()
  @IsEnum(AnalyticsCopilotConversationRole)
  role: AnalyticsCopilotConversationRole;

  @IsOptional()
  @IsString()
  content?: string;

  @IsString()
  @IsOptional()
  @Type(() => AnalyticsCopilotGptFunction)
  function_call?: AnalyticsCopilotGptFunction;
}
