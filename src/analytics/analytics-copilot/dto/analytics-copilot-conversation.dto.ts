import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AnalyticsCopilotConversationRole } from '../enums/analytics-copilot-conversation-role.enum';
import { AnalyticsCopilotGptFunction } from './analytics-copilot-gpt.dto';
import {
  FeedbackScore,
  FeedbackReasonType,
} from '../enums/analytics-copilot-conversation-message-feedback.enum';
import {
  AnalyticsCopilotInsight,
  AnalyticsCopilotInsightBigQueryItem,
  AnalyticsCopilotInsightBigQueryItemV2,
  AnalyticsCopilotInsightFunctionResponse,
  AnalyticsCopilotTagInsightBigQueryItem,
} from './analytics-copilot-insight.dto';
import { Type } from 'class-transformer';
import { InsightType } from '../analytics-copilot-types';

export class AnalyticsCopilotConversationInsight extends AnalyticsCopilotInsight {
  @IsArray()
  @ValidateNested({ each: true })
  insights:
    | AnalyticsCopilotInsightBigQueryItem[]
    | AnalyticsCopilotInsightBigQueryItemV2[];

  @IsOptional()
  @IsNumber()
  version?: number;
}

export class AnalyticsCopilotConversationTagInsight extends AnalyticsCopilotInsight {
  @IsArray()
  @ValidateNested({ each: true })
  insights: AnalyticsCopilotTagInsightBigQueryItem[];
}

export class AnalyticsCopilotConversationHistory {
  @IsNotEmpty()
  @IsEnum(AnalyticsCopilotConversationRole)
  role: AnalyticsCopilotConversationRole;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  content?: string | InsightType[];

  @IsOptional()
  @IsString()
  additional_comments?: string;

  @IsOptional()
  @IsString()
  reason_type?: FeedbackReasonType;

  @IsString()
  @IsOptional()
  @Type(() => AnalyticsCopilotGptFunction)
  function_call?: AnalyticsCopilotGptFunction;

  @IsOptional()
  @IsEnum(FeedbackScore)
  feedback?: FeedbackScore;

  @IsOptional()
  @IsString()
  id?: string;

  @IsOptional()
  @Type(() => AnalyticsCopilotInsightFunctionResponse)
  insight?: AnalyticsCopilotInsightFunctionResponse;
}

export class AnalyticsCopilotConversation {
  @IsNotEmpty()
  @IsString()
  user_id: string;

  @IsNotEmpty()
  @IsString()
  chat_id: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AnalyticsCopilotConversationHistory)
  history: AnalyticsCopilotConversationHistory[];

  @IsNotEmpty()
  @IsString()
  timestamp: string;
}

export class AnalyticsCopilotConversationListItem {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  timestamp: string;
}

export class AnalyticsCopilotConversationListItemV2 {
  @IsNotEmpty()
  @IsString()
  id: string;

  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsString()
  timestamp: Date;
}
