import { AnalyticsCopilotConversationListItem } from '../dto/analytics-copilot-conversation.dto';
import {
  FeedbackScore,
  FeedbackReasonType,
} from '../enums/analytics-copilot-conversation-message-feedback.enum';
import { AnalyticsCopilotConversationHistory } from '../dto/analytics-copilot-conversation.dto';
import { InsightType } from '../analytics-copilot-types';

export interface NormativeInsightsRepository {
  /**
   * Retrieves a list of conversations for a specific user within an organization.
   */
  getConversationList(
    organizationId: string,
    userId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]>;

  /**
   * Retrieves the conversation history for a specific chat.
   */
  getConversationHistory(
    organizationId: string,
    userId: string,
    chatId: string,
  ): Promise<AnalyticsCopilotConversationHistory[]>;

  /**
   * Updates or appends chat history for a conversation.
   */
  updateChatHistory(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    history: AnalyticsCopilotConversationHistory[];
    title: string;
  }): Promise<void>;

  /**
   * Updates feedback for a specific message in a conversation.
   */
  updateMessageFeedback(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    messageId: string;
    feedback: FeedbackScore;
    additionalComments?: string;
    reasonType?: FeedbackReasonType;
  }): Promise<boolean>;

  /**
   * Retrieves insights for a specific message ID.
   */
  getInsightsByMessageId(
    organizationId: string,
    messageId: string,
  ): Promise<InsightType[]>;

  /**
   * Creates new insights for a message.
   */
  createNewInsights(props: {
    organizationId: string;
    messageId: string;
    insights: InsightType[];
    queryVersion: number;
  }): Promise<void>;

  /**
   * Renames a chat by updating its title.
   */
  renameChatTitle(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    newTitle: string;
  }): Promise<boolean>;

  /**
   * Deletes a chat by removing its data.
   */
  deleteChat(props: {
    organizationId: string;
    userId: string;
    chatId: string;
  }): Promise<boolean>;
}
