import {
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { MongoDbService } from '../../brands-copilot/repository/mongo-db.service';
import { NormativeConversationDocument } from '../model/normative-conversation-document.model';
import {
  AnalyticsCopilotConversationHistory,
  AnalyticsCopilotConversationListItem,
} from '../dto/analytics-copilot-conversation.dto';
import {
  FeedbackScore,
  FeedbackReasonType,
} from '../enums/analytics-copilot-conversation-message-feedback.enum';
import { InsightType } from '../analytics-copilot-types';
import { NORMATIVE_INSIGHTS_COLLECTION_PREFIX } from '../constants/analytics-copilot.constants';
import { NormativeInsightsRepository } from './normative-insights.model';

@Injectable()
export class NormativeInsightsMongoDbRepository
  implements NormativeInsightsRepository
{
  private readonly logger = new Logger(NormativeInsightsMongoDbRepository.name);

  constructor(
    private readonly mongoService: MongoDbService<NormativeConversationDocument>,
  ) {}

  /**
   * Constructs the collection name dynamically for normative insights.
   *
   * @param organizationId - The ID of the organization.
   * @returns The name of the collection for normative insights.
   */
  private getCollectionName(organizationId: string): string {
    return `${NORMATIVE_INSIGHTS_COLLECTION_PREFIX}-${organizationId}`;
  }

  /**
   * Updates or appends chat history for a conversation.
   *
   * @param props - The chat update properties.
   */
  async updateChatHistory(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    history: AnalyticsCopilotConversationHistory[];
    title: string;
  }): Promise<void> {
    const { organizationId, userId, chatId, history, title } = props;

    try {
      const collectionName = this.getCollectionName(organizationId);
      const existing = await this.mongoService.get(collectionName, chatId);
      if (existing) {
        await this.mongoService.index(collectionName, chatId, {
          ...existing,
          history: [...existing.history, ...history],
          title,
          timestamp: new Date(),
        });
        this.logger.log(`Updated chat history for chat ID ${chatId}`);
      } else {
        const newDocument: NormativeConversationDocument = {
          _id: chatId,
          userId,
          organizationId,
          history,
          title,
          data: {},
          timestamp: new Date(),
          messages: [],
          lastInteractionDate: new Date(),
        };
        await this.mongoService.index(collectionName, chatId, newDocument);
        this.logger.log(`Created new chat history for chat ID ${chatId}`);
      }
    } catch (error) {
      this.logger.error(`Error updating chat history: ${error.message}`);
      throw error;
    }
  }

  /**
   * Retrieves a list of conversations for a specific user.
   *
   * @param organizationId - The ID of the organization.
   * @param userId - The ID of the user.
   * @returns A list of conversations.
   */
  async getConversationList(
    organizationId: string,
    userId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    try {
      const collectionName = this.getCollectionName(organizationId);
      const results = await this.mongoService.search(
        collectionName,
        { userId },
        100, // Limit to 100 items
        { timestamp: -1 },
      );

      return results.map((conversation) => ({
        id: conversation.id,
        title: conversation.title || '',
        timestamp: conversation.timestamp.toISOString(),
      }));
    } catch (error) {
      this.logger.error(`Error fetching conversation list: ${error.message}`);
      return [];
    }
  }

  /**
   * Retrieves conversation history for a specific chat.
   *
   * @param organizationId - The ID of the organization.
   * @param userId - The ID of the user.
   * @param chatId - The ID of the chat.
   * @returns The conversation history.
   */
  async getConversationHistory(
    organizationId: string,
    userId: string,
    chatId: string,
  ): Promise<AnalyticsCopilotConversationHistory[]> {
    try {
      const collectionName = this.getCollectionName(organizationId);
      const document = await this.mongoService.get(collectionName, chatId);
      if (document?.userId !== userId) {
        throw new ForbiddenException(
          `Unauthorized access for chat ID ${chatId}`,
        );
      }
      return document?.history || [];
    } catch (error) {
      this.logger.error(
        `Error fetching conversation history for chat ID ${chatId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Updates feedback for a specific message in a conversation.
   *
   * @param props - The feedback update properties.
   * @returns `true` if the update was successful, otherwise `false`.
   */
  async updateMessageFeedback(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    messageId: string;
    feedback: FeedbackScore;
    additionalComments?: string;
    reasonType?: FeedbackReasonType;
  }): Promise<boolean> {
    const {
      organizationId,
      userId,
      chatId,
      messageId,
      feedback,
      additionalComments,
      reasonType,
    } = props;

    try {
      const collectionName = this.getCollectionName(organizationId);
      const document = await this.mongoService.get(collectionName, chatId);

      if (!document || document.userId !== userId) {
        this.logger.warn(`Chat ${chatId} not found or unauthorized.`);
        return false;
      }

      const message = document.history.find((m: any) => m.id === messageId);
      if (message) {
        message.feedback = feedback;
        if (additionalComments)
          message.additional_comments = additionalComments;
        if (reasonType) message.reason_type = reasonType;

        await this.mongoService.index(collectionName, chatId, {
          ...document,
          history: document.history,
        });
        return true;
      }

      this.logger.warn(
        `Message with ID ${messageId} not found in chat ${chatId}`,
      );
      return false;
    } catch (error) {
      this.logger.error(
        `Error updating message feedback for message ID ${messageId}: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Retrieves insights for a specific message ID.
   *
   * @param organizationId - The ID of the organization.
   * @param messageId - The ID of the message.
   * @returns A list of insights.
   */
  async getInsightsByMessageId(
    organizationId: string,
    messageId: string,
  ): Promise<InsightType[]> {
    try {
      const collectionName = this.getCollectionName(organizationId);
      const results = await this.mongoService.search(
        collectionName,
        { messageId },
        100, // Limit to 100 items
        { timestamp: -1 },
      );

      return results as InsightType[];
    } catch (error) {
      this.logger.error(
        `Error fetching insights for message ID ${messageId}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Creates new insights for a message.
   *
   * @param props - The properties for creating insights.
   */
  async createNewInsights(props: {
    organizationId: string;
    messageId: string;
    insights: InsightType[];
    queryVersion: number;
  }): Promise<void> {
    const { organizationId, messageId, insights, queryVersion } = props;

    if (!insights?.length) {
      this.logger.warn(`No insights provided for message ID ${messageId}`);
      return;
    }

    try {
      const collectionName = this.getCollectionName(organizationId);
      await Promise.all(
        insights.map((insight) =>
          this.mongoService.index(collectionName, insight.id, {
            ...insight,
            messageId,
            queryVersion,
            timestamp: new Date(),
          }),
        ),
      );

      this.logger.log(
        `Inserted ${insights.length} insights for message ID ${messageId} in collection ${collectionName}`,
      );
    } catch (error) {
      this.logger.error(
        `Error creating new insights for organization ${organizationId}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Renames a chat by updating its title.
   *
   * @param props - The properties for renaming a chat.
   * @returns `true` if the rename was successful, otherwise `false`.
   */
  async renameChatTitle(props: {
    organizationId: string;
    userId: string;
    chatId: string;
    newTitle: string;
  }): Promise<boolean> {
    const { organizationId, userId, chatId, newTitle } = props;

    try {
      const collectionName = this.getCollectionName(organizationId);
      const document = await this.mongoService.get(collectionName, chatId);

      if (!document || document.userId !== userId) {
        this.logger.warn(`Chat ${chatId} not found or unauthorized.`);
        return false;
      }

      await this.mongoService.index(collectionName, chatId, {
        ...document,
        title: newTitle,
      });
      return true;
    } catch (error) {
      this.logger.error(
        `Error renaming chat title for chat ID ${chatId}: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Deletes a chat by removing its data.
   *
   * @param props - The properties for deleting a chat.
   * @returns `true` if the deletion was successful, otherwise `false`.
   */
  async deleteChat(props: {
    organizationId: string;
    userId: string;
    chatId: string;
  }): Promise<boolean> {
    const { organizationId, userId, chatId } = props;

    try {
      const collectionName = this.getCollectionName(organizationId);
      const document = await this.mongoService.get(collectionName, chatId);

      if (!document || document.userId !== userId) {
        this.logger.warn(`Chat ${chatId} not found or unauthorized.`);
        return false;
      }

      await this.mongoService.delete(collectionName, chatId);
      this.logger.log(`Deleted chat ${chatId} for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting chat ${chatId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Updates a normative insight document
   */
  async updateNormativeInsight(
    organizationId: string,
    insightId: string,
    insightLibraryId: string,
  ): Promise<void> {
    try {
      const collectionName = this.getCollectionName(organizationId);

      const filter = { _id: insightId };

      const update = {
        $set: { insightLibraryId },
      };

      // Use our partial update method
      const { matchedCount, modifiedCount } = await this.mongoService.updateOne(
        collectionName,
        filter,
        update,
      );

      // If no document matches, throw a NotFoundException
      if (matchedCount === 0) {
        this.logger.warn(
          `No insight found with _id=${insightId} in org ${organizationId}`,
        );
        throw new NotFoundException(`Insight with ID ${insightId} not found.`);
      }

      // If document matched but no fields changed, log a warning
      if (modifiedCount === 0) {
        this.logger.warn(
          `Document matched, but no fields updated for insightId=${insightId} (already set or no changes).`,
        );
      } else {
        this.logger.log(
          `Updated normative insight ${insightId} with libraryId=${insightLibraryId}.`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error updating normative insight ${insightId} with library ID: ${error.message}`,
      );
      throw error;
    }
  }
}
