import { Injectable, Logger } from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import {
  CHAT_HISTORY_DB_INDEX,
  ELASTIC_SEARCH_API_KEY,
  ELASTIC_SEARCH_NODE,
  INSIGHTS_DB_INDEX,
} from './constants/analytics-copilot.constants';
import {
  AnalyticsCopilotConversation,
  AnalyticsCopilotConversationHistory,
  AnalyticsCopilotConversationListItem,
} from './dto/analytics-copilot-conversation.dto';
import {
  FeedbackScore,
  FeedbackReasonType,
} from './enums/analytics-copilot-conversation-message-feedback.enum';
import { InsightType } from './analytics-copilot-types';

@Injectable()
export class ElasticsearchService {
  private readonly logger = new Logger(ElasticsearchService.name);
  private esClient: Client;

  constructor() {
    this.esClient = new Client({
      node: ELASTIC_SEARCH_NODE,
      auth: {
        apiKey: ELASTIC_SEARCH_API_KEY,
      },
    });
  }

  async updateMessageFeedback(props: {
    userId: string;
    chatId: string;
    messageId: string;
    feedback: FeedbackScore;
    additionalComments?: string;
    reasonType?: FeedbackReasonType;
  }): Promise<boolean> {
    const {
      userId,
      chatId,
      messageId,
      feedback,
      additionalComments,
      reasonType,
    } = props;
    try {
      if (userId && chatId && messageId && feedback) {
        const searchResponse = await this.esClient.search({
          index: CHAT_HISTORY_DB_INDEX,
          body: {
            query: {
              bool: {
                must: [
                  { term: { user_id: userId } },
                  { term: { 'chat_id.keyword': chatId } },
                ],
              },
            },
          },
          size: 1,
        });
        const searchItem = searchResponse.hits.hits[0];
        const id = searchItem?._id;
        const chat = searchItem?._source as AnalyticsCopilotConversation;

        if (id && chat?.history && chat?.history.length > 0) {
          const updatedHistory = [...chat.history];
          const message = updatedHistory.find((m) => m.id === messageId);
          if (message) {
            message.feedback = feedback;
            if (additionalComments) {
              message.additional_comments = additionalComments;
            }
            if (reasonType) {
              message.reason_type = reasonType;
            }
            await this.esClient.update({
              index: CHAT_HISTORY_DB_INDEX,
              id: id,
              body: {
                doc: {
                  history: updatedHistory,
                },
              },
            });
            return true;
          }
        }
      }
      return false;
    } catch (e) {
      this.logger.error(`Error while updating copilot message feedback ${e}`);
      return false;
    }
  }

  async updateChatHistory(props: {
    userId: string;
    chatId: string;
    history: AnalyticsCopilotConversationHistory[];
    title: string;
  }) {
    const { userId, chatId, history, title } = props;
    try {
      const searchResponse = await this.esClient.search({
        index: CHAT_HISTORY_DB_INDEX,
        body: {
          query: {
            bool: {
              must: [
                { term: { user_id: userId } },
                { term: { 'chat_id.keyword': chatId } },
              ],
            },
          },
        },
        size: 1,
      });
      const searchItem = searchResponse.hits.hits[0];
      const id = searchItem?._id;
      const chat = searchItem?._source as AnalyticsCopilotConversation;
      if (id && chat?.history && chat?.history.length > 0) {
        await this.esClient.update({
          index: CHAT_HISTORY_DB_INDEX,
          id: id,
          body: {
            doc: {
              history: [...chat.history, ...history],
              timestamp: new Date(),
            },
          },
        });
      } else {
        const body = {
          user_id: userId,
          chat_id: chatId,
          history: history,
          title: title,
          timestamp: new Date(),
        };
        await this.esClient.index({
          index: CHAT_HISTORY_DB_INDEX,
          refresh: 'true',
          body: body,
        });
      }
    } catch (e) {
      this.logger.error(`Error while updating copilot chat history ${e}`);
    }
  }

  async renameChatTitle(props: {
    userId: string;
    chatId: string;
    newTitle: string;
  }) {
    const { userId, chatId, newTitle } = props;
    try {
      const searchResponse = await this.esClient.search({
        index: CHAT_HISTORY_DB_INDEX,
        body: {
          query: {
            bool: {
              must: [
                { term: { user_id: userId } },
                { term: { 'chat_id.keyword': chatId } },
              ],
            },
          },
        },
        size: 1,
      });

      const searchItem = searchResponse.hits.hits[0];
      const id = searchItem?._id;

      if (id) {
        await this.esClient.update({
          index: CHAT_HISTORY_DB_INDEX,
          id: id,
          body: {
            doc: {
              title: newTitle,
            },
          },
        });
        return true;
      } else {
        this.logger.warn(`Chat with id ${chatId} not found for user ${userId}`);
        return false;
      }
    } catch (e) {
      this.logger.error(`Error while renaming copilot chat title: ${e}`);
      return false;
    }
  }

  async deleteChat(props: { userId: string; chatId: string }) {
    const { userId, chatId } = props;
    try {
      const searchResponse = await this.esClient.search({
        index: CHAT_HISTORY_DB_INDEX,
        body: {
          query: {
            bool: {
              must: [
                { term: { user_id: userId } },
                { term: { 'chat_id.keyword': chatId } },
              ],
            },
          },
        },
        size: 1,
      });

      const searchItem = searchResponse.hits.hits[0];
      const id = searchItem?._id;

      if (id) {
        await this.esClient.delete({
          index: CHAT_HISTORY_DB_INDEX,
          id: id,
        });
        return true;
      } else {
        this.logger.warn(`Chat with id ${chatId} not found for user ${userId}`);
        return false;
      }
    } catch (e) {
      this.logger.error(
        `Error while deleting copilot chat: ${chatId} by user ${userId} ${e}`,
      );
      return false;
    }
  }

  async createNewInsights(props: {
    messageId: string;
    insights: InsightType[];
    queryVersion: number;
  }) {
    const { messageId, insights, queryVersion } = props;
    if (!insights || insights.length === 0) {
      return;
    }
    insights.forEach(async (insight) => {
      await this.esClient.index({
        index: INSIGHTS_DB_INDEX,
        id: insight.id,
        refresh: 'true',
        body: {
          messageId,
          ...insight,
          queryVersion,
          timestamp: new Date(),
        },
      });
    });
  }

  async getInsightsByMessageId(messageId: string) {
    try {
      const response = await this.esClient.search({
        index: INSIGHTS_DB_INDEX,
        body: {
          query: {
            term: { 'messageId.keyword': messageId },
          },
          sort: [{ timestamp: { order: 'desc' } }],
        },
        size: 1000,
      });

      return response.hits.hits.map((hit) => hit._source) as InsightType[];
    } catch (e) {
      this.logger.error(
        `Error while trying to get insights by message id ${e}`,
      );
      return [];
    }
  }

  async getConversationList(
    userId: string,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    try {
      const response = await this.esClient.search({
        index: CHAT_HISTORY_DB_INDEX,
        body: {
          query: {
            bool: {
              must: [{ term: { user_id: userId } }],
            },
          },
          sort: [{ timestamp: { order: 'desc' } }],
        },
        size: 1000,
      });

      const chats = response.hits.hits.map(
        (hit) => hit._source,
      ) as AnalyticsCopilotConversation[];

      return chats.map((chat) => ({
        id: chat.chat_id,
        title: chat.title,
        timestamp: chat.timestamp,
      }));
    } catch (e) {
      this.logger.error(
        `Error while trying to get copilot conversation list ${e}`,
      );
      return [];
    }
  }

  async getConversationHistory(
    userId: string,
    chatId: string,
  ): Promise<AnalyticsCopilotConversationHistory[]> {
    try {
      const response = await this.esClient.search({
        index: CHAT_HISTORY_DB_INDEX,
        body: {
          query: {
            bool: {
              must: [
                { term: { user_id: userId } },
                { term: { 'chat_id.keyword': chatId } },
              ],
            },
          },
        },
        size: 1,
      });
      const chat = response.hits.hits.map(
        (hit) => hit._source,
      )[0] as AnalyticsCopilotConversation;

      return chat ? chat.history : [];
    } catch (e) {
      this.logger.error(
        `Error while trying to get copilot conversation history ${e}`,
      );
      return [];
    }
  }
}
