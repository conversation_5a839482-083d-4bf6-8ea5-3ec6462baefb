import { ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Request,
} from '@nestjs/common';
import { AnalyticsCopilotService } from './analytics-copilot.service';
import { ConversationService } from './conversation.service';
import {
  AnalyticsCopilotNextPayload,
  AnalyticsCopilotNextResponse,
} from './dto/analytics-copilot-next.dto';
import {
  AnalyticsCopilotConversationHistory,
  AnalyticsCopilotConversationListItem,
} from './dto/analytics-copilot-conversation.dto';
import { CONVERSATION_STARTERS } from './constants/analytics-copilot.constants';
import { UpdateMessageFeedbackDto } from './dto/update-message-feedback.dto';

@ApiTags('Copilot')
@Controller({
  path: 'analytics-copilot',
})
export class AnalyticsCopilotController {
  constructor(
    private readonly analyticsCopilotService: AnalyticsCopilotService,
    private readonly conversationService: ConversationService,
  ) {}

  @Post('next')
  async getLLMResponse(
    @Request() request: any,
    @Body()
    body: AnalyticsCopilotNextPayload,
  ): Promise<AnalyticsCopilotNextResponse> {
    const { userInput, chatId } = body;
    const { userId } = request;
    const host = request.get('host');
    return await this.analyticsCopilotService.getLLMResponse({
      userInput,
      chatId,
      userId,
      host,
      version: 1,
    });
  }

  @Post('v2/next')
  async getLLMResponseV2(
    @Request() request: any,
    @Body()
    body: AnalyticsCopilotNextPayload,
  ): Promise<AnalyticsCopilotNextResponse> {
    const { userId } = request;
    const { userInput, chatId } = body;
    const host = request.get('host');
    return await this.analyticsCopilotService.getLLMResponse({
      userInput,
      userId,
      chatId,
      host,
      version: 2,
    });
  }

  @Get('conversation-list')
  async getConversationList(
    @Request() request: any,
  ): Promise<AnalyticsCopilotConversationListItem[]> {
    const { userId } = request;
    return await this.conversationService.getConversationList(userId);
  }

  @Patch('conversation/:chatId/rename')
  async renameConversation(
    @Request() request: any,
    @Param('chatId') chatId: string,
    @Body() body: { newTitle: string },
  ): Promise<boolean> {
    const { userId } = request;
    return await this.conversationService.renameChat({
      userId,
      chatId,
      newTitle: body.newTitle,
    });
  }

  @Delete('conversation/:chatId')
  async deleteConversation(
    @Request() request: any,
    @Param('chatId') chatId: string,
  ): Promise<boolean> {
    const { userId } = request;
    return await this.conversationService.deleteChat({
      userId,
      chatId,
    });
  }

  @Get('conversation-history/:chatId')
  async getConversationHistory(
    @Request() request: any,
    @Param('chatId') chatId: string,
  ): Promise<AnalyticsCopilotConversationHistory[]> {
    const { userId } = request;
    return await this.conversationService.getConversationHistory({
      userId,
      chatId,
    });
  }

  @Post('update-message-feedback/:chatId/:messageId')
  async updateMessageFeedback(
    @Request() request: any,
    @Param('chatId') chatId: string,
    @Param('messageId') messageId: string,
    @Body() body: UpdateMessageFeedbackDto,
  ): Promise<boolean> {
    const { userId } = request;
    const { feedback, additionalComments, reasonType } = body;
    return await this.conversationService.updateMessageFeedback({
      userId,
      chatId,
      messageId,
      feedback,
      additionalComments,
      reasonType,
    });
  }

  @Get('conversation-starters')
  async getConversationStarters(): Promise<string[]> {
    return CONVERSATION_STARTERS;
  }
}
