import { Injectable, Logger } from '@nestjs/common';
import {
  CHAT_HISTORY_LIMIT,
  ERROR_MESSAGE,
  NO_RESULTS_MESSAGE,
} from './constants/analytics-copilot.constants';
import { v4 as uuidv4 } from 'uuid';
import { NormativePerformanceService } from '../normative-performance/normative-performance.service';
import { NormativePerformanceRequestDto } from '../normative-performance/dto/normative-performance-request.dto';
import { NormativePerformanceForKpiDto } from '../normative-performance/dto/normative-performance.dto';
import { AnalyticsCopilotConversationHistory } from './dto/analytics-copilot-conversation.dto';
import { AnalyticsCopilotConversationRole } from './enums/analytics-copilot-conversation-role.enum';
import { getFormattedEndDate, getFormattedStartDate } from './utils/date.utils';
import { getPlatform } from './utils/platform.utils';
import { getKpiNameFromCategoryAndPlatform } from './utils/kpi.utils';
import { ElasticsearchService } from './analytics-copilot-elastic-search.service';
import { OpenAiService } from './analytics-copilot-open-ai.service';
import { AnalyticsCopilotGptResponse } from './dto/analytics-copilot-gpt.dto';
import { getFinalInsights, getFinalTagInsights } from './utils/llm.utils';
import { AnalyticsCopilotNextResponse } from './dto/analytics-copilot-next.dto';
import {
  AnalyticsCopilotInsight,
  AnalyticsCopilotInsightBigQueryResponseItem,
  AnalyticsCopilotInsightFunctionResponse,
} from './dto/analytics-copilot-insight.dto';
import { AnalyticsCopilotBenchmark } from './dto/analytics-copilot-benchmark.dto';
import { CopilotBigQueryService } from './analytics-copilot-big-query.service';
import { KPIService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { IndustryService } from '@vidmob/vidmob-studio-service-sdk';
import { Platform } from 'src/constants/platform.constants';
import { AnalyticsCopilotFunction } from './enums/analytics-copilot-function.enum';
import { InsightType, MissingFieldsInputs } from './analytics-copilot-types';
import { getSystemPrompt } from './prompts/system-prompts';
import { getUserPrompt } from './prompts/user-prompts';
import { parseMessage } from './utils/message.utils';
import { getFunctionPromptForBenchmark } from './prompts/benchmark-prompts';
import { getInsightsPrompt } from './prompts/insight-prompts';
import { getInsightsByTagPrompt } from './prompts/tag-insight-prompts';
import { ConversationService } from './conversation.service';

@Injectable()
export class AnalyticsCopilotService {
  private readonly logger = new Logger(AnalyticsCopilotService.name);

  constructor(
    private readonly openAiService: OpenAiService,
    private readonly bigQueryService: CopilotBigQueryService,
    private readonly elasticsearchService: ElasticsearchService,
    private readonly normativePerformanceService: NormativePerformanceService,
    private readonly kpiService: KPIService,
    private readonly industryService: IndustryService,
    private readonly conversationService: ConversationService,
  ) {}

  async getNormativePerformance(
    normsConfiguration: NormativePerformanceRequestDto,
  ): Promise<NormativePerformanceForKpiDto | null> {
    try {
      const response =
        (await this.normativePerformanceService.getNormativePerformance(
          normsConfiguration,
          {
            perPage: 10,
            offset: 0,
          },
        )) as {
          result?: NormativePerformanceForKpiDto[];
          items?: NormativePerformanceForKpiDto[];
        };

      const items = response?.result || response?.items;
      if (items && items.length > 0) {
        return items[0];
      }

      return null;
    } catch (e) {
      this.logger.error(`Error while trying to get normative performance ${e}`);
      return null;
    }
  }

  async callLLMWithHistory(
    chatHistory: AnalyticsCopilotConversationHistory[],
    version?: number,
  ): Promise<AnalyticsCopilotGptResponse> {
    const messages: AnalyticsCopilotConversationHistory[] = [getSystemPrompt()];
    messages.push(...chatHistory);
    const messagesWithStringifiedContent = messages.map((message) => {
      if (typeof message !== 'string') {
        return { ...message, content: JSON.stringify(message.content) };
      }
      return message;
    });

    const response = await this.openAiService.getGptResponse(
      messagesWithStringifiedContent,
      true,
      version,
    );

    return response;
  }

  getLLMInstructions(props: {
    id: string;
    userId: string;
    userInput: string;
    content: string;
    log?: string;
    insight?: AnalyticsCopilotInsightFunctionResponse;
    version: number;
  }): AnalyticsCopilotNextResponse {
    try {
      const { id, userId, userInput, content, log, insight, version } = props;

      const assistant = JSON.parse(content) as {
        answer: string | InsightType[];
      };

      let dataForInsights =
        insight && insight.advertisingData.length
          ? insight?.advertisingData?.slice(0, 10)
          : [];
      const messageId = uuidv4();

      let insights = undefined;
      // if assistant.answer is an array then we need to create the insight response here and then add the insight ids to the history object
      // insights are saved in their own table and not on the message
      if (Array.isArray(assistant.answer)) {
        insights = assistant.answer.map((answer: InsightType) => {
          return { ...answer, id: uuidv4() };
        });
        dataForInsights = this.filterAdvertisingData(insight, insights);
        this.elasticsearchService.createNewInsights({
          messageId,
          insights: insights as InsightType[],
          queryVersion: version,
        });
      }

      this.elasticsearchService.updateChatHistory({
        userId,
        chatId: id,
        title: userInput,
        history: [
          {
            role: AnalyticsCopilotConversationRole.USER,
            content: userInput,
            id: uuidv4(),
          },
          {
            role: AnalyticsCopilotConversationRole.ASSISTANT,
            content: insights ? '' : (assistant.answer as string),
            id: messageId,
            insight: insight && {
              ...insight,
              advertisingData: dataForInsights,
            },
          },
        ],
      });

      return {
        id,
        answer: parseMessage(insights ? insights : assistant.answer),
        log,
        messageId,
        insight,
      };
    } catch (e) {
      this.logger.error(`Error while trying to get LLM answer ${e}`);
      return {
        id: props.id,
        answer: ERROR_MESSAGE.replace('{replace}', ' on getting answer'),
      };
    }
  }

  private filterAdvertisingData(
    insight?: AnalyticsCopilotInsightFunctionResponse,
    insights?: InsightType[],
  ): AnalyticsCopilotInsightBigQueryResponseItem[] {
    if (
      !insight ||
      !insight.advertisingData ||
      !insight.advertisingData.length
    ) {
      return [];
    }

    const elementsInInsights =
      insights?.flatMap((insight) => insight.elements.map((e) => e.element)) ||
      [];

    const advertisingDataArray = insight.advertisingData.filter((data) =>
      elementsInInsights.includes(data.element),
    );

    return advertisingDataArray.length
      ? advertisingDataArray
      : insight.advertisingData.slice(0, 10);
  }

  async callLLMAfterFunctionResponse(props: {
    id: string;
    userId: string;
    userInput: string;
    arguments: string;
    history: AnalyticsCopilotConversationHistory[];
    functionName: string;
    functionContent: string;
    log?: string;
    insight?: AnalyticsCopilotInsightFunctionResponse;
    version: number;
  }): Promise<AnalyticsCopilotNextResponse> {
    const {
      id,
      userId,
      userInput,
      functionName,
      functionContent,
      log,
      insight,
      version,
    } = props;

    try {
      const llmResponse = await this.openAiService.getGptResponse(
        [
          getSystemPrompt(),
          {
            role: AnalyticsCopilotConversationRole.USER,
            content: functionContent,
          },
        ],
        false,
      );

      const content =
        llmResponse.content ||
        JSON.stringify({
          answer: ERROR_MESSAGE.replace('{replace}', ''),
        });

      if (content === functionContent) {
        // There are some unknown cases where GPT return the same content as the function content
        // In this case we need to return error message
        return {
          id: props.id,
          answer: ERROR_MESSAGE.replace(
            '{replace}',
            ` on parsing ${functionName} response`,
          ),
        };
      }

      return this.getLLMInstructions({
        id,
        userId,
        userInput,
        content,
        log,
        insight,
        version,
      });
    } catch (e) {
      this.logger.error(
        `Error while trying to call LLM after function response ${e}`,
      );
      return {
        id: props.id,
        answer: ERROR_MESSAGE.replace(
          '{replace}',
          ` on parsing ${functionName} response`,
        ),
      };
    }
  }

  async getKpiDetailsAndFormula(
    platform: Platform | undefined,
    name: string | undefined,
  ) {
    if (!platform || !name) return null;

    const { result } =
      await this.kpiService.fetchKpiByNameWithConvertedFormulaAsPromise(
        platform,
        {
          name: name,
        },
      );
    return result;
  }

  async getIndustryDetails(industryName: string) {
    const { result } =
      await this.industryService.industryControllerGetIndustryByNameAsPromise(
        industryName,
      );

    return result;
  }

  async getInsights(props: {
    id: string;
    userId: string;
    userInput: string;
    arguments: string;
    history: AnalyticsCopilotConversationHistory[];
    version: number;
  }): Promise<AnalyticsCopilotNextResponse> {
    try {
      const { platform, industry_name, kpi_category, start_date, end_date } =
        JSON.parse(props.arguments) as AnalyticsCopilotInsight;

      const platformEnum = getPlatform(platform || 'facebook');
      const startDate = getFormattedStartDate(start_date);
      const endDate = getFormattedEndDate(end_date);
      const kpiName = getKpiNameFromCategoryAndPlatform(
        kpi_category,
        platformEnum,
      );
      const kpiInfo = await this.getKpiDetailsAndFormula(platformEnum, kpiName);
      const industryInfo = await this.getIndustryDetails(
        industry_name || 'CPG',
      );

      const industryName = industryInfo?.name || industry_name || 'CPG';
      const payload = {
        platform: platformEnum,
        kpiName: kpiName,
        kpiID: kpiInfo?.id,
        formula: kpiInfo?.bigQueryFormula,
        industryName: industryName,
        industryId: industryInfo?.id,
        startDate: startDate,
        endDate: endDate,
      };

      const log = `Analytics-copilot-get-insights-final-payload: ${JSON.stringify(
        payload,
      )}`;
      this.logger.log(log);

      if (
        platformEnum &&
        industryName &&
        kpiName &&
        kpiInfo?.bigQueryFormula &&
        startDate &&
        endDate
      ) {
        const insights = await this.bigQueryService.queryInsights({
          platform: platformEnum,
          industry: industryName,
          startDate: startDate,
          endDate: endDate,
          formula: kpiInfo?.bigQueryFormula,
          inverseHealth: Boolean(kpiInfo?.inverseHealth),
          version: props.version,
        });

        if (insights && insights.length > 0) {
          const insightObj = getFinalInsights({
            platform: platformEnum,
            industry_name: industryName,
            industry_id: industryInfo?.id,
            kpi_name: kpiName,
            kpi_id: kpiInfo?.id,
            start_date: startDate,
            end_date: endDate,
            insights: insights,
            version: props.version,
          });
          return await this.callLLMAfterFunctionResponse({
            ...props,
            functionContent: getInsightsPrompt(
              props.version,
              Boolean(kpiInfo?.inverseHealth),
              props.userInput,
              JSON.stringify({
                ...insightObj.advertisingData,
                overall_avg: insightObj.overall_avg,
              }),
            ),
            functionName: AnalyticsCopilotFunction.GET_INSIGHTS,
            log,
            insight: {
              ...insightObj,
              inverse_health: Boolean(kpiInfo?.inverseHealth),
              kpi_format: kpiInfo?.format,
            },
            version: props.version,
          });
        }
      }
      const answer = this.getMissingFieldsMessage({
        platformEnum,
        industryName,
        kpiName,
        startDate,
        endDate,
      });
      return this.getLLMInstructions({
        id: props.id,
        userId: props.userId,
        userInput: props.userInput,
        content: JSON.stringify({
          answer: answer,
        }),
        log,
        version: props.version,
      });
    } catch (e) {
      const errorMessage =
        e.message ||
        ERROR_MESSAGE.replace('{replace}', ` on trying to get insights`);
      this.logger.error(`Error while trying to get insights ${e}`);
      return {
        id: props.id,
        answer: errorMessage,
      };
    }
  }

  async getBenchmarks(props: {
    id: string;
    userId: string;
    userInput: string;
    arguments: string;
    history: AnalyticsCopilotConversationHistory[];
    version: number;
  }): Promise<AnalyticsCopilotNextResponse> {
    try {
      const { platform, industry_name, kpi_category, start_date, end_date } =
        JSON.parse(props.arguments) as AnalyticsCopilotBenchmark;
      const platformEnum = getPlatform(platform || 'facebook');
      const kpiName = getKpiNameFromCategoryAndPlatform(
        kpi_category,
        platformEnum,
      );

      const kpiInfo = await this.getKpiDetailsAndFormula(platformEnum, kpiName);

      const kpiId = kpiInfo?.id;
      const industryInfo = await this.getIndustryDetails(
        industry_name || 'CPG',
      );
      const industryName = industryInfo?.name || industry_name || 'CPG';
      const industryId = industryInfo?.id;

      const startDate = getFormattedStartDate(start_date);
      const endDate = getFormattedEndDate(end_date);

      const payload = {
        platform: platformEnum,
        kpiName: kpiName,
        kpiID: kpiId,
        industryName: industryName,
        industryId: industryInfo?.id,
        startDate: startDate,
        endDate: endDate,
      };
      const log = `Analytics-copilot-get-benchmarks-final-payload: ${JSON.stringify(
        payload,
      )}`;
      this.logger.log(log);

      if (platformEnum && kpiId && industryId && startDate && endDate) {
        const response = await this.getNormativePerformance({
          platform: platformEnum,
          startDate: startDate,
          endDate: endDate,
          kpiIds: [kpiId],
          industryIds: [industryId],
        });

        if (response?.normativePerformance && kpiName) {
          return await this.callLLMAfterFunctionResponse({
            ...props,
            functionContent: getFunctionPromptForBenchmark({
              kpi_name: kpiName,
              platform: platformEnum,
              industry_name,
              start_date: startDate,
              end_date: endDate,
              normativePerformance: response.normativePerformance,
              format: response.format,
            }),
            functionName: AnalyticsCopilotFunction.GET_BENCHMARKS,
            log,
            version: props.version,
          });
        }
      }

      const answer = this.getMissingFieldsMessage({
        platformEnum,
        industryName,
        kpiName,
        startDate,
        endDate,
      });
      return this.getLLMInstructions({
        id: props.id,
        userId: props.userId,
        userInput: props.userInput,
        content: JSON.stringify({
          answer: answer,
        }),
        log,
        version: props.version,
      });
    } catch (e) {
      const errorMessage =
        e.message ||
        ERROR_MESSAGE.replace('{replace}', ` on trying to get benchmarks`);
      this.logger.error(`Error while trying to get benchmarks ${e}`);
      return {
        id: props.id,
        answer: errorMessage,
      };
    }
  }

  async getInsightsByTag(props: {
    id: string;
    userId: string;
    userInput: string;
    arguments: string;
    history: AnalyticsCopilotConversationHistory[];
    version: number;
  }): Promise<AnalyticsCopilotNextResponse> {
    try {
      const {
        platform,
        industry_name,
        kpi_category,
        start_date,
        end_date,
        tag_hierarchy,
      } = JSON.parse(props.arguments) as AnalyticsCopilotInsight;
      let tag_hierarchy_level_3;
      let tag_hierarchy_level_4;

      if (tag_hierarchy) {
        [tag_hierarchy_level_3, tag_hierarchy_level_4] = tag_hierarchy
          .split(':')
          .map((part) => part.trim());
      }

      const platformEnum = getPlatform(platform || 'facebook');
      const startDate = getFormattedStartDate(start_date);
      const endDate = getFormattedEndDate(end_date);
      const kpiName = getKpiNameFromCategoryAndPlatform(
        kpi_category,
        platformEnum,
      );
      const kpiInfo = await this.getKpiDetailsAndFormula(platformEnum, kpiName);
      const industryInfo = await this.getIndustryDetails(
        industry_name || 'CPG',
      );
      const industryName = industryInfo?.name || industry_name || 'CPG';

      const payload = {
        platform: platformEnum,
        kpiName: kpiName,
        kpiID: kpiInfo?.id,
        formula: kpiInfo?.bigQueryFormula,
        industryName: industryName,
        industryId: industryInfo?.id,
        startDate: startDate,
        endDate: endDate,
        tag_hierarchy_level_3: tag_hierarchy_level_3,
        tag_hierarchy_level_4: tag_hierarchy_level_4,
      };
      const log = `Analytics-copilot-get-insights-by-tag-final-payload: ${JSON.stringify(
        payload,
      )}`;
      this.logger.log(log);

      if (
        platformEnum &&
        industryName &&
        kpiName &&
        kpiInfo?.bigQueryFormula &&
        startDate &&
        endDate &&
        tag_hierarchy_level_3
      ) {
        const insights = await this.bigQueryService.queryInsightsByTag({
          platform: platformEnum,
          industry: industryName,
          startDate: startDate,
          endDate: endDate,
          formula: kpiInfo?.bigQueryFormula,
          tag_hierarchy_level_3: tag_hierarchy_level_3,
          tag_hierarchy_level_4: tag_hierarchy_level_4,
        });

        if (insights && insights.length > 0) {
          const insightObj = getFinalTagInsights({
            platform: platformEnum,
            industry_name: industryName,
            industry_id: industryInfo?.id,
            kpi_id: kpiInfo?.id,
            kpi_name: kpiName,
            start_date: startDate,
            end_date: endDate,
            insights: insights,
          });

          return await this.callLLMAfterFunctionResponse({
            ...props,
            functionContent: getInsightsByTagPrompt(
              Boolean(kpiInfo?.inverseHealth),
              props.userInput,
              JSON.stringify({
                ...insightObj.advertisingData,
                overall_avg: insightObj.overall_avg,
              }),
            ),
            functionName: AnalyticsCopilotFunction.GET_INSIGHTS_BY_TAG,
            log,
            insight: {
              ...insightObj,
              inverse_health: Boolean(kpiInfo?.inverseHealth),
              kpi_format: kpiInfo?.format,
            },
            version: props.version,
          });
        }
      }

      const answer = this.getMissingFieldsMessage({
        platformEnum,
        industryName,
        kpiName,
        startDate,
        endDate,
      });

      return this.getLLMInstructions({
        id: props.id,
        userId: props.userId,
        userInput: props.userInput,
        content: JSON.stringify({
          answer: answer,
        }),
        log,
        version: props.version,
      });
    } catch (e) {
      const errorMessage =
        e.message ||
        ERROR_MESSAGE.replace('{replace}', ` on trying to get insights`);
      this.logger.error(`Error while trying to get insights ${e}`);
      return {
        id: props.id,
        answer: errorMessage,
      };
    }
  }

  async getLLMResponse(props: {
    userInput: string;
    userId: string;
    chatId?: string;
    host?: string;
    version: number;
  }): Promise<AnalyticsCopilotNextResponse> {
    const { userInput, userId, chatId, version } = props;
    const id = chatId || uuidv4();
    try {
      const chatHistory = await this.conversationService.getConversationHistory(
        {
          userId,
          chatId: id,
        },
      );
      // new function for sliced
      const chatHistorySliced = chatHistory
        .map((item) => {
          const { feedback, id, insight, ...others } = item;
          return others;
        })
        .slice(-CHAT_HISTORY_LIMIT);
      const llmResponse = await this.callLLMWithHistory(
        [...chatHistorySliced, getUserPrompt(userInput)],
        version,
      );

      if (llmResponse.function_call) {
        const { name } = llmResponse.function_call;
        // maybe we can use a map
        // Flow Strategy has a map
        switch (name) {
          case AnalyticsCopilotFunction.GET_INSIGHTS:
            return this.getInsights({
              id,
              userId,
              userInput,
              arguments: llmResponse.function_call.arguments,
              history: chatHistorySliced,
              version: props.version,
            });
          case AnalyticsCopilotFunction.GET_BENCHMARKS:
            return this.getBenchmarks({
              id,
              userId,
              userInput,
              arguments: llmResponse.function_call.arguments,
              history: chatHistorySliced,
              version: props.version,
            });
          case AnalyticsCopilotFunction.GET_INSIGHTS_BY_TAG:
            return this.getInsightsByTag({
              id,
              userId,
              userInput,
              arguments: llmResponse.function_call.arguments,
              history: chatHistorySliced,
              version: props.version,
            });
        }
      } else if (llmResponse.content) {
        return this.getLLMInstructions({
          id,
          userId,
          userInput,
          content: llmResponse.content,
          version: props.version,
        });
      }

      return {
        id,
        answer: 'Please provide more information.',
      };
    } catch (e) {
      this.logger.error(`Error while trying to get LLm response ${e}`);
      return {
        id,
        answer: ERROR_MESSAGE.replace(
          '{replace}',
          ` on trying to get any response`,
        ),
      };
    }
  }

  getMissingFieldsMessage(inputs: MissingFieldsInputs): string {
    const missingFields = [];

    if (!inputs.platformEnum) missingFields.push('platform');
    if (!inputs.industryName) missingFields.push('industry name');
    if (!inputs.kpiName) missingFields.push('KPI');
    if (!inputs.startDate) missingFields.push('start date');
    if (!inputs.endDate) missingFields.push('end date');

    return missingFields.length
      ? `Please provide the following missing information: ${missingFields.join(
          ', ',
        )}.`
      : NO_RESULTS_MESSAGE;
  }
}
