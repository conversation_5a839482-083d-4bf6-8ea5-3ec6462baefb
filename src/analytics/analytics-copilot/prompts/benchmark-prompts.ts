import { BENCHMARK_FUNCTION_PROMPT } from '../constants/analytics-copilot-prompt-constants';
import { AnalyticsCopilotBenchmarkFunctionResponse } from '../dto/analytics-copilot-benchmark.dto';
import { formatNormativeInsightByKpiFormat } from '../utils/llm.utils';
import { mapPlatformToDisplayName } from '../utils/platform.utils';

export function getFunctionPromptForBenchmark(
  props: AnalyticsCopilotBenchmarkFunctionResponse,
): string {
  let prompt = BENCHMARK_FUNCTION_PROMPT.replace(
    '{platform}',
    mapPlatformToDisplayName(props.platform),
  );

  const formattedNormativePerformance = formatNormativeInsightByKpiFormat(
    props.normativePerformance,
    props.format,
  );

  prompt = prompt.replace('{industry_name}', props.industry_name);
  prompt = prompt.replace('{kpi_name}', props.kpi_name);
  prompt = prompt.replace('{start_date}', props.start_date);
  prompt = prompt.replace('{end_date}', props.end_date);
  prompt = prompt.replace(
    '{normativePerformance}',
    `${formattedNormativePerformance}`,
  );
  return prompt.replace(/\n/g, ' ');
}
