import { USER_PROMPT } from '../constants/analytics-copilot-prompt-constants';
import { AnalyticsCopilotConversationHistory } from '../dto/analytics-copilot-conversation.dto';
import { AnalyticsCopilotConversationRole } from '../enums/analytics-copilot-conversation-role.enum';
import { getLastYear } from '../utils/date.utils';

export function getUserPrompt(
  userInput: string,
): AnalyticsCopilotConversationHistory {
  const { startDate, endDate } = getLastYear();
  let prompt = USER_PROMPT.replace('user_query', userInput);
  prompt = prompt.replace('{start_date}', startDate);
  prompt = prompt.replace('{end_date}', endDate);
  return {
    role: AnalyticsCopilotConversationRole.USER,
    content: prompt.replace(/\n/g, ' '),
  };
}
