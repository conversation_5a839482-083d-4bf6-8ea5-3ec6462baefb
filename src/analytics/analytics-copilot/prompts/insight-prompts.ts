import {
  BASE_PROMPT,
  INVERSE_HEALTH_DIRECTIONS,
  NO_INVERSE_HEALTH_DIRECTIONS,
  V1_FORMAT,
  V1_INSIGHTS_DIRECTIONS,
  V1_NUMBER_OF_INSIGHTS,
  V2_FORMAT,
  V2_INSIGHTS_DIRECTIONS,
  V2_NUMBER_OF_INSIGHTS,
} from '../constants/analytics-copilot-prompt-constants';

export const getInsightsPrompt = (
  version: number,
  inverseHealth: boolean,
  userQuery: string,
  insights: string,
) => {
  const replaceDirections =
    version === 1 ? V1_INSIGHTS_DIRECTIONS : V2_INSIGHTS_DIRECTIONS;

  const format = version === 1 ? V1_FORMAT : V2_FORMAT;

  const numberOfInsights =
    version === 1 ? V1_NUMBER_OF_INSIGHTS : V2_NUMBER_OF_INSIGHTS;

  const inverseHealthDirections = inverseHealth
    ? INVERSE_HEALTH_DIRECTIONS
    : NO_INVERSE_HEALTH_DIRECTIONS;

  let prompt = BASE_PROMPT.replace('{user_query}', userQuery);
  prompt = prompt.replace('{insights}', insights);
  prompt = prompt.replace('{inverseHealth}', inverseHealthDirections);
  prompt = prompt.replace('{replace}', replaceDirections);
  prompt = prompt.replace('{format}', format);
  prompt = prompt.replace('{numberOfInsights}', numberOfInsights);
  prompt = prompt.replace(/\n/g, ' ');
  return prompt;
};
