import {
  INVERSE_HEALTH_DIRECTIONS,
  NO_INVERSE_HEALTH_DIRECTIONS,
  INVERSE_HEALTH_TAG_DIRECTIONS,
  NO_INVERSE_HEALTH_TAG_DIRECTIONS,
  V2_INSIGHTS_BY_TAG_DIRECTIONS,
  BASE_PROMPT,
  V2_FORMAT,
  V2_NUMBER_OF_INSIGHTS_BY_TAG,
} from '../constants/analytics-copilot-prompt-constants';

export const getInsightsByTagPrompt = (
  inverseHealth: boolean,
  userQuery: string,
  insights: string,
) => {
  const inverseHealthDirections = inverseHealth
    ? INVERSE_HEALTH_DIRECTIONS
    : NO_INVERSE_HEALTH_DIRECTIONS;

  const inverseHealthTagDirections = inverseHealth
    ? INVERSE_HEALTH_TAG_DIRECTIONS
    : NO_INVERSE_HEALTH_TAG_DIRECTIONS;

  const v2TagDirections = V2_INSIGHTS_BY_TAG_DIRECTIONS.replace(
    '{inverseHealthTagDirections}',
    inverseHealthTagDirections,
  );

  let prompt = BASE_PROMPT.replace('{user_query}', userQuery);
  prompt = prompt.replace('{insights}', insights);
  prompt = prompt.replace('{inverseHealth}', inverseHealthDirections);
  prompt = prompt.replace('{replace}', v2TagDirections);
  prompt = prompt.replace('{format}', V2_FORMAT);
  prompt = prompt.replace('{numberOfInsights}', V2_NUMBER_OF_INSIGHTS_BY_TAG);
  prompt = prompt.replace(/\n/g, ' ');
  return prompt;
};
