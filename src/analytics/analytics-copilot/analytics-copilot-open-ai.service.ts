import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ERROR_MESSAGE } from './constants/analytics-copilot.constants';
import { AnalyticsCopilotConversationHistory } from './dto/analytics-copilot-conversation.dto';
import OpenAI from 'openai';
import { AnalyticsCopilotGptResponse } from './dto/analytics-copilot-gpt.dto';
import { AnalyticsCopilotConversationRole } from './enums/analytics-copilot-conversation-role.enum';
import {
  ChatCompletionCreateParamsNonStreaming,
  ChatCompletionMessageParam,
} from 'openai/resources';
import { SecretsConfigurationService } from '@vidmob/vidmob-nestjs-common';
import {
  GET_INSIGHTS_FUNCTION,
  GET_BENCHMARKS_FUNCTION,
  GET_INSIGHTS_BY_TAG_FUNCTION,
} from './constants/analytics-copilot-function-constants';

@Injectable()
export class OpenAiService implements OnModuleInit {
  private readonly logger = new Logger(OpenAiService.name);
  private openAI: OpenAI;

  constructor(
    private readonly secretsConfigurationService: SecretsConfigurationService,
  ) {}

  async onModuleInit() {
    try {
      // Need valid security token to get the OpenAI config
      const openAIConfig = await this.secretsConfigurationService.get<any>('openai');
      this.openAI = new OpenAI({
        apiKey: openAIConfig?.apiKey,
      });
      this.logger.log('OpenAI client initialized successfully for Analytics Copilot.');
    } catch (e) {
      this.logger.error(`Error while trying to initialize OpenAI client for Analytics Copilot: ${e}`);
    }
  }

  async getGptResponse(
    messages: AnalyticsCopilotConversationHistory[],
    withFunctions: boolean,
    version?: number,
  ): Promise<AnalyticsCopilotGptResponse> {
    try {
      if (!this.openAI) {
        return {
          role: AnalyticsCopilotConversationRole.ASSISTANT,
          content: `{"answer":"${ERROR_MESSAGE.replace('{replace}', '')}"}`,
        };
      }

      const payload: ChatCompletionCreateParamsNonStreaming = {
        messages: messages as ChatCompletionMessageParam[],
        model: 'gpt-4o',
        temperature: 0.1,
        response_format: {
          type: 'json_object',
        },
      };

      if (withFunctions) {
        payload.functions = [GET_INSIGHTS_FUNCTION, GET_BENCHMARKS_FUNCTION];

        if (version === 2) {
          payload.functions.push(GET_INSIGHTS_BY_TAG_FUNCTION);
        }
      }

      const response = await this.openAI.chat.completions.create(payload);
      const message = response.choices[0].message;

      return {
        role: AnalyticsCopilotConversationRole.ASSISTANT,
        content: message.content || '',
        function_call: message.function_call || undefined,
      };
    } catch (e) {
      this.logger.error(`Error while trying to get response from GPT: ${e}`);
      return {
        role: AnalyticsCopilotConversationRole.ASSISTANT,
        content: `{"answer":"${ERROR_MESSAGE.replace(
          '{replace}',
          ' on getting response from GPT',
        )}"}`,
      };
    }
  }
}
