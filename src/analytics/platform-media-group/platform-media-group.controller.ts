import {
  Controller,
  Request,
  ValidationPipe,
  Post,
  Body,
  Param,
  Delete,
} from '@nestjs/common';
import { PlatformMediaGroupService } from './platform-media-group.service';
import {
  createOrganizationWorkspaceAdAccountReports,
  readOrganizationWorkspaceAdAccountReports,
} from '../analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { PlatformMediaGroupCreateDto } from './dto/platform-media-group-create.dto';
import { ApiBody, ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PlatformMediaGroupUpdateDto } from './dto/platform-media-group-update.dto';
import { PlatformMediaGroupRequestDto } from './dto/platform-media-group-request.dto';
import { PlatformMediaGroupIndividualRequestDto } from './dto/platform-media-group-individual-request.dto';
import { GetAllPlatformMediaGroupsRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/getAllPlatformMediaGroupsRequestDto';

@ApiTags('Platform Media Group')
@ApiSecurity('Bearer Token')
@Controller('analytics/organization/:organizationId/platformMediaGroup')
export class PlatformMediaGroupController {
  constructor(
    private readonly platformMediaGroupService: PlatformMediaGroupService,
  ) {}

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiBody({ type: PlatformMediaGroupCreateDto })
  @Post()
  @Permissions(createOrganizationWorkspaceAdAccountReports)
  async createPlatformMediaGroup(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(new ValidationPipe())
    platformMediaGroupCreateDto: PlatformMediaGroupCreateDto,
  ) {
    const { userId, headers } = req;

    const userDetails = {
      userId,
      organizationId,
      authorization: headers?.authorization,
    };

    const platformMediaGroupRequestDto = {
      ...platformMediaGroupCreateDto,
      platform:
        platformMediaGroupCreateDto.platform as GetAllPlatformMediaGroupsRequestDto.PlatformEnum,
      organizationId,
      createdBy: userId,
    };

    return this.platformMediaGroupService.create(
      platformMediaGroupRequestDto,
      userDetails,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @Post('/all')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getPlatformMediaGroupsByAdAccountIds(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
    @Body()
    platformMediaGroupRequestDto: PlatformMediaGroupRequestDto,
  ) {
    const { userId, headers } = req;
    const { workspaceIds, platform } = platformMediaGroupRequestDto;

    const userDetails = {
      userId,
      organizationId,
      authorization: headers?.authorization,
    };

    return this.platformMediaGroupService.getPlatformMediaGroupsByPlatformAccountIds(
      {
        ...platformMediaGroupRequestDto,
        platform: platform as GetAllPlatformMediaGroupsRequestDto.PlatformEnum,
        userId,
      },
      paginationOptions,
      workspaceIds,
      userDetails,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'platformMediaGroupId',
    description: 'The id of the platform media group',
  })
  @Post('/:platformMediaGroupId')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getPlatformMediaGroup(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('platformMediaGroupId') platformMediaGroupId: number,
    @Body()
    platformMediaGroupIndividualRequestDto: PlatformMediaGroupIndividualRequestDto,
  ) {
    const { userId, headers } = req;
    const { workspaceIds } = platformMediaGroupIndividualRequestDto;

    const userDetails = {
      userId,
      organizationId,
      authorization: headers?.authorization,
    };

    return this.platformMediaGroupService.getPlatformMediaGroupById(
      platformMediaGroupId,
      userDetails,
      workspaceIds,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'platformMediaGroupId',
    description: 'The id of the platform media group',
  })
  @ApiBody({ type: PlatformMediaGroupUpdateDto })
  @Post('/:platformMediaGroupId/update')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async updatePlatformMediaGroup(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('platformMediaGroupId') platformMediaGroupId: number,
    @Body()
    platformMediaGroupUpdateDto: PlatformMediaGroupUpdateDto,
  ) {
    const { userId, headers } = req;

    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };

    return this.platformMediaGroupService.update(
      platformMediaGroupId,
      platformMediaGroupUpdateDto,
      userDetails,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The id of the organization',
  })
  @ApiParam({
    name: 'platformMediaGroupId',
    description: 'The id of the platform media group',
  })
  @Delete('/:platformMediaGroupId')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async deletePlatformMediaGroup(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('platformMediaGroupId') platformMediaGroupId: number,
  ) {
    const { userId } = req;

    return this.platformMediaGroupService.delete(platformMediaGroupId, userId);
  }
}
