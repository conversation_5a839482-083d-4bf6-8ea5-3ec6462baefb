import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import {
  PlatformMediaGroupRequestDto,
  PlatformMediaGroupService as PlatformMediaGroupServiceSDK,
  PlatformMediaGroupUpdateRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PlatformMediaGroupUpdateDto } from './dto/platform-media-group-update.dto';
import { UserDetailsDto } from '../dto/user-details.dto';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { GetAllPlatformMediaGroupsRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/getAllPlatformMediaGroupsRequestDto';

@Injectable()
export class PlatformMediaGroupService {
  private readonly logger = new Logger(PlatformMediaGroupService.name);

  constructor(
    private readonly platformMediaGroupServiceSDK: PlatformMediaGroupServiceSDK,
    private readonly analyticsUserService: AnalyticsUserService,
  ) {}

  async create(
    platformMediaGroupRequestDto: PlatformMediaGroupRequestDto,
    userDetails: UserDetailsDto,
  ) {
    const { workspaceIds, adAccountIds } = platformMediaGroupRequestDto;
    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      userDetails,
      workspaceIds,
      adAccountIds,
    );

    return this.platformMediaGroupServiceSDK.createPlatformMediaGroupAsPromise(
      platformMediaGroupRequestDto,
    );
  }

  async getPlatformMediaGroupById(
    platformMediaGroupId: number,
    userDetails: UserDetailsDto,
    workspaceIds: number[],
  ) {
    await this.validateUserAccessToWorkspaces(userDetails, workspaceIds);

    return this.platformMediaGroupServiceSDK.getByIdAsPromise(
      platformMediaGroupId,
      userDetails.userId,
    );
  }

  async getPlatformMediaGroupsByPlatformAccountIds(
    getAllPlatformMediaGroupsRequestDto: GetAllPlatformMediaGroupsRequestDto,
    paginationOptions: PaginationOptions,
    workspaceIds: number[],
    userDetails: UserDetailsDto,
  ) {
    const { offset, perPage, queryId } = paginationOptions;

    await this.validateUserAccessToWorkspaces(userDetails, workspaceIds);

    return this.platformMediaGroupServiceSDK.getByPlatformAdAccountIdsAsPromise(
      getAllPlatformMediaGroupsRequestDto,
      offset,
      perPage,
      queryId,
    );
  }

  async update(
    platformMediaGroupId: number,
    platformMediaGroupUpdateDto: PlatformMediaGroupUpdateDto,
    userDetails: UserDetailsDto,
  ) {
    const updateRequestDto: PlatformMediaGroupUpdateRequestDto = {
      ...platformMediaGroupUpdateDto,
      userId: userDetails.userId,
    };

    const { workspaceIds, adAccountIds } = platformMediaGroupUpdateDto;

    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      userDetails,
      workspaceIds,
      adAccountIds,
    );

    return this.platformMediaGroupServiceSDK.updatePlatformMediaGroupAsPromise(
      platformMediaGroupId,
      updateRequestDto,
    );
  }

  async delete(platformMediaGroupId: number, requesterId: number) {
    return this.platformMediaGroupServiceSDK.deletePlatformMediaGroupAsPromise(
      platformMediaGroupId,
      requesterId,
    );
  }

  private async validateUserAccessToWorkspaces(
    userDetails: UserDetailsDto,
    workspaceIds: number[],
  ) {
    const userWorkspaces =
      await this.analyticsUserService.fetchAllOrganizationWorkspacesForUser(
        userDetails,
      );

    const userWorkspaceIds = userWorkspaces.map((workspace) => workspace.id);
    const allExistInUserWorkspaces = workspaceIds.every((workspaceId) =>
      userWorkspaceIds.includes(workspaceId),
    );

    if (!allExistInUserWorkspaces) {
      throw new ForbiddenException(
        `User ${userDetails.userId} does not have access to all the submitted workspaces`,
      );
    }
  }
}
