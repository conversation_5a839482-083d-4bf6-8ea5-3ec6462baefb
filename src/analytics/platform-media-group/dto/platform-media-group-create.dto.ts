import {
  IsDateString,
  IsEnum,
  <PERSON><PERSON>otEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
} from 'class-validator';
import { PlatformMediaGroupRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/platformMediaGroupRequestDto';
import { Platform } from '../../../constants/platform.constants';
import { Transform } from 'class-transformer';

export class PlatformMediaGroupCreateDto {
  @IsNotEmpty()
  @MaxLength(128)
  name: string;

  @IsNotEmpty()
  @MaxLength(50)
  @IsEnum(Platform)
  @Transform(({ value }) => value.toLowerCase())
  platform: Platform;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  @IsNotEmpty()
  privacyLevel: PlatformMediaGroupRequestDto.PrivacyLevelEnum;

  @IsNotEmpty()
  platformMediaIds: string[];

  @IsNotEmpty()
  adAccountIds: string[];

  @IsNotEmpty()
  workspaceIds: number[];
}
