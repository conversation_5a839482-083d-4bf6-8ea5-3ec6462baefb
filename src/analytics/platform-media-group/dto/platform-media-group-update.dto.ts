import { AutoMap } from '@automapper/classes';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';
import { PlatformMediaGroupResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk';
import PrivacyLevelEnum = PlatformMediaGroupResponseDto.PrivacyLevelEnum;

export class PlatformMediaGroupUpdateDto {
  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  @IsString({ each: true })
  adAccountIds: string[];

  @AutoMap()
  @IsString()
  @IsOptional()
  name?: string;

  @AutoMap()
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @AutoMap()
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @AutoMap()
  @IsOptional()
  @IsEnum(PrivacyLevelEnum)
  privacyLevel?: PrivacyLevelEnum;

  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  @IsString({ each: true })
  platformMediaIds: string[];

  @AutoMap()
  @IsArray()
  workspaceIds: number[];
}
