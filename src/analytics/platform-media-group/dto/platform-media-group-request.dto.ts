import { AutoMap } from '@automapper/classes';
import {
  <PERSON>Array,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Platform } from '../../../constants/platform.constants';
import { GetAllPlatformMediaGroupsRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/getAllPlatformMediaGroupsRequestDto';

export class PlatformMediaGroupRequestDto {
  @AutoMap()
  @IsNotEmpty()
  @IsEnum(Platform)
  platform: Platform;

  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  @IsString({ each: true })
  adAccountIds: string[];

  @AutoMap()
  @IsNotEmpty()
  @IsNumber({}, { each: true })
  workspaceIds: number[];

  @AutoMap()
  @IsEnum(GetAllPlatformMediaGroupsRequestDto.SortByEnum)
  @IsOptional()
  sortBy: GetAllPlatformMediaGroupsRequestDto.SortByEnum =
    GetAllPlatformMediaGroupsRequestDto.SortByEnum.LastUpdated;

  @AutoMap()
  @IsEnum(GetAllPlatformMediaGroupsRequestDto.SortDirectionEnum)
  @IsOptional()
  sortDirection: GetAllPlatformMediaGroupsRequestDto.SortDirectionEnum =
    GetAllPlatformMediaGroupsRequestDto.SortDirectionEnum.Desc;
}
