import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PlatformMediaGroupService } from './platform-media-group.service';
import { PlatformMediaGroupController } from './platform-media-group.controller';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { HttpModule } from '@nestjs/axios';
import { AuthService } from '../../auth/services/auth.service';

@Module({
  imports: [HttpModule],
  controllers: [PlatformMediaGroupController],
  providers: [
    PlatformMediaGroupService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
  ],
})
export class PlatformMediaGroupModule {}
