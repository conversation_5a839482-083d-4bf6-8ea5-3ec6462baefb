import { <PERSON>du<PERSON> } from '@nestjs/common';
import { PlatformMediaGroupService } from './platform-media-group.service';
import { PlatformMediaGroupController } from './platform-media-group.controller';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { HttpModule } from '@nestjs/axios';
import { AuthService } from '../../auth/services/auth.service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { AccountManagementModule } from '../../account-management/account-management.module';

@Module({
  imports: [HttpModule, AccountManagementModule],
  controllers: [PlatformMediaGroupController],
  providers: [
    PlatformMediaGroupService,
    AnalyticsUserService,
    AuthService,
    OrganizationUserService,
  ],
})
export class PlatformMediaGroupModule {}
