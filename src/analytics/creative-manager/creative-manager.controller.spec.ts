import { CreativeManagerController } from './creative-manager.controller';
import { CreativeManagerService } from './creative-manager.service';
import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { DimensionPerformanceRequest } from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  mockUserAdAccountIds,
  mockWorkspaceId,
  mockUserAdAccountsAndWorkspaces,
} from '../saved-report/mocks/analytics-report.mock';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

describe('CreativeManagerController', () => {
  let controller: CreativeManagerController;
  let creativeManagerService: CreativeManagerService;
  let reportSpendPermissionService: ReportSpendPermissionService;

  const mockRequest: DimensionPerformanceRequest = {
    startDate: '2022-01-01',
    endDate: '2023-12-07',
    workspaceIds: [mockWorkspaceId],
    adAccountIds: mockUserAdAccountIds,
    kpiId: '99',
    platform: DimensionPerformanceRequest.PlatformEnum.Facebook,
    currency: 'BRL',
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    sortBy: 'impressions',
    sortDirection: DimensionPerformanceRequest.SortDirectionEnum.Asc,
  };

  const mockAuthorization = {
    userId: 20202,
    headers: {
      authorization: 'mock-authorization',
    },
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CreativeManagerController],
      providers: [
        {
          provide: CreativeManagerService,
          useValue: {
            getCreatives: jest.fn(),
            getAds: jest.fn(),
          },
        },
        {
          provide: AnalyticsUserService,
          useValue: {
            fetchUserAdAccountsAndWorkspaces: jest
              .fn()
              .mockResolvedValue(mockUserAdAccountsAndWorkspaces),
            fetchAllAdAccountsForWorkspaces: jest
              .fn()
              .mockResolvedValue(
                mockUserAdAccountIds.map((id) => ({ platformAccountId: id })),
              ),
            validateUserAccessToAdAccountsAndWorkspaces:
              AnalyticsUserService.prototype
                .validateUserAccessToAdAccountsAndWorkspaces,
          },
        },
        {
          provide: ReportSpendPermissionService,
          useValue: {
            validateOrganizationAccessToSpendKpi: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<CreativeManagerController>(
      CreativeManagerController,
    );
    creativeManagerService = module.get<CreativeManagerService>(
      CreativeManagerService,
    );
    reportSpendPermissionService = module.get<ReportSpendPermissionService>(
      ReportSpendPermissionService,
    );
  });

  it('should call service to get creative when validation passes', async () => {
    await controller.getCreativesPerformance(
      mockRequest,
      { perPage: 10, offset: 10 },
      'mock-organization-id',
      mockAuthorization,
    );

    expect(creativeManagerService.getCreatives).toBeCalledWith(mockRequest, {
      perPage: 10,
      offset: 10,
    });
    expect(
      reportSpendPermissionService.validateOrganizationAccessToSpendKpi,
    ).toBeCalledWith(
      mockRequest.kpiId,
      mockRequest.platform,
      'mock-organization-id',
    );
  });

  it('should call service to get ads when validation passes', async () => {
    await controller.getAdsPerformance(
      mockRequest,
      { perPage: 10, offset: 10 },
      'mock-organization-id',
      mockAuthorization,
    );

    expect(creativeManagerService.getAds).toBeCalledWith(mockRequest, {
      perPage: 10,
      offset: 10,
    });
    expect(
      reportSpendPermissionService.validateOrganizationAccessToSpendKpi,
    ).toBeCalledWith(
      mockRequest.kpiId,
      mockRequest.platform,
      'mock-organization-id',
    );
  });

  it('should call fail to get ads when ad accounts validation fails', async () => {
    await expect(
      controller.getCreativesPerformance(
        { ...mockRequest, workspaceIds: [2222] },
        { perPage: 10, offset: 10 },
        'mock-organization-id',
        mockAuthorization,
      ),
    ).rejects.toThrow(
      'User 20202 does not have access to all the submitted workspaces',
    );
  });

  it('should call fail to get creatives when workspaces validation fails', async () => {
    await expect(
      controller.getAdsPerformance(
        { ...mockRequest, adAccountIds: ['e2u94028u84280y09u42'] },
        { perPage: 10, offset: 10 },
        'mock-organization-id',
        mockAuthorization,
      ),
    ).rejects.toThrow(
      'User 20202 does not have access to all the submitted ad accounts',
    );
  });
});
