import { Api<PERSON>aram, ApiTags } from '@nestjs/swagger';
import { Body, Controller, Param, Post, Request, Res } from '@nestjs/common';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { readOrganizationWorkspaceAdAccountReports } from '../analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { CreativeManagerService } from './creative-manager.service';
import {
  DimensionPerformanceRequest,
  GetAdLevelPerformance200Response,
  GetCreativeLevelPerformance200Response,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { Response } from 'express';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

@ApiTags('Creative Manager')
@Controller({
  path: 'creative-manager/organization/:organizationId',
})
export class CreativeManagerController {
  constructor(
    private readonly creativeManagerService: CreativeManagerService,
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly reportSpendPermissionService: ReportSpendPermissionService,
  ) {}

  @ApiParam({
    name: 'organizationId',
    description: 'The organization we get creatives for',
    required: true,
  })
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Post('creatives')
  async getCreativesPerformance(
    @Body() creativeRequestDto: DimensionPerformanceRequest,
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('organizationId') organizationId: string,
    @Request() req: any,
  ): Promise<GetCreativeLevelPerformance200Response> {
    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      creativeRequestDto.kpiId,
      creativeRequestDto.platform,
      organizationId,
    );

    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      {
        organizationId,
        userId: req.userId,
        authorization: req.headers.authorization,
      },
      creativeRequestDto.workspaceIds,
      creativeRequestDto.adAccountIds,
    );

    return this.creativeManagerService.getCreatives(
      creativeRequestDto,
      paginationOptions,
    );
  }

  @ApiParam({
    name: 'organizationId',
    description: 'The organization we get creatives for',
    required: true,
  })
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Post('ads')
  async getAdsPerformance(
    @Body() adRequestDto: DimensionPerformanceRequest,
    @GetPagination() paginationOptions: PaginationOptions,
    @Param('organizationId') organizationId: string,
    @Request() req: any,
  ): Promise<GetAdLevelPerformance200Response> {
    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      adRequestDto.kpiId,
      adRequestDto.platform,
      organizationId,
    );
    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      {
        organizationId,
        userId: req.userId,
        authorization: req.headers.authorization,
      },
      adRequestDto.workspaceIds,
      adRequestDto.adAccountIds,
    );

    return this.creativeManagerService.getAds(adRequestDto, paginationOptions);
  }

  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Post('creatives/csv')
  async downloadCreativesPerformanceCSV(
    @Param('organizationId') organizationId: string,
    @Body() creativeRequestDto: DimensionPerformanceRequest,
    @Request() req: any,
    @Res() res: Response,
  ): Promise<void> {
    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      creativeRequestDto.kpiId,
      creativeRequestDto.platform,
      organizationId,
    );
    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      {
        organizationId,
        userId: req.userId,
        authorization: req.headers.authorization,
      },
      creativeRequestDto.workspaceIds,
      creativeRequestDto.adAccountIds,
    );

    const csvString =
      await this.creativeManagerService.getCreativesPerformanceAsCSV(
        creativeRequestDto,
      );
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="Creatives_Creative_Manager_${creativeRequestDto.startDate}_${creativeRequestDto.endDate}"`,
    );
    res.send(csvString);
  }

  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Post('ads/csv')
  async downloadAdsPerformanceCSV(
    @Param('organizationId') organizationId: string,
    @Body() adRequestDto: DimensionPerformanceRequest,
    @Request() req: any,
    @Res() res: Response,
  ): Promise<void> {
    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      adRequestDto.kpiId,
      adRequestDto.platform,
      organizationId,
    );
    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      {
        organizationId,
        userId: req.userId,
        authorization: req.headers.authorization,
      },
      adRequestDto.workspaceIds,
      adRequestDto.adAccountIds,
    );

    const csvString = await this.creativeManagerService.getAdsPerformanceAsCSV(
      adRequestDto,
    );
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="Ads_Creative_Manager_${adRequestDto.startDate}_${adRequestDto.endDate}"`,
    );
    res.send(csvString);
  }
}
