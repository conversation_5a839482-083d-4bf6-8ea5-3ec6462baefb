import { <PERSON><PERSON><PERSON> } from '@nestjs/common';

import { HttpModule } from '@nestjs/axios';
import { CreativeManagerController } from './creative-manager.controller';
import { CreativeManagerService } from './creative-manager.service';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';

@Module({
  imports: [HttpModule],
  controllers: [CreativeManagerController],
  providers: [
    CreativeManagerService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
    ReportSpendPermissionService,
    OrganizationUserService,
  ],
})
export class CreativeManagerModule {}
