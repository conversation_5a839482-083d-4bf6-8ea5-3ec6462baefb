import { Injectable } from '@nestjs/common';
import {
  GetAdLevelPerformance200Response,
  GetCreativeLevelPerformance200Response,
  DimensionPerformanceRequest,
  DimensionPerformanceService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

@Injectable()
export class CreativeManagerService {
  constructor(
    private readonly dimensionPerformanceService: DimensionPerformanceService,
  ) {}

  async getCreatives(
    creativePerformanceRequest: DimensionPerformanceRequest,
    paginationOptions: PaginationOptions,
  ): Promise<GetCreativeLevelPerformance200Response> {
    return this.dimensionPerformanceService.getCreativeLevelPerformanceAsPromise(
      creativePerformanceRequest,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async getAds(
    adPerformanceRequest: DimensionPerformanceRequest,
    paginationOptions: PaginationOptions,
  ): Promise<GetAdLevelPerformance200Response> {
    return this.dimensionPerformanceService.getAdLevelPerformanceAsPromise(
      adPerformanceRequest,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async getCreativesPerformanceAsCSV(
    creativePerformanceRequest: DimensionPerformanceRequest,
  ): Promise<any> {
    return this.dimensionPerformanceService.getCreativeLevelPerformanceCsvAsPromise(
      creativePerformanceRequest,
    );
  }

  async getAdsPerformanceAsCSV(
    adPerformanceRequest: DimensionPerformanceRequest,
  ): Promise<any> {
    return this.dimensionPerformanceService.getAdLevelPerformanceCsvAsPromise(
      adPerformanceRequest,
    );
  }
}
