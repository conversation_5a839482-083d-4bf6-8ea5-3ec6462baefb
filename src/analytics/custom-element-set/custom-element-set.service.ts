import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  CreateElementSetRequestDto,
  CustomElementSetService as CustomElementSetServiceSDK,
  GetAllCustomElementSets200Response,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { CreateCustomElementSet200Response } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/createCustomElementSet200Response';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';

@Injectable()
export class CustomElementSetService {
  constructor(
    private readonly customElementSetServiceSDK: CustomElementSetServiceSDK,
    private readonly legacyAnalyticsService: LegacyAnalyticsService,
  ) {}

  async createCustomElementSet(
    organizationId: string,
    { userId, authorization }: { userId: number; authorization: string },
    createElementSetRequestDto: CreateElementSetRequestDto,
  ): Promise<CreateCustomElementSet200Response> {
    const response =
      await this.customElementSetServiceSDK.createCustomElementSetAsPromise(
        organizationId,
        userId,
        createElementSetRequestDto,
      );

    await this.legacyAnalyticsService.clearLegacyAnalyticsServiceCache(
      organizationId,
      authorization,
    );

    return response;
  }

  async updateCustomElementSet(
    organizationId: string,
    { userId, authorization }: { userId: number; authorization: string },
    customElementSetId: number,
    createElementSetRequestDto: CreateElementSetRequestDto,
  ): Promise<CreateCustomElementSet200Response> {
    const response =
      await this.customElementSetServiceSDK.updateCustomElementSetAsPromise(
        organizationId,
        userId,
        customElementSetId,
        createElementSetRequestDto,
      );

    await this.legacyAnalyticsService.clearLegacyAnalyticsServiceCache(
      organizationId,
      authorization,
    );

    return response;
  }

  async deleteCustomElementSet(
    organizationId: string,
    platform: string,
    customElementSetId: number,
    authorization: string,
  ) {
    const response =
      await this.customElementSetServiceSDK.deleteCustomElementSetAsPromise(
        organizationId,
        platform,
        customElementSetId,
      );

    await this.legacyAnalyticsService.clearLegacyAnalyticsServiceCache(
      organizationId,
      authorization,
    );

    return response;
  }

  async getCustomElementSetById(
    organizationId: string,
    customElementSetId: number,
  ): Promise<CreateCustomElementSet200Response> {
    return this.customElementSetServiceSDK.getCustomElementSetByIdAsPromise(
      organizationId,
      customElementSetId,
    );
  }

  async getAllCustomElementSets(
    organizationId: string,
    platform: string,
    paginationOptions: PaginationOptions,
  ): Promise<GetAllCustomElementSets200Response> {
    return this.customElementSetServiceSDK.getAllCustomElementSetsAsPromise(
      organizationId,
      platform,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }
}
