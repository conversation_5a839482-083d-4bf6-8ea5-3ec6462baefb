import { Api<PERSON>aram, ApiTags } from '@nestjs/swagger';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Request,
} from '@nestjs/common';
import {
  CreateElementSetRequestDto,
  DeleteCustomElementSet200Response,
  CreateCustomElementSet200Response,
  GetAllCustomElementSets200Response,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { CustomElementSetService } from './custom-element-set.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  updateOrganizationAdAccountsDetails,
  readOrganizationAdAccountsDetails,
} from '../analytics.permissions';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';

@ApiTags('Custom Element Set')
@Controller('custom-element-set')
export class CustomElementSetController {
  constructor(
    private readonly customElementSetService: CustomElementSetService,
  ) {}

  /**
   * Save a custom element set to mysql and snowflake media tag tables
   * See soa-analytics-service-sdk for the request and response types
   *
   * @param req
   * @param organizationId
   * @param customElementSetRequest
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The id of organization to which the custom element set belongs',
    required: true,
  })
  @Post('organization/:organizationId')
  @Permissions(updateOrganizationAdAccountsDetails)
  createCustomElementSet(
    @Param('organizationId') organizationId: string,
    @Body() customElementSetRequest: CreateElementSetRequestDto,
    @Request() req: any,
  ): Promise<CreateCustomElementSet200Response> {
    return this.customElementSetService.createCustomElementSet(
      organizationId,
      { userId: req.userId, authorization: req.headers.authorization },
      customElementSetRequest,
    );
  }

  /**
   * Update single custom element set for organization and platform
   * See soa-analytics-service-sdk for the request and response types
   *
   * @param organizationId
   * @param customElementSetId
   * @param customElementSetRequest
   * @param req
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The id of organization to which the custom element set belongs',
    required: true,
  })
  @ApiParam({
    name: 'customElementSetId',
    description: 'The id of element set to be fetched',
    required: true,
  })
  @Post('organization/:organizationId/customElementSet/:customElementSetId')
  @Permissions(updateOrganizationAdAccountsDetails)
  updateCustomElementSet(
    @Param('organizationId') organizationId: string,
    @Param('customElementSetId') customElementSetId: number,
    @Body() customElementSetRequest: CreateElementSetRequestDto,
    @Request() req: any,
  ): Promise<CreateCustomElementSet200Response> {
    return this.customElementSetService.updateCustomElementSet(
      organizationId,
      { userId: req.userId, authorization: req.headers.authorization },
      customElementSetId,
      customElementSetRequest,
    );
  }

  /**
   * Delete single custom element set for organization and platform
   * See soa-analytics-service-sdk for the request and response types
   *
   * @param organizationId
   * @param platform
   * @param customElementSetId
   * @param req
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The id of organization to which the custom element set belongs',
    required: true,
  })
  @ApiParam({
    name: 'platform',
    description: 'The platform for which the custom element set is created',
    required: true,
  })
  @ApiParam({
    name: 'customElementSetId',
    description: 'The id of element set to be fetched',
    required: true,
  })
  @Delete(
    'organization/:organizationId/platform/:platform/customElementSet/:customElementSetId',
  )
  @Permissions(updateOrganizationAdAccountsDetails)
  deleteCustomElementSet(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @Param('customElementSetId') customElementSetId: number,
    @Request() req: any,
  ): Promise<DeleteCustomElementSet200Response> {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    return this.customElementSetService.deleteCustomElementSet(
      organizationId,
      platform,
      customElementSetId,
      req.headers.authorization,
    );
  }

  /**
   * Get single custom element set by id
   * See soa-analytics-service-sdk for the request and response types
   *
   * @param organizationId
   * @param customElementSetId
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The id of organization to which the custom element set belongs',
    required: true,
  })
  @ApiParam({
    name: 'customElementSetId',
    description: 'The id of element set to be fetched',
    required: true,
  })
  @Get('organization/:organizationId/customElementSet/:customElementSetId')
  @Permissions(readOrganizationAdAccountsDetails)
  getCustomElementSetById(
    @Param('organizationId') organizationId: string,
    @Param('customElementSetId') customElementSetId: number,
  ): Promise<CreateCustomElementSet200Response> {
    return this.customElementSetService.getCustomElementSetById(
      organizationId,
      customElementSetId,
    );
  }

  /**
   * Get all custom element sets for ad accounts
   * See soa-analytics-service-sdk for the request and response types
   *
   * @param paginationOptions
   * @param organizationId
   * @param platform
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The id of organization to which the custom element set belongs',
    required: true,
  })
  @ApiParam({
    name: 'platform',
    description: 'The platform for which the custom element set was created',
    required: true,
  })
  @Get('organization/:organizationId/platform/:platform')
  @Permissions(readOrganizationAdAccountsDetails)
  getAllCustomElementSets(
    @Param('organizationId') organizationId: string,
    @Param('platform') platform: string,
    @GetPagination() paginationOptions: PaginationOptions,
  ): Promise<GetAllCustomElementSets200Response> {
    return this.customElementSetService.getAllCustomElementSets(
      organizationId,
      platform,
      paginationOptions,
    );
  }
}
