import { CustomElementSetService } from './custom-element-set.service';
import {
  CreateElementSetRequestDto,
  CustomElementSetService as CustomElementSetServiceSDK,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Test, TestingModule } from '@nestjs/testing';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';

describe('CustomElementSetService', () => {
  let service: CustomElementSetService;
  const mockCreateCustomElementSetAsPromise = jest.fn();
  const mockUpdateCustomElementSetAsPromise = jest.fn();
  const mockDeleteCustomElementSetAsPromise = jest.fn();
  const mockGetCustomElementSetByIdAsPromise = jest.fn();
  const mockGetAllCustomElementSetsAsPromise = jest.fn();

  const mockClearLegacyAnalyticsServiceCache = jest.fn();

  const mockCreateCustomElementSetRequestDto: CreateElementSetRequestDto = {
    description: 'test',
    name: 'test',
    elementSetInfo: {
      type: 'OPERATION',
      operation: 'OR',
      elements: [
        {
          type: 'TAG',
          elementType: 'CTA:BY_LINE',
          name: 'Learn More',
        },
        {
          type: 'TAG',
          elementType: 'CTA:BY_LINE',
          name: 'Shop Now',
        },
      ],
    },
    platform: 'facebook',
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomElementSetService,
        {
          provide: LegacyAnalyticsService,
          useValue: {
            clearLegacyAnalyticsServiceCache:
              mockClearLegacyAnalyticsServiceCache,
          },
        },
        {
          provide: CustomElementSetServiceSDK,
          useValue: {
            createCustomElementSetAsPromise:
              mockCreateCustomElementSetAsPromise,
            updateCustomElementSetAsPromise:
              mockUpdateCustomElementSetAsPromise,
            deleteCustomElementSetAsPromise:
              mockDeleteCustomElementSetAsPromise,
            getCustomElementSetByIdAsPromise:
              mockGetCustomElementSetByIdAsPromise,
            getAllCustomElementSetsAsPromise:
              mockGetAllCustomElementSetsAsPromise,
          },
        },
      ],
    }).compile();

    service = module.get<CustomElementSetService>(CustomElementSetService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('createCustomElementSet should call serviceSDK createCustomElementSetAsPromise', async () => {
    await service.createCustomElementSet(
      'organizationId',
      { userId: 11111, authorization: 'Bearer token' },
      mockCreateCustomElementSetRequestDto,
    );
    expect(mockCreateCustomElementSetAsPromise).toHaveBeenCalledWith(
      'organizationId',
      11111,
      mockCreateCustomElementSetRequestDto,
    );
    expect(mockClearLegacyAnalyticsServiceCache).toHaveBeenCalledWith(
      'organizationId',
      'Bearer token',
    );
  });

  it('updateCustomElementSet should call serviceSDK updateCustomElementSetAsPromise', async () => {
    await service.updateCustomElementSet(
      'organizationId',
      { userId: 11111, authorization: 'Bearer token' },
      10,
      mockCreateCustomElementSetRequestDto,
    );
    expect(mockUpdateCustomElementSetAsPromise).toHaveBeenCalledWith(
      'organizationId',
      11111,
      10,
      mockCreateCustomElementSetRequestDto,
    );
    expect(mockClearLegacyAnalyticsServiceCache).toHaveBeenCalledWith(
      'organizationId',
      'Bearer token',
    );
  });

  it('deleteCustomElementSet should call serviceSDK deleteCustomElementSetAsPromise', async () => {
    await service.deleteCustomElementSet(
      'organizationId',
      'facebook',
      1,
      'Bearer token',
    );
    expect(mockDeleteCustomElementSetAsPromise).toHaveBeenCalledWith(
      'organizationId',
      'facebook',
      1,
    );
  });

  it('getCustomElementSetById should call serviceSDK getCustomElementSetByIdAsPromise', async () => {
    await service.getCustomElementSetById('organizationId', 1);
    expect(mockGetCustomElementSetByIdAsPromise).toHaveBeenCalledWith(
      'organizationId',
      1,
    );
  });

  it('getAllCustomElementSets should call serviceSDK getAllCustomElementSetsAsPromise', async () => {
    await service.getAllCustomElementSets('organizationId', 'facebook', {
      perPage: 10,
      offset: 0,
    });
    expect(mockGetAllCustomElementSetsAsPromise).toHaveBeenCalledWith(
      'organizationId',
      'facebook',
      0,
      10,
      undefined,
    );
  });

  it('getAllCustomElementSets should call serviceSDK getAllCustomElementSetsAsPromise with queryId', async () => {
    await service.getAllCustomElementSets('organizationId', 'facebook', {
      perPage: 10,
      offset: 0,
      queryId: 'test',
    });
    expect(mockGetAllCustomElementSetsAsPromise).toHaveBeenCalledWith(
      'organizationId',
      'facebook',
      0,
      10,
      'test',
    );
  });
});
