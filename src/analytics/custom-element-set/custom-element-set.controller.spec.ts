import { CustomElementSetController } from './custom-element-set.controller';
import { Test, TestingModule } from '@nestjs/testing';
import { CreateElementSetRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { CustomElementSetService } from './custom-element-set.service';

describe('CustomElementSetController', () => {
  let controller: CustomElementSetController;

  const mockCreateCustomElementSet = jest.fn();
  const mockUpdateCustomElementSet = jest.fn();
  const mockDeleteCustomElementSet = jest.fn();
  const mockGetCustomElementSetById = jest.fn();
  const mockGetAllCustomElementSets = jest.fn();

  const mockCreateCustomElementSetRequestDto: CreateElementSetRequestDto = {
    description: 'test',
    name: 'test',
    elementSetInfo: {
      type: 'OPERATION',
      operation: 'OR',
      elements: [
        {
          type: 'TAG',
          elementType: 'CTA:BY_LINE',
          name: 'Learn More',
        },
        {
          type: 'TAG',
          elementType: 'CTA:BY_LINE',
          name: 'Shop Now',
        },
      ],
    },
    platform: 'facebook',
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CustomElementSetController,
        {
          provide: CustomElementSetService,
          useValue: {
            createCustomElementSet: mockCreateCustomElementSet,
            updateCustomElementSet: mockUpdateCustomElementSet,
            deleteCustomElementSet: mockDeleteCustomElementSet,
            getCustomElementSetById: mockGetCustomElementSetById,
            getAllCustomElementSets: mockGetAllCustomElementSets,
          },
        },
      ],
    }).compile();

    controller = module.get<CustomElementSetController>(
      CustomElementSetController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('createCustomElementSet should call controllerSDK createCustomElementSet', async () => {
    await controller.createCustomElementSet(
      'organizationId',
      mockCreateCustomElementSetRequestDto,
      { userId: 11111, headers: { authorization: 'Bearer token' } },
    );
    expect(mockCreateCustomElementSet).toHaveBeenCalledWith(
      'organizationId',
      { userId: 11111, authorization: 'Bearer token' },
      mockCreateCustomElementSetRequestDto,
    );
  });

  it('updateCustomElementSet should call controllerSDK updateCustomElementSet', async () => {
    await controller.updateCustomElementSet(
      'organizationId',
      1,
      mockCreateCustomElementSetRequestDto,
      { userId: 11111, headers: { authorization: 'Bearer token' } },
    );
    expect(mockUpdateCustomElementSet).toHaveBeenCalledWith(
      'organizationId',
      { userId: 11111, authorization: 'Bearer token' },
      1,
      mockCreateCustomElementSetRequestDto,
    );
  });

  it('deleteCustomElementSet should call controllerSDK deleteCustomElementSet', async () => {
    await controller.deleteCustomElementSet(
      'mockOrganizationId',
      'facebook',
      1,
      { headers: { authorization: 'Bearer token' } },
    );
    expect(mockDeleteCustomElementSet).toHaveBeenCalledWith(
      'mockOrganizationId',
      'facebook',
      1,
      'Bearer token',
    );
  });

  it('getCustomElementSetById should call controllerSDK getCustomElementSetById', async () => {
    await controller.getCustomElementSetById('organizationId', 1);
    expect(mockGetCustomElementSetById).toHaveBeenCalledWith(
      'organizationId',
      1,
    );
  });

  it('getAllCustomElementSets should call controllerSDK getAllCustomElementSets', async () => {
    await controller.getAllCustomElementSets('organizationId', 'facebook', {
      perPage: 10,
      offset: 0,
    });
    expect(mockGetAllCustomElementSets).toHaveBeenCalledWith(
      'organizationId',
      'facebook',
      {
        perPage: 10,
        offset: 0,
      },
    );
  });
});
