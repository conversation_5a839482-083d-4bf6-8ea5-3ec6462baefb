import { Injectable } from '@nestjs/common';
import { MediaService } from '@vidmob/vidmob-organization-service-sdk';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';
import { UserDetailsDto } from '../dto/user-details.dto';
import { PlatformMediaRequestDto } from './dto/platform-media-request.dto';
import {
  DimensionsService,
  ReadMultiAssetCreativeDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';

interface PlatformMediaItem {
  id: number;
  mediaId: number;
  platformMediaId: string;
  isCreativeInMultiAssetAd?: boolean;
  media: object;
}

export interface PlatformMediaResponse {
  platformMedias: Record<string, PlatformMediaItem>;
}

@Injectable()
export class AnalyticsMediaService {
  constructor(
    private readonly mediaServiceSDK: MediaService,
    private readonly dimensionServiceSDK: DimensionsService,
    private readonly legacyAnalyticsService: LegacyAnalyticsService,
  ) {}

  /**
   * Validates if the user can view the media and returns the correct media
   * @param mediaId
   * @param userId
   */
  async getMediaForUser(mediaId: number, userId: number) {
    try {
      const { result } =
        await this.mediaServiceSDK.validatePersonCanViewMediaAsPromise(
          mediaId,
          userId,
        );

      if (!result.canView) {
        throw new Error(
          `User ${userId} does not have permission to view media Id ${mediaId}`,
        );
      }

      return this.mediaServiceSDK.getMediaByIdAsPromise(mediaId);
    } catch (e) {
      throw new Error(`Unable to get media for media Id ${mediaId}, ${e}`);
    }
  }

  async getPlatformMedia(
    userDetails: UserDetailsDto,
    platformMediaRequestDto: PlatformMediaRequestDto,
  ) {
    const [{ result: multiAssetCreativeFlags }, platformMedia] =
      await Promise.all([
        this.dimensionServiceSDK.getMultiAssetFlagForCreativesAsPromise({
          platform: platformMediaRequestDto.platform,
          adAccountIds: platformMediaRequestDto.adAccountIds,
          startDate: platformMediaRequestDto.startDate,
          endDate: platformMediaRequestDto.endDate,
          platformMediaIds: platformMediaRequestDto.platformMediaIds,
        }),
        this.legacyAnalyticsService.getPlatformMedia(
          userDetails,
          platformMediaRequestDto,
        ),
      ]);

    return this.mergePlatformMediasWithMultiAssetFlag(
      platformMedia,
      multiAssetCreativeFlags,
    );
  }

  private mergePlatformMediasWithMultiAssetFlag(
    platformMedia: PlatformMediaResponse,
    flags: ReadMultiAssetCreativeDto[],
  ) {
    const flagMap = new Map(
      flags.map((f) => [f.platformMediaId, f.isCreativeInMultiAssetAd]),
    );

    Object.values(platformMedia.platformMedias ?? {}).forEach((media) => {
      media.isCreativeInMultiAssetAd =
        flagMap.get(media.platformMediaId) ?? false;
    });

    return platformMedia;
  }
}
