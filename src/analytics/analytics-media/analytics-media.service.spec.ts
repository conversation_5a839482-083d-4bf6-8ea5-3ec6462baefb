import { Test, TestingModule } from '@nestjs/testing';
import { UserDetailsDto } from '../dto/user-details.dto';
import {
  AnalyticsMediaService,
  PlatformMediaResponse,
} from './analytics-media.service';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';
import {
  DimensionsService,
  ReadMultiAssetCreativeDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PlatformMediaRequestDto } from './dto/platform-media-request.dto';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { MediaService } from '@vidmob/vidmob-organization-service-sdk';

describe('PlatformMediaService', () => {
  let service: AnalyticsMediaService;
  let dimensionServiceSdk: jest.Mocked<DimensionsService>;
  let legacyAnalyticsService: jest.Mocked<LegacyAnalyticsService>;
  let mediaServiceSdk: jest.Mocked<MediaService>;

  const mockUserDetails: UserDetailsDto = {
    userId: 1,
    authorization: 'Bearer tok',
    organizationId: 'org1',
  };

  const mockPlatformMediaRequestDto: PlatformMediaRequestDto = {
    platform: Platform.FACEBOOK,
    platformMediaIds: ['pm1', 'pm2', 'pm3'],
    workspaceIds: [10, 20],
    adAccountIds: ['acc1'],
    startDate: '2025-01-01',
    endDate: '2025-01-31',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AnalyticsMediaService,
        {
          provide: DimensionsService,
          useValue: {
            getMultiAssetFlagForCreativesAsPromise: jest.fn(),
          },
        },
        {
          provide: LegacyAnalyticsService,
          useValue: {
            getPlatformMedia: jest.fn(),
          },
        },
        {
          provide: MediaService,
          useValue: {
            validatePersonCanViewMediaAsPromise: jest.fn(),
            getMediaByIdAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(AnalyticsMediaService);
    dimensionServiceSdk = module.get(DimensionsService) as any;
    legacyAnalyticsService = module.get(LegacyAnalyticsService) as any;
    mediaServiceSdk = module.get(MediaService) as any;
  });

  describe('mergePlatformMediasWithMultiAssetFlag (private)', () => {
    it('sets isCreativeInMultiAssetAd true/false based on flags and defaults to false', () => {
      const input: PlatformMediaResponse = {
        platformMedias: {
          pm1: { platformMediaId: 'pm1', id: 1, mediaId: 111, media: {} },
          pm2: { platformMediaId: 'pm2', id: 2, mediaId: 222, media: {} },
          pm3: { platformMediaId: 'pm3', id: 3, mediaId: 333, media: {} },
        },
      };

      const flags: ReadMultiAssetCreativeDto[] = [
        { platformMediaId: 'pm1', isCreativeInMultiAssetAd: true },
        { platformMediaId: 'pm2', isCreativeInMultiAssetAd: false },
      ];

      const output = (service as any).mergePlatformMediasWithMultiAssetFlag(
        JSON.parse(JSON.stringify(input)),
        flags,
      ) as PlatformMediaResponse;

      expect(output.platformMedias.pm1.isCreativeInMultiAssetAd).toBe(true);
      expect(output.platformMedias.pm2.isCreativeInMultiAssetAd).toBe(false);
      expect(output.platformMedias.pm3.isCreativeInMultiAssetAd).toBe(false);
    });
  });

  describe('getPlatformMedia', () => {
    it('calls both SDK methods and returns merged result', async () => {
      const fakeFlags = [
        { platformMediaId: 'pm1', isCreativeInMultiAssetAd: true },
      ];
      const fakePlatformMedia: PlatformMediaResponse = {
        platformMedias: {
          pm1: { platformMediaId: 'pm1', id: 1, mediaId: 111, media: {} },
          pm2: { platformMediaId: 'pm2', id: 2, mediaId: 222, media: {} },
        },
      };

      dimensionServiceSdk.getMultiAssetFlagForCreativesAsPromise.mockResolvedValue(
        {
          result: fakeFlags,
        } as any,
      );
      legacyAnalyticsService.getPlatformMedia.mockResolvedValue(
        fakePlatformMedia,
      );

      const result = await service.getPlatformMedia(
        mockUserDetails,
        mockPlatformMediaRequestDto,
      );

      expect(
        dimensionServiceSdk.getMultiAssetFlagForCreativesAsPromise,
      ).toHaveBeenCalledWith({
        platform: mockPlatformMediaRequestDto.platform,
        adAccountIds: mockPlatformMediaRequestDto.adAccountIds,
        startDate: mockPlatformMediaRequestDto.startDate,
        endDate: mockPlatformMediaRequestDto.endDate,
        platformMediaIds: mockPlatformMediaRequestDto.platformMediaIds,
      });
      expect(legacyAnalyticsService.getPlatformMedia).toHaveBeenCalledWith(
        mockUserDetails,
        mockPlatformMediaRequestDto,
      );

      expect(result.platformMedias.pm1.isCreativeInMultiAssetAd).toBe(true);
      expect(result.platformMedias.pm2.isCreativeInMultiAssetAd).toBe(false);
    });

    it('handles empty flags array by defaulting all to false', async () => {
      dimensionServiceSdk.getMultiAssetFlagForCreativesAsPromise.mockResolvedValue(
        {
          result: [],
        } as any,
      );
      legacyAnalyticsService.getPlatformMedia.mockResolvedValue({
        platformMedias: {
          pmA: { platformMediaId: 'pmA' },
          pmB: { platformMediaId: 'pmB' },
        },
      });

      const merged = await service.getPlatformMedia(
        mockUserDetails,
        mockPlatformMediaRequestDto,
      );
      Object.values(merged.platformMedias).forEach((m) => {
        expect(m.isCreativeInMultiAssetAd).toBe(false);
      });
    });
  });
});
