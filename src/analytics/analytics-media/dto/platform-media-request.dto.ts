import {
  Is<PERSON><PERSON>y,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { GetMultiAssetFlagRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/getMultiAssetFlagRequestDto';

export class PlatformMediaRequestDto {
  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  @IsString({ each: true })
  platformMediaIds: string[];

  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  @IsNumber()
  workspaceIds: number[];

  @AutoMap()
  @IsArray()
  @IsNotEmpty()
  @IsString({ each: true })
  adAccountIds: string[];

  @AutoMap()
  @IsEnum(GetMultiAssetFlagRequestDto.PlatformEnum)
  @IsNotEmpty()
  platform: GetMultiAssetFlagRequestDto.PlatformEnum;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  startDate: string;

  @AutoMap()
  @IsDateString()
  @IsNotEmpty()
  endDate: string;
}
