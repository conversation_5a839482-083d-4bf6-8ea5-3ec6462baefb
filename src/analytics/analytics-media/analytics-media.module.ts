import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AnalyticsMediaController } from './analytics-media.controller';
import { AnalyticsMediaService } from './analytics-media.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from '../../auth/services/auth.service';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';
@Module({
  providers: [AuthService, AnalyticsMediaService, LegacyAnalyticsService],
  controllers: [AnalyticsMediaController],
  imports: [HttpModule, TypeOrmModule.forFeature([])],
})
export class AnalyticsMediaModule {}
