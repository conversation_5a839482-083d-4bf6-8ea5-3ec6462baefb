import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException, Logger } from '@nestjs/common';
import { InsightTitleGeneratorService } from './insight-title-generator.service';
import { LlmClientRouter } from '../../brands-copilot/llm/llm-client-router.service';
import { LLMModel } from '../../brands-copilot/model/llm-model.enum';

describe('InsightTitleGeneratorService', () => {
  let service: InsightTitleGeneratorService;
  let llmProviderService: LlmClientRouter;

  // mock for the provider returned by getLlmClient
  const mockGetMessagesJsonResponse = jest.fn();
  const mockProvider = {
    getJsonResponse: mockGetMessagesJsonResponse,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InsightTitleGeneratorService,
        {
          provide: LlmClientRouter,
          useValue: {
            getLlmClient: jest.fn().mockReturnValue(mockProvider),
          },
        },
      ],
    }).compile();

    service = module.get<InsightTitleGeneratorService>(
      InsightTitleGeneratorService,
    );
    llmProviderService = module.get<LlmClientRouter>(LlmClientRouter);

    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should throw BadRequestException if both finding and recommendation are missing', async () => {
    const dto = { finding: '', recommendation: '' } as any;
    const warnSpy = jest.spyOn(Logger.prototype, 'warn');
    await expect(
      service.generateInsightTitle(dto, LLMModel.OPEN_AI_4_O),
    ).rejects.toThrow(BadRequestException);
    expect(warnSpy).toHaveBeenCalledWith(
      'Both finding and recommendation are missing.',
    );
  });

  it('should call LlmProviderService with correct prompts', async () => {
    const dto = {
      finding: 'Finding text',
      recommendation: 'Recommendation text',
    };
    const llmModel = LLMModel.OPEN_AI_4_1;

    mockGetMessagesJsonResponse.mockResolvedValue({
      isSuccessful: () => true,
      value: { title: 'A valid title' },
    });

    await service.generateInsightTitle(dto, llmModel);

    // ensure we picked the right provider
    expect(llmProviderService.getLlmClient).toHaveBeenCalledWith(llmModel);
    expect(mockGetMessagesJsonResponse).toHaveBeenCalledTimes(1);

    const [, messages, modelArg] = mockGetMessagesJsonResponse.mock.calls[0];
    expect(modelArg).toBe(llmModel);

    const systemMsg = messages.find((m: any) => m.role === 'system');
    expect(systemMsg.content).toContain(
      'You are an AI assistant specializing in generating concise insight titles.',
    );

    const userMsg = messages.find((m: any) => m.role === 'user');
    expect(userMsg.content).toContain('Finding: Finding text');
    expect(userMsg.content).toContain('Recommendation: Recommendation text');
  });

  it('should return title when generation is successful', async () => {
    const dto = { finding: 'Foo', recommendation: 'Bar' };
    const llmModel = LLMModel.GEMINI_1_5_PRO;

    mockGetMessagesJsonResponse.mockResolvedValue({
      isSuccessful: () => true,
      value: { title: 'Short Title' },
    });

    const result = await service.generateInsightTitle(dto, llmModel);
    expect(result.title).toBe('Short Title');
  });

  it('should throw error when LLM response unsuccessful', async () => {
    const dto = { finding: 'Foo', recommendation: 'Bar' };

    mockGetMessagesJsonResponse.mockResolvedValue({
      isSuccessful: () => false,
      error: 'Some error',
    });

    await expect(
      service.generateInsightTitle(dto, LLMModel.OPEN_AI_4_O),
    ).rejects.toThrow('Error generating insight title from OpenAI.');
  });

  it('should use fallback title when LLM returns refusal or empty', async () => {
    const dto = { finding: 'Foo', recommendation: 'Bar' };

    mockGetMessagesJsonResponse.mockResolvedValue({
      isSuccessful: () => true,
      value: { title: 'insufficient data provided' },
    });

    const result = await service.generateInsightTitle(
      dto,
      LLMModel.OPEN_AI_4_O,
    );
    expect(result.title).toBe('Strategic Key Observation');
  });

  it('should throw error if generated title exceeds 50 characters', async () => {
    const dto = { finding: 'Foo', recommendation: 'Bar' };
    const longTitle = 'A'.repeat(51); // 51 chars

    mockGetMessagesJsonResponse.mockResolvedValue({
      isSuccessful: () => true,
      value: { title: longTitle },
    });

    await expect(
      service.generateInsightTitle(dto, LLMModel.CLAUDE_3_5_SONNET),
    ).rejects.toThrow(`Generated title exceeds 50 characters: "${longTitle}"`);
  });
});
