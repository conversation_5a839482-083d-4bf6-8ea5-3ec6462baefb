import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { GenerateInsightTitleRequestDto } from '../dto/generate-insight-title-request.dto';
import {
  CopilotRole,
  LLMMessage,
} from '../../brands-copilot/model/llm-constants.model';
import { z } from 'zod';
import { GenerateInsightTitleResponseDto } from '../dto/generate-insight-title-response.dto';
import { LlmClientRouter } from '../../brands-copilot/llm/llm-client-router.service';
import { LLMModel } from '../../brands-copilot/model/llm-model.enum';

@Injectable()
export class InsightTitleGeneratorService {
  private readonly logger = new Logger(InsightTitleGeneratorService.name);

  constructor(private readonly llmProviderService: LlmClientRouter) {}

  private static readonly SYSTEM_PROMPT = `
    You are an AI assistant specializing in generating concise insight titles.
    Given a recommendation and findings text, craft a compelling, short title that effectively
    summarizes both. Return only valid JSON with a single field "title".
    Title must not exceed 50 characters including spaces. If you cannot comply, respond with a refusal.
    Double-check your final output length carefully before you respond to ensure it is <= 50 characters.
  `.trim();

  private static readonly FALLBACK_TITLE = `Strategic Key Observation`;

  async generateInsightTitle(
    { finding, recommendation }: GenerateInsightTitleRequestDto,
    llmModel: LLMModel,
  ): Promise<GenerateInsightTitleResponseDto> {
    if (!finding && !recommendation) {
      this.logger.warn('Both finding and recommendation are missing.');
      throw new BadRequestException(
        'Must provide either a finding or a recommendation to generate a title.',
      );
    }

    const userPrompt = `Finding: ${finding}\nRecommendation: ${recommendation}`;

    const messages: LLMMessage[] = [
      {
        role: CopilotRole.SYSTEM,
        content: InsightTitleGeneratorService.SYSTEM_PROMPT,
      },
      { role: CopilotRole.USER, content: userPrompt.trim() },
    ];

    const TitleSchema = z.object({ title: z.string() });

    const result = await this.llmProviderService
      .getLlmClient(llmModel)
      .getJsonResponse(TitleSchema, messages, llmModel);

    if (!result.isSuccessful()) {
      this.logger.error(`Failed to generate insight title: ${result.error}`);
      throw new Error('Error generating insight title from OpenAI.');
    }

    let { title } = result.value;

    if (!title || /insufficient data|refusal|cannot comply/i.test(title)) {
      this.logger.warn(
        'OpenAI responded with insufficient information/refusal. Using fallback.',
      );
      title = InsightTitleGeneratorService.FALLBACK_TITLE;
    }

    if (title.length > 50) {
      this.logger.error(`Generated title exceeds 50 characters: "${title}"`);
      throw new Error(`Generated title exceeds 50 characters: "${title}"`);
    }

    return { title };
  }
}
