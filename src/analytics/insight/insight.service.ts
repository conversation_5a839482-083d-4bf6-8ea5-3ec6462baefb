import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { API_ENDPOINTS } from './insight.constants';
import {
  createInsightQuery,
  linkProjectInsightMutation,
} from './insight.queries';
import { Client } from '@elastic/elasticsearch';
import {
  ELASTIC_SEARCH_API_KEY,
  ELASTIC_SEARCH_NODE,
  INSIGHTS_DB_INDEX,
} from '../analytics-copilot/constants/analytics-copilot.constants';
import {
  CreateInsightRequestDto,
  InsightsService as InsightServiceSdk,
  ListInsightsFilterDto as ListInsightsFilterDtoSdk,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { INSIGHT_TYPE } from 'src/constants/insight.constants';
import { CopilotElasticSearchRepository } from '../brands-copilot/repository/copilot-elastic-search-repository.service';
import { UUID } from 'crypto';
import { UpdateInsightRequestDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/updateInsightRequestDto';
import { ScopeFilterService } from '@vidmob/vidmob-organization-service-sdk';
import { CopilotInsightRequestDto } from './dto/copilot-insight-request.dto';
import { CreateCopilotInsightRequestDto } from './dto/create-copilot-insight-request.dto';

@Injectable()
export class InsightService {
  private readonly serviceUrl: string;
  private readonly token: string;
  private esClient: Client;
  private readonly logger = new Logger(InsightService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly insightServiceSdk: InsightServiceSdk,
    private readonly copilotRepository: CopilotElasticSearchRepository,
    private readonly scopeFilterService: ScopeFilterService,
  ) {
    this.serviceUrl = this.configService.get<string>('serviceUrl') || 'local';
    this.esClient = new Client({
      node: ELASTIC_SEARCH_NODE,
      auth: {
        apiKey: ELASTIC_SEARCH_API_KEY,
      },
    });
  }

  async createInsight(
    userId: number,
    createInsightRequestDto: CreateInsightRequestDto,
  ) {
    await this.hydrateCreateInsightWithBrandsAndMarkets(
      createInsightRequestDto,
    );

    return await this.insightServiceSdk.createInsightAsPromise({
      ...createInsightRequestDto,
      userId,
    });
  }

  async getAllInsights(
    listInsightsFilterDto: ListInsightsFilterDtoSdk,
    paginationOptions: PaginationOptions,
  ) {
    return await this.insightServiceSdk.listInsightsAsPromise(
      listInsightsFilterDto,
      paginationOptions.offset,
      paginationOptions.perPage,
    );
  }

  async getInsightById(
    insightId: string,
    organizationId: string,
    userId: number,
    workspaceIds: number[],
    activeWorkspaceId?: number,
  ) {
    return await this.insightServiceSdk.getInsightAsPromise(
      insightId,
      workspaceIds,
      organizationId,
      userId,
      activeWorkspaceId,
    );
  }

  async updateInsight(
    insightId: string,
    updateInsightRequestDto: UpdateInsightRequestDto,
  ) {
    return await this.insightServiceSdk.updateInsightAsPromise(
      insightId,
      updateInsightRequestDto,
    );
  }

  async favoriteInsight(insightId: string, favoriteInsightRequestDto: any) {
    return await this.insightServiceSdk.favoriteInsightAsPromise(
      insightId,
      favoriteInsightRequestDto,
    );
  }

  async deleteInsight(
    insightId: string,
    workspaceIds: number[],
    organizationId: string,
    userId: number,
    isUserOrganizationAdmin: boolean,
  ) {
    return await this.insightServiceSdk.deleteInsightAsPromise(
      insightId,
      workspaceIds,
      organizationId,
      userId,
      isUserOrganizationAdmin,
    );
  }

  async hydrateCreateInsightWithBrandsAndMarkets(
    createInsightRequestDto: CreateInsightRequestDto | CopilotInsightRequestDto,
  ) {
    const { adAccountIds, brandIds, marketIds } = createInsightRequestDto;
    if ((!brandIds?.length || !marketIds?.length) && adAccountIds?.length) {
      const { result } =
        await this.scopeFilterService.getBrandsAndMarketsByAdAccountsAsPromise({
          adAccountIds,
        });

      if (result?.brandIds?.length && !brandIds?.length) {
        createInsightRequestDto.brandIds = result.brandIds;
      }

      if (result?.marketIds?.length && !marketIds?.length) {
        createInsightRequestDto.marketIds = result.marketIds;
      }
    }
  }

  private async handlePost(
    endPoint: string,
    token: string,
    request: any,
  ): Promise<any> {
    const url = `${this.serviceUrl}${endPoint}`;
    const headers = {
      Authorization: token,
    };

    try {
      const response = await axios.post(url, request, { headers });
      const dataFromRequest = response.data;

      if (dataFromRequest?.data) {
        return dataFromRequest.data;
      }

      return {};
    } catch (error) {
      throw new InternalServerErrorException('Failed to make POST request');
    }
  }

  async createCopilotInsights(
    insights: any[],
    organizationId: string,
    workspaceIds: string[],
    token: string,
    projectId?: number,
    chatId?: UUID,
    messageId?: UUID,
  ) {
    if (!insights || insights.length === 0) {
      return;
    }
    const isNormativeInsight = insights[0].type === INSIGHT_TYPE.INDUSTRY;
    const isBrandInsight = insights[0].type === INSIGHT_TYPE.BRAND;

    const results = await this.createInsights(
      insights,
      organizationId,
      workspaceIds,
      token,
      projectId,
    );

    results.forEach(async (result) => {
      if (isNormativeInsight) {
        await this.updateNormativeInsight(result.id, result.insightLibraryId);
      } else if (isBrandInsight && chatId && messageId) {
        await this.updateBrandInsight(
          chatId,
          messageId,
          result.id,
          result.insightLibraryId,
        );
      }
    });

    return results;
  }

  async updateBrandInsight(
    chatId: UUID,
    messageId: UUID,
    insightId: UUID,
    insightLibraryId: UUID,
  ) {
    try {
      await this.copilotRepository.updateInsightWithLibraryId(
        chatId,
        messageId,
        insightId,
        insightLibraryId,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        // the main action of this function is to create the insight, so if the conversation, message or insight is not found, we log an error but do not propagate it
        this.logger.error(
          `Conversation ${chatId}, message ${messageId} or insight ${insightId} not found`,
        );
        return;
      }

      // For other errors, throw an internal server error
      throw new InternalServerErrorException(
        `Failed to update insight ${insightId} with library id ${insightLibraryId}`,
      );
    }
  }

  async updateNormativeInsight(insightId: string, insightLibraryId: string) {
    try {
      await this.esClient.update({
        index: INSIGHTS_DB_INDEX,
        id: insightId,
        body: {
          doc: {
            insightLibraryId,
          },
        },
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to update insight ${insightId}`,
      );
    }
  }

  async createInsights(
    insights: any[],
    organizationId: string,
    workspaceIds: string[],
    token: string,
    projectId?: number,
  ): Promise<any[]> {
    const results = [];

    for (const insight of insights) {
      if (insight.insightLibraryId) {
        results.push({
          id: insight.id,
          insightLibraryId: insight.insightLibraryId,
        });
      } else {
        const request = this.getCreateInsightRequest(
          insight,
          organizationId,
          workspaceIds,
        );
        const response = await this.handlePost(
          API_ENDPOINTS.INSIGHTS_SERVICE,
          token,
          request,
        );
        const { createInsight } = response;

        if (createInsight?.result) {
          const insightLibraryId = createInsight.result.insightId;
          results.push({
            id: insight.id,
            insightLibraryId,
          });

          // Link the insight to the project if a projectId is provided
          if (projectId) {
            await this.linkInsightToProject(
              insightLibraryId,
              organizationId,
              workspaceIds,
              projectId,
              token,
            );
          }
        } else {
          throw new Error(
            'Unexpected response from createInsight API. Response: ' +
              JSON.stringify(response),
          );
        }
      }
    }

    return results;
  }

  private getCreateInsightRequest(
    insightRequest: any,
    organizationId: string,
    workspaceIds: string[],
  ): any {
    const { id, ...insightRequestWithoutId } = insightRequest;
    return {
      query: createInsightQuery,
      variables: {
        insightRequest: insightRequestWithoutId,
        organizationId,
        workspaceIds,
      },
    };
  }

  private async linkInsightToProject(
    insightLibraryId: string,
    organizationId: string,
    workspaceIds: string[],
    projectId: number,
    token: string,
  ): Promise<void> {
    const request = {
      query: linkProjectInsightMutation,
      variables: {
        organizationId,
        workspaceIds,
        projectId,
        insightIds: [insightLibraryId],
        recommendedFrom: null,
      },
    };

    try {
      await this.handlePost(API_ENDPOINTS.PROJECT_INSIGHTS, token, request);
    } catch (error) {
      this.logger.error(
        `Error linking insight ${insightLibraryId} to project ${projectId}:`,
        error,
      );
      throw new InternalServerErrorException(
        `Failed to link insight to project ${projectId}`,
      );
    }
  }
}
