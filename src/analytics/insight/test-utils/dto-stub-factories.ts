import {
  CreateInsightRequestDto,
  InsightPermissionDetailsResponseDto,
  ReadListInsightDto,
  ReadUserDto,
  UpdateInsightFolderRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  generateList,
  generateRandomBoolean,
  generateRandomId,
  generateRandomLoremWords,
  generateRandomString,
  getRandomSopranosCharacter,
  Platform,
  randomEnumValue,
} from '@vidmob/vidmob-nestjs-common';
import {
  INSIGHT_NON_DELETED_STATUS,
  INSIGHT_SOURCE,
  INSIGHT_TYPE,
} from '../../../constants/insight.constants';
import { CreateProjectInsightRequestDto } from '../dto/create-project-insight-request.dto';
import { CreateInsightFolderRequestDto } from '../dto/create-insight-folder-request.dto';
import { InsightStatus } from '../insight-filters/constants';
import TypeEnum = ReadListInsightDto.TypeEnum;
import { UserDetailsDto } from '../../dto/user-details.dto';
import { UserDetailsWithWorkspaceAccess } from '../permission/insight-permission.service';

export const bigIntId = (): number => {
  return generateRandomId(6);
};

export const stringUUID = (): string => {
  return generateRandomString(36);
};

export const createStubCreateInsightRequestDto = (
  overrides: Partial<CreateInsightRequestDto>,
): CreateInsightRequestDto => {
  return {
    organizationId: stringUUID(),
    platform: randomEnumValue(Platform) as Platform,
    source: randomEnumValue(INSIGHT_SOURCE) as INSIGHT_SOURCE,
    title: generateRandomLoremWords(4),
    userId: bigIntId(),
    type: randomEnumValue(INSIGHT_TYPE) as INSIGHT_TYPE,
    status: randomEnumValue(INSIGHT_NON_DELETED_STATUS) as object,
    ...overrides,
  };
};

export const createStubInsightPermissionDetailsResponseDto = (
  overrides: Partial<InsightPermissionDetailsResponseDto>,
): InsightPermissionDetailsResponseDto => {
  return {
    id: stringUUID(),
    organizationId: stringUUID(),
    workspaces: generateList(3, () => bigIntId()),
    type: randomEnumValue(INSIGHT_TYPE) as INSIGHT_TYPE,
    status: randomEnumValue(INSIGHT_NON_DELETED_STATUS) as InsightStatus,
    ownerId: bigIntId(),
    ...overrides,
  };
};

export const createStubProjectInsightRequestDto = (
  overrides: Partial<CreateProjectInsightRequestDto>,
): CreateProjectInsightRequestDto => {
  return {
    insightId: stringUUID(),
    projectIds: generateList(2, () => bigIntId()),
    ...overrides,
  };
};

export const createStubInsightFolderRequestDto = (
  overrides: Partial<CreateInsightFolderRequestDto>,
): CreateInsightFolderRequestDto => {
  return {
    name: generateRandomLoremWords(3),
    description: generateRandomLoremWords(10),
    parentFolderId: stringUUID(),
    ...overrides,
  };
};

export const createStubUpdateInsightFolderRequestDto = (
  overrides: Partial<UpdateInsightFolderRequestDto>,
): UpdateInsightFolderRequestDto => {
  return {
    name: generateRandomLoremWords(3),
    description: generateRandomLoremWords(10),
    ...overrides,
  };
};

export const createStubReadUserDto = (
  overrides: Partial<ReadUserDto>,
): ReadUserDto => {
  const user = getRandomSopranosCharacter();
  return {
    id: bigIntId(),
    displayName: `${user.firstName} ${user.lastName}`,
    ...overrides,
  };
};

export const createStubReadListInsightDto = (
  overrides: Partial<ReadListInsightDto> = {},
): ReadListInsightDto => {
  const user = getRandomSopranosCharacter();
  return {
    id: stringUUID(),
    brands: generateList(2, () => generateRandomLoremWords(1)), // Default length of 3 for brands array
    createdBy: createStubReadUserDto({}),
    dateCreated: new Date().toString(),
    favorited: generateRandomBoolean(),
    finding: generateRandomLoremWords(10),
    industryName: generateRandomLoremWords(1),
    markets: generateList(2, () => generateRandomLoremWords(1)), // Default length of 2 for markets array
    platform: randomEnumValue(Platform) as Platform,
    status: randomEnumValue(INSIGHT_NON_DELETED_STATUS) as InsightStatus,
    title: generateRandomLoremWords(5),
    type: randomEnumValue(INSIGHT_TYPE) as TypeEnum,
    ...overrides, // Apply any overrides passed in the argument
  };
};

export const createStubUserDetailsDto = (
  overrides: Partial<UserDetailsDto> = {},
): UserDetailsDto => {
  return {
    organizationId: stringUUID(),
    userId: bigIntId(),
    authorization: `Bearer fake`,
    ...overrides,
  };
};

export const createStubUserDetailsWithWorkspaceAccess = (
  overrides: Partial<UserDetailsWithWorkspaceAccess> = {},
): UserDetailsWithWorkspaceAccess => {
  const userDetails = createStubUserDetailsDto(overrides);
  return {
    ...userDetails,
    workspaceIdsUserHasAccess: generateList(2, () => bigIntId()),
    adAccountIdsUserHasAccess: generateList(4, () => stringUUID()),
    ...overrides,
  };
};
