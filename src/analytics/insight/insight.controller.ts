import {
  Body,
  Controller,
  DefaultValuePipe,
  Delete,
  ForbiddenException,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  ValidationPipe,
  Version,
} from '@nestjs/common';
import { InsightService } from './insight.service';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { UUID } from 'crypto';
import { INSIGHT_TYPE } from '../../constants/insight.constants';
import { UserDetailsDto } from '../dto/user-details.dto';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import {
  CreateInsightRequestDto,
  ListInsightsFilterDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { UpdateInsightRequestDto } from './dto/update-insight-request.dto';
import { FavoriteInsightRequestDto } from './dto/favorite-insight-request.dto';
import { OrganizationUserRoles } from '../../constants/role.constants';
import { InsightPermissionService } from './permission/insight-permission.service';
import { InsightTitleGeneratorService } from './insight-title-generator/insight-title-generator.service';
import { GenerateInsightTitleRequestDto } from './dto/generate-insight-title-request.dto';
import { InsightFolderPermissionService } from './insight-folders/insight-folder-permission.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  readInsightFolderOrg,
  readInsightFolderWorkspace,
} from './insight-folders/insight-folder.permissions';
import { LLMModel } from '../brands-copilot/model/llm-model.enum';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Insight')
@ApiSecurity('Bearer Token')
@Controller('organization/:organizationId/insight')
export class InsightController {
  constructor(
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly insightsService: InsightService,
    private readonly organizationUserService: OrganizationUserService,
    private readonly insightPermissionService: InsightPermissionService,
    private readonly insightTitleGeneratorService: InsightTitleGeneratorService,
    private readonly insightFolderPermissionService: InsightFolderPermissionService,
  ) {}

  // TODO This is possibly unused, remove with VID-10948
  @Post('create')
  async createCopilotInsights(
    @Request() req: any,
    @Body()
    body: {
      insightRequest: any[];
      organizationId: string;
      workspaceIds: string[];
      projectId?: number;
      chatId?: UUID;
      messageId?: UUID;
    },
  ) {
    const authorization = req.headers.authorization;

    return this.insightsService.createCopilotInsights(
      body.insightRequest,
      body.organizationId,
      body.workspaceIds,
      authorization,
      body.projectId,
      body.chatId,
      body.messageId,
    );
  }

  /**
   * Create insight endpoint.
   * This V2 endpoint will create the insight directly on MySQL database.
   */
  @Post()
  async createInsight(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe) createInsightRequestDto: CreateInsightRequestDto,
  ) {
    const authorization = req.headers.authorization;
    const { userId } = req;
    const userDetails = { userId, authorization, organizationId };

    try {
      if (createInsightRequestDto.type === INSIGHT_TYPE.BRAND) {
        return await this.validateUserAccessAndCreateBrandInsight(
          userDetails,
          createInsightRequestDto,
          organizationId,
        );
      } else if (createInsightRequestDto.type === INSIGHT_TYPE.INDUSTRY) {
        return await this.validateUserAccessAndCreateIndustryInsight(
          userDetails,
          organizationId,
          createInsightRequestDto,
        );
      }
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw new Error('Error creating insight with response ' + error);
      }
      throw error;
    }
  }

  /**
   * List insights endpoint.
   * This endpoint will list insights from MySQL database.
   */
  @Post('/all')
  async getListOfInsights(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe) listInsightsFilterDto: ListInsightsFilterDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const authorization = req.headers.authorization;
    const { userId } = req;

    await this.validateAccessToOrganizationInsights(organizationId, userId);

    const workspaceIdsUserHasAccess = await this.getUserWorkspaceIds({
      userId,
      authorization,
      organizationId,
    });

    if (listInsightsFilterDto?.workspaceIds) {
      await this.validateAccessToWorkspaceInsights(
        listInsightsFilterDto.workspaceIds,
        workspaceIdsUserHasAccess,
      );
    }

    return this.insightsService.getAllInsights(
      {
        ...listInsightsFilterDto,
        organizationId,
        workspaceIdsUserHasAccess,
        userId,
      },
      paginationOptions,
    );
  }

  /**
   * List insights endpoint.
   * This endpoint will list insights from MySQL database.
   */
  @Version('2')
  @Post('/workspace/:workspaceId/all/with-folder-permission')
  @Permissions([readInsightFolderWorkspace, readInsightFolderOrg])
  async getListOfInsightsWithFolderPermission(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(ValidationPipe) listInsightsFilterDto: ListInsightsFilterDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId: organizationId,
    };

    if (listInsightsFilterDto.parentFolderId) {
      await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
        workspaceId,
        listInsightsFilterDto.parentFolderId,
      );
    }

    await this.validateAccessToOrganizationInsights(
      userDetails.organizationId,
      userDetails.userId,
    );

    const workspaceIdsUserHasAccess = await this.getUserWorkspaceIds(
      userDetails,
    );

    if (listInsightsFilterDto?.workspaceIds) {
      await this.validateAccessToWorkspaceInsights(
        listInsightsFilterDto.workspaceIds,
        workspaceIdsUserHasAccess,
      );
    }

    return this.insightsService.getAllInsights(
      {
        ...listInsightsFilterDto,
        activeWorkspaceId: workspaceId,
        organizationId: userDetails.organizationId,
        workspaceIdsUserHasAccess,
        userId: userDetails.userId,
      },
      paginationOptions,
    );
  }

  /**
   * Obtains a single insight. This endpoint will get a single insight from MySQL database.
   */
  @Get('/:insightId/detail')
  async getIndividualInsight(
    @Request() req: any,
    @Param('insightId') insightId: string,
    @Param('organizationId') organizationId: string,
    @Query('activeWorkspaceId') activeWorkspaceId?: string,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    const workspaceId = activeWorkspaceId
      ? parseInt(activeWorkspaceId, 10)
      : undefined;

    await this.validateAccessToOrganizationInsights(organizationId, userId);

    const workspaceIdsUserHasAccess = await this.getUserWorkspaceIds({
      userId,
      authorization,
      organizationId,
    });

    return this.insightsService.getInsightById(
      insightId,
      organizationId,
      userId,
      workspaceIdsUserHasAccess,
      workspaceId,
    );
  }

  @Patch('/:insightId')
  async updateInsight(
    @Request() req: any,
    @Param('insightId') insightId: string,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe) updateInsightRequestDto: UpdateInsightRequestDto,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    const workspaceIdsUserHasAccess = await this.getUserWorkspaceIds({
      userId,
      authorization,
      organizationId,
    });

    const isUserOrganizationAdmin = await this.getIsUserOrganizationAdmin(
      organizationId,
      userId,
    );

    return await this.insightsService.updateInsight(insightId, {
      ...updateInsightRequestDto,
      organizationId,
      userId,
      workspaceIds: workspaceIdsUserHasAccess,
      isUserOrganizationAdmin,
    });
  }

  @Post('/:insightId/favorite')
  async favoriteInsight(
    @Request() req: any,
    @Param('insightId') insightId: string,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe) favoriteInsightRequestDto: FavoriteInsightRequestDto,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    const workspaceIdsUserHasAccess = await this.getUserWorkspaceIds({
      userId,
      authorization,
      organizationId,
    });

    return await this.insightsService.favoriteInsight(insightId, {
      ...favoriteInsightRequestDto,
      organizationId,
      userId,
      workspaceIds: workspaceIdsUserHasAccess,
    });
  }

  @Delete('/:insightId')
  async deleteInsight(
    @Request() req: any,
    @Param('insightId') insightId: string,
    @Param('organizationId') organizationId: string,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    const workspaceIdsUserHasAccess = await this.getUserWorkspaceIds({
      userId,
      authorization,
      organizationId,
    });

    const isUserOrganizationAdmin = await this.getIsUserOrganizationAdmin(
      organizationId,
      userId,
    );

    return await this.insightsService.deleteInsight(
      insightId,
      workspaceIdsUserHasAccess,
      organizationId,
      userId,
      isUserOrganizationAdmin,
    );
  }

  @Post('generate-title')
  async generateInsightTitle(
    @Request() req: any,
    @Body() generateInsightTitleRequestDto: GenerateInsightTitleRequestDto,
    @Param('organizationId') organizationId: string,
  ) {
    const { userId } = req;
    await this.validateAccessToOrganizationInsights(organizationId, userId);

    return await this.insightTitleGeneratorService.generateInsightTitle(
      generateInsightTitleRequestDto,
      LLMModel.OPEN_AI_4_1,
    );
  }

  private async validateUserAccessAndCreateBrandInsight(
    userDetailsDto: UserDetailsDto,
    bodyDto: CreateInsightRequestDto,
    organizationId: string,
  ) {
    const hydratedBody: CreateInsightRequestDto =
      await this.insightPermissionService.validateAndHydrateBrandInsightCreation(
        'Create Brand Insight',
        bodyDto,
        userDetailsDto,
        organizationId,
      );

    return await this.insightsService.createInsight(
      userDetailsDto.userId,
      hydratedBody,
    );
  }

  private async validateUserAccessAndCreateIndustryInsight(
    userDetailsDto: UserDetailsDto,
    organizationId: string,
    bodyDto: CreateInsightRequestDto,
  ) {
    const hydratedBody =
      await this.insightPermissionService.validateAndHydrateIndustryInsight(
        'Create Industry Insight',
        bodyDto,
        userDetailsDto,
        organizationId,
      );

    return await this.insightsService.createInsight(
      userDetailsDto.userId,
      hydratedBody,
    );
  }

  private async validateAccessToWorkspaceInsights(
    workspaceIds: number[],
    workspaceIdsUserHasAccess: number[],
  ) {
    const workspaceIdsUserHasAccessSet = new Set(workspaceIdsUserHasAccess);
    for (const workspaceId of workspaceIds) {
      if (!workspaceIdsUserHasAccessSet.has(workspaceId)) {
        throw new ForbiddenException(
          'User does not have access to workspace: ' + workspaceId,
        );
      }
    }
  }

  private async validateAccessToOrganizationInsights(
    organizationId: string,
    userId: number,
  ): Promise<boolean> {
    try {
      await this.organizationUserService.getUserInOrganization(
        organizationId,
        userId,
      );
    } catch (error) {
      throw new ForbiddenException(
        'Error accessing insight with response: ' + error,
      );
    }

    return true;
  }

  private async getUserWorkspaceIds(userDetails: UserDetailsDto) {
    const workspaces =
      await this.analyticsUserService.fetchAllOrganizationWorkspacesForUser(
        userDetails,
      );

    return workspaces.map((workspace) => workspace.id);
  }

  private async getIsUserOrganizationAdmin(
    organizationId: string,
    userId: number,
  ) {
    const response = await this.organizationUserService.getUserInOrganization(
      organizationId,
      userId,
    );

    return response?.result?.roles.some(
      (role) => role.identifier === OrganizationUserRoles.ORG_ADMIN,
    );
  }
}
