import { Test, TestingModule } from '@nestjs/testing';
import { ProjectInsightsService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ProjectInsightController } from './project-insight.controller';
import { InsightPermissionService } from '../permission/insight-permission.service';
import {
  bigIntId,
  createStubProjectInsightRequestDto,
  stringUUID,
} from '../test-utils/dto-stub-factories';

const defaultRequest = {
  headers: { authorization: `Bearer blopp blorp` },
  userId: bigIntId(),
};
const buildProjectInsightController = async ({
  projectInsightServiceCreatefn = jest.fn(),
  projectInsightServiceInsightByProjectfn = jest.fn(),
  projectInsightServiceDeletefn = jest.fn(),
  insightPermissionService = jest.fn(),
}: {
  projectInsightServiceCreatefn?: any;
  projectInsightServiceInsightByProjectfn?: any;
  projectInsightServiceDeletefn?: any;
  insightPermissionService?: any;
}) => {
  const module: TestingModule = await Test.createTestingModule({
    providers: [
      ProjectInsightController,
      {
        provide: InsightPermissionService,
        useValue: {
          accessToInsight: insightPermissionService,
        },
      },
      {
        provide: ProjectInsightsService,
        useValue: {
          createProjectInsightAsPromise: projectInsightServiceCreatefn,
          getProjectInsightsAsPromise: projectInsightServiceInsightByProjectfn,
          deleteProjectInsightAsPromise: projectInsightServiceDeletefn,
        },
      },
    ],
  }).compile();
  return module.get<ProjectInsightController>(ProjectInsightController);
};

describe('ProjectInsightController', () => {
  describe('createProjectInsight', () => {
    it('does not create a project insight when user does not have access to the insight', async () => {
      const mockInsightPermissionService = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));
      const mockProjectInsightService = jest.fn();
      const projectInsightController = await buildProjectInsightController({
        insightPermissionService: mockInsightPermissionService,
        projectInsightServiceCreatefn: mockProjectInsightService,
      });
      await expect(
        projectInsightController.createProjectInsight(
          defaultRequest,
          stringUUID(),
          createStubProjectInsightRequestDto({}),
        ),
      ).rejects.toThrowError('no permishies');
      expect(mockProjectInsightService).not.toHaveBeenCalled();
    });
    it('attempts to create a project insight when user has all the required permissions', async () => {
      const createProjectInsightRequestDto = createStubProjectInsightRequestDto(
        {},
      );
      const mockProjectInsightService = jest.fn();
      const projectInsightController = await buildProjectInsightController({
        projectInsightServiceCreatefn: mockProjectInsightService,
      });

      await projectInsightController.createProjectInsight(
        defaultRequest,
        stringUUID(),
        createProjectInsightRequestDto,
      );
      expect(mockProjectInsightService).toHaveBeenCalledWith({
        insightId: createProjectInsightRequestDto.insightId,
        projectIds: createProjectInsightRequestDto.projectIds,
      });
    });
  });
  describe('getProjectInsightsByProject', () => {
    it('attempts to fetch project insights for project if user has permissions', async () => {
      const projectId = bigIntId();
      const mockProjectInsightService = jest.fn();
      const projectInsightController = await buildProjectInsightController({
        projectInsightServiceInsightByProjectfn: mockProjectInsightService,
      });

      await projectInsightController.getProjectInsightsByProject(
        defaultRequest,
        stringUUID(),
        projectId,
        { offset: 1, perPage: 2 },
      );
      expect(mockProjectInsightService).toHaveBeenCalledWith(projectId, 1, 2);
    });
  });

  describe('deleteProjectInsight', () => {
    it('does not create a project insight when user does not have access to the insight', async () => {
      const mockInsightPermissionService = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));
      const mockProjectInsightService = jest.fn();
      const projectInsightController = await buildProjectInsightController({
        insightPermissionService: mockInsightPermissionService,
        projectInsightServiceDeletefn: mockProjectInsightService,
      });
      await expect(
        projectInsightController.deleteProjectInsight(
          defaultRequest,
          stringUUID(),
          stringUUID(),
          bigIntId(),
        ),
      ).rejects.toThrowError('no permishies');
      expect(mockProjectInsightService).not.toHaveBeenCalled();
    });
    it('attempts to create a project insight when user has all the required permissions', async () => {
      const insightId = stringUUID();
      const projectId = bigIntId();
      const mockProjectInsightService = jest.fn();
      const projectInsightController = await buildProjectInsightController({
        projectInsightServiceDeletefn: mockProjectInsightService,
      });

      await projectInsightController.deleteProjectInsight(
        defaultRequest,
        stringUUID(),
        insightId,
        projectId,
      );
      expect(mockProjectInsightService).toHaveBeenCalledWith(
        insightId,
        projectId,
      );
    });
  });
});
