import { PermissionDomain } from '../../../auth/enums/permission.domain.enum';
import {
  projectFromParamsHandler,
  projectsFromBodyHandler,
} from '../../../auth/decorators/permission.decorator';
import { PermissionAction } from '../../../auth/enums/permission.action.enum';
import { PermissionSubResource } from '../../../auth/enums/permission.subresource.enum';

// create
export const createProjectInsightProjectLevel = {
  domain: PermissionDomain.PROJECT,
  domainContextHandler: projectsFromBodyHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.PROJECT_INSIGHT,
    },
  ],
};

// Read
export const readProjectInsightProjectLevel = {
  domain: PermissionDomain.PROJECT,
  domainContextHandler: projectFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.PROJECT_INSIGHT,
    },
  ],
};

// delete
export const deleteProjectInsightProjectLevel = {
  domain: PermissionDomain.PROJECT,
  domainContextHandler: projectFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.PROJECT_INSIGHT,
    },
  ],
};
