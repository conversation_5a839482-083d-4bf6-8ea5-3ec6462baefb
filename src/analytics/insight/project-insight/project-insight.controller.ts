import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Request,
  ValidationPipe,
} from '@nestjs/common';
import { CreateProjectInsightRequestDto } from '../dto/create-project-insight-request.dto';
import {
  ProjectInsightsService,
  CreateBulkProjectInsightDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { InsightPermissionService } from '../permission/insight-permission.service';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import {
  createProjectInsightProjectLevel,
  deleteProjectInsightProjectLevel,
  readProjectInsightProjectLevel,
} from './project-insight.permissions';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

/**
 * Controller for managing project insights within an organization.
 * This controller handles the creation of project insight mappings.
 */
@ApiTags('Project Insight')
@ApiSecurity('Bearer Token')
@Controller('organization/:organizationId/project-insight')
export class ProjectInsightController {
  constructor(
    private readonly projectInsightsService: ProjectInsightsService,
    private readonly insightPermissionService: InsightPermissionService,
  ) {}

  /**
   * Creates a mapping between a project and an insight, verifying the user's permissions
   * to modify the project and access the insight.
   *
   * This method first checks if the user has modify permissions on the provided projects.
   * Then, it verifies if the user has the necessary permissions to access the insight.
   * If both checks pass, the project insight mapping is created.
   *
   * @param req - The request object, containing user authentication details.
   * @param organizationId - The ID of the organization in which the project insight will be created.
   * @param createProjectInsightRequestDto - The DTO containing the details for creating the project insight mapping.
   * @returns A promise that resolves with the created project insight.
   * @throws ForbiddenException if the user does not have permission to modify the project or access the insight.
   */
  @Post()
  @Permissions([createProjectInsightProjectLevel])
  async createProjectInsight(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe)
    createProjectInsightRequestDto: CreateProjectInsightRequestDto,
  ) {
    const { insightId, projectIds } = createProjectInsightRequestDto;
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId,
    };

    await this.insightPermissionService.accessToInsight(
      userDetails,
      insightId,
      'Create Project Insight',
    );
    return await this.projectInsightsService.createProjectInsightAsPromise({
      insightId,
      projectIds,
    });
  }

  /**
   *
   */
  @Post('bulk')
  @Permissions([createProjectInsightProjectLevel])
  async createBulkProjectInsights(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe)
    createBulkProjectInsightDto: CreateBulkProjectInsightDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId,
    };

    await this.insightPermissionService.validateBulkProjectInsightCreation(
      userDetails,
      createBulkProjectInsightDto.insightIds,
    );

    return this.projectInsightsService.createBulkProjectInsightsAsPromise(
      createBulkProjectInsightDto,
    );
  }

  /**
   * Retrieves insights for a specific project within an organization.
   *
   * This endpoint fetches project insights based on the provided project ID and organization ID. It also ensures that the user making the request has the necessary permissions to view project insights.
   * The response is paginated, with options for controlling the offset and number of results per page.
   *
   * @param {Request} req - The request object containing user and authorization details.
   * @param {string} organizationId - The ID of the organization to which the project belongs.
   * @param {number} projectId - The ID of the project for which insights are being requested.
   * @param {PaginationOptions} paginationOptions - The pagination options to control the result set.
   * @returns {Promise<InsightResponseDto[]>} - A promise that resolves to an array of project insights for the given project, paginated according to the provided options.
   *
   * @throws {ForbiddenException} - Throws an error if the user does not have the necessary permissions to view project insights.
   */
  @Get('projectId/:projectId')
  @Permissions(readProjectInsightProjectLevel)
  async getProjectInsightsByProject(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('projectId') projectId: number,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    return this.projectInsightsService.getProjectInsightsAsPromise(
      projectId,
      paginationOptions.offset,
      paginationOptions.perPage,
    );
  }

  /**
   * Checks if the user has the necessary permissions to read the project insight details.
   * This function is invoked when a GET request is made to the endpoint
   * `/projectId/:projectId/insightId/:insightId`, where `:projectId` and `:insightId`
   * are placeholders for the project and insight IDs respectively. The user must have the
   * `readProjectInsightProjectLevel` permission to access this endpoint.
   *
   * @param {Request} req - The request object that contains user details (e.g., userId, authorization header).
   * @param {string} organizationId - The organization ID associated with the project.
   * @param {number} projectId - The ID of the project for which the insight is being checked.
   * @param {string} insightId - The ID of the specific insight being queried.
   *
   * @returns {Promise<any>} - A promise that resolves to the project insight details for the specified insightId.
   *
   * @throws {UnauthorizedException} - If the user does not have the necessary permissions to access the project insight.
   */
  @Get('projectId/:projectId/insightId/:insightId')
  @Permissions([readProjectInsightProjectLevel])
  async getProjectInsightByProject(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('projectId') projectId: number,
    @Param('insightId') insightId: string,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId,
    };
    return await this.projectInsightsService.getProjectInsightDetailAsPromise(
      insightId,
      organizationId,
      req.userId,
    );
  }

  /**
   * Deletes a single mapping between a project and an insight, verifying the user's permissions
   * to modify the project and access the insight.
   *
   * This method checks if the user has modify permissions on the provided project.
   * Then, it verifies if the user has the necessary permissions to access the insight.
   * If both checks pass, the project insight mapping is deleted.
   *
   * @param req - The request object, containing user authentication details.
   * @param organizationId - The ID of the organization in which the project insight will be deleted.
   * @param insightId - The ID of the insight to delete.
   * @param projectId - The ID of the project to delete the insight mapping for.
   * @returns A promise that resolves when the project insight mapping is deleted.
   * @throws ForbiddenException if the user does not have permission to modify the project or access the insight.
   */
  @Delete('/insight/:insightId/projectId/:projectId')
  @Permissions(deleteProjectInsightProjectLevel)
  async deleteProjectInsight(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('insightId') insightId: string,
    @Param('projectId') projectId: number,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId,
    };

    // Check if the user has access to the specified insight
    await this.insightPermissionService.accessToInsight(
      userDetails,
      insightId,
      'Delete Project Insight',
    );

    // Call the service to delete the project insight mapping for the specified project and insight
    return await this.projectInsightsService.deleteProjectInsightAsPromise(
      insightId,
      projectId,
    );
  }
}
