import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateInsightFolderRequestDto {
  @AutoMap()
  @IsString()
  @IsNotEmpty({
    message: 'Insight Folder name must contain at least one character.',
  })
  name: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  description?: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  parentFolderId?: string;
}
