import { AutoMap } from '@automapper/classes';
import { IsBoolean, IsOptional, IsString } from 'class-validator';

export class UpdateInsightRequestDto {
  @AutoMap()
  @IsOptional()
  @IsString()
  finding?: string;

  @AutoMap()
  @IsOptional()
  @IsBoolean()
  publish?: boolean;

  @AutoMap()
  @IsOptional()
  @IsString()
  title?: string;

  @AutoMap()
  @IsOptional()
  @IsString()
  recommendation?: string;
}
