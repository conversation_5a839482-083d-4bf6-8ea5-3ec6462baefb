import { AutoMap } from '@automapper/classes';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { INSIGHT_TYPE } from 'src/constants/insight.constants';

export class ListTopInsightsFilterRequestDto {
  /**
   * count of top insights to retrieve
   * default 30, max of 100
   * @example 30
   */
  @AutoMap()
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  count?: number;

  /**
   * list of types to filter
   * @example ["BRAND", "INDUSTRY", "PLATFORM"]
   */
  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsEnum(INSIGHT_TYPE, { each: true })
  @IsOptional()
  typeFilter?: INSIGHT_TYPE[];

  /**
   * List of workspace ids user has access to
   * @example [1, 2]
   */
  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  @Type(() => Number)
  workspaceIds: number[];

  /**
   * organization id
   * @example 'xxxx-yyyy'
   */
  @AutoMap()
  @IsString()
  @Type(() => String)
  organizationId: string;
}
