import {
  ArrayNotEmpty,
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import {
  CampaignDto,
  CopilotBrandMetadataDto,
  CreateAnalyticsReportFilterMetadataDto,
  NormativeMetadataDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { AutoMap } from '@automapper/classes';
import { Transform, Type } from 'class-transformer';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { AnalyticsReportType } from '../../saved-report/model/analytics-report';
import {
  INSIGHT_NON_ARCHIVED_STATUS,
  INSIGHT_SOURCE,
  INSIGHT_TYPE,
} from '../../../constants/insight.constants';

export class InsightRequestDto {
  @AutoMap()
  @IsNumber()
  @IsOptional()
  activeWorkspaceId?: number;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  adAccountIds?: string[];

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  adAccountIdsUserHasAccess?: string[];

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  audienceIds?: string[];

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  brandIds?: string[];

  @AutoMap()
  @IsOptional()
  @IsObject()
  brandFilters?: CreateAnalyticsReportFilterMetadataDto;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @IsOptional()
  campaigns?: CampaignDto[];

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  categoryIds?: string[];

  @AutoMap()
  @IsOptional()
  @IsString()
  copilotChatId?: string;

  @AutoMap()
  @IsNumber()
  @IsOptional()
  endDate?: number;

  @AutoMap()
  @IsString()
  @IsOptional()
  finding?: string;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  formats?: string[];

  @AutoMap()
  @IsNumber()
  @IsOptional()
  industryId?: number;

  @AutoMap()
  @IsNumber({}, { each: true })
  @IsOptional()
  kpiIds?: number[];

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  marketIds?: string[];

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  @IsOptional()
  mediaTypes?: string[];

  @AutoMap()
  @IsOptional()
  @IsObject()
  normativeMetadata?: NormativeMetadataDto;

  @AutoMap()
  @IsOptional()
  @IsObject()
  copilotBrandMetadata?: CopilotBrandMetadataDto;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  @IsOptional()
  objectiveIds?: number[];

  @AutoMap()
  @IsString()
  organizationId: string;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  @IsOptional()
  placementIds?: number[];

  @AutoMap()
  @IsEnum(Platform)
  @Transform(({ value }) => value.toLowerCase())
  platform: Platform;

  @AutoMap()
  @IsString()
  @IsOptional()
  recommendation?: string;

  @AutoMap()
  @IsString()
  @IsOptional()
  reportId?: string;

  @AutoMap()
  @IsEnum(AnalyticsReportType)
  @IsOptional()
  reportType?: AnalyticsReportType;

  @AutoMap()
  @IsEnum(INSIGHT_SOURCE)
  source: INSIGHT_SOURCE;

  @AutoMap()
  @IsNumber()
  @IsOptional()
  startDate?: number;

  // TODO figure out the type here
  @AutoMap()
  @IsEnum(INSIGHT_NON_ARCHIVED_STATUS)
  @Transform(({ value }) => value as string)
  status: any;

  @AutoMap()
  @IsString()
  @IsNotEmpty({ message: 'Insight title must contain at least one character.' })
  title: string;

  @AutoMap()
  @IsEnum(INSIGHT_TYPE)
  type: INSIGHT_TYPE;

  @AutoMap()
  @IsArray()
  @ArrayNotEmpty()
  @IsOptional()
  @IsNumber({}, { each: true })
  workspaceIds?: number[];
}

export class CopilotInsightRequestDto extends InsightRequestDto {
  @IsUUID()
  id: string;
}
