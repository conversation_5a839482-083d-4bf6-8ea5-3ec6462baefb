import { Test, TestingModule } from '@nestjs/testing';
import { InsightPermissionService } from './insight-permission.service';
import {
  InsightPermissionDetailsService as InsightPermissionDetailsServiceSdk,
  InsightPermissionDetailsResponseDto,
  CreateInsightRequestDto,
  InsightsService,
  InsightsFoldersService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  bigIntId,
  createStubCreateInsightRequestDto,
  createStubInsightPermissionDetailsResponseDto,
  stringUUID,
} from '../test-utils/dto-stub-factories';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { UserAdAccountsAndWorkspacesDto } from '../../saved-report/model/analytics-report';
import {
  createStubReadPlatformAdAcccountDto,
  createStubUserAdAccountsAndWorkspacedDto,
  createStubUserDetails,
  createStubWorkspace,
} from '../../saved-report/test-utils/test-object-factories';
import { INSIGHT_TYPE } from '../../../constants/insight.constants';
import { OrganizationUserService } from '../../../account-management/organization/organization-user/organization-user.service';
import { ForbiddenException } from '@nestjs/common';

const defaultUserDetails = createStubUserDetails({});

const buildInsightPermissionSerivce = async ({
  insightPermissionDetails = undefined,
  insightPermissionDetailsList = [],
  insightCreateDto = undefined,
  userAccountsAndWorkspaces = undefined,
}: {
  insightPermissionDetails?: InsightPermissionDetailsResponseDto | undefined;
  insightPermissionDetailsList?: InsightPermissionDetailsResponseDto[];
  insightCreateDto?: CreateInsightRequestDto | undefined;
  userAccountsAndWorkspaces?: UserAdAccountsAndWorkspacesDto | undefined;
}) => {
  let insightPermissionDetailResponse = undefined;
  if (insightPermissionDetails) {
    insightPermissionDetailResponse = insightPermissionDetails;
  } else if (insightCreateDto) {
    insightPermissionDetailResponse = {
      id: undefined,
      organizationId: insightCreateDto.organizationId,
      workspaces: insightCreateDto?.workspaceIds,
      type: insightCreateDto.type,
    };
  }
  const module: TestingModule = await Test.createTestingModule({
    providers: [
      InsightPermissionService,
      {
        provide: InsightPermissionDetailsServiceSdk,
        useValue: {
          insightPermissionDetailsAsPromise: jest
            .fn()
            .mockResolvedValue(insightPermissionDetailResponse),
          insightPermissionDetailsListAsPromise: jest
            .fn()
            .mockResolvedValue({ result: insightPermissionDetailsList }),
        },
      },
      {
        provide: InsightsFoldersService,
        useValue: {
          getFolderAsPromise: jest.fn().mockResolvedValue(undefined),
        },
      },
      {
        provide: AnalyticsUserService,
        useValue: {
          fetchUserAdAccountsAndWorkspaces: jest
            .fn()
            .mockResolvedValue(userAccountsAndWorkspaces),
        },
      },
      {
        provide: OrganizationUserService,
        useValue: {
          getUserInOrganization: jest.fn(),
        },
      },
      {
        provide: InsightsService,
        useValue: {
          getListOfPublishedInsightsByIdAsPromise: jest.fn().mockResolvedValue({
            result: [],
          }),
        },
      },
    ],
  }).compile();
  return module.get<InsightPermissionService>(InsightPermissionService);
};

describe('InsightPermissionService', () => {
  describe('brand insight', () => {
    describe('when insight id', () => {
      it('throws the expected error when insight does not have workspaces', async () => {
        const insightWithNoWorkspaces =
          createStubInsightPermissionDetailsResponseDto({
            workspaces: [],
            type: INSIGHT_TYPE.BRAND,
          });
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails: insightWithNoWorkspaces,
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        await expect(
          insightPermissionService.accessToInsight(
            defaultUserDetails,
            insightWithNoWorkspaces.id,
            'Test Action',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: Error: WorkspaceIds are required to Test Action on brand insights`,
        );
      });
      it('throws the expected error when insight contains workspaces user does not have access to', async () => {
        const insightWithWorkspaces =
          createStubInsightPermissionDetailsResponseDto({
            workspaces: [123, 456],
            type: INSIGHT_TYPE.BRAND,
          });
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails: insightWithWorkspaces,
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 789 }),
              createStubWorkspace({ id: 123 }),
            ],
          }),
        });
        await expect(
          insightPermissionService.accessToInsight(
            defaultUserDetails,
            insightWithWorkspaces.id,
            'Test Action',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: ForbiddenException: User does not have access to workspace: 456`,
        );
      });
      it('returns true when user has access to the insight based on workspaces', async () => {
        const insightWithWorkspaces =
          createStubInsightPermissionDetailsResponseDto({
            workspaces: [123, 456],
            type: INSIGHT_TYPE.BRAND,
          });
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails: insightWithWorkspaces,
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 123 }),
              createStubWorkspace({ id: 456 }),
            ],
          }),
        });
        expect(
          await insightPermissionService.accessToInsight(
            defaultUserDetails,
            insightWithWorkspaces.id,
            'Test Action',
          ),
        ).toEqual(true);
      });
    });
    describe('when insight is dto', () => {
      it('throws the expected error when insight does not have workspaces', async () => {
        const insightWithNoWorkspaces = createStubCreateInsightRequestDto({
          workspaceIds: [],
          type: INSIGHT_TYPE.BRAND,
        });
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightCreateDto: insightWithNoWorkspaces,
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        await expect(
          insightPermissionService.accessToCreateInsight(
            defaultUserDetails,
            insightWithNoWorkspaces,
            'Test Action',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: Error: WorkspaceIds are required to Test Action on brand insights`,
        );
      });
      it('throws the expected error when insight contains workspaces user does not have access to', async () => {
        const insightWithWorkspaces = createStubCreateInsightRequestDto({
          workspaceIds: [123, 456, 789],
          type: INSIGHT_TYPE.BRAND,
        });
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightCreateDto: insightWithWorkspaces,
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 789 }),
              createStubWorkspace({ id: 101112 }),
            ],
          }),
        });
        await expect(
          insightPermissionService.accessToCreateInsight(
            defaultUserDetails,
            insightWithWorkspaces,
            'Test Action',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: ForbiddenException: User does not have access to workspace: 123,456`,
        );
      });
      it('returns true when user has access to the insight based on workspaces', async () => {
        const insightWithWorkspaces = createStubCreateInsightRequestDto({
          workspaceIds: [123, 456],
          type: INSIGHT_TYPE.BRAND,
        });
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightCreateDto: insightWithWorkspaces,
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 123 }),
              createStubWorkspace({ id: 456 }),
            ],
          }),
        });
        expect(
          await insightPermissionService.accessToCreateInsight(
            defaultUserDetails,
            insightWithWorkspaces,
            'Test Action',
          ),
        ).toEqual(true);
      });
    });
    describe('validateAndHydrateBrandInsight', () => {
      it('throws the expected error when insight does not have workspaces', async () => {
        const insightWithNoWorkspaces = createStubCreateInsightRequestDto({
          type: INSIGHT_TYPE.BRAND,
        });
        const insightPermissionService = await buildInsightPermissionSerivce({
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });

        await expect(
          insightPermissionService.validateAndHydrateBrandInsightCreation(
            'Test Action',
            insightWithNoWorkspaces,
            defaultUserDetails,
            'organization-id',
          ),
        ).rejects.toThrowError(
          `WorkspaceIds are required to Test Action on brand insights`,
        );
      });
      it('throws the expected error when insight contains workspaces user does not have access to', async () => {
        const insightWithWorkspaces = createStubCreateInsightRequestDto({
          workspaceIds: [123, 456],
          type: INSIGHT_TYPE.BRAND,
        });

        const stubUserAdAccoountsAndWorkspaces =
          createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 789 }),
              createStubWorkspace({ id: 123 }),
            ],
          });
        const insightPermissionService = await buildInsightPermissionSerivce({
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 789 }),
              createStubWorkspace({ id: 123 }),
            ],
          }),
        });
        await expect(
          insightPermissionService.validateAndHydrateBrandInsightCreation(
            'Test Action',
            insightWithWorkspaces,
            defaultUserDetails,
            'organization-id',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: ForbiddenException: User does not have access to workspace: 456`,
        );
      });
      it('returns true when user has access to the insight based on workspaces and ad accounts', async () => {
        const insightWithWorkspaces = createStubCreateInsightRequestDto({
          workspaceIds: [123, 789],
          type: INSIGHT_TYPE.BRAND,
          adAccountIds: ['aaa'],
        });

        const stubUserAdAccountsAndWorkspaces =
          createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: 789 }),
              createStubWorkspace({ id: 123 }),
            ],
            adAccounts: [
              createStubReadPlatformAdAcccountDto({ platformAccountId: 'aaa' }),
            ],
          });

        const insightPermissionService = await buildInsightPermissionSerivce({
          userAccountsAndWorkspaces: stubUserAdAccountsAndWorkspaces,
        });

        const result =
          await insightPermissionService.validateAndHydrateBrandInsightCreation(
            'Test Action',
            insightWithWorkspaces,
            defaultUserDetails,
            'organization-id',
          );

        expect(result.organizationId).toEqual('organization-id');
        expect(result.userId).toEqual(defaultUserDetails.userId);
      });
    });
    describe('accessToInsights', () => {
      it('throws an error if there are any insights in the request that a user does not have access to', async () => {
        const orgId = stringUUID();
        const accessWorkspaceId = bigIntId();
        const noAccessWorkspaceId = bigIntId();
        const ownerId = bigIntId();
        const insightIds = [stringUUID(), stringUUID(), stringUUID()];
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetailsList: [
            {
              id: insightIds[0],
              organizationId: orgId,
              workspaces: [accessWorkspaceId],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[1],
              organizationId: orgId,
              workspaces: [accessWorkspaceId],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[2],
              organizationId: orgId,
              workspaces: [noAccessWorkspaceId],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
          ],
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [createStubWorkspace({ id: accessWorkspaceId })],
          }),
        });
        await expect(
          insightPermissionService.accessToInsights(
            defaultUserDetails,
            insightIds,
            'Test Action',
          ),
        ).rejects.toThrowError(
          `User ${defaultUserDetails.userId} does not have access to Test Action on the following insights: ${insightIds[2]}`,
        );
      });
      it('returns true if there are no insights in the request', async () => {
        const accessWorkspaceId = bigIntId();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetailsList: [],
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [createStubWorkspace({ id: accessWorkspaceId })],
          }),
        });
        const result = await insightPermissionService.accessToInsights(
          defaultUserDetails,
          [],
          'Test Action',
        );
        expect(result).toEqual(true);
      }); // aka "do I have permission to do nothing, why yes you do
      it('returns true if user has access to all insights', async () => {
        const orgId = stringUUID();
        const accessWorkspaceId = bigIntId();
        const secondAccessWorkspaceId = bigIntId();
        const insightIds = [stringUUID(), stringUUID(), stringUUID()];
        const ownerId = bigIntId();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetailsList: [
            {
              id: insightIds[0],
              organizationId: orgId,
              workspaces: [accessWorkspaceId],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[1],
              organizationId: orgId,
              workspaces: [accessWorkspaceId],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[2],
              organizationId: orgId,
              workspaces: [secondAccessWorkspaceId],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
          ],
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [
              createStubWorkspace({ id: accessWorkspaceId }),
              createStubWorkspace({ id: secondAccessWorkspaceId }),
            ],
          }),
        });
        const result = await insightPermissionService.accessToInsights(
          defaultUserDetails,
          insightIds,
          'Test Action',
        );

        expect(result).toEqual(true);
      });
    });
  });
  describe('industry insight', () => {
    describe('when insight is id', () => {
      it('returns true if insight does not have an organization id', async () => {
        const id = stringUUID();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails:
            createStubInsightPermissionDetailsResponseDto({
              id,
              organizationId: undefined,
              type: INSIGHT_TYPE.INDUSTRY,
            }),
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        expect(
          await insightPermissionService.accessToInsight(
            defaultUserDetails,
            id,
            'Test Action',
          ),
        ).toEqual(true);
      });
      it('returns true if insight organization is present and the same as user organization', async () => {
        const organizationId = stringUUID();
        const insightId = stringUUID();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails:
            createStubInsightPermissionDetailsResponseDto({
              id: insightId,
              organizationId,
              type: INSIGHT_TYPE.INDUSTRY,
            }),
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        expect(
          await insightPermissionService.accessToInsight(
            createStubUserDetails({ organizationId }),
            insightId,
            'Test Action',
          ),
        ).toEqual(true);
      });
      it('throws an error when insight organization is not the same as user organization', async () => {
        const orgId = stringUUID();
        const insightId = stringUUID();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails:
            createStubInsightPermissionDetailsResponseDto({
              id: insightId,
              type: INSIGHT_TYPE.INDUSTRY,
              organizationId: orgId,
            }),
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        await expect(
          insightPermissionService.accessToInsight(
            defaultUserDetails,
            stringUUID(),
            'Test Action',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: ForbiddenException: User does not have access to organization ${orgId} on insight ${insightId}`,
        );
      });
    });
    describe('when insight is dto', () => {
      it('returns true if insight does not have an organization id', async () => {
        const id = stringUUID();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetails:
            createStubInsightPermissionDetailsResponseDto({
              organizationId: undefined,
              type: INSIGHT_TYPE.INDUSTRY,
            }),
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        expect(
          await insightPermissionService.accessToInsight(
            defaultUserDetails,
            id,
            'Test Action',
          ),
        ).toEqual(true);
      });
      it('returns true if insight organization is present and the same as user organization', async () => {
        const organizationId = stringUUID();
        const insightId = stringUUID();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightCreateDto: createStubCreateInsightRequestDto({
            organizationId,
            type: INSIGHT_TYPE.INDUSTRY,
          }),
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        expect(
          await insightPermissionService.accessToInsight(
            createStubUserDetails({ organizationId }),
            insightId,
            'Test Action',
          ),
        ).toEqual(true);
      });
      it('throws an error when insight organization is not the same as user orgainzation', async () => {
        const orgId = stringUUID();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightCreateDto: createStubCreateInsightRequestDto({
            organizationId: orgId,
            type: INSIGHT_TYPE.INDUSTRY,
          }),
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        await expect(
          insightPermissionService.accessToInsight(
            defaultUserDetails,
            stringUUID(),
            'Test Action',
          ),
        ).rejects.toThrowError(
          `Error occurred while trying to Test Action for user ${defaultUserDetails.userId}: ForbiddenException: User does not have access to organization ${orgId}`,
        );
      });
    });
    describe('validateAndHydrateIndustryInsight', () => {
      it('attaches the organizationId and userId if the user exists in the organization', async () => {
        const insightPermissionService = await buildInsightPermissionSerivce(
          {},
        );
        const result =
          await insightPermissionService.validateAndHydrateIndustryInsight(
            'Test Action',
            createStubCreateInsightRequestDto({}),
            defaultUserDetails,
            'organization-id',
          );
        expect(result.organizationId).toEqual('organization-id');
        expect(result.userId).toEqual(defaultUserDetails.userId);
      });
    });
    describe('accessToInsights', () => {
      it('throws an error if there are any insights in the request that a user does not have access to', async () => {
        const differentOrgId = stringUUID();
        const ownerId = bigIntId();
        const insightIds = [stringUUID(), stringUUID(), stringUUID()];
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetailsList: [
            {
              id: insightIds[0],
              organizationId: defaultUserDetails.organizationId,
              workspaces: [bigIntId()],
              type: CreateInsightRequestDto.TypeEnum.Industry,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[1],
              organizationId: defaultUserDetails.organizationId,
              workspaces: [bigIntId()],
              type: CreateInsightRequestDto.TypeEnum.Industry,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[2],
              organizationId: differentOrgId,
              workspaces: [bigIntId()],
              type: CreateInsightRequestDto.TypeEnum.Brand,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
          ],
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [createStubWorkspace({ id: bigIntId() })],
          }),
        });
        await expect(
          insightPermissionService.accessToInsights(
            defaultUserDetails,
            insightIds,
            'Test Action',
          ),
        ).rejects.toThrowError(
          `User ${defaultUserDetails.userId} does not have access to Test Action on the following insights: ${insightIds[2]}`,
        );
      });
      it('returns true if there are no insights in the request', async () => {
        const accessWorkspaceId = bigIntId();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetailsList: [],
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({
            workspaces: [createStubWorkspace({ id: accessWorkspaceId })],
          }),
        });
        const result = await insightPermissionService.accessToInsights(
          defaultUserDetails,
          [],
          'Test Action',
        );
        expect(result).toEqual(true);
      }); // aka "do I have permission to do nothing, why yes you do
      it('returns true if user has access to all insights', async () => {
        const insightIds = [stringUUID(), stringUUID(), stringUUID()];
        const ownerId = bigIntId();
        const insightPermissionService = await buildInsightPermissionSerivce({
          insightPermissionDetailsList: [
            {
              id: insightIds[0],
              organizationId: defaultUserDetails.organizationId,
              workspaces: [],
              type: CreateInsightRequestDto.TypeEnum.Industry,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[1],
              organizationId: defaultUserDetails.organizationId,
              workspaces: [],
              type: CreateInsightRequestDto.TypeEnum.Industry,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
            {
              id: insightIds[2],
              organizationId: defaultUserDetails.organizationId,
              workspaces: [],
              type: CreateInsightRequestDto.TypeEnum.Industry,
              status: InsightPermissionDetailsResponseDto.StatusEnum.Published,
              ownerId,
            },
          ],
          userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto(
            {},
          ),
        });
        const result = await insightPermissionService.accessToInsights(
          defaultUserDetails,
          insightIds,
          'Test Action',
        );

        expect(result).toEqual(true);
      });
    });
  });

  describe('hydrateUserDetailsWithWorkspaceAndAdAccount', () => {
    const defaultUserDetails = createStubUserDetails({});
    let service: InsightPermissionService;

    beforeEach(async () => {
      service = await buildInsightPermissionSerivce({
        userAccountsAndWorkspaces: createStubUserAdAccountsAndWorkspacedDto({}),
      });

      jest
        .spyOn(service, 'hydrateUserDetailsWithWorkspaceAndAdAccount')
        .mockImplementation(async (userDetails) => ({
          ...userDetails,
          workspaceIdsUserHasAccess: [bigIntId()],
          adAccountIdsUserHasAccess: [],
        }));
    });

    it('throws ForbiddenException when the user lacks access to any requested insight', async () => {
      const insightIds = [stringUUID(), stringUUID()]; // user will be missing the last one

      (service as any).insightServiceSdk = {
        getListOfPublishedInsightsByIdAsPromise: jest
          .fn()
          .mockResolvedValue({ result: [insightIds[0]] }),
      };

      await expect(
        service.validateBulkProjectInsightCreation(
          defaultUserDetails,
          insightIds,
        ),
      ).rejects.toThrow(
        new ForbiddenException(
          `User ${defaultUserDetails.userId} is missing access to one or more insights`,
        ),
      );
    });

    it('resolves successfully when the user has access to every requested insight', async () => {
      const insightIds = [stringUUID(), stringUUID(), stringUUID()];

      (service as any).insightServiceSdk = {
        getListOfPublishedInsightsByIdAsPromise: jest
          .fn()
          .mockResolvedValue({ result: insightIds }),
      };

      await expect(
        service.validateBulkProjectInsightCreation(
          defaultUserDetails,
          insightIds,
        ),
      ).resolves.toBeUndefined();
    });
  });
});
