import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
import {
  InsightPermissionDetailsService as InsightPermissionDetailsServiceSdk,
  InsightPermissionDetailsResponseDto,
  CreateInsightRequestDto,
  InsightsService,
  InsightsFoldersService as InsightFolderSerivceSdk,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { UserDetailsDto } from '../../dto/user-details.dto';
import { INSIGHT_TYPE } from '../../../constants/insight.constants';
import { CopilotInsightRequestDto } from '../dto/copilot-insight-request.dto';
import { OrganizationUserService } from '../../../account-management/organization/organization-user/organization-user.service';

export type InsightPermissionDetails = {
  id?: string;
  organizationId?: string;
  workspaces?: number[];
  type: CreateInsightRequestDto.TypeEnum;
  result?: InsightPermissionDetailsResponseDto;
};

export class UserDetailsWithWorkspaceAccess extends UserDetailsDto {
  workspaceIdsUserHasAccess: number[];
  adAccountIdsUserHasAccess: string[];
}

/**
 * Service to handle permission checks related to insights, including access control
 * based on workspace, organization, and insight type (Brand, Industry).
 */
@Injectable()
export class InsightPermissionService {
  private readonly logger = new Logger(InsightPermissionService.name);

  constructor(
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly insightPermissionDetailServiceSdk: InsightPermissionDetailsServiceSdk,
    private readonly organizationUserService: OrganizationUserService,
    private readonly insightServiceSdk: InsightsService,
    private readonly insightFolderServiceSdk: InsightFolderSerivceSdk,
  ) {}

  /**
   * Fetches the permission details for an insight using the SDK.
   *
   * @param insightId - The ID of the insight to retrieve permission details for.
   * @returns A promise that resolves to the insight permission details.
   */
  private async getInsightPermissionDetails(
    insightId: string,
  ): Promise<InsightPermissionDetailsResponseDto> {
    return await this.insightPermissionDetailServiceSdk.insightPermissionDetailsAsPromise(
      insightId,
    );
  }

  /**
   * Fetches the permission details for multiple insights using the SDK.
   *
   * @param insightIds - The list of insight IDs to retrieve permission details for.
   * @returns A promise that resolves to a list of insight permission details.
   */
  private async getInsightPermissionDetailsList(
    insightIds: string[],
  ): Promise<InsightPermissionDetailsResponseDto[]> {
    return await this.insightPermissionDetailServiceSdk.insightPermissionDetailsListAsPromise(
      {
        insightIds,
      },
    );
  }

  /**
   * Hydrates user details with their accessible workspaces and ad account information.
   *
   * @param userDetails - The user details to be hydrated with workspace and ad account access.
   * @returns A promise that resolves to user details enriched with workspace and ad account access.
   */
  async hydrateUserDetailsWithWorkspaceAndAdAccount(
    userDetails: UserDetailsDto,
  ): Promise<UserDetailsWithWorkspaceAccess> {
    const workspaceAndAdAccount =
      await this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
        userDetails,
      );

    return {
      ...userDetails,
      workspaceIdsUserHasAccess: workspaceAndAdAccount.workspaces.map(
        (workspace) => workspace.id,
      ),
      adAccountIdsUserHasAccess: workspaceAndAdAccount.adAccounts.map(
        (adAccount) => adAccount.platformAccountId,
      ),
    };
  }

  /**
   * Validates if the user has access to the provided workspaces.
   *
   * @param workspaceIds - The list of workspace IDs the user is trying to access.
   * @param workspaceIdsUserHasAccess - The list of workspace IDs the user has access to.
   * @throws ForbiddenException if the user does not have access to any of the provided workspaces.
   * @returns A boolean that is `true` if the user has access to all workspaces.
   */
  private validateAccessToWorkspaceInsights(
    workspaceIds: number[],
    workspaceIdsUserHasAccess: number[],
  ) {
    const workspaceIdsUserHasAccessSet = new Set(workspaceIdsUserHasAccess);
    const workspacesWithoutAccess = workspaceIds.filter(
      (workspaceId) => !workspaceIdsUserHasAccessSet.has(workspaceId),
    );

    if (workspacesWithoutAccess.length > 0) {
      throw new ForbiddenException(
        `User does not have access to workspace: ${workspacesWithoutAccess}`,
      );
    }
    return true;
  }

  /**
   * Validates if the user has access to the provided adAccounts.
   *
   * @throws ForbiddenException if the user does not have access to any of the provided ad accounts.
   * @returns A promise that resolves to `true` if the user has access to all ad accounts.
   * @param adAccountIds
   * @param adAccountUserHasAccess
   */
  private async validateAccessToInsightAdAccounts(
    adAccountIds: string[],
    adAccountUserHasAccess: string[],
  ) {
    const adAccountIdsUserHasAccessSet = new Set(adAccountUserHasAccess);
    const adAccountIdsWithoutAccess = adAccountIds.filter(
      (adAccountId) => !adAccountIdsUserHasAccessSet.has(adAccountId),
    );

    if (adAccountIdsWithoutAccess.length > 0) {
      throw new ForbiddenException(
        `User does not have access to adAccounts: ${adAccountIdsWithoutAccess}`,
      );
    }
    return true;
  }

  /**
   * Checks if the user has access to a brand insight.
   *
   * @param userDetails - The user details (including organization and workspaces).
   * @param insight - The insight to check access for.
   * @param actionMessage - A message describing the action being attempted (e.g., "view", "edit").
   * @throws Error if the insight does not include workspaces or the user does not have access.
   * @returns A boolean indicating whether the user has access to the brand insight.
   */
  private accessToBrandInsight(
    userDetails: UserDetailsWithWorkspaceAccess,
    insight: InsightPermissionDetails,
    actionMessage: string,
  ): boolean {
    if (!insight.workspaces || insight.workspaces.length <= 0) {
      throw new Error(
        `WorkspaceIds are required to ${actionMessage} on brand insights`,
      );
    }

    return this.validateAccessToWorkspaceInsights(
      insight.workspaces,
      userDetails.workspaceIdsUserHasAccess ?? [],
    );
  }

  /**
   * Checks if the user has access to a normative (industry) insight.
   *
   * @param userDetails - The user details (including organization).
   * @param insight - The insight to check access for.
   * @throws ForbiddenException if the user does not have access to the organization or insight.
   * @returns A boolean indicating whether the user has access to the normative insight.
   */
  private accessToNormativeInsight(
    userDetails: UserDetailsDto,
    insight: InsightPermissionDetails,
  ): boolean {
    // if no organization id then there is access
    if (insight.organizationId) {
      if (userDetails.organizationId != insight.organizationId) {
        let errorMessage = `User does not have access to organization ${insight.organizationId}`;
        if (insight.id) {
          errorMessage = errorMessage + ` on insight ${insight.id}`;
        }
        throw new ForbiddenException(errorMessage);
      }
    }
    return true;
  }

  /**
   * Checks access to an insight based on its type (Brand or Industry).
   *
   * @param userDetailsDto - The user details (including organization and workspaces).
   * @param insight - The insight to check access for.
   * @param actionMessage - A message describing the action being attempted (e.g., "view", "edit").
   * @throws Error if an unknown insight type is encountered.
   * @returns A boolean indicating whether the user has access to the insight.
   */
  private accessToInsightByType(
    userDetailsDto: UserDetailsWithWorkspaceAccess,
    insight: InsightPermissionDetails,
    actionMessage: string,
  ): boolean {
    try {
      if (insight.type === INSIGHT_TYPE.BRAND) {
        return this.accessToBrandInsight(
          userDetailsDto,
          insight,
          actionMessage,
        );
      } else if (insight.type === INSIGHT_TYPE.INDUSTRY) {
        return this.accessToNormativeInsight(userDetailsDto, insight);
      }
    } catch (error) {
      throw new Error(
        `Error occurred while trying to ${actionMessage} for user ${userDetailsDto.userId}: ` +
          error,
      );
    }
    return true;
  }

  private validateAccessToInsightByStatus(
    userDetailsDto: UserDetailsDto,
    insight: InsightPermissionDetails,
  ) {
    if (
      insight.result?.status ===
        InsightPermissionDetailsResponseDto.StatusEnum.Draft &&
      insight.result?.ownerId !== userDetailsDto.userId
    ) {
      throw new ForbiddenException(
        `User does not have access to draft insight ${insight.id}`,
      );
    }
  }

  /**
   * Checks if the user has access to a specific insight.
   *
   * @param userDetailsDto - The user details (including organization and workspaces).
   * @param insightId - The ID of the insight to check access for.
   * @param actionMessage - A message describing the action being attempted (e.g., "view", "edit").
   * @returns A promise that resolves to `true` if the user has access to the insight.
   */
  async accessToInsight(
    userDetailsDto: UserDetailsDto,
    insightId: string,
    actionMessage: string,
  ): Promise<boolean> {
    const insightPermissionDetails = await this.getInsightPermissionDetails(
      insightId,
    );

    this.validateAccessToInsightByStatus(
      userDetailsDto,
      insightPermissionDetails,
    );

    return this.accessToInsightByType(
      await this.hydrateUserDetailsWithWorkspaceAndAdAccount(userDetailsDto),
      insightPermissionDetails,
      actionMessage,
    );
  }

  /**
   * Checks if the user has access to multiple insights.
   *
   * @param userDetails - The user details (including organization and workspaces).
   * @param insightIds - The list of insight IDs to check access for.
   * @param actionMessage - A message describing the action being attempted (e.g., "view", "edit").
   * @returns A promise that resolves to `true` if the user has access to all insights.
   * @throws ForbiddenException if the user does not have access to any of the insights.
   */
  async accessToInsights(
    userDetails: UserDetailsDto,
    insightIds: string[],
    actionMessage: string,
  ): Promise<boolean> {
    const insightPermissionDetails = await this.getInsightPermissionDetailsList(
      insightIds,
    );

    const hydratedUserDetails =
      await this.hydrateUserDetailsWithWorkspaceAndAdAccount(userDetails);

    const noAccessInsights = (
      (insightPermissionDetails as any).result as InsightPermissionDetails[]
    )
      .map((insightPermission) => {
        let access = false;

        try {
          access = this.accessToInsightByType(
            hydratedUserDetails,
            insightPermission,
            actionMessage,
          );
        } catch (error) {
          access = false;
        }
        return {
          id: insightPermission.id,
          hasAccessToInsight: access,
        };
      })
      .filter((insightAccess) => {
        return !insightAccess.hasAccessToInsight;
      });
    if (insightIds.length > 0 && noAccessInsights.length > 0) {
      throw new ForbiddenException(
        `User ${
          userDetails.userId
        } does not have access to ${actionMessage} on the following insights: ${noAccessInsights
          .map((noAccessInsight) => noAccessInsight.id)
          .join(', ')}`,
      );
    }
    return true;
  }

  /**
   * Checks if the user has access to create a new insight.
   *
   * @param userDetailsDto - The user details (including organization and workspaces).
   * @param insight - The insight creation request details.
   * @param actionMessage - A message describing the action being attempted (e.g., "create").
   * @returns A promise that resolves to `true` if the user can create the insight.
   */
  async accessToCreateInsight(
    userDetailsDto: UserDetailsDto,
    insight: CreateInsightRequestDto,
    actionMessage: string,
  ): Promise<boolean> {
    return this.accessToInsightByType(
      await this.hydrateUserDetailsWithWorkspaceAndAdAccount(userDetailsDto),
      {
        organizationId: insight.organizationId,
        workspaces: insight.workspaceIds,
        type: insight.type,
      },
      actionMessage,
    );
  }

  /**
   * Validates the user's access to a brand insight creation by checking workspaces and ad accounts.
   * @param actionMessage
   * @param insight
   * @param userDetails
   * @param organizationId
   */
  async validateAndHydrateBrandInsightCreation(
    actionMessage: string,
    insight: CreateInsightRequestDto | CopilotInsightRequestDto,
    userDetails: UserDetailsDto,
    organizationId: string,
  ): Promise<CreateInsightRequestDto> {
    try {
      const hydratedUserDetails =
        await this.hydrateUserDetailsWithWorkspaceAndAdAccount(userDetails);

      this.accessToBrandInsight(
        hydratedUserDetails,
        {
          organizationId: insight.organizationId,
          workspaces: insight.workspaceIds,
          type: insight.type,
        },
        actionMessage,
      );

      if (!insight.adAccountIds || insight.adAccountIds.length <= 0) {
        throw new Error(
          `AdAccountIds are required to ${actionMessage} on brand insights`,
        );
      }

      await this.validateAccessToInsightAdAccounts(
        insight.adAccountIds,
        hydratedUserDetails.adAccountIdsUserHasAccess,
      );

      return {
        ...insight,
        userId: userDetails.userId,
        organizationId: organizationId,
      };
    } catch (error) {
      throw new Error(
        `Error occurred while trying to ${actionMessage} for user ${userDetails.userId}: ` +
          error,
      );
    }
  }

  /**
   * Validates and hydrates an industry (normative) insight creation request.
   *
   * @param actionMessage - A message describing the action being attempted (e.g., "create").
   * @param insight - The industry insight creation request details.
   * @param userDetails - The user details (including organization and workspaces).
   * @param organizationId - The organization ID associated with the insight.
   * @returns A promise that resolves to the hydrated industry insight creation request.
   * @throws ForbiddenException if the user is not part of the specified organization.
   */
  async validateAndHydrateIndustryInsight(
    actionMessage: string,
    insight: CreateInsightRequestDto | CopilotInsightRequestDto,
    userDetails: UserDetailsDto,
    organizationId: string,
  ): Promise<CreateInsightRequestDto> {
    try {
      await this.organizationUserService.getUserInOrganization(
        organizationId,
        userDetails.userId,
      );
    } catch (error) {
      throw new ForbiddenException(
        `Error trying to ${actionMessage} insight with response: ` + error,
      );
    }
    return {
      ...insight,
      userId: userDetails.userId,
      organizationId: organizationId,
    };
  }

  /**
   * Validates if the user has access to create multiple insights in bulk.
   * User must have access to each individual insight to be able to create them in bulk.
   * @param userDetails
   * @param insightIds
   */
  async validateBulkProjectInsightCreation(
    userDetails: UserDetailsDto,
    insightIds: string[],
  ) {
    const hydratedUserDetails =
      await this.hydrateUserDetailsWithWorkspaceAndAdAccount(userDetails);

    const { result: insightsUserHasAccess } =
      (await this.insightServiceSdk.getListOfPublishedInsightsByIdAsPromise({
        insightIds,
        organizationId: userDetails.organizationId,
        workspaceIdsUserHasAccess:
          hydratedUserDetails.workspaceIdsUserHasAccess,
      })) as unknown as { result: string[] };

    const userHasAccessToEveryInsight = insightIds.every((insightId) => {
      return insightsUserHasAccess.includes(insightId);
    });

    if (!userHasAccessToEveryInsight) {
      throw new ForbiddenException(
        `User ${userDetails.userId} is missing access to one or more insights`,
      );
    }
  }

  async getInsightAccessPermission(
    userDetailsDto: UserDetailsDto,
    insightId: string,
  ) {
    try {
      await this.accessToInsight(
        userDetailsDto,
        insightId,
        'get shared insight permissions',
      );
      return true;
    } catch (error) {
      this.logger.debug(
        `User ${userDetailsDto.userId} does not have access to insight ${insightId} with response: 
            ${error}`,
      );
      return false;
    }
  }

  /**
   * Checks if the folder exists
   * @param folderId
   */
  async getFolderAccessPermission(folderId: string) {
    try {
      await this.insightFolderServiceSdk.getFolderAsPromise(folderId);
      return true;
    } catch (error) {
      this.logger.debug(
        `Folder ${folderId} does not exist with response: 
        ${error}`,
      );
      return false;
    }
  }

  /**
   * Checks if the user is within the organization
   * @param userDetailsDto
   * @param organizationId
   */
  async getOrganizationAccessPermission(
    userDetailsDto: UserDetailsDto,
    organizationId: string,
  ) {
    try {
      await this.organizationUserService.getUserInOrganization(
        organizationId,
        userDetailsDto.userId,
      );
      return true;
    } catch (error) {
      this.logger.debug(
        `User ${userDetailsDto.userId} does not have access to organization ${organizationId} with response: 
        ${error}`,
      );
      return false;
    }
  }

  /**
   * Checks if the user has access to the specified workspace.
   * @param userDetailsDto
   * @param workspaceId
   */
  async getWorkspaceAccessPermission(
    userDetailsDto: UserDetailsDto,
    workspaceId: number,
  ) {
    const userWorkspaces =
      await this.analyticsUserService.fetchAllOrganizationWorkspacesForUser(
        userDetailsDto,
      );

    const userWorkspaceIds: number[] = userWorkspaces.map(
      (workspace) => workspace.id,
    );

    if (!userWorkspaceIds.includes(workspaceId)) {
      this.logger.debug(
        `User ${userDetailsDto.userId} does not have access to workspace ${workspaceId}`,
      );
      return false;
    }

    return true;
  }
}
