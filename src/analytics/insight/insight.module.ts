import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { InsightService } from './insight.service';
import { InsightsController } from './insights.controller';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { WorkspaceService } from 'src/account-management/organization/workspace/services/workspace.service';
import { AuthService } from 'src/auth/services/auth.service';
import { CopilotElasticSearchRepository } from '../brands-copilot/repository/copilot-elastic-search-repository.service';
import { BrandElasticsearchService } from '../brands-copilot/repository/elasticsearch.service';
import { InsightV2Controller } from './v2/insight.controller';
import { InsightV2Service } from './v2/insight.service';
import { NormativeInsightsMongoDbRepository } from '../analytics-copilot/repository/normative-insights-mongo-db-repository.service';
import { CopilotMongoDbRepository } from '../brands-copilot/repository/copilot-mongo-db-repository.service';
import { MongoDbService } from '../brands-copilot/repository/mongo-db.service';
import { InsightController } from './insight.controller';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { ProjectInsightController } from './project-insight/project-insight.controller';
import { MySQLClientService } from '../../common/services/mysql-client-wrapper';
import { InsightPermissionService } from './permission/insight-permission.service';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Project } from '../../entities/project.entity';
import { CopilotInsightService } from './copilot-insight/copilot-insight.service';
import { CopilotInsightsController } from './copilot-insight/copilot-insights.controller';
import { InsightFolderController } from './insight-folders/insight-folder.controller';
import { InsightTitleGeneratorService } from './insight-title-generator/insight-title-generator.service';
import { LlmOpenAiClient } from '../brands-copilot/llm/llm-open-ai-client.service';
import {
  SecretsConfigurationService,
  SecretsManagerService,
} from '@vidmob/vidmob-nestjs-common';
import { InsightFolderPermissionService } from './insight-folders/insight-folder-permission.service';
import { InsightPermissionController } from './permission/insight-permission.controller';
import { LlmClientRouter } from '../brands-copilot/llm/llm-client-router.service';
// import { LlmGeminiClient } from '../brands-copilot/llm/llm-gemini-client.service';
import { LlmVertexAiClient } from '../brands-copilot/llm/llm-vertex-ai-client.service';

@Module({
  imports: [
    HttpModule,
    ConfigModule.forRoot(),
    TypeOrmModule.forFeature([Project]),
  ],
  providers: [
    InsightService,
    InsightV2Service,
    InsightService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
    BrandElasticsearchService,
    CopilotElasticSearchRepository,
    MongoDbService,
    CopilotMongoDbRepository,
    NormativeInsightsMongoDbRepository,
    OrganizationUserService,
    MySQLClientService,
    InsightPermissionService,
    CopilotInsightService,
    InsightTitleGeneratorService,
    LlmOpenAiClient,
    // LlmGeminiClient,
    LlmVertexAiClient,
    LlmClientRouter,
    SecretsConfigurationService,
    SecretsManagerService,
    InsightFolderPermissionService,
  ],

  controllers: [
    InsightsController,
    InsightController,
    InsightV2Controller,
    ProjectInsightController,
    CopilotInsightsController,
    InsightFolderController,
    InsightPermissionController,
  ],
})
export class InsightModule {}
