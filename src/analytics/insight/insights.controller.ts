import { Controller, Post, Body, Request } from '@nestjs/common';
import { InsightService } from './insight.service';
import { UUID } from 'crypto';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

/**
 * This controller will be deleted once the frontend is updated to use the singular /insight endpoints
 */
@ApiTags('Insights v1 (Replaced by /insight)')
@ApiSecurity('Bearer Token')
@Controller('insights')
export class InsightsController {
  constructor(private readonly insightsService: InsightService) {}

  @Post('create')
  async createInsights(
    @Request() req: any,
    @Body()
    body: {
      insightRequest: any[];
      organizationId: string;
      workspaceIds: string[];
      projectId?: number;
      chatId?: UUID;
      messageId?: UUID;
    },
  ) {
    const authorization = req.headers.authorization;

    return this.insightsService.createCopilotInsights(
      body.insightRequest,
      body.organizationId,
      body.workspaceIds,
      authorization,
      body.projectId,
      body.chatId,
      body.messageId,
    );
  }
}
