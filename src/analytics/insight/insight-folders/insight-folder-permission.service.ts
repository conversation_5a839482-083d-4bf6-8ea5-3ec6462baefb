import { ForbiddenException, Injectable } from '@nestjs/common';

import {
  InsightsFoldersService,
  ReadInsightFolderDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';

@Injectable()
export class InsightFolderPermissionService {
  constructor(private readonly insightFolderService: InsightsFoldersService) {}

  async activeWorkspaceIsFolderWorkspace(
    workspaceId: number,
    folderId: string,
  ) {
    const folder = await this.insightFolderService.getFolderAsPromise(folderId);
    const folderWorkspaceId = ((folder as any).result as ReadInsightFolderDto)
      .workspaceId;
    if (folderWorkspaceId != workspaceId) {
      throw new ForbiddenException(
        `User not allowed to perform action on folder with id ${folderId} in workspace ${folderWorkspaceId}`,
      );
    }
    return true;
  }
}
