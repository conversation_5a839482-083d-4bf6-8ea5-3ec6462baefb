import { Test, TestingModule } from '@nestjs/testing';
import { InsightsFoldersService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  bigIntId,
  createStubInsightFolderRequestDto,
  createStubReadListInsightDto,
  createStubUpdateInsightFolderRequestDto,
  createStubUserDetailsWithWorkspaceAccess,
  stringUUID,
} from '../test-utils/dto-stub-factories';
import { InsightFolderController } from './insight-folder.controller';
import { ForbiddenException } from '@nestjs/common';
import { SortOrder } from '../../../reports/model/sort-order';
import { InsightSortBy } from '../constants/constants';
import { InsightPermissionService } from '../permission/insight-permission.service';
import { InsightFolderPermissionService } from './insight-folder-permission.service';

const defaultRequest = {
  headers: { authorization: `Bearer blopp blorp` },
  userId: bigIntId(),
};
const buildInsightFolderController = async ({
  createInsightFolderFn = jest.fn(),
  updateInsightFolderFn = jest.fn(),
  deleteInsightFolderFn = jest.fn(),
  favoriteInsightFolderFn = jest.fn(),
  findInsightFolderFn = jest.fn(),
  findInsightFn = jest.fn(),
  moveInsightFn = jest.fn(),
  moveFolderFn = jest.fn(),
  getFolderFn = jest.fn(),
  hydrateUserRequestFn = jest.fn(),
  accessToInsightsFn = jest.fn(),
  activeWorkspaceIsFolderWorkspaceFn = jest.fn(),
  findFilteredFoldersAsPromiseFn = jest.fn(),
}: {
  createInsightFolderFn?: any;
  updateInsightFolderFn?: any;
  deleteInsightFolderFn?: any;
  favoriteInsightFolderFn?: any;
  findInsightFolderFn?: any;
  findInsightFn?: any;
  moveInsightFn?: any;
  moveFolderFn?: any;
  getFolderFn?: any;
  hydrateUserRequestFn?: any;
  accessToInsightsFn?: any;
  activeWorkspaceIsFolderWorkspaceFn?: any;
  findFilteredFoldersAsPromiseFn?: any;
}) => {
  const module: TestingModule = await Test.createTestingModule({
    providers: [
      InsightFolderController,
      {
        provide: InsightsFoldersService,
        useValue: {
          createInsightFolderAsPromise: createInsightFolderFn,
          updateInsightFolderAsPromise: updateInsightFolderFn,
          deleteInsightFolderAsPromise: deleteInsightFolderFn,
          favoriteInsightFolderAsPromise: favoriteInsightFolderFn,
          findFoldersForParentAsPromise: findInsightFolderFn,
          findFilteredFoldersAsPromise: findFilteredFoldersAsPromiseFn,
          findInsightsForParentAsPromise: findInsightFn,
          moveInsightAsPromise: moveInsightFn,
          moveFolderAsPromise: moveFolderFn,
          getFolderAsPromise: getFolderFn,
        },
      },
      {
        provide: InsightPermissionService,
        useValue: {
          accessToInsights: accessToInsightsFn,
          hydrateUserDetailsWithWorkspaceAndAdAccount: hydrateUserRequestFn,
        },
      },
      {
        provide: InsightFolderPermissionService,
        useValue: {
          activeWorkspaceIsFolderWorkspace: activeWorkspaceIsFolderWorkspaceFn,
        },
      },
    ],
  }).compile();
  return module.get<InsightFolderController>(InsightFolderController);
};

describe('InsightFolderController', () => {
  it('creates an insight folder', async () => {
    const mockCreateInsightFolder = jest.fn();
    const insightFolderService = await buildInsightFolderController({
      createInsightFolderFn: mockCreateInsightFolder,
    });

    const organizationId = stringUUID();
    const workspaceId = bigIntId();
    const createRequestDto = createStubInsightFolderRequestDto({});

    const result = await insightFolderService.createInsightFolder(
      defaultRequest,
      organizationId,
      workspaceId,
      createRequestDto,
    );

    expect(mockCreateInsightFolder).toHaveBeenCalledWith({
      createdBy: defaultRequest.userId,
      description: createRequestDto.description,
      name: createRequestDto.name,
      organizationId: organizationId,
      parentFolderId: createRequestDto.parentFolderId,
      workspaceId: workspaceId,
    });
  });
  describe('update', () => {
    it('updates an insight folder successfully', async () => {
      const mockUpdateInsightFolder = jest.fn();
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockResolvedValue(true);
      const insightFolderService = await buildInsightFolderController({
        updateInsightFolderFn: mockUpdateInsightFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const folderId = stringUUID();
      const updateRequestDto = createStubUpdateInsightFolderRequestDto({});

      const result = await insightFolderService.updateInsightFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        folderId,
        updateRequestDto,
      );

      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalled();
      expect(mockUpdateInsightFolder).toHaveBeenCalledWith(
        folderId,
        updateRequestDto,
      );
    });

    it('throws ForbiddenException if the folder belongs to a different workspace', async () => {
      const mockUpdateInsightFolder = jest.fn();
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));

      const insightFolderService = await buildInsightFolderController({
        updateInsightFolderFn: mockUpdateInsightFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const folderId = stringUUID();
      const updateRequestDto = createStubUpdateInsightFolderRequestDto({});

      await expect(
        insightFolderService.updateInsightFolder(
          defaultRequest,
          organizationId,
          workspaceId,
          folderId,
          updateRequestDto,
        ),
      ).rejects.toThrowError('no permishies');
      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        folderId,
      );
      expect(mockUpdateInsightFolder).not.toHaveBeenCalled(); // Ensure update was not called
    });
  });
  describe('deleteInsightFolder', () => {
    it('deletes an insight folder successfully', async () => {
      const mockDeleteInsightFolder = jest.fn();
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockResolvedValue(true);
      const insightFolderService = await buildInsightFolderController({
        deleteInsightFolderFn: mockDeleteInsightFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const folderId = stringUUID();

      const result = await insightFolderService.deleteInsightFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        folderId,
      );

      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        folderId,
      );
      expect(mockDeleteInsightFolder).toHaveBeenCalledWith(folderId);
    });

    it('throws ForbiddenException if the folder belongs to a different workspace', async () => {
      const mockDeleteInsightFolder = jest.fn();
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));

      const insightFolderService = await buildInsightFolderController({
        deleteInsightFolderFn: mockDeleteInsightFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const folderId = stringUUID();

      await expect(
        insightFolderService.deleteInsightFolder(
          defaultRequest,
          organizationId,
          workspaceId,
          folderId,
        ),
      ).rejects.toThrowError('no permishies');

      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        folderId,
      );
      expect(mockDeleteInsightFolder).not.toHaveBeenCalled();
    });
  });
  describe('favoriteInsight', () => {
    it('favorites an insight folder successfully', async () => {
      const mockFavoriteInsightFolder = jest.fn();
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockResolvedValue(true);
      const insightFolderService = await buildInsightFolderController({
        favoriteInsightFolderFn: mockFavoriteInsightFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const folderId = stringUUID();
      const favoriteRequestDto = { favorite: true };

      const result = await insightFolderService.favoriteInsightFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        folderId,
        favoriteRequestDto,
      );

      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        folderId,
      );
      expect(mockFavoriteInsightFolder).toHaveBeenCalledWith(folderId, {
        userId: defaultRequest.userId,
        ...favoriteRequestDto,
      });
    });

    it('throws ForbiddenException if the folder belongs to a different workspace', async () => {
      const mockFavoriteInsightFolder = jest.fn();
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));

      const insightFolderService = await buildInsightFolderController({
        favoriteInsightFolderFn: mockFavoriteInsightFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const folderId = stringUUID();
      const favoriteRequestDto = { favorite: true };

      await expect(
        insightFolderService.favoriteInsightFolder(
          defaultRequest,
          organizationId,
          workspaceId,
          folderId,
          favoriteRequestDto,
        ),
      ).rejects.toThrowError('no permishies');

      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        folderId,
      );
      expect(mockFavoriteInsightFolder).not.toHaveBeenCalled();
    });
  });
  describe('findFoldersForParent', () => {
    it('returns a paginated list of folders', async () => {
      const mockFindFoldersForParent = jest.fn().mockResolvedValue({
        data: [{ id: 'folder1' }, { id: 'folder2' }],
        pagination: { total: 2, perPage: 10, offset: 0 },
      });

      const insightFolderService = await buildInsightFolderController({
        findInsightFolderFn: mockFindFoldersForParent,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const paginationOptions = { offset: 0, perPage: 10 };
      const parentFolderId = stringUUID();

      const result = await insightFolderService.findFoldersForParent(
        defaultRequest,
        organizationId,
        workspaceId,
        paginationOptions,
        parentFolderId,
      );

      expect(mockFindFoldersForParent).toHaveBeenCalledWith(
        {
          userId: defaultRequest.userId,
          workspaceId: workspaceId,
          organizationId: organizationId,
          parentFolderId: parentFolderId,
        },
        paginationOptions.offset,
        paginationOptions.perPage,
      );
      expect(result).toEqual({
        data: [{ id: 'folder1' }, { id: 'folder2' }],
        pagination: { total: 2, perPage: 10, offset: 0 },
      });
    });

    it('handles missing parentFolderId correctly', async () => {
      const mockFindFoldersForParent = jest.fn().mockResolvedValue({
        data: [{ id: 'folder1' }, { id: 'folder2' }],
        pagination: { total: 2, perPage: 10, offset: 0 },
      });

      const insightFolderService = await buildInsightFolderController({
        findInsightFolderFn: mockFindFoldersForParent,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const paginationOptions = { offset: 0, perPage: 10 };

      const result = await insightFolderService.findFoldersForParent(
        defaultRequest,
        organizationId,
        workspaceId,
        paginationOptions,
      );

      expect(mockFindFoldersForParent).toHaveBeenCalledWith(
        {
          userId: defaultRequest.userId,
          workspaceId: workspaceId,
          organizationId: organizationId,
        },
        paginationOptions.offset,
        paginationOptions.perPage,
      );
      expect(result).toEqual({
        data: [{ id: 'folder1' }, { id: 'folder2' }],
        pagination: { total: 2, perPage: 10, offset: 0 },
      });
    });
  });
  describe('findFoldersFiltered', () => {
    it('returns a paginated list of folders with filtering applied', async () => {
      const mockFindFolders = jest.fn().mockResolvedValue({
        data: [{ id: 'folder1' }, { id: 'folder2' }],
        pagination: { total: 2, perPage: 10, offset: 0 },
      });

      const insightFolderService = await buildInsightFolderController({
        findFilteredFoldersAsPromiseFn: mockFindFolders,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const paginationOptions = { offset: 0, perPage: 10 };
      const parentFolderId = stringUUID();

      const result = await insightFolderService.findFoldersFiltered(
        defaultRequest,
        organizationId,
        workspaceId,
        { favorited: true },
        paginationOptions,
      );

      expect(mockFindFolders).toHaveBeenCalledWith(
        {
          userId: defaultRequest.userId,
          workspaceId: workspaceId,
          organizationId: organizationId,
          favorited: true,
        },
        paginationOptions.offset,
        paginationOptions.perPage,
      );

      expect(result).toEqual({
        data: [{ id: 'folder1' }, { id: 'folder2' }],
        pagination: { total: 2, perPage: 10, offset: 0 },
      });
    });
  });
  describe('findInsightsForParentFolder', () => {
    it('returns a paginated list of insights for the parent folder', async () => {
      const insightResult = [
        createStubReadListInsightDto(),
        createStubReadListInsightDto(),
      ];
      const workspaceId = bigIntId();
      const userDetails = createStubUserDetailsWithWorkspaceAccess({});

      const mockFindInsightsForParent = jest.fn().mockResolvedValue({
        data: insightResult,
        pagination: { total: 2, perPage: 10, offset: 0 },
      });
      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockResolvedValue(true);

      const mockHydrateFn = jest.fn().mockResolvedValue(userDetails);

      const insightFolderService = await buildInsightFolderController({
        findInsightFn: mockFindInsightsForParent,
        hydrateUserRequestFn: mockHydrateFn,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const paginationOptions = { offset: 0, perPage: 10 };
      const parentFolderId = stringUUID();
      const sortOrder = SortOrder.DESC;
      const sortBy = InsightSortBy.DATE_CREATED;

      const result = await insightFolderService.findInsightsForParentFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        paginationOptions,
        parentFolderId,
        sortOrder,
        sortBy,
      );

      expect(mockFindInsightsForParent).toHaveBeenCalledWith(
        {
          userId: defaultRequest.userId,
          workspaceId: workspaceId,
          organizationId: organizationId,
          workspaceIdsUserHasAccess: userDetails.workspaceIdsUserHasAccess,
          sortBy: sortBy,
          sortOrder: sortOrder,
          parentFolderId: parentFolderId,
        },
        paginationOptions.offset,
        paginationOptions.perPage,
      );
      expect(result).toEqual({
        data: insightResult,
        pagination: { total: 2, perPage: 10, offset: 0 },
      });
      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        parentFolderId,
      );
    });
    it('when parent folder is not provided find insights at root', async () => {
      const insightResult = [
        createStubReadListInsightDto(),
        createStubReadListInsightDto(),
      ];
      const workspaceId = bigIntId();
      const userDetails = createStubUserDetailsWithWorkspaceAccess({});

      const mockFindInsightsForParent = jest.fn().mockResolvedValue({
        data: insightResult,
        pagination: { total: 2, perPage: 10, offset: 0 },
      });

      const mockHydrateFn = jest.fn().mockResolvedValue(userDetails);

      const insightFolderService = await buildInsightFolderController({
        findInsightFn: mockFindInsightsForParent,
        hydrateUserRequestFn: mockHydrateFn,
      });

      const organizationId = stringUUID();
      const paginationOptions = { offset: 0, perPage: 10 };
      const sortOrder = SortOrder.DESC;
      const sortBy = InsightSortBy.DATE_CREATED;

      const result = await insightFolderService.findInsightsForParentFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        paginationOptions,
        undefined,
        sortOrder,
        sortBy,
      );

      expect(mockFindInsightsForParent).toHaveBeenCalledWith(
        {
          userId: defaultRequest.userId,
          workspaceId: workspaceId,
          organizationId: organizationId,
          workspaceIdsUserHasAccess: userDetails.workspaceIdsUserHasAccess,
          sortBy: sortBy,
          sortOrder: sortOrder,
        },
        paginationOptions.offset,
        paginationOptions.perPage,
      );
      expect(result).toEqual({
        data: insightResult,
        pagination: { total: 2, perPage: 10, offset: 0 },
      });
    });
    it('throws an error when the provided workspace id is not the same as the parent folder id', async () => {
      const insightResult = [
        createStubReadListInsightDto(),
        createStubReadListInsightDto(),
      ];
      const providedWorkspaceId = bigIntId();
      const folderWorkspaceId = bigIntId();

      const userDetails = createStubUserDetailsWithWorkspaceAccess({});

      const mockActiveWorkspaceisFolderWorkspace = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));

      const mockHydrateFn = jest.fn().mockResolvedValue(userDetails);

      const insightFolderService = await buildInsightFolderController({
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceisFolderWorkspace,
        hydrateUserRequestFn: mockHydrateFn,
      });

      const organizationId = stringUUID();
      const paginationOptions = { offset: 0, perPage: 10 };
      const parentFolderId = stringUUID();
      const sortOrder = SortOrder.DESC;
      const sortBy = InsightSortBy.DATE_CREATED;

      await expect(
        insightFolderService.findInsightsForParentFolder(
          defaultRequest,
          organizationId,
          providedWorkspaceId,
          paginationOptions,
          parentFolderId,
          sortOrder,
          sortBy,
        ),
      ).rejects.toThrowError(`no permishies`);
    });
  });
  describe('moveInsights', () => {
    it('moves insights successfully', async () => {
      const workspaceId = bigIntId();

      const mockActiveWorkspaceIsFolderWorkspaceFn = jest
        .fn()
        .mockResolvedValue(true);

      const mockMoveInsight = jest.fn().mockResolvedValue({});
      const mockAccessToInsights = jest.fn().mockResolvedValue(true);
      const insightFolderService = await buildInsightFolderController({
        moveInsightFn: mockMoveInsight,
        accessToInsightsFn: mockAccessToInsights,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspaceFn,
      });

      const organizationId = stringUUID();
      const parentFolderId = stringUUID();
      const moveInsightRequestDto = {
        insightIds: [stringUUID(), stringUUID(), stringUUID()],
        parentFolderId,
      };

      const result = await insightFolderService.moveInsights(
        defaultRequest,
        organizationId,
        workspaceId,
        moveInsightRequestDto,
      );

      expect(mockAccessToInsights).toHaveBeenCalledWith(
        expect.objectContaining({ userId: defaultRequest.userId }),
        moveInsightRequestDto.insightIds,
        'move insights',
      );
      expect(mockMoveInsight).toHaveBeenCalledWith({
        insightIds: moveInsightRequestDto.insightIds,
        organizationId,
        workspaceId,
        destinationFolderId: moveInsightRequestDto.parentFolderId,
      });
      expect(mockActiveWorkspaceIsFolderWorkspaceFn).toHaveBeenCalledWith(
        workspaceId,
        parentFolderId,
      );
    });
    it('throws ForbiddenException if no access to insight', async () => {
      const mockMoveInsight = jest.fn();
      const mockAccessToInsights = jest
        .fn()
        .mockRejectedValue(new ForbiddenException());

      const insightFolderService = await buildInsightFolderController({
        moveInsightFn: mockMoveInsight,
        accessToInsightsFn: mockAccessToInsights,
      });

      const organizationId = stringUUID();
      const workspaceId = 1;
      const moveInsightRequestDto = {
        insightIds: [stringUUID()],
        parentFolderId: stringUUID(),
      };

      await expect(
        insightFolderService.moveInsights(
          defaultRequest,
          organizationId,
          workspaceId,
          moveInsightRequestDto,
        ),
      ).rejects.toThrow(ForbiddenException);

      expect(mockAccessToInsights).toHaveBeenCalledWith(
        expect.objectContaining({ userId: defaultRequest.userId }),
        moveInsightRequestDto.insightIds,
        'move insights',
      );
      expect(mockMoveInsight).not.toHaveBeenCalled();
    });
    it('throws forbidden exceptions if parent folder workspace is not provided workspace', async () => {
      const workspaceId = bigIntId();
      const folderWorkspaceId = bigIntId();
      const parentFolderId = stringUUID();

      const mockActiveWorkspaceIsFolderWorkspace = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));

      const mockMoveInsight = jest.fn().mockResolvedValue({});
      const mockAccessToInsights = jest.fn().mockResolvedValue(true);
      const insightFolderService = await buildInsightFolderController({
        moveInsightFn: mockMoveInsight,
        accessToInsightsFn: mockAccessToInsights,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspace,
      });

      const organizationId = stringUUID();
      const moveInsightRequestDto = {
        insightIds: [stringUUID(), stringUUID(), stringUUID()],
        parentFolderId,
      };

      await expect(
        insightFolderService.moveInsights(
          defaultRequest,
          organizationId,
          workspaceId,
          moveInsightRequestDto,
        ),
      ).rejects.toThrow(`no permishies`);

      expect(mockMoveInsight).not.toHaveBeenCalled();
    });
    it('allows optional parent folder id', async () => {
      const workspaceId = bigIntId();

      const mockMoveInsight = jest.fn().mockResolvedValue({});
      const mockAccessToInsights = jest.fn().mockResolvedValue(true);
      const insightFolderService = await buildInsightFolderController({
        moveInsightFn: mockMoveInsight,
        accessToInsightsFn: mockAccessToInsights,
      });

      const organizationId = stringUUID();
      const moveInsightRequestDto = {
        insightIds: [stringUUID(), stringUUID(), stringUUID()],
      };

      const result = await insightFolderService.moveInsights(
        defaultRequest,
        organizationId,
        workspaceId,
        moveInsightRequestDto,
      );

      expect(mockAccessToInsights).toHaveBeenCalledWith(
        expect.objectContaining({ userId: defaultRequest.userId }),
        moveInsightRequestDto.insightIds,
        'move insights',
      );
      expect(mockMoveInsight).toHaveBeenCalledWith({
        insightIds: moveInsightRequestDto.insightIds,
        organizationId,
        workspaceId,
      });
    });
  });
  describe('moveFolder', () => {
    it('moves folder successfully', async () => {
      const workspaceId = bigIntId();

      const mockActiveWorkspaceIsFolderWorkspace = jest
        .fn()
        .mockResolvedValue(true);

      const mockMoveFolder = jest.fn().mockResolvedValue({});
      const insightFolderService = await buildInsightFolderController({
        moveFolderFn: mockMoveFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspace,
      });

      const organizationId = stringUUID();
      const moveFolderRequestDto = {
        insightFolderIds: [stringUUID(), stringUUID(), stringUUID()],
        parentFolderId: stringUUID(),
      };

      const result = await insightFolderService.moveFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        moveFolderRequestDto,
      );

      expect(mockMoveFolder).toHaveBeenCalledWith({
        folderIds: moveFolderRequestDto.insightFolderIds,
        organizationId,
        workspaceId,
        destinationFolderId: moveFolderRequestDto.parentFolderId,
      });
      expect(mockActiveWorkspaceIsFolderWorkspace).toHaveBeenCalledWith(
        workspaceId,
        moveFolderRequestDto.parentFolderId,
      );
    });

    it('throws ForbiddenException provided workspace is not the same as parent folder workspace', async () => {
      const workspaceId = bigIntId();
      const folderWorkspaceId = bigIntId();
      const parentFolderId = stringUUID();

      const mockActiveWorkspaceIsFolderWorkspace = jest
        .fn()
        .mockRejectedValue(new Error('no permishies'));
      const mockMoveFolder = jest.fn();

      const insightFolderService = await buildInsightFolderController({
        moveFolderFn: mockMoveFolder,
        activeWorkspaceIsFolderWorkspaceFn:
          mockActiveWorkspaceIsFolderWorkspace,
      });

      const organizationId = stringUUID();
      const moveFolderRequestDto = {
        insightFolderIds: ['folder1'],
        parentFolderId,
      };

      await expect(
        insightFolderService.moveFolder(
          defaultRequest,
          organizationId,
          workspaceId,
          moveFolderRequestDto,
        ),
      ).rejects.toThrow(`no permishies`);
      expect(mockActiveWorkspaceIsFolderWorkspace).toHaveBeenCalledWith(
        workspaceId,
        parentFolderId,
      );
      expect(mockMoveFolder).not.toHaveBeenCalled();
    });

    it('allows optional parent folder id', async () => {
      const mockMoveFolder = jest.fn().mockResolvedValue({});
      const insightFolderService = await buildInsightFolderController({
        moveFolderFn: mockMoveFolder,
      });

      const workspaceId = bigIntId();
      const organizationId = stringUUID();
      const moveFolderRequestDto = {
        insightFolderIds: [stringUUID(), stringUUID(), stringUUID()],
      };

      const result = await insightFolderService.moveFolder(
        defaultRequest,
        organizationId,
        workspaceId,
        moveFolderRequestDto,
      );

      expect(mockMoveFolder).toHaveBeenCalledWith({
        folderIds: moveFolderRequestDto.insightFolderIds,
        organizationId,
        workspaceId,
      });
    });
  });
});
