import { PermissionDomain } from '../../../auth/enums/permission.domain.enum';
import {
  organizationFromParamsHandler,
  partnerFromParamsHandler,
} from '../../../auth/decorators/permission.decorator';
import { PermissionAction } from '../../../auth/enums/permission.action.enum';
import { PermissionSubResource } from '../../../auth/enums/permission.subresource.enum';

export const createInsightFolderWorkspace = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

export const createInsightFolderOrg = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.CREATE,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

// Update
export const updateInsightFolderWorkspace = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

export const updateInsightFolderOrg = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.UPDATE,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

// Read
export const readInsightFolderWorkspace = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

export const readInsightFolderOrg = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.READ,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

// delete
export const deleteInsightFolderWorkspace = {
  domain: PermissionDomain.WORKSPACE,
  domainContextHandler: partnerFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};

export const deleteInsightFolderOrg = {
  domain: PermissionDomain.ORGANIZATION,
  domainContextHandler: organizationFromParamsHandler,
  required: [
    {
      action: PermissionAction.DELETE,
      subresource: PermissionSubResource.INSIGHT_FOLDER,
    },
  ],
};
