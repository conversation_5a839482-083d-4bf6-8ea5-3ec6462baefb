import { Test, TestingModule } from '@nestjs/testing';
import { ForbiddenException } from '@nestjs/common';
import { InsightFolderPermissionService } from './insight-folder-permission.service';
import { InsightsFoldersService } from '@vidmob/vidmob-soa-analytics-service-sdk';

// Create a mock version of InsightsFoldersService
const mockInsightsFoldersService = {
  getFolderAsPromise: jest.fn(),
};

describe('InsightFolderPermissionService', () => {
  let service: InsightFolderPermissionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InsightFolderPermissionService,
        {
          provide: InsightsFoldersService,
          useValue: mockInsightsFoldersService,
        },
      ],
    }).compile();

    service = module.get<InsightFolderPermissionService>(
      InsightFolderPermissionService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('activeWorkspaceIsFolderWorkspace', () => {
    it('should throw ForbiddenException when workspaceId does not match folder workspaceId', async () => {
      // Arrange: mock the folder to have a different workspaceId
      const workspaceId = 1;
      const folderId = 'folder-id';
      const folderMock = {
        result: {
          workspaceId: 2, // Different from workspaceId
        },
      };
      mockInsightsFoldersService.getFolderAsPromise.mockResolvedValue(
        folderMock,
      );

      // Act & Assert
      await expect(
        service.activeWorkspaceIsFolderWorkspace(workspaceId, folderId),
      ).rejects.toThrowError(
        new ForbiddenException(
          `User not allowed to perform action on folder with id ${folderId} in workspace 2`,
        ),
      );
    });

    it('should return true when workspaceId matches folder workspaceId', async () => {
      // Arrange: mock the folder to have the same workspaceId
      const workspaceId = 1;
      const folderId = 'folder-id';
      const folderMock = {
        result: {
          workspaceId: workspaceId, // Same as workspaceId
        },
      };
      mockInsightsFoldersService.getFolderAsPromise.mockResolvedValue(
        folderMock,
      );

      // Act
      const result = await service.activeWorkspaceIsFolderWorkspace(
        workspaceId,
        folderId,
      );

      // Assert
      expect(result).toBe(true);
    });

    it('should handle when getFolderAsPromise fails', async () => {
      // Arrange: mock an error from the service
      const workspaceId = 1;
      const folderId = 'folder-id';
      mockInsightsFoldersService.getFolderAsPromise.mockRejectedValue(
        new Error('Error fetching folder'),
      );

      // Act & Assert
      await expect(
        service.activeWorkspaceIsFolderWorkspace(workspaceId, folderId),
      ).rejects.toThrowError('Error fetching folder');
    });
  });
});
