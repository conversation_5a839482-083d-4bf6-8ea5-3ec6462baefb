import {
  Body,
  Controller,
  Delete,
  Get,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Request,
  ValidationPipe,
} from '@nestjs/common';
import {
  InsightsFoldersService,
  UpdateInsightFolderRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import {
  createInsightFolderOrg,
  createInsightFolderWorkspace,
  deleteInsightFolderOrg,
  deleteInsightFolderWorkspace,
  readInsightFolderOrg,
  readInsightFolderWorkspace,
  updateInsightFolderOrg,
  updateInsightFolderWorkspace,
} from './insight-folder.permissions';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { CreateInsightFolderRequestDto } from '../dto/create-insight-folder-request.dto';
import { FavoriteInsightFolderRequestDto } from '../dto/favorite-insight-folder-request.dto';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { DEFAULT_PAGINATION_OPTIONS } from '../../constants/constants';
import { MoveInsightRequestDto } from '../dto/move-insight-request.dto';
import { MoveFolderRequestDto } from '../dto/move-folder-request.dto';
import { InsightPermissionService } from '../permission/insight-permission.service';
import { UserDetailsDto } from '../../dto/user-details.dto';
import { StrictSortOrderValidationPipe } from '../../../pagination-validators/sort-order-validation-pipe';
import { SortOrder } from '../../../reports/model/sort-order';
import { DynamicStrictSortByValidationPipe } from '../../../pagination-validators/sort-by-validation-pipe';
import { InsightSortBy } from '../constants/constants';
import { InsightFolderPermissionService } from './insight-folder-permission.service';
import { ListFilteredInsightFolderRequestDto } from '../dto/list-filtered-folders-request.dto';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Insight Folders')
@ApiSecurity('Bearer Token')
@Controller(
  'organization/:organizationId/workspace/:workspaceId/insight-folders',
)
export class InsightFolderController {
  private readonly logger = new Logger(InsightFolderController.name);

  constructor(
    private readonly insightFolderService: InsightsFoldersService,
    private readonly insightPermissionService: InsightPermissionService,
    private readonly insightFolderPermissionService: InsightFolderPermissionService,
  ) {}

  @Permissions([createInsightFolderWorkspace, createInsightFolderOrg])
  @Post()
  async createInsightFolder(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(ValidationPipe)
    createInsightFolderRequestDto: CreateInsightFolderRequestDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    return await this.insightFolderService.createInsightFolderAsPromise({
      ...createInsightFolderRequestDto,
      workspaceId: workspaceId,
      organizationId: organizationId,
      createdBy: userDetails.userId,
    });
  }

  @Patch(':folderId')
  @Permissions([updateInsightFolderWorkspace, updateInsightFolderOrg])
  async updateInsightFolder(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param('folderId') folderId: string,
    @Body(ValidationPipe)
    updateInsightFolderRequestDto: UpdateInsightFolderRequestDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
      workspaceId,
      folderId,
    );

    return await this.insightFolderService.updateInsightFolderAsPromise(
      folderId,
      updateInsightFolderRequestDto,
    );
  }

  @Delete(':folderId')
  @Permissions([deleteInsightFolderWorkspace, deleteInsightFolderOrg])
  async deleteInsightFolder(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param('folderId') folderId: string,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
      workspaceId,
      folderId,
    );
    return await this.insightFolderService.deleteInsightFolderAsPromise(
      folderId,
    );
  }

  @Post(':folderId/favorite')
  @Permissions([updateInsightFolderWorkspace, updateInsightFolderOrg])
  async favoriteInsightFolder(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Param('folderId') folderId: string,
    @Body() favoriteInsightFolderRequestDto: FavoriteInsightFolderRequestDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
      workspaceId,
      folderId,
    );
    return await this.insightFolderService.favoriteInsightFolderAsPromise(
      folderId,
      {
        ...favoriteInsightFolderRequestDto,
        userId: userDetails.userId,
      },
    );
  }

  @Get('/list')
  @Permissions([readInsightFolderWorkspace, readInsightFolderOrg])
  async findFoldersForParent(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('parentFolderId') parentFolderId?: string, // Optional query parameter
  ): Promise<PaginatedResultArray<any>> {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };
    return await this.insightFolderService.findFoldersForParentAsPromise(
      {
        userId: userDetails.userId,
        workspaceId: workspaceId,
        organizationId: organizationId,
        ...(parentFolderId && { parentFolderId }),
      },
      paginationOptions.offset ?? DEFAULT_PAGINATION_OPTIONS.offset,
      paginationOptions.perPage ?? DEFAULT_PAGINATION_OPTIONS.perPage,
    );
  }

  @Post('list-filtered')
  @Permissions([readInsightFolderWorkspace, readInsightFolderOrg])
  async findFoldersFiltered(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(ValidationPipe)
    filteredFolderRequestDto: ListFilteredInsightFolderRequestDto,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };
    return await this.insightFolderService.findFilteredFoldersAsPromise(
      {
        ...filteredFolderRequestDto,
        userId: userDetails.userId,
        workspaceId: workspaceId,
        organizationId: organizationId,
      },
      paginationOptions.offset ?? DEFAULT_PAGINATION_OPTIONS.offset,
      paginationOptions.perPage ?? DEFAULT_PAGINATION_OPTIONS.perPage,
    );
  }

  @Get('/hierarchy')
  @Permissions([readInsightFolderWorkspace, readInsightFolderOrg])
  async findFolderHierarchy(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };
    return await this.insightFolderService.getFolderHierarchyAsPromise(
      workspaceId,
      organizationId,
    );
  }

  @Get('/insights')
  @Permissions([readInsightFolderWorkspace, readInsightFolderOrg])
  async findInsightsForParentFolder(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @GetPagination() paginationOptions: PaginationOptions,
    @Query('parentFolderId') parentFolderId?: string,
    @Query('sortOrder', StrictSortOrderValidationPipe)
    sortOrder: SortOrder = SortOrder.DESC,
    @Query('sortBy', new DynamicStrictSortByValidationPipe(InsightSortBy))
    sortBy = InsightSortBy.DATE_CREATED,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId: organizationId,
    };

    const hydratedUserDetails =
      await this.insightPermissionService.hydrateUserDetailsWithWorkspaceAndAdAccount(
        userDetails,
      );

    if (parentFolderId) {
      await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
        workspaceId,
        parentFolderId,
      );
    }

    return await this.insightFolderService.findInsightsForParentAsPromise(
      {
        userId: userDetails.userId,
        workspaceId: workspaceId,
        organizationId: organizationId,
        workspaceIdsUserHasAccess:
          hydratedUserDetails.workspaceIdsUserHasAccess,
        sortBy: sortBy ?? InsightSortBy.DATE_CREATED,
        sortOrder: sortOrder ?? SortOrder.DESC,
        ...(parentFolderId ? { parentFolderId } : {}),
      },
      paginationOptions.offset ?? DEFAULT_PAGINATION_OPTIONS.offset,
      paginationOptions.perPage ?? DEFAULT_PAGINATION_OPTIONS.perPage,
    );
  }

  @Post('/move-insight')
  @Permissions([updateInsightFolderWorkspace, updateInsightFolderOrg])
  async moveInsights(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(ValidationPipe)
    moveInsightRequestDto: MoveInsightRequestDto,
  ) {
    const userDetails: UserDetailsDto = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId: organizationId,
    };

    await this.insightPermissionService.accessToInsights(
      userDetails,
      moveInsightRequestDto.insightIds ?? [],
      `move insights`,
    );

    if (moveInsightRequestDto.parentFolderId) {
      await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
        workspaceId,
        moveInsightRequestDto.parentFolderId,
      );
    }

    return await this.insightFolderService.moveInsightAsPromise({
      insightIds: moveInsightRequestDto.insightIds ?? [],
      organizationId: organizationId,
      workspaceId: workspaceId,
      ...(moveInsightRequestDto.parentFolderId && {
        destinationFolderId: moveInsightRequestDto.parentFolderId,
      }),
    });
  }

  @Post('/move-folder')
  @Permissions([updateInsightFolderWorkspace, updateInsightFolderOrg])
  async moveFolder(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Param('workspaceId', ParseIntPipe) workspaceId: number,
    @Body(ValidationPipe)
    moveFolderRequestDto: MoveFolderRequestDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
    };

    if (moveFolderRequestDto.parentFolderId) {
      await this.insightFolderPermissionService.activeWorkspaceIsFolderWorkspace(
        workspaceId,
        moveFolderRequestDto.parentFolderId,
      );
    }

    return await this.insightFolderService.moveFolderAsPromise({
      folderIds: moveFolderRequestDto.insightFolderIds ?? [],
      workspaceId: workspaceId,
      organizationId: organizationId,
      ...(moveFolderRequestDto.parentFolderId && {
        destinationFolderId: moveFolderRequestDto.parentFolderId,
      }),
    });
  }
}
