import { Injectable } from '@nestjs/common';
import { InsightsFiltersService as InsightFilterServiceSdk } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { MarketService as MarketServiceSdk } from '@vidmob/vidmob-organization-service-sdk';
import { BrandService as BrandServiceSdk } from '@vidmob/vidmob-organization-service-sdk';
import { PaginationOptions, Platform } from '@vidmob/vidmob-nestjs-common';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { UserDetailsDto } from '../../dto/user-details.dto';
import { InsightStatus, InsightType } from './constants';
import { OrganizationUserService as OrganizationUserServiceSdk } from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class InsightFilterService {
  constructor(
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly brandServiceSdk: BrandServiceSdk,
    private readonly insightFilterServiceSdk: InsightFilterServiceSdk,
    private readonly marketServiceSdk: MarketServiceSdk,
    private readonly organizationUserService: OrganizationUserServiceSdk,
  ) {}

  async getBrandsForInsightFilters(
    userDetails: UserDetailsDto,
    organizationId: string,
    paginationOptions?: PaginationOptions,
  ) {
    const workspaces =
      await this.analyticsUserService.fetchAllOrganizationWorkspacesForUser(
        userDetails,
      );

    return await this.brandServiceSdk.getBrandsByWorkspacesAsPromise(
      organizationId,
      { workspaceIds: workspaces.map(({ id }) => id) },
      paginationOptions?.offset,
      paginationOptions?.perPage,
    );
  }

  async getInsightCategories() {
    return await this.insightFilterServiceSdk.getCategoryOptionsAsPromise();
  }

  async getDimensionValueFilters(filterType: string, platforms?: string[]) {
    return await this.insightFilterServiceSdk.getDimensionFilterValuesAsPromise(
      filterType,
      platforms ?? [],
    );
  }

  async getOrganizationMarkets(
    userDetails: UserDetailsDto,
    organizationId: string,
    paginationOptions?: PaginationOptions,
  ) {
    const workspaces =
      await this.analyticsUserService.fetchAllOrganizationWorkspacesForUser(
        userDetails,
      );

    return await this.marketServiceSdk.getWorkspaceAdAccountMarketsAsPromise(
      organizationId,
      { workspaceIds: workspaces.map(({ id }) => id) },
      paginationOptions?.offset,
      paginationOptions?.perPage,
    );
  }

  async getKpiFilters(userDetails: UserDetailsDto, platforms?: Platform[]) {
    const { adAccounts } =
      await this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
        userDetails,
      );

    const userAdAccountIds = adAccounts.map(
      ({ platformAccountId }) => platformAccountId,
    );

    return await this.insightFilterServiceSdk.getAnalyticsKpisAsPromise({
      adAccountIds: userAdAccountIds,
      platforms: platforms,
    });
  }

  async getInsightStatuses() {
    return [
      { id: InsightStatus.DRAFT, name: 'Draft' },
      { id: InsightStatus.PUBLISHED, name: 'Published' },
    ];
  }

  async getInsightTypes(includeCopilotTypes?: boolean) {
    const types = [{ id: InsightType.BRAND, name: 'Brand' }];

    if (includeCopilotTypes) {
      types.push({ id: InsightType.COPILOT_BRAND, name: 'Brand via Maddie' });
      types.push({
        id: InsightType.COPILOT_INDUSTRY,
        name: 'Normative via Maddie',
      });
    }

    return types;
  }

  async getInsightUsers(
    userDetails: UserDetailsDto,
    organizationId: string,
    paginationOptions?: PaginationOptions,
  ) {
    const workspaces =
      await this.analyticsUserService.fetchAllOrganizationWorkspacesForUser(
        userDetails,
      );

    return await this.organizationUserService.getUsersByWorkspaceIdsAsPromise(
      organizationId,
      { workspaceIds: workspaces.map(({ id }) => id) },
      paginationOptions?.offset,
      paginationOptions?.perPage,
    );
  }
}
