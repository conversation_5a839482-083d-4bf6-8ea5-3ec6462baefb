import {
  Controller,
  Get,
  Param,
  ParseArrayPipe,
  Query,
  Request,
} from '@nestjs/common';
import { InsightFilterService } from './insight-filter.service';
import { Permissions } from '../../../auth/decorators/permission.decorator';
import { readOrganizationWorkspaceAdAccountReports } from '../../analytics.permissions';
import { ApiTags } from '@nestjs/swagger';
import {
  GetPagination,
  PaginationOptions,
  Platform,
} from '@vidmob/vidmob-nestjs-common';

@ApiTags('Insight Filters')
@Controller('insight-filter/organization/:organizationId')
export class InsightFilterController {
  constructor(private readonly insightFilterService: InsightFilterService) {}

  @Get('brand')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getInsightBrands(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const userDetails = { userId, organizationId, authorization };

    return await this.insightFilterService.getBrandsForInsightFilters(
      userDetails,
      organizationId,
      paginationOptions,
    );
  }

  @Get('category')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getInsightCategories() {
    return await this.insightFilterService.getInsightCategories();
  }

  @Get('campaign-objective')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getCampaignObjectives(
    @Query('platforms', new ParseArrayPipe({ items: String, optional: true }))
    platforms?: string[],
  ) {
    const mappedPlatforms = platforms?.map((platform) =>
      platform.toLowerCase(),
    );

    return await this.insightFilterService.getDimensionValueFilters(
      'CAMPAIGN_OBJECTIVE',
      mappedPlatforms,
    );
  }

  @Get('market')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getOrganizationMarkets(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const userDetails = { userId, organizationId, authorization };

    return await this.insightFilterService.getOrganizationMarkets(
      userDetails,
      organizationId,
      paginationOptions,
    );
  }

  @Get('kpi')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getKpiFilters(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Query('platforms', new ParseArrayPipe({ items: String, optional: true }))
    platforms?: Platform[],
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const userDetails = { userId, organizationId, authorization };

    return await this.insightFilterService.getKpiFilters(
      userDetails,
      platforms,
    );
  }

  @Get('status')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getInsightStatuses() {
    return await this.insightFilterService.getInsightStatuses();
  }

  @Get('type')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getInsightTypes(
    @Query('includeCopilotTypes') includeCopilotTypes?: boolean,
  ) {
    return await this.insightFilterService.getInsightTypes(includeCopilotTypes);
  }

  @Get('user')
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  async getInsightUsers(
    @Param('organizationId') organizationId: string,
    @Request() req: any,
    @GetPagination() paginationOptions: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;
    const userDetails = { userId, organizationId, authorization };

    return await this.insightFilterService.getInsightUsers(
      userDetails,
      organizationId,
      paginationOptions,
    );
  }
}
