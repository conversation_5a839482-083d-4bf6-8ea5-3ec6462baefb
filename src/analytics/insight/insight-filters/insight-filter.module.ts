import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { InsightFilterController } from './insight-filter.controller';
import { InsightFilterService } from './insight-filter.service';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { WorkspaceService } from '../../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../../auth/services/auth.service';

@Module({
  imports: [HttpModule, ConfigModule.forRoot()],
  providers: [
    InsightFilterService,
    AnalyticsUserService,
    WorkspaceService,
    AuthService,
  ],

  controllers: [InsightFilterController],
})
export class InsightFilterModule {}
