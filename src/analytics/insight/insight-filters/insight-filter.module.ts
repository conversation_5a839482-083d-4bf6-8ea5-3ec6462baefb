import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { InsightFilterController } from './insight-filter.controller';
import { InsightFilterService } from './insight-filter.service';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { AuthService } from '../../../auth/services/auth.service';
import { AccountManagementModule } from '../../../account-management/account-management.module';

@Module({
  imports: [HttpModule, AccountManagementModule, ConfigModule.forRoot()],
  providers: [InsightFilterService, AnalyticsUserService, AuthService],
  controllers: [InsightFilterController],
})
export class InsightFilterModule {}
