import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { API_ENDPOINTS } from '../../insight/insight.constants';
import {
  createInsightQuery,
  linkProjectInsightMutation,
} from '../../insight/insight.queries';
import { CopilotMongoDbRepository } from '../../brands-copilot/repository/copilot-mongo-db-repository.service';
import { UUID } from 'crypto';
import { INSIGHT_TYPE } from 'src/constants/insight.constants';
import { NormativeInsightsMongoDbRepository } from '../../analytics-copilot/repository/normative-insights-mongo-db-repository.service';
import { fetchCurrentUser } from 'src/studio/project-csv-download/utils/project-csv-download.utils';

/**
 * This "InsightsV2Service" replicates the ES-based approach of creating insights
 * via an external API call (e.g., GraphQL). Once the insight is created, you can
 * optionally sync brand or normative data in Mongo if needed.
 */
@Injectable()
export class InsightV2Service {
  private readonly logger = new Logger(InsightV2Service.name);
  private readonly serviceUrl: string;
  private readonly baseVidMobApiUrl: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly brandCopilotRepository: CopilotMongoDbRepository,
    private readonly normativeCopilotRepository: NormativeInsightsMongoDbRepository,
  ) {
    this.serviceUrl = this.configService.get<string>('serviceUrl') || 'local';
    this.baseVidMobApiUrl = this.configService.get<string>(
      'baseVidMobApiUrl',
      '',
    );
  }

  /**
   * Main entry point for creating multiple insights (similar to "createCopilotInsights" in ES-based).
   * 1) Iterates over each insight, calls an external API to create it if needed.
   * 2) If a projectId is provided, links the created insight to that project.
   * 3) If brand or normative logic is relevant (e.g., chatId/messageId),
   *    calls updateBrandInsightV2 or updateNormativeInsightV2.
   *
   * Returns an array of { id, insightLibraryId } objects for all insights.
   */
  async createCopilotInsightsV2(
    insights: any[],
    organizationId: string,
    workspaceIds: string[],
    token: string,
    projectId?: number,
    chatId?: UUID,
    messageId?: UUID,
  ): Promise<Array<{ id: string; insightLibraryId: string }>> {
    // Quick check to avoid unnecessary processing
    if (!insights || insights.length === 0) {
      return [];
    }

    // You may handle different insight types in one array;
    // for simplicity, we check the first item for a single type.
    const isNormativeInsight = insights[0].type === INSIGHT_TYPE.INDUSTRY;
    const isBrandInsight = insights[0].type === INSIGHT_TYPE.BRAND;

    // 1) Create the insights using the external API approach (similar to original ES code).
    const createdInsights = await this.createInsightsV2(
      insights,
      organizationId,
      workspaceIds,
      token,
      projectId,
    );

    const hasValidChatAndMessage = Boolean(chatId && messageId);

    // 2) If brand or normative logic applies, handle post-creation updates.
    //    We iterate over each created insight (could do per item-type checks).
    for (const created of createdInsights) {
      if (isNormativeInsight && hasValidChatAndMessage) {
        await this.updateNormativeInsightV2(
          organizationId,
          created.id,
          created.insightLibraryId,
        );
      } else if (isBrandInsight && hasValidChatAndMessage) {
        await this.updateBrandInsightV2(
          organizationId,
          chatId as UUID,
          messageId as UUID,
          created.id,
          created.insightLibraryId,
        );
      } else {
        this.logger.warn(
          `Skipping insight update for ID=${created.id} because chatId or messageId is missing.`,
        );
      }
    }

    return createdInsights;
  }

  /**
   * Equivalent to "createInsights" in the ES-based service. It:
   *  - Checks if an insight has an existing insightLibraryId
   *  - If not, calls the external "createInsight" query
   *  - Optionally links the new insight to a project
   */
  private async createInsightsV2(
    insights: any[],
    organizationId: string,
    workspaceIds: string[],
    token: string,
    projectId?: number,
  ): Promise<Array<{ id: string; insightLibraryId: string }>> {
    const results: Array<{ id: string; insightLibraryId: string }> = [];

    for (const insight of insights) {
      // Reuse an existing insightLibraryId if present
      if (insight.insightLibraryId) {
        results.push({
          id: insight.id,
          insightLibraryId: insight.insightLibraryId,
        });
      } else {
        // Otherwise, call the external "createInsight" GraphQL to get a new ID
        const request = this.getCreateInsightRequest(
          insight,
          organizationId,
          workspaceIds,
        );
        const response = await this.handlePost(
          API_ENDPOINTS.INSIGHTS_SERVICE,
          token,
          request,
        );
        const { createInsight } = response;

        if (createInsight?.result) {
          const insightLibraryId = createInsight.result.insightId;

          results.push({
            id: insight.id,
            insightLibraryId,
          });

          // Link the insight to the project if a projectId is provided
          if (projectId) {
            await this.linkInsightToProjectV2(
              insightLibraryId,
              organizationId,
              workspaceIds,
              projectId,
              token,
            );
          }
        } else {
          this.logger.error(
            `Unexpected response from createInsight API. Response: ${JSON.stringify(
              response,
            )}`,
          );
          throw new InternalServerErrorException(
            `Unexpected response from createInsight API. Please check the request and response payloads.`,
          );
        }
      }
    }

    return results;
  }

  /**
   * A helper to build the external "createInsight" GraphQL request.
   */
  private getCreateInsightRequest(
    insight: any,
    organizationId: string,
    workspaceIds: string[],
  ): any {
    const { id, ...rest } = insight;
    return {
      query: createInsightQuery,
      variables: {
        insightRequest: rest,
        organizationId,
        workspaceIds,
      },
    };
  }

  private async linkInsightToProjectV2(
    insightLibraryId: string,
    organizationId: string,
    workspaceIds: string[],
    projectId: number,
    token: string,
  ): Promise<void> {
    const request = {
      query: linkProjectInsightMutation,
      variables: {
        organizationId,
        workspaceIds,
        projectId,
        insightIds: [insightLibraryId],
        recommendedFrom: null,
      },
    };

    try {
      await this.handlePost(API_ENDPOINTS.PROJECT_INSIGHTS, token, request);
    } catch (error) {
      this.logger.error(
        `Error linking insight ${insightLibraryId} to project ${projectId}:`,
        error,
      );
      throw new InternalServerErrorException(
        `Failed to link insight to project ${projectId}`,
      );
    }
  }

  /**
   * Normative logic.
   * If the insight is normative, you might store additional data or
   * update a normative repository in Mongo.
   */
  private async updateNormativeInsightV2(
    organizationId: string,
    insightId: string,
    insightLibraryId: string,
  ): Promise<void> {
    try {
      await this.normativeCopilotRepository.updateNormativeInsight(
        organizationId,
        insightId,
        insightLibraryId,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.warn(
          `Normative insight ${insightId} not found for update with libraryId`,
        );
        return;
      }
      throw new InternalServerErrorException(
        `Failed to update normative insight (v2) for ${insightId}`,
      );
    }
  }

  /**
   * Brand logic.
   * If the insight is brand-based and has a conversation context (chatId, messageId),
   * we call "updateInsightWithLibraryId" on CopilotMongoDbRepository
   * to attach that libraryId to the conversation message's insight object.
   */
  private async updateBrandInsightV2(
    organizationId: string,
    chatId: UUID,
    messageId: UUID,
    insightId: string,
    insightLibraryId: string,
  ) {
    try {
      await this.brandCopilotRepository.updateInsightWithLibraryId(
        organizationId,
        chatId,
        messageId,
        insightId as unknown as UUID,
        insightLibraryId,
      );
    } catch (error) {
      // If conversation or message not found, we log a warning
      if (error instanceof NotFoundException) {
        this.logger.warn(
          `Conversation ${chatId} or message ${messageId} not found for brand insight ${insightId}`,
        );
        return;
      }
      throw new InternalServerErrorException(
        `Failed to update brand insight (v2) for insight ${insightId}`,
      );
    }
  }

  /**
   * The same handlePost logic as in the ES-based code, used for external GraphQL/REST calls.
   */
  private async handlePost(endPoint: string, token: string, request: any) {
    const url = `${this.serviceUrl}${endPoint}`;
    const headers = { Authorization: token };

    try {
      const response = await axios.post(url, request, { headers });
      const dataFromRequest = response.data;
      return dataFromRequest?.data || {};
    } catch (error) {
      this.logger.error(`Failed to make POST request in v2: ${error.message}`);
      throw new InternalServerErrorException(
        `Failed to make POST request in v2: ${error.message}`,
      );
    }
  }
}
