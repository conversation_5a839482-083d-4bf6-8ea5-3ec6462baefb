import { Controller, Post, Body, Request } from '@nestjs/common';
import { InsightV2Service } from './insight.service';
import { UUID } from 'crypto';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

// TODO this is getting replaces by copilot-insight controller remove with VID-10948
@ApiTags('Insight v2 (Replaced by /copilot-insight)')
@ApiSecurity('Bearer Token')
@Controller({
  path: 'insight',
  version: '2',
})
export class InsightV2Controller {
  constructor(private readonly insightService: InsightV2Service) {}

  @Post('create')
  async createInsightsV2(
    @Request() req: any,
    @Body()
    body: {
      insightRequest: any[];
      organizationId: string;
      workspaceIds: string[];
      projectId?: number;
      chatId?: UUID;
      messageId?: UUID;
    },
  ) {
    const authorization = req.headers.authorization;

    return this.insightService.createCopilotInsightsV2(
      body.insightRequest,
      body.organizationId,
      body.workspaceIds,
      authorization,
      body.projectId,
      body.chatId,
      body.messageId,
    );
  }
}
