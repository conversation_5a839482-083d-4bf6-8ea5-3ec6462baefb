export const errorText = `
  identifier
  message
  system
  type
`;

export const eventsText = `
  FAVORITED
  LINKED_TO_PROJECT
  UNLINKED_FROM_PROJECT
  VIEW
  VIEW_DETAILS
`;

export const fullInsightResultText = `
    adAccountIds
    audiences
    campaigns
    campaignsInfo {
        id
        name
    }
    chatId
    createdBy {
        id
        name
    }
    createdTime
    detail
    detailedInsightBuckets
    dimensionGroups
    dimensionValues
    endDate
    events {
        ${eventsText}
    }
    formats
    highLevelInsightBuckets
    industryId
    insightCreativeExamples {
        examplePlatformMediaIds
        exampleType
        tagType
        tagValue
    }
    insightCreativePlatformMediaIds
    insightId
    insightReportUrl
    kpis
    kpisInfo {
        id
        name
    }
    lastUpdatedTime
    mediaTypes
    normativeMetaData {
        accountCounts
        elements {
            elementAdCount
            elementAdVideoCount
            elementImpressions
            elementKpiValue
            elementPercentLift
            elementTagType
            elementValue
        }
        kpi {
            id
            name
        }
        level
        levelAdCount
        levelAdVideoCount
        levelImpressions
        levelKpiValue
        levelName
        time
    }
    objectives {
        id
        name
    }
    placements {
        id
        name
    }
    platform
    publishDate
    recommendation
    source
    savedAnalyticsReportId
    startDate
    status
    title
    type
`;

export const createInsightQuery = `
  mutation MyMutation ($insightRequest: CreateInsightRequest, $organizationId: String!, $workspaceIds: [Int]!) {
    createInsight(insightRequest: $insightRequest, organizationId: $organizationId, workspaceIds: $workspaceIds) {
        error {
            ${errorText}
        }
        message
        result {
            ${fullInsightResultText}
        }
        status
        traceId
    }
  }
`;

export const linkProjectInsightMutation = `
  mutation MyMutation(
    $organizationId: String!,
    $workspaceIds: [Int]!,
    $projectId: Int!,
    $insightIds: [String]!,
    $recommendedFrom: String
  ) {
    linkProjectInsight(
      organizationId: $organizationId,
      workspaceIds: $workspaceIds,
      projectId: $projectId,
      insightIds: $insightIds,
      recommendedFrom: $recommendedFrom
    ) {
      error {
        identifier
        message
        system
        type
      }
      message
      result {
        projectId
        processedInsightIds
        unprocessedInsightIds
      }
      status
      traceId
    }
  }
`;
