import { InsightService } from './insight.service';
import { ScopeFilterService } from '@vidmob/vidmob-organization-service-sdk';
import { Test, TestingModule } from '@nestjs/testing';
import {
  CreateInsightRequestDto,
  InsightsService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { INSIGHT_TYPE } from '../../constants/insight.constants';
import SourceEnum = CreateInsightRequestDto.SourceEnum;
import { ConfigService } from '@nestjs/config';
import { CopilotElasticSearchRepository } from '../brands-copilot/repository/copilot-elastic-search-repository.service';

describe('InsightService', () => {
  let service: InsightService;
  let scopeFilterService: ScopeFilterService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InsightService,
        {
          provide: ScopeFilterService,
          useValue: {
            getBrandsAndMarketsByAdAccountsAsPromise: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('mocked analytics service url'),
          },
        },
        {
          provide: CopilotElasticSearchRepository,
          useValue: {
            getInsightsById: jest.fn(),
            createInsight: jest.fn(),
            updateInsight: jest.fn(),
            deleteInsight: jest.fn(),
          },
        },
        {
          provide: InsightsService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<InsightService>(InsightService);
    scopeFilterService = module.get<ScopeFilterService>(ScopeFilterService);
  });

  describe('hydrateCreateInsightWithBrandsAndMarkets', () => {
    it('should hydrate brandIds and marketIds when missing and adAccountIds are present', async () => {
      const dto: CreateInsightRequestDto = {
        organizationId: '',
        platform: Platform.FACEBOOK,
        source: SourceEnum.Manual,
        status: {},
        title: '',
        type: INSIGHT_TYPE.BRAND,
        userId: 0,
        adAccountIds: ['acc1'],
        brandIds: [],
        marketIds: [],
      };

      const scopeFilterServiceSpy = jest.spyOn(
        scopeFilterService,
        'getBrandsAndMarketsByAdAccountsAsPromise',
      );
      scopeFilterServiceSpy.mockResolvedValueOnce({
        result: {
          brandIds: ['brand1', 'brand2'],
          marketIds: ['market1', 'market2'],
        },
      });

      await service.hydrateCreateInsightWithBrandsAndMarkets(dto);

      expect(dto.brandIds).toEqual(['brand1', 'brand2']);
      expect(dto.marketIds).toEqual(['market1', 'market2']);
    });

    it('should not override existing brandIds or marketIds', async () => {
      const dto: CreateInsightRequestDto = {
        organizationId: '',
        platform: Platform.FACEBOOK,
        source: SourceEnum.Manual,
        status: {},
        title: '',
        type: INSIGHT_TYPE.BRAND,
        userId: 0,
        adAccountIds: ['acc1'],
        brandIds: ['existingBrand'],
        marketIds: ['existingMarket'],
      };

      await service.hydrateCreateInsightWithBrandsAndMarkets(dto);

      expect(
        scopeFilterService.getBrandsAndMarketsByAdAccountsAsPromise,
      ).not.toHaveBeenCalled();
      expect(dto.brandIds).toEqual(['existingBrand']);
      expect(dto.marketIds).toEqual(['existingMarket']);
    });

    it('should populate only brands if markets exist', async () => {
      const dto: CreateInsightRequestDto = {
        organizationId: '',
        platform: Platform.FACEBOOK,
        source: SourceEnum.Manual,
        status: {},
        title: '',
        type: INSIGHT_TYPE.BRAND,
        userId: 0,
        adAccountIds: ['acc1'],
        brandIds: [],
        marketIds: ['existingMarket'],
      };

      const scopeFilterServiceSpy = jest.spyOn(
        scopeFilterService,
        'getBrandsAndMarketsByAdAccountsAsPromise',
      );
      scopeFilterServiceSpy.mockResolvedValueOnce({
        result: {
          brandIds: ['brand1', 'brand2'],
          marketIds: ['market1', 'market2'],
        },
      });

      await service.hydrateCreateInsightWithBrandsAndMarkets(dto);
      expect(dto.brandIds).toEqual(['brand1', 'brand2']);
      expect(dto.marketIds).toEqual(['existingMarket']);
    });

    it('should populate only markets if brands exist', async () => {
      const dto: CreateInsightRequestDto = {
        organizationId: '',
        platform: Platform.FACEBOOK,
        source: SourceEnum.Manual,
        status: {},
        title: '',
        type: INSIGHT_TYPE.BRAND,
        userId: 0,
        adAccountIds: ['acc1'],
        brandIds: ['existingBrand'],
        marketIds: [],
      };

      const scopeFilterServiceSpy = jest.spyOn(
        scopeFilterService,
        'getBrandsAndMarketsByAdAccountsAsPromise',
      );
      scopeFilterServiceSpy.mockResolvedValueOnce({
        result: {
          brandIds: ['brand1', 'brand2'],
          marketIds: ['market1', 'market2'],
        },
      });

      await service.hydrateCreateInsightWithBrandsAndMarkets(dto);
      expect(dto.brandIds).toEqual(['existingBrand']);
      expect(dto.marketIds).toEqual(['market1', 'market2']);
    });
  });
});
