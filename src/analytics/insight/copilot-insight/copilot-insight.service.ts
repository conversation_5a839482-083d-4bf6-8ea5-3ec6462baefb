import {
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { Client } from '@elastic/elasticsearch';
import {
  CreateInsightRequestDto,
  InsightsService as InsightServiceSdk,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { INSIGHT_TYPE } from 'src/constants/insight.constants';
import { UUID } from 'crypto';
import { CopilotElasticSearchRepository } from '../../brands-copilot/repository/copilot-elastic-search-repository.service';
import { CreateCopilotInsightRequestDto } from '../dto/create-copilot-insight-request.dto';
import {
  ELASTIC_SEARCH_API_KEY,
  ELASTIC_SEARCH_NODE,
  INSIGHTS_DB_INDEX,
} from '../../analytics-copilot/constants/analytics-copilot.constants';
import { UserDetailsDto } from '../../dto/user-details.dto';
import { CopilotInsightRequestDto } from '../dto/copilot-insight-request.dto';
import { CopilotMongoDbRepository } from '../../brands-copilot/repository/copilot-mongo-db-repository.service';
import { NormativeInsightsMongoDbRepository } from '../../analytics-copilot/repository/normative-insights-mongo-db-repository.service';
import { InsightPermissionService } from '../permission/insight-permission.service';
import { CreateCopilotInsightResponseDto } from '../dto/create-copilot-insight-response.dto';
import { InsightService } from '../insight.service';

@Injectable()
export class CopilotInsightService {
  private readonly serviceUrl: string;
  private readonly token: string;
  private esClient: Client;
  private readonly logger = new Logger(CopilotInsightService.name);

  constructor(
    private readonly insightServiceSdk: InsightServiceSdk,
    private readonly copilotElasticRepository: CopilotElasticSearchRepository,
    private readonly brandCopilotMongoRepository: CopilotMongoDbRepository,
    private readonly normativeCopilotMongoRepository: NormativeInsightsMongoDbRepository,
    private readonly insightPermissionsService: InsightPermissionService,
    private readonly insightService: InsightService,
  ) {
    this.esClient = new Client({
      node: ELASTIC_SEARCH_NODE,
      auth: {
        apiKey: ELASTIC_SEARCH_API_KEY,
      },
    });
  }

  async createBrandInsightAndLinkToCopilotEs(
    createCopilotInsightRequest: CreateCopilotInsightRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<CreateCopilotInsightResponseDto[]> {
    return this.createInsightAndLinkToCopilot(
      createCopilotInsightRequest,
      userDetails,
      this.insightPermissionsService.validateAndHydrateBrandInsightCreation.bind(
        this.insightPermissionsService,
      ),
      this.updateESBrandInsight.bind(this),
      this.updateESNormativeInsight.bind(this),
      'Create Brand Insight',
    );
  }

  async createBrandInsightAndLinkToCopilotMongo(
    createCopilotInsightRequest: CreateCopilotInsightRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<CreateCopilotInsightResponseDto[]> {
    return this.createInsightAndLinkToCopilot(
      createCopilotInsightRequest,
      userDetails,
      this.insightPermissionsService.validateAndHydrateBrandInsightCreation.bind(
        this.insightPermissionsService,
      ),
      this.updateMongoBrandInsight.bind(this),
      this.updateMongoNormativeInsight.bind(this),
      'Create Brand Insight',
    );
  }

  async createIndustryInsightAndLinkToCopilotEs(
    createCopilotInsightRequest: CreateCopilotInsightRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<CreateCopilotInsightResponseDto[]> {
    return this.createInsightAndLinkToCopilot(
      createCopilotInsightRequest,
      userDetails,
      this.insightPermissionsService.validateAndHydrateIndustryInsight.bind(
        this.insightPermissionsService,
      ),
      this.updateESBrandInsight.bind(this),
      this.updateESNormativeInsight.bind(this),
      'Create Industry Insight',
    );
  }

  async createIndustryInsightAndLinkToCopilotMongo(
    createCopilotInsightRequest: CreateCopilotInsightRequestDto,
    userDetails: UserDetailsDto,
  ): Promise<CreateCopilotInsightResponseDto[]> {
    return this.createInsightAndLinkToCopilot(
      createCopilotInsightRequest,
      userDetails,
      this.insightPermissionsService.validateAndHydrateIndustryInsight.bind(
        this.insightPermissionsService,
      ),
      this.updateMongoBrandInsight.bind(this),
      this.updateMongoNormativeInsight.bind(this),
      'Create Industry Insight',
    );
  }

  private async createInsightAndLinkToCopilot(
    createCopilotInsightRequest: CreateCopilotInsightRequestDto,
    userDetails: UserDetailsDto,
    validateAndHydrateInsight: (
      actionMessage: string,
      insight: CreateInsightRequestDto | CopilotInsightRequestDto,
      userDetails: UserDetailsDto,
      organizationId: string,
      workspaceIds?: number[],
    ) => Promise<CreateInsightRequestDto>,
    updateBrandInsight: (
      organizationId: string,
      chatId: string,
      messageId: string,
      insightId: string,
      libraryInsightId: string,
    ) => Promise<void>,
    updateIndustryInsight: (
      organizationId: string,
      insightId: string,
      libraryInsightId: string,
    ) => Promise<void>,
    actionMessage: string,
  ): Promise<CreateCopilotInsightResponseDto[]> {
    const createdInsights = await Promise.all(
      createCopilotInsightRequest.insightRequest.map(async (insight) => {
        const validatedAndHydratedInsightRequest =
          await validateAndHydrateInsight(
            actionMessage,
            await this.hydrateInsightWithCopilotDetails(
              insight,
              createCopilotInsightRequest,
            ),
            userDetails,
            createCopilotInsightRequest.organizationId,
          );
        const libraryInsight: any = await this.createInsightLibraryInsight(
          validatedAndHydratedInsightRequest,
        );

        if (insight.type === INSIGHT_TYPE.INDUSTRY) {
          await updateIndustryInsight(
            createCopilotInsightRequest.organizationId,
            insight.id,
            libraryInsight.result.id,
          );
        } else if (
          insight.type === INSIGHT_TYPE.BRAND &&
          createCopilotInsightRequest.chatId &&
          createCopilotInsightRequest.messageId
        ) {
          await updateBrandInsight(
            createCopilotInsightRequest.organizationId,
            createCopilotInsightRequest.chatId,
            createCopilotInsightRequest.messageId,
            insight.id,
            libraryInsight.result.id,
          );
        }

        return {
          id: insight.id,
          insightLibraryId: libraryInsight.result.id,
        };
      }),
    );
    return createdInsights;
  }

  private async hydrateInsightWithCopilotDetails(
    insight: CopilotInsightRequestDto,
    createRequest: CreateCopilotInsightRequestDto,
  ): Promise<CopilotInsightRequestDto> {
    const hydratedInsight = {
      ...insight,
      type:
        insight.type === INSIGHT_TYPE.INDUSTRY
          ? INSIGHT_TYPE.COPILOT_INDUSTRY
          : insight.type === INSIGHT_TYPE.BRAND
          ? INSIGHT_TYPE.COPILOT_BRAND
          : insight.type,
      copilotChatId: createRequest.chatId,
    };

    if (hydratedInsight.type === INSIGHT_TYPE.COPILOT_BRAND) {
      await this.insightService.hydrateCreateInsightWithBrandsAndMarkets(
        hydratedInsight,
      );
    }

    if (createRequest.workspaceIds && createRequest.workspaceIds.length > 0) {
      hydratedInsight.workspaceIds = createRequest.workspaceIds;
    }

    return hydratedInsight;
  }

  private async createInsightLibraryInsight(insight: CreateInsightRequestDto) {
    return await this.insightServiceSdk.createInsightAsPromise(insight);
  }

  private async updateESNormativeInsight(
    organizationId: string, // unused but here to match contract with mongo
    insightId: string,
    insightLibraryId: string,
  ) {
    try {
      await this.esClient.update({
        index: INSIGHTS_DB_INDEX,
        id: insightId,
        body: {
          doc: {
            insightLibraryId,
          },
        },
      });
    } catch (error) {
      throw new InternalServerErrorException(
        `Failed to update insight ${insightId}`,
      );
    }
  }

  private async updateESBrandInsight(
    organizationId: string, // unused, here to match mongo contract
    chatId: UUID,
    messageId: UUID,
    insightId: string,
    insightLibraryId: UUID,
  ) {
    try {
      await this.copilotElasticRepository.updateInsightWithLibraryId(
        chatId,
        messageId,
        insightId as UUID,
        insightLibraryId,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        // the main action of this function is to create the insight, so if the conversation, message or insight is not found, we log an error but do not propagate it
        this.logger.error(
          `Conversation ${chatId}, message ${messageId} or insight ${insightId} not found`,
        );
        return;
      }

      // For other errors, throw an internal server error
      throw new InternalServerErrorException(
        `Failed to update insight ${insightId} with library id ${insightLibraryId}`,
      );
    }
  }

  /**
   * Brand logic.
   * If the insight is brand-based and has a conversation context (chatId, messageId),
   * we call "updateInsightWithLibraryId" on CopilotMongoDbRepository
   * to attach that libraryId to the conversation message's insight object.
   */
  private async updateMongoBrandInsight(
    organizationId: string,
    chatId: UUID,
    messageId: UUID,
    insightId: string,
    insightLibraryId: string,
  ) {
    try {
      await this.brandCopilotMongoRepository.updateInsightWithLibraryId(
        organizationId,
        chatId,
        messageId,
        insightId as unknown as UUID,
        insightLibraryId,
      );
    } catch (error) {
      // If conversation or message not found, we log a warning
      if (error instanceof NotFoundException) {
        this.logger.warn(
          `Conversation ${chatId} or message ${messageId} not found for brand insight ${insightId}`,
        );
        return;
      }
      throw new InternalServerErrorException(
        `Failed to update brand insight (v2) for insight ${insightId}`,
      );
    }
  }

  /**
   * Normative logic.
   * If the insight is normative, you might store additional data or
   * update a normative repository in Mongo.
   */
  private async updateMongoNormativeInsight(
    organizationId: string,
    insightId: string,
    insightLibraryId: string,
  ): Promise<void> {
    try {
      await this.normativeCopilotMongoRepository.updateNormativeInsight(
        organizationId,
        insightId,
        insightLibraryId,
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        this.logger.warn(
          `Normative insight ${insightId} not found for update with libraryId`,
        );
        return;
      }
      throw new InternalServerErrorException(
        `Failed to update normative insight (v2) for ${insightId}`,
      );
    }
  }
}
