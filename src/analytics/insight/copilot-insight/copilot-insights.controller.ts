import {
  Controller,
  Post,
  Body,
  Request,
  ValidationPipe,
  Param,
  Version,
  BadRequestException,
} from '@nestjs/common';
import { CopilotInsightService } from './copilot-insight.service';
import { CreateCopilotInsightRequestDto } from '../dto/create-copilot-insight-request.dto';
import { INSIGHT_TYPE } from '../../../constants/insight.constants';
import { ApiSecurity, ApiTags } from '@nestjs/swagger';

@ApiTags('Copilot Insight')
@ApiSecurity('Bearer Token')
@Controller('/organization/:organizationId/copilot-insight')
export class CopilotInsightsController {
  constructor(private readonly copilotInsightService: CopilotInsightService) {}

  @Post()
  async createCopilotInsightsLinkedToElastic(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe)
    createCopilotInsightRequestDto: CreateCopilotInsightRequestDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId: organizationId,
    };

    if (
      createCopilotInsightRequestDto.insightRequest[0].type ===
      INSIGHT_TYPE.INDUSTRY
    ) {
      return await this.copilotInsightService.createIndustryInsightAndLinkToCopilotEs(
        createCopilotInsightRequestDto,
        userDetails,
      );
    } else if (
      createCopilotInsightRequestDto.insightRequest[0].type ===
      INSIGHT_TYPE.BRAND
    ) {
      return await this.copilotInsightService.createBrandInsightAndLinkToCopilotEs(
        createCopilotInsightRequestDto,
        userDetails,
      );
    } else {
      throw new BadRequestException(
        `Unrecognized insight type ${createCopilotInsightRequestDto.insightRequest[0].type}`,
      );
    }
  }

  @Version('2')
  @Post()
  async createCopilotInsightsLinkedToMongo(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(ValidationPipe)
    createCopilotInsightRequestDto: CreateCopilotInsightRequestDto,
  ) {
    const userDetails = {
      userId: req.userId,
      authorization: req.headers.authorization,
      organizationId: organizationId,
    };

    if (
      createCopilotInsightRequestDto.insightRequest[0].type ===
      INSIGHT_TYPE.INDUSTRY
    ) {
      return await this.copilotInsightService.createIndustryInsightAndLinkToCopilotMongo(
        createCopilotInsightRequestDto,
        userDetails,
      );
    } else if (
      createCopilotInsightRequestDto.insightRequest[0].type ===
      INSIGHT_TYPE.BRAND
    ) {
      return await this.copilotInsightService.createBrandInsightAndLinkToCopilotMongo(
        createCopilotInsightRequestDto,
        userDetails,
      );
    } else {
      throw new BadRequestException(
        `Unrecognized insight type ${createCopilotInsightRequestDto.insightRequest[0].type}`,
      );
    }
  }
}
