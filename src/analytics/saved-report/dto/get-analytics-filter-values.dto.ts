import {
  <PERSON><PERSON><PERSON><PERSON>,
  Is<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>Not<PERSON>mpty,
  <PERSON><PERSON><PERSON>ber,
  Is<PERSON><PERSON>al,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Platform } from '../../../constants/platform.constants';
import { DateRange } from '../../dto/date-range.dto';
import { ApiProperty } from '@nestjs/swagger';
import { MediaType } from './create-analytics-report.dto';
import { AdvancedFilterEnum } from '@vidmob/vidmob-nestjs-common/dist/analytics-queries/types/request-response-types';

export enum FilterType {
  CAMPAIGN_IDENTIFIER = 'campaignIdentifier',
  CAMPAIGN_OBJECTIVE = 'campaignObjective',
  AD_SET_IDENTIFIER = 'adSetIdentifier',
  AD_IDENTIFIER = 'adIdentifier',
  AD_PLACEMENT = 'adPlacement',
  AD_TYPE = 'adType',
  KPI = 'kpi',
  CRITERIA = 'criteria',
}

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore // only for advanced filters that require request to get options
export const ADVANCED_FILTER_TYPE_TO_REQUEST_FILTER_TYPE_MAP: Record<
  AdvancedFilterEnum,
  FilterType
> = {
  [AdvancedFilterEnum.CAMPAIGN_IDENTIFIER]: FilterType.CAMPAIGN_IDENTIFIER,
  [AdvancedFilterEnum.CAMPAIGN_OBJECTIVE]: FilterType.CAMPAIGN_OBJECTIVE,
  [AdvancedFilterEnum.ADSET_IDENTIFIER]: FilterType.AD_SET_IDENTIFIER,
  [AdvancedFilterEnum.AD_IDENTIFIER]: FilterType.AD_IDENTIFIER,
  [AdvancedFilterEnum.AD_PLACEMENT]: FilterType.AD_PLACEMENT,
  [AdvancedFilterEnum.AD_TYPE]: FilterType.AD_TYPE,
};

export class GetAnalyticsReportFilterOptionsRequestDto {
  /**
   * The platform to get filter options for
   */
  @IsEnum(Platform)
  @IsNotEmpty()
  @Transform(({ value }) => value.toLowerCase())
  platform: Platform;

  /**
   * Type of filter to fetch filter options for
   */
  @IsEnum(FilterType)
  @IsNotEmpty()
  filterType: FilterType;

  /**
   * The workspaces to get filter options for
   */
  @IsNumber({}, { each: true })
  @IsOptional()
  workspaceIds?: number[];

  /**
   * The platform ad account ids to get filter options for
   * @example ['****************', 's9sq99e80io39i82']
   */
  @IsOptional()
  @IsString({ each: true })
  adAccountIds?: string[];

  /**
   * The date range to get filter options for
   * @example { startDate: '2021-01-01', endDate: '2021-01-31' }
   */
  @ValidateNested()
  @Type(() => DateRange)
  @IsOptional()
  dateRange?: DateRange;

  /**
   * The media types to get filter options for
   * @example ['video', 'image']
   */
  @IsArray()
  @Transform(({ value }) => value.map((val: string) => val.toLowerCase()))
  @IsEnum(MediaType, {
    each: true,
    always: true,
    message:
      'mediaTypes must be one of: ' + Object.values(MediaType).join(', '),
  })
  @IsNotEmpty()
  @IsOptional()
  mediaTypes?: MediaType[];

  /**
   * String to search for in filter options. Case-insensitive search.
   * @example '_campaign_'
   */
  @IsString()
  @IsOptional()
  searchString?: string;

  /**
   * whether to include standard criteria in the results. Only applies to criteria filter. Defaults to false
   * @example true
   */
  @IsOptional()
  @IsBoolean()
  shouldIncludeStandardCriteria?: boolean;

  /**
   * Whether to include spend kpis for all platforms. Defaults to false
   */
  shouldIncludeAllSpendKpis?: boolean;
}

export class FilterItem {
  @ApiProperty({
    description: 'The id of the filter object',
    example: 720,
    type: String || Number,
  })
  @IsString()
  @IsNumber()
  @IsNotEmpty()
  id: string | number;

  @ApiProperty({
    description: 'The name of the filter object',
    example: 'Awareness',
    type: String,
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description:
      'The nested values for this filter object. eg. campaign objectives have nested values',
    example: [
      { id: 854, name: 'REACH' },
      { id: 855, name: 'RF_REACH' },
    ],
    isArray: true,
    type: FilterItem,
  })
  @IsOptional()
  @ValidateNested()
  values?: FilterItem[];
}
