import 'reflect-metadata';
import { Sort } from '../../../reports/model/sort';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  ValidateNested,
  ArrayNotEmpty,
} from 'class-validator';
import { AnalyticsReportType } from '../model/analytics-report';
import { Transform, Type } from 'class-transformer';
import { Platform } from '../../../constants/platform.constants';
import { DateRange } from '../../dto/date-range.dto';
import { CurrencyDto } from '../../currency/dto/currency.dto';

export enum MediaType {
  VIDEO = 'video',
  IMAGE = 'image',
}

export enum StatCompareAgainst {
  VERTICAL = 'vertical',
  HORIZONTAL = 'horizontal',
}

export enum StatConfidence {
  '95_PERCENT' = 95,
  '90_PERCENT' = 90,
  '85_PERCENT' = 85,
}

export enum StatBaseComparison {
  IMPRESSIONS = 'impressions',
  FORMATTED_VALUE = 'formattedValue',
  STAT_LIFT_AGAINST = 'statLiftAgainst',
  AD_VIDEO_COUNT = 'adVideoCount',
}

export enum StatAverage {
  ELEMENT = 'element',
  KPI = 'kpi',
}

export enum StatMinimumTagConfidenceLevel {
  LOW = 65,
  MEDIUM = 75,
  HIGH = 85,
  VERY_HIGH = 95,
}

export enum GroupByColumns {
  AD_GROUP_TYPE_DETAILED = 'ad_group_type_detailed',
  AD_TYPE_PLACEMENT = 'ad_type_placement',
  AUDIENCE = 'audience',
  CAMPAIGN = 'campaign',
  CREATIVE = 'creative',
  CREATIVE_TYPE = 'creative_type',
  DEVICE_PLACEMENT = 'device_placement',
  DURATION = 'duration',
  ELEMENT_PRESENCE = 'element_presence',
  HIGH_LEVEL_OBJECTIVES = 'high_level_objectives',
  LOW_LEVEL_OBJECTIVES = 'detailed_objectives',
  NORMAL_AD_TYPE = 'normal_ad_type',
  PLACEMENT = 'placement',
  PLATFORM = 'platform',
  PLATFORM_PLACEMENT = 'platform_placement',
  RATIO_FORMAT = 'ratio_format',
  VIDMOB_VS_NON_VIDMOB = 'vidmob_vs_non_vidmob',
  MEDIA_TYPE = 'media_type',
  MARKET = 'market',
  BRAND = 'brand',
  KPI = 'kpi',
  NONE = 'none',
}

export enum GroupByRows {
  KPI = 'kpi',
  ELEMENT = 'element',
  CRITERIA = 'criteria',
  NONE = 'none',
  UNGROUPED = 'ungrouped',
  CRITERIA_GROUP = 'criteriaGroup',
}

export class ImpressionsRange {
  @IsOptional()
  @IsNumber()
  minimum?: number;

  @IsOptional()
  @IsNumber()
  maximum?: number;
}

export class AnalyticsReportGroupByDto {
  @IsString()
  @IsNotEmpty()
  @IsEnum(GroupByColumns, {
    always: true,
    message:
      'groupBy columns must be one of: ' +
      Object.values(GroupByColumns).join(', '),
  })
  columns: GroupByColumns;

  @IsString()
  @IsNotEmpty()
  @IsEnum(GroupByRows, {
    always: true,
    message:
      'groupBy rows must be one of: ' + Object.values(GroupByRows).join(', '),
  })
  rows: GroupByRows;
}

export class StatSettings {
  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(StatCompareAgainst, {
    always: true,
    message:
      'statSettings direction must be one of: ' +
      Object.values(StatCompareAgainst).join(', '),
  })
  direction: StatCompareAgainst;

  @IsNumber()
  @IsNotEmpty()
  @IsEnum(StatConfidence, {
    always: true,
    message: `statSettings confidence must be one of: ${Object.values(
      StatConfidence,
    ).join(', ')}`,
  })
  statConfidence: StatConfidence;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(StatAverage, {
    always: true,
    message:
      'statSettings average must be one of: ' +
      Object.values(StatAverage).join(', '),
  })
  average: StatAverage;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(StatBaseComparison, {
    always: true,
    message:
      'statSettings baseComparison must be one of: ' +
      Object.values(StatBaseComparison).join(', '),
  })
  baseComparison: StatBaseComparison;

  @IsNumber()
  @IsOptional()
  @IsEnum(StatMinimumTagConfidenceLevel, {
    always: true,
    message:
      'statSetting minimumTagConfidenceLevel must be one of: ' +
      Object.values(StatMinimumTagConfidenceLevel).join(', '),
  })
  minimumTagConfidenceLevel: StatMinimumTagConfidenceLevel;

  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  isDefaultKPITimeRangeActive?: boolean;
}

export class NormsConfigurationDateRange {
  @IsString()
  @IsNotEmpty()
  scope: string;

  @IsString()
  @IsNotEmpty()
  scopeKey: string;

  @IsString()
  @IsNotEmpty()
  startDate: string;

  @IsString()
  @IsNotEmpty()
  endDate: string;
}

export class NormsConfigurationIndustries {
  @IsOptional()
  @IsNumber({}, { each: true })
  industryIds: number[];

  @IsOptional()
  @IsNumber({}, { each: true })
  subIndustryIds: number[];
}

export class NormsConfigurationMarkets {
  @IsOptional()
  @IsNumber({}, { each: true })
  regionIds: number[];

  @IsOptional()
  @IsString({ each: true })
  countries: string[];
}

export class NormsConfiguration {
  @ValidateNested()
  @Type(() => NormsConfigurationDateRange)
  dateRange: NormsConfigurationDateRange;

  @ValidateNested()
  @Type(() => NormsConfigurationIndustries)
  industries: NormsConfigurationIndustries;

  @IsOptional()
  @ValidateNested()
  @Type(() => NormsConfigurationMarkets)
  markets?: NormsConfigurationMarkets;

  @IsOptional()
  @ArrayNotEmpty()
  @IsNumber({}, { each: true })
  objectiveGroupIds?: number[];
}

export class BaseAnalyticsReportFilterMetadataDto {
  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(Platform, {
    always: true,
    message: 'platform must be one of: ' + Object.values(Platform).join(', '),
  })
  platform: Platform;

  @ValidateNested()
  @Type(() => DateRange)
  @IsNotEmpty()
  dateRange: DateRange;

  @IsString()
  @IsOptional()
  kpiId?: string;

  @IsString()
  @IsOptional()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(GroupByColumns, {
    always: true,
    message:
      'viewBy must be one of: ' + Object.values(GroupByColumns).join(', '),
  })
  viewBy?: GroupByColumns;

  @IsArray()
  @Transform(({ value }) => value.map((val: string) => val.toLowerCase()))
  @IsEnum(MediaType, {
    each: true,
    always: true,
    message:
      'mediaTypes must be one of: ' + Object.values(MediaType).join(', '),
  })
  @IsNotEmpty()
  mediaTypes: MediaType[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  adTypes?: string[];

  @IsOptional()
  columnSelections?: Record<string, any>;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  campaignIds?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  campaignSearchStrings?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  adsetIds?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  adsetSearchStrings?: string[];

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  adIds?: string[];

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  adSearchString?: string[];

  @ValidateNested()
  @IsOptional()
  @Type(() => ImpressionsRange)
  creativeImpressionsRange?: ImpressionsRange;

  @ValidateNested()
  @IsOptional()
  @Type(() => ImpressionsRange)
  adImpressionsRange?: ImpressionsRange;

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  objectives?: string[];

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  placements?: string[];

  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  criteriaGroupIds?: string[];

  @IsArray()
  @IsOptional()
  selectedRows?: string[][];

  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  isDefaultKPITimeRangeFilterActive?: boolean; // deprecated, but how it is on saved reports before 6/27/2024 (now it lives under statSettings.isDefaultKPITimeRangeActive)

  @IsBoolean()
  @IsOptional()
  createdByVidmob?: boolean;

  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  isShowAppAdsOnlyActive?: boolean;

  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  isShowSparkAdsOnlyActive?: boolean;

  @ValidateNested()
  @Type(() => StatSettings)
  @IsNotEmpty()
  @IsOptional()
  statSettings?: StatSettings;

  @ValidateNested()
  @Type(() => NormsConfiguration)
  @IsOptional()
  normsConfiguration?: NormsConfiguration;

  @ValidateNested()
  @Type(() => CurrencyDto)
  @IsOptional()
  currency?: CurrencyDto;
}

export class CreateAnalyticsReportFilterMetadataDto extends BaseAnalyticsReportFilterMetadataDto {
  @IsArray()
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  @ArrayNotEmpty()
  workspaceIds: number[];

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  @ArrayNotEmpty()
  adAccountIds: string[];
}

export class CreateCreativeLeaderboardReportFilterMetadataDto {
  @IsArray()
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  @ArrayNotEmpty()
  workspaceIds: number[];

  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(Platform, {
    always: true,
    message: 'platform must be one of: ' + Object.values(Platform).join(', '),
  })
  platform: Platform;

  @ValidateNested()
  @Type(() => DateRange)
  @IsNotEmpty()
  dateRange: DateRange;

  @IsString()
  @IsNotEmpty()
  kpiId: string;

  @IsNumber()
  @IsNotEmpty()
  limit: number;

  @IsNumber()
  @IsNotEmpty()
  limitPerAdAccount: number;

  @IsNumber()
  @IsNotEmpty()
  minimumDaysLive: number;

  @IsNumber()
  @IsNotEmpty()
  minimumImpressionsPerCreative: number;
}

export class BaseAnalyticsReportDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.toUpperCase())
  @IsEnum(AnalyticsReportType, {
    always: true,
    message:
      'reportType must be one of: ' +
      Object.values(AnalyticsReportType).join(', '),
  })
  reportType: AnalyticsReportType;

  @ValidateNested()
  @Type(() => Sort)
  @IsNotEmpty()
  @IsOptional()
  sortBy?: Sort;

  @ValidateNested()
  @Type(() => AnalyticsReportGroupByDto)
  @IsNotEmpty()
  @IsOptional()
  groupBy?: AnalyticsReportGroupByDto;
}

export class CreateAnalyticsReportDto extends BaseAnalyticsReportDto {
  @ValidateNested()
  @Type(() => CreateAnalyticsReportFilterMetadataDto)
  @IsNotEmpty()
  filters: CreateAnalyticsReportFilterMetadataDto;
}

export class CreateCreativeLeaderboardReportDto extends BaseAnalyticsReportDto {
  @ValidateNested()
  @Type(() => CreateCreativeLeaderboardReportFilterMetadataDto)
  @IsNotEmpty()
  filters: CreateCreativeLeaderboardReportFilterMetadataDto;
}

export class RenameAnalyticsReportDto {
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  description?: string;
}
