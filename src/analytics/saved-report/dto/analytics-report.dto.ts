import {
  BaseAnalyticsReportDto,
  BaseAnalyticsReportFilterMetadataDto,
  MediaType,
} from './create-analytics-report.dto';
import {
  IsArray,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
  ArrayNotEmpty,
  IsEnum,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { AutoMap } from '@automapper/classes';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';
import { ENTITY_PERMISSIONS } from '../../../constants/permission.constants';
import { ReadReportUserDto } from './read-report-user.dto';
import { Platform } from '../../../constants/platform.constants';
import { DateRange } from '../../dto/date-range.dto';

export class Workspace {
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @IsNotEmpty()
  @IsString()
  name: string;
}

export class AnalyticsReportFilterDto extends BaseAnalyticsReportFilterMetadataDto {
  @IsNotEmpty()
  @IsString()
  organizationId: string;

  @IsArray()
  @IsNotEmpty()
  @ArrayNotEmpty()
  workspaces: Workspace[];

  @IsArray()
  @IsNotEmpty()
  @ArrayNotEmpty()
  adAccounts: ReadPlatformAdAccountDto[];
}

export class LeaderboardReportFiltersDto {
  @IsNotEmpty()
  @IsString()
  organizationId: string;

  @IsArray()
  @IsNotEmpty()
  @ArrayNotEmpty()
  workspaces: Workspace[];

  @IsArray()
  @IsNotEmpty()
  @ArrayNotEmpty()
  adAccounts: ReadPlatformAdAccountDto[];

  @IsString()
  @IsNotEmpty()
  @Transform(({ value }) => value.toLowerCase())
  @IsEnum(Platform, {
    always: true,
    message: 'platform must be one of: ' + Object.values(Platform).join(', '),
  })
  platform: Platform;

  @ValidateNested()
  @Type(() => DateRange)
  @IsNotEmpty()
  dateRange: DateRange;

  @IsString()
  @IsNotEmpty()
  kpiId: string;

  @IsString()
  @IsNotEmpty()
  kpiName: string;

  @IsNumber()
  @IsNotEmpty()
  limit: number;

  @IsNumber()
  @IsNotEmpty()
  limitPerAdAccount: number;

  @IsNumber()
  @IsNotEmpty()
  minimumDaysLive: number;

  @IsNumber()
  @IsNotEmpty()
  minimumImpressionsPerCreative: number;

  @IsArray()
  @Transform(({ value }) => value.map((val: string) => val.toLowerCase()))
  @IsEnum(MediaType, {
    each: true,
    always: true,
    message:
      'mediaTypes must be one of: ' + Object.values(MediaType).join(', '),
  })
  @IsNotEmpty()
  mediaTypes: MediaType[];
}

export class UserAccessStatusDto {
  @IsNotEmpty()
  @IsString()
  accountsAccessStatus:
    | ENTITY_PERMISSIONS.PARTIAL_PERMISSION
    | ENTITY_PERMISSIONS.DENY_PERMISSION
    | ENTITY_PERMISSIONS.ALLOW_PERMISSION;

  @IsNotEmpty()
  @IsString()
  workspacesAccessStatus:
    | ENTITY_PERMISSIONS.PARTIAL_PERMISSION
    | ENTITY_PERMISSIONS.DENY_PERMISSION
    | ENTITY_PERMISSIONS.ALLOW_PERMISSION;
}

export class AnalyticsReportWithNoFiltersDto extends BaseAnalyticsReportDto {
  @IsNotEmpty()
  @IsString()
  id: string;

  @AutoMap()
  @Type(() => ReadReportUserDto)
  createdBy: ReadReportUserDto;

  @AutoMap()
  @IsDate()
  dateCreated: Date;

  @AutoMap()
  @IsDate()
  lastUpdated: Date;
}

export class AnalyticsReportDto extends AnalyticsReportWithNoFiltersDto {
  @IsNumber()
  @IsOptional()
  filtersVersion?: number;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => AnalyticsReportFilterDto)
  filters: AnalyticsReportFilterDto;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UserAccessStatusDto)
  userAccessStatus: UserAccessStatusDto;
}

export class LeaderboardReportDto extends AnalyticsReportWithNoFiltersDto {
  @IsNumber()
  @IsOptional()
  filtersVersion?: number;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => LeaderboardReportFiltersDto)
  filters: LeaderboardReportFiltersDto;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => UserAccessStatusDto)
  userAccessStatus: UserAccessStatusDto;
}
