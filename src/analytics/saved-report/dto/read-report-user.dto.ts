import { AutoMap } from '@automapper/classes';

export class ReadReportUserDto {
  /**
   * Id of the User
   * @example 123
   */
  @AutoMap()
  id: number;

  /**
   * First name of user
   * @example FirstName
   */
  @AutoMap()
  firstName: string;

  /**
   * Last name of user
   * @example LastName
   */
  @AutoMap()
  lastName: string;

  /**
   * Display name of user
   * @example FirstName LastName
   */
  @AutoMap()
  displayName: string;

  /**
   * Photo of user
   * @example https://d2mcrmdbaugu8j.cloudfront.net/ABC123DEFHIJ/user/avatar/user-avatar.png
   */
  @AutoMap()
  photo: string;
}
