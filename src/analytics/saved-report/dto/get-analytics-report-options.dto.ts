import {
  <PERSON><PERSON><PERSON>y,
  <PERSON><PERSON>num,
  <PERSON><PERSON><PERSON>ber,
  IsString,
  IsOptional,
} from 'class-validator';
import { AnalyticsReportType } from '../model/analytics-report';
import { DateRange } from 'src/analytics/dto/date-range.dto';
import { Type } from 'class-transformer';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { SortBy } from '../../../reports/reports-enums';
import { SortOrder } from '../../../reports/model/sort-order';

export class GetAnalyticsReportOptionsDto {
  offset?: number;
  perPage?: number;
  queryId?: string;
  searchTerm?: string;
  sortOrder: string;
  sortBy: string;
  types: AnalyticsReportType[];
}

export class GetAnalyticsReportFilterQueryDto {
  offset?: number;
  perPage?: number;
  queryId?: string;
  sortOrder: SortOrder;
  sortBy: SortBy;
}

export class GetAnalyticsReportBodyDto {
  @IsOptional()
  @IsArray()
  types: AnalyticsReportType[];

  @IsOptional()
  @IsString()
  searchTerm?: string;

  @IsOptional()
  @Type(() => DateRange)
  dateCreated?: DateRange;

  @IsOptional()
  @Type(() => DateRange)
  lastUpdated?: DateRange;

  @IsOptional()
  @IsNumber({}, { each: true })
  createdBy?: number[];

  @IsOptional()
  @IsArray()
  @IsEnum(Platform, { each: true })
  channels?: Platform[];

  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  workspaces?: number[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  adAccounts?: string[];
}

export type GetAnalyticsReportFilterOptionsDto =
  GetAnalyticsReportFilterQueryDto & GetAnalyticsReportBodyDto;
