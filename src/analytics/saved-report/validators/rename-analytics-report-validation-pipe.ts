import { PipeTransform, BadRequestException } from '@nestjs/common';

export class RenameAnalyticsReportValidationPipe implements PipeTransform {
  transform(value: any) {
    if (value && typeof value === 'object' && Object.keys(value).length > 0) {
      return value;
    }
    throw new BadRequestException(
      'Report validation failed. Body must contain at least new name or new description.',
    );
  }
}
