import { BadRequestException, PipeTransform } from '@nestjs/common';
import {
  CreateAnalyticsReportDto,
  CreateCreativeLeaderboardReportDto,
} from '../dto/create-analytics-report.dto';
import { plainToInstance } from 'class-transformer';
import { validateSync, ValidationError } from 'class-validator';
import { AnalyticsReportType } from '../model/analytics-report';

export class CreateAnalyticsReportValidationPipe implements PipeTransform {
  transform(value: Record<string, any>) {
    const reportType = value.reportType;

    let reportDto = null;

    if (reportType === AnalyticsReportType.CREATIVE_LEADERBOARD) {
      reportDto = plainToInstance(CreateCreativeLeaderboardReportDto, value);
    } else {
      reportDto = plainToInstance(CreateAnalyticsReportDto, value);
    }

    const errors: ValidationError[] = validateSync(reportDto, {
      skipMissingProperties: false,
    });

    if (errors.length > 0) {
      throw new BadRequestException(`Report validation failed. ${errors}`);
    }

    return reportDto;
  }
}
