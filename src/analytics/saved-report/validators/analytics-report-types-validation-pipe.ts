import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { AnalyticsReportType } from '../model/analytics-report';

@Injectable()
export class GetAllAnalyticsReportTypesValidationPipe implements PipeTransform {
  transform(value: string): AnalyticsReportType[] {
    if (!value) {
      return Object.values(AnalyticsReportType);
    }

    const values = value.split(',');
    const uppercaseValues: string[] = values.map((str) => str.toUpperCase());
    if (
      !uppercaseValues.every((item) =>
        Object.values(AnalyticsReportType).includes(
          item as AnalyticsReportType,
        ),
      )
    ) {
      throw new BadRequestException(
        'Invalid report type values. Expected report types to be values: ' +
          Object.values(AnalyticsReportType),
      );
    }

    return values.map(
      (item) =>
        AnalyticsReportType[
          item.toUpperCase() as keyof typeof AnalyticsReportType
        ],
    );
  }
}
