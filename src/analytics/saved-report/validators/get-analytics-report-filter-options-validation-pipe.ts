import { BadRequestException, Logger, PipeTransform } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { validateSync, ValidationError } from 'class-validator';
import { GetAnalyticsReportFilterOptionsRequestDto } from '../dto/get-analytics-filter-values.dto';

export class GetAnalyticsReportFilterOptionsValidationPipe
  implements PipeTransform
{
  private readonly logger = new Logger(
    GetAnalyticsReportFilterOptionsValidationPipe.name,
  );
  transform(
    value: Record<string, any>,
  ): GetAnalyticsReportFilterOptionsRequestDto {
    const requestDto = plainToInstance(
      GetAnalyticsReportFilterOptionsRequestDto,
      value,
    );

    const errors: ValidationError[] = validateSync(requestDto, {
      skipMissingProperties: false,
    });
    if (errors.length > 0) {
      const errorMessage = `Request body validation failed. ${errors}`;
      this.logger.error(errorMessage);
      throw new BadRequestException(errorMessage);
    }

    return value as GetAnalyticsReportFilterOptionsRequestDto;
  }
}
