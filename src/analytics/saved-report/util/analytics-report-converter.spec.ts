import 'reflect-metadata';
import {
  expectedAnalyticsReportDto,
  expectedAnalyticsReportWithNoAccessDto,
  expectedAnalyticsReportWithPartialAccessDto,
  expectedReportToSave,
  mockFirstPlatformAccountId,
  mockUserAdAccountsAndWorkspacesDto,
  mockWorkspaceId,
} from '../mocks/analytics-report.mock';
import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsReportConverter } from './analytics-report-converter';
import {
  createPerson,
  createReport,
} from '../test-utils/test-object-factories';

describe('AnalyticsReportConverter returns expected analytics report dto when user', () => {
  let analyticsReportConverter: AnalyticsReportConverter;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AnalyticsReportConverter],
    }).compile();

    analyticsReportConverter = module.get<AnalyticsReportConverter>(
      AnalyticsReportConverter,
    );
  });

  it('has access to all workspaces and ad accounts', () => {
    const analyticsReportDto = analyticsReportConverter.convert(
      expectedReportToSave,
      mockUserAdAccountsAndWorkspacesDto,
    );
    expect(analyticsReportDto).toEqual(expectedAnalyticsReportDto);
  });

  it('is missing access to some workspaces and ad accounts', () => {
    const analyticsReportDto = analyticsReportConverter.convert(
      {
        ...expectedReportToSave,
        filter: {
          ...expectedReportToSave.filter,
          filters: JSON.stringify({
            ...JSON.parse(expectedReportToSave.filter.filters),
            workspaceIds: [mockWorkspaceId, 1234],
            adAccountIds: [mockFirstPlatformAccountId, '1234'],
          }),
        },
      },
      mockUserAdAccountsAndWorkspacesDto,
    );
    expect(analyticsReportDto).toEqual(
      expectedAnalyticsReportWithPartialAccessDto,
    );
  });

  it('has no access to any workspace and ad account in report', () => {
    const analyticsReportDto = analyticsReportConverter.convert(
      {
        ...expectedReportToSave,
        filter: {
          ...expectedReportToSave.filter,
          filters: JSON.stringify({
            ...JSON.parse(expectedReportToSave.filter.filters),
            workspaceIds: [1234, 4567],
            adAccountIds: ['1234', '4567'],
          }),
        },
      },
      mockUserAdAccountsAndWorkspacesDto,
    );
    expect(analyticsReportDto).toEqual(expectedAnalyticsReportWithNoAccessDto);
  });

  describe('convertAll', () => {
    it('converts ReportModels to AnalyticsReportWithNoFiltersDto', () => {
      const reportModel = createReport({}, 1);
      const result = analyticsReportConverter.convertAll([reportModel]);

      expect(result.length).toEqual(1);
      const report = result[0];
      expect(report.id).toEqual(reportModel.id);
      expect(report.name).toEqual(reportModel.name);
      expect(report.description).toEqual(reportModel.description);
      expect(report.dateCreated).toEqual(new Date(reportModel.dateCreated));
      expect(report.createdBy.id).toEqual(reportModel.owner.id);
      expect(report.createdBy.firstName).toEqual(reportModel.owner.firstName);
      expect(report.createdBy.lastName).toEqual(reportModel.owner.lastName);
      expect(report.createdBy.displayName).toEqual(
        reportModel.owner.displayName,
      );
      expect(report.createdBy.photo).toEqual(reportModel.owner.photo);
    });
    it('adds deactivated user to deactivated users display name', () => {
      const deactivatedUser = createPerson({ deactivatedUser: true });
      const reportModel = createReport({ owner: deactivatedUser }, 1);
      const result = analyticsReportConverter.convertAll([reportModel]);

      expect(result.length).toEqual(1);
      const report = result[0];
      expect(report.createdBy.displayName).toEqual(
        `${reportModel.owner.displayName} - Deactivated User`,
      );
    });
  });
});
