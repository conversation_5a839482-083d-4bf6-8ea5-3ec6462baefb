import {
  AnalyticsReportDto,
  LeaderboardReportDto,
} from '../dto/analytics-report.dto';
import {
  BaseAnalyticsReportFilterMetadataDto,
  CreateAnalyticsReportDto,
  CreateCreativeLeaderboardReportDto,
} from '../dto/create-analytics-report.dto';
import { AnalyticsReportType } from '../model/analytics-report';
import { addCopyTextToReportName } from './report-name';

function createCreateCreativeLeaderboardReportDto(
  report: LeaderboardReportDto,
  isCopy = false,
): CreateCreativeLeaderboardReportDto {
  const newReport: CreateCreativeLeaderboardReportDto = {
    reportType: report.reportType,
    sortBy: report.sortBy,
    groupBy: report.groupBy,
    name: isCopy ? `${addCopyTextToReportName(report.name)}` : report.name,
    filters: {
      workspaceIds: report.filters.workspaces.map((w) => w.id),
      dateRange: report.filters.dateRange,
      kpiId: report.filters.kpiId ?? '', /// this is optional in the report DTO but should always be here,
      limit: report.filters.limit,
      limitPerAdAccount: report.filters.limitPerAdAccount,
      minimumDaysLive: report.filters.minimumDaysLive,
      minimumImpressionsPerCreative:
        report.filters.minimumImpressionsPerCreative,
      platform: report.filters.platform,
    },
  };

  if (report.description) {
    newReport.description = report.description;
  }

  return newReport;
}

function createCreateAnalyticsReportDto(
  report: AnalyticsReportDto,
  isCopy = false,
): CreateAnalyticsReportDto {
  const {
    dateCreated,
    createdBy,
    filtersVersion,
    id,
    lastUpdated,
    userAccessStatus,
    ...strippedReport
  } = report;

  const { adAccounts, workspaces, organizationId, ...strippedReportFilters } =
    strippedReport.filters;

  const newReport: CreateAnalyticsReportDto = {
    ...strippedReport,
    reportType: report.reportType,
    sortBy: report.sortBy,
    groupBy: report.groupBy,
    name: isCopy ? `${addCopyTextToReportName(report.name)}` : report.name,
    filters: {
      ...strippedReportFilters,
      platform: report.filters.platform,
      dateRange: report.filters.dateRange,
      viewBy: report.filters.viewBy,
      mediaTypes: report.filters.mediaTypes,
      isShowAppAdsOnlyActive: report.filters.isShowAppAdsOnlyActive,
      isShowSparkAdsOnlyActive: report.filters.isShowSparkAdsOnlyActive,
      statSettings: report.filters.statSettings,
      workspaceIds: workspaces.map((w) => w.id),
      adAccountIds: adAccounts.map((aa) => aa.platformAccountId),
    },
  };

  return newReport;
}

function isLeaderboardReport(
  report: AnalyticsReportDto | LeaderboardReportDto,
): report is LeaderboardReportDto {
  return report.reportType === AnalyticsReportType.CREATIVE_LEADERBOARD;
}

export function buildCreateReportDto(
  report: AnalyticsReportDto | LeaderboardReportDto,
  isCopy = false,
): CreateAnalyticsReportDto | CreateCreativeLeaderboardReportDto | undefined {
  if (isLeaderboardReport(report)) {
    return createCreateCreativeLeaderboardReportDto(report, isCopy);
  }
  return createCreateAnalyticsReportDto(report, isCopy);
}
