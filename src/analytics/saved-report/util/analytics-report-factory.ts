import { BadRequestException, Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  CreateAnalyticsReportDto,
  CreateCreativeLeaderboardReportDto,
} from '../dto/create-analytics-report.dto';
import { Report } from '../../../entities/report.entity';
import { ReportFilter } from '../../../entities/report-filter.entity';
import { CURRENT_ANALYTICS_FILTER_VERSION } from '../../constants/constants';
import { Person } from '../../../entities/person.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  AnalyticsReportType,
  UserAdAccountsAndWorkspacesDto,
} from '../model/analytics-report';
import { Workspace } from '../dto/analytics-report.dto';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';
import { UserDetailsDto } from '../../dto/user-details.dto';

@Injectable()
export class AnalyticsReportFactory {
  constructor(
    @InjectRepository(Person)
    private personRepository: Repository<Person>,
  ) {}

  private getReportName(
    reportName: string,
    reportType: AnalyticsReportType,
  ): string {
    return reportName.length
      ? reportName
      : `Untitled ${reportType.toLowerCase().replace('-', ' ')} Report`;
  }

  private async fetchOwner(ownerId: number): Promise<Person> {
    const owner = await this.personRepository.findOneBy({
      id: ownerId,
    });
    if (!owner) {
      throw Error('Owner not found');
    }
    return owner;
  }

  private validateWorkspaces(
    workspaceIds: number[],
    userWorkspacesWithAccess: Workspace[],
  ) {
    if (!workspaceIds?.length) {
      throw new BadRequestException('Missing workspace ids in request');
    }

    const workspaceIdsUserCanAccess = userWorkspacesWithAccess.map(
      (item) => item.id,
    );
    const invalidWorkspacesInRequest = workspaceIds.filter(
      (id) => !workspaceIdsUserCanAccess.includes(id),
    );
    if (invalidWorkspacesInRequest.length) {
      throw new BadRequestException(
        'Missing access to some workspaces in request',
      );
    }
  }

  private validateAdAccounts(
    adAccountIds: string[] | null | undefined,
    userAdAccountsWithAccess: ReadPlatformAdAccountDto[],
  ): void {
    if (!adAccountIds?.length) {
      throw new BadRequestException('adAccountIds are required');
    }

    const accountIdsUserCanAccess = userAdAccountsWithAccess.map(
      (item) => item.platformAccountId,
    );
    const invalidAccountsInRequest = adAccountIds?.filter(
      (id) => !accountIdsUserCanAccess.includes(id),
    );
    if (invalidAccountsInRequest?.length || !adAccountIds?.length) {
      throw new BadRequestException(
        'Missing access to some accounts in request. Note that all accounts in a report must be for the same platform/channel',
      );
    }
  }

  isCreativeLeaderboardReport(
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
  ): boolean {
    return (
      createAnalyticsReportDto.reportType ===
      AnalyticsReportType.CREATIVE_LEADERBOARD
    );
  }

  /**
   * Validates the workspaces and ad accounts in the request. Ad account
   * validation doesn't apply to creative leaderboard reports because
   * the ad account ids are fetched directly from the user's workspaces
   * @param createAnalyticsReportDto
   * @param userAdAccountsAndWorkspacesDto
   * @private
   */
  private validateReportWorkspacesAndAdAccounts(
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
    userAdAccountsAndWorkspacesDto: UserAdAccountsAndWorkspacesDto,
  ) {
    this.validateWorkspaces(
      createAnalyticsReportDto.filters.workspaceIds,
      userAdAccountsAndWorkspacesDto.workspaces,
    );

    if (!this.isCreativeLeaderboardReport(createAnalyticsReportDto)) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore // disables check for adAccountIds on leaderboard dto
      const adAccountIds = createAnalyticsReportDto.filters?.adAccountIds;
      if (adAccountIds) {
        this.validateAdAccounts(
          adAccountIds,
          userAdAccountsAndWorkspacesDto.adAccounts,
        );
      }
    }
  }

  private getReportFiltersJson(
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
    userDetailsDto: UserDetailsDto,
    userAdAccountIdsForLeaderboard?: string[] | null,
  ) {
    if (this.isCreativeLeaderboardReport(createAnalyticsReportDto)) {
      return JSON.stringify({
        ...createAnalyticsReportDto.filters,
        organizationId: userDetailsDto.organizationId,
        adAccountIds: userAdAccountIdsForLeaderboard,
      });
    }

    return JSON.stringify({
      ...createAnalyticsReportDto.filters,
      organizationId: userDetailsDto.organizationId,
    });
  }

  private async buildReportFilter(
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
    validatedOwner: Person,
    currentDate: Date,
    userAdAccountsAndWorkspacesDto: UserAdAccountsAndWorkspacesDto,
    userDetailsDto: UserDetailsDto,
    adAccountIdsForLeaderboard?: string[] | null,
  ): Promise<ReportFilter> {
    const reportFilter = new ReportFilter();

    this.validateReportWorkspacesAndAdAccounts(
      createAnalyticsReportDto,
      userAdAccountsAndWorkspacesDto,
    );

    reportFilter.id = uuidv4();
    reportFilter.owner = validatedOwner;
    reportFilter.adHoc = false;
    reportFilter.shared = false;
    reportFilter.filtersVersion = CURRENT_ANALYTICS_FILTER_VERSION;
    reportFilter.filters = this.getReportFiltersJson(
      createAnalyticsReportDto,
      userDetailsDto,
      adAccountIdsForLeaderboard,
    );
    reportFilter.sortBy = JSON.stringify(createAnalyticsReportDto.sortBy);
    reportFilter.groupBy = JSON.stringify(createAnalyticsReportDto.groupBy);
    reportFilter.aggregationColumns = '';
    reportFilter.dateCreated = currentDate;
    reportFilter.lastUpdated = currentDate;
    reportFilter.lastUpdatedByUser = validatedOwner;
    reportFilter.deleted = false;

    return reportFilter;
  }

  async buildReportForCreate(
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
    userDetailsDto: UserDetailsDto,
    userAdAccountsAndWorkspacesDto: UserAdAccountsAndWorkspacesDto,
    adAccountIdsForLeaderboard?: string[] | null,
  ): Promise<Report> {
    const { userId, organizationId } = userDetailsDto;
    const currentDate = new Date();
    const validatedOwner = await this.fetchOwner(userId);
    const reportFilterMetadata = await this.buildReportFilter(
      createAnalyticsReportDto,
      validatedOwner,
      currentDate,
      userAdAccountsAndWorkspacesDto,
      userDetailsDto,
      adAccountIdsForLeaderboard,
    );

    const report = new Report();
    report.id = uuidv4();
    report.name = this.getReportName(
      createAnalyticsReportDto.name,
      createAnalyticsReportDto.reportType,
    );
    report.description = createAnalyticsReportDto.description || '';
    report.owner = validatedOwner;
    report.organizationId = organizationId;
    report.filter = reportFilterMetadata;
    report.shared = false;
    report.reportType = createAnalyticsReportDto.reportType;
    report.dateCreated = currentDate;
    report.lastUpdated = currentDate;
    report.lastUpdatedByUser = validatedOwner;
    report.deleted = false;

    return report;
  }

  async buildReportForUpdate(
    savedReport: Report,
    updateAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
    userDetailsDto: UserDetailsDto,
    userAdAccountsAndWorkspacesDto: UserAdAccountsAndWorkspacesDto,
    adAccountIdsForLeaderboard?: string[] | null,
  ): Promise<Report> {
    const report = await this.buildReportForCreate(
      updateAnalyticsReportDto,
      userDetailsDto,
      userAdAccountsAndWorkspacesDto,
      adAccountIdsForLeaderboard,
    );

    report.id = savedReport.id;
    report.dateCreated = savedReport.dateCreated;
    report.filter.id = savedReport.filter.id;
    report.filter.dateCreated = savedReport.filter.dateCreated;

    return report;
  }
}
