import {
  createStubAnalyticsReportDto,
  createStubLeaderboardDto,
} from '../test-utils/saved-report-stub-factories';
import { buildCreateReportDto } from './create-report-factory';
import { AnalyticsReportType } from '../model/analytics-report';
import {
  AnalyticsReportDto,
  LeaderboardReportDto,
} from '../dto/analytics-report.dto';

describe('buildCreateReportDto', () => {
  it('builds a CreateCreativeLeaderboardReportDto when given a leaderboardReport', () => {
    const leaderboardReport = createStubLeaderboardDto({});

    const createLeaderboardReportDto = buildCreateReportDto(leaderboardReport);

    expect(createLeaderboardReportDto?.reportType).toEqual(
      AnalyticsReportType.CREATIVE_LEADERBOARD,
    );
    expect(Object.keys(createLeaderboardReportDto ?? {})).toEqual([
      'reportType',
      'sortBy',
      'groupBy',
      'name',
      'filters',
      'description',
    ]);

    expect(Object.keys(createLeaderboardReportDto?.filters ?? {})).toEqual([
      'workspaceIds',
      'dateRange',
      'kpiId',
      'limit',
      'limitPerAdAccount',
      'minimumDaysLive',
      'minimumImpressionsPerCreative',
      'platform',
    ]);
  });

  it.each([
    AnalyticsReportType.MEDIA_IMPACT,
    AnalyticsReportType.ELEMENT_PRESENCE,
    AnalyticsReportType.ELEMENT_IMPACT,
    AnalyticsReportType.CRITERIA_PERFORMANCE,
  ])('creates a createReportDto for report type: %s', (reportType) => {
    const analyticsReport = createStubAnalyticsReportDto(reportType, false);
    const createAnalyticsReportDto = buildCreateReportDto(analyticsReport);

    expect(createAnalyticsReportDto?.reportType).toEqual(
      analyticsReport.reportType,
    );

    expect(Object.keys(createAnalyticsReportDto ?? {}).sort()).toEqual(
      [
        'reportType',
        'sortBy',
        'groupBy',
        'name',
        'filters',
        'description',
      ].sort(),
    );

    expect(Object.keys(createAnalyticsReportDto?.filters ?? {}).sort()).toEqual(
      [
        'workspaceIds',
        'adAccountIds',
        'platform',
        'dateRange',
        'kpiId',
        'viewBy',
        'mediaTypes',
        'isShowAppAdsOnlyActive',
        'isShowSparkAdsOnlyActive',
        'statSettings',
      ].sort(),
    );
  });

  it('includes optional filter arguments if present', () => {
    const analyticsReport: AnalyticsReportDto = createStubAnalyticsReportDto(
      AnalyticsReportType.ELEMENT_IMPACT,
      true,
    );

    const createAnalyticsReportDto = buildCreateReportDto(analyticsReport);

    expect(Object.keys(createAnalyticsReportDto?.filters ?? {}).sort()).toEqual(
      [
        'workspaceIds',
        'adAccountIds',
        'platform',
        'dateRange',
        'kpiId',
        'viewBy',
        'mediaTypes',
        'isShowAppAdsOnlyActive',
        'isShowSparkAdsOnlyActive',
        'statSettings',
        'adTypes',
        'campaignIds',
        'campaignSearchStrings',
        'adsetIds',
        'adsetSearchStrings',
        'adIds',
        'adSearchString',
        'creativeImpressionsRange',
        'objectives',
        'placements',
        'criteriaGroupIds',
        'selectedRows',
        'isDefaultKPITimeRangeFilterActive',
        'adImpressionsRange',
        'createdByVidmob',
        'normsConfiguration',
      ].sort(),
    );
  });

  describe('copy name functionality', () => {
    it('adds "Copy(n)" to the report name if isCopy is true, given a Leaderboard report type', () => {
      const leaderboardReport = createStubLeaderboardDto({});
      const createLeaderboardReportReportDtoCopy1 = buildCreateReportDto(
        leaderboardReport,
        true,
      );
      const expectedCreateLeaderboardReportReportDtoCopy1Name = `${leaderboardReport.name} Copy(1)`;
      expect(createLeaderboardReportReportDtoCopy1?.name).toEqual(
        expectedCreateLeaderboardReportReportDtoCopy1Name,
      );
      const leaderboardReportCopy: LeaderboardReportDto = {
        ...leaderboardReport,
        name: expectedCreateLeaderboardReportReportDtoCopy1Name,
      };

      const createLeaderboardReportReportDtoCopy2 = buildCreateReportDto(
        leaderboardReportCopy,
        true,
      );
      expect(createLeaderboardReportReportDtoCopy2?.name).toEqual(
        `${leaderboardReport.name} Copy(2)`,
      );
    });

    it('adds "Copy(n)" to the report name if isCopy is true, given a non-Leaderboard report type', () => {
      const analyticsReport: AnalyticsReportDto = createStubAnalyticsReportDto(
        AnalyticsReportType.ELEMENT_IMPACT,
        false,
      );
      const createAnalyticsReportDtoCopy1 = buildCreateReportDto(
        analyticsReport,
        true,
      );
      const expectedCreateAnalyticsReportDtoCopy1Name = `${analyticsReport.name} Copy(1)`;
      expect(createAnalyticsReportDtoCopy1?.name).toEqual(
        expectedCreateAnalyticsReportDtoCopy1Name,
      );
      const analyticsReportCopy: AnalyticsReportDto = {
        ...analyticsReport,
        name: expectedCreateAnalyticsReportDtoCopy1Name,
      };

      const createAnalyticsReportDtoCopy2 = buildCreateReportDto(
        analyticsReportCopy,
        true,
      );
      expect(createAnalyticsReportDtoCopy2?.name).toEqual(
        `${analyticsReport.name} Copy(2)`,
      );
    });

    it('does not add "Copy(n)" to the report name if isCopy is false/undefined, given a Leaderboard report type', () => {
      const leaderboardReport = createStubLeaderboardDto({});
      const createLeaderboardReportReportDtoCopy1 =
        buildCreateReportDto(leaderboardReport);
      const expectedCreateLeaderboardReportReportDtoCopy1Name =
        leaderboardReport.name;
      expect(createLeaderboardReportReportDtoCopy1?.name).toEqual(
        expectedCreateLeaderboardReportReportDtoCopy1Name,
      );
      const leaderboardReportCopy: LeaderboardReportDto = {
        ...leaderboardReport,
        name: expectedCreateLeaderboardReportReportDtoCopy1Name,
      };

      const createLeaderboardReportReportDtoCopy2 = buildCreateReportDto(
        leaderboardReportCopy,
        false,
      );
      expect(createLeaderboardReportReportDtoCopy2?.name).toEqual(
        expectedCreateLeaderboardReportReportDtoCopy1Name,
      );
    });

    it('does not add "Copy(n)" to the report name if isCopy is false/undefined, given a non-Leaderboard report type', () => {
      const analyticsReport: AnalyticsReportDto = createStubAnalyticsReportDto(
        AnalyticsReportType.ELEMENT_IMPACT,
        false,
      );
      const createAnalyticsReportDtoCopy1 =
        buildCreateReportDto(analyticsReport);
      const expectCreateAnalyticsReportDtoCopy1Name = analyticsReport.name;
      expect(createAnalyticsReportDtoCopy1?.name).toEqual(
        expectCreateAnalyticsReportDtoCopy1Name,
      );
      const analyticsReportCopy: AnalyticsReportDto = {
        ...analyticsReport,
        name: expectCreateAnalyticsReportDtoCopy1Name,
      };

      const createAnalyticsReportDto = buildCreateReportDto(
        analyticsReportCopy,
        false,
      );
      expect(createAnalyticsReportDto?.name).toEqual(
        expectCreateAnalyticsReportDtoCopy1Name,
      );
    });
  });
});
