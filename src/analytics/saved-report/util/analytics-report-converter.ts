import { Report } from '../../../entities/report.entity';
import {
  AnalyticsReportDto,
  AnalyticsReportFilterDto,
  AnalyticsReportWithNoFiltersDto,
  UserAccessStatusDto,
} from '../dto/analytics-report.dto';
import {
  AnalyticsReportType,
  UserAdAccountsAndWorkspacesDto,
  Report as ReportModel,
} from '../model/analytics-report';
import { Injectable } from '@nestjs/common';
import { ENTITY_PERMISSIONS } from '../../../constants/permission.constants';
import { Person } from '../../../entities/person.entity';
import { SortOrder } from '../../../reports/model/sort-order';
import {
  GroupByColumns,
  GroupByRows,
} from '../dto/create-analytics-report.dto';

@Injectable()
export class AnalyticsReportConverter {
  convert(
    report: Report,
    userAdAccountsAndWorkspaces: UserAdAccountsAndWorkspacesDto,
  ): AnalyticsReportDto {
    const { filter: reportFilter, owner } = report;
    const { filters: reportFilterMetadata } = reportFilter;
    const {
      workspaceIds: savedWorkspaceIds,
      adAccountIds: savedAdAccountIds,
      ...otherFilters
    } = JSON.parse(reportFilterMetadata);
    const workspacesUserHasAccessTo = userAdAccountsAndWorkspaces.workspaces
      .filter((workspace) => savedWorkspaceIds?.includes(workspace.id))
      .map((item) => ({
        id: item.id,
        name: item.name,
      }));

    const adAccountsUserHasAccessTo =
      userAdAccountsAndWorkspaces.adAccounts.filter((adAccount) =>
        savedAdAccountIds?.includes(adAccount.platformAccountId),
      );

    const analyticsReportFilterDto: AnalyticsReportFilterDto = {
      ...otherFilters,
      workspaces: workspacesUserHasAccessTo,
      adAccounts: adAccountsUserHasAccessTo,
    };

    const userAccessStatus: UserAccessStatusDto = {
      accountsAccessStatus: this.getItemAccessStatus(
        adAccountsUserHasAccessTo,
        savedAdAccountIds,
      ),
      workspacesAccessStatus: this.getItemAccessStatus(
        workspacesUserHasAccessTo,
        savedWorkspaceIds,
      ),
    };

    const createdBy = {
      id: owner.id,
      firstName: owner.firstName,
      lastName: owner.lastName,
      displayName: owner.displayName,
      photo: owner.photo,
    };

    const isPersonDeactivatedFromOrganization =
      owner.personOrganizationMaps?.length == 0;

    if (isPersonDeactivatedFromOrganization) {
      createdBy.displayName = `${owner.email} - Deactivated User`;
    }

    return {
      id: report.id,
      name: report.name,
      description: report.description,
      reportType: report.reportType as AnalyticsReportType,
      filtersVersion: reportFilter.filtersVersion,
      filters: analyticsReportFilterDto,
      sortBy: reportFilter.sortBy ? JSON.parse(reportFilter.sortBy) : null,
      groupBy: reportFilter.groupBy ? JSON.parse(reportFilter.groupBy) : null,
      userAccessStatus,
      dateCreated: report.dateCreated,
      lastUpdated: report.lastUpdated,
      createdBy: createdBy,
    };
  }

  convertAllWithoutFilters(
    reports: Report[],
    userAdAccountsAndWorkspaces: UserAdAccountsAndWorkspacesDto,
  ): AnalyticsReportWithNoFiltersDto[] {
    return reports.map((report) => {
      const { owner, workspaceMaps, platformAdAccountMaps } = report;
      const reportWorkspaceIds = [
        ...new Set(
          workspaceMaps?.map(
            (reportWorkspaceMap) => reportWorkspaceMap.workspaceId,
          ),
        ),
      ];
      const reportAdAccountIds = [
        ...new Set(
          platformAdAccountMaps?.map(
            (reportPlatformAccountMap) =>
              reportPlatformAccountMap.platformAdAccount?.platform_account_id,
          ),
        ),
      ];

      const workspacesUserHasAccessTo = userAdAccountsAndWorkspaces.workspaces
        .filter((workspace) => reportWorkspaceIds?.includes(workspace.id))
        .map((item) => ({
          id: item.id,
          name: item.name,
        }));

      const adAccountsUserHasAccessTo =
        userAdAccountsAndWorkspaces.adAccounts.filter((adAccount) =>
          reportAdAccountIds?.includes(adAccount.platformAccountId),
        );

      const bareBonesReportFilter = {
        workspaces: workspacesUserHasAccessTo,
        adAccounts: adAccountsUserHasAccessTo,
      };

      return {
        id: report.id,
        name: report.name,
        description: report.description,
        reportType: report.reportType as AnalyticsReportType,
        filtersVersion: 0,
        filters: bareBonesReportFilter,
        sortBy: { sortBy: 'dateCreated', sortOrder: SortOrder.DESC },
        groupBy: { columns: GroupByColumns.NONE, rows: GroupByRows.NONE },
        dateCreated: report.dateCreated,
        lastUpdated: report.lastUpdated,
        createdBy: this.parseCreatedBy(owner),
      };
    });
  }

  // the difference between this and convertWithoutFilters is the type of the input
  // this one is used in reportsListService which does not query data from the ReportEntityType because its not
  // using typeorm
  convertAll(reports: ReportModel[]): AnalyticsReportWithNoFiltersDto[] {
    const result: AnalyticsReportWithNoFiltersDto[] = reports.map((report) => {
      return {
        id: report.id,
        name: report.name,
        description: report.description,
        reportType: report.reportType,
        filtersVersion: 0,
        filters: {
          workspaces: report.workspaces,
          adAccounts: report.adAccounts,
          channel: report.platform,
        },
        sortBy: { sortBy: 'dateCreated', sortOrder: SortOrder.DESC },
        groupBy: { columns: GroupByColumns.NONE, rows: GroupByRows.NONE },
        lastUpdated: new Date(report.lastUpdated),
        dateCreated: new Date(report.dateCreated),
        createdBy: {
          id: report.owner.id,
          firstName: report.owner.firstName,
          lastName: report.owner.lastName,
          displayName: report.owner.deactivatedUser
            ? `${report.owner.displayName} - Deactivated User`
            : report.owner.displayName,
          photo:
            report.owner.deactivatedUser || !report.owner.photo
              ? ''
              : report.owner.photo,
        },
      };
    });
    return result;
  }

  private parseCreatedBy(owner: Person) {
    const createdBy = {
      id: owner.id,
      firstName: owner.firstName,
      lastName: owner.lastName,
      displayName: owner.displayName,
      photo: owner.photo,
    };

    const isPersonDeactivatedFromOrganization =
      owner.personOrganizationMaps?.length == 0;

    if (isPersonDeactivatedFromOrganization) {
      createdBy.displayName = `${owner.email} - Deactivated User`;
    }

    return createdBy;
  }

  private getItemAccessStatus<T>(
    itemsUserCanAccess: T[],
    savedItems: T[],
  ):
    | ENTITY_PERMISSIONS.ALLOW_PERMISSION
    | ENTITY_PERMISSIONS.DENY_PERMISSION
    | ENTITY_PERMISSIONS.PARTIAL_PERMISSION {
    if (itemsUserCanAccess.length == savedItems.length) {
      return ENTITY_PERMISSIONS.ALLOW_PERMISSION;
    }
    if (itemsUserCanAccess.length == 0) {
      return ENTITY_PERMISSIONS.DENY_PERMISSION;
    }
    return ENTITY_PERMISSIONS.PARTIAL_PERMISSION;
  }
}
