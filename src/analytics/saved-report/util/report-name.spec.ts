import { generateRandomLoremWords } from '../../../utils/random';
import { addCopyTextToReportName } from './report-name';

describe('addCopyTextToReportName', () => {
  it('adds Copy(1) to a something that doesnt contain the word copy', () => {
    const stubName = generateRandomLoremWords(3);
    expect(addCopyTextToReportName(stubName)).toEqual(`${stubName} Copy(1)`);
  });

  it('increments the copy number if the report name already contains copy(#)', () => {
    const stubNameBase = generateRandomLoremWords(3);
    expect(addCopyTextToReportName(`${stubNameBase} Copy(5)`)).toEqual(
      `${stubNameBase} Copy(6)`,
    );
  });

  it('does not change the word copy in a name unless followed by a count', () => {
    const name = 'copy Copy COPY copy(fake) copycopy';
    expect(addCopyTextToReportName(name)).toEqual(`${name} Copy(1)`);
  });
});
