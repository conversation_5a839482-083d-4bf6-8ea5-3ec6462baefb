import { Test, TestingModule } from '@nestjs/testing';
import { AnalyticsReportsService } from './analytics-reports.service';
import { AnalyticsReportsController } from './analytics-reports.controller';
import { AnalyticsReportType } from './model/analytics-report';
import {
  mockCreateAnalyticsReportDto,
  mockGetAnalyticsReportFilterOptionsRequestDto,
  mockOrganizationId,
  mockRequest,
  mockUpdateAnalyticsReportDto,
  mockUserDetails,
  mockUUIDReturnValue,
} from './mocks/analytics-report.mock';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { createStubAnalyticsReportDto } from './test-utils/saved-report-stub-factories';
import { AnalyticsReportDto } from './dto/analytics-report.dto';
import { SortOrder } from '../../reports/model/sort-order';
import { SortBy } from '../../reports/reports-enums';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { ReportListService } from './services/report-list-service';

describe('AnalyticsReportsController', () => {
  let analyticsReportsController: AnalyticsReportsController;
  const mockGetAllAnalyticsReports = jest.fn();
  const mockSaveAnalyticsReport = jest.fn();
  const mockUpdateAnalyticsReport = jest.fn();
  const mockGetAnalyticsReportForUser = jest.fn();
  const mockDeleteAnalyticsReportForUser = jest.fn();
  const mockGetAnalyticsReportFilterOptions = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AnalyticsReportsController],
      providers: [
        {
          provide: AnalyticsReportsService,
          useValue: {
            getAllAnalyticsReports: mockGetAllAnalyticsReports,
            saveAnalyticsReport: mockSaveAnalyticsReport,
            updateAnalyticsReport: mockUpdateAnalyticsReport,
            getAnalyticsReportForUser: mockGetAnalyticsReportForUser,
            deleteAnalyticsReportForUser: mockDeleteAnalyticsReportForUser,
            getAnalyticsReportFilterOptions:
              mockGetAnalyticsReportFilterOptions,
          },
        },
        {
          provide: WorkspaceService,
          useValue: {},
        },
        {
          provide: ReportListService,
          useValue: {},
        },
      ],
    }).compile();

    analyticsReportsController = module.get<AnalyticsReportsController>(
      AnalyticsReportsController,
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(analyticsReportsController).toBeDefined();
  });

  it('should call getAnalyticsReports', async () => {
    await analyticsReportsController.getAllAnalyticsReports(
      mockRequest,
      {},
      mockOrganizationId,
      [
        AnalyticsReportType.ELEMENT_IMPACT,
        AnalyticsReportType.MEDIA_IMPACT,
        AnalyticsReportType.ELEMENT_PRESENCE,
      ],
      SortOrder.DESC,
      SortBy.DateCreated,
      undefined,
    );

    expect(mockGetAllAnalyticsReports).toHaveBeenCalledWith(mockUserDetails, {
      sortOrder: 'DESC',
      sortBy: 'dateCreated',
      types: [
        AnalyticsReportType.ELEMENT_IMPACT,
        AnalyticsReportType.MEDIA_IMPACT,
        AnalyticsReportType.ELEMENT_PRESENCE,
      ],
    });
  });

  it('should call saveAnalyticsReport', async () => {
    await analyticsReportsController.createAnalyticsReport(
      mockRequest,
      mockOrganizationId,
      mockCreateAnalyticsReportDto,
    );

    expect(mockSaveAnalyticsReport).toHaveBeenCalledWith(
      mockUserDetails,
      mockCreateAnalyticsReportDto,
    );
  });

  it('should call updateAnalyticsReport', async () => {
    await analyticsReportsController.updateAnalyticsReport(
      mockRequest,
      mockOrganizationId,
      mockUUIDReturnValue,
      mockUpdateAnalyticsReportDto,
    );

    expect(mockUpdateAnalyticsReport).toHaveBeenCalledWith(
      mockUserDetails,
      mockUUIDReturnValue,
      mockUpdateAnalyticsReportDto,
    );
  });

  it('should call getSingleAnalyticsReport', async () => {
    await analyticsReportsController.getSingleAnalyticsReport(
      mockRequest,
      mockOrganizationId,
      mockUUIDReturnValue,
    );

    expect(mockGetAnalyticsReportForUser).toHaveBeenCalledWith(
      mockUserDetails,
      mockUUIDReturnValue,
    );
  });

  it('should call deleteAnalyticsReportForUser', async () => {
    await analyticsReportsController.deleteAnalyticsReport(
      mockRequest,
      mockOrganizationId,
      mockUUIDReturnValue,
    );

    expect(mockDeleteAnalyticsReportForUser).toHaveBeenCalledWith(
      mockUserDetails,
      mockUUIDReturnValue,
    );
  });

  it('should call getAnalyticsReportFilterOptions', async () => {
    await analyticsReportsController.getAnalyticsReportFilterOptions(
      mockRequest,
      mockOrganizationId,
      mockGetAnalyticsReportFilterOptionsRequestDto,
      {
        offset: 0,
        perPage: 10,
        queryId: 'eisiei-eke9ihr39rb',
      },
    );

    expect(mockGetAnalyticsReportFilterOptions).toHaveBeenCalledWith(
      mockUserDetails,
      mockGetAnalyticsReportFilterOptionsRequestDto,
      {
        offset: 0,
        perPage: 10,
        queryId: 'eisiei-eke9ihr39rb',
      },
    );
  });

  describe('duplicateAnalyticsReport', () => {
    it('should fetch the report with given id and pass a createReportDto to the service', async () => {
      const reportToDuplicate: AnalyticsReportDto =
        createStubAnalyticsReportDto(AnalyticsReportType.ELEMENT_IMPACT, false);
      mockGetAnalyticsReportForUser.mockResolvedValueOnce(reportToDuplicate);
      await analyticsReportsController.duplicateAnalyticsReport(
        mockRequest,
        mockOrganizationId,
        'valid-report-id',
      );

      expect(mockSaveAnalyticsReport).toHaveBeenCalledWith(mockUserDetails, {
        reportType: AnalyticsReportType.ELEMENT_IMPACT,
        sortBy: reportToDuplicate.sortBy,
        groupBy: reportToDuplicate.groupBy,
        name: `${reportToDuplicate.name} Copy(1)`,
        filters: {
          platform: reportToDuplicate.filters.platform,
          dateRange: reportToDuplicate.filters.dateRange,
          viewBy: reportToDuplicate.filters.viewBy,
          mediaTypes: reportToDuplicate.filters.mediaTypes,
          isShowSparkAdsOnlyActive:
            reportToDuplicate.filters.isShowAppAdsOnlyActive,
          isShowAppAdsOnlyActive:
            reportToDuplicate.filters.isShowSparkAdsOnlyActive,
          statSettings: reportToDuplicate.filters.statSettings,
          workspaceIds: reportToDuplicate.filters.workspaces.map((w) => w.id),
          adAccountIds: reportToDuplicate.filters.adAccounts.map(
            (aa) => aa.platformAccountId,
          ),
          kpiId: reportToDuplicate.filters.kpiId,
        },
        description: reportToDuplicate.description,
      });
    });
  });

  describe('renameAnalyticsReport', () => {
    it('should throw an error if the report does not belong to the user', async () => {
      const reportToRename: AnalyticsReportDto = createStubAnalyticsReportDto(
        AnalyticsReportType.ELEMENT_IMPACT,
        false,
      );

      const mockRequestWithDifferentUserId = {
        ...mockRequest,
        userId: 'different-user-id',
      };

      mockGetAnalyticsReportForUser.mockResolvedValueOnce(reportToRename);

      await expect(
        analyticsReportsController.renameAnalyticsReport(
          mockRequestWithDifferentUserId,
          mockOrganizationId,
          'valid-report-id',
          { name: 'new name' },
        ),
      ).rejects.toThrowError(
        'Error renaming report valid-report-id, user different-user-id is not the creator of the report',
      );
    });

    it('should fetch the report with given id and pass a createReportDto to the service', async () => {
      const reportToRename: AnalyticsReportDto = createStubAnalyticsReportDto(
        AnalyticsReportType.ELEMENT_IMPACT,
        false,
      );

      const mockRequestWithMatchingUserId = {
        ...mockRequest,
        userId: reportToRename.createdBy.id,
      };

      const mockUserDetailsWithMatchingUserId = {
        ...mockUserDetails,
        userId: reportToRename.createdBy.id,
      };

      const mockReportId = 'valid-report-id';
      const renameReportNameDto = { name: 'new name', description: '' };
      mockGetAnalyticsReportForUser.mockResolvedValueOnce(reportToRename);
      await analyticsReportsController.renameAnalyticsReport(
        mockRequestWithMatchingUserId,
        mockOrganizationId,
        mockReportId,
        renameReportNameDto,
      );

      expect(mockUpdateAnalyticsReport).toHaveBeenCalledWith(
        mockUserDetailsWithMatchingUserId,
        mockReportId,
        {
          reportType: AnalyticsReportType.ELEMENT_IMPACT,
          sortBy: reportToRename.sortBy,
          groupBy: reportToRename.groupBy,
          name: renameReportNameDto.name,
          filters: {
            platform: reportToRename.filters.platform,
            dateRange: reportToRename.filters.dateRange,
            viewBy: reportToRename.filters.viewBy,
            mediaTypes: reportToRename.filters.mediaTypes,
            isShowSparkAdsOnlyActive:
              reportToRename.filters.isShowAppAdsOnlyActive,
            isShowAppAdsOnlyActive:
              reportToRename.filters.isShowSparkAdsOnlyActive,
            statSettings: reportToRename.filters.statSettings,
            workspaceIds: reportToRename.filters.workspaces.map((w) => w.id),
            adAccountIds: reportToRename.filters.adAccounts.map(
              (aa) => aa.platformAccountId,
            ),
            kpiId: reportToRename.filters.kpiId,
          },
          description: renameReportNameDto.description,
        },
      );
    });
  });
});
