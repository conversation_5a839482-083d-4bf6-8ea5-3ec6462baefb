import {
  CreateAnalyticsReportDto,
  CreateCreativeLeaderboardReportDto,
  GroupByColumns,
  GroupByRows,
  MediaType,
  StatAverage,
  StatBaseComparison,
  StatCompareAgainst,
  StatConfidence,
  StatMinimumTagConfidenceLevel,
} from '../dto/create-analytics-report.dto';
import {
  AnalyticsReportType,
  UserAdAccountsAndWorkspacesDto,
} from '../model/analytics-report';
import { SortOrder } from '../../../reports/model/sort-order';
import { Report } from '../../../entities/report.entity';
import { ReportFilter } from '../../../entities/report-filter.entity';
import { AnalyticsReportDto } from '../dto/analytics-report.dto';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';
import { ENTITY_PERMISSIONS } from '../../../constants/permission.constants';
import { UserDetailsDto } from '../../dto/user-details.dto';
import { Platform } from '../../../constants/platform.constants';
import {
  FilterType,
  GetAnalyticsReportFilterOptionsRequestDto,
} from '../dto/get-analytics-filter-values.dto';
import { PersonOrganizationMap } from 'src/entities/person-organization-map.entity';

export const mockOrganizationId = '2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c';

export const mockRequest = {
  userId: 1234,
  organizationId: mockOrganizationId,
  headers: { authorization: 'Bearer 1234' },
};

export const mockUserDetails: UserDetailsDto = {
  userId: mockRequest.userId,
  organizationId: mockRequest.organizationId,
  authorization: mockRequest.headers.authorization,
};

export const mockWorkspaceId = 11111;
export const mockFirstPlatformAccountId = '***************';
export const mockSecondPlatformAccountId = '***************';

export const mockCreateAnalyticsReportDto: CreateAnalyticsReportDto = {
  name: '2021 Campaign Report',
  description: 'Compare objective performance for different KPIs',
  reportType: AnalyticsReportType.MEDIA_IMPACT,
  filters: {
    workspaceIds: [mockWorkspaceId],
    platform: Platform.FACEBOOK,
    adAccountIds: [mockFirstPlatformAccountId, mockSecondPlatformAccountId],
    dateRange: {
      startDate: '2021-01-01',
      endDate: '2021-10-23',
    },
    kpiId: '1',
    viewBy: GroupByColumns.CAMPAIGN,
    mediaTypes: [MediaType.IMAGE, MediaType.VIDEO],
    adTypes: [
      'mobile:facebook:instant_article',
      'mobile_app:facebook:instant_article',
      'unknown:facebook:instant_article',
    ],
    objectives: ['WEBSITE_CLICKS', 'POST_ENGAGEMENT'],
    creativeImpressionsRange: {
      minimum: 500,
      maximum: 50000,
    },
    adImpressionsRange: {
      minimum: 500,
      maximum: 50000,
    },
    isShowAppAdsOnlyActive: false,
    isShowSparkAdsOnlyActive: false,
    statSettings: {
      statConfidence: StatConfidence['95_PERCENT'],
      baseComparison: StatBaseComparison.IMPRESSIONS,
      direction: StatCompareAgainst.VERTICAL,
      average: StatAverage.ELEMENT,
      minimumTagConfidenceLevel: StatMinimumTagConfidenceLevel.MEDIUM,
      isDefaultKPITimeRangeActive: true,
    },
  },
  sortBy: {
    sortBy: 'accountAverage',
    sortOrder: SortOrder.DESC,
  },
  groupBy: {
    columns: GroupByColumns.CAMPAIGN,
    rows: GroupByRows.KPI,
  },
};
export const mockCreateCreativeLeaderboardReportDto: CreateCreativeLeaderboardReportDto =
  {
    name: '2021 Creative Leaderboard',
    description: 'leaderboard',
    reportType: AnalyticsReportType.CREATIVE_LEADERBOARD,
    filters: {
      workspaceIds: [mockWorkspaceId],
      platform: Platform.FACEBOOK,
      dateRange: {
        startDate: '2021-01-01',
        endDate: '2021-10-23',
      },
      kpiId: '1',
      limit: 1000,
      minimumImpressionsPerCreative: 0,
      limitPerAdAccount: 20,
      minimumDaysLive: 7,
    },
    sortBy: {
      sortBy: 'rank',
      sortOrder: SortOrder.ASC,
    },
    groupBy: {
      columns: GroupByColumns.NONE,
      rows: GroupByRows.NONE,
    },
  };

export const mockUser = {
  id: mockUserDetails.userId,
  username: 'mock username',
  firstName: 'mock first name',
  lastName: 'mock last name',
  displayName: 'mock display name',
  photo: 'mock photo',
  email: '<EMAIL>',
  personOrganizationMaps: [
    {
      organizationId: mockUserDetails.organizationId,
    },
  ] as PersonOrganizationMap[],
};

export const mockUUIDReturnValue = 'mocked-uuid-value';

export const mockDateValue = new Date(2020, 3, 1);

const mockReportFilterToSave: ReportFilter = {
  id: mockUUIDReturnValue,
  owner: mockUser,
  adHoc: false,
  shared: false,
  filtersVersion: 1,
  filters: JSON.stringify({
    ...mockCreateAnalyticsReportDto.filters,
    organizationId: mockUserDetails.organizationId,
  }),
  sortBy: JSON.stringify(mockCreateAnalyticsReportDto.sortBy),
  groupBy: JSON.stringify(mockCreateAnalyticsReportDto.groupBy),
  aggregationColumns: '',
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  lastUpdatedByUser: mockUser,
  deleted: false,
};

const mockCreativeLeaderboardFilterToSave: ReportFilter = {
  id: mockUUIDReturnValue,
  owner: mockUser,
  adHoc: false,
  shared: false,
  filtersVersion: 1,
  filters: JSON.stringify({
    ...mockCreateCreativeLeaderboardReportDto.filters,
    organizationId: mockUserDetails.organizationId,
    adAccountIds: [mockFirstPlatformAccountId, mockSecondPlatformAccountId],
  }),
  sortBy: JSON.stringify(mockCreateCreativeLeaderboardReportDto.sortBy),
  groupBy: JSON.stringify(mockCreateCreativeLeaderboardReportDto.groupBy),
  aggregationColumns: '',
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  lastUpdatedByUser: mockUser,
  deleted: false,
};

export const mockUpdateAnalyticsReportDto: CreateAnalyticsReportDto = {
  ...mockCreateAnalyticsReportDto,
  name: 'Newly Updated 2021 Campaign Report',
  description: 'Demo objective performance for different KPIs',
  filters: {
    ...mockCreateAnalyticsReportDto.filters,
    placements: ['feed', 'story'],
  },
};

export const mockUpdateLeaderboardReportDto: CreateCreativeLeaderboardReportDto =
  {
    ...mockCreateCreativeLeaderboardReportDto,
    name: 'Updated 2024 Leaderboard',
    description: 'Updated Report',
    filters: {
      ...mockCreateCreativeLeaderboardReportDto.filters,
    },
  };

export const expectedReportToSave: Report = {
  id: mockUUIDReturnValue,
  name: mockCreateAnalyticsReportDto.name,
  description: mockCreateAnalyticsReportDto.description as string,
  owner: mockUser,
  organizationId: mockUserDetails.organizationId,
  filter: mockReportFilterToSave,
  shared: false,
  reportType: mockCreateAnalyticsReportDto.reportType,
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  lastUpdatedByUser: mockUser,
  deleted: false,
};

export const expectedUpdatedReportFilterToSave: ReportFilter = {
  ...mockReportFilterToSave,
  filters: JSON.stringify({
    ...mockCreateAnalyticsReportDto.filters,
    placements: ['feed', 'story'],
    organizationId: mockUserDetails.organizationId,
  }),
};

export const expectedUpdatedLeaderboardReportFilterToSave: ReportFilter = {
  ...mockCreativeLeaderboardFilterToSave,
  filters: JSON.stringify({
    ...mockCreateCreativeLeaderboardReportDto.filters,
    organizationId: mockUserDetails.organizationId,
    adAccountIds: [mockFirstPlatformAccountId, mockSecondPlatformAccountId],
  }),
};

export const expectedLeaderboardReportToSave: Report = {
  id: mockUUIDReturnValue,
  name: mockCreateCreativeLeaderboardReportDto.name,
  description: mockCreateCreativeLeaderboardReportDto.description as string,
  owner: mockUser,
  organizationId: mockUserDetails.organizationId,
  filter: mockCreativeLeaderboardFilterToSave,
  shared: false,
  reportType: mockCreateCreativeLeaderboardReportDto.reportType,
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  lastUpdatedByUser: mockUser,
  deleted: false,
};

export const expectedUpdatedReportToSave: Report = {
  id: mockUUIDReturnValue,
  name: mockUpdateAnalyticsReportDto.name as string,
  description: mockUpdateAnalyticsReportDto.description as string,
  owner: mockUser,
  organizationId: mockUserDetails.organizationId,
  filter: expectedUpdatedReportFilterToSave,
  shared: false,
  reportType: mockCreateAnalyticsReportDto.reportType,
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  lastUpdatedByUser: mockUser,
  deleted: false,
};

export const expectedUpdatedLeaderboardReportToSave: Report = {
  id: mockUUIDReturnValue,
  name: mockUpdateLeaderboardReportDto.name as string,
  description: mockUpdateLeaderboardReportDto.description as string,
  owner: mockUser,
  organizationId: mockUserDetails.organizationId,
  filter: expectedUpdatedLeaderboardReportFilterToSave,
  shared: false,
  reportType: mockCreateCreativeLeaderboardReportDto.reportType,
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  lastUpdatedByUser: mockUser,
  deleted: false,
};

export const expectedReportToDelete = {
  ...expectedReportToSave,
  deleted: true,
};

const mockWorkspace = { id: mockWorkspaceId, name: 'dummy-name' };
export const mockGetWorkspacesByOrganizationIdResponse = {
  items: [mockWorkspace],
  totalCount: 1,
};

const mockFirstPlatformAccount: ReadPlatformAdAccountDto = {
  id: 1223,
  platform: Platform.FACEBOOK,
  platformAccountName: 'dummy-name-one',
  platformAccountId: mockFirstPlatformAccountId,
  dateCreated: '2021-10-01',
  processingCompleted: 1,
  processingCompletedDate: '2021-10-01',
  lastUpdated: '2021-10-01',
  lastSuccessfulProcessingDate: '2021-10-01',
  canAccess: 1,
};

const mockSecondPlatformAccount: ReadPlatformAdAccountDto = {
  id: 4567,
  platform: Platform.FACEBOOK,
  platformAccountName: 'dummy-name-two',
  platformAccountId: mockSecondPlatformAccountId,
  dateCreated: '2021-10-01',
  processingCompleted: 1,
  processingCompletedDate: '2021-10-01',
  lastUpdated: '2021-10-01',
  lastSuccessfulProcessingDate: '2021-10-01',
  canAccess: 1,
};

export const mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromiseResponse =
  {
    result: [mockFirstPlatformAccount, mockSecondPlatformAccount],
    pagination: {
      totalSize: 2,
    },
  };

export const mockUserAdAccountsAndWorkspacesDto: UserAdAccountsAndWorkspacesDto =
  {
    adAccounts:
      mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromiseResponse.result,
    workspaces: mockGetWorkspacesByOrganizationIdResponse.items,
  };

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const { workspaceIds, adAccountIds, ...baseFilters } =
  mockCreateAnalyticsReportDto.filters;

export const expectedAnalyticsReportDto: AnalyticsReportDto = {
  id: mockUUIDReturnValue,
  ...mockCreateAnalyticsReportDto,
  filtersVersion: 1,
  filters: {
    ...baseFilters,
    organizationId: mockUserDetails.organizationId,
    adAccounts:
      mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromiseResponse.result,
    workspaces: mockGetWorkspacesByOrganizationIdResponse.items,
  },
  userAccessStatus: {
    accountsAccessStatus: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
    workspacesAccessStatus: ENTITY_PERMISSIONS.ALLOW_PERMISSION,
  },
  dateCreated: mockDateValue,
  lastUpdated: mockDateValue,
  createdBy: {
    displayName: mockUser.displayName,
    firstName: mockUser.firstName,
    lastName: mockUser.lastName,
    id: mockUser.id,
    photo: mockUser.photo,
  },
};

export const expectedAnalyticsReportWithPartialAccessDto: AnalyticsReportDto = {
  ...expectedAnalyticsReportDto,
  filters: {
    ...expectedAnalyticsReportDto.filters,
    adAccounts: [mockFirstPlatformAccount],
    workspaces: [mockWorkspace],
  },
  userAccessStatus: {
    accountsAccessStatus: ENTITY_PERMISSIONS.PARTIAL_PERMISSION,
    workspacesAccessStatus: ENTITY_PERMISSIONS.PARTIAL_PERMISSION,
  },
};

export const expectedAnalyticsReportWithNoAccessDto: AnalyticsReportDto = {
  ...expectedAnalyticsReportDto,
  filters: {
    ...expectedAnalyticsReportDto.filters,
    adAccounts: [],
    workspaces: [],
  },
  userAccessStatus: {
    accountsAccessStatus: ENTITY_PERMISSIONS.DENY_PERMISSION,
    workspacesAccessStatus: ENTITY_PERMISSIONS.DENY_PERMISSION,
  },
};

export const mockGetAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto =
  {
    platform: Platform.FACEBOOK,
    workspaceIds: [mockWorkspaceId],
    adAccountIds: [mockFirstPlatformAccountId, mockSecondPlatformAccountId],
    filterType: FilterType.CAMPAIGN_IDENTIFIER,
    dateRange: {
      startDate: '2021-01-01',
      endDate: '2021-10-23',
    },
  };

export const mockAdwordsGetAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto =
  {
    platform: Platform.ADWORDS,
    filterType: FilterType.AD_TYPE,
  };

export const mockCampaignStats = [
  {
    campaign: { id: '8ejonwfnoie3', name: 'Fake Campaign Name' },
    impressions: 132444,
  },
  {
    campaign: { id: '***********', name: 'Really Fake Campaign Name' },
    impressions: 132444,
  },
];

export const mockUserAdAccountsAndWorkspaces = {
  adAccounts: [
    {
      platform: mockCreateAnalyticsReportDto.filters.platform,
      platformAccountName: 'VidMob 1 - Acquisition',
      platformAccountId: mockFirstPlatformAccountId,
      dateCreated: '2018-05-14T22:57:08.000Z',
      processingCompleted: 1,
      lastSuccessfulProcessingDate: '2024-02-06T02:01:13.000Z',
    },

    {
      platform: mockCreateAnalyticsReportDto.filters.platform,
      platformAccountName: 'OfficialVidMob',
      platformAccountId: mockSecondPlatformAccountId,
      dateCreated: '2022-11-06T08:00:25.000Z',
      processingCompleted: 1,
      lastSuccessfulProcessingDate: '2024-02-05T05:07:30.000Z',
    },
  ],
  workspaces: [
    {
      id: mockCreateAnalyticsReportDto.filters.workspaceIds[0],
      name: 'Another Workspace~',
      logoUrl: null,
      isPrimary: false,
      organizationId: mockOrganizationId,
      organizationName: 'Leandro Org Test',
      markets: [],
      brands: [Array],
      users: [],
    },
  ],
};

export const mockUserAdAccountIds = [
  mockFirstPlatformAccountId,
  mockSecondPlatformAccountId,
];

export const mockCriteriaGroups = [
  {
    name: 'Includes brand colors of interest',
    identifier: 'ABI_BRAND_COLOR',
    platform: 'FACEBOOK',
    criteriaType: 'PARTNER_OPTIONAL',
    isBestPractice: false,
    countPlatformMediaPass: 30,
    countPlatformMediaFail: 2726,
    parameters: {
      brandColors: ['black'],
    },
    applicabilityMediaTypes: ['IMAGE', 'VIDEO'],
    criteriaIds: [4314],
  },
  {
    name: 'Includes brand colors of interest',
    identifier: 'ABI_BRAND_COLOR',
    platform: 'FACEBOOK',
    criteriaType: 'PARTNER_OPTIONAL',
    isBestPractice: false,
    countPlatformMediaPass: 2397,
    countPlatformMediaFail: 576,
    parameters: {
      brandColors: ['blue'],
    },
    applicabilityMediaTypes: ['IMAGE', 'VIDEO'],
    criteriaIds: [2300],
  },
  {
    name: 'Includes brand logo OR brand name OR visual of product within the first {maxFirstBrandOrProductAppearance} seconds',
    identifier: 'ABI_BRAND_LINK',
    platform: 'FACEBOOK',
    criteriaType: 'PARTNER_OPTIONAL',
    isBestPractice: false,
    countPlatformMediaPass: 369,
    countPlatformMediaFail: 3517,
    parameters: {
      maxFirstBrandOrProductAppearance: 1,
    },
    applicabilityMediaTypes: ['IMAGE', 'VIDEO'],
    criteriaIds: [2455],
  },
];
