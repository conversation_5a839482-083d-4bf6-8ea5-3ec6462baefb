import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ReportSpendPermissionService } from './report-spend-permission.service';
import { KPIService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { OrganizationService } from '@vidmob/vidmob-organization-service-sdk';

describe('ReportSpendPermissionService', () => {
  let service: ReportSpendPermissionService;
  let kpiService: KPIService;
  let organizationService: OrganizationService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportSpendPermissionService,
        {
          provide: KPIService,
          useValue: {
            getKpiByIdAsPromise: jest.fn(),
            getGenericKpisByIdsAsPromise: jest.fn(),
          },
        },
        {
          provide: OrganizationService,
          useValue: {
            findOneAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ReportSpendPermissionService>(
      ReportSpendPermissionService,
    );
    kpiService = module.get<KPIService>(KPIService);
    organizationService = module.get<OrganizationService>(OrganizationService);
  });

  describe('validateOrganizationAccessToSpendKpi', () => {
    it('should throw ForbiddenException if KPI is SPEND and spend is not enabled', async () => {
      (kpiService.getKpiByIdAsPromise as jest.Mock).mockResolvedValue({
        result: { format: 'SPEND' },
      });
      (organizationService.findOneAsPromise as jest.Mock).mockResolvedValue({
        result: { spendEnabled: false },
      });

      await expect(
        service.validateOrganizationAccessToSpendKpi(
          'kpi123',
          'facebook',
          'org456',
        ),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should not throw if KPI is SPEND and spend is enabled', async () => {
      (kpiService.getKpiByIdAsPromise as jest.Mock).mockResolvedValue({
        result: { format: 'SPEND' },
      });
      (organizationService.findOneAsPromise as jest.Mock).mockResolvedValue({
        result: { spendEnabled: true },
      });

      await expect(
        service.validateOrganizationAccessToSpendKpi(
          'kpi123',
          'facebook',
          'org456',
        ),
      ).resolves.toBeUndefined();
    });

    it('should not throw if KPI is not SPEND', async () => {
      (kpiService.getKpiByIdAsPromise as jest.Mock).mockResolvedValue({
        result: { format: 'PERCENTAGE' },
      });
      (organizationService.findOneAsPromise as jest.Mock).mockResolvedValue({
        result: { spendEnabled: false },
      });

      await expect(
        service.validateOrganizationAccessToSpendKpi(
          'kpi123',
          'facebook',
          'org456',
        ),
      ).resolves.toBeUndefined();
    });
  });

  describe('getOrganizationAccessToSpendKpi', () => {
    it('should return spendEnabled status from organization', async () => {
      (organizationService.findOneAsPromise as jest.Mock).mockResolvedValue({
        result: { spendEnabled: true },
      });

      const result = await service.getOrganizationAccessToSpendKpi('org456');
      expect(result).toBe(true);
    });

    it('should throw ForbiddenException if organization not found', async () => {
      (organizationService.findOneAsPromise as jest.Mock).mockResolvedValue({
        result: null,
      });

      await expect(
        service.getOrganizationAccessToSpendKpi('org456'),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should return false if spendEnabled is not set', async () => {
      (organizationService.findOneAsPromise as jest.Mock).mockResolvedValue({
        result: {},
      });

      const result = await service.getOrganizationAccessToSpendKpi('org456');
      expect(result).toBe(false);
    });
  });
});
