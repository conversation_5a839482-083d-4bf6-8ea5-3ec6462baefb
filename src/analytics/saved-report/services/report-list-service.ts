import {
  BadRequestException,
  Injectable,
  Logger,
  NotImplementedException,
} from '@nestjs/common';
import { ReportsService } from '../../../reports/reports.service';
import { UserDetailsDto } from '../../dto/user-details.dto';
import { GetAnalyticsReportFilterOptionsDto } from '../dto/get-analytics-report-options.dto';
import { AnalyticsReportWithNoFiltersDto } from '../dto/analytics-report.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { OrganizationUserRoles } from '../../../constants/role.constants';
import { OrganizationUserService } from '../../../account-management/organization/organization-user/organization-user.service';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { AnalyticsReportType } from '../model/analytics-report';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { SortOrder } from '../../../reports/model/sort-order';
import { SortBy } from '../../../reports/reports-enums';
import { DEFAULT_PAGINATION_OPTIONS } from '../../constants/constants';
import { MySQLClientService } from '../../../common/services/mysql-client-wrapper';
import { AnalyticsReportConverter } from '../util/analytics-report-converter';
import { ScoringReportType } from '../../../reports/model/report';
import { QueryAndBindings } from '../../../common/types/query-and-bindings';
import { generateParameterString } from '../../../utils/sql-utils';
import { InjectRepository } from '@nestjs/typeorm';
import { Report } from '../../../entities/report.entity';
import { Repository } from 'typeorm';

/*
 this is a "V2" of AnalyticsReportService.

 the difference between ReportListService and AnalyticsReportService is that
 1. analyticsReportService also contains service functions to get report filters, and general report CRUD
 2. reportListService just contains queries to support listing reports and report landing page
 3. functions to get lists of reports do not use typeorm. This is to simplify the code and to
 account for complexity in report access, filtering and sorting

 the intention is that ReportListService will replace the functions in AnalyticsReportService that
 support report lists. In theory they both will support the FE analytics report page as of 01-22-24.
 But additional features should be added to this class and not AnalyticsReportService,

 you should consider AnalyticsReportService Depricated with respect to the report list
 */

type DateRange = {
  startDate: string;
  endDate: string;
};

type ReportFilters = {
  reportType?: ScoringReportType[] | AnalyticsReportType[];
  dateCreated?: DateRange;
  lastUpdated?: DateRange;
  createdBy?: number[];
  channels?: Platform[];
  workspaces?: number[];
  adAccounts?: string[];
};

@Injectable()
export class ReportListService {
  private readonly logger = new Logger(ReportsService.name);

  constructor(
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly organizationUserService: OrganizationUserService,
    private readonly mysqlClientService: MySQLClientService,
    private readonly analyticsReportConverter: AnalyticsReportConverter,
  ) {}

  private async getIsUserOrgAdmin(
    organizationId: string,
    userId: number,
  ): Promise<boolean> {
    const response = await this.organizationUserService.getUserInOrganization(
      organizationId,
      userId,
    );

    return response.result.roles.some(
      (role) => role.identifier === OrganizationUserRoles.ORG_ADMIN,
    );
  }

  private generateSelects(
    accountsToFilter: string[],
    workspacesToFilter: number[],
  ): QueryAndBindings {
    const baseSelects =
      ' SELECT\n' +
      '        r.id,\n' +
      '        r.name,\n' +
      '        r.description,\n' +
      '        r.report_type as reportType,\n' +
      '        rcm.channel as platform,\n' +
      '        (SELECT JSON_ARRAYAGG(JSON_OBJECT("id", partner_inner.id, "name", partner_inner.name))\n' +
      '         FROM report_workspace_map rwm_inner\n' +
      '         JOIN partner partner_inner ON rwm_inner.workspace_id = partner_inner.id\n' +
      '         WHERE rwm_inner.report_id = r.id) AS workspaces,\n' +
      '        (SELECT JSON_ARRAYAGG(JSON_OBJECT("id", paa_inner.platform_account_id, "name", paa_inner.platform_account_name))\n' +
      '         FROM report_platform_ad_account_map rpam_inner\n' +
      '         JOIN platform_ad_account paa_inner ON rpam_inner.platform_ad_account_id = paa_inner.id\n' +
      '         WHERE rpam_inner.report_id = r.id) AS adAccounts,\n' +
      '        JSON_OBJECT(\n' +
      "            'id', person.id,\n" +
      "            'displayName', person.display_name,\n" +
      "            'firstName', person.first_name,\n" +
      "            'lastName', person.last_name,\n" +
      "            'photo', person.photo,\n" +
      "            'deactivatedUser', CASE\n" +
      '                                   WHEN pom.person_id IS NULL OR pom.organization_id IS NULL THEN 1\n' +
      '                                   ELSE 0\n' +
      '                               END\n' +
      '        ) AS owner,\n' +
      '        r.last_updated as lastUpdated,\n' +
      '        r.date_created as dateCreated,\n' +
      '        person.display_name as person_display_name, \n';

    const accessSelects = this.generateAccessCheckSelects(
      accountsToFilter,
      workspacesToFilter,
    );

    return {
      query: baseSelects + accessSelects.query,
      bindings: [...accessSelects.bindings],
    };
  }

  private generateAccessCheckSelects(
    accountsToFilter: string[],
    workspacesToFilter: number[],
  ): QueryAndBindings {
    const accountsParamString = generateParameterString(accountsToFilter);
    const workspacesParamString = generateParameterString(workspacesToFilter);

    const selects =
      '        COUNT(DISTINCT rpam.platform_ad_account_id) AS total_ad_accounts,\n' +
      '        COUNT(DISTINCT IF(\n' +
      '          paa.platform_account_id IN (' +
      accountsParamString +
      '),\n' +
      '          rpam.platform_ad_account_id,\n' +
      '          NULL\n' +
      '        )) AS matched_ad_accounts, \n ' +
      '        COUNT(DISTINCT rwm.workspace_id) AS total_workspaces,\n' +
      '        COUNT(DISTINCT IF(\n' +
      '          rwm.workspace_id IN (' +
      workspacesParamString +
      '),\n' +
      '          rwm.workspace_id,\n' +
      '          NULL\n' +
      '        )) AS matched_workspaces \n';

    return {
      query: selects,
      bindings: [...accountsToFilter, ...workspacesToFilter],
    };
  }

  private generateFromJoinedQuery(
    accessWhere: QueryAndBindings,
    filterWhere: QueryAndBindings,
    searchWhere: QueryAndBindings,
    accessHaving: QueryAndBindings,
  ): QueryAndBindings {
    const wheresQuery = [
      accessWhere.query,
      filterWhere.query,
      searchWhere.query,
    ]
      .filter((wq) => wq.length > 1)
      .join(' AND ');

    const whereBindings = [
      ...accessWhere.bindings,
      ...filterWhere.bindings,
      ...searchWhere.bindings,
    ];

    const query =
      ' FROM\n' +
      '        report r\n' +
      '    INNER JOIN\n' +
      '        report_channel_map rcm ON rcm.report_id = r.id\n' +
      '    INNER JOIN\n' +
      '        report_workspace_map rwm ON rwm.report_id = r.id\n' +
      '    INNER JOIN\n' +
      '        partner partner ON rwm.workspace_id = partner.id\n' +
      '    INNER JOIN\n' +
      '        report_platform_ad_account_map rpam ON rpam.report_id = r.id\n' +
      '    INNER JOIN\n' +
      '        platform_ad_account paa ON rpam.platform_ad_account_id = paa.id\n' +
      '    INNER JOIN\n' +
      '        report_filter rf ON r.filter_id = rf.id\n' +
      '    INNER JOIN\n' +
      '        person person ON r.owner_id = person.id\n' +
      '    LEFT JOIN person_organization_map pom ON\n' +
      '        pom.person_id = person.id AND\n' +
      '                  pom.organization_id = r.organization_id\n' +
      'WHERE ' +
      wheresQuery +
      '    GROUP BY\n' +
      '        r.id, r.name, r.description, r.report_type' +
      ` ${accessHaving.query}`;

    return {
      query: query,
      bindings: [...whereBindings, ...accessHaving.bindings],
    };
  }

  private generateFilterWhereClauses(filters: ReportFilters): QueryAndBindings {
    const filterWhereClauses = [];
    const filterWhereBindings = [];

    if (filters.reportType) {
      filterWhereClauses.push(
        `r.report_type IN (${generateParameterString(filters.reportType)})`,
      );
      filterWhereBindings.push(...filters.reportType);
    }

    if (filters.dateCreated) {
      filterWhereClauses.push(
        'DATE(r.date_created) >= DATE(?) AND DATE(r.date_created) <= DATE(?)',
      );
      filterWhereBindings.push(
        filters.dateCreated.startDate,
        filters.dateCreated.endDate,
      );
    }

    if (filters.lastUpdated) {
      filterWhereClauses.push(
        'DATE(r.last_updated) >= DATE(?) AND DATE(r.last_updated) <= DATE(?)',
      );
      filterWhereBindings.push(
        filters.lastUpdated.startDate,
        filters.lastUpdated.endDate,
      );
    }

    if (filters.createdBy && filters.createdBy.length > 0) {
      filterWhereClauses.push(
        `r.owner_id IN (${generateParameterString(filters.createdBy)})`,
      );
      filterWhereBindings.push(...filters.createdBy);
    }

    if (filters.channels) {
      filterWhereClauses.push(
        `rcm.channel IN (${generateParameterString(filters.channels)})`,
      );
      filterWhereBindings.push(...filters.channels);
    }

    if (filters.adAccounts) {
      filterWhereClauses.push(
        `r.id IN (SELECT report_id FROM report_platform_ad_account_map JOIN platform_ad_account paa on paa.id = report_platform_ad_account_map.platform_ad_account_id WHERE paa.platform_account_id IN (${generateParameterString(
          filters.adAccounts,
        )}))`,
      );
      filterWhereBindings.push(...filters.adAccounts);
    }

    if (filters.workspaces) {
      filterWhereClauses.push(
        `rwm.workspace_id IN (${generateParameterString(filters.workspaces)})`,
      );
      filterWhereBindings.push(...filters.workspaces);
    }

    return {
      query: filterWhereClauses.join(' AND '),
      bindings: filterWhereBindings,
    };
  }

  /*
    the scoring reports landing page uses the partner Id to determine if a report is included in the list
    the analytics reports landing page uses the organization to determine if a report is included

    scoring reports Have a partner id and usually not an organization id, analytics reports are the opposite

    when constructing queries for analytics or scoring reports, you should use one of generateAnalyticsAccessWhereClauses() or
    generateScoringAccessWhereClauses but not both
   */
  private generateAnalyticsAccessWhereClauses(
    organizationId: string,
  ): QueryAndBindings {
    const query = 'r.organization_id = ? AND r.deleted = false';
    return {
      query: query,
      bindings: [organizationId],
    };
  }

  private generateScoringAccessWhereClauses(): QueryAndBindings {
    throw new NotImplementedException();
  }

  // Only the workspace part would apply to scoring,
  private generateAccessHavingClauses(): QueryAndBindings {
    const queryString = `HAVING
            total_ad_accounts = matched_ad_accounts
            AND total_workspaces = matched_workspaces`;
    return {
      query: queryString,
      bindings: [],
    };
  }

  private generateSearchWhere(
    searchTerm: string | undefined,
  ): QueryAndBindings {
    return {
      query: searchTerm ? `(r.name LIKE ? OR r.description LIKE ?)` : '',
      bindings: searchTerm ? [searchTerm, searchTerm] : [],
    };
  }

  private generateSortClause(sortBy: SortBy, sortOrder: SortOrder): string {
    switch (sortBy) {
      case SortBy.DateCreated:
        return `ORDER BY dateCreated ${sortOrder} `;
      case SortBy.LastUpdated:
        return `ORDER BY lastUpdated ${sortOrder} `;
      case SortBy.Name:
        return `ORDER BY name ${sortOrder} `;
      case SortBy.CreatedBy:
        return `ORDER BY person_display_name ${sortOrder} `;
      case SortBy.ReportType:
        return `ORDER BY reportType ${sortOrder} `;
      case SortBy.Channel:
        return `ORDER BY CASE WHEN platform = 'ADWORDS' THEN 'GOOGLE_ADWORDS' ELSE platform END ${sortOrder} `;
      case SortBy.Workspace:
        return `ORDER BY JSON_EXTRACT(JSON_EXTRACT(workspaces, '$[0]'), '$.name') ${sortOrder} `;
      case SortBy.AdAccount:
        return `ORDER BY JSON_EXTRACT(JSON_EXTRACT(adAccounts, '$[0]'), '$.name') ${sortOrder} `;
      default:
        return '';
    }
  }

  private generateLimitOffsetClause(
    limit: number | undefined,
    offset: number | undefined,
  ): QueryAndBindings {
    return {
      query: `LIMIT ? OFFSET ?`,
      bindings: [
        limit ?? DEFAULT_PAGINATION_OPTIONS.perPage,
        offset ?? DEFAULT_PAGINATION_OPTIONS.offset,
      ],
    };
  }

  private generateDataQuery(
    selects: QueryAndBindings,
    baseFilteredQuery: QueryAndBindings,
    sortQuery: string,
    limitQuery: QueryAndBindings,
  ): QueryAndBindings {
    const dataQuery = `
    ${selects.query}
    ${baseFilteredQuery.query}
    ${sortQuery}
    ${limitQuery.query};
  `;

    return {
      query: dataQuery,
      bindings: [
        ...selects.bindings,
        ...baseFilteredQuery.bindings,
        ...limitQuery.bindings,
      ],
    };
  }

  private generateCountQuery(
    baseFilteredQuery: QueryAndBindings,
    adAccountsUserHasAccess: string[],
    workspacesUserHasAccess: number[],
  ): QueryAndBindings {
    const adAccountsParamString = generateParameterString(
      adAccountsUserHasAccess,
    );
    const workspacesParamString = generateParameterString(
      workspacesUserHasAccess,
    );

    const accessCheckSelects = this.generateAccessCheckSelects(
      adAccountsUserHasAccess,
      workspacesUserHasAccess,
    );
    const countQuery = `
    SELECT COUNT(*) AS totalCount
    FROM (
      SELECT r.id,
        ${accessCheckSelects.query}
        ${baseFilteredQuery.query}
    ) AS count_groups
  `;

    return {
      query: countQuery,
      bindings: [...accessCheckSelects.bindings, ...baseFilteredQuery.bindings],
    };
  }

  private generateReportFilterRequestFromReportRequest(
    getAnalyticsReportOptions: GetAnalyticsReportFilterOptionsDto,
    defaultReportTypes: ScoringReportType[] | AnalyticsReportType[],
  ): ReportFilters {
    return {
      reportType: getAnalyticsReportOptions.types ?? defaultReportTypes,
      dateCreated: getAnalyticsReportOptions.dateCreated,
      lastUpdated: getAnalyticsReportOptions.lastUpdated,
      createdBy: getAnalyticsReportOptions.createdBy,
      channels: getAnalyticsReportOptions.channels,
      workspaces: getAnalyticsReportOptions.workspaces,
      adAccounts: getAnalyticsReportOptions.adAccounts,
    };
  }

  private async getWorkspacesAndAccountsWithAccess(
    userDetails: UserDetailsDto,
  ): Promise<{
    adAccountsUserHasAccess: string[];
    workspacesUserHasAccess: number[];
  }> {
    const userAdAccountsAndWorkspaces =
      await this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
        userDetails,
      );

    return {
      adAccountsUserHasAccess: userAdAccountsAndWorkspaces.adAccounts.map(
        (adAccount) => adAccount.platformAccountId,
      ),
      workspacesUserHasAccess: userAdAccountsAndWorkspaces.workspaces.map(
        (workspace) => workspace.id,
      ),
    };
  }
  async getAllAnalyticsReports(
    userDetails: UserDetailsDto,
    getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto,
  ): Promise<PaginatedResultArray<AnalyticsReportWithNoFiltersDto>> {
    try {
      const isUserOrgAdmin = await this.getIsUserOrgAdmin(
        userDetails.organizationId,
        userDetails.userId,
      );

      const { adAccountsUserHasAccess, workspacesUserHasAccess } =
        await this.getWorkspacesAndAccountsWithAccess(userDetails);

      if (
        !isUserOrgAdmin &&
        (!adAccountsUserHasAccess.length || !workspacesUserHasAccess.length)
      ) {
        return new PaginatedResultArray([], 0);
      }

      const baseFilteredQuery = this.generateFromJoinedQuery(
        this.generateAnalyticsAccessWhereClauses(userDetails.organizationId),
        this.generateFilterWhereClauses(
          this.generateReportFilterRequestFromReportRequest(
            getAnalyticsReportsOptions,
            Object.values(AnalyticsReportType),
          ),
        ),
        this.generateSearchWhere(getAnalyticsReportsOptions.searchTerm),
        this.generateAccessHavingClauses(),
      );

      const countQueryAndBindings = this.generateCountQuery(
        baseFilteredQuery,
        adAccountsUserHasAccess,
        workspacesUserHasAccess,
      );

      const dataQueryAndBindings = this.generateDataQuery(
        this.generateSelects(adAccountsUserHasAccess, workspacesUserHasAccess),
        baseFilteredQuery,
        this.generateSortClause(
          getAnalyticsReportsOptions.sortBy,
          getAnalyticsReportsOptions.sortOrder,
        ),
        this.generateLimitOffsetClause(
          getAnalyticsReportsOptions.perPage,
          getAnalyticsReportsOptions.offset,
        ),
      );

      this.logger.debug(`Generated SQL query: ${dataQueryAndBindings.query}`);
      this.logger.debug(
        `Query bindings: ${JSON.stringify(dataQueryAndBindings.bindings)}`,
      );

      const [result, totalCountResult] = await Promise.all([
        this.mysqlClientService.executeRawQuery(
          dataQueryAndBindings.query,
          dataQueryAndBindings.bindings,
        ),
        this.mysqlClientService.executeRawQuery(
          countQueryAndBindings.query,
          countQueryAndBindings.bindings,
        ),
      ]);

      const formattedResult = this.analyticsReportConverter.convertAll(result);

      const count =
        totalCountResult.length > 0
          ? Number(totalCountResult[0].totalCount)
          : 0;

      return new PaginatedResultArray(formattedResult, count);
    } catch (error) {
      const baseErrorMessage = `Failed to get analytics reports for user ${userDetails.userId} in organization ${userDetails.organizationId}.`;
      this.logger.error(`${baseErrorMessage}. ${error}`);
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  async getAllScoringReports(): Promise<PaginatedResultArray<any>> {
    throw new NotImplementedException();
  }
}
