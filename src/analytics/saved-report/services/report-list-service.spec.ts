import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationUserService } from '../../../account-management/organization/organization-user/organization-user.service';
import { OrganizationUserRoles } from '../../../constants/role.constants';
import { AnalyticsUserService } from '../../analytics-user-service/analytics-user-service';
import { ReportListService } from './report-list-service';
import {
  createStubAnalyticsReportFilterOptions,
  createStubReadPlatformAdAcccountDto,
  createStubUserAdAccountsAndWorkspacedDto,
  createStubUserDetails,
  createStubWorkspace,
} from '../test-utils/test-object-factories';
import {
  AnalyticsReportType,
  UserAdAccountsAndWorkspacesDto,
} from '../model/analytics-report';
import { MySQLClientService } from '../../../common/services/mysql-client-wrapper';
import { AnalyticsReportConverter } from '../util/analytics-report-converter';
import { normalizeString } from '../test-utils/string-helpers';
import { Platform } from '@vidmob/vidmob-nestjs-common';
import { DEFAULT_PAGINATION_OPTIONS } from '../../constants/constants';
import { SortBy } from '../../../reports/reports-enums';
import { SortOrder } from '../../../reports/model/sort-order';

// mocks
const mockGetUserInOrganization = jest.fn();

// stubs
const defaultWorkspaceAndAccountResponse =
  createStubUserAdAccountsAndWorkspacedDto({});
const defaultUserDetails = createStubUserDetails({});
const defaultAnalyticsReportFilterOptions =
  createStubAnalyticsReportFilterOptions({});

const buildTestModule = async ({
  userRole = OrganizationUserRoles.ORG_STANDARD,
  addAccountsAndWorkspacesResponse = defaultWorkspaceAndAccountResponse,
  mockMySqlClient = jest.fn(),
}: {
  userRole?: OrganizationUserRoles;
  addAccountsAndWorkspacesResponse?: UserAdAccountsAndWorkspacesDto;
  mockMySqlClient?: any;
}) => {
  const module: TestingModule = await Test.createTestingModule({
    providers: [
      ReportListService,
      {
        provide: OrganizationUserService,
        useValue: {
          getUserInOrganization: mockGetUserInOrganization.mockResolvedValue({
            result: {
              roles: [{ identifier: userRole }],
            },
          }),
        },
      },
      {
        provide: AnalyticsUserService,
        useValue: {
          fetchUserAdAccountsAndWorkspaces: jest
            .fn()
            .mockResolvedValue(addAccountsAndWorkspacesResponse),
        },
      },
      {
        provide: MySQLClientService,
        useValue: {
          executeRawQuery: mockMySqlClient.mockResolvedValue([]),
        },
      },
      {
        provide: AnalyticsReportConverter,
        useValue: {
          convertAll: jest.fn().mockResolvedValue([]),
        },
      },
    ],
  }).compile();

  return module.get<ReportListService>(ReportListService);
};

describe('ReportListService', () => {
  describe('getAllAnalyticsReports', () => {
    describe('user is not org admin', () => {
      it('returns an empty result if user does not have access to accounts', async () => {
        const mockMySqlClient = jest.fn();
        const reportListService = await buildTestModule({
          addAccountsAndWorkspacesResponse:
            createStubUserAdAccountsAndWorkspacedDto({ adAccounts: [] }),
          mockMySqlClient,
        });
        const result = await reportListService.getAllAnalyticsReports(
          defaultUserDetails,
          defaultAnalyticsReportFilterOptions,
        );

        expect(result.totalCount).toEqual(0);
        expect(result.items.length).toEqual(0);

        expect(mockMySqlClient).not.toHaveBeenCalled();
      });
      it('returns an empty result if user does not have access to workspaces', async () => {
        const mockMySqlClient = jest.fn();
        const reportListService = await buildTestModule({
          addAccountsAndWorkspacesResponse:
            createStubUserAdAccountsAndWorkspacedDto({ workspaces: [] }),
          mockMySqlClient,
        });

        const result = await reportListService.getAllAnalyticsReports(
          defaultUserDetails,
          defaultAnalyticsReportFilterOptions,
        );

        expect(result.totalCount).toEqual(0);
        expect(result.items.length).toEqual(0);
        expect(mockMySqlClient).not.toHaveBeenCalled();
      });
      describe('user is org admin', () => {
        it('still queries for reports if admin does not have access to workspaces or accounts', async () => {
          const mockMySqlClient = jest.fn();
          const reportsListService = await buildTestModule({
            userRole: OrganizationUserRoles.ORG_ADMIN,
            mockMySqlClient,
          });

          const result = await reportsListService.getAllAnalyticsReports(
            defaultUserDetails,
            defaultAnalyticsReportFilterOptions,
          );

          expect(result.totalCount).toEqual(0);
          expect(mockMySqlClient).toHaveBeenCalled();
        });
      });
    });
    describe('query', () => {
      const checkContainsTwoStr = (query: string, expected: string) => {
        expect(
          normalizeString(query[0][0]).includes(normalizeString(expected)),
        );
      };

      const bindingsContain = (
        bindings: any[],
        valuesToCheck: any[],
        numInstances: number,
      ) => {
        expect(
          bindings.filter((p: any) => valuesToCheck.includes(p)).length,
        ).toEqual(numInstances);
      };

      describe('selects', () => {
        it('generates the correct selects in the grouped_results sub query', async () => {
          const mockMySqlClient = jest.fn();
          const reportListService = await buildTestModule({ mockMySqlClient });
          const result = await reportListService.getAllAnalyticsReports(
            defaultUserDetails,
            defaultAnalyticsReportFilterOptions,
          );

          const expectedSelects =
            ' SELECT\n' +
            '        r.id,\n' +
            '        r.name,\n' +
            '        r.description,\n' +
            '        r.report_type as reportType,\n' +
            '        rcm.channel as platform,\n' +
            '        (SELECT JSON_ARRAYAGG(JSON_OBJECT("id", partner_inner.id, "name", partner_inner.name))\n' +
            '         FROM report_workspace_map rwm_inner\n' +
            '         JOIN partner partner_inner ON rwm_inner.workspace_id = partner_inner.id\n' +
            '         WHERE rwm_inner.report_id = r.id) AS workspaces,\n' +
            '        (SELECT JSON_ARRAYAGG(JSON_OBJECT("id", paa_inner.platform_account_id, "name", paa_inner.platform_account_name))\n' +
            '         FROM report_platform_ad_account_map rpam_inner\n' +
            '         JOIN platform_ad_account paa_inner ON rpam_inner.platform_ad_account_id = paa_inner.id\n' +
            '         WHERE rpam_inner.report_id = r.id) AS adAccounts,\n' +
            '        JSON_OBJECT(\n' +
            "            'id', person.id,\n" +
            "            'displayName', person.display_name,\n" +
            "            'firstName', person.first_name,\n" +
            "            'lastName', person.last_name,\n" +
            "            'photo', person.photo,\n" +
            "            'deactivatedUser', CASE\n" +
            '                                   WHEN pom.person_id IS NULL OR pom.organization_id IS NULL THEN 1\n' +
            '                                   ELSE 0\n' +
            '                               END\n' +
            '        ) AS owner,\n' +
            '        r.last_updated as lastUpdated,\n' +
            '        r.date_created as dateCreated,\n' +
            '        person.display_name as person_display_name, \n';
          '        COUNT(DISTINCT rpam.platform_ad_account_id) AS total_ad_accounts,\n' +
            '        COUNT(DISTINCT IF(\n' +
            '          paa.platform_account_id IN (';
          '),\n' +
            '          rpam.platform_ad_account_id,\n' +
            '          NULL\n' +
            '        )) AS matched_ad_accounts, \n ' +
            '        COUNT(DISTINCT rwm.workspace_id) AS total_workspaces,\n' +
            '        COUNT(DISTINCT IF(\n' +
            '          rwm.workspace_id IN (' +
            '),\n' +
            '          rwm.workspace_id,\n' +
            '          NULL\n' +
            '        )) AS matched_workspaces \n';

          const calls = mockMySqlClient.mock.calls;
          expect(
            normalizeString(calls[0][0]).includes(
              normalizeString(expectedSelects),
            ),
          ).toBeTruthy();
        });
      });
      describe('baseJoinedAndFilteredQuery', () => {
        it('generates the correct joins', async () => {
          const mockMySqlClient = jest.fn();
          const reportListService = await buildTestModule({ mockMySqlClient });
          const result = await reportListService.getAllAnalyticsReports(
            defaultUserDetails,
            defaultAnalyticsReportFilterOptions,
          );

          const expectedjoins =
            ' FROM\n' +
            '        report r\n' +
            '    INNER JOIN\n' +
            '        report_channel_map rcm ON rcm.report_id = r.id\n' +
            '    INNER JOIN\n' +
            '        report_workspace_map rwm ON rwm.report_id = r.id\n' +
            '    INNER JOIN\n' +
            '        partner partner ON rwm.workspace_id = partner.id\n' +
            '    INNER JOIN\n' +
            '        report_platform_ad_account_map rpam ON rpam.report_id = r.id\n' +
            '    INNER JOIN\n' +
            '        platform_ad_account paa ON rpam.platform_ad_account_id = paa.id\n' +
            '    INNER JOIN\n' +
            '        report_filter rf ON r.filter_id = rf.id\n' +
            '    INNER JOIN\n' +
            '        person person ON r.owner_id = person.id\n' +
            '    LEFT JOIN person_organization_map pom ON\n' +
            '        pom.person_id = person.id AND\n' +
            '                  pom.organization_id = r.organization_id\n';

          const calls = mockMySqlClient.mock.calls;
          checkContainsTwoStr(calls[0][0], expectedjoins);
        });

        it('includes the access where clauses', async () => {
          const mockMySqlClient = jest.fn();
          const reportListService = await buildTestModule({ mockMySqlClient });
          const result = await reportListService.getAllAnalyticsReports(
            createStubUserDetails({
              organizationId: 'i-am-the-organization-id',
            }),
            defaultAnalyticsReportFilterOptions,
          );

          const expectedWhereClause =
            'r.organization_id = ? AND r.deleted = false';

          const calls = mockMySqlClient.mock.calls;
          checkContainsTwoStr(calls[0][0], expectedWhereClause);
          bindingsContain(calls[0][1], ['i-am-the-organization-id'], 1);
        });
        describe('filters', () => {
          it('adds a report type filter', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                types: [
                  AnalyticsReportType.ELEMENT_IMPACT,
                  AnalyticsReportType.MEDIA_IMPACT,
                ],
              }),
            );

            const expectedReportTypeWhere = 'r.report_type IN (?, ?)';

            const calls = mockMySqlClient.mock.calls;
            checkContainsTwoStr(calls[0][0], expectedReportTypeWhere);
            bindingsContain(
              calls[0][1],
              [
                AnalyticsReportType.MEDIA_IMPACT,
                AnalyticsReportType.ELEMENT_IMPACT,
              ],
              2,
            );
          });
          it('adds the date created filter', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                dateCreated: {
                  startDate: '2024-01-01',
                  endDate: '2024-01-01',
                },
              }),
            );

            const expectedCreatedDateWhere =
              'DATE(r.date_created) >= DATE(?) AND DATE(r.date_created) <= DATE(?)';

            const calls = mockMySqlClient.mock.calls;
            checkContainsTwoStr(calls[0][0], expectedCreatedDateWhere);
            bindingsContain(calls[0][1], ['2024-01-01', '2024-01-01'], 2);
          });
          it('adds the last updated filter', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                lastUpdated: {
                  startDate: '2024-01-01',
                  endDate: '2024-01-01',
                },
              }),
            );

            const expectedLastUpdatedWhere =
              'DATE(r.last_updated) >= DATE(?) AND DATE(r.last_updated) <= DATE(?)';

            const calls = mockMySqlClient.mock.calls;
            checkContainsTwoStr(calls[0][0], expectedLastUpdatedWhere);
            bindingsContain(calls[0][1], ['2024-02-01', '2024-01-01'], 2);
          });
          it('adds the createdBy filter', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                createdBy: [111, 222],
              }),
            );

            const expectedCreatedByWhere = 'r.owner_id IN (?, ?)';

            const calls = mockMySqlClient.mock.calls;
            checkContainsTwoStr(calls[0][0], expectedCreatedByWhere);
            bindingsContain(calls[0][1], [111, 222], 2);
          });
          it('adds the channels filter', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                channels: [Platform.FACEBOOK, Platform.TIKTOK],
              }),
            );

            const expectedChannelWhere =
              'r.id IN (SELECT report_id FROM report_channel_map WHERE channel IN (?, ?))';

            const calls = mockMySqlClient.mock.calls;
            checkContainsTwoStr(calls[0][0], expectedChannelWhere);
            bindingsContain(
              calls[0][1],
              [Platform.FACEBOOK, Platform.TIKTOK],
              2,
            );
          });
        });
        describe('search', () => {
          it('includes the search where clause', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                searchTerm: 'my super awesome report',
              }),
            );

            const expectedSearchTermWhere =
              '(r.name LIKE ? OR r.description LIKE ?)';

            const calls = mockMySqlClient.mock.calls;
            checkContainsTwoStr(calls[0][0], expectedSearchTermWhere);
            bindingsContain(calls[0][1], ['my super awesome report'], 2);
          });
        });
        it('includes the workspace and account having clauses', async () => {
          const mockMySqlClient = jest.fn();
          const reportListService = await buildTestModule({
            addAccountsAndWorkspacesResponse: {
              adAccounts: [
                createStubReadPlatformAdAcccountDto({
                  platformAccountId: '111',
                }),
                createStubReadPlatformAdAcccountDto({
                  platformAccountId: '222',
                }),
                createStubReadPlatformAdAcccountDto({
                  platformAccountId: '333',
                }),
              ],
              workspaces: [
                createStubWorkspace({ id: 123 }),
                createStubWorkspace({ id: 456 }),
                createStubWorkspace({ id: 789 }),
              ],
            },
            mockMySqlClient,
          });
          const result = await reportListService.getAllAnalyticsReports(
            defaultUserDetails,
            defaultAnalyticsReportFilterOptions,
          );

          const expectedHaving = `HAVING
            total_ad_accounts = matched_ad_accounts
            AND total_workspaces = matched_workspaces`;

          const calls = mockMySqlClient.mock.calls;
          checkContainsTwoStr(calls[0][0], expectedHaving);
        });
        describe('pagedAndSortedQuery', () => {
          it('includes the limit and offset when provided', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              createStubAnalyticsReportFilterOptions({
                offset: 420,
                perPage: 69,
              }),
            );

            const expectedPagedQuery = 'LIMIT ? OFFSET ?';

            const calls = mockMySqlClient.mock.calls;
            normalizeString(calls[0][0]).includes(expectedPagedQuery);
            bindingsContain(calls[0][1], [420, 69], 2);
          });
          it('uses the default limit and offset', async () => {
            const mockMySqlClient = jest.fn();
            const reportListService = await buildTestModule({
              mockMySqlClient,
            });
            const result = await reportListService.getAllAnalyticsReports(
              defaultUserDetails,
              defaultAnalyticsReportFilterOptions,
            );

            const expectedPagedQeury = 'LIMIT ? OFFSET ?';

            const calls = mockMySqlClient.mock.calls;
            normalizeString(calls[0][0]).includes(expectedPagedQeury);
            bindingsContain(
              calls[0][1],
              [
                DEFAULT_PAGINATION_OPTIONS.offset,
                DEFAULT_PAGINATION_OPTIONS.perPage,
              ],
              2,
            );
          });
          describe('sorted', () => {
            it('sort by date created', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.DateCreated,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY dateCreated DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });
            it('sort by last updated', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.LastUpdated,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY lastUpdated DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });
            it('sort by report name', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.Name,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY name DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });

            it('sort by created by name', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.CreatedBy,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY person_display_name DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });
            it('sort by report type', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.ReportType,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY reportType DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });
            it('sort by channel', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.CreatedBy,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY platform DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });
            it('sort by workspace', async () => {
              const mockMySqlClient = jest.fn();
              const reportListService = await buildTestModule({
                mockMySqlClient,
              });
              const result = await reportListService.getAllAnalyticsReports(
                defaultUserDetails,
                createStubAnalyticsReportFilterOptions({
                  sortBy: SortBy.Workspace,
                  sortOrder: SortOrder.DESC,
                }),
              );

              const expectedSort = `ORDER BY workspace DESC`;

              const calls = mockMySqlClient.mock.calls;
              normalizeString(calls[0][0]).includes(
                normalizeString(expectedSort),
              );
            });
          });
        });
      });
    });
  });
});
