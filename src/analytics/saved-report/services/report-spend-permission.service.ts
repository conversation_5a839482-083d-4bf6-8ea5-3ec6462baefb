import { ForbiddenException, Injectable } from '@nestjs/common';
import { KPIService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { OrganizationService } from '@vidmob/vidmob-organization-service-sdk';

@Injectable()
export class ReportSpendPermissionService {
  constructor(
    private readonly kpiService: KPIService,
    private readonly organizationService: OrganizationService,
  ) {}

  async validateOrganizationAccessToSpendKpi(
    kpiId: string,
    platform: string,
    organizationId: string,
  ) {
    if (!kpiId) {
      return;
    }

    const [kpiResponse, organizationResponse] = await Promise.all([
      this.kpiService.getKpiByIdAsPromise(kpiId, platform),
      this.organizationService.findOneAsPromise(organizationId),
    ]);

    const { result: kpi } = kpiResponse;
    const { result: organization } = organizationResponse;

    if (kpi?.format === 'SPEND' && !organization?.spendEnabled) {
      throw new ForbiddenException(
        `Organization ${organizationId} does not have spend reporting enabled.`,
      );
    }

    return;
  }

  async getOrganizationAccessToSpendKpi(organizationId: string) {
    const organizationResponse =
      await this.organizationService.findOneAsPromise(organizationId);
    const { result: organization } = organizationResponse;

    if (!organization) {
      throw new ForbiddenException(`Organization ${organizationId} not found.`);
    }

    return organization.spendEnabled ?? false;
  }
}
