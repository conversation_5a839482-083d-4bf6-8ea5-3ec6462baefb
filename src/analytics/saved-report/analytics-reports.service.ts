import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Report } from '../../entities/report.entity';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { DEFAULT_PAGINATION_OPTIONS } from '../constants/constants';
import {
  CreateAnalyticsReportDto,
  CreateCreativeLeaderboardReportDto,
} from './dto/create-analytics-report.dto';
import {
  AnalyticsReportDto,
  AnalyticsReportWithNoFiltersDto,
} from './dto/analytics-report.dto';
import { AnalyticsReportConverter } from './util/analytics-report-converter';
import { AnalyticsReportFactory } from './util/analytics-report-factory';
import {
  FilterAdAccountRequestDto,
  OrganizationService as OrganizationServiceSDK,
  ReadOrganizationDto,
  ScopeFilterRequestDto,
  ScopeFilterService,
  WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK,
} from '@vidmob/vidmob-organization-service-sdk';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import { UserDetailsDto } from '../dto/user-details.dto';
import {
  AD_DIMENSION_BY_PLATFORM,
  AD_GROUP_TYPE_DETAILED,
  ADSET_DIMENSION_BY_PLATFORM,
  CAMPAIGN_DIMENSION,
  FACEBOOK_AD_TYPE_VALUE_KEYS_FROM_DIMENSION_GROUPS_RESPONSE,
  Platform,
  PLATFORMS_SUPPORT_CAMPAIGN_IDENTIFIER,
  PLATFORMS_SUPPORT_CONVERSION_KPIS,
  PLATFORMS_SUPPORT_SPEND_KPIS,
  SPEND_KPI_NAME,
} from '../../constants/platform.constants';
import {
  FilterItem,
  FilterType,
  GetAnalyticsReportFilterOptionsRequestDto,
} from './dto/get-analytics-filter-values.dto';
import { LegacyAnalyticsServiceStatsRequestDto } from '../../dto/legacy-analytics-service-stats-request.dto';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';
import { DateRange } from '../dto/date-range.dto';
import {
  GetPlatformKpis200Response,
  KPIService,
  ReportFiltersService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { OrganizationUserRoles } from '../../constants/role.constants';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { Workspace } from '../../entities/workspace.entity';
import { PlatformAdAccount } from '../../entities/platform-ad-account.entity';
import { GetAnalyticsReportFilterOptionsDto } from './dto/get-analytics-report-options.dto';
import { AnalyticsReportType } from './model/analytics-report';
import { ScoringReportType } from '../../reports/model/report';
import {
  getPlatformAdAccountIdsFromPlatformData,
  normalizeSQL,
  saveReport,
} from '../../reports/util/common-report-utils';
import { AnalyticsKpiResponseDto } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/analyticsKpiResponseDto';

@Injectable()
export class AnalyticsReportsService {
  private readonly logger = new Logger(AnalyticsReportsService.name);

  constructor(
    private readonly organizationServiceSDK: OrganizationServiceSDK,
    @InjectRepository(Report)
    private reportsRepository: Repository<Report>,
    @InjectRepository(Workspace)
    private workspaceRepository: Repository<Workspace>,
    @InjectRepository(PlatformAdAccount)
    private platformAdAccountRepository: Repository<PlatformAdAccount>,
    private readonly analyticsReportConverter: AnalyticsReportConverter,
    private readonly analyticsReportFactory: AnalyticsReportFactory,
    private readonly legacyAnalyticsService: LegacyAnalyticsService,
    private readonly kpiService: KPIService,
    private readonly reportFiltersService: ReportFiltersService,
    private readonly analyticsUserService: AnalyticsUserService,
    private readonly workspaceAdAccountServiceSDK: WorkspaceAdAccountServiceSDK,
    private readonly scoringCriteriaService: ScoringCriteriaService,
    private readonly scopeFilterServiceSDK: ScopeFilterService,
    private readonly organizationUserService: OrganizationUserService,
  ) {}

  async getAllAnalyticsReports(
    userDetails: UserDetailsDto,
    getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto,
  ): Promise<PaginatedResultArray<AnalyticsReportWithNoFiltersDto>> {
    try {
      const isUserOrgAdmin = await this.getIsUserOrgAdmin(
        userDetails.organizationId,
        userDetails.userId,
      );

      const userAdAccountsAndWorkspaces =
        await this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
          userDetails,
        );

      const adAccountsUserHasAccess =
        userAdAccountsAndWorkspaces.adAccounts.map(
          (adAccount) => adAccount.platformAccountId,
        );

      const workspacesUserHasAccess =
        userAdAccountsAndWorkspaces.workspaces.map((workspace) => workspace.id);

      if (
        !isUserOrgAdmin &&
        (!adAccountsUserHasAccess.length || !workspacesUserHasAccess.length)
      ) {
        return new PaginatedResultArray([], 0);
      }

      const [result, totalCount] = await this.fetchUserAnalyticsReportsAndCount(
        userDetails.organizationId,
        getAnalyticsReportsOptions,
        adAccountsUserHasAccess,
        workspacesUserHasAccess,
        isUserOrgAdmin,
      );

      const analyticsReports =
        this.analyticsReportConverter.convertAllWithoutFilters(
          result,
          userAdAccountsAndWorkspaces,
        );

      return new PaginatedResultArray(analyticsReports, totalCount);
    } catch (error) {
      const baseErrorMessage = `Failed to get analytics reports for user ${userDetails.userId} in organization ${userDetails.organizationId}.`;
      this.logger.error(`${baseErrorMessage}. ${error}`);
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  private async getIsUserOrgAdmin(
    organizationId: string,
    userId: number,
  ): Promise<boolean> {
    const response = await this.organizationUserService.getUserInOrganization(
      organizationId,
      userId,
    );

    return response.result.roles.some(
      (role) => role.identifier === OrganizationUserRoles.ORG_ADMIN,
    );
  }

  async saveAnalyticsReport(
    userDetails: UserDetailsDto,
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
  ): Promise<AnalyticsReportDto> {
    try {
      const userAdAccountsAndWorkspaces =
        await this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
          userDetails,
          createAnalyticsReportDto.filters.platform,
        );

      const adAccountIdsForLeaderboard =
        await this.getUserAdAccountsForLeaderboard(createAnalyticsReportDto);

      const reportToSave =
        await this.analyticsReportFactory.buildReportForCreate(
          createAnalyticsReportDto,
          userDetails,
          userAdAccountsAndWorkspaces,
          adAccountIdsForLeaderboard,
        );

      const platformAccountIds =
        'adAccountIds' in createAnalyticsReportDto.filters
          ? createAnalyticsReportDto.filters.adAccountIds
          : adAccountIdsForLeaderboard ?? undefined;

      const platform = createAnalyticsReportDto.filters.platform;
      const platformAdAccountIds = await this.getPlatformAdAccountIds(
        platform,
        platformAccountIds,
      );
      const dataSource = this.reportsRepository.manager.connection;
      const savedReport = await saveReport(
        reportToSave,
        {
          workspaceIds: createAnalyticsReportDto.filters.workspaceIds,
          platformAdAccountIds,
          platformAccountIds,
          channels: [platform.valueOf()],
        },
        dataSource,
      );

      return this.analyticsReportConverter.convert(
        savedReport,
        userAdAccountsAndWorkspaces,
      );
    } catch (error) {
      const baseErrorMessage = `Failed to save analytics report for user ${userDetails.userId} in organization ${userDetails.organizationId}`;
      this.logger.error(
        `${baseErrorMessage}. ${error}. \nRequest: ${JSON.stringify(
          createAnalyticsReportDto,
        )}`,
      );
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  async updateAnalyticsReport(
    userDetails: UserDetailsDto,
    reportId: string,
    updateAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
  ): Promise<AnalyticsReportDto> {
    const { userId, organizationId } = userDetails;

    try {
      const [savedAnalyticsReport, userAdAccountsAndWorkspaces] =
        await Promise.all([
          this.fetchSingleAnalyticsReportByOrganizationId(
            userDetails.organizationId,
            reportId,
          ),
          this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
            userDetails,
            updateAnalyticsReportDto.filters?.platform,
          ),
        ]);

      if (savedAnalyticsReport.owner.id !== userDetails.userId) {
        throw new ForbiddenException(
          `Error fetching report ${reportId} for user ${userDetails.userId}. User is not owner of report.`,
        );
      }

      const adAccountIdsForLeaderboard =
        await this.getUserAdAccountsForLeaderboard(updateAnalyticsReportDto);

      const updatedReportToSave: Report =
        await this.analyticsReportFactory.buildReportForUpdate(
          savedAnalyticsReport,
          updateAnalyticsReportDto,
          userDetails,
          userAdAccountsAndWorkspaces,
          adAccountIdsForLeaderboard,
        );

      const isFiltersUnchanged =
        updatedReportToSave.filter.filters ===
          savedAnalyticsReport.filter.filters &&
        updatedReportToSave.filter.groupBy ===
          savedAnalyticsReport.filter.groupBy;
      if (isFiltersUnchanged) {
        // only update filters if they have changed
        updatedReportToSave.filter = savedAnalyticsReport.filter;
      }

      const platformAccountIds =
        'adAccountIds' in updateAnalyticsReportDto.filters
          ? updateAnalyticsReportDto.filters.adAccountIds
          : adAccountIdsForLeaderboard ?? undefined;

      const platform = updateAnalyticsReportDto.filters.platform;
      const platformAdAccountIds = await this.getPlatformAdAccountIds(
        platform,
        platformAccountIds,
      );
      const dataSource = this.reportsRepository.manager.connection;
      const savedReport = await saveReport(
        updatedReportToSave,
        {
          workspaceIds: updateAnalyticsReportDto.filters.workspaceIds,
          platformAdAccountIds,
          platformAccountIds,
          channels: [platform.valueOf()],
        },
        dataSource,
      );

      return this.analyticsReportConverter.convert(
        savedReport,
        userAdAccountsAndWorkspaces,
      );
    } catch (error) {
      const baseErrorMessage = `Error updating report ${reportId} for user ${userId} and organization ${organizationId}`;
      this.logger.error(
        `${baseErrorMessage}. ${error}. \nRequest: ${JSON.stringify(
          updateAnalyticsReportDto,
        )}`,
      );
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  private async getPlatformAdAccountIds(
    platform: Platform,
    platformAccountIds: string[] | undefined,
  ) {
    const platformAccountFilters = platformAccountIds?.map((id) => ({
      platformAccountId: id,
      channel: platform.valueOf(),
    }));
    return platformAccountFilters
      ? await getPlatformAdAccountIdsFromPlatformData(
          this.platformAdAccountRepository,
          platformAccountFilters,
        )
      : undefined;
  }

  async getAnalyticsReportForUser(
    userDetails: UserDetailsDto,
    reportId: string,
  ): Promise<AnalyticsReportDto> {
    try {
      const userAdAccountsAndWorkspaces =
        await this.analyticsUserService.fetchUserAdAccountsAndWorkspaces(
          userDetails,
        );
      const adAccountsUserHasAccess =
        userAdAccountsAndWorkspaces.adAccounts.map(
          (adAccount) => adAccount.platformAccountId,
        );

      const report = await this.fetchSingleAnalyticsReport(
        userDetails,
        reportId,
        adAccountsUserHasAccess,
      );

      return this.analyticsReportConverter.convert(
        report,
        userAdAccountsAndWorkspaces,
      );
    } catch (error) {
      const baseErrorMessage = `Error fetching report ${reportId} for user ${userDetails.userId} in organization ${userDetails.organizationId}`;
      this.logger.error(`${baseErrorMessage}. ${error}`);
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  async deleteAnalyticsReportForUser(
    userDetails: UserDetailsDto,
    reportId: string,
  ): Promise<{ deleted: boolean }> {
    try {
      const report = await this.fetchSingleAnalyticsReportByOrganizationId(
        userDetails.organizationId,
        reportId,
      );

      if (report.owner.id !== userDetails.userId) {
        throw new ForbiddenException(
          `Error fetching report ${reportId} for user ${userDetails.userId}. User is not owner of report.`,
        );
      }

      report.deleted = true;
      await this.reportsRepository.save(report);

      return { deleted: true };
    } catch (error) {
      const baseErrorMessage = `Error deleting report ${reportId} for user ${userDetails.userId} in organization ${userDetails.organizationId}`;
      this.logger.error(`${baseErrorMessage}. ${error}`);
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  async getUserAdAccountsForLeaderboard(
    createAnalyticsReportDto:
      | CreateAnalyticsReportDto
      | CreateCreativeLeaderboardReportDto,
  ) {
    if (
      this.analyticsReportFactory.isCreativeLeaderboardReport(
        createAnalyticsReportDto,
      )
    ) {
      const allAdAccounts =
        await this.analyticsUserService.fetchAllAdAccountsForWorkspaces(
          createAnalyticsReportDto.filters.workspaceIds,
          createAnalyticsReportDto.filters.platform,
        );
      return allAdAccounts.map((adAccount) => adAccount.platformAccountId);
    }

    return null;
  }

  async getAnalyticsReportFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
    paginationOptions?: PaginationOptions,
  ): Promise<FilterItem[]> {
    const { filterType } = getAnalyticsReportFilterOptionsRequestDto;
    // await response, instead of returning this.filterFunc(), to avoid duplicate error handling in each case's function
    let response: FilterItem[];

    try {
      switch (filterType) {
        case FilterType.CAMPAIGN_IDENTIFIER:
          response = await this.getCampaignIdentifierFilterOptions(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.CAMPAIGN_OBJECTIVE:
          response = await this.getCampaignObjectivesFilterForPlatform(
            getAnalyticsReportFilterOptionsRequestDto.platform,
            userDetails.authorization,
          );
          return response;
        case FilterType.AD_IDENTIFIER:
          response = await this.getAdIdentifierFilterOptions(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.AD_SET_IDENTIFIER:
          response = await this.getAdsetIdentifierFilterOptions(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.AD_PLACEMENT:
          response = await this.getAdPlacementFilterOptions(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.AD_TYPE:
          response = await this.getAdTypeFilterOptions(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.KPI:
          response = await this.getKPIs(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.CRITERIA:
          response = await this.getCriteriaGroups(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
            paginationOptions,
          );
          return response;
        default:
          throw new BadRequestException(
            `Error fetching filter ${filterType} for user ${userDetails.userId} in organization ${userDetails.organizationId}. Unsupported filter type in request: ${filterType}`,
          );
      }
    } catch (error) {
      const baseErrorMessage = `Error fetching filter ${filterType} for user ${userDetails.userId} in organization ${userDetails.organizationId}`;
      this.logger.error(
        `${baseErrorMessage}. Error: ${error} \nRequest: ${JSON.stringify(
          getAnalyticsReportFilterOptionsRequestDto,
        )}`,
      );
      throw new BadRequestException(`${baseErrorMessage}. ${error.message}`);
    }
  }

  async getAnalyticsReportFilterOptionsV2(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
    paginationOptions?: PaginationOptions,
  ): Promise<PaginatedResultArray<FilterItem> | FilterItem[]> {
    const { filterType } = getAnalyticsReportFilterOptionsRequestDto;
    // await response, instead of returning this.filterFunc(), to avoid duplicate error handling in each case's function
    let response: PaginatedResultArray<FilterItem> | FilterItem[];

    try {
      switch (filterType) {
        case FilterType.CAMPAIGN_IDENTIFIER:
        case FilterType.AD_IDENTIFIER:
        case FilterType.AD_SET_IDENTIFIER:
          response = await this.getIdentifierFilterOptions({
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
            paginationOptions,
            areAdAccountIdsRequired: false,
          });
          return response;
        case FilterType.CAMPAIGN_OBJECTIVE:
        case FilterType.AD_PLACEMENT:
        case FilterType.AD_TYPE:
          response = await this.getDimensionFilterOptions(
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.KPI:
          response = await this.getKPIs(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
          );
          return response;
        case FilterType.CRITERIA:
          response = await this.getCriteriaGroups(
            userDetails,
            getAnalyticsReportFilterOptionsRequestDto,
            paginationOptions,
          );
          return response;
        default:
          throw new BadRequestException(
            `Error fetching filter ${filterType} for user ${userDetails.userId} in organization ${userDetails.organizationId}. Unsupported filter type in request: ${filterType}`,
          );
      }
    } catch (error) {
      const vidmobErrorMessage =
        error.response?.error?.message || error.message;
      const returnErrorMessage = `Error fetching filter ${filterType} for user ${userDetails.userId} in organization ${userDetails.organizationId}. ${vidmobErrorMessage}`;
      this.logger.error(
        `${returnErrorMessage}. Error: ${error} \nRequest: ${JSON.stringify(
          getAnalyticsReportFilterOptionsRequestDto,
        )}`,
      );
      throw new BadRequestException(returnErrorMessage);
    }
  }

  private async getIdentifierFilterOptions({
    userDetails,
    getAnalyticsReportFilterOptionsRequestDto,
    paginationOptions,
    areAdAccountIdsRequired = true,
  }: {
    userDetails: UserDetailsDto;
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto;
    paginationOptions?: PaginationOptions;
    areAdAccountIdsRequired?: boolean;
  }): Promise<PaginatedResultArray<FilterItem>> {
    const {
      searchString,
      filterType,
      platform,
      workspaceIds,
      adAccountIds,
      dateRange,
    } = getAnalyticsReportFilterOptionsRequestDto;
    const { offset, perPage } = paginationOptions || DEFAULT_PAGINATION_OPTIONS;

    let adAccountIdsForRequest = adAccountIds;

    if (
      (!adAccountIds || adAccountIds.length === 0) &&
      !areAdAccountIdsRequired
    ) {
      const { result: allUserAdAccountsForWorkspacesAndPlatform } =
        await this.workspaceAdAccountServiceSDK.getConnectedAdAccountsForWorkspacesAndPlatformAsPromise(
          {
            workspaces: workspaceIds as number[],
            platform,
          },
        );

      // if there are no ad accounts, bypass the Analytics service and return empty array
      if (allUserAdAccountsForWorkspacesAndPlatform?.length === 0) {
        return {
          items: [],
        };
      }

      adAccountIdsForRequest = allUserAdAccountsForWorkspacesAndPlatform.map(
        (adAccount) => adAccount.platformAccountId,
      );
    }

    await this.handleRequiredDateAndAccountsForFilterOptions(userDetails, {
      ...getAnalyticsReportFilterOptionsRequestDto,
      adAccountIds: adAccountIdsForRequest,
    });

    /* eslint-disable @typescript-eslint/ban-ts-comment */
    // @ts-ignore
    return await this.reportFiltersService.getReportIdentifierFilterValuesAsPromise(
      // @ts-ignore
      filterType,
      {
        platform,
        adAccountIds: adAccountIdsForRequest as string[],
        startDate: dateRange?.startDate as string,
        endDate: dateRange?.endDate as string,
        searchString,
      },
      offset,
      perPage,
    );
  }

  private async getDimensionFilterOptions(
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<PaginatedResultArray<FilterItem>> {
    const { filterType, platform } = getAnalyticsReportFilterOptionsRequestDto;

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    return await this.reportFiltersService.getReportDimensionFilterValuesAsPromise(
      platform,
      // @ts-ignore
      filterType,
    );
  }

  private filterPlatformSpendKpis(kpis: AnalyticsKpiResponseDto[]) {
    return kpis.filter((kpi) => kpi.name !== SPEND_KPI_NAME);
  }

  private hydrateKpiDescriptionAndEnabledStatus(
    kpis: AnalyticsKpiResponseDto[],
    organization: ReadOrganizationDto,
  ) {
    return kpis.map((kpi) => ({
      ...kpi,
      isEnabled: organization.spendEnabled || kpi.format !== 'SPEND',
      ...(kpi.name === kpi.description
        ? { description: 'Custom KPI set up specifically on ad account.' }
        : {}),
    }));
  }

  private adjustSpendKpis(
    kpiResponse: GetPlatformKpis200Response,
    organization: ReadOrganizationDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ) {
    const { platform, shouldIncludeAllSpendKpis } =
      getAnalyticsReportFilterOptionsRequestDto;
    let result = kpiResponse.result ?? [];

    if (
      !shouldIncludeAllSpendKpis &&
      !PLATFORMS_SUPPORT_SPEND_KPIS.includes(platform)
    ) {
      result = this.filterPlatformSpendKpis(result);
    }

    return this.hydrateKpiDescriptionAndEnabledStatus(result, organization);
  }

  public async getKPIs(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<FilterItem[]> {
    const { platform, adAccountIds } =
      getAnalyticsReportFilterOptionsRequestDto;

    const { result: organization } =
      await this.organizationServiceSDK.findOneAsPromise(
        userDetails.organizationId,
      );

    if (
      PLATFORMS_SUPPORT_CONVERSION_KPIS.includes(platform) &&
      adAccountIds?.length
    ) {
      await this.handleRequiredDateAndAccountsForFilterOptions(
        userDetails,
        getAnalyticsReportFilterOptionsRequestDto,
        false,
      );

      const [platformKpis, customConversionKpis] = await Promise.all([
        this.kpiService.getPlatformKpisAsPromise(platform),
        this.kpiService.getCustomConversionKpisForAdAccountsAsPromise(
          platform,
          { adAccountIds: adAccountIds as string[] },
        ),
      ]);
      return [
        ...this.adjustSpendKpis(
          platformKpis,
          organization,
          getAnalyticsReportFilterOptionsRequestDto,
        ),
        ...this.adjustSpendKpis(
          customConversionKpis,
          organization,
          getAnalyticsReportFilterOptionsRequestDto,
        ),
      ];
    }

    const platformKpis = await this.kpiService.getPlatformKpisAsPromise(
      platform,
    );

    return this.adjustSpendKpis(
      platformKpis,
      organization,
      getAnalyticsReportFilterOptionsRequestDto,
    );
  }

  private async getAdTypeFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<FilterItem[]> {
    const { platform } = getAnalyticsReportFilterOptionsRequestDto;
    switch (platform) {
      case Platform.FACEBOOK:
        return this.getAdTypeFilterOptionsForFacebook(userDetails);
      case Platform.ADWORDS:
        return this.getAdTypeFilterOptionsForAdwords(userDetails);
      case Platform.DV360:
        return this.getAdTypeFilterOptionsForDV360(userDetails);
      default:
        throw new BadRequestException(
          `Platform ${platform} does not support ad type filter type`,
        );
    }
  }

  private async getAdTypeFilterOptionsForAdwords(
    userDetails: UserDetailsDto,
  ): Promise<FilterItem[]> {
    const nestedAdTypes = await this.legacyAnalyticsService.getPlatformAdTypes(
      Platform.ADWORDS,
      userDetails.authorization,
    );

    return Object.keys(nestedAdTypes).map((adType) => {
      const valuesForAdType = nestedAdTypes[adType]
        .map((grouping) => grouping.values)
        .flat()
        .map((nestedValue: any) => ({
          id: nestedValue.id,
          name: nestedValue.value,
        }));
      return {
        id: adType,
        name: adType,
        values: valuesForAdType,
      };
    });
  }

  private async getAdTypeFilterOptionsForDV360(
    userDetails: UserDetailsDto,
  ): Promise<FilterItem[]> {
    const adTypes = await this.legacyAnalyticsService.getPlatformAdTypes(
      Platform.DV360,
      userDetails.authorization,
    );

    return adTypes?.[AD_GROUP_TYPE_DETAILED]?.map((adType) => ({
      id: adType.id,
      name: adType.name,
      values: adType.values?.map((value) => ({
        id: value.id,
        name: value.value,
      })),
    }));
  }

  private getAdTypeFilterOptionsForFacebook(
    userDetails: UserDetailsDto,
  ): Promise<FilterItem[]> {
    // facebook's ad type filter is actually one of its placement nesting types
    return this.legacyAnalyticsService.getPlatformPlacements(
      Platform.FACEBOOK,
      userDetails.authorization,
      FACEBOOK_AD_TYPE_VALUE_KEYS_FROM_DIMENSION_GROUPS_RESPONSE,
    );
  }

  private getAdPlacementFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<FilterItem[]> {
    return this.legacyAnalyticsService.getPlatformPlacements(
      getAnalyticsReportFilterOptionsRequestDto.platform,
      userDetails.authorization,
    );
  }

  private async getAdsetIdentifierFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<FilterItem[]> {
    const { organizationId, authorization } = userDetails;
    const { platform, adAccountIds, dateRange } =
      getAnalyticsReportFilterOptionsRequestDto;

    await this.handleRequiredDateAndAccountsForFilterOptions(
      userDetails,
      getAnalyticsReportFilterOptionsRequestDto,
    );

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore // needed for undefined values
    const adSetDimension = ADSET_DIMENSION_BY_PLATFORM[platform];
    if (!adSetDimension) {
      throw new BadRequestException(
        `Platform ${platform} does not support adset identifier filter type`,
      );
    }

    const legacyAnalyticsServiceStatsRequest: LegacyAnalyticsServiceStatsRequestDto =
      {
        tokenWithBearerPrefix: authorization,
        organizationId,
        workspaceIds:
          getAnalyticsReportFilterOptionsRequestDto.workspaceIds as number[],
        platform,
        adAccountIds: adAccountIds as string[],
        dateRange: dateRange as DateRange,
        dimension: adSetDimension,
      };
    return this.legacyAnalyticsService.getAnalyticsDimensionValues(
      legacyAnalyticsServiceStatsRequest,
    );
  }

  private async getAdIdentifierFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<FilterItem[]> {
    const { organizationId, authorization } = userDetails;
    const { platform, adAccountIds, dateRange, workspaceIds } =
      getAnalyticsReportFilterOptionsRequestDto;

    await this.handleRequiredDateAndAccountsForFilterOptions(
      userDetails,
      getAnalyticsReportFilterOptionsRequestDto,
    );

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore // needed for undefined values
    const platformAdDimension = AD_DIMENSION_BY_PLATFORM[platform];
    if (!platformAdDimension) {
      throw new BadRequestException(
        `Platform ${platform} is missing ad dimension configuration`,
      );
    }

    const legacyAnalyticsServiceStatsRequest: LegacyAnalyticsServiceStatsRequestDto =
      {
        tokenWithBearerPrefix: authorization,
        platform,
        organizationId,
        workspaceIds: workspaceIds as number[],
        adAccountIds: adAccountIds as string[],
        dateRange: dateRange as DateRange,
        dimension: platformAdDimension,
      };
    return this.legacyAnalyticsService.getAnalyticsDimensionValues(
      legacyAnalyticsServiceStatsRequest,
    );
  }

  private getCampaignObjectivesFilterForPlatform(
    platform: Platform,
    tokenWithBearerPrefix: string,
  ): Promise<FilterItem[]> {
    return this.legacyAnalyticsService.getPlatformObjectives(
      platform,
      tokenWithBearerPrefix,
    );
  }

  private async getCampaignIdentifierFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
  ): Promise<FilterItem[]> {
    const { organizationId, authorization } = userDetails;
    const { platform, adAccountIds, dateRange } =
      getAnalyticsReportFilterOptionsRequestDto;

    if (!PLATFORMS_SUPPORT_CAMPAIGN_IDENTIFIER.includes(platform)) {
      throw new BadRequestException(
        `Platform ${platform} does not support campaign identifier filter type`,
      );
    }

    await this.handleRequiredDateAndAccountsForFilterOptions(
      userDetails,
      getAnalyticsReportFilterOptionsRequestDto,
    );

    const legacyAnalyticsServiceStatsRequest: LegacyAnalyticsServiceStatsRequestDto =
      {
        tokenWithBearerPrefix: authorization,
        organizationId,
        workspaceIds:
          getAnalyticsReportFilterOptionsRequestDto.workspaceIds as number[],
        platform,
        adAccountIds: adAccountIds as string[],
        dateRange: dateRange as DateRange,
        dimension: CAMPAIGN_DIMENSION,
      };
    return this.legacyAnalyticsService.getAnalyticsDimensionValues(
      legacyAnalyticsServiceStatsRequest,
    );
  }

  private async getCriteriaGroups(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
    paginationOptions?: PaginationOptions,
  ): Promise<any> {
    const {
      workspaceIds,
      platform,
      mediaTypes,
      shouldIncludeStandardCriteria,
    } = getAnalyticsReportFilterOptionsRequestDto;

    if (!workspaceIds?.length) {
      throw new BadRequestException(
        `Workspace ids are required for filter type ${getAnalyticsReportFilterOptionsRequestDto.filterType}`,
      );
    }

    const { organizationId, userId } = userDetails;
    const { result } =
      await this.organizationServiceSDK.validateWorkspacesWithinOrganizationAsPromise(
        organizationId,
        userId,
        {
          workspaceIds,
        },
      );

    if (!result.success) {
      throw new BadRequestException(
        `Workspace ids are not valid for organization ${organizationId}`,
      );
    }

    return this.scoringCriteriaService.searchGroupedCriteriaAsPromise(
      {
        organizationId,
        workspaceIds,
        platform,
        applicabilityMediaTypes: mediaTypes,
        shouldIncludeStandardCriteria,
      },
      paginationOptions?.offset,
      paginationOptions?.perPage,
      paginationOptions?.queryId,
    );
  }

  private async handleRequiredDateAndAccountsForFilterOptions(
    userDetails: UserDetailsDto,
    getAnalyticsReportFilterOptionsRequestDto: GetAnalyticsReportFilterOptionsRequestDto,
    isDateRangeRequired = true,
  ): Promise<void> {
    const { adAccountIds, dateRange, workspaceIds } =
      getAnalyticsReportFilterOptionsRequestDto;
    const { organizationId, userId } = userDetails;

    if (!adAccountIds || adAccountIds.length === 0) {
      throw new BadRequestException(
        `Ad account ids are required for ad identifier filter type ${getAnalyticsReportFilterOptionsRequestDto.filterType}`,
      );
    }

    if (!workspaceIds || workspaceIds.length === 0) {
      throw new BadRequestException(
        `Workspace ids are required for filter type ${getAnalyticsReportFilterOptionsRequestDto.filterType}`,
      );
    }

    const { result } =
      await this.organizationServiceSDK.validateAdAccountsWithinOrganizationAsPromise(
        organizationId,
        userId,
        {
          adAccountIds: adAccountIds as string[],
          workspaceIds: workspaceIds as number[],
        },
      );
    if (!result.success) {
      throw new BadRequestException(
        `Ad account ids are not valid for organization ${organizationId} and workspaces ${workspaceIds}`,
      );
    }

    if (isDateRangeRequired && !dateRange) {
      throw new BadRequestException(
        `Date range is required for ad identifier filter type ${getAnalyticsReportFilterOptionsRequestDto.filterType}`,
      );
    }
  }

  private addConditionsToGetAllQueryBuilder(whereConditionConfig: {
    queryBuilder: SelectQueryBuilder<Report>;
    organizationId: string;
    getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto;
    adAccountsUserHasAccess: string[];
    workspacesUserHasAccess: number[];
    isUserOrgAdmin: boolean;
  }): SelectQueryBuilder<Report> {
    const {
      organizationId,
      getAnalyticsReportsOptions,
      adAccountsUserHasAccess,
      workspacesUserHasAccess,
      isUserOrgAdmin,
    } = whereConditionConfig;
    let queryBuilder = whereConditionConfig.queryBuilder;

    const {
      types,
      searchTerm,
      dateCreated,
      lastUpdated,
      createdBy,
      channels,
      adAccounts,
      workspaces,
    } = getAnalyticsReportsOptions;

    queryBuilder
      .where('reports.reportType IN (:...types)', { types })
      .andWhere('reports.organizationId = :organizationId', {
        organizationId,
      })
      .andWhere('reports.deleted = false');

    const adAccountsToFilter = adAccountsUserHasAccess.filter((adAccount) => {
      if (adAccounts) {
        return adAccounts.includes(adAccount);
      }
      return true;
    });

    const workspacesToFilter = workspacesUserHasAccess.filter((workspace) => {
      if (workspaces) {
        return workspaces.includes(workspace);
      }
      return true;
    });

    queryBuilder
      .having(
        normalizeSQL(
          `COUNT(DISTINCT rpaam.platform_ad_account_id) = (SELECT COUNT(DISTINCT rpaam_inner.platform_ad_account_id) 
                                                    FROM report_platform_ad_account_map rpaam_inner
                                                    JOIN platform_ad_account ON rpaam_inner.platform_ad_account_id = platform_ad_account.id 
                                                    WHERE rpaam_inner.report_id = reports.id
                                                    AND platform_ad_account.platform_account_id IN (:adAccountsToFilter))`,
        ),
        {
          adAccountsToFilter,
        },
      )
      .andHaving(
        `COUNT(DISTINCT rwm.workspace_id) = (SELECT COUNT(DISTINCT rwm_inner.workspace_id) 
                                                FROM report_workspace_map rwm_inner 
                                                WHERE rwm_inner.report_id = reports.id 
                                                AND rwm_inner.workspace_id IN (:workspacesToFilter))`,
        {
          workspacesToFilter,
        },
      );

    if (searchTerm) {
      queryBuilder = queryBuilder.andWhere(
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        { searchTerm: `%${searchTerm}%` },
      );
    }

    if (dateCreated) {
      queryBuilder = queryBuilder.andWhere(
        'DATE(reports.dateCreated) >= DATE(:startDate) AND DATE(reports.dateCreated) <= DATE(:endDate)',
        {
          startDate: dateCreated.startDate,
          endDate: dateCreated.endDate,
        },
      );
    }

    if (lastUpdated) {
      queryBuilder = queryBuilder.andWhere(
        'DATE(reports.lastUpdated) >= DATE(:startDate) AND DATE(reports.lastUpdated) <= DATE(:endDate)',
        {
          startDate: lastUpdated.startDate,
          endDate: lastUpdated.endDate,
        },
      );
    }

    if (createdBy && createdBy.length > 0) {
      queryBuilder = queryBuilder.andWhere('reports.owner_id IN (:createdBy)', {
        createdBy,
      });
    }

    if (channels && channels.length > 0) {
      queryBuilder = queryBuilder.andWhere(
        `JSON_UNQUOTE(JSON_EXTRACT(filter.filters, '$.platform')) IN (:channels)`,
        { channels },
      );
    }

    return queryBuilder;
  }

  private async getUserAnalyticsReportsTotalCount(
    organizationId: string,
    getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto,
    adAccountsUserHasAccess: string[],
    workspacesUserHasAccess: number[],
  ): Promise<number> {
    let queryBuilder = this.reportsRepository
      .createQueryBuilder('reports')
      .select('reports.id')
      .innerJoin('reports.owner', 'owner')
      .innerJoin('reports.workspaceMaps', 'rwm')
      .innerJoin('reports.platformAdAccountMaps', 'rpaam')
      .innerJoin('reports.filter', 'filter', 'filter.id = reports.filter_id')
      .leftJoin(
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId', // This will include either 1 or 0 personOrganizationMap array length, 0 means a deactivated user
      )
      .groupBy('reports.id');

    queryBuilder = this.addConditionsToGetAllQueryBuilder({
      queryBuilder,
      organizationId,
      getAnalyticsReportsOptions,
      adAccountsUserHasAccess,
      workspacesUserHasAccess,
      isUserOrgAdmin: false,
    });

    const response = await queryBuilder.getMany();

    return response.length;
  }

  private async fetchUserAnalyticsReportsAndCount(
    organizationId: string,
    getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto,
    adAccountsUserHasAccess: string[],
    workspacesUserHasAccess: number[],
    isUserOrgAdmin = false,
  ): Promise<[Report[], number]> {
    const offset =
      getAnalyticsReportsOptions.offset ?? DEFAULT_PAGINATION_OPTIONS.offset;
    const perPage =
      getAnalyticsReportsOptions.perPage ?? DEFAULT_PAGINATION_OPTIONS.perPage;
    const sortOrder = getAnalyticsReportsOptions.sortOrder;

    let queryBuilder = this.reportsRepository
      .createQueryBuilder('reports')
      .innerJoinAndSelect('reports.owner', 'owner')
      .innerJoinAndSelect('reports.workspaceMaps', 'rwm')
      .innerJoinAndSelect('reports.platformAdAccountMaps', 'rpaam')
      .innerJoinAndSelect('rpaam.platformAdAccount', 'platformAdAccount')
      .innerJoinAndSelect(
        'reports.filter',
        'filter',
        'filter.id = reports.filter_id',
      )
      .leftJoinAndSelect(
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId', // This will include either 1 or 0 personOrganizationMap array length, 0 means a deactivated user
      )
      .groupBy('reports.id, reports.lastUpdated');

    queryBuilder = this.addConditionsToGetAllQueryBuilder({
      queryBuilder,
      organizationId,
      getAnalyticsReportsOptions,
      adAccountsUserHasAccess,
      workspacesUserHasAccess,
      isUserOrgAdmin,
    });

    queryBuilder
      .take(perPage)
      .skip(offset)
      .orderBy('reports.lastUpdated', sortOrder);

    const result = await this.fetchAndCount(
      isUserOrgAdmin,
      organizationId,
      getAnalyticsReportsOptions,
      adAccountsUserHasAccess,
      workspacesUserHasAccess,
      queryBuilder,
    );

    let hydratedResult = result[0];

    if (result[0].length > 0) {
      hydratedResult = await this.getWorkspacesAndAccountDetails(result[0]);
    }

    return [hydratedResult, result[1]];
  }

  private async fetchAndCount(
    isUserOrgAdmin: boolean,
    organizationId: string,
    getAnalyticsReportsOptions: GetAnalyticsReportFilterOptionsDto,
    adAccountsUserHasAccess: string[],
    workspacesUserHasAccess: number[],
    queryBuilder: any,
  ): Promise<[Report[], number]> {
    return Promise.all([
      queryBuilder.getMany(),
      this.getUserAnalyticsReportsTotalCount(
        organizationId,
        getAnalyticsReportsOptions,
        adAccountsUserHasAccess,
        workspacesUserHasAccess,
      ),
    ]);
  }

  private async getWorkspacesAndAccountDetails(
    reportList: Report[],
  ): Promise<any[]> {
    const reportIds = reportList.map((report) => report?.id);

    // get full map ( all workspace and all account
    const reportsToWorkspaces = await this.reportsRepository
      .createQueryBuilder('reports')
      .innerJoinAndSelect('reports.workspaceMaps', 'rwm')
      .innerJoinAndSelect('reports.platformAdAccountMaps', 'rpaam')
      .innerJoinAndSelect('rpaam.platformAdAccount', 'platformAdAccount')
      .innerJoinAndSelect('reports.owner', 'owner')
      .where('reports.id IN (:...reportIds)', { reportIds })
      .getMany();

    const reportById = new Map(reportsToWorkspaces.map((r) => [r.id, r]));

    // hydrate workspaces
    const workspaceIds = [
      ...new Set(
        reportsToWorkspaces.flatMap(
          (report) => report.workspaceMaps?.map((rwm) => rwm.workspaceId) || [],
        ),
      ),
    ];

    const workspaces = await this.workspaceRepository
      .createQueryBuilder('workspace')
      .where('workspace.id in (:...workspaceIds)', {
        workspaceIds: workspaceIds,
      })
      .getMany();

    const workspaceMap = new Map(
      workspaces.map((workspace) => [workspace.id, workspace]),
    );

    // hydrate accounts
    const accountIds = [
      ...new Set(
        reportsToWorkspaces.flatMap(
          (report) =>
            report.platformAdAccountMaps?.map(
              (rpam) => rpam.platformAdAccountId,
            ) || [],
        ),
      ),
    ];

    const accounts = await this.platformAdAccountRepository
      .createQueryBuilder('platformAccounts')
      .where('id in (:...accountIds)', {
        accountIds: accountIds,
      })
      .getMany();

    const accountsMap = new Map(
      accounts.map((account) => [account.id, account]),
    );

    return reportList.map((originalReport) => {
      const report = reportById.get(originalReport.id);

      const workspaceDetails =
        report?.workspaceMaps?.map((rwm) =>
          workspaceMap.get(rwm.workspaceId),
        ) || [];

      const accountDetails =
        report?.platformAdAccountMaps?.map((rpam) =>
          accountsMap.get(rpam.platformAdAccountId),
        ) || [];

      return {
        ...report,
        workspaces: workspaceDetails,
        accounts: accountDetails,
      };
    });
  }

  private async fetchSingleAnalyticsReport(
    userDetails: UserDetailsDto,
    reportId: string,
    adAccountsUserHasAccess: string[],
  ): Promise<Report> {
    const { userId, organizationId } = userDetails;
    const report = await this.fetchSingleAnalyticsReportByOrganizationId(
      organizationId,
      reportId,
    );

    const { adAccountIds: savedAdAccountIds } = JSON.parse(
      report.filter.filters,
    );

    const userHasAccessToAllReportAdAccounts = savedAdAccountIds.every(
      (adAccountId: string) => adAccountsUserHasAccess.includes(adAccountId),
    );

    if (!userHasAccessToAllReportAdAccounts) {
      const reportAccountsUserDoesNotHaveAccessTo = savedAdAccountIds.filter(
        (adAccountId: string) => !adAccountsUserHasAccess.includes(adAccountId),
      );
      this.logger.error(
        `User ${userId} does not have access for report ${reportId} due to missing access to ad accounts: ${reportAccountsUserDoesNotHaveAccessTo}.`,
      );

      throw new ForbiddenException(
        `Error fetching report ${reportId} for user ${userId}. User does not have access to this report.`,
      );
    }

    return report;
  }

  private async fetchSingleAnalyticsReportByOrganizationId(
    organizationId: string,
    reportId: string,
  ): Promise<Report> {
    const report = await this.reportsRepository
      .createQueryBuilder('reports')
      .leftJoinAndSelect('reports.filter', 'filter')
      .leftJoinAndSelect('reports.owner', 'owner')
      .leftJoinAndSelect(
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId', // This will include either 1 or 0 personOrganizationMap array length, 0 means a deactivated user
      )
      .where('reports.id = :reportId', { reportId })
      .andWhere('reports.organizationId = :organizationId', {
        organizationId,
      })
      .andWhere('reports.deleted = false')
      .getOne();

    if (!report) {
      throw new NotFoundException(
        `Report ${reportId} not found in organization ${organizationId}.`,
      );
    }

    return report;
  }

  async filterAdAccountsForBrandsOrMarketBreakdown(
    filterAdAccountRequestDto: FilterAdAccountRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    const { perPage, offset } = paginationOptions;
    return this.workspaceAdAccountServiceSDK.getFilteredAdAccountsByBrandsOrMarketsAsPromise(
      filterAdAccountRequestDto,
      offset,
      perPage,
    );
  }

  async filterAdAccountsByScope(
    dto: ScopeFilterRequestDto,
    paginationOptions: PaginationOptions,
  ) {
    return await this.scopeFilterServiceSDK.filterAdAccountsByScopeAsPromise(
      dto,
      paginationOptions.offset,
      paginationOptions.perPage,
      paginationOptions.queryId,
    );
  }

  async getAllUsersThatHaveReportsInUserWorkspaces(
    userDetails: UserDetailsDto,
    workspaceIds: number[],
    reportTypes: AnalyticsReportType[] | ScoringReportType[] = [],
  ): Promise<any> {
    await this.analyticsUserService.validateUserAccessToAdAccountsAndWorkspaces(
      userDetails,
      workspaceIds,
    );

    if (reportTypes.length === 0) {
      reportTypes = Object.values(AnalyticsReportType);
    }

    // get all users with reports in the workspaces, and return the users deduplicated
    const users = await this.reportsRepository
      .createQueryBuilder('reports')
      .select('reports.id')
      .select('reports.reportType')
      .innerJoinAndSelect('reports.owner', 'owner')
      .innerJoin('reports.workspaceMaps', 'rwm')
      .leftJoin(
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId',
      )
      .where('reports.reportType IN (:...reportTypes)', { reportTypes })
      .andWhere('reports.deleted = false')
      .andWhere('rwm.workspace_id IN (:...workspaceIds)', {
        workspaceIds,
      })
      .groupBy('owner.id')
      .distinct(true)
      .getMany();

    return users.map((user) => user.owner);
  }
}
