import { Report } from '../../entities/report.entity';
import { AnalyticsReportsService } from './analytics-reports.service';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Person } from '../../entities/person.entity';
import { AnalyticsReportType } from './model/analytics-report';
import { AnalyticsReportConverter } from './util/analytics-report-converter';
import { AnalyticsReportFactory } from './util/analytics-report-factory';
import {
  expectedLeaderboardReportToSave,
  expectedReportToDelete,
  expectedReportToSave,
  expectedUpdatedLeaderboardReportToSave,
  expectedUpdatedReportToSave,
  mockAdwordsGetAnalyticsReportFilterOptionsRequestDto,
  mockCampaignStats,
  mockCreateAnalyticsReportDto,
  mockCreateCreativeLeaderboardReportDto,
  mockCriteriaGroups,
  mockDateValue,
  mockFirstPlatformAccountId,
  mockGetAnalyticsReportFilterOptionsRequestDto,
  mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromiseResponse,
  mockGetWorkspacesByOrganizationIdResponse,
  mockOrganizationId,
  mockSecondPlatformAccountId,
  mockUpdateAnalyticsReportDto,
  mockUpdateLeaderboardReportDto,
  mockUser,
  mockUserAdAccountIds,
  mockUserAdAccountsAndWorkspaces,
  mockUserDetails,
  mockUUIDReturnValue,
  mockWorkspaceId,
} from './mocks/analytics-report.mock';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import {
  OrganizationService as OrganizationServiceSDK,
  ScopeFilterService,
  WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK,
} from '@vidmob/vidmob-organization-service-sdk';
import { ScoringCriteriaService } from '@vidmob/vidmob-soa-scoring-service-sdk';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { CreateAnalyticsReportValidationPipe } from './validators/create-analytics-report-validation-pipe';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';
import { FilterType } from './dto/get-analytics-filter-values.dto';
import {
  KPIService,
  ReportFiltersService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { Platform } from '../../constants/platform.constants';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { OrganizationUserRoles } from '../../constants/role.constants';
import { Workspace } from '../../entities/workspace.entity';
import { PlatformAdAccount } from '../../entities/platform-ad-account.entity';
import { SortOrder } from '../../reports/model/sort-order';
import { SortBy } from '../../reports/reports-enums';
import { normalizeSQL } from '../../reports/util/common-report-utils';

jest.mock('uuid', () => ({ v4: () => mockUUIDReturnValue }));
jest.mock('axios');

let analyticsReportsService: AnalyticsReportsService;

const mockReportsFindAndCount = jest.fn();
const mockReportSave = jest.fn();
const mockReportsFindOne = jest.fn();
const mockPersonFindOneBy = jest.fn();
const mockGetPlatformKpisAsPromise = jest.fn();
const mockGetCustomConversionKpisForAdAccountsAsPromise = jest.fn();
const mockSearchGroupedCriteriaAsPromise = jest.fn();
const mockGetReportIdentifierFilterValuesAsPromise = jest.fn();
const mockGetReportDimensionFilterValuesAsPromise = jest.fn();
const mockGetUserInOrganization = jest.fn();
const mockValidateAdAccountsWithinOrganizationAsPromise = jest
  .fn()
  .mockResolvedValue({ result: { success: true } });
const mockValidateWorkspacesWithinOrganizationAsPromise = jest
  .fn()
  .mockResolvedValue({ result: { success: true } });
const mockGetOneOrganization = jest.fn();

const createQueryBuilder: any = {
  from: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  select: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  innerJoinAndSelect: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  innerJoin: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  leftJoinAndSelect: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  leftJoin: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  where: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  andWhere: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  having: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  andHaving: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  take: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  skip: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  orderBy: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  getRawOne: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  getManyAndCount: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  getMany: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  getQuery: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  groupBy: jest.fn().mockImplementation(() => {
    return createQueryBuilder;
  }),
  setParameters: jest.fn().mockReturnThis(),
  getRawMany: jest.fn().mockResolvedValue([]),
  getOne: mockReportsFindOne,
};

afterEach(() => {
  jest.clearAllMocks();
});

afterAll(() => {
  jest.useRealTimers();
});

const mockReportMapInsert = jest.fn();
const mockReportMapDelete = jest.fn();
const mockTransactionalEntityManager = {
  save: mockReportSave,
  delete: mockReportMapDelete,
  insert: mockReportMapInsert,
};

beforeAll(async () => {
  jest.useFakeTimers({ now: mockDateValue });

  const module: TestingModule = await Test.createTestingModule({
    providers: [
      AnalyticsReportsService,
      {
        provide: getRepositoryToken(Report),
        useValue: {
          findAndCount: mockReportsFindAndCount,
          findOne: mockReportsFindOne,
          createQueryBuilder: jest
            .fn()
            .mockImplementation(() => createQueryBuilder),
          save: mockReportSave,
          manager: {
            connection: {
              transaction: jest.fn().mockImplementation(async (callback) => {
                return callback(mockTransactionalEntityManager);
              }),
            },
          },
        },
      },
      {
        provide: getRepositoryToken(Workspace),
        useValue: {
          createQueryBuilder: jest
            .fn()
            .mockImplementation(() => createQueryBuilder),
        },
      },
      {
        provide: getRepositoryToken(PlatformAdAccount),
        useValue: {
          createQueryBuilder: jest
            .fn()
            .mockImplementation(() => createQueryBuilder),
        },
      },
      {
        provide: getRepositoryToken(Person),
        useValue: {
          findOneBy: mockPersonFindOneBy,
          findOne: jest.fn(),
        },
      },
      {
        provide: OrganizationUserService,
        useValue: {
          getUserInOrganization: mockGetUserInOrganization.mockResolvedValue({
            result: {
              roles: [{ identifier: OrganizationUserRoles.ORG_STANDARD }],
            },
          }),
        },
      },
      {
        provide: AnalyticsReportConverter,
        useClass: AnalyticsReportConverter,
      },
      {
        provide: AnalyticsReportFactory,
        useClass: AnalyticsReportFactory,
      },
      {
        provide: WorkspaceService,
        useValue: {
          getWorkspacesByOrganizationId: jest
            .fn()
            .mockResolvedValue(mockGetWorkspacesByOrganizationIdResponse),
        },
      },
      {
        provide: WorkspaceAdAccountServiceSDK,
        useValue: {
          getConnectedAdAccountsForWorkspacesAndPlatformAsPromise: jest
            .fn()
            .mockResolvedValue(
              mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromiseResponse,
            ),
        },
      },
      {
        provide: ScopeFilterService,
        useValue: {
          filterAdAccountsByScopeAsPromise: jest.fn(),
        },
      },
      {
        provide: OrganizationServiceSDK,
        useValue: {
          validateAdAccountsWithinOrganizationAsPromise:
            mockValidateAdAccountsWithinOrganizationAsPromise,
          validateWorkspacesWithinOrganizationAsPromise:
            mockValidateWorkspacesWithinOrganizationAsPromise,
          findOneAsPromise: mockGetOneOrganization,
        },
      },
      LegacyAnalyticsService,
      {
        provide: KPIService,
        useValue: {
          getPlatformKpisAsPromise: mockGetPlatformKpisAsPromise,
          getCustomConversionKpisForAdAccountsAsPromise:
            mockGetCustomConversionKpisForAdAccountsAsPromise,
        },
      },
      {
        provide: ReportFiltersService,
        useValue: {
          getReportIdentifierFilterValuesAsPromise:
            mockGetReportIdentifierFilterValuesAsPromise,
          getReportDimensionFilterValuesAsPromise:
            mockGetReportDimensionFilterValuesAsPromise,
        },
      },
      {
        provide: ConfigService,
        useValue: {
          get: jest.fn().mockReturnValue('http://localhost:0000'),
        },
      },
      {
        provide: AnalyticsUserService,
        useValue: {
          fetchUserAdAccountsAndWorkspaces: jest
            .fn()
            .mockResolvedValue(mockUserAdAccountsAndWorkspaces),
          fetchAllAdAccountsForWorkspaces: jest
            .fn()
            .mockResolvedValue(
              mockUserAdAccountIds.map((id) => ({ platformAccountId: id })),
            ),
        },
      },
      {
        provide: ScoringCriteriaService,
        useValue: {
          searchGroupedCriteriaAsPromise: mockSearchGroupedCriteriaAsPromise,
        },
      },
      CreateAnalyticsReportValidationPipe,
    ],
  }).compile();

  analyticsReportsService = module.get<AnalyticsReportsService>(
    AnalyticsReportsService,
  );
});

describe('AnalyticsReportsService getAnalyticsReports', () => {
  it('return user reports for organization', async () => {
    jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValue([]);
    jest.spyOn(createQueryBuilder, 'getRawOne').mockResolvedValueOnce('60');
    await analyticsReportsService.getAllAnalyticsReports(mockUserDetails, {
      sortBy: SortBy.LastUpdated,
      sortOrder: SortOrder.DESC,
      types: [
        AnalyticsReportType.ELEMENT_IMPACT,
        AnalyticsReportType.MEDIA_IMPACT,
        AnalyticsReportType.ELEMENT_PRESENCE,
      ],
      searchTerm: 'test',
      offset: 40,
    });

    const adAccountIds = mockUserAdAccountsAndWorkspaces.adAccounts.map(
      (adAccount) => adAccount.platformAccountId,
    );
    const workspaceIds = mockUserAdAccountsAndWorkspaces.workspaces.map(
      (workspace) => workspace.id,
    );
    expect(createQueryBuilder.innerJoinAndSelect.mock.calls).toEqual([
      ['reports.owner', 'owner'],
      ['reports.workspaceMaps', 'rwm'],
      ['reports.platformAdAccountMaps', 'rpaam'],
      ['rpaam.platformAdAccount', 'platformAdAccount'],
      ['reports.filter', 'filter', 'filter.id = reports.filter_id'],
    ]);
    expect(createQueryBuilder.leftJoinAndSelect.mock.calls).toEqual([
      [
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId',
      ],
    ]);
    expect(createQueryBuilder.where.mock.calls).toEqual([
      [
        'reports.reportType IN (:...types)',
        {
          types: ['ELEMENT_IMPACT', 'MEDIA_IMPACT', 'ELEMENT_PRESENCE'],
        },
      ],
      [
        'reports.reportType IN (:...types)',
        {
          types: ['ELEMENT_IMPACT', 'MEDIA_IMPACT', 'ELEMENT_PRESENCE'],
        },
      ],
    ]);
    expect(createQueryBuilder.having.mock.calls).toEqual([
      [
        normalizeSQL(
          `COUNT(DISTINCT rpaam.platform_ad_account_id) = (SELECT COUNT(DISTINCT rpaam_inner.platform_ad_account_id)
          FROM report_platform_ad_account_map rpaam_inner JOIN
          platform_ad_account ON rpaam_inner.platform_ad_account_id = platform_ad_account.id
          WHERE rpaam_inner.report_id = reports.id
          AND platform_ad_account.platform_account_id IN (:adAccountsToFilter))`,
        ),
        { adAccountsToFilter: adAccountIds },
      ],
      [
        normalizeSQL(
          `COUNT(DISTINCT rpaam.platform_ad_account_id) = (SELECT COUNT(DISTINCT rpaam_inner.platform_ad_account_id)
          FROM report_platform_ad_account_map rpaam_inner JOIN
          platform_ad_account ON rpaam_inner.platform_ad_account_id = platform_ad_account.id
          WHERE rpaam_inner.report_id = reports.id
          AND platform_ad_account.platform_account_id IN (:adAccountsToFilter))`,
        ),
        {
          adAccountsToFilter: ['***************', '***************'],
        },
      ],
    ]);
    expect(createQueryBuilder.andHaving.mock.calls).toEqual([
      [
        'COUNT(DISTINCT rwm.workspace_id) = (SELECT COUNT(DISTINCT rwm_inner.workspace_id) \n                                                FROM report_workspace_map rwm_inner \n                                                WHERE rwm_inner.report_id = reports.id \n                                                AND rwm_inner.workspace_id IN (:workspacesToFilter))',
        { workspacesToFilter: workspaceIds },
      ],
      [
        'COUNT(DISTINCT rwm.workspace_id) = (SELECT COUNT(DISTINCT rwm_inner.workspace_id) \n                                                FROM report_workspace_map rwm_inner \n                                                WHERE rwm_inner.report_id = reports.id \n                                                AND rwm_inner.workspace_id IN (:workspacesToFilter))',
        {
          workspacesToFilter: [11111],
        },
      ],
    ]);
    expect(createQueryBuilder.andWhere.mock.calls).toStrictEqual([
      [
        'reports.organizationId = :organizationId',
        { organizationId: mockUserDetails.organizationId },
      ],
      ['reports.deleted = false'],
      [
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        { searchTerm: '%test%' },
      ],
      [
        'reports.organizationId = :organizationId',
        {
          organizationId: '2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c',
        },
      ],
      ['reports.deleted = false'],
      [
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        {
          searchTerm: '%test%',
        },
      ],
    ]);
    expect(createQueryBuilder.take.mock.calls).toEqual([[20]]);
    expect(createQueryBuilder.skip.mock.calls).toEqual([[40]]);
    expect(createQueryBuilder.orderBy.mock.calls).toEqual([
      ['reports.lastUpdated', 'DESC'],
    ]);
  });

  it('return all organization reports for org admins', async () => {
    mockGetUserInOrganization.mockResolvedValue({
      result: { roles: [{ identifier: OrganizationUserRoles.ORG_ADMIN }] },
    });
    jest
      .spyOn(createQueryBuilder, 'getManyAndCount')
      .mockResolvedValue([[], 60]);
    jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValue([]);
    await analyticsReportsService.getAllAnalyticsReports(mockUserDetails, {
      sortBy: SortBy.LastUpdated,
      sortOrder: SortOrder.DESC,
      types: [
        AnalyticsReportType.ELEMENT_IMPACT,
        AnalyticsReportType.MEDIA_IMPACT,
        AnalyticsReportType.ELEMENT_PRESENCE,
      ],
      searchTerm: 'test',
      offset: 40,
    });

    expect(createQueryBuilder.innerJoinAndSelect.mock.calls).toEqual([
      ['reports.owner', 'owner'],
      ['reports.workspaceMaps', 'rwm'],
      ['reports.platformAdAccountMaps', 'rpaam'],
      ['rpaam.platformAdAccount', 'platformAdAccount'],
      ['reports.filter', 'filter', 'filter.id = reports.filter_id'],
    ]);
    expect(createQueryBuilder.leftJoinAndSelect.mock.calls).toEqual([
      [
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId',
      ],
    ]);
    expect(createQueryBuilder.where.mock.calls).toEqual([
      [
        'reports.reportType IN (:...types)',
        { types: ['ELEMENT_IMPACT', 'MEDIA_IMPACT', 'ELEMENT_PRESENCE'] },
      ],
      [
        'reports.reportType IN (:...types)',
        {
          types: ['ELEMENT_IMPACT', 'MEDIA_IMPACT', 'ELEMENT_PRESENCE'],
        },
      ],
    ]);
    expect(createQueryBuilder.andWhere.mock.calls).toStrictEqual([
      [
        'reports.organizationId = :organizationId',
        { organizationId: mockUserDetails.organizationId },
      ],
      ['reports.deleted = false'],
      [
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        { searchTerm: '%test%' },
      ],
      [
        'reports.organizationId = :organizationId',
        {
          organizationId: '2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c',
        },
      ],
      ['reports.deleted = false'],
      [
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        {
          searchTerm: '%test%',
        },
      ],
    ]);
    expect(createQueryBuilder.take.mock.calls).toEqual([[20]]);
    expect(createQueryBuilder.skip.mock.calls).toEqual([[40]]);
    expect(createQueryBuilder.orderBy.mock.calls).toEqual([
      ['reports.lastUpdated', 'DESC'],
    ]);
  });

  it('return all organization reports for org admins with v2 query', async () => {
    mockGetUserInOrganization.mockResolvedValue({
      result: { roles: [{ identifier: OrganizationUserRoles.ORG_ADMIN }] },
    });
    jest
      .spyOn(createQueryBuilder, 'getManyAndCount')
      .mockResolvedValue([[], 60]);
    jest.spyOn(createQueryBuilder, 'getMany').mockResolvedValue([]);
    await analyticsReportsService.getAllAnalyticsReports(mockUserDetails, {
      sortBy: SortBy.LastUpdated,
      sortOrder: SortOrder.DESC,
      types: [
        AnalyticsReportType.ELEMENT_IMPACT,
        AnalyticsReportType.MEDIA_IMPACT,
        AnalyticsReportType.ELEMENT_PRESENCE,
      ],
      searchTerm: 'test',
      offset: 40,
      dateCreated: {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      },
      lastUpdated: {
        startDate: '2024-01-01',
        endDate: '2024-01-31',
      },
      createdBy: [123, 321],
      channels: [Platform.FACEBOOK, Platform.DV360],
      adAccounts: ['ad123', 'ad321'],
      workspaces: [11111, 22222],
    });

    expect(createQueryBuilder.innerJoinAndSelect.mock.calls).toEqual([
      ['reports.owner', 'owner'],
      ['reports.workspaceMaps', 'rwm'],
      ['reports.platformAdAccountMaps', 'rpaam'],
      ['rpaam.platformAdAccount', 'platformAdAccount'],
      ['reports.filter', 'filter', 'filter.id = reports.filter_id'],
    ]);
    expect(createQueryBuilder.leftJoinAndSelect.mock.calls).toEqual([
      [
        'owner.personOrganizationMaps',
        'personOrganizationMaps',
        'personOrganizationMaps.organizationId = reports.organizationId',
      ],
    ]);
    expect(createQueryBuilder.where.mock.calls).toEqual([
      [
        'reports.reportType IN (:...types)',
        { types: ['ELEMENT_IMPACT', 'MEDIA_IMPACT', 'ELEMENT_PRESENCE'] },
      ],
      [
        'reports.reportType IN (:...types)',
        {
          types: ['ELEMENT_IMPACT', 'MEDIA_IMPACT', 'ELEMENT_PRESENCE'],
        },
      ],
    ]);

    expect(createQueryBuilder.andWhere.mock.calls).toStrictEqual([
      [
        'reports.organizationId = :organizationId',
        { organizationId: mockUserDetails.organizationId },
      ],
      ['reports.deleted = false'],
      [
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        { searchTerm: '%test%' },
      ],
      [
        'DATE(reports.dateCreated) >= DATE(:startDate) AND DATE(reports.dateCreated) <= DATE(:endDate)',
        {
          endDate: '2024-01-31',
          startDate: '2024-01-01',
        },
      ],
      [
        'DATE(reports.lastUpdated) >= DATE(:startDate) AND DATE(reports.lastUpdated) <= DATE(:endDate)',
        {
          endDate: '2024-01-31',
          startDate: '2024-01-01',
        },
      ],
      ['reports.owner_id IN (:createdBy)', { createdBy: [123, 321] }],
      [
        "JSON_UNQUOTE(JSON_EXTRACT(filter.filters, '$.platform')) IN (:channels)",
        { channels: [Platform.FACEBOOK, Platform.DV360] },
      ],
      [
        'reports.organizationId = :organizationId',
        {
          organizationId: '2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c',
        },
      ],
      ['reports.deleted = false'],
      [
        '(reports.name LIKE :searchTerm OR reports.description LIKE :searchTerm)',
        {
          searchTerm: '%test%',
        },
      ],
      [
        'DATE(reports.dateCreated) >= DATE(:startDate) AND DATE(reports.dateCreated) <= DATE(:endDate)',
        {
          endDate: '2024-01-31',
          startDate: '2024-01-01',
        },
      ],
      [
        'DATE(reports.lastUpdated) >= DATE(:startDate) AND DATE(reports.lastUpdated) <= DATE(:endDate)',
        {
          endDate: '2024-01-31',
          startDate: '2024-01-01',
        },
      ],
      ['reports.owner_id IN (:createdBy)', { createdBy: [123, 321] }],
      [
        "JSON_UNQUOTE(JSON_EXTRACT(filter.filters, '$.platform')) IN (:channels)",
        { channels: [Platform.FACEBOOK, Platform.DV360] },
      ],
    ]);

    expect(createQueryBuilder.take.mock.calls).toEqual([[20]]);
    expect(createQueryBuilder.skip.mock.calls).toEqual([[40]]);
    expect(createQueryBuilder.orderBy.mock.calls).toEqual([
      ['reports.lastUpdated', 'DESC'],
    ]);
  });
});

describe('AnalyticsReportsService saveAnalyticsReport', () => {
  it('successfully save report', async () => {
    mockPersonFindOneBy.mockReturnValue(mockUser);
    mockReportSave.mockResolvedValue(expectedReportToSave);
    await analyticsReportsService.saveAnalyticsReport(
      mockUserDetails,
      mockCreateAnalyticsReportDto,
    );

    expect(
      mockReportMapInsert.mock.calls.some(
        (call) =>
          JSON.stringify(call[1]) ===
          JSON.stringify([
            { reportId: mockUUIDReturnValue, workspaceId: mockWorkspaceId },
          ]),
      ),
    ).toBe(true);
    expect(mockReportSave).toHaveBeenCalledWith(expectedReportToSave);
  });

  it('successfully save creative leaderboard report', async () => {
    mockPersonFindOneBy.mockReturnValue(mockUser);
    mockReportSave.mockResolvedValue(expectedLeaderboardReportToSave);
    await analyticsReportsService.saveAnalyticsReport(
      mockUserDetails,
      mockCreateCreativeLeaderboardReportDto,
    );

    expect(mockReportSave).toHaveBeenCalledWith(
      expectedLeaderboardReportToSave,
    );
  });

  it('fail if partner/workspace user access validation fails', async () => {
    mockPersonFindOneBy.mockReturnValue(mockUser);
    await expect(
      analyticsReportsService.saveAnalyticsReport(mockUserDetails, {
        ...mockCreateAnalyticsReportDto,
        filters: {
          ...mockCreateAnalyticsReportDto.filters,
          workspaceIds: [
            ...mockCreateAnalyticsReportDto.filters.workspaceIds,
            9999,
          ],
        },
      }),
    ).rejects.toThrow('Missing access to some workspaces in request');
  });

  it('fail if user is missing access to ad accounts in filter', async () => {
    mockPersonFindOneBy.mockReturnValue(mockUser);
    await expect(
      analyticsReportsService.saveAnalyticsReport(mockUserDetails, {
        ...mockCreateAnalyticsReportDto,
        filters: {
          ...mockCreateAnalyticsReportDto.filters,
          adAccountIds: ['invalid-account-id'],
        },
      }),
    ).rejects.toThrow(
      'Failed to save analytics report for user 1234 in organization 2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c. Missing access to some accounts in request. Note that all accounts in a report must be for the same platform/channel',
    );
  });
});

describe('AnalyticsReportsService updateAnalyticsReport', () => {
  it('successfully update saved analytics report', async () => {
    mockReportsFindOne.mockReturnValue(expectedReportToSave);
    mockReportSave.mockResolvedValue(expectedUpdatedReportToSave);

    mockPersonFindOneBy.mockReturnValue(mockUser);
    await analyticsReportsService.updateAnalyticsReport(
      mockUserDetails,
      mockUUIDReturnValue,
      mockUpdateAnalyticsReportDto,
    );

    expect(mockReportMapDelete).toHaveBeenCalled();
    expect(
      mockReportMapInsert.mock.calls.some(
        (call) =>
          JSON.stringify(call[1]) ===
          JSON.stringify([
            { reportId: mockUUIDReturnValue, workspaceId: mockWorkspaceId },
          ]),
      ),
    ).toBe(true);
    expect(mockReportSave).toHaveBeenCalledWith(expectedUpdatedReportToSave);
  });

  it('successfully update a saved leaderboard report', async () => {
    mockReportsFindOne.mockReturnValue(expectedLeaderboardReportToSave);
    mockReportSave.mockResolvedValue(expectedUpdatedLeaderboardReportToSave);

    mockPersonFindOneBy.mockReturnValue(mockUser);
    await analyticsReportsService.updateAnalyticsReport(
      mockUserDetails,
      mockUUIDReturnValue,
      mockUpdateLeaderboardReportDto,
    );

    expect(mockReportSave).toHaveBeenCalledWith(
      expectedUpdatedLeaderboardReportToSave,
    );
  });

  it('fail if report is not found for the organization', async () => {
    mockReportsFindOne.mockReturnValue(undefined);
    await expect(
      analyticsReportsService.updateAnalyticsReport(
        mockUserDetails,
        mockUUIDReturnValue,
        mockUpdateAnalyticsReportDto,
      ),
    ).rejects.toThrow(
      `Report ${mockUUIDReturnValue} not found in organization ${mockUserDetails.organizationId}.`,
    );
  });

  it('fail if user is not owner', async () => {
    mockReportsFindOne.mockReturnValue({
      ...expectedReportToSave,
      owner: { id: 9999 },
    });
    await expect(
      analyticsReportsService.updateAnalyticsReport(
        mockUserDetails,
        mockUUIDReturnValue,
        mockUpdateAnalyticsReportDto,
      ),
    ).rejects.toThrow(
      'Error fetching report mocked-uuid-value for user 1234. User is not owner of report.',
    );
  });
});

describe('AnalyticsReportsService deleteAnalyticsReportForUser', () => {
  it('delete saved analytics report owned by user', async () => {
    mockReportsFindOne.mockReturnValue(expectedReportToSave);
    mockPersonFindOneBy.mockReturnValue(mockUser);
    await analyticsReportsService.deleteAnalyticsReportForUser(
      mockUserDetails,
      mockUUIDReturnValue,
    );

    expect(mockReportSave).toHaveBeenCalledWith(expectedReportToDelete);
  });

  it('fail delete report if user is not owner', async () => {
    mockReportsFindOne.mockReturnValue({
      ...expectedReportToSave,
      owner: { id: 9999 },
    });
    await expect(
      analyticsReportsService.deleteAnalyticsReportForUser(
        mockUserDetails,
        mockUUIDReturnValue,
      ),
    ).rejects.toThrow(
      'Error fetching report mocked-uuid-value for user 1234. User is not owner of report.',
    );
  });
});

describe('AnalyticsReportsService getAnalyticsReportFilterOptions', () => {
  it('successfully get analytics report filter options', async () => {
    axios.post = jest.fn().mockResolvedValue({
      data: {
        result: {
          stats: mockCampaignStats,
        },
      },
    });
    const campaignIdentifierFilterOptions =
      await analyticsReportsService.getAnalyticsReportFilterOptions(
        mockUserDetails,
        mockGetAnalyticsReportFilterOptionsRequestDto,
      );
    expect(campaignIdentifierFilterOptions).toEqual([
      mockCampaignStats[0].campaign,
      mockCampaignStats[1].campaign,
    ]);
  });

  it('fails if campaignIdentifier request is missing adAccountIds or dateRange', async () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { adAccountIds, dateRange, ...remainingRequest } =
      mockGetAnalyticsReportFilterOptionsRequestDto;
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(mockUserDetails, {
        ...remainingRequest,
      }),
    ).rejects.toThrow(
      'Ad account ids are required for ad identifier filter type campaignIdentifier',
    );
  });

  it('fails if filter type is unsupported', async () => {
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(mockUserDetails, {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        filterType: 'unsupported',
      }),
    ).rejects.toThrow(
      'Error fetching filter unsupported for user 1234 in organization 2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c. Unsupported filter type',
    );
  });

  it('fails if platform does not support campaignObjective filter', async () => {
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(mockUserDetails, {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.CAMPAIGN_OBJECTIVE,
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        platform: 'reddit',
      }),
    ).rejects.toThrow(
      'Platform reddit does not support campaign objectives filter type',
    );
  });

  it('fails if filter requires adAccountIds and they are invalid for organization', async () => {
    mockValidateAdAccountsWithinOrganizationAsPromise.mockResolvedValue({
      result: {
        areAllAccountsValidForOrganization: false,
      },
    });
    axios.post = jest.fn().mockResolvedValue({
      data: {
        result: {
          stats: mockCampaignStats,
        },
      },
    });
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(
        mockUserDetails,
        mockGetAnalyticsReportFilterOptionsRequestDto,
      ),
    ).rejects.toThrow(
      `Ad account ids are not valid for organization ${mockOrganizationId}`,
    );
  });

  it('fails if facebook KPI request with adAccountIds fails validation', async () => {
    mockValidateAdAccountsWithinOrganizationAsPromise.mockResolvedValue({
      result: {
        areAllAccountsValidForOrganization: false,
      },
    });
    mockGetOneOrganization.mockResolvedValue({
      result: {
        id: mockOrganizationId,
        name: 'Test Organization',
      },
    });
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(mockUserDetails, {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.KPI,
      }),
    ).rejects.toThrow(
      `Ad account ids are not valid for organization ${mockOrganizationId}`,
    );
  });

  it('correctly query both platform and conversion KPI options for facebook when ad account ids are present', async () => {
    mockValidateAdAccountsWithinOrganizationAsPromise.mockResolvedValue({
      result: {
        success: true,
      },
    });
    mockGetPlatformKpisAsPromise.mockResolvedValue({
      result: [{ id: '94', name: 'Dummy platform KPI' }],
    });
    mockGetCustomConversionKpisForAdAccountsAsPromise.mockResolvedValue({
      result: [{ id: '***********:1', name: 'Dummy custom conversion KPI' }],
    });
    mockGetOneOrganization.mockResolvedValue({
      result: {
        id: mockOrganizationId,
        name: 'Test Organization',
      },
    });
    const kpis = await analyticsReportsService.getAnalyticsReportFilterOptions(
      mockUserDetails,
      {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.KPI,
      },
    );
    expect(kpis).toEqual([
      { id: '94', name: 'Dummy platform KPI', isEnabled: true },
      {
        id: '***********:1',
        name: 'Dummy custom conversion KPI',
        isEnabled: true,
      },
    ]);
    expect(mockGetPlatformKpisAsPromise).toHaveBeenCalled();
    expect(
      mockGetCustomConversionKpisForAdAccountsAsPromise,
    ).toHaveBeenCalled();
  });

  it('only query platform KPI options for facebook when no ad account ids in request', async () => {
    mockGetPlatformKpisAsPromise.mockResolvedValue({
      result: [{ id: '94', name: 'Dummy platform KPI' }],
    });
    mockGetCustomConversionKpisForAdAccountsAsPromise.mockResolvedValue({
      result: [{ id: '***********:1', name: 'Dummy custom conversion KPI' }],
    });
    mockGetOneOrganization.mockResolvedValue({
      result: {
        id: mockOrganizationId,
        name: 'Test Organization',
      },
    });
    const kpis = await analyticsReportsService.getAnalyticsReportFilterOptions(
      mockUserDetails,
      {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.KPI,
        adAccountIds: undefined,
      },
    );
    expect(kpis).toEqual([
      { id: '94', name: 'Dummy platform KPI', isEnabled: true },
    ]);
    expect(mockGetPlatformKpisAsPromise).toHaveBeenCalled();
    expect(mockGetCustomConversionKpisForAdAccountsAsPromise).toBeCalledTimes(
      0,
    );
  });

  it('only query platform KPI options for non facebook platforms', async () => {
    mockGetPlatformKpisAsPromise.mockResolvedValue({
      result: [{ id: '94', name: 'Dummy platform KPI' }],
    });
    mockGetCustomConversionKpisForAdAccountsAsPromise.mockResolvedValue({
      result: [{ id: '***********:1', name: 'Dummy custom conversion KPI' }],
    });
    mockGetOneOrganization.mockResolvedValue({
      result: {
        id: mockOrganizationId,
        name: 'Test Organization',
      },
    });
    const kpis = await analyticsReportsService.getAnalyticsReportFilterOptions(
      mockUserDetails,
      {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.KPI,
        platform: Platform.DV360,
      },
    );
    expect(kpis).toEqual([
      { id: '94', name: 'Dummy platform KPI', isEnabled: true },
    ]);
    expect(mockGetPlatformKpisAsPromise).toHaveBeenCalled();
    expect(mockGetCustomConversionKpisForAdAccountsAsPromise).toBeCalledTimes(
      0,
    );
  });

  it('returns isEnabled as false for spend KPIs that are not enabled in the organization', async () => {
    mockGetOneOrganization.mockResolvedValue({
      result: {
        id: mockOrganizationId,
        name: 'Test Organization',
        spendEnabled: false,
      },
    });
    mockGetPlatformKpisAsPromise.mockResolvedValue({
      result: [{ id: '94', name: 'Dummy platform KPI', format: 'SPEND' }],
    });

    const kpis = await analyticsReportsService.getAnalyticsReportFilterOptions(
      mockUserDetails,
      {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.KPI,
        adAccountIds: undefined,
      },
    );

    expect(kpis).toEqual([
      {
        id: '94',
        name: 'Dummy platform KPI',
        isEnabled: false,
        format: 'SPEND',
      },
    ]);
  });

  it('fails if Criteria Groups request is missing workspace ids', async () => {
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: {
        areAllWorkspacesValidForOrganization: false,
      },
    });
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(mockUserDetails, {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        workspaceIds: [],
        filterType: FilterType.CRITERIA,
      }),
    ).rejects.toThrow(
      `Workspace ids are required for filter type ${FilterType.CRITERIA}`,
    );
  });

  it('fails if Criteria Groups request workspace ids fail validation', async () => {
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: {
        areAllWorkspacesValidForOrganization: false,
      },
    });
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptions(mockUserDetails, {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        filterType: FilterType.CRITERIA,
      }),
    ).rejects.toThrow(
      `Workspace ids are not valid for organization ${mockOrganizationId}`,
    );
  });

  it('correctly queries the Scoring service for Criteria Groups', async () => {
    mockValidateWorkspacesWithinOrganizationAsPromise.mockResolvedValue({
      result: {
        success: true,
      },
    });
    mockSearchGroupedCriteriaAsPromise.mockResolvedValue({
      result: mockCriteriaGroups,
    });

    const criteriaGroups =
      await analyticsReportsService.getAnalyticsReportFilterOptions(
        mockUserDetails,
        {
          ...mockGetAnalyticsReportFilterOptionsRequestDto,
          filterType: FilterType.CRITERIA,
        },
        { offset: 0, perPage: 20 },
      );
    expect(criteriaGroups).toEqual({ result: mockCriteriaGroups });
    expect(mockSearchGroupedCriteriaAsPromise).toHaveBeenCalled();
  });

  it('fails to fetch identifier filters when user is missing access to ad account ids', async () => {
    mockValidateAdAccountsWithinOrganizationAsPromise.mockResolvedValueOnce({
      result: {
        success: false,
      },
    });
    await expect(
      analyticsReportsService.getAnalyticsReportFilterOptionsV2(
        mockUserDetails,
        {
          ...mockGetAnalyticsReportFilterOptionsRequestDto,
          filterType: FilterType.AD_IDENTIFIER,
        },
      ),
    ).rejects.toThrow(
      `Error fetching filter adIdentifier for user 1234 in organization 2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c. Ad account ids are not valid for organization ${mockOrganizationId} and workspaces ${mockWorkspaceId}`,
    );
  });

  it('correctly queries the report filters service for identifier filters', async () => {
    await analyticsReportsService.getAnalyticsReportFilterOptionsV2(
      mockUserDetails,
      {
        ...mockGetAnalyticsReportFilterOptionsRequestDto,
        searchString: 'OM|',
      },
      { offset: 50, perPage: 25 },
    );

    expect(mockGetReportIdentifierFilterValuesAsPromise).toHaveBeenCalledWith(
      FilterType.CAMPAIGN_IDENTIFIER,
      {
        platform: Platform.FACEBOOK,
        adAccountIds: [mockFirstPlatformAccountId, mockSecondPlatformAccountId],
        startDate: '2021-01-01',
        endDate: '2021-10-23',
        searchString: 'OM|',
      },
      50,
      25,
    );
  });

  it('correctly queries the report filters service for dimension filters', async () => {
    await analyticsReportsService.getAnalyticsReportFilterOptionsV2(
      mockUserDetails,
      mockAdwordsGetAnalyticsReportFilterOptionsRequestDto,
    );

    expect(mockGetReportDimensionFilterValuesAsPromise).toHaveBeenCalledWith(
      Platform.ADWORDS,
      FilterType.AD_TYPE,
    );
  });
});
