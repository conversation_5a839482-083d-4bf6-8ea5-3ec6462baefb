import {
  AnalyticsReportDto,
  AnalyticsReportFilterDto,
  LeaderboardReportDto,
  LeaderboardReportFiltersDto,
  UserAccessStatusDto,
  Workspace,
} from '../dto/analytics-report.dto';
import {
  generateList,
  generateRandomBoolean,
  generateRandomId,
  generateRandomInteger,
  generateRandomLoremWords,
  generateRandomStringDate,
  getRandomSopranosCharacter,
  randomEnumValue,
  randomListValue,
} from '../../../utils/random';
import { Platform } from '../../../constants/platform.constants';
import {
  AnalyticsReportGroupByDto,
  BaseAnalyticsReportFilterMetadataDto,
  GroupByColumns,
  GroupByRows,
  ImpressionsRange,
  MediaType,
  NormsConfiguration,
  StatAverage,
  StatBaseComparison,
  StatCompareAgainst,
  StatConfidence,
  StatMinimumTagConfidenceLevel,
  StatSettings,
} from '../dto/create-analytics-report.dto';
import { ENTITY_PERMISSIONS } from '../../../constants/permission.constants';
import { AnalyticsReportType } from '../model/analytics-report';
import { Sort } from '../../../reports/model/sort';
import { SortOrder } from '../../../reports/model/sort-order';
import { ReadReportUserDto } from '../dto/read-report-user.dto';
import { getDateString } from '../../../utils/date-utils';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';

const createStubNormsConfiguration = (): NormsConfiguration => {
  const startDate = generateRandomStringDate(new Date('2018-01-01'));
  return {
    dateRange: {
      scope: generateRandomLoremWords(1),
      scopeKey: generateRandomLoremWords(1),
      startDate: startDate,
      endDate: generateRandomStringDate(new Date(startDate)),
    },
    industries: {
      industryIds: generateList(3, () => generateRandomId(8)),
      subIndustryIds: generateList(2, () => generateRandomId(8)),
    },
  };
};

const createStubImpressionRange = (): ImpressionsRange => {
  return {
    minimum: generateRandomInteger(0, 5000),
    maximum: generateRandomInteger(5000, 10000),
  };
};

const createStubStatSettings = (): StatSettings => {
  return {
    direction: randomEnumValue(StatCompareAgainst) as StatCompareAgainst,
    statConfidence: randomEnumValue(StatConfidence) as StatConfidence,
    average: randomEnumValue(StatAverage) as StatAverage,
    baseComparison: randomEnumValue(StatBaseComparison) as StatBaseComparison,
    minimumTagConfidenceLevel: randomEnumValue(
      StatMinimumTagConfidenceLevel,
    ) as StatMinimumTagConfidenceLevel,
  };
};

const createStubAdAccount = (platform: Platform): ReadPlatformAdAccountDto => {
  const createdDate = generateRandomStringDate(new Date('2018-01-01'));
  return {
    id: generateRandomId(10),
    platform: platform,
    platformAccountName: generateRandomLoremWords(2),
    platformAccountId: generateRandomId(12).toString(),
    dateCreated: createdDate,
    processingCompleted: 1,
    processingCompletedDate: new Date(
      new Date(createdDate).getDate() + 1,
    ).toUTCString(),
    lastUpdated: generateRandomStringDate(new Date(createdDate)),
    canAccess: 1,
    lastSuccessfulProcessingDate: generateRandomStringDate(
      new Date(createdDate),
    ),
  };
};

const createStubWorkspace = (): Workspace => {
  return {
    id: generateRandomId(5),
    name: generateRandomLoremWords(1),
  };
};

const createStubCreatedBy = (): ReadReportUserDto => {
  const user = getRandomSopranosCharacter();
  return {
    id: generateRandomId(9),
    firstName: user.firstName,
    lastName: user.lastName,
    displayName: `${user.firstName.split('')[0]}${user.lastName}}`,
    photo: '',
  };
};

const createStubGroupBy = (
  overrides: Partial<AnalyticsReportGroupByDto>,
): AnalyticsReportGroupByDto => {
  return {
    columns: randomEnumValue(GroupByColumns) as GroupByColumns,
    rows: randomEnumValue(GroupByRows) as GroupByRows,
    ...overrides,
  };
};

const createStubSortBy = (overrides: Partial<Sort>): Sort => {
  return {
    sortBy: generateRandomLoremWords(1),
    sortOrder: randomEnumValue(SortOrder) as SortOrder,
    ...overrides,
  };
};

const createStubUserAccessStatus = (
  overrides: Partial<UserAccessStatusDto>,
): UserAccessStatusDto => {
  return {
    accountsAccessStatus: randomListValue([
      ENTITY_PERMISSIONS.ALLOW_PERMISSION,
      ENTITY_PERMISSIONS.PARTIAL_PERMISSION,
      ENTITY_PERMISSIONS.DENY_PERMISSION,
    ]),
    workspacesAccessStatus: randomListValue([
      ENTITY_PERMISSIONS.ALLOW_PERMISSION,
      ENTITY_PERMISSIONS.PARTIAL_PERMISSION,
      ENTITY_PERMISSIONS.DENY_PERMISSION,
    ]),
    ...overrides,
  };
};

const createStubLeaderboardFilters = (
  overrides: Partial<LeaderboardReportFiltersDto>,
): LeaderboardReportFiltersDto => {
  const startDate = generateRandomStringDate(new Date('2018-01-01'));
  const platform: Platform = randomEnumValue(Platform) as Platform;
  return {
    adAccounts: generateList(2, () => createStubAdAccount(platform)),
    dateRange: {
      startDate: startDate,
      endDate: generateRandomStringDate(new Date(startDate)),
      ...overrides.dateRange,
    },
    kpiId: generateRandomInteger(0, 200).toString(),
    kpiName: generateRandomLoremWords(2),
    limit: generateRandomInteger(1, 2000),
    limitPerAdAccount: generateRandomInteger(2, 500),
    minimumDaysLive: generateRandomInteger(0, 90),
    minimumImpressionsPerCreative: generateRandomInteger(100, 10000),
    organizationId: generateRandomId(16).toString(),
    workspaces: generateList(3, () => createStubWorkspace()),
    platform: platform,
    mediaTypes: [randomEnumValue(MediaType) as MediaType],
    ...overrides,
  };
};

const createOptionalFilters =
  (): Partial<BaseAnalyticsReportFilterMetadataDto> => {
    return {
      adTypes: generateList(5, () => generateRandomLoremWords(1)),
      campaignIds: generateList(10, () => generateRandomId(1)),
      campaignSearchStrings: generateList(3, () => generateRandomLoremWords(1)),
      adsetIds: generateList(8, () => generateRandomId(8)),
      adsetSearchStrings: generateList(5, () => generateRandomLoremWords(1)),
      adIds: generateList(5, () => generateRandomId(8)),
      adSearchString: generateList(5, () => generateRandomLoremWords(1)),
      creativeImpressionsRange: createStubImpressionRange(),
      adImpressionsRange: createStubImpressionRange(),
      objectives: generateList(5, () => generateRandomLoremWords(1)),
      placements: generateList(2, () => generateRandomLoremWords(2)),
      criteriaGroupIds: generateList(3, () => generateRandomLoremWords(1)),
      selectedRows: generateList(3, () =>
        generateList(3, () => generateRandomLoremWords(1)),
      ),
      isDefaultKPITimeRangeFilterActive: generateRandomBoolean(),
      createdByVidmob: generateRandomBoolean(),
      normsConfiguration: createStubNormsConfiguration(),
    };
  };

const createStubAnalyticsFilters = (
  overrides: Partial<AnalyticsReportFilterDto>,
  includeOptionalFilters: boolean,
): AnalyticsReportFilterDto => {
  const startDate = generateRandomStringDate(new Date('2018-01-01'));
  const platform: Platform = randomEnumValue(Platform) as Platform;
  let filters: AnalyticsReportFilterDto = {
    organizationId: generateRandomId(10).toString(),
    workspaces: generateList(2, () => createStubWorkspace()),
    adAccounts: generateList(2, () => createStubAdAccount(platform)),
    platform: platform,
    dateRange: {
      startDate: startDate,
      endDate: generateRandomStringDate(new Date(startDate)),
      ...overrides.dateRange,
    },
    viewBy: randomEnumValue(GroupByColumns) as GroupByColumns,
    mediaTypes: [randomEnumValue(MediaType) as MediaType],
    isShowAppAdsOnlyActive: generateRandomBoolean(),
    isShowSparkAdsOnlyActive: generateRandomBoolean(),
    statSettings: createStubStatSettings(),
    kpiId: generateRandomInteger(0, 100).toString(),
  };

  if (includeOptionalFilters) {
    filters = {
      ...filters,
      ...createOptionalFilters(),
    };
  }

  return filters;
};

export const createStubLeaderboardDto = (
  overrides: Partial<LeaderboardReportDto>,
): LeaderboardReportDto => {
  const dateCreated = getDateString(new Date('2018-01-01'));
  return {
    id: generateRandomId(16).toString(),
    createdBy: createStubCreatedBy(),
    dateCreated: new Date(dateCreated),
    lastUpdated: new Date(getDateString(new Date(dateCreated))),
    name: generateList(3, () => generateRandomLoremWords(1)).join(' '),
    description: generateRandomLoremWords(10),
    reportType: AnalyticsReportType.CREATIVE_LEADERBOARD,
    sortBy: createStubSortBy({ ...(overrides.sortBy ?? {}) }),
    groupBy: createStubGroupBy({ ...(overrides.groupBy ?? {}) }),
    filters: createStubLeaderboardFilters({ ...(overrides.filters ?? {}) }),
    filtersVersion: 1,
    userAccessStatus: createStubUserAccessStatus({
      ...(overrides.userAccessStatus ?? {}),
    }),
  };
};

export const createStubAnalyticsReportDto = (
  reportType: AnalyticsReportType,
  includeOptionalFilters: boolean,
): AnalyticsReportDto => {
  const dateCreated = getDateString(new Date('2018-01-01'));
  return {
    createdBy: createStubCreatedBy(),
    dateCreated: new Date(dateCreated),
    description: generateRandomLoremWords(8),
    filters: createStubAnalyticsFilters({}, includeOptionalFilters),
    filtersVersion: 1,
    groupBy: createStubGroupBy({}),
    id: generateRandomId(16).toString(),
    lastUpdated: new Date(generateRandomStringDate(new Date(dateCreated))),
    name: generateRandomLoremWords(4),
    reportType: reportType,
    sortBy: createStubSortBy({}),
    userAccessStatus: createStubUserAccessStatus({}),
  };
};
