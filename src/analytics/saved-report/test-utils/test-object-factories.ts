import { UserDetailsDto } from '../../dto/user-details.dto';
import { randomUUID } from 'crypto';
import {
  generateList,
  generateRandomId,
  generateRandomLoremWords,
  generateRandomString,
  generateRandomStringDate,
  getRandomSopranosCharacter,
  randomEnumValue,
} from '../../../utils/random';
import { GetAnalyticsReportFilterOptionsDto } from '../dto/get-analytics-report-options.dto';
import { AnalyticsReportType, Person, Report } from '../model/analytics-report';
import { SortOrder } from '../../../reports/model/sort-order';
import { SortBy } from '../../../reports/reports-enums';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';
import { Workspace } from '../dto/analytics-report.dto';
import { Platform } from '@vidmob/vidmob-nestjs-common';

// USER
export const createStubUserDetails = (
  overrides: Partial<UserDetailsDto>,
): UserDetailsDto => {
  return {
    organizationId: randomUUID(),
    authorization: 'Bearer fake',
    userId: generateRandomId(5),
    ...overrides,
  };
};

//Analytics Report Filters
export const createStubAnalyticsReportFilterOptions = (
  overrides: Partial<GetAnalyticsReportFilterOptionsDto>,
): GetAnalyticsReportFilterOptionsDto => {
  return {
    types: [randomEnumValue(AnalyticsReportType) as AnalyticsReportType],
    adAccounts: generateList(3, () => generateRandomId(10)),
    sortOrder: randomEnumValue(SortOrder) as SortOrder,
    sortBy: randomEnumValue(SortBy) as SortBy,
    ...overrides,
  };
};

export interface UserAdAccountsAndWorkspacesDto {
  adAccounts: ReadPlatformAdAccountDto[];
  workspaces: Workspace[];
}

// UserAdAccountsAndWorksapcesDto
export const createStubReadPlatformAdAcccountDto = (
  overrides: Partial<ReadPlatformAdAccountDto>,
): ReadPlatformAdAccountDto => {
  return {
    id: generateRandomId(10),
    platform: randomEnumValue(Platform) as string,
    platformAccountName: generateRandomLoremWords(2),
    platformAccountId: String(generateRandomId(12)),
    dateCreated: generateRandomStringDate(new Date('01-01-2025')),
    processingCompleted: 1,
    processingCompletedDate: generateRandomStringDate(new Date('01-01-2025')),
    lastUpdated: generateRandomStringDate(new Date('01-01-2025')),
    canAccess: 1,
    lastSuccessfulProcessingDate: generateRandomStringDate(
      new Date('01-01-2025'),
    ),
    ...overrides,
  };
};

export const createStubWorkspace = (
  overrides: Partial<Workspace>,
): Workspace => {
  return {
    id: generateRandomId(4),
    name: generateRandomLoremWords(2),
    ...overrides,
  };
};

export const createStubUserAdAccountsAndWorkspacedDto = (
  overrides: Partial<UserAdAccountsAndWorkspacesDto>,
): UserAdAccountsAndWorkspacesDto => {
  return {
    adAccounts: generateList(2, () => createStubReadPlatformAdAcccountDto({})),
    workspaces: generateList(2, () => createStubWorkspace({})),
    ...overrides,
  };
};

export const createPerson = (overrides: Partial<Person>): Person => {
  const person = getRandomSopranosCharacter();
  return {
    id: generateRandomId(5),
    username: person.firstName[0] + person.lastName,
    displayName: person.firstName + ' ' + person.lastName,
    firstName: person.firstName,
    lastName: person.lastName,
    email: `${person.firstName[0]}${person.lastName}@vidmob.com`,
    photo: 'some_path.jpg',
    deactivatedUser: false,
    ...overrides,
  };
};

export const createReport = (
  overrides: Partial<Report>,
  count: number,
): Report => {
  return {
    id: generateRandomString(10),
    name: generateRandomLoremWords(2),
    description: generateRandomLoremWords(4),
    reportType: randomEnumValue(AnalyticsReportType) as AnalyticsReportType,
    workspaces: generateList(2, () => createStubWorkspace({})),
    adAccounts: generateList(2, () => generateRandomId(6).toString()),
    platform: randomEnumValue(Platform) as Platform,
    owner: createPerson({}),
    dateCreated: generateRandomStringDate(new Date('01-01-2025')),
    lastUpdated: generateRandomStringDate(new Date('01-01-2025')),
    count: count,
    ...overrides,
  };
};
