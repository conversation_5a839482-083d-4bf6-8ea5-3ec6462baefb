import { Workspace } from '../dto/analytics-report.dto';
import { ReadPlatformAdAccountDto } from '@vidmob/vidmob-organization-service-sdk/dist/model/readPlatformAdAccountDto';
import { Platform } from '@vidmob/vidmob-nestjs-common';

export enum AnalyticsReportType {
  ELEMENT_IMPACT = 'ELEMENT_IMPACT',
  MEDIA_IMPACT = 'MEDIA_IMPACT',
  ELEMENT_PRESENCE = 'ELEMENT_PRESENCE',
  CREATIVE_LEADERBOARD = 'CREATIVE_LEADERBOARD',
  CRITERIA_PERFORMANCE = 'CRITERIA_PERFORMANCE',
  CREATIVE_MANAGER = 'CREATIVE_MANAGER',
}

// reports to be shown on the report landing page
export const VISIBLE_ANALYTICS_REPORT_TYPES = [
  AnalyticsReportType.ELEMENT_IMPACT,
  AnalyticsReportType.MEDIA_IMPACT,
  AnalyticsReportType.ELEMENT_PRESENCE,
  AnalyticsReportType.CREATIVE_LEADERBOARD,
  AnalyticsReportType.CRITERIA_PERFORMANCE,
];

export interface UserAdAccountsAndWorkspacesDto {
  adAccounts: ReadPlatformAdAccountDto[];
  workspaces: Workspace[];
}

export interface Person {
  id: number;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  photo: string;
  displayName: string;
  deactivatedUser: boolean;
}

export interface Report {
  id: string;
  name: string;
  description: string;
  reportType: AnalyticsReportType;
  workspaces: Workspace[];
  adAccounts: string[];
  platform: Platform;
  owner: Person;
  dateCreated: string;
  lastUpdated: string;
  count: number;
}
