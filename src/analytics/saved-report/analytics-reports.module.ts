import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReportFilter } from '../../entities/report-filter.entity';
import { Report } from '../../entities/report.entity';
import { Person } from '../../entities/person.entity';
import { AnalyticsReportsService } from './analytics-reports.service';
import { AnalyticsReportsController } from './analytics-reports.controller';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { AnalyticsReportConverter } from './util/analytics-report-converter';
import { AnalyticsReportFactory } from './util/analytics-report-factory';
import { LegacyAnalyticsService } from '../../legacy-services/legacy-analytics.service';
import { ConfigService } from '@nestjs/config';
import { CreateAnalyticsReportValidationPipe } from './validators/create-analytics-report-validation-pipe';
import { HttpModule } from '@nestjs/axios';
import { PersonOrganizationMap } from 'src/entities/person-organization-map.entity';
import { AnalyticsUserService } from '../analytics-user-service/analytics-user-service';
import { Organization } from 'src/entities/organization.entity';
import { ReportWorkspaceMap } from '../../entities/report-workspace-map.entity';
import { ReportPlatformAccountMap } from '../../entities/report-platform-account-map.entity';
import { ReportPlatformAdAccountMap } from '../../entities/report-platform-ad-account-map.entity';
import { OrganizationUserService } from '../../account-management/organization/organization-user/organization-user.service';
import { Workspace } from '../../entities/workspace.entity';
import { PlatformAdAccount } from '../../entities/platform-ad-account.entity';
import { ReportChannelMap } from 'src/entities/report-channel.map.entity';
import { ReportListService } from './services/report-list-service';
import { MySQLClientService } from '../../common/services/mysql-client-wrapper';

@Module({
  imports: [
    HttpModule,
    TypeOrmModule.forFeature([
      ReportFilter,
      Report,
      ReportWorkspaceMap,
      ReportPlatformAccountMap,
      ReportPlatformAdAccountMap,
      ReportChannelMap,
      Person,
      PersonOrganizationMap,
      Organization,
      Workspace,
      PlatformAdAccount,
    ]),
  ],
  providers: [
    AnalyticsReportsService,
    WorkspaceService,
    AnalyticsReportConverter,
    AuthService,
    AnalyticsReportFactory,
    LegacyAnalyticsService,
    ConfigService,
    CreateAnalyticsReportValidationPipe,
    AnalyticsUserService,
    OrganizationUserService,
    ReportListService,
    MySQLClientService,
  ],
  controllers: [AnalyticsReportsController],
  exports: [AnalyticsReportsService, AnalyticsUserService],
})
export class AnalyticsReportsModule {}
