import { Test, TestingModule } from '@nestjs/testing';
import { LeaderboardService } from './leaderboard.service';
import { DimensionPerformanceService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PersonService } from '@vidmob/vidmob-authorization-service-sdk';
import { WorkspaceAdAccountService } from '@vidmob/vidmob-organization-service-sdk';
import { LeaderboardController } from './leaderboard.controller';
import { mockOrganizationId } from '../saved-report/mocks/analytics-report.mock';
import { Platform } from '../../constants/platform.constants';
import { GetLeaderboardRequestDto } from './dto/get-leaderboard-request.dto';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

function createLeaderboardRequest(
  overrides: Partial<GetLeaderboardRequestDto>,
): GetLeaderboardRequestDto {
  const baseRequest: GetLeaderboardRequestDto = {
    startDate: '2020-08-10',
    endDate: '2020-08-17',
    workspaces: [123, 4556],
    platform: Platform.FACEBOOK,
    kpiId: '1',
    limit: 1000,
    minimumImpressionsPerCreative: 1000,
    limitPerAdAccount: 20,
    minimumDaysLive: 7,
    currency: 'USD',
  };

  return {
    ...baseRequest,
    ...overrides,
  };
}

describe('LeaderboardController', () => {
  const mockGetLeaderboard = jest.fn();
  const mockGetLeaderboardAccountConnectionStatus = jest.fn();
  const mockGetLeaderboardDataForOrganization = jest.fn();
  const mockGetLeaderboardAccountConnectionStatusForOrganization = jest.fn();
  let leaderboardController: LeaderboardController;
  let reportSpendPermissionService: ReportSpendPermissionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [LeaderboardController],
      providers: [
        {
          provide: LeaderboardService,
          useValue: {
            getLeaderboardData: mockGetLeaderboard,
            getLeaderboardAccountConnectionStatus:
              mockGetLeaderboardAccountConnectionStatus,
            getLeaderboardDataForOrganization:
              mockGetLeaderboardDataForOrganization,
            getLeaderboardAccountConnectionStatusForOrganization:
              mockGetLeaderboardAccountConnectionStatusForOrganization,
          },
        },
        {
          provide: DimensionPerformanceService,
          useValue: {
            getTopPerformingCreativesAsPromise: jest.fn(),
          },
        },
        {
          provide: WorkspaceAdAccountService,
          useValue: {
            findAllAdAccountsForAWorkspace: jest.fn(),
          },
        },
        {
          provide: PersonService,
          useValue: {
            getAllRolesForUser: jest.fn(),
          },
        },
        {
          provide: ReportSpendPermissionService,
          useValue: {
            validateOrganizationAccessToSpendKpi: jest.fn(),
          },
        },
      ],
    }).compile();

    leaderboardController = module.get<LeaderboardController>(
      LeaderboardController,
    );
    reportSpendPermissionService = module.get<ReportSpendPermissionService>(
      ReportSpendPermissionService,
    );
  });

  it('should be defined', () => {
    expect(leaderboardController).toBeDefined();
  });

  it('should call getLeaderboard', async () => {
    const baseLeaderboardRequest = createLeaderboardRequest({});
    await leaderboardController.getLeaderboardDataForOrganization(
      { userId: 1234, headers: { authorization: 'Bearer 1234' } },
      mockOrganizationId,
      baseLeaderboardRequest,
    );
    expect(mockGetLeaderboardDataForOrganization).toHaveBeenCalled();
    expect(
      reportSpendPermissionService.validateOrganizationAccessToSpendKpi,
    ).toBeCalledWith(
      baseLeaderboardRequest.kpiId,
      baseLeaderboardRequest.platform,
      mockOrganizationId,
    );
  });

  it('call getLeaderboard with ad accounts', async () => {
    const leaderboardRequest = createLeaderboardRequest({
      adAccountIds: ['123', '456'],
    });
    await leaderboardController.getLeaderboardDataForOrganization(
      { userId: 1234, headers: { authorization: 'Bearer 1234' } },
      mockOrganizationId,
      leaderboardRequest,
    );
    expect(mockGetLeaderboardDataForOrganization).toHaveBeenCalled();
    expect(
      reportSpendPermissionService.validateOrganizationAccessToSpendKpi,
    ).toBeCalledWith(
      leaderboardRequest.kpiId,
      leaderboardRequest.platform,
      mockOrganizationId,
    );
  });

  it('deprecated should call getLeaderboardAccountConnectionStatus', async () => {
    await leaderboardController.getLeaderboardAccountConnectionStatusForOrganization(
      { userId: 1234, headers: { authorization: 'Bearer 1234' } },
      mockOrganizationId,
      createLeaderboardRequest({}),
    );
    expect(
      mockGetLeaderboardAccountConnectionStatusForOrganization,
    ).toHaveBeenCalled();
  });
});
