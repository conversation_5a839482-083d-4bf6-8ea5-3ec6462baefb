import { ArgumentMetadata, BadRequestException } from '@nestjs/common';
import { GetLeaderboardRequestDto } from '../dto/get-leaderboard-request.dto';
import { DatePickerFilterV2ValidationPipe } from './date-picker-filter-v2-validation.pipe';

describe('DatePickerFilterV2ValidationPipe', () => {
  let pipe: DatePickerFilterV2ValidationPipe | null;

  beforeEach(() => {
    pipe = new DatePickerFilterV2ValidationPipe();
  });

  afterEach(() => {
    pipe = null;
  });

  describe('transform', () => {
    it('should throw error when end date is less than start date', async () => {
      expect(() =>
        pipe!.transform(
          {
            startDate: '01-01-2019',
            endDate: '01-01-2018',
          } as GetLeaderboardRequestDto,
          {} as ArgumentMetadata,
        ),
      ).toThrowError(BadRequestException);
    });

    it('should throw error when startDate is below the minimum allowed', async () => {
      expect(() =>
        pipe!.transform(
          {
            startDate: '01-31-17',
            endDate: '01-01-2018',
          } as GetLeaderboardRequestDto,
          {} as ArgumentMetadata,
        ),
      ).toThrowError(BadRequestException);
    });

    it('should not thrown an error if start date and end date meets filter v2 criteria', async () => {
      const body = {
        startDate: '01-01-2018',
        endDate: '01-01-2019',
      } as GetLeaderboardRequestDto;
      expect(pipe!.transform(body, {} as ArgumentMetadata)).toStrictEqual(body);
    });
  });
});
