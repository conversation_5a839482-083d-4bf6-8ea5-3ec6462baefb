import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  PipeTransform,
} from '@nestjs/common';
import { GetLeaderboardRequestDto } from '../dto/get-leaderboard-request.dto';
import { LEADERBOARD_DATE_PICKER_FILTER_V2_MINIMUM_DATE } from '../constants/constants';

@Injectable()
export class DatePickerFilterV2ValidationPipe implements PipeTransform {
  transform(body: GetLeaderboardRequestDto, _metadata: ArgumentMetadata): any {
    const startDate = new Date(body.startDate);
    const endDate = new Date(body.endDate);

    this.validateStartDateAndEndDate(startDate, endDate);
    this.validateMinimumDateAllowed(startDate);

    return body;
  }

  validateStartDateAndEndDate(startDate: Date, endDate: Date) {
    if (startDate > endDate)
      throw new BadRequestException(
        'Start date cannot be greater than end date',
      );
  }

  validateMinimumDateAllowed(startDate: Date) {
    const minimumDateAllowed = new Date(
      LEADERBOARD_DATE_PICKER_FILTER_V2_MINIMUM_DATE,
    );

    if (minimumDateAllowed > startDate) {
      throw new BadRequestException(
        `Start date cannot be set before ${LEADERBOARD_DATE_PICKER_FILTER_V2_MINIMUM_DATE}`,
      );
    }
  }
}
