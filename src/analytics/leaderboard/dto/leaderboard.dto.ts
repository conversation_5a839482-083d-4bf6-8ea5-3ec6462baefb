import { AutoMap } from '@automapper/classes';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  IsN<PERSON>ber,
  IsString,
  ValidateNested,
} from 'class-validator';

export class IdNameDto {
  /**
   * object's id
   * @example '23847238947238947'
   */
  @AutoMap()
  @IsString()
  id: string;

  /**
   * object's name
   * @example 'J&J'
   */
  @AutoMap()
  @IsString()
  name: string;
}

export class LeaderboardItemDto {
  /**
   * creative's rank by kpi value for given kpi and date range
   * @example 1
   */
  @AutoMap()
  @IsNumber()
  rank: number;

  /**
   * previous creative's rank by kpi value for given kpi
   * previous timeframe is determined by time difference of request's start and endDate
   * value is 0 if creative did not exist in previous timeframe's leaderboard (no data or not in top "count" of account's creatives)
   * @example 3
   */
  @AutoMap()
  @IsNumber()
  previousRank: number;

  /**
   * creative's platform ad account's media id
   * @example '38ei489348932904'
   */
  @AutoMap()
  @IsString()
  platformMediaId: string;

  /**
   * creative's KPI value for given KPI and date range
   * @example 0.*********
   */
  @AutoMap()
  @IsNumber()
  kpiValue: number;

  /**
   * creative's number of impressions for given date range
   * @example 1000.0
   */
  @AutoMap()
  @IsNumber()
  impressions: number;

  /**
   * creative's platform ad account
   */
  @AutoMap(() => IdNameDto)
  platformAdAccount: IdNameDto;

  /**
   * campaigns creative was featured in during given date range
   */
  @AutoMap(() => [IdNameDto])
  @IsArray()
  @ValidateNested({ each: true })
  campaigns: IdNameDto[];

  /**
   * Whether the creative is part of a multi-asset ad
   */
  @AutoMap()
  @IsBoolean()
  isCreativeInMultiAssetAd: boolean;
}
