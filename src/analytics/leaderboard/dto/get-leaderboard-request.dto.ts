import {
  IsArray,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AutoMap } from '@automapper/classes';
import { Platform } from '../../../constants/platform.constants';
import { Transform, Type } from 'class-transformer';
import {
  AdvancedFiltersRequestDto,
  ExchangeRateRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import CurrencyCodeEnum = ExchangeRateRequestDto.CurrencyCodeEnum;

export class GetLeaderboardRequestDto {
  /**
   * The start date via UTC timestamp; will always be Saturday midnight
   * @example 2022-08-01
   */
  @AutoMap()
  @IsNotEmpty()
  @IsDateString()
  startDate: string;

  /**
   * The end date via UTC timestamp; will always be Saturday midnight a week
   * after the start date
   * @example 2022-08-08
   */
  @AutoMap()
  @IsNotEmpty()
  @IsDateString()
  endDate: string;

  /**
   * Array of workspaces to filter by. Optional.
   * If not included, return all workspaces user has access to.
   * @example [8923, 553, 3232]
   */
  @AutoMap()
  @IsArray()
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  workspaces: number[];

  /**
   * Ad Account Ids to filter by. Optional.
   */
  @IsArray()
  @IsOptional()
  @IsString({ each: true })
  adAccountIds?: string[];

  /**
   * Kpi id to filter by.
   * @example "103"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  kpiId: string;

  /**
   * Platform name to filter by.
   * @example "FACEBOOK"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsEnum(Platform)
  @Transform(({ value }) => value.toLowerCase())
  platform: Platform;

  /**
   * limit for total number of creatives to return for leaderboard
   * @example 1000
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  limit: number;

  /**
   * limit for number of top performing creatives to return for each ad account
   * @example 20
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  limitPerAdAccount: number;

  /**
   * minimum number of days creative must be live to qualify for the leaderboard
   * @example 7
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  minimumDaysLive: number;

  /**
   * creative's total impressions must be greater than or equal to this value for the given start and end dates to qualify for the leaderboard
   * @example 1000
   */
  @AutoMap()
  @IsNotEmpty()
  @IsNumber()
  minimumImpressionsPerCreative: number;

  @AutoMap()
  @IsOptional()
  @ValidateNested()
  advancedFilters?: AdvancedFiltersRequestDto;

  /**
   * Currency code to convert costs to
   */
  @AutoMap()
  currency: CurrencyCodeEnum = CurrencyCodeEnum.Usd;
}
