import {
  Request,
  Body,
  Controller,
  Post,
  Param,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { ApiParam, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { LeaderboardService } from './leaderboard.service';
import { GetLeaderboardRequestDto } from './dto/get-leaderboard-request.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { LeaderboardItemDto } from './dto/leaderboard.dto';
import {
  VmApiOkPaginatedArrayResponse,
  VmApiOkResponse,
} from '@vidmob/vidmob-nestjs-common';
import { LeaderboardAccountInfoDto } from './dto/leaderboard-account-info.dto';
import { readOrganizationWorkspaceAdAccountReports } from '../analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { DatePickerFilterV2ValidationPipe } from './validator/date-picker-filter-v2-validation.pipe';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

@ApiTags('Leaderboard')
@ApiSecurity('Bearer Token')
@Controller('leaderboard')
export class LeaderboardController {
  constructor(
    private readonly leaderboardService: LeaderboardService,
    private readonly reportSpendPermissionService: ReportSpendPermissionService,
  ) {}

  /**
   * Get ranked performance creatives in workspaces compared to previous week leaderboard for specific KPI
   * will return a maximum of 1000 results, with 20 or less creatives from each ad account
   *
   * @param req
   * @param organizationId
   * @param leaderboardRequest
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization for workspaces in request to fetch creative performance for',
    required: true,
  })
  @VmApiOkPaginatedArrayResponse({ type: LeaderboardItemDto })
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @UsePipes(
    new ValidationPipe({
      transform: true,
    }),
  )
  @Post('organization/:organizationId')
  async getLeaderboardDataForOrganization(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body(new DatePickerFilterV2ValidationPipe())
    leaderboardRequest: GetLeaderboardRequestDto,
  ): Promise<PaginatedResultArray<LeaderboardItemDto>> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };
    await this.reportSpendPermissionService.validateOrganizationAccessToSpendKpi(
      leaderboardRequest.kpiId,
      leaderboardRequest.platform,
      organizationId,
    );
    return this.leaderboardService.getLeaderboardDataForOrganization(
      userDetails,
      leaderboardRequest,
    );
  }

  /**
   * Return information on the state of the leaderboard based on the user's workspace access to ad accounts
   *
   * @param req
   * @param organizationId
   * @param leaderboardRequest
   */
  @ApiParam({
    name: 'organizationId',
    description:
      'The organization for workspaces in request to fetch creative performance for',
    required: true,
  })
  @VmApiOkResponse({ type: LeaderboardAccountInfoDto })
  @Permissions(readOrganizationWorkspaceAdAccountReports)
  @Post('organization/:organizationId/status')
  getLeaderboardAccountConnectionStatusForOrganization(
    @Request() req: any,
    @Param('organizationId') organizationId: string,
    @Body() leaderboardRequest: GetLeaderboardRequestDto,
  ): Promise<LeaderboardAccountInfoDto> {
    const { userId, headers } = req;
    const userDetails = {
      userId,
      organizationId,
      authorization: headers.authorization,
    };
    return this.leaderboardService.getLeaderboardAccountConnectionStatusForOrganization(
      userDetails,
      leaderboardRequest,
    );
  }
}
