import { Test, TestingModule } from '@nestjs/testing';
import { LeaderboardService } from './leaderboard.service';
import { DimensionPerformanceService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PersonService } from '@vidmob/vidmob-authorization-service-sdk';
import { WorkspaceAdAccountService } from '@vidmob/vidmob-organization-service-sdk';
import {
  EXPECTED_LEADERBOARD_ITEM,
  MOCK_CREATIVE_PERFORMANCE_REQUEST,
  MOCK_FACEBOOK_ACCOUNTS,
  MOCK_PINTEREST_ACCOUNTS,
  MOCK_PREVIOUS_PERFORMANCE_DATA,
  MOCK_PERFORMANCE_DATA,
} from './mock-data/mock-test-data';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { mockUserDetails } from '../saved-report/mocks/analytics-report.mock';

describe('LeaderboardService', () => {
  let service: LeaderboardService;

  const mockGetTopPerformingCreativesAsPromise = jest.fn();
  const mockGetWorkspacesByOrganizationId = jest.fn();
  const mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise = jest
    .fn()
    .mockImplementation((request) => {
      if (request.platform.toLowerCase() === 'facebook') {
        return Promise.resolve({
          result: MOCK_FACEBOOK_ACCOUNTS,
          status: 'OK',
        });
      } else {
        return Promise.resolve({
          result: MOCK_PINTEREST_ACCOUNTS,
          status: 'OK',
        });
      }
    });
  const mockGetAllRolesForUser = jest.fn();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LeaderboardService,
        {
          provide: DimensionPerformanceService,
          useValue: {
            getTopPerformingCreativesAsPromise:
              mockGetTopPerformingCreativesAsPromise,
          },
        },
        {
          provide: WorkspaceAdAccountService,
          useValue: {
            getConnectedAdAccountsForWorkspacesAndPlatformAsPromise:
              mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise,
          },
        },
        {
          provide: WorkspaceService,
          useValue: {
            getWorkspacesByOrganizationId: mockGetWorkspacesByOrganizationId,
          },
        },
        {
          provide: PersonService,
          useValue: {
            getAllRolesForUser: mockGetAllRolesForUser,
          },
        },
      ],
    }).compile();

    service = module.get<LeaderboardService>(LeaderboardService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('getLeaderboardAccountConnectionStatusForOrganization should return correct value', async () => {
    mockGetWorkspacesByOrganizationId.mockResolvedValue({
      items: [],
      totalCount: 0,
    });
    await expect(
      service.getLeaderboardAccountConnectionStatusForOrganization(
        mockUserDetails,
        MOCK_CREATIVE_PERFORMANCE_REQUEST,
      ),
    ).rejects.toThrow(
      'User is missing access to some workspaces 123,456 in request for organization 2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c',
    );
  });

  it('getLeaderboardDataForOrganization should fails if organization user has no permission to workspaces', async () => {
    mockGetWorkspacesByOrganizationId.mockResolvedValue({
      items: [],
      totalCount: 0,
    });
    await expect(
      service.getLeaderboardDataForOrganization(
        mockUserDetails,
        MOCK_CREATIVE_PERFORMANCE_REQUEST,
      ),
    ).rejects.toThrow(
      'User is missing access to some workspaces 123,456 in request for organization 2948mkeoo-91f6-413d-9kjej-b2b8b3f0994c',
    );
  });

  it('getHasMoreData should return correct value for pagination', () => {
    const hasMoreDataOne = service.getHasMoreData({
      offset: 0,
      perPage: 50,
      nextOffset: 50,
      totalSize: 150,
      queryId: 'irjjf-owrpoj-jnlrrllnrrohr',
    });
    const hasMoreDataTwo = service.getHasMoreData({
      offset: 100,
      perPage: 50,
      nextOffset: 150,
      totalSize: 150,
      queryId: 'irjjf-owrpoj-jnlrrllnrrohr',
    });
    expect(hasMoreDataOne).toBe(true);
    expect(hasMoreDataTwo).toBe(false);
  });

  it('getAdAccountIdsForWorkspacesAndPlatform should return correct value', async () => {
    const adAccountIds = await service.getAdAccountIdsForWorkspacesAndPlatform(
      MOCK_CREATIVE_PERFORMANCE_REQUEST.workspaces,
      'FACEBOOK',
    );
    const expectedRequest = {
      workspaces: MOCK_CREATIVE_PERFORMANCE_REQUEST.workspaces,
      platform: 'FACEBOOK',
    };
    const expectedResponse = MOCK_FACEBOOK_ACCOUNTS.map(
      (acct) => acct.platformAccountId,
    );

    expect(
      mockGetConnectedAdAccountsForWorkspacesAndPlatformAsPromise,
    ).toBeCalledWith(expectedRequest, 0, 1000, undefined);
    expect(adAccountIds).toEqual(expectedResponse);
  });

  it('rankCreativePerformanceData returns correct leaderboard ranking', () => {
    const leaderboardItems = service.rankCreativePerformanceData(
      MOCK_PERFORMANCE_DATA,
      MOCK_PREVIOUS_PERFORMANCE_DATA,
      MOCK_CREATIVE_PERFORMANCE_REQUEST.limit,
    );
    expect(leaderboardItems).toEqual(EXPECTED_LEADERBOARD_ITEM);
  });

  it('getPreviousDateRange returns correct date range', () => {
    const weeklyPrevDateRange = service.getPreviousDateRange({
      startDate: '2021-01-08',
      endDate: '2021-01-14',
    });
    const monthlyPrevDateRange = service.getPreviousDateRange({
      startDate: '2021-02-01',
      endDate: '2021-02-31',
    });
    const quarterlyPrevDateRange = service.getPreviousDateRange({
      startDate: '2021-04-01',
      endDate: '2021-06-30',
    });

    expect(weeklyPrevDateRange).toEqual({
      startDate: '2021-01-01',
      endDate: '2021-01-07',
    });
    expect(monthlyPrevDateRange).toEqual({
      startDate: '2021-01-01',
      endDate: '2021-01-31',
    });
    expect(quarterlyPrevDateRange).toEqual({
      startDate: '2020-12-31',
      endDate: '2021-03-31',
    });
  });
});
