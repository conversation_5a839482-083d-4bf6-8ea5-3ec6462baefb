import { Modu<PERSON> } from '@nestjs/common';
import { LeaderboardService } from './leaderboard.service';
import { LeaderboardController } from './leaderboard.controller';
import { AuthService } from '../../auth/services/auth.service';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';
import { AccountManagementModule } from '../../account-management/account-management.module';

@Module({
  imports: [HttpModule, ConfigModule, AccountManagementModule],
  controllers: [LeaderboardController],
  providers: [LeaderboardService, AuthService, ReportSpendPermissionService],
})
export class LeaderboardModule {}
