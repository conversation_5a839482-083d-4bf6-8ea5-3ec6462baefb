import { <PERSON>du<PERSON> } from '@nestjs/common';
import { LeaderboardService } from './leaderboard.service';
import { LeaderboardController } from './leaderboard.controller';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AuthService } from '../../auth/services/auth.service';
import { ConfigModule } from '@nestjs/config';
import { HttpModule } from '@nestjs/axios';
import { ReportSpendPermissionService } from '../saved-report/services/report-spend-permission.service';

@Module({
  imports: [HttpModule, ConfigModule],
  controllers: [LeaderboardController],
  providers: [
    LeaderboardService,
    WorkspaceService,
    AuthService,
    ReportSpendPermissionService,
  ],
})
export class LeaderboardModule {}
