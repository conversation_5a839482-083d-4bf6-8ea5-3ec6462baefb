import { CreativeLeaderboardItem } from '@vidmob/vidmob-soa-analytics-service-sdk/dist/model/creativeLeaderboardItem';
import { LeaderboardItemDto } from '../dto/leaderboard.dto';
import { GetLeaderboardRequestDto } from '../dto/get-leaderboard-request.dto';
import { Platform } from '../../../constants/platform.constants';

export const MOCK_CREATIVE_PERFORMANCE_REQUEST: GetLeaderboardRequestDto = {
  startDate: '2023-06-21',
  endDate: '2023-06-28',
  workspaces: [123, 456],
  kpiId: '1',
  adAccountIds: ['123', '456'],
  platform: Platform.FACEBOOK,
  limit: 100,
  limitPerAdAccount: 10,
  minimumDaysLive: 20,
  minimumImpressionsPerCreative: 20,
  currency: 'USD',
};

export const MOCK_FACEBOOK_ACCOUNTS = [
  {
    id: 1,
    platformAccountName: 'FB-TEST-ONE',
    platformAccountId: 'FOIUP3EORIT',
    platform: 'facebook',
    permissions: [],
    dateCreated: '2021-08-25T18:00:00.000Z',
    processingCompleted: true,
    processingCompletedDate: '2021-08-25T18:00:00.000Z',
    lastSuccessfulProcessingDate: '2021-08-25T18:00:00.000Z',
    connected: true,
  },
  {
    id: 2,
    platformAccountName: 'FB-TEST-TWO',
    platformAccountId: 'PROTITIOPWEKJ',
    platform: 'facebook',
    permissions: [],
    dateCreated: '2021-08-25T18:00:00.000Z',
    processingCompleted: true,
    processingCompletedDate: '2021-08-25T18:00:00.000Z',
    lastSuccessfulProcessingDate: '2021-08-25T18:00:00.000Z',
    connected: true,
  },
  {
    id: 2,
    platformAccountName: 'FB-TEST-THREE',
    platformAccountId: 'OEIFUIWOPLDFKJ',
    platform: 'facebook',
    permissions: [],
    dateCreated: '2021-08-25T18:00:00.000Z',
    processingCompleted: true,
    processingCompletedDate: '2021-08-25T18:00:00.000Z',
    lastSuccessfulProcessingDate: '2021-08-25T18:00:00.000Z',
    connected: true,
  },
];

export const MOCK_PINTEREST_ACCOUNTS = [
  {
    id: 3,
    platformAccountName: 'PINTEREST-TEST-ONE',
    platformAccountId: 'EPROIGKLSIIJRFK',
    platform: 'pinterest',
    permissions: [],
    dateCreated: '2021-08-25T18:00:00.000Z',
    processingCompleted: true,
    processingCompletedDate: '2021-08-25T18:00:00.000Z',
    lastSuccessfulProcessingDate: '2021-08-25T18:00:00.000Z',
    connected: true,
  },
  {
    id: 3,
    platformAccountName: 'PINTEREST-TEST-TWO',
    platformAccountId: 'E0IFJGVUYEKJJFL',
    platform: 'pinterest',
    permissions: [],
    dateCreated: '2021-08-25T18:00:00.000Z',
    processingCompleted: true,
    processingCompletedDate: '2021-08-25T18:00:00.000Z',
    lastSuccessfulProcessingDate: '2021-08-25T18:00:00.000Z',
    connected: true,
  },
];

export const MOCK_PERFORMANCE_DATA: CreativeLeaderboardItem[] = [
  {
    platform: 'facebook',
    platformMediaId: 'mock-platform-media-id-one',
    platformAdAccount: {
      id: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountId,
      name: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountName,
    },
    campaigns: [{ id: 'e9u4r', name: '98uruhb4eri' }],
    kpiValue: 0.**********,
    impressions: 1000.0,
    isCreativeInMultiAssetAd: false,
  },
  {
    platform: 'facebook',
    platformMediaId: 'mock-platform-media-id-two',
    platformAdAccount: {
      id: MOCK_FACEBOOK_ACCOUNTS[1].platformAccountId,
      name: MOCK_FACEBOOK_ACCOUNTS[1].platformAccountName,
    },
    campaigns: [{ id: 'e9u4r', name: '98uruhb4eri' }],
    kpiValue: 10.**********,
    impressions: 1000.0,
    isCreativeInMultiAssetAd: false,
  },
  {
    platform: 'facebook',
    platformMediaId: 'mock-platform-media-id-three',
    platformAdAccount: {
      id: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountId,
      name: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountName,
    },
    campaigns: [{ id: 'e9u4r', name: '98uruhb4eri' }],
    kpiValue: 2.**********,
    impressions: 1000.0,
    isCreativeInMultiAssetAd: false,
  },
];

export const MOCK_PREVIOUS_PERFORMANCE_DATA: CreativeLeaderboardItem[] = [
  {
    platform: 'facebook',
    platformMediaId: 'mock-platform-media-id-four',
    platformAdAccount: {
      id: MOCK_FACEBOOK_ACCOUNTS[1].platformAccountId,
      name: MOCK_FACEBOOK_ACCOUNTS[1].platformAccountName,
    },
    campaigns: [{ id: 'e9u4r', name: '98uruhb4eri' }],
    kpiValue: 3.3333,
    impressions: 1000.0,
    isCreativeInMultiAssetAd: false,
  },
  {
    platform: 'facebook',
    platformMediaId: 'mock-platform-media-id-five',
    platformAdAccount: {
      id: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountId,
      name: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountName,
    },
    campaigns: [{ id: 'e9u4r', name: '98uruhb4eri' }],
    kpiValue: 7.66666,
    impressions: 1000.0,
    isCreativeInMultiAssetAd: false,
  },
  {
    platform: 'facebook',
    platformMediaId: 'mock-platform-media-id-three',
    platformAdAccount: {
      id: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountId,
      name: MOCK_FACEBOOK_ACCOUNTS[0].platformAccountName,
    },
    campaigns: [{ id: 'e9u4r', name: '98uruhb4eri' }],
    kpiValue: 1.2344545,
    impressions: 1000.0,
    isCreativeInMultiAssetAd: false,
  },
];

export const EXPECTED_LEADERBOARD_ITEM: LeaderboardItemDto[] = [
  {
    campaigns: [
      {
        id: 'e9u4r',
        name: '98uruhb4eri',
      },
    ],
    kpiValue: 0.**********,
    impressions: 1000.0,
    platformAdAccount: {
      id: 'FOIUP3EORIT',
      name: 'FB-TEST-ONE',
    },
    platformMediaId: 'mock-platform-media-id-one',
    previousRank: 0,
    rank: 1,
    isCreativeInMultiAssetAd: false,
  },
  {
    campaigns: [
      {
        id: 'e9u4r',
        name: '98uruhb4eri',
      },
    ],
    kpiValue: 10.**********,
    impressions: 1000.0,
    platformAdAccount: {
      id: 'PROTITIOPWEKJ',
      name: 'FB-TEST-TWO',
    },
    platformMediaId: 'mock-platform-media-id-two',
    previousRank: 0,
    rank: 2,
    isCreativeInMultiAssetAd: false,
  },
  {
    campaigns: [
      {
        id: 'e9u4r',
        name: '98uruhb4eri',
      },
    ],
    kpiValue: 2.**********,
    impressions: 1000.0,
    platformAdAccount: {
      id: 'FOIUP3EORIT',
      name: 'FB-TEST-ONE',
    },
    platformMediaId: 'mock-platform-media-id-three',
    previousRank: 3,
    rank: 3,
    isCreativeInMultiAssetAd: false,
  },
];
