import {
  BadRequestException,
  HttpException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { GetLeaderboardRequestDto } from './dto/get-leaderboard-request.dto';
import {
  CreativeLeaderboardItem,
  DimensionPerformanceService,
  CreativeLeaderboardRequestDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { GET_AD_ACCOUNTS_FOR_WORKSPACE_PAGINATION_OPTIONS } from './constants/constants';
import { PersonService } from '@vidmob/vidmob-authorization-service-sdk';
import { WorkspaceAdAccountService as WorkspaceAdAccountServiceSDK } from '@vidmob/vidmob-organization-service-sdk';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { LeaderboardItemDto } from './dto/leaderboard.dto';
import { AxiosError } from 'axios';
import { LeaderboardAccountInfoDto } from './dto/leaderboard-account-info.dto';
import { GetConnectedAdAccountsForWorkspacesAndPlatform200Response } from '@vidmob/vidmob-organization-service-sdk/dist/model/getConnectedAdAccountsForWorkspacesAndPlatform200Response';
import { DEFAULT_PAGINATION_OPTIONS_INTERNAL_REQUESTS } from '../constants/constants';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { UserDetailsDto } from '../dto/user-details.dto';

@Injectable()
export class LeaderboardService {
  private readonly logger = new Logger(LeaderboardService.name);

  constructor(
    private readonly dimensionPerformanceService: DimensionPerformanceService,
    private readonly workspaceAdAccountServiceSDK: WorkspaceAdAccountServiceSDK,
    private readonly workspaceService: WorkspaceService,
    private readonly personService: PersonService,
  ) {}

  async getLeaderboardAccountConnectionStatusForOrganization(
    userDetails: UserDetailsDto,
    getLeaderboardRequest: GetLeaderboardRequestDto,
  ): Promise<LeaderboardAccountInfoDto> {
    const { workspaces, platform } = getLeaderboardRequest;
    if (!workspaces.length) {
      return { hasConnectedAdAccountsForWorkspaces: false };
    }

    await this.validateOrganizationWorkspaces(userDetails, workspaces);

    const connectedAdAccountsForWorkspaces =
      await this.getAdAccountIdsForWorkspacesAndPlatform(workspaces, platform);

    return {
      hasConnectedAdAccountsForWorkspaces: Boolean(
        connectedAdAccountsForWorkspaces.length,
      ),
    };
  }

  async getLeaderboardDataForOrganization(
    userDetails: UserDetailsDto,
    getLeaderboardRequest: GetLeaderboardRequestDto,
  ): Promise<PaginatedResultArray<LeaderboardItemDto>> {
    const { workspaces, platform } = getLeaderboardRequest;
    if (!workspaces.length) {
      return new PaginatedResultArray<LeaderboardItemDto>([], 0);
    }

    await this.validateOrganizationWorkspaces(userDetails, workspaces);

    const platformAdAccountIds =
      await this.getAdAccountIdsForWorkspacesAndPlatform(workspaces, platform);

    if (!platformAdAccountIds.length) {
      return new PaginatedResultArray<LeaderboardItemDto>([], 0);
    }

    return this.fetchAndRankCreativePerformanceData(
      getLeaderboardRequest,
      platformAdAccountIds,
    );
  }

  async fetchAndRankCreativePerformanceData(
    getLeaderboardRequest: GetLeaderboardRequestDto,
    platformAdAccountIds: string[],
  ) {
    const { startDate, endDate, limit } = getLeaderboardRequest;
    const { startDate: previousRangeStartDate, endDate: previousRangeEndDate } =
      this.getPreviousDateRange({ startDate, endDate });
    const currentWeekPerformanceRequest = {
      ...getLeaderboardRequest,
      adAccountIds: platformAdAccountIds,
      kpiId: getLeaderboardRequest.kpiId,
      platform:
        getLeaderboardRequest.platform as CreativeLeaderboardRequestDto.PlatformEnum,
    };
    const previousWeekPerformanceRequest = {
      ...currentWeekPerformanceRequest,
      startDate: previousRangeStartDate,
      endDate: previousRangeEndDate,
    };

    const [performanceDataForRequestedWeek, performanceDataForPreviousWeek] =
      await Promise.all([
        this.dimensionPerformanceService.getTopPerformingCreativesAsPromise(
          currentWeekPerformanceRequest,
        ),
        this.dimensionPerformanceService.getTopPerformingCreativesAsPromise(
          previousWeekPerformanceRequest,
        ),
      ]);

    const leaderboardItems = this.rankCreativePerformanceData(
      performanceDataForRequestedWeek.result.filter(
        (performance: CreativeLeaderboardItem) => performance.kpiValue !== 0,
      ),
      performanceDataForPreviousWeek.result.filter(
        (performance) => performance.kpiValue !== 0,
      ),
      limit,
    );

    return new PaginatedResultArray(leaderboardItems, leaderboardItems.length);
  }

  getPreviousDateRange(dateRange: { startDate: string; endDate: string }): {
    startDate: string;
    endDate: string;
  } {
    const { startDate, endDate } = dateRange;
    const durationInMilliseconds =
      new Date(endDate).getTime() - new Date(startDate).getTime();

    const newEndDate = new Date(new Date(startDate).getTime() - 1)
      .toISOString()
      .slice(0, 10);
    const newStartDate = new Date(
      new Date(newEndDate).getTime() - durationInMilliseconds,
    )
      .toISOString()
      .slice(0, 10);

    return { startDate: newStartDate, endDate: newEndDate };
  }

  rankCreativePerformanceData(
    performanceData: CreativeLeaderboardItem[],
    previousWeekPerformanceData: CreativeLeaderboardItem[],
    limit: number,
  ): LeaderboardItemDto[] {
    const previousWeekCreativeRankById = previousWeekPerformanceData.reduce(
      (acc: Record<string, number>, creativePerformance, index) => {
        acc[
          `${creativePerformance.platformMediaId}-${creativePerformance.platformAdAccount.id}`
        ] = index + 1;
        return acc;
      },
      {},
    );

    return performanceData
      .slice(0, limit)
      .map((creativePerformance, index) => ({
        rank: index + 1,
        previousRank:
          previousWeekCreativeRankById[
            `${creativePerformance.platformMediaId}-${creativePerformance.platformAdAccount.id}`
          ] ?? 0,
        platformMediaId: creativePerformance.platformMediaId,
        kpiValue: creativePerformance.kpiValue,
        impressions: creativePerformance.impressions,
        platformAdAccount: creativePerformance.platformAdAccount,
        campaigns: creativePerformance.campaigns,
        isCreativeInMultiAssetAd: creativePerformance.isCreativeInMultiAssetAd,
        ...(creativePerformance.currency
          ? { currency: creativePerformance.currency }
          : {}),
      }));
  }

  async getAdAccountIdsForWorkspacesAndPlatform(
    workspaces: number[],
    platform: string,
  ): Promise<string[]> {
    try {
      const paginationOptions: PaginationOptions =
        GET_AD_ACCOUNTS_FOR_WORKSPACE_PAGINATION_OPTIONS;
      let hasMoreData = true;
      const platformAccounts: string[] = [];
      const validPlatform =
        platform.toUpperCase() === 'SNAP' ? 'SNAPCHAT' : platform.toUpperCase();

      while (hasMoreData) {
        const platformAccountsResponse: GetConnectedAdAccountsForWorkspacesAndPlatform200Response =
          await this.workspaceAdAccountServiceSDK.getConnectedAdAccountsForWorkspacesAndPlatformAsPromise(
            {
              workspaces,
              platform: validPlatform,
            },
            paginationOptions.offset,
            paginationOptions.perPage,
            paginationOptions.queryId,
          );

        platformAccounts.push(
          ...platformAccountsResponse.result.map(
            (adAccount) => adAccount.platformAccountId,
          ),
        );

        hasMoreData = this.getHasMoreData(platformAccountsResponse.pagination);
        paginationOptions.offset =
          platformAccountsResponse.pagination?.nextOffset;
        paginationOptions.queryId =
          platformAccountsResponse.pagination?.queryId;
      }

      return platformAccounts;
    } catch (error) {
      const errorMessage = `Something went wrong fetching ad accounts for workspaces ${workspaces} and platform ${platform}`;
      this.logger.error(`${errorMessage}. ${error}`);
      throw new BadRequestException(`${errorMessage}`);
    }
  }

  private async validateOrganizationWorkspaces(
    userDetails: UserDetailsDto,
    workspaceIds: number[],
  ): Promise<boolean> {
    const { userId, organizationId, authorization } = userDetails;
    const { perPage } = DEFAULT_PAGINATION_OPTIONS_INTERNAL_REQUESTS;
    let hasMore = true;
    let offset = 0;
    const organizationWorkspaces = [];

    while (hasMore) {
      const { items, totalCount } =
        await this.workspaceService.getWorkspacesByOrganizationId(
          organizationId,
          userId,
          authorization,
          {
            offset,
            perPage: perPage,
          },
          {},
        );
      organizationWorkspaces.push(...items);

      hasMore = Boolean(
        totalCount && organizationWorkspaces.length < totalCount,
      );
      offset = organizationWorkspaces.length;
    }

    const validWorkspaceIds = organizationWorkspaces.map(
      (workspace) => workspace.id,
    );
    const hasInvalidWorkspaces = workspaceIds.some(
      (workspaceId) => !validWorkspaceIds.includes(workspaceId),
    );

    if (hasInvalidWorkspaces) {
      const errorMessage = `User is missing access to some workspaces ${workspaceIds} in request for organization ${organizationId}`;
      this.logger.error(errorMessage);
      throw new BadRequestException(errorMessage);
    }

    return true;
  }

  getHasMoreData(
    pagination:
      | {
          offset: number;
          perPage: number;
          nextOffset: number;
          totalSize: number;
          queryId?: string;
        }
      | undefined
      | null,
  ): boolean {
    return Boolean(
      pagination &&
        pagination.nextOffset &&
        pagination.nextOffset < pagination.totalSize,
    );
  }

  rethrowAxiosError = (err: any): never => {
    if (err.isAxiosError && err.response) {
      const axiosError: AxiosError = err;
      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const { data, status } = axiosError.response!;
      if (data && typeof data === 'object' && 'status' in data) {
        throw new HttpException(data, status);
      }
    }
    throw err;
  };
}
