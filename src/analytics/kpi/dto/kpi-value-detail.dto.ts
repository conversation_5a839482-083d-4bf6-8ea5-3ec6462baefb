import { IsEnum, IsNumber } from 'class-validator';
import { AutoMap } from '@automapper/classes';

import { AnalyticsKpiFormat } from '../analytics-kpi.enum';

export class KpiValueDetailDto {
  /**
   * creative's KPI value for given KPI and date range
   * @example 0.598484993
   */
  @AutoMap()
  @IsNumber()
  value: number;

  /**
   * Format of the KPI's value
   * @example PERCENTAGE
   */
  @AutoMap()
  @IsEnum(AnalyticsKpiFormat)
  format: AnalyticsKpiFormat;
}
