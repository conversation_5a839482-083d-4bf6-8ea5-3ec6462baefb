import { IsDateString, <PERSON>NotEmpt<PERSON>, <PERSON><PERSON>eng<PERSON> } from 'class-validator';

export class DateRange {
  /**
   * The start date of the date range
   * @example '2021-01-01'
   */
  @IsNotEmpty()
  @IsDateString()
  @MaxLength(10)
  startDate: string;

  /**
   * The end date of the date range
   * @example '2021-12-31'
   */
  @IsNotEmpty()
  @IsDateString()
  @MaxLength(10)
  endDate: string;
}
