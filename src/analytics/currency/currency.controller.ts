import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { CurrencyService } from './currency.service';
import { Permissions } from '../../auth/decorators/permission.decorator';
import { readOrganizationAdAccountsDetails } from '../analytics.permissions';

@ApiTags('Currency')
@Controller('/organization/:organizationId/currency')
export class CurrencyController {
  constructor(private readonly currencyService: CurrencyService) {}

  @Get()
  @Permissions(readOrganizationAdAccountsDetails)
  getCurrencies() {
    return this.currencyService.getCurrencies();
  }
}
