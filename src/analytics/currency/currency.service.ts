import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { CurrencyDto } from './dto/currency.dto';
import { CurrencyExchangeRateService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PINNED_CURRENCY_CODES } from './currency.constants';

@Injectable()
export class CurrencyService {
  private readonly logger = new Logger(CurrencyService.name);

  constructor(
    private readonly currencyExchangeRateService: CurrencyExchangeRateService,
  ) {}

  async getCurrencies(): Promise<Array<CurrencyDto>> {
    const { result } =
      await this.currencyExchangeRateService.getCurrencyOptionsAsPromise();

    const pinnedCurrencies: CurrencyDto[] = [];
    const restOfCurrencies: CurrencyDto[] = [];

    result.forEach((currency: CurrencyDto) => {
      if (PINNED_CURRENCY_CODES.includes(currency.id)) {
        pinnedCurrencies.push(currency);
      } else {
        restOfCurrencies.push(currency);
      }
    });

    pinnedCurrencies.sort(
      (a, b) =>
        PINNED_CURRENCY_CODES.indexOf(a.id) -
        PINNED_CURRENCY_CODES.indexOf(b.id),
    );

    return [...pinnedCurrencies, ...restOfCurrencies];
  }
}
