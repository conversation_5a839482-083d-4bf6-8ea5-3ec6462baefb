import { AutoMap } from '@automapper/classes';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CurrencyDto {
  /**
   * Currency ISO code
   * @example USD
   */
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  id: string;

  /**
   * Currency origin and name
   * @example United States Dollar
   */
  @AutoMap()
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * Currency symbol (if available -- some currencies do not have a symbol)
   * @example $
   */
  @IsOptional()
  @AutoMap()
  @IsString()
  symbol?: string;
}
