import {
  <PERSON><PERSON>num,
  <PERSON>NotEmpty,
  IsN<PERSON>ber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { Platform } from '../constants/platform.constants';
import { DateRange } from '../analytics/dto/date-range.dto';

export class LegacyAnalyticsServiceStatsRequestDto {
  @IsString()
  @IsNotEmpty()
  tokenWithBearerPrefix: string;

  @IsString()
  @IsNotEmpty()
  organizationId: string;

  @IsNumber({}, { each: true })
  @IsNotEmpty()
  workspaceIds: number[];

  @IsString()
  @IsNotEmpty()
  dimension: string;

  @IsEnum(Platform)
  @IsNotEmpty()
  @Transform(({ value }) => value.toLowerCase())
  platform: Platform;

  @IsNotEmpty()
  @IsString({ each: true })
  adAccountIds: string[];

  @ValidateNested()
  @Type(() => DateRange)
  @IsNotEmpty()
  dateRange: DateRange;
}
