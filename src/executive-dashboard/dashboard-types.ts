import { Platform } from '../constants/scoring-analytics.constants';
import { DataResponseDto } from './dto/dashboard-data.dto';
import { z } from 'zod/v4';
import { ReportFilterOperator } from '../reports/model/report-filters.dto';
import { SortOrder } from '../reports/model/sort-order';
import { DashboardFilterDto } from './dto/create-dashboard.dto';

export enum WidgetType {
  ASSETS_SCORED = 'ASSETS_SCORED',
  IMPRESSION_ANALYZED = 'IMPRESSION_ANALYZED',
  CHANNEL_ADHERENCE_TRENDS = 'CHANNEL_ADHERENCE_TRENDS',
  ASSET_OVERVIEW = 'ASSET_OVERVIEW',
  KEY_FINDINGS = 'KEY_FINDINGS',
  ASSET_TRENDS = 'ASSET_TRENDS',
  CRITERIA_PERFORMANCE = 'CRITERIA_PERFORMANCE',
  ADHERENCE_SCORE = 'ADHERENCE_SCORE',
}

export enum ToggleControl {
  CompareToPreviousPeriod = 'isCompareToPreviousPeriodAvailable',
  ViewDataLabels = 'isViewDataLabelsAvailable',
  ViewByPeriod = 'isViewByPeriodAvailable',
  KpiLift = 'isKpiLiftAvailable',
  IncludeTotal = 'isIncludeTotalAvailable',
}

export interface VisualizationOptions {
  [ToggleControl.CompareToPreviousPeriod]?: boolean;
  [ToggleControl.ViewDataLabels]?: boolean;
  [ToggleControl.ViewByPeriod]?: boolean;
  [ToggleControl.KpiLift]?: boolean;
  [ToggleControl.IncludeTotal]?: boolean;
  viewByPeriodOptions?: { id: string; name: string }[];
  includeTotalLabel?: string;
  groupByDefault: { rows: string[]; columns: string[] };
}

export interface WidgetTransformCtx {
  [key: string]: unknown;
}

export enum FilterField {
  MEDIA_CREATE_DATE = 'mediaCreateDate',
  ASSET_SOURCE = 'assetSource',
  WORKSPACE = 'workspace',
  CHANNEL = 'channel',
  BRAND = 'brand',
  MARKET = 'market',
  DATE = 'date',
  KPI = 'kpi',
}

export type ToggleControlConfig = {
  key: ToggleControl;
  label: string;
  helpText?: string;
};

export type FilterDefinition = {
  key: FilterField;
  displayLabel: string;
  dependsOn?: FilterField[];
  defaultValue?: any;
  staticOptions?: string[];
  control?: ToggleControlConfig;
  type:
    | 'date-range'
    | 'multi-select'
    | 'single-select'
    | 'filtered-multi-select';
};

export enum VisualizationType {
  METRIC = 'METRIC',
  BAR = 'BAR',
  COLUMN = 'COLUMN',
  DONUT = 'DONUT',
  TABLE = 'TABLE',
  LINE = 'LINE',
  TEXT = 'TEXT',
}

export enum ChangeDirection {
  HIGHER_IS_BETTER = 'HIGHER_IS_BETTER',
  LOWER_IS_BETTER = 'LOWER_IS_BETTER',
}

export const DEFAULT_DATE_PERIOD = 'P6M'; // 6 months

export type WidgetTypeDefinition = {
  widgetType: WidgetType;
  name: string;
  description: string;
  iconUrl: string;
  defaultVisualizationType: VisualizationType;
  visualizationTypes: VisualizationType[];
  defaultFilter: DashboardFilterDto;
  options?: Partial<Record<VisualizationType, VisualizationOptions>>;
  requiredFilters: FilterDefinition[];
  optionalFilters: FilterDefinition[];
  workspaceSubscriptionsRequired: WorkspaceSubscription[];
};

export interface FilterValues {
  id: string;
  name: string;
  iconUrl?: string;
}

export interface PaginationObject {
  offset: number;
  perPage: number;
  nextOffset: number;
  totalSize: number;
}

export interface FilterOptions {
  values: FilterValues[];
  pagination?: PaginationObject;
}

export enum WorkspaceSubscription {
  SCORING = 'BRAND-GOVERNANCE',
  ANALYTICS = 'CREATIVE-INTELLIGENCE',
}

export enum ViewByPeriods {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  QUARTER = 'QUARTER',
  YEAR = 'YEAR',
}

export interface WidgetTypeDefinitionWithValues extends WidgetTypeDefinition {
  filterValues: Partial<Record<FilterField, FilterOptions>>;
}

// zod schema for dashboard filters

export const AssetSource = ['PRE_FLIGHT', 'IN_FLIGHT'] as const;
export const ISO_DATE = /^\d{4}-\d{2}-\d{2}$/;
export const ISO_DURATION = /^(?:P\d+(?:D|W|M|Y)|MTD|QTD|YTD)$/;

const sortSchema = z.object({
  sortBy: z.string(),
  sortOrder: z.nativeEnum(SortOrder),
});

const groupBySchema = z.object({
  columns: z.array(z.string()),
  rows: z.array(z.string()),
});

const filterSchema = z.discriminatedUnion('key', [
  z.object({
    key: z.literal('date'),
    operator: z.union([
      z.literal(ReportFilterOperator.Between),
      z.literal(ReportFilterOperator.After),
    ]),
    value: z.union([
      z.tuple([z.string().regex(ISO_DATE), z.string().regex(ISO_DATE)]),
      z.string().regex(ISO_DURATION),
    ]),
  }),
  z.object({
    key: z.literal('mediaCreateDate'),
    operator: z.union([
      z.literal(ReportFilterOperator.Between),
      z.literal(ReportFilterOperator.After),
    ]),
    value: z.union([
      z.tuple([z.string().regex(ISO_DATE), z.string().regex(ISO_DATE)]),
      z.string().regex(ISO_DURATION),
    ]),
  }),
  z.object({
    key: z.literal('channel'),
    operator: z.literal(ReportFilterOperator.In),
    value: z.array(z.object({ id: z.enum(Platform), name: z.string() })).min(1),
  }),
  z.object({
    key: z.literal('kpi'),
    operator: z.literal(ReportFilterOperator.In),
    value: z.array(z.object({ id: z.string(), name: z.string() })).min(1),
  }),
  z.object({
    key: z.literal('batchType'),
    operator: z.literal(ReportFilterOperator.Equals),
    value: z.array(z.enum(['PRE_FLIGHT', 'IN_FLIGHT'])).min(1),
  }),
  z.object({
    key: z.literal('creativeType'),
    operator: z.literal(ReportFilterOperator.In),
    value: z.array(z.enum(['VIDEO', 'IMAGE', 'ANIMATED_IMAGE', 'HTML'])).min(1),
  }),
  z.object({
    key: z.literal('adherenceRange'),
    operator: z.literal(ReportFilterOperator.Between),
    value: z.tuple([z.number(), z.number()]),
  }),
  z.object({
    key: z.literal('assetSource'),
    operator: z.literal(ReportFilterOperator.In),
    value: z
      .array(z.object({ id: z.enum(AssetSource), name: z.string() }))
      .min(1),
  }),
  z.object({
    key: z.literal('workspace'),
    operator: z.literal(ReportFilterOperator.In),
    value: z
      .array(
        z.object({ id: z.union([z.string(), z.number()]), name: z.string() }),
      )
      .min(1),
  }),
  z.object({
    key: z.literal('brand'),
    operator: z.literal(ReportFilterOperator.In),
    value: z.array(z.object({ id: z.string(), name: z.string() })).min(1),
  }),
  z.object({
    key: z.literal('market'),
    operator: z.literal(ReportFilterOperator.In),
    value: z.array(z.object({ id: z.string(), name: z.string() })).min(1),
  }),
]);

export const dashboardFilterSchema = z.object({
  filters: z
    .array(filterSchema)
    .refine(
      (f) => {
        const hasKpi = f.some((x) => x.key === 'kpi');
        const channelFilters = f.filter((x) => x.key === 'channel');
        if (!hasKpi) return true;
        if (channelFilters.length !== 1) return false;
        const values = channelFilters[0].value;
        return Array.isArray(values) && values.length === 1;
      },
      { message: '`kpi` requires exactly one `channel`' },
    )
    .refine(
      (f) => {
        const needsWs = f.some((x) => ['brand', 'market'].includes(x.key));
        const hasWs = f.some((x) => x.key === 'workspace');
        return !needsWs || hasWs;
      },
      { message: '`brand`/`market` require `workspace`' },
    ),
  sortBy: sortSchema.optional(),
  groupBy: groupBySchema.optional(),
});

export interface WidgetDataConfig {
  schema: z.ZodTypeAny;

  /**
   * One transform per visualization type.
   * Receives the *validated* payload and returns UI-ready data.
   */
  transforms: Partial<
    Record<
      VisualizationType,
      (validated: any, ctx: WidgetTransformCtx) => DataResponseDto
    >
  >;

  /**
   * Optional finalizer step for widgets that need to modify the filter before fetching data.
   */
  finalizeFilter?: (
    filter: DashboardFilterDto,
    ctx: any,
  ) => Promise<DashboardFilterDto>;

  /**
   * Optional pre-validator step for widgets that need extra data
   * (e.g., Criteria Performance → best-practice lookup).
   * Runs before `schema.parse`.
   */
  enrich?: (raw: any, ctx: any) => Promise<any>;
}
