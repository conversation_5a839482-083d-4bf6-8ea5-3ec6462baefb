import {
  IsOptional,
  ValidateNested,
  IsString,
  IsISO8601,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DashboardSortBy, SortOrder } from '../constants/constants';

export class DateRangeDto {
  @IsOptional()
  @IsISO8601()
  startDate?: string;

  @IsOptional()
  @IsISO8601()
  endDate?: string;
}
export class GetDashboardListQueryDto {
  offset?: number;
  perPage?: number;
  sortOrder: SortOrder[];
  sortBy: DashboardSortBy[];
}

export class ListDashboardsFilterDto {
  /** filters the result by name OR description (ILIKE '%searchTerm%') */
  @IsOptional()
  @IsString()
  searchTerm?: string;

  /** filter by creation date range */
  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeDto)
  dateCreated?: DateRangeDto;

  /** filter by last-updated date range */
  @IsOptional()
  @ValidateNested()
  @Type(() => DateRangeDto)
  lastUpdated?: DateRangeDto;
}

export class ListDashboardsDto {
  @IsOptional()
  @ValidateNested()
  @Type(() => ListDashboardsFilterDto)
  filter?: ListDashboardsFilterDto;
}

export type GetDashboardListFilterQueryDto = GetDashboardListQueryDto &
  ListDashboardsDto;
