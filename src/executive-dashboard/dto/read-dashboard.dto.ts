// src/executive-dashboard/dashboard/dto/read-dashboard.dto.ts

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UserDto } from './user.dto';
import { WidgetDto } from './widget.dto';
import { DashboardFilterDto } from './create-dashboard.dto';
import { DashboardSharingScope } from '../constants/constants';

export class ReadDashboardDto {
  @ApiProperty({
    example: '66666666-6666-6666-6666-666666666666',
    description: 'Unique identifier for the dashboard',
  })
  id: string;

  @ApiProperty({
    example: 'Enterprise Adherence Overview',
    description: 'Human-readable name of the dashboard',
  })
  name: string;

  @ApiPropertyOptional({
    example: 'Instance created from the Enterprise Adherence Overview preset',
    description: 'Optional long description of the dashboard',
    nullable: true,
  })
  description?: string | null;

  @ApiPropertyOptional({
    example: 'https://example.com/dashboard/12345',
    description: 'Optional URL for the dashboard',
    nullable: true,
  })
  photoUrl?: string;

  @ApiPropertyOptional({
    description: 'Global filter configuration applied to this dashboard',
    type: DashboardFilterDto,
    nullable: true,
  })
  dashboardFilter?: DashboardFilterDto;

  @ApiProperty({
    example: true,
    description:
      'Flag indicating whether the dashboard is marked as a favorite',
  })
  isFavorite: boolean;

  @ApiProperty({
    enum: DashboardSharingScope,
    example: DashboardSharingScope.PRIVATE,
    description:
      'Sharing scope of the dashboard (PRIVATE, ORGANIZATION, WORKSPACE)',
  })
  sharingScope: DashboardSharingScope;

  @ApiProperty({
    example: '2025-05-07T00:00:00Z',
    description: 'ISO timestamp when the dashboard was created',
  })
  dateCreated: string;

  @ApiProperty({
    example: '2025-05-07T00:00:00Z',
    description: 'ISO timestamp when the dashboard was last updated',
  })
  lastUpdated: string;

  @ApiProperty({
    type: () => UserDto,
    description: 'User who originally created the dashboard',
  })
  createdBy: UserDto;

  @ApiProperty({
    type: () => UserDto,
    description: 'User who last modified the dashboard',
  })
  lastModifiedBy: UserDto;

  @ApiPropertyOptional({
    type: () => [WidgetDto],
    description:
      'List of widgets belonging to this dashboard (empty array if none)',
  })
  widgets?: WidgetDto[];
}
