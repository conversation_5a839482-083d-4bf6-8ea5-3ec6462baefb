import { ApiProperty } from '@nestjs/swagger';
import { UserDto } from './user.dto';
import { DashboardFilterDto } from './create-dashboard.dto';
import { AutoMap } from '@automapper/classes';

export class WidgetDto {
  @ApiProperty({ example: '77777777-7777-7777-7777-777777777777' })
  id?: string;

  @ApiProperty({ example: 'AVERAGE_ADHERENCE_SCORE' })
  widgetType: string;

  @ApiProperty({ example: 'Average Adherence Score' })
  name: string;

  @ApiProperty({ example: 'Displays the overall average adherence score' })
  description: string;

  @ApiProperty({
    description: 'Widget type-specific parameters (JSON)',
    required: false,
    type: Object,
  })
  parameters?: Record<string, unknown>;

  @ApiProperty({ example: 0 })
  gridX: number;

  @ApiProperty({ example: 0 })
  gridY: number;

  @ApiProperty({ example: 4 })
  gridWidth: number;

  @ApiProperty({ example: 2 })
  gridHeight: number;

  @ApiProperty({ example: 'METRIC' })
  visualizationType: string;

  @ApiProperty({ description: 'Widget specific filter configuration' })
  filter?: DashboardFilterDto;

  /*
   * Widget data options
   * Must match type and naming with entity members for Automapper to automatically map these fields.
   */

  @AutoMap() isCompareToPreviousPeriodEnabled!: boolean;
  @AutoMap() isViewDataLabelsEnabled!: boolean;
  @AutoMap() isIncludeTotalEnabled!: boolean;
  @AutoMap() isKpiLiftEnabled!: boolean;

  @ApiProperty({ example: '2025-05-07T00:00:00Z' })
  dateCreated?: string;

  @ApiProperty({ example: '2025-05-07T00:00:00Z' })
  lastUpdated?: string;

  @ApiProperty({ type: () => UserDto })
  createdBy?: UserDto;

  @ApiProperty({ type: () => UserDto })
  lastModifiedBy?: UserDto;
}
