import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import {
  ChangeDirection,
  ViewByPeriods,
  VisualizationType,
  WidgetType,
} from '../dashboard-types';
import { DashboardFilterDto } from './create-dashboard.dto';

export class WidgetDataRequestDto {
  @ApiPropertyOptional({ enum: WidgetType })
  @IsOptional()
  widgetType?: WidgetType;

  @ApiPropertyOptional({ enum: VisualizationType })
  @IsOptional()
  visualizationType?: VisualizationType;

  @ApiPropertyOptional({ type: Object })
  @IsOptional()
  @IsObject()
  parameters?: Record<string, unknown>;

  @ApiPropertyOptional({ type: () => DashboardFilterDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => DashboardFilterDto)
  filter?: DashboardFilterDto;

  @ApiPropertyOptional({ type: () => DashboardFilterDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => DashboardFilterDto)
  dashboardFilter?: DashboardFilterDto;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isCompareToPreviousPeriodEnabled?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isIncludeTotalEnabled?: boolean;

  @ApiPropertyOptional({ enum: ViewByPeriods })
  @IsOptional()
  @IsEnum(ViewByPeriods)
  viewByPeriod?: ViewByPeriods;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isViewDataLabelsEnabled?: boolean;

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  isKpiLiftEnabled?: boolean;
}

export class WidgetDataResponseDto {
  @ApiProperty()
  widgetId!: string;

  @ApiProperty({ enum: WidgetType })
  widgetType!: WidgetType;

  @ApiProperty({ enum: VisualizationType })
  visualizationType!: VisualizationType;

  @ApiProperty({ type: Object })
  @ValidateNested()
  @Type(() => Object)
  data!: DataResponseDto;

  @ApiPropertyOptional({ type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  reportPeriodLabels?: string[];

  @ApiPropertyOptional({ type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  previousPeriodLabels?: string[];

  @ApiProperty({ type: String, format: 'date-time' })
  lastRefreshed!: string;
}

/* ----------------------- Metric DTOs ----------------------- */
export class MetricValueDto {
  @ApiProperty()
  @IsNumber()
  value: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  previousValue?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  changePercentage?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  label?: string;
}

export class MetricDataResponseDto {
  @ApiProperty({ type: [MetricValueDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MetricValueDto)
  values: MetricValueDto[];

  @ApiProperty()
  @IsString()
  unitLabel: string;
}

/* ------------------- Bar / Column / Line DTOs ------------------- */
export class SeriesDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ type: [Number] })
  @IsArray()
  @IsNumber({}, { each: true })
  data: number[];

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  previousPeriodData?: number[];

  @ApiPropertyOptional({ type: [Number] })
  @IsOptional()
  @IsArray()
  @IsNumber({}, { each: true })
  changePercentageData?: number[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  color?: string;
}

export class TableColumnDto {
  @ApiProperty() @IsString() key: string;
  @ApiProperty() @IsString() label: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  iconUrl?: string;

  @ApiProperty() @IsString() type: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  units?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  previousPeriodLabel?: string;

  @ApiProperty() @IsBoolean() sortable: boolean;
  @ApiProperty() @IsBoolean() sortActive: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sortDirection?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  changeDirection?: ChangeDirection;
}

export class TableDataResponseDto {
  @ApiProperty({ type: [TableColumnDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TableColumnDto)
  columns: TableColumnDto[];

  @ApiProperty({ type: 'array', items: { type: 'object' } })
  @IsArray()
  rows: Record<string, any>[];
}

export class BarDataResponseDto {
  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  categories: string[];

  @ApiProperty({ type: [SeriesDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SeriesDto)
  series: SeriesDto[];

  @ApiProperty()
  @IsString()
  xAxisLabel: string;

  @ApiProperty()
  @IsString()
  yAxisLabel: string;

  @ApiProperty()
  @IsString()
  unitLabelValues: string;
}

export type DataResponseDto =
  | MetricDataResponseDto
  | BarDataResponseDto
  | TableDataResponseDto;
