import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsString } from 'class-validator';
import { CreateWidgetDto } from './create-widget.dto';
import { Person } from '../../entities/person.entity';
import { ViewByPeriods } from '../dashboard-types';

export class ReadWidgetDto extends CreateWidgetDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  dateCreated: string;

  @ApiProperty()
  lastUpdated: string;

  @ApiProperty()
  createdBy: Person;

  @ApiProperty()
  lastModifiedBy: Person;

  @ApiPropertyOptional({ enum: ViewByPeriods })
  @IsEnum(ViewByPeriods)
  viewByPeriod?: ViewByPeriods;

  @ApiProperty()
  @IsBoolean()
  isIncludeTotalEnabled: boolean;

  @ApiProperty()
  @IsBoolean()
  isKpiLiftEnabled?: boolean;
}
