import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  ValidateNested,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { DashboardFilterDto } from './create-dashboard.dto';
import {
  ViewByPeriods,
  VisualizationType,
  WidgetType,
} from '../dashboard-types';

export class CreateWidgetDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ required: false })
  @IsEnum(WidgetType)
  @IsOptional()
  widgetType: WidgetType;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  visualizationType?: VisualizationType;

  @ApiProperty({ required: false, type: Object })
  @IsOptional()
  parameters?: Record<string, unknown>;

  @ApiProperty({ required: false, type: DashboardFilterDto })
  @ValidateNested()
  @Type(() => DashboardFilterDto)
  @IsOptional()
  filter?: DashboardFilterDto;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isCompareToPreviousPeriodEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isKpiLiftEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsBoolean()
  @IsOptional()
  isViewDataLabelsEnabled?: boolean;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  gridX?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  gridY?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  gridWidth?: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  gridHeight?: number;

  @ApiPropertyOptional({ enum: ViewByPeriods })
  @IsEnum(ViewByPeriods)
  @IsOptional()
  viewByPeriod?: ViewByPeriods;

  @ApiPropertyOptional()
  @IsBoolean()
  @IsOptional()
  isIncludeTotalEnabled?: boolean;
}
