import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsOptional,
  IsString,
  ValidateNested,
  IsArray,
  ValidateIf,
  IsUUID,
} from 'class-validator';
import { Type } from 'class-transformer';
import { WidgetDto } from './widget.dto';
import { DashboardSharingScope } from '../constants/constants';

import { ReportFilterDtoV2 } from '../../reports/model/report-filters.dto';
import { CreateReportGroupByDto } from '../../reports/model/create-report-group-by.dto';
import { Sort } from '../../reports/model/sort';

export class DashboardFilterDto {
  @ApiProperty({
    description: 'The filter definitions to apply to the dashboard',
    required: true,
    type: [ReportFilterDtoV2],
  })
  @ValidateNested({ each: true })
  @Type(() => ReportFilterDtoV2)
  @IsArray()
  filters: ReportFilterDtoV2[];

  @ApiProperty({
    description: 'The sort configuration for the dashboard',
    required: false,
    type: ReportFilterDtoV2,
  })
  @ValidateNested()
  @Type(() => Sort)
  @IsOptional()
  sortBy?: Sort;

  @ApiProperty({
    description: 'The group by configuration for the dashboard',
    required: false,
    type: CreateReportGroupByDto,
  })
  @ValidateNested()
  @Type(() => CreateReportGroupByDto)
  @IsOptional()
  groupBy?: CreateReportGroupByDto;
}

export class CreateDashboardDto {
  @ApiPropertyOptional({
    description:
      'If cloning an existing dashboard, the ID of the source to copy from',
    format: 'uuid',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  sourceDashboardId?: string;

  @ApiPropertyOptional({
    description: 'Name of the new dashboard',
    required: false,
  })
  @ValidateIf((o) => o.sourceDashboardId == null)
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Description for the dashboard',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'The global filter definition to apply to the dashboard',
    required: false,
    type: DashboardFilterDto,
  })
  @ValidateIf((o) => o.dashboardFilter != null)
  @ValidateNested()
  @Type(() => DashboardFilterDto)
  @IsOptional()
  dashboardFilter?: DashboardFilterDto;

  @ApiPropertyOptional({
    description: 'If the widget is favorited by the user',
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isFavorite?: boolean;

  @ApiPropertyOptional({
    description: 'The scope within which the dashboard is shared',
    enum: DashboardSharingScope,
    default: DashboardSharingScope.PRIVATE,
    required: false,
  })
  @IsString()
  @IsOptional()
  sharingScope?: DashboardSharingScope = DashboardSharingScope.PRIVATE;

  @ApiPropertyOptional({
    description: 'The widgets to add to the dashboard',
    required: false,
    type: [WidgetDto],
  })
  @ValidateNested({ each: true })
  @Type(() => WidgetDto)
  @IsOptional()
  widgets?: WidgetDto[];
}
