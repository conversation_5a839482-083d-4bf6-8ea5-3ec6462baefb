import {
  getAvailableViewByPeriods,
  getDurationInDays,
} from './dashboard-constants';
import { ViewByPeriods } from '../dashboard-types';

describe('getDurationInDays', () => {
  beforeAll(() => {
    jest.useFakeTimers().setSystemTime(new Date('2025-07-10T00:00:00-04:00'));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  it('handles absolute range', () => {
    expect(getDurationInDays(['2024-01-01', '2024-06-30'])).toBe(181);
  });

  it('handles single absolute date', () => {
    expect(getDurationInDays('2025-07-01')).toBe(9);
  });

  it('handles MTD/QTD/YTD shorthands', () => {
    expect(getDurationInDays('MTD')).toBe(9);
    expect(getDurationInDays('QTD')).toBe(9);
    expect(getDurationInDays('YTD')).toBe(190);
  });

  it('handles ISO-8601 duration', () => {
    expect(getDurationInDays('P6M')).toBe(184); // Jul 10 → Jan 10
  });

  it('returns null for invalid input', () => {
    expect(getDurationInDays(['bad', 'date'] as any)).toBeNull();
  });
});

describe('getAvailableViewByPeriods', () => {
  it('returns DAY for ≤10 days', () => {
    expect(getAvailableViewByPeriods(5)).toEqual([ViewByPeriods.DAY]);
  });

  it('returns WEEK & MONTH for 45 days', () => {
    expect(getAvailableViewByPeriods(45)).toEqual([
      ViewByPeriods.WEEK,
      ViewByPeriods.MONTH,
    ]);
  });

  it('returns MONTH & QUARTER for 100 days', () => {
    expect(getAvailableViewByPeriods(100)).toEqual([
      ViewByPeriods.MONTH,
      ViewByPeriods.QUARTER,
    ]);
  });

  it('returns YEAR for >730 days', () => {
    expect(getAvailableViewByPeriods(800)).toEqual([ViewByPeriods.YEAR]);
  });
});
