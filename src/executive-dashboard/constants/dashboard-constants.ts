import {
  Fi<PERSON><PERSON>ield,
  ViewByPeriods,
  WidgetDataConfig,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../dashboard-types';
import { parse as parseIsoDuration } from 'iso8601-duration';
import {
  parseISO,
  differenceInCalendarDays,
  add,
  startOfMonth,
  startOfQuarter,
  startOfYear,
} from 'date-fns';
import {
  ASSETS_SCORED_WIDGET,
  AssetsScoredDataSchema,
  assetsScoredTransforms,
} from '../schemas/widget-types/assets-scored';
import { ASSET_OVERVIEW_WIDGET } from '../schemas/widget-types/asset-overview';
import { KEY_FINDINGS_WIDGET } from '../schemas/widget-types/key-findings';
import {
  CRITERIA_PERFORMANCE_WIDGET,
  CriteriaPerformanceDataSchema,
  criteriaPerformanceTransforms,
  enrichCriteriaPerformance,
  finalizeCriteriaPerformanceFilter,
} from '../schemas/widget-types/criteria-performance';
import {
  ADHERENCE_SCORE_WIDGET,
  AdherenceScoreDataSchema,
  adherenceScoreTransforms,
} from '../schemas/widget-types/adherence-score';
import { BadRequestException } from '@nestjs/common';
import { ReportFilterOperator } from '../../reports/model/report-filters.dto';
import { DashboardFilterDto } from '../dto/create-dashboard.dto';
import {
  IMPRESSIONS_ANALYZED_WIDGET,
  ImpressionsAnalyzedDataSchema,
  impressionsAnalyzedTransforms,
} from '../schemas/widget-types/impressions-analyzed';

export const MAX_WIDGETS_PER_DASHBOARD = 16;

export const CURRENT_DASHBOARD_FILTER_VERSION = 2;

export const WIDGET_TYPE_DEFINITIONS: WidgetTypeDefinition[] = [
  ASSETS_SCORED_WIDGET,
  ASSET_OVERVIEW_WIDGET,
  KEY_FINDINGS_WIDGET,
  CRITERIA_PERFORMANCE_WIDGET,
  ADHERENCE_SCORE_WIDGET,
  IMPRESSIONS_ANALYZED_WIDGET,
];

export function handleDynamicWidgetProperties(
  def: WidgetTypeDefinition,
  filters?: DashboardFilterDto,
): WidgetTypeDefinition {
  const out: WidgetTypeDefinition = { ...def };

  /* mediaCreateDate ➜ viewByPeriodOptions */
  const viewBy = getViewByPeriodOptionsFromFilters(filters);
  if (viewBy.length) out.viewByPeriodOptions = viewBy;

  // future dynamic props can be handled here …

  return out;
}

export const getWidgetTypeDefinition = (
  widgetType: WidgetType,
  filters?: DashboardFilterDto,
): WidgetTypeDefinition => {
  const def = WIDGET_TYPE_DEFINITIONS.find((w) => w.widgetType === widgetType);
  if (!def)
    throw new BadRequestException(`Widget type ${widgetType} not supported`);

  return handleDynamicWidgetProperties(def, filters);
};

export const dashboardSubscriptionTypes = [
  WorkspaceSubscription.ANALYTICS,
  WorkspaceSubscription.SCORING,
];

export const widgetDataRegistry: Partial<Record<WidgetType, WidgetDataConfig>> =
  {
    [WidgetType.ASSETS_SCORED]: {
      schema: AssetsScoredDataSchema,
      transforms: assetsScoredTransforms,
    },
    [WidgetType.CRITERIA_PERFORMANCE]: {
      schema: CriteriaPerformanceDataSchema,
      transforms: criteriaPerformanceTransforms,
      finalizeFilter: finalizeCriteriaPerformanceFilter,
      enrich: enrichCriteriaPerformance,
    },
    [WidgetType.ADHERENCE_SCORE]: {
      schema: AdherenceScoreDataSchema,
      transforms: adherenceScoreTransforms,
    },
    [WidgetType.IMPRESSIONS_ANALYZED]: {
      schema: ImpressionsAnalyzedDataSchema,
      transforms: impressionsAnalyzedTransforms,
    },
  } as const;

export function getDurationInDays(
  value: string | [string, string],
): number | null {
  // absolute range ["2024-01-01", "2024-06-30"]
  if (Array.isArray(value) && value.length === 2) {
    const [start, end] = value.map((d) => parseISO(d)) as [Date, Date];
    return isNaN(start.getTime()) || isNaN(end.getTime())
      ? null
      : Math.abs(differenceInCalendarDays(end, start));
  }

  // single absolute date "2024-01-01"
  if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(value)) {
    return Math.abs(differenceInCalendarDays(new Date(), parseISO(value)));
  }

  // MTD / QTD / YTD
  if (value === 'MTD' || value === 'QTD' || value === 'YTD') {
    const start =
      value === 'MTD'
        ? startOfMonth(new Date())
        : value === 'QTD'
        ? startOfQuarter(new Date())
        : startOfYear(new Date());
    return differenceInCalendarDays(new Date(), start);
  }

  // ISO-8601 duration "P6M", "P30D", "P1Y" …
  if (typeof value === 'string' && value.startsWith('P')) {
    try {
      const dur = parseIsoDuration(value); // { years, months, weeks, days, … }
      const end = add(new Date(), {
        years: dur.years,
        months: dur.months,
        weeks: dur.weeks,
        days: dur.days,
      });
      return Math.abs(differenceInCalendarDays(end, new Date()));
    } catch {
      return null;
    }
  }

  return null;
}

export function getAvailableViewByPeriods(
  durationInDays: number,
): ViewByPeriods[] {
  const out: ViewByPeriods[] = [];

  if (durationInDays <= 10) out.push(ViewByPeriods.DAY);
  if (durationInDays > 10 && durationInDays <= 60) out.push(ViewByPeriods.WEEK);
  if (durationInDays >= 31 && durationInDays <= 365)
    out.push(ViewByPeriods.MONTH);
  if (durationInDays > 60 && durationInDays <= 730)
    out.push(ViewByPeriods.QUARTER);
  if (durationInDays > 730) out.push(ViewByPeriods.YEAR);

  return out;
}

export function getDurationFromDateFilter(filter: {
  key: FilterField;
  operator: ReportFilterOperator;
  value: unknown;
}): number | null {
  if (filter.key !== FilterField.MEDIA_CREATE_DATE) return null;
  return getDurationInDays(filter.value as string | [string, string]);
}

export function getViewByPeriodOptionsFromFilters(
  filters?: DashboardFilterDto,
): ViewByPeriods[] {
  if (!filters) return [];

  const dateFilter = filters.filters.find(
    (f) => f.key === FilterField.MEDIA_CREATE_DATE,
  ) as
    | {
        key: FilterField.MEDIA_CREATE_DATE;
        operator: any;
        value: string | [string, string];
      }
    | undefined;

  if (!dateFilter) return [];

  const days = getDurationFromDateFilter(dateFilter);
  if (days === null) return [];

  return getAvailableViewByPeriods(days);
}
