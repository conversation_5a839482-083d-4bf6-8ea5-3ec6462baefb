export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum DashboardSortBy {
  NAME = 'name',
  DATE_CREATED = 'dateCreated',
  LAST_UPDATED = 'lastUpdated',
  SHARING_SCOPE = 'sharingScope',
  IS_FAVORITE = 'isFavorite',
  CREATED_BY = 'createdBy',
  VERSION = 'version',
}

export const DEFAULT_PAGINATION_OPTIONS = {
  offset: 0,
  perPage: 20,
};

export const DEFAULT_PAGINATION_OPTIONS_INTERNAL_REQUESTS = {
  offset: 0,
  perPage: 1000,
};

export enum DashboardSharingScope {
  PRIVATE = 'PRIVATE',
  ORGANIZATION = 'ORGANIZATION',
  WORKSPACE = 'WORKSPACE',
}
