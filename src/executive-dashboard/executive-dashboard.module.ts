import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AccountManagementModule } from '../account-management/account-management.module';

import { DashboardService } from './dashboard/dashboard.service';
import { WidgetService } from './widget/widget.service';
import { DashboardController } from './dashboard/dashboard.controller';
import { WidgetController } from './widget/widget.controller';

import { DashboardWidget } from '../entities/dashboard-widget.entity';
import { Dashboard } from '../entities/dashboard.entity';
import { Person } from '../entities/person.entity';
import { ReportFilter } from '../entities/report-filter.entity';
import { DashboardUserConfig } from '../entities/dashboard-user-config.entity';
import { AuthService } from '../auth/services/auth.service';
import { DashboardProfile } from './dashboard/mapper/dashboard.profile';
import { ScoringModule } from '../scoring/scoring.module';
import { AnalyticsReportsModule } from '../analytics/saved-report/analytics-reports.module';

@Module({
  imports: [
    AccountManagementModule,
    ScoringModule,
    AnalyticsReportsModule,
    TypeOrmModule.forFeature([
      Dashboard,
      DashboardWidget,
      DashboardUserConfig,
      ReportFilter,
      Person,
      HttpModule,
    ]),
  ],
  controllers: [DashboardController, WidgetController],
  providers: [DashboardService, WidgetService, AuthService, DashboardProfile],
})
export class ExecutiveDashboardModule {}
