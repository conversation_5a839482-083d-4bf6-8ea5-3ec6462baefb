import { DashboardFilterDto } from '../dto/create-dashboard.dto';
import { ReportFilterDtoV2 } from '../../reports/model/report-filters.dto';

/**
 * Merges multiple DashboardFilterDto objects with priority handling.
 * Filters are merged from lowest-priority to highest-priority.
 *
 * @param filters - Array of DashboardFilterDto objects, ordered from lowest to highest priority
 * @returns Merged DashboardFilterDto
 */
export function mergeDashboardFilters(
  filters: DashboardFilterDto[],
): DashboardFilterDto {
  if (!filters || filters.length === 0) {
    return { filters: [] };
  }

  // Start with the lowest priority filter
  const result: DashboardFilterDto = {
    filters: [...(filters[0]?.filters || [])],
    sortBy: filters[0]?.sortBy,
    groupBy: filters[0]?.groupBy,
  };

  // Merge each subsequent filter (higher priority)
  for (let i = 1; i < filters.length; i++) {
    const currentFilter = filters[i];
    if (!currentFilter) continue;

    // Merge filters by key - higher priority overwrites lower priority
    if (currentFilter.filters) {
      const existingFilterMap = new Map<string, ReportFilterDtoV2>();

      // Add existing filters to map
      result.filters.forEach((filter) => {
        existingFilterMap.set(filter.key, filter);
      });

      // Overwrite with higher priority filters
      currentFilter.filters.forEach((filter) => {
        existingFilterMap.set(filter.key, filter);
      });

      // Convert back to array
      result.filters = Array.from(existingFilterMap.values());
    }

    // Completely replace sortBy and groupBy if present in higher priority
    if (currentFilter.sortBy !== undefined) {
      result.sortBy = currentFilter.sortBy;
    }

    if (currentFilter.groupBy !== undefined) {
      result.groupBy = currentFilter.groupBy;
    }
  }

  return result;
}

/**
 * Type-safe version that ensures all filters are defined
 */
export function mergeDashboardFiltersStrict(
  filters: (DashboardFilterDto | undefined | null)[],
): DashboardFilterDto {
  const validFilters = filters.filter(
    (filter): filter is DashboardFilterDto =>
      filter !== undefined && filter !== null,
  );

  return mergeDashboardFilters(validFilters);
}
