import {
  mergeDashboardFilters,
  mergeDashboardFiltersStrict,
} from './filter-merger';
import { DashboardFilterDto } from '../dto/create-dashboard.dto';
import { ReportFilterOperator } from '../../reports/model/report-filters.dto';

describe('FilterMerger', () => {
  describe('mergeDashboardFilters', () => {
    it('should return empty filters when no filters provided', () => {
      const result = mergeDashboardFilters([]);
      expect(result).toEqual({ filters: [] });
    });

    it('should return the single filter when only one provided', () => {
      const filter: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 1, name: 'Workspace 1' }],
          },
        ],
      };

      const result = mergeDashboardFilters([filter]);
      expect(result).toEqual(filter);
    });

    it('should merge filters by key with higher priority overwriting', () => {
      const lowPriority: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 1, name: 'Workspace 1' }],
          },
          {
            key: 'channel',
            operator: ReportFilterOperator.In,
            value: [{ id: 'facebook', name: 'Facebook' }],
          },
        ],
      };

      const highPriority: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 2, name: 'Workspace 2' }],
          },
          {
            key: 'brand',
            operator: ReportFilterOperator.In,
            value: [{ id: 'brand1', name: 'Brand 1' }],
          },
        ],
      };

      const result = mergeDashboardFilters([lowPriority, highPriority]);

      expect(result.filters).toHaveLength(3);
      expect(result.filters.find((f) => f.key === 'workspace')).toEqual(
        highPriority.filters[0],
      );
      expect(result.filters.find((f) => f.key === 'channel')).toEqual(
        lowPriority.filters[1],
      );
      expect(result.filters.find((f) => f.key === 'brand')).toEqual(
        highPriority.filters[1],
      );
    });

    it('should completely replace sortBy and groupBy from higher priority', () => {
      const lowPriority: DashboardFilterDto = {
        filters: [],
        sortBy: { sortBy: 'date', sortOrder: 'ASC' as any },
        groupBy: { columns: ['workspace'], rows: [] },
      };

      const highPriority: DashboardFilterDto = {
        filters: [],
        sortBy: { sortBy: 'name', sortOrder: 'DESC' as any },
        groupBy: { columns: [], rows: ['brand'] },
      };

      const result = mergeDashboardFilters([lowPriority, highPriority]);

      expect(result.sortBy).toEqual(highPriority.sortBy);
      expect(result.groupBy).toEqual(highPriority.groupBy);
    });

    it('should handle multiple priority levels', () => {
      const lowest: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 1, name: 'Workspace 1' }],
          },
        ],
      };

      const medium: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 2, name: 'Workspace 2' }],
          },
          {
            key: 'channel',
            operator: ReportFilterOperator.In,
            value: [{ id: 'facebook', name: 'Facebook' }],
          },
        ],
      };

      const highest: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 3, name: 'Workspace 3' }],
          },
          {
            key: 'brand',
            operator: ReportFilterOperator.In,
            value: [{ id: 'brand1', name: 'Brand 1' }],
          },
        ],
      };

      const result = mergeDashboardFilters([lowest, medium, highest]);

      expect(result.filters).toHaveLength(3);
      expect(result.filters.find((f) => f.key === 'workspace')).toEqual(
        highest.filters[0],
      );
      expect(result.filters.find((f) => f.key === 'channel')).toEqual(
        medium.filters[1],
      );
      expect(result.filters.find((f) => f.key === 'brand')).toEqual(
        highest.filters[1],
      );
    });
  });

  describe('mergeDashboardFiltersStrict', () => {
    it('should filter out null and undefined values', () => {
      const validFilter: DashboardFilterDto = {
        filters: [
          {
            key: 'workspace',
            operator: ReportFilterOperator.In,
            value: [{ id: 1, name: 'Workspace 1' }],
          },
        ],
      };

      const result = mergeDashboardFiltersStrict([
        null,
        undefined,
        validFilter,
      ]);

      expect(result).toEqual(validFilter);
    });
  });
});
