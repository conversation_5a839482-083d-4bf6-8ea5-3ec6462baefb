import { z, ZodError } from 'zod/v4';
import {
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { DataResponseDto } from '../dto/dashboard-data.dto';
import {
  VisualizationType,
  WidgetTransformCtx,
  WidgetType,
} from '../dashboard-types';
import { widgetDataRegistry } from '../constants/dashboard-constants';

export class WidgetDataMapper {
  private readonly widgetSchema: z.ZodTypeAny;
  private readonly vizTransform: (
    validated: any,
    ctx: WidgetTransformCtx,
  ) => DataResponseDto;
  private readonly widgetCfg: (typeof widgetDataRegistry)[WidgetType];

  constructor(
    private readonly widgetType: WidgetType,
    private readonly vizType: VisualizationType,
  ) {
    const cfg = widgetDataRegistry[widgetType];
    if (!cfg) {
      throw new BadRequestException(`Unsupported widget type ${widgetType}`);
    }
    this.widgetCfg = cfg;
    this.widgetSchema = cfg.schema;

    const transform = cfg.transforms[vizType];
    if (!transform) {
      throw new BadRequestException(
        `Widget ${widgetType} doesn’t support ${vizType}`,
      );
    }
    this.vizTransform = transform;
  }

  /** validate → enrich → transform */
  async map(
    raw: unknown,
    ctx: WidgetTransformCtx = {},
  ): Promise<DataResponseDto> {
    const validated = this.validatePayload(raw);
    const enriched = await this.enrichIfNeeded(validated, ctx);
    return this.applyTransform(enriched, ctx);
  }

  /** ── 1. validation ───────────────────────────── */
  private validatePayload(raw: unknown): any {
    return this.widgetSchema.parse(raw);
  }

  /** ── 2. optional enrichment ──────────────────── */
  private async enrichIfNeeded(validated: any, ctx: WidgetTransformCtx) {
    return this.widgetCfg?.enrich
      ? await this.widgetCfg.enrich(validated, ctx)
      : validated;
  }

  /** ── 3. transform to UI DTO ──────────────────── */
  private applyTransform(data: any, ctx: WidgetTransformCtx): DataResponseDto {
    return this.vizTransform(data, ctx);
  }
}
