import { Injectable } from '@nestjs/common';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Mapper, createMap, forMember, mapFrom } from '@automapper/core';

import { Dashboard } from '../../../entities/dashboard.entity';
import { ReadListDashboardDto } from '../../dto/read-list-dashboard.dto';
import { UserDto } from '../../dto/user.dto';
import { DashboardSharingScope } from '../../constants/constants';
import { ReadDashboardDto } from '../../dto/read-dashboard.dto';
import { DashboardWidget } from '../../../entities/dashboard-widget.entity';
import { WidgetDto } from '../../../executive-dashboard/dto/widget.dto';
import { ReportFilter } from '../../../entities/report-filter.entity';
import { DashboardFilterDto } from '../../../executive-dashboard/dto/create-dashboard.dto';
import { ReportFilterDtoV2 } from '../../../reports/model/report-filters.dto';
import { Sort } from '../../../reports/model/sort';
import { CreateReportGroupByDto } from '../../../reports/model/create-report-group-by.dto';

@Injectable()
export class DashboardProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(
        mapper,
        Dashboard,
        ReadListDashboardDto,

        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.id),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
        forMember(
          (dest) => dest.description,
          mapFrom((src) => src.description ?? null),
        ),

        forMember(
          (dest) => dest.photoUrl,
          mapFrom((_src) => ''),
        ),

        forMember(
          (dest) => dest.dashboardFilter,
          mapFrom((src) => mapFilter(src.dashboardFilter)),
        ),

        forMember(
          (dest) => dest.isFavorite,
          mapFrom((src) => Boolean(src.dashboardUserConfigs?.[0]?.isFavorite)),
        ),

        forMember(
          (dest) => dest.sharingScope,
          mapFrom((src) => src.sharingScope as DashboardSharingScope),
        ),

        forMember(
          (dest) => dest.dateCreated,
          mapFrom((src) => src.dateCreated.toISOString()),
        ),
        forMember(
          (dest) => dest.lastUpdated,
          mapFrom((src) => src.lastUpdated.toISOString()),
        ),

        forMember(
          (dest) => dest.createdBy,
          mapFrom(
            (src) =>
              ({
                id: src.createdByPerson?.id,
                displayName: src.createdByPerson?.displayName,
                photoUrl: src.createdByPerson?.photo,
              } as UserDto),
          ),
        ),
        forMember(
          (dest) => dest.lastModifiedBy,
          mapFrom(
            (src) =>
              ({
                id: src.lastModifiedByPerson?.id,
                displayName: src.lastModifiedByPerson?.displayName,
                photoUrl: src.lastModifiedByPerson?.photo,
              } as UserDto),
          ),
        ),
      );

      createMap(
        mapper,
        DashboardWidget,
        WidgetDto,

        // We do not need to add members here for additional when the entity (source) names and types match the dto (destination) names and types.
        // This is already the case for data flags (isCompareToPreviousPeriodEnabled, isIncludeTotalEnabled, etc.).

        forMember(
          (d) => d.id,
          mapFrom((s) => s.id),
        ),
        forMember(
          (d) => d.widgetType,
          mapFrom((s) => s.widgetType),
        ),
        forMember(
          (d) => d.name,
          mapFrom((s) => s.name),
        ),
        forMember(
          (d) => d.description,
          mapFrom((s) => s.description),
        ),

        forMember(
          (d) => d.parameters,
          mapFrom((s) => s.parameters),
        ),

        forMember(
          (d) => d.gridX,
          mapFrom((s) => s.gridX),
        ),
        forMember(
          (d) => d.gridY,
          mapFrom((s) => s.gridY),
        ),
        forMember(
          (d) => d.gridWidth,
          mapFrom((s) => s.gridWidth),
        ),
        forMember(
          (d) => d.gridHeight,
          mapFrom((s) => s.gridHeight),
        ),

        forMember(
          (d) => d.visualizationType,
          mapFrom((s) => s.visualizationType),
        ),

        forMember(
          (d) => d.filter,
          mapFrom((src) => mapFilter(src.filter)),
        ),
        forMember(
          (d) => d.dateCreated,
          mapFrom((s) => s.dateCreated.toISOString()),
        ),
        forMember(
          (d) => d.lastUpdated,
          mapFrom((s) => s.lastUpdated.toISOString()),
        ),

        forMember(
          (d) => d.createdBy,
          mapFrom(
            (s) =>
              ({
                id: s.createdBy.id,
                displayName: s.createdBy.displayName,
                photoUrl: s.createdBy.photo,
              } as UserDto),
          ),
        ),
        forMember(
          (d) => d.lastModifiedBy,
          mapFrom(
            (s) =>
              ({
                id: s.lastModifiedBy.id,
                displayName: s.lastModifiedBy.displayName,
                photoUrl: s.lastModifiedBy.photo,
              } as UserDto),
          ),
        ),
      );

      createMap(
        mapper,
        Dashboard,
        ReadDashboardDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.id),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.name),
        ),
        forMember(
          (dest) => dest.description,
          mapFrom((src) => src.description ?? null),
        ),

        forMember(
          (dest) => dest.photoUrl,
          mapFrom((_src) => ''),
        ),

        forMember(
          (dest) => dest.dashboardFilter,
          mapFrom((src) => mapFilter(src.dashboardFilter)),
        ),

        forMember(
          (dest) => dest.isFavorite,
          mapFrom((src) => Boolean(src.dashboardUserConfigs?.[0]?.isFavorite)),
        ),

        forMember(
          (dest) => dest.sharingScope,
          mapFrom((src) => src.sharingScope as DashboardSharingScope),
        ),

        forMember(
          (dest) => dest.dateCreated,
          mapFrom((src) => src.dateCreated.toISOString()),
        ),
        forMember(
          (dest) => dest.lastUpdated,
          mapFrom((src) => src.lastUpdated.toISOString()),
        ),

        forMember(
          (dest) => dest.createdBy,
          mapFrom(
            (src) =>
              ({
                id: src.createdByPerson?.id,
                displayName: src.createdByPerson?.displayName,
                photoUrl: src.createdByPerson?.photo,
              } as UserDto),
          ),
        ),
        forMember(
          (dest) => dest.lastModifiedBy,
          mapFrom(
            (src) =>
              ({
                id: src.lastModifiedByPerson?.id,
                displayName: src.lastModifiedByPerson?.displayName,
                photoUrl: src.lastModifiedByPerson?.photo,
              } as UserDto),
          ),
        ),
        forMember(
          (dest) => dest.widgets,
          mapFrom((src) =>
            (src.widgets ?? []).map((w) =>
              mapper.map(w, DashboardWidget, WidgetDto),
            ),
          ),
        ),
      );
    };
  }
}

function safeParse<T>(raw?: string): T | undefined {
  try {
    return raw ? JSON.parse(raw) : undefined;
  } catch {
    return undefined;
  }
}

export function mapFilter(filter?: ReportFilter): DashboardFilterDto {
  const parsed = safeParse<DashboardFilterDto | ReportFilterDtoV2[]>(
    filter?.filters,
  );
  return {
    filters: Array.isArray(parsed) ? parsed : parsed?.filters ?? [],
    sortBy: safeParse<Sort>(filter?.sortBy),
    groupBy: safeParse<CreateReportGroupByDto>(filter?.groupBy),
  };
}
