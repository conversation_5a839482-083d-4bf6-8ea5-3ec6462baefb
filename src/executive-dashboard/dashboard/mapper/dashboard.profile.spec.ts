import { createMapper, Mapper, MappingProfile } from '@automapper/core';
import { classes } from '@automapper/classes';
import { DashboardProfile } from './dashboard.profile';
import { Dashboard } from '../../../entities/dashboard.entity';
import { ReadDashboardDto } from '../../dto/read-dashboard.dto';
import { DashboardSharingScope } from '../../constants/constants';
import { DashboardWidget } from '../../../entities/dashboard-widget.entity';
import { ReportFilter } from '../../../entities/report-filter.entity';
import { plainToInstance } from 'class-transformer';
import { VisualizationType, WidgetType } from '../../dashboard-types';

describe('DashboardProfile', () => {
  let mapper: Mapper;
  let profile: MappingProfile;

  beforeEach(() => {
    mapper = createMapper({ strategyInitializer: classes() });

    const dashboardProfile = new DashboardProfile(mapper);
    profile = dashboardProfile.profile;
    profile(mapper);
  });

  it('should map Dashboard to ReadDashboardDto correctly (no widgets)', () => {
    const now = new Date();
    const dashboard = {
      id: '1',
      name: 'Test Dashboard',
      description: 'A dashboard',
      dashboardFilter: { filters: '[]' },
      isFavorite: 0,
      sharingScope: DashboardSharingScope.PRIVATE,
      dateCreated: now,
      lastUpdated: now,
      createdByPerson: { id: 10, displayName: 'Alice', photo: 'alice.jpg' },
      lastModifiedByPerson: { id: 11, displayName: 'Bob', photo: 'bob.jpg' },
      widgets: [],
    } as unknown as Dashboard;

    const result = mapper.map(dashboard, Dashboard, ReadDashboardDto);

    expect(result.id).toBe(dashboard.id);
    expect(result.name).toBe(dashboard.name);
    expect(result.description).toBe(dashboard.description);
    expect(result.dashboardFilter).toEqual({
      filters: [],
      sortBy: undefined,
      groupBy: undefined,
    });
    expect(result.isFavorite).toBe(false);
    expect(result.sharingScope).toBe(dashboard.sharingScope);
    expect(result.dateCreated).toBe(now.toISOString());
    expect(result.lastUpdated).toBe(now.toISOString());
    expect(result.createdBy).toEqual({
      id: 10,
      displayName: 'Alice',
      photoUrl: 'alice.jpg',
    });
    expect(result.lastModifiedBy).toEqual({
      id: 11,
      displayName: 'Bob',
      photoUrl: 'bob.jpg',
    });
    expect(result.widgets).toEqual([]);
  });

  it('should map Dashboard to ReadDashboardDto with widgets', () => {
    const now = new Date();

    const widgetEntity = new DashboardWidget();
    widgetEntity.id = 'w1';
    widgetEntity.widgetType = WidgetType.ADHERENCE_SCORE;
    widgetEntity.name = 'Widget1';
    widgetEntity.description = 'desc';
    widgetEntity.parameters = { content: 'some text testing here' };
    widgetEntity.gridX = 1;
    widgetEntity.gridY = 2;
    widgetEntity.gridWidth = 3;
    widgetEntity.gridHeight = 4;
    widgetEntity.visualizationType = VisualizationType.METRIC;
    widgetEntity.filter = {
      filters: JSON.stringify([
        {
          key: 'date',
          operator: 'between',
          value: ['2025-06-01T00:00:00Z', '2025-06-30T23:59:59Z'],
        },
      ]),
      sortBy: JSON.stringify({ sortBy: 'a', sortOrder: 'ASC' }),
      groupBy: JSON.stringify({ columns: ['c'], rows: [] }),
    } as any;
    widgetEntity.isCompareToPreviousPeriodEnabled = false;
    widgetEntity.isViewDataLabelsEnabled = true;
    widgetEntity.isKpiLiftEnabled = false;
    widgetEntity.isIncludeTotalEnabled = false;
    widgetEntity.dateCreated = now;
    widgetEntity.lastUpdated = now;
    widgetEntity.createdBy = {
      id: 10,
      displayName: 'Alice',
      photo: 'alice.jpg',
    } as any;
    widgetEntity.lastModifiedBy = {
      id: 11,
      displayName: 'Bob',
      photo: 'bob.jpg',
    } as any;

    const dashboard = {
      id: '1',
      name: 'Test Dashboard',
      description: 'A dashboard',
      dashboardFilter: { filters: '[]' },
      isFavorite: 1,
      sharingScope: DashboardSharingScope.PRIVATE,
      dateCreated: now,
      lastUpdated: now,
      createdByPerson: {
        id: 10,
        displayName: 'Alice',
        photo: 'alice.jpg',
      } as any,
      lastModifiedByPerson: {
        id: 11,
        displayName: 'Bob',
        photo: 'bob.jpg',
      } as any,
      widgets: [widgetEntity],
    } as unknown as Dashboard;

    const result = mapper.map(dashboard, Dashboard, ReadDashboardDto);

    expect(result.widgets).toHaveLength(1);

    const widget = result.widgets![0];

    expect(widget.filter!.filters).toEqual(
      expect.arrayContaining([
        {
          key: 'date',
          operator: 'between',
          value: ['2025-06-01T00:00:00Z', '2025-06-30T23:59:59Z'],
        },
      ]),
    );
    expect(widget.filter!.sortBy).toEqual({ sortBy: 'a', sortOrder: 'ASC' });
    expect(widget.filter!.groupBy).toEqual({ columns: ['c'], rows: [] });

    expect(widget).toMatchObject({
      id: 'w1',
      widgetType: WidgetType.ADHERENCE_SCORE,
      name: 'Widget1',
      description: 'desc',
      parameters: { content: 'some text testing here' },
      gridX: 1,
      gridY: 2,
      gridWidth: 3,
      gridHeight: 4,
      visualizationType: 'METRIC',
      isCompareToPreviousPeriodEnabled: false,
      isViewDataLabelsEnabled: true,
      isIncludeTotalEnabled: false,
      isKpiLiftEnabled: false,
      dateCreated: now.toISOString(),
      lastUpdated: now.toISOString(),
      createdBy: { id: 10, displayName: 'Alice', photoUrl: 'alice.jpg' },
      lastModifiedBy: { id: 11, displayName: 'Bob', photoUrl: 'bob.jpg' },
    });
  });
});
