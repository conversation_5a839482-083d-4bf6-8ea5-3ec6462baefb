// src/executive-dashboard/dashboard/dashboard.service.spec.ts

import { Test, TestingModule } from '@nestjs/testing';
import {
  NotFoundException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import {
  DataSource,
  Repository,
  SelectQueryBuilder,
  QueryRunner,
  ObjectLiteral,
} from 'typeorm';
import { Mapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';

import { DashboardService } from './dashboard.service';
import { Dashboard } from '../../entities/dashboard.entity';
import { DashboardUserConfig } from '../../entities/dashboard-user-config.entity';
import { ReportFilter } from '../../entities/report-filter.entity';
import { WidgetService } from '../widget/widget.service';

import { ReadDashboardDto } from '../dto/read-dashboard.dto';
import { ReadListDashboardDto } from '../dto/read-list-dashboard.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import {
  DashboardSortBy,
  SortOrder,
  DashboardSharingScope,
} from '../constants/constants';
import { GetDashboardListFilterQueryDto } from '../dto/list-dashboards.dto';
import { UpdateDashboardDto } from '../dto/update-dashboard.dto';
import { mapFilter } from './mapper/dashboard.profile';

class FakeDashboardEntity extends Dashboard {
  id = 'dash-1';
  createdBy = 1;
  organizationId = 'org-1';
  isDeleted = false;
  filter: {
    filters: string;
    sortBy: string | null;
    groupBy: string | null;
  };
}

type MockQB = Partial<
  Record<keyof SelectQueryBuilder<Dashboard>, jest.Mock>
> & {
  getOne?: jest.Mock;
  getManyAndCount?: jest.Mock;
};

type MockRepo<T extends ObjectLiteral> = Partial<
  Record<keyof Repository<T>, jest.Mock>
>;

const qbFactory = (): MockQB => ({
  leftJoinAndSelect: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  andWhere: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  take: jest.fn().mockReturnThis(),
  addSelect: jest.fn().mockReturnThis(),
  orderBy: jest.fn().mockReturnThis(),
  addOrderBy: jest.fn().mockReturnThis(),
  getManyAndCount: jest.fn(),
  getOne: jest.fn(),
});

const repoFactory = <T extends ObjectLiteral>(): MockRepo<T> => ({
  createQueryBuilder: jest.fn(),
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
});

describe('DashboardService', () => {
  let service: DashboardService;
  let dashRepo: MockRepo<Dashboard>;
  let configRepo: MockRepo<DashboardUserConfig>;
  let filterRepo: MockRepo<ReportFilter>;
  let dataSource: Partial<DataSource>;
  let mapper: Partial<Record<keyof Mapper, jest.Mock>>;
  let fakeQb: MockQB;
  let fakeQr: Partial<QueryRunner>;

  beforeEach(async () => {
    // QueryBuilder for list/get
    fakeQb = qbFactory();
    fakeQb.getManyAndCount!.mockResolvedValue([[new FakeDashboardEntity()], 1]);
    fakeQb.getOne!.mockResolvedValue(new FakeDashboardEntity());

    // Repository mocks
    dashRepo = repoFactory<Dashboard>();
    (dashRepo.createQueryBuilder as jest.Mock).mockReturnValue(fakeQb as any);
    configRepo = repoFactory<DashboardUserConfig>();
    filterRepo = repoFactory<ReportFilter>();

    // DataSource / QueryRunner mocks for update()
    fakeQr = {
      connect: jest.fn().mockResolvedValue(undefined),
      startTransaction: jest.fn().mockResolvedValue(undefined),
      commitTransaction: jest.fn().mockResolvedValue(undefined),
      rollbackTransaction: jest.fn().mockResolvedValue(undefined),
      release: jest.fn().mockResolvedValue(undefined),
      manager: {
        findOne: jest.fn(),
        save: jest.fn(),
      } as any,
    };
    dataSource = {
      createQueryRunner: jest.fn().mockReturnValue(fakeQr as QueryRunner),
    };

    // Mapper stub
    mapper = {
      mapArrayAsync: jest
        .fn()
        .mockResolvedValue([{ id: 'dash-1' }] as ReadListDashboardDto[]),
      mapAsync: jest
        .fn()
        .mockResolvedValue({ id: 'dash-1' } as ReadDashboardDto),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DashboardService,
        { provide: WidgetService, useValue: {} },
        { provide: getRepositoryToken(Dashboard), useValue: dashRepo },
        {
          provide: getRepositoryToken(DashboardUserConfig),
          useValue: configRepo,
        },
        { provide: getRepositoryToken(ReportFilter), useValue: filterRepo },
        { provide: DataSource, useValue: dataSource },
        { provide: getMapperToken(), useValue: mapper },
      ],
    }).compile();

    service = module.get(DashboardService);
  });

  describe('listDashboards()', () => {
    it('should fetch, map, and return a paginated result', async () => {
      const userDetails = {
        userId: 99,
        organizationId: 'org-1',
        authorization: 'auth-token',
      };
      const opts: GetDashboardListFilterQueryDto = {
        offset: 5,
        perPage: 10,
        sortBy: [DashboardSortBy.NAME],
        sortOrder: [SortOrder.ASC],
        filter: undefined,
      };

      const result = await service.listDashboards(userDetails as any, opts);

      expect(dashRepo.createQueryBuilder).toHaveBeenCalledWith('d');
      expect(fakeQb.where).toHaveBeenCalledWith('d.organizationId = :orgId', {
        orgId: 'org-1',
      });
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        '(d.createdBy = :userId OR d.sharingScope = :orgScope)',
        { userId: 99, orgScope: DashboardSharingScope.ORGANIZATION },
      );
      expect(fakeQb.skip).toHaveBeenCalledWith(5);
      expect(fakeQb.take).toHaveBeenCalledWith(10);
      expect(fakeQb.orderBy).toHaveBeenCalledWith('d.name', SortOrder.ASC);
      expect(mapper.mapArrayAsync).toHaveBeenCalledWith(
        [expect.any(FakeDashboardEntity)],
        Dashboard,
        ReadListDashboardDto,
      );
      expect(result).toBeInstanceOf(PaginatedResultArray);
    });

    it('should apply multiple sortBy fields', async () => {
      const userDetails = {
        userId: 99,
        organizationId: 'org-1',
        authorization: 'auth-token',
      };
      const options: GetDashboardListFilterQueryDto = {
        offset: 5,
        perPage: 10,
        sortBy: [
          DashboardSortBy.IS_FAVORITE,
          DashboardSortBy.LAST_UPDATED,
        ] as any,
        sortOrder: [SortOrder.DESC],
        filter: undefined,
      };

      const result = await service.listDashboards(userDetails, options);

      expect(fakeQb.orderBy).toHaveBeenCalledWith(
        'favoriteFlag',
        SortOrder.DESC,
      );

      expect(fakeQb.addOrderBy).toHaveBeenCalledWith(
        'd.lastUpdated',
        SortOrder.DESC,
      );

      expect(result).toBeInstanceOf(PaginatedResultArray);
    });

    it('should apply multiple sortBy fields with one sortOrder (reused)', async () => {
      const userDetails = {
        userId: 99,
        organizationId: 'org-1',
        authorization: 'auth-token',
      };
      const options: GetDashboardListFilterQueryDto = {
        offset: 0,
        perPage: 5,
        sortBy: [
          DashboardSortBy.IS_FAVORITE,
          DashboardSortBy.LAST_UPDATED,
          DashboardSortBy.NAME,
        ] as any,
        sortOrder: [SortOrder.DESC],
        filter: undefined,
      };

      const result = await service.listDashboards(userDetails as any, options);

      expect(fakeQb.orderBy).toHaveBeenCalledWith(
        'favoriteFlag',
        SortOrder.DESC,
      );

      expect(fakeQb.addOrderBy).toHaveBeenCalledWith(
        'd.lastUpdated',
        SortOrder.DESC,
      );
      expect(fakeQb.addOrderBy).toHaveBeenCalledWith('d.name', SortOrder.DESC);
      expect(result).toBeInstanceOf(PaginatedResultArray);
    });

    it('should apply multiple sortBy fields with matching sortOrder array', async () => {
      const userDetails = {
        userId: 99,
        organizationId: 'org-1',
        authorization: 'auth-token',
      };
      const options: GetDashboardListFilterQueryDto = {
        offset: 2,
        perPage: 3,
        sortBy: [
          DashboardSortBy.SHARING_SCOPE,
          DashboardSortBy.DATE_CREATED,
        ] as any,
        sortOrder: [SortOrder.ASC, SortOrder.DESC],
        filter: undefined,
      };

      const result = await service.listDashboards(userDetails as any, options);

      expect(fakeQb.orderBy).toHaveBeenCalledWith(
        'd.sharingScope',
        SortOrder.ASC,
      );

      expect(fakeQb.addOrderBy).toHaveBeenCalledWith(
        'd.dateCreated',
        SortOrder.DESC,
      );
      expect(result).toBeInstanceOf(PaginatedResultArray);
    });

    it('should apply searchTerm filter', async () => {
      const userDetails = {
        userId: 1,
        organizationId: 'org-1',
        authorization: '',
      } as any;
      const opts: GetDashboardListFilterQueryDto = {
        offset: 0,
        perPage: 10,
        sortBy: [DashboardSortBy.DATE_CREATED],
        sortOrder: [SortOrder.DESC],
        filter: { searchTerm: 'stuff' },
      };

      await service.listDashboards(userDetails, opts);

      // verify we applied the LIKE filter
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        '(d.name LIKE :search OR d.description LIKE :search)',
        { search: '%stuff%' },
      );
    });

    it('should apply dateCreated range filter', async () => {
      const userDetails = {
        userId: 1,
        organizationId: 'org-1',
        authorization: '',
      } as any;
      const opts: GetDashboardListFilterQueryDto = {
        offset: 0,
        perPage: 10,
        sortBy: [DashboardSortBy.DATE_CREATED],
        sortOrder: [SortOrder.DESC],
        filter: {
          dateCreated: { startDate: '2025-01-01', endDate: '2025-06-16' },
        },
      };

      await service.listDashboards(userDetails, opts);

      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        'DATE(d.date_created) >= :createdStart',
        { createdStart: '2025-01-01' },
      );
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        'DATE(d.date_created) <= :createdEnd',
        { createdEnd: '2025-06-16' },
      );
    });

    it('should apply lastUpdated range filter', async () => {
      const userDetails = {
        userId: 1,
        organizationId: 'org-1',
        authorization: '',
      } as any;
      const opts: GetDashboardListFilterQueryDto = {
        offset: 0,
        perPage: 10,
        sortBy: [DashboardSortBy.DATE_CREATED],
        sortOrder: [SortOrder.DESC],
        filter: {
          lastUpdated: { startDate: '2025-01-01', endDate: '2025-06-16' },
        },
      };

      await service.listDashboards(userDetails, opts);

      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        'DATE(d.last_updated) >= :updatedStart',
        { updatedStart: '2025-01-01' },
      );
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        'DATE(d.last_updated) <= :updatedEnd',
        { updatedEnd: '2025-06-16' },
      );
    });

    it('should apply combined searchTerm, dateCreated and lastUpdated filters', async () => {
      const userDetails = {
        userId: 1,
        organizationId: 'org-1',
        authorization: '',
      } as any;
      const opts: GetDashboardListFilterQueryDto = {
        offset: 0,
        perPage: 10,
        sortBy: [DashboardSortBy.DATE_CREATED],
        sortOrder: [SortOrder.DESC],
        filter: {
          searchTerm: 'foo',
          dateCreated: { startDate: '2025-02-01' },
          lastUpdated: { endDate: '2025-02-28' },
        },
      };

      await service.listDashboards(userDetails, opts);

      // text
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        '(d.name LIKE :search OR d.description LIKE :search)',
        { search: '%foo%' },
      );
      // created >=
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        'DATE(d.date_created) >= :createdStart',
        { createdStart: '2025-02-01' },
      );
      // updated <=
      expect(fakeQb.andWhere).toHaveBeenCalledWith(
        'DATE(d.last_updated) <= :updatedEnd',
        { updatedEnd: '2025-02-28' },
      );
    });
  });

  describe('getDashboard()', () => {
    it('should fetch single dashboard, map, and return DTO', async () => {
      fakeQb.getOne!.mockResolvedValue(new FakeDashboardEntity());

      const userDetails = {
        userId: 42,
        organizationId: 'org-2',
        authorization: 'tok',
      };
      const dto = await service.getDashboard(userDetails as any, 'dash-1');

      expect(dashRepo.createQueryBuilder).toHaveBeenCalledWith('d');
      expect(fakeQb.andWhere).toHaveBeenCalledWith('d.id = :dashboardId', {
        dashboardId: 'dash-1',
      });
      expect(mapper.mapAsync).toHaveBeenCalledWith(
        expect.any(FakeDashboardEntity),
        Dashboard,
        ReadDashboardDto,
      );
      expect(dto).toEqual({ id: 'dash-1' });
    });

    it('throws NotFoundException if not found', async () => {
      fakeQb.getOne!.mockResolvedValue(undefined);
      await expect(
        service.getDashboard(
          { userId: 1, organizationId: 'o', authorization: '' } as any,
          'no-such',
        ),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('returns dashboardFilter with correct structure', async () => {
      const rawFilter =
        '{"filters":[{"fieldName":"date","operator":"Between","value":["2025-06-01T00:00:00Z","2025-06-30T23:59:59Z"]}],"sortBy":{"field":"impressions","direction":"DESC"},"groupBy":{"fields":["campaignId"]}}';

      const ent = new FakeDashboardEntity();
      ent.filter = { filters: rawFilter, sortBy: null, groupBy: null };

      fakeQb.getOne!.mockResolvedValue(ent);

      (mapper.mapAsync as jest.Mock).mockImplementation(async (e: any) => ({
        id: e.id,
        dashboardFilter: mapFilter({
          filters: e.filter.filters,
          sortBy: e.filter.sortBy,
          groupBy: e.filter.groupBy,
        } as any),
      }));

      const dto = await service.getDashboard(
        { userId: 42, organizationId: 'org-2', authorization: 'tok' } as any,
        ent.id,
      );

      expect(dto.dashboardFilter).toBeDefined();
      expect(Array.isArray(dto.dashboardFilter!.filters)).toBe(true);
      expect(dto.dashboardFilter!.filters[0]).toHaveProperty(
        'fieldName',
        'date',
      );
    });
  });

  describe('updateFavorite()', () => {
    it('throws NotFoundException if dashboard missing', async () => {
      dashRepo.findOne!.mockResolvedValue(undefined);
      await expect(
        service.updateFavorite(1, 'org-1', 'dash-1', true),
      ).rejects.toThrow(NotFoundException);
    });

    it('throws ForbiddenException if not owner', async () => {
      dashRepo.findOne!.mockResolvedValue({
        id: 'dash-1',
        createdBy: 2,
        isDeleted: false,
        sharingScope: DashboardSharingScope.PRIVATE,
      } as any);
      await expect(
        service.updateFavorite(1, 'org-1', 'dash-1', true),
      ).rejects.toThrow(ForbiddenException);
    });

    it('creates new config when absent', async () => {
      dashRepo.findOne!.mockResolvedValue({
        id: 'dash-1',
        createdBy: 1,
        isDeleted: false,
      } as any);
      configRepo.findOne!.mockResolvedValue(undefined);
      const newCfg = { dashboardId: 'dash-1', userId: 1, isFavorite: true };
      configRepo.create!.mockReturnValue(newCfg as any);
      configRepo.save!.mockResolvedValue(newCfg as any);

      const res = await service.updateFavorite(1, 'org-1', 'dash-1', true);
      expect(configRepo.create).toHaveBeenCalledWith({
        dashboardId: 'dash-1',
        userId: 1,
        isFavorite: true,
      });
      expect(res).toEqual({ id: 'dash-1', isFavorite: true });
    });

    it('updates existing config when present', async () => {
      dashRepo.findOne!.mockResolvedValue({
        id: 'dash-1',
        createdBy: 1,
        isDeleted: false,
      } as any);
      const existing = { dashboardId: 'dash-1', userId: 1, isFavorite: false };
      configRepo.findOne!.mockResolvedValue(existing as any);
      configRepo.save!.mockResolvedValue({
        ...existing,
        isFavorite: true,
      } as any);

      const res = await service.updateFavorite(1, 'org-1', 'dash-1', true);
      expect(existing.isFavorite).toBe(true);
      expect(res).toEqual({ id: 'dash-1', isFavorite: true });
    });

    it('wraps DB errors', async () => {
      dashRepo.findOne!.mockResolvedValue({
        id: 'dash-1',
        createdBy: 1,
        isDeleted: false,
      } as any);
      configRepo.findOne!.mockResolvedValue(undefined);
      configRepo.create!.mockReturnValue({} as any);
      configRepo.save!.mockRejectedValue(new Error('fail'));

      await expect(
        service.updateFavorite(1, 'org-1', 'dash-1', true),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('remove()', () => {
    const user = { userId: 1, organizationId: 'org-1' } as any;

    it('throws NotFoundException if missing', async () => {
      dashRepo.findOne!.mockResolvedValue(undefined);
      await expect(service.remove(user, 'dash-1')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('throws ForbiddenException if not owner', async () => {
      dashRepo.findOne!.mockResolvedValue({ createdBy: 2 } as any);
      await expect(service.remove(user, 'dash-1')).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('soft-deletes when allowed', async () => {
      const dash: any = { id: 'dash-1', createdBy: 1, isDeleted: false };
      dashRepo.findOne!.mockResolvedValue(dash);
      dashRepo.save!.mockResolvedValue({ ...dash, isDeleted: true });

      await service.remove(user, 'dash-1');

      expect(dash.isDeleted).toBe(true);
      expect(dash.deleteDate).toBeInstanceOf(Date);
      expect(dash.lastModifiedBy).toBe(1);
    });

    it('wraps save errors', async () => {
      const dash: any = { id: 'dash-1', createdBy: 1, isDeleted: false };
      dashRepo.findOne!.mockResolvedValue(dash);
      dashRepo.save!.mockRejectedValue(new Error('db'));

      await expect(service.remove(user, 'dash-1')).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('update()', () => {
    const userDetails = { organizationId: 'org-1', userId: 1 };
    const dashId = 'dash-1';

    it('throws NotFoundException if dashboard not found', async () => {
      // qr.manager.findOne returns null
      (fakeQr.manager!.findOne as jest.Mock).mockResolvedValue(undefined);
      await expect(
        service.update(userDetails, dashId, {}),
      ).rejects.toBeInstanceOf(NotFoundException);
    });

    it('throws ForbiddenException if not owner', async () => {
      const found = { id: dashId, createdBy: 2, isDeleted: false, widgets: [] };
      (fakeQr.manager!.findOne as jest.Mock).mockResolvedValue(found);
      await expect(
        service.update(userDetails, dashId, {}),
      ).rejects.toBeInstanceOf(ForbiddenException);
    });

    it('applies simple patches and returns updated DTO', async () => {
      // found dashboard
      const entity: any = {
        id: dashId,
        createdBy: 1,
        isDeleted: false,
        widgets: [],
        save: jest.fn(),
      };
      (fakeQr.manager!.findOne as jest.Mock).mockResolvedValue(entity);
      // manager.save and reload
      (fakeQr.manager!.save as jest.Mock).mockResolvedValue(entity);
      // after widgets, reload full
      const full: any = { id: dashId };
      (fakeQr.manager!.findOne as jest.Mock)
        .mockResolvedValueOnce(entity)
        .mockResolvedValueOnce(full);
      const dto: UpdateDashboardDto = {
        name: 'New Name',
        description: 'New Desc',
      };
      const res = await service.update(userDetails, dashId, dto);
      expect(entity.name).toBe('New Name');
      expect(entity.description).toBe('New Desc');
      expect(res).toEqual({ id: dashId });
    });

    it('wraps unexpected errors into InternalServerErrorException', async () => {
      (fakeQr.manager!.findOne as jest.Mock).mockRejectedValue(
        new Error('oops'),
      );
      await expect(
        service.update(userDetails, dashId, {}),
      ).rejects.toBeInstanceOf(InternalServerErrorException);
    });
  });
});
