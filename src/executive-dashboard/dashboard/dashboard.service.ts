import {
  ForbiddenException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateDashboardDto } from '../dto/create-dashboard.dto';
import { UpdateDashboardDto } from '../dto/update-dashboard.dto';
import { ReadDashboardDto } from '../dto/read-dashboard.dto';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';

import { Dashboard } from '../../entities/dashboard.entity';
import {
  DataSource,
  QueryRunner,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';

import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { DashboardUserConfig } from '../../entities/dashboard-user-config.entity';
import { DEFAULT_PAGINATION_OPTIONS } from '../constants/constants';
import { UserDetailsDto } from '../../analytics/dto/user-details.dto';
import { DashboardSortBy, SortOrder } from '../constants/constants';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { DashboardSharingScope } from '../constants/constants';
import {
  GetDashboardListFilterQueryDto,
  ListDashboardsFilterDto,
} from '../dto/list-dashboards.dto';
import { ReadListDashboardDto } from '../dto/read-list-dashboard.dto';
import { WidgetService } from '../widget/widget.service';
import { VisualizationType, WidgetType } from '../dashboard-types';
import { Person } from '../../entities/person.entity';
import { ReportFilter } from '../../entities/report-filter.entity';
import { mapFilter } from './mapper/dashboard.profile';
import { CreateWidgetDto } from '../dto/create-widget.dto';
import { CURRENT_DASHBOARD_FILTER_VERSION } from '../constants/dashboard-constants';

@Injectable()
export class DashboardService {
  constructor(
    private readonly widgetService: WidgetService,

    @InjectDataSource()
    private readonly ds: DataSource,

    @InjectRepository(Dashboard)
    private readonly dashboardRepository: Repository<Dashboard>,

    @InjectRepository(DashboardUserConfig)
    private readonly configDashboardRepository: Repository<DashboardUserConfig>,

    @InjectRepository(ReportFilter)
    private readonly filterRepo: Repository<ReportFilter>,

    @InjectMapper()
    private readonly mapper: Mapper,
  ) {}

  private async withTransaction<T>(
    task: (qr: QueryRunner) => Promise<T>,
    errorMessage: string,
  ): Promise<T> {
    const qr = this.ds.createQueryRunner();
    await qr.connect();
    await qr.startTransaction();
    try {
      const result = await task(qr);
      await qr.commitTransaction();
      return result;
    } catch (err) {
      await qr.rollbackTransaction();
      if (err instanceof HttpException) {
        throw err;
      }

      throw new InternalServerErrorException(`${errorMessage}: ${err}`);
    } finally {
      await qr.release();
    }
  }

  async create(
    userDetails: { organizationId: string; userId: number },
    dto: CreateDashboardDto,
  ): Promise<ReadDashboardDto> {
    const { organizationId, userId } = userDetails;

    return this.withTransaction(async (qr) => {
      let baseName = dto.name;
      let baseDesc = dto.description;
      let baseScope = dto.sharingScope;
      let baseFilterDto = dto.dashboardFilter;
      let widgetDtos: CreateWidgetDto[] | undefined = dto.widgets
        ? dto.widgets.map((w) => ({
            ...w,
            widgetType: w.widgetType as WidgetType,
            visualizationType: w.visualizationType as VisualizationType,
          }))
        : undefined;

      if (dto.sourceDashboardId) {
        const src = await qr.manager.findOne(Dashboard, {
          where: { id: dto.sourceDashboardId, isDeleted: false },
          relations: [
            'dashboardFilter',
            'widgets',
            'widgets.filter',
            'widgets.createdBy',
            'widgets.lastModifiedBy',
          ],
        });
        if (!src) {
          throw new NotFoundException(
            `Source dashboard not found: ${dto.sourceDashboardId}`,
          );
        }

        baseName = baseName ?? `${src.name} (Copy)`;
        baseDesc = baseDesc ?? src.description;
        baseScope = baseScope ?? src.sharingScope;

        if (!baseFilterDto && src.dashboardFilter) {
          baseFilterDto = mapFilter(src.dashboardFilter);
        }

        if (!widgetDtos) {
          // map from entity → CreateWidgetDto
          widgetDtos = src.widgets
            .filter((w) => !w.isDeleted)
            .map((w) => ({
              widgetType: w.widgetType,
              name: w.name!,
              description: w.description!,
              visualizationType: w.visualizationType,
              parameters: w.parameters,
              filter: mapFilter(w.filter),
              isCompareToPreviousPeriodEnabled:
                w.isCompareToPreviousPeriodEnabled,
              isViewDataLabelsEnabled: w.isViewDataLabelsEnabled,
              gridX: w.gridX,
              gridY: w.gridY,
              gridWidth: w.gridWidth,
              gridHeight: w.gridHeight,
            }));
        }
      }

      const dash = this.dashboardRepository.create({
        organizationId,
        name: baseName!,
        description: baseDesc ?? '',
        sharingScope: baseScope ?? DashboardSharingScope.PRIVATE,
        sourceDashboardId: dto.sourceDashboardId,
        createdBy: userId,
        lastModifiedBy: userId,
      });
      const savedDash = await qr.manager.save(dash);

      if (baseFilterDto) {
        const rfEntity = this.filterRepo.create({
          owner: { id: userId } as Person,
          lastUpdatedByUser: { id: userId } as Person,
          filtersVersion: CURRENT_DASHBOARD_FILTER_VERSION,
          filters: JSON.stringify(baseFilterDto.filters),
          sortBy: baseFilterDto.sortBy
            ? JSON.stringify(baseFilterDto.sortBy)
            : undefined,
          groupBy: baseFilterDto.groupBy
            ? JSON.stringify(baseFilterDto.groupBy)
            : undefined,
        });
        const savedRf = await qr.manager.save(rfEntity);
        savedDash.dashboardFilterId = savedRf.id;
        await qr.manager.save(savedDash);
      }

      if (widgetDtos && widgetDtos.length) {
        for (const wDto of widgetDtos) {
          await this.widgetService.createWidgetWithRunner(
            qr,
            { id: savedDash.id, organizationId },
            wDto as any, // CreateWidgetDto shape
            userId,
          );
        }
      }

      const full = await qr.manager.findOne(Dashboard, {
        where: { id: savedDash.id },
        relations: [
          'dashboardFilter',
          'createdByPerson',
          'lastModifiedByPerson',
          'widgets',
          'widgets.filter',
          'widgets.createdBy',
          'widgets.lastModifiedBy',
        ],
      });
      if (!full) {
        throw new InternalServerErrorException('Reload failed');
      }
      return this.mapper.mapAsync(full, Dashboard, ReadDashboardDto);
    }, 'failed to create dashboard');
  }

  async listDashboards(
    userDetails: UserDetailsDto,
    options: GetDashboardListFilterQueryDto,
  ): Promise<PaginatedResultArray<ReadListDashboardDto>> {
    const { userId, organizationId } = userDetails;

    const {
      offset = DEFAULT_PAGINATION_OPTIONS.offset,
      perPage = DEFAULT_PAGINATION_OPTIONS.perPage,
      sortBy = [DashboardSortBy.DATE_CREATED],
      sortOrder = [SortOrder.DESC],
      filter,
    } = options;

    const qb = this.buildBaseQuery(organizationId, userId);

    this.applyDashboardListFilters(qb, filter);

    this.applySorting(qb, sortBy, sortOrder);

    qb.skip(offset).take(perPage);
    const [dashboards, totalCount] = await qb.getManyAndCount();

    const dashboardDtos = await this.mapper.mapArrayAsync(
      dashboards,
      Dashboard,
      ReadListDashboardDto,
    );

    return new PaginatedResultArray(dashboardDtos, totalCount);
  }

  private buildBaseQuery(
    organizationId: string,
    userId: number,
  ): SelectQueryBuilder<Dashboard> {
    return this.dashboardRepository
      .createQueryBuilder('d')
      .leftJoinAndSelect('d.dashboardFilter', 'filter')
      .leftJoinAndSelect('d.createdByPerson', 'creator')
      .leftJoinAndSelect('d.lastModifiedByPerson', 'modifier')
      .leftJoinAndSelect(
        'd.dashboardUserConfigs',
        'uc',
        'uc.dashboardId = d.id AND uc.userId = :userId',
        { userId },
      )
      .addSelect('COALESCE(uc.is_favorite, false)', 'favoriteFlag')
      .where('d.organizationId = :orgId', { orgId: organizationId })
      .andWhere('d.isDeleted = false')
      .andWhere('(d.createdBy = :userId OR d.sharingScope = :orgScope)', {
        userId,
        orgScope: DashboardSharingScope.ORGANIZATION,
      });
  }

  private applySorting(
    qb: SelectQueryBuilder<Dashboard>,
    sortBy: DashboardSortBy[] = [],
    sortOrders: SortOrder[] = [],
  ) {
    // default if nothing passed
    if (!sortBy.length) {
      qb.orderBy('d.dateCreated', SortOrder.DESC);
      return;
    }

    const columnFor = (s: DashboardSortBy) =>
      ({
        [DashboardSortBy.NAME]: 'd.name',
        [DashboardSortBy.DATE_CREATED]: 'd.dateCreated',
        [DashboardSortBy.LAST_UPDATED]: 'd.lastUpdated',
        [DashboardSortBy.SHARING_SCOPE]: 'd.sharingScope',
        [DashboardSortBy.IS_FAVORITE]: 'favoriteFlag',
        [DashboardSortBy.CREATED_BY]: 'creator.displayName',
        [DashboardSortBy.VERSION]: 'd.version',
      }[s]);

    const getOrder = (idx: number) =>
      sortOrders[idx] ?? sortOrders[sortOrders.length - 1] ?? SortOrder.DESC;

    qb.orderBy(columnFor(sortBy[0]), getOrder(0));

    for (let i = 1; i < sortBy.length; i++) {
      qb.addOrderBy(columnFor(sortBy[i]), getOrder(i));
    }
  }

  /**
   * Applies filters to the dashboard list query.
   * @param qb - The query builder for the Dashboard entity.
   * @param filter - The filter criteria to apply.
   */
  async applyDashboardListFilters(
    qb: SelectQueryBuilder<Dashboard>,
    filter?: ListDashboardsFilterDto,
  ): Promise<void> {
    if (!filter) return;

    if (filter.searchTerm) {
      qb.andWhere('(d.name LIKE :search OR d.description LIKE :search)', {
        search: `%${filter.searchTerm}%`,
      });
    }

    if (filter.dateCreated) {
      const { startDate, endDate } = filter.dateCreated;
      if (startDate) {
        qb.andWhere('DATE(d.date_created) >= :createdStart', {
          createdStart: startDate,
        });
      }
      if (endDate) {
        qb.andWhere('DATE(d.date_created) <= :createdEnd', {
          createdEnd: endDate,
        });
      }
    }

    if (filter.lastUpdated) {
      const { startDate, endDate } = filter.lastUpdated;
      if (startDate) {
        qb.andWhere('DATE(d.last_updated) >= :updatedStart', {
          updatedStart: startDate,
        });
      }
      if (endDate) {
        qb.andWhere('DATE(d.last_updated) <= :updatedEnd', {
          updatedEnd: endDate,
        });
      }
    }
  }

  /**
   * Fetches a single dashboard by ID, including its widgets and associated metadata.
   */
  async getDashboard(
    userDetails: UserDetailsDto,
    dashboardId: string,
  ): Promise<ReadDashboardDto> {
    const { userId, organizationId } = userDetails;

    const dashboard = await this.buildBaseQuery(organizationId, userId)
      .andWhere('d.id = :dashboardId', { dashboardId })

      .leftJoinAndSelect('d.widgets', 'w', 'w.isDeleted = :notDeleted', {
        notDeleted: 0,
      })

      .leftJoinAndSelect('w.filter', 'wFilter')
      .leftJoinAndSelect('w.createdBy', 'wCreator')
      .leftJoinAndSelect('w.lastModifiedBy', 'wModifier')
      .getOne();

    if (!dashboard) {
      throw new NotFoundException(`Dashboard not found: ${dashboardId}`);
    }

    return this.mapper.mapAsync(dashboard, Dashboard, ReadDashboardDto);
  }

  async update(
    userDetails: { organizationId: string; userId: number },
    dashboardId: string,
    dto: UpdateDashboardDto,
  ): Promise<ReadDashboardDto> {
    const { organizationId, userId } = userDetails;

    return this.withTransaction(async (qr) => {
      const dash = await qr.manager.findOne(Dashboard, {
        where: { id: dashboardId, organizationId, isDeleted: false },
        relations: ['dashboardFilter', 'widgets', 'widgets.filter'],
      });
      if (!dash) {
        throw new NotFoundException(`Dashboard not found: ${dashboardId}`);
      }
      if (dash.createdBy !== userId) {
        throw new ForbiddenException(
          `You do not have permission to update this dashboard`,
        );
      }

      if (dto.name !== undefined) {
        dash.name = dto.name;
      }
      if (dto.description !== undefined) {
        dash.description = dto.description;
      }
      if (dto.sharingScope !== undefined) {
        dash.sharingScope = dto.sharingScope;
      }

      if (dto.dashboardFilter) {
        if (dash.dashboardFilterId) {
          const rf = await qr.manager.findOne(ReportFilter, {
            where: { id: dash.dashboardFilterId },
          });
          if (!rf) {
            throw new NotFoundException(
              `Filter not found: ${dash.dashboardFilterId}`,
            );
          } else {
            rf.filters = JSON.stringify(dto.dashboardFilter.filters);
            rf.sortBy = dto.dashboardFilter.sortBy
              ? JSON.stringify(dto.dashboardFilter.sortBy)
              : undefined;
            rf.groupBy = dto.dashboardFilter.groupBy
              ? JSON.stringify(dto.dashboardFilter.groupBy)
              : undefined;
            await qr.manager.save(rf);
          }
        } else {
          const rfEntity = this.filterRepo.create({
            owner: { id: userId } as Person,
            lastUpdatedByUser: { id: userId } as Person,
            filtersVersion: CURRENT_DASHBOARD_FILTER_VERSION,
            filters: JSON.stringify(dto.dashboardFilter.filters),
            sortBy: dto.dashboardFilter.sortBy
              ? JSON.stringify(dto.dashboardFilter.sortBy)
              : undefined,
            groupBy: dto.dashboardFilter.groupBy
              ? JSON.stringify(dto.dashboardFilter.groupBy)
              : undefined,
          });
          const savedRf = await qr.manager.save(rfEntity);
          dash.dashboardFilterId = savedRf.id;
        }
      }

      await qr.manager.save(dash);

      if (dto.widgets) {
        const incoming = dto.widgets;
        const existing = dash.widgets.filter((w) => !w.isDeleted);
        const incomingIds = new Set(
          incoming.map((w) => w.id).filter((id): id is string => !!id),
        );

        for (const w of existing) {
          if (!incomingIds.has(w.id)) {
            w.isDeleted = true;
            await qr.manager.save(w);
          }
        }

        for (const wDto of incoming) {
          if (wDto.id) {
            await this.widgetService.updateWidgetWithRunner(
              qr,
              wDto.id,
              wDto as any,
              userId,
              organizationId,
            );
          } else {
            await this.widgetService.createWidgetWithRunner(
              qr,
              { id: dashboardId, organizationId },
              wDto as any,
              userId,
            );
          }
        }
      }

      const full = await qr.manager.findOne(Dashboard, {
        where: { id: dashboardId, isDeleted: false },
        relations: [
          'dashboardFilter',
          'createdByPerson',
          'lastModifiedByPerson',
          'widgets',
          'widgets.filter',
          'widgets.createdBy',
          'widgets.lastModifiedBy',
        ],
      });
      if (!full) {
        throw new InternalServerErrorException('Failed to reload dashboard');
      }

      return this.mapper.mapAsync(full, Dashboard, ReadDashboardDto);
    }, 'failed to update dashboard');
  }

  async remove(userDetails: any, dashboardId: string): Promise<void> {
    const { userId, organizationId } = userDetails;

    const dash = await this.dashboardRepository.findOne({
      where: { id: dashboardId, organizationId, isDeleted: false },
      select: ['id', 'createdBy'],
    });
    if (!dash) {
      throw new NotFoundException(`Dashboard not found: ${dashboardId}`);
    }

    if (dash.createdBy !== userId) {
      throw new ForbiddenException(
        `You do not have permission to delete this dashboard`,
      );
    }

    dash.isDeleted = true;
    dash.deleteDate = new Date();
    dash.lastUpdated = new Date();
    dash.lastModifiedBy = userId;

    try {
      await this.dashboardRepository.save(dash);
    } catch (err) {
      throw new InternalServerErrorException(
        `Failed to delete dashboard: ${dashboardId}`,
      );
    }
  }

  async updateFavorite(
    userId: number,
    organizationId: string,
    dashboardId: string,
    isFavorite: boolean,
  ): Promise<{ id: string; isFavorite: boolean }> {
    const dashboard = await this.dashboardRepository.findOne({
      where: { id: dashboardId, organizationId, isDeleted: false },
      select: ['id', 'createdBy', 'sharingScope'],
    });
    if (!dashboard)
      throw new NotFoundException(`Dashboard not found: ${dashboardId}`);

    let dashboardUserConfig = await this.configDashboardRepository.findOne({
      where: { dashboardId: dashboardId, userId: userId },
    });

    if (
      dashboard.createdBy !== userId &&
      dashboard.sharingScope === DashboardSharingScope.PRIVATE
    ) {
      throw new ForbiddenException(`Not allowed to update dashboard favorite`);
    }

    if (!dashboardUserConfig) {
      dashboardUserConfig = this.configDashboardRepository.create({
        dashboardId,
        userId,
        isFavorite,
      });
    } else {
      dashboardUserConfig.isFavorite = isFavorite;
    }

    try {
      await this.configDashboardRepository.save(dashboardUserConfig);
    } catch {
      throw new InternalServerErrorException(`Failed to update favorite flag`);
    }

    return { id: dashboardId, isFavorite: dashboardUserConfig.isFavorite };
  }
}
