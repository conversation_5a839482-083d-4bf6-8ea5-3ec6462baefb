import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { CreateDashboardDto } from '../dto/create-dashboard.dto';
import { ReadDashboardDto } from '../dto/read-dashboard.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  DashboardSortBy,
  SortOrder,
  DashboardSharingScope,
} from '../constants/constants';
import {
  ListDashboardsDto,
  GetDashboardListFilterQueryDto,
} from '../dto/list-dashboards.dto';
import { UserDetailsDto } from '../../analytics/dto/user-details.dto';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { ReadListDashboardDto } from '../dto/read-list-dashboard.dto';
import { UpdateDashboardFavoriteDto } from '../dto/update-dashboard-favorite.dto';
import { UpdateDashboardDto } from '../dto/update-dashboard.dto';

describe('DashboardController', () => {
  let controller: DashboardController;
  let service: jest.Mocked<DashboardService>;

  beforeEach(async () => {
    service = {
      create: jest.fn(),
      listDashboards: jest.fn(),
      getDashboard: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      updateFavorite: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [DashboardController],
      providers: [{ provide: DashboardService, useValue: service }],
    }).compile();

    controller = module.get(DashboardController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create()', () => {
    it('forwards correct args for a new dashboard', async () => {
      const fakeReq: any = { userId: 42 };
      const dto: CreateDashboardDto = {
        name: 'New Dash',
        description: 'Desc',
        sharingScope: DashboardSharingScope.ORGANIZATION,
        widgets: [],
      };
      const expected: ReadDashboardDto = { id: 'd1' } as any;
      service.create.mockResolvedValueOnce(expected);

      const result = await controller.create(fakeReq, 'org-123', dto);

      expect(service.create).toHaveBeenCalledWith(
        { organizationId: 'org-123', userId: 42 },
        dto,
      );
      expect(result).toBe(expected);
    });

    it('forwards correct args when cloning', async () => {
      const fakeReq: any = { userId: 7 };
      const dto: CreateDashboardDto = { sourceDashboardId: 'src-abc' };
      const expected: ReadDashboardDto = { id: 'd2' } as any;
      service.create.mockResolvedValueOnce(expected);

      const result = await controller.create(fakeReq, 'org-xyz', dto);

      expect(service.create).toHaveBeenCalledWith(
        { organizationId: 'org-xyz', userId: 7 },
        dto,
      );
      expect(result).toBe(expected);
    });
  });

  describe('listDashboards()', () => {
    it('forwards correct args', async () => {
      const fakeReq: any = { headers: { authorization: 'tok' }, userId: 5 };
      const pagination: PaginationOptions = {
        offset: 1,
        perPage: 2,
        queryId: 'q',
      };
      const sortBy = [DashboardSortBy.NAME];
      const sortOrder = [SortOrder.ASC];
      const body: ListDashboardsDto = { filter: { searchTerm: 'foo' } };
      const expectedPage = new PaginatedResultArray<ReadListDashboardDto>(
        [],
        0,
      );
      service.listDashboards.mockResolvedValueOnce(expectedPage);

      const result = await controller.listDashboards(
        fakeReq,
        'org-1',
        pagination,
        sortBy,
        sortOrder,
        body,
      );

      expect(service.listDashboards).toHaveBeenCalledWith(
        {
          organizationId: 'org-1',
          userId: 5,
          authorization: 'tok',
        } as UserDetailsDto,
        {
          ...pagination,
          sortBy,
          sortOrder,
          filter: body.filter,
        } as GetDashboardListFilterQueryDto,
      );
      expect(result).toBe(expectedPage);
    });

    it('forwards correct args', async () => {
      const fakeReq: any = { headers: { authorization: 'tok' }, userId: 5 };
      const pagination: PaginationOptions = {
        offset: 1,
        perPage: 2,
        queryId: 'q',
      };
      const sortBy = [DashboardSortBy.NAME];
      const sortOrder = [SortOrder.ASC];
      const body: ListDashboardsDto = { filter: { searchTerm: 'foo' } };
      const expectedPage = new PaginatedResultArray<ReadListDashboardDto>(
        [],
        0,
      );
      service.listDashboards.mockResolvedValueOnce(expectedPage);

      const result = await controller.listDashboards(
        fakeReq,
        'org-1',
        pagination,
        sortBy,
        sortOrder,
        body,
      );

      expect(service.listDashboards).toHaveBeenCalledWith(
        {
          organizationId: 'org-1',
          userId: 5,
          authorization: 'tok',
        } as UserDetailsDto,
        {
          ...pagination,
          sortBy,
          sortOrder,
          filter: body.filter,
        } as GetDashboardListFilterQueryDto,
      );
      expect(result).toBe(expectedPage);
    });

    it('forwards correct args when multiple sort fields & directions', async () => {
      const fakeReq: any = { headers: { authorization: 'tok2' }, userId: 7 };
      const pagination: PaginationOptions = {
        offset: 0,
        perPage: 5,
        queryId: 'xyz',
      };
      const sortBy = [
        DashboardSortBy.IS_FAVORITE,
        DashboardSortBy.LAST_UPDATED,
        DashboardSortBy.NAME,
      ];
      const sortOrder = [SortOrder.DESC, SortOrder.ASC, SortOrder.DESC];
      const body: ListDashboardsDto = { filter: { searchTerm: 'bar' } };
      const expectedPage = new PaginatedResultArray<ReadListDashboardDto>(
        [],
        0,
      );
      service.listDashboards.mockResolvedValueOnce(expectedPage);

      const result = await controller.listDashboards(
        fakeReq,
        'org-2',
        pagination,
        sortBy,
        sortOrder,
        body,
      );

      expect(service.listDashboards).toHaveBeenCalledWith(
        {
          organizationId: 'org-2',
          userId: 7,
          authorization: 'tok2',
        } as UserDetailsDto,
        {
          ...pagination,
          sortBy,
          sortOrder,
          filter: body.filter,
        } as GetDashboardListFilterQueryDto,
      );
      expect(result).toBe(expectedPage);
    });
  });

  describe('getDashboard()', () => {
    it('forwards correct args and returns DTO', async () => {
      const fakeReq: any = { headers: { authorization: 'bearer' }, userId: 9 };
      const expected: ReadDashboardDto = { id: 'd42' } as any;
      service.getDashboard.mockResolvedValueOnce(expected);

      const result = await controller.getDashboard(fakeReq, 'org-foo', 'd42');

      expect(service.getDashboard).toHaveBeenCalledWith(
        {
          organizationId: 'org-foo',
          userId: 9,
          authorization: 'bearer',
        },
        'd42',
      );
      expect(result).toBe(expected);
    });
  });

  describe('updateFavorite()', () => {
    it('forwards correct args and returns result', async () => {
      const fakeReq: any = { userId: 11 };
      const dto: UpdateDashboardFavoriteDto = { isFavorite: true };
      const expected = { id: 'd7', isFavorite: true };
      service.updateFavorite.mockResolvedValueOnce(expected);

      const result = await controller.updateFavorite(
        'org-bar',
        'd7',
        dto,
        fakeReq,
      );

      expect(service.updateFavorite).toHaveBeenCalledWith(
        11,
        'org-bar',
        'd7',
        true,
      );
      expect(result).toBe(expected);
    });
  });

  describe('remove()', () => {
    it('forwards correct args to DashboardService.remove and returns void', async () => {
      // Arrange
      const fakeReq: any = { userId: 55 };
      service.remove.mockResolvedValueOnce(undefined);

      // Act
      const result = await controller.remove(fakeReq, 'org-1', 'dash-123');

      // Assert
      expect(service.remove).toHaveBeenCalledWith(
        { organizationId: 'org-1', userId: 55 },
        'dash-123',
      );
      expect(result).toBeUndefined();
    });

    it('propagates NotFoundException', async () => {
      const fakeReq: any = { userId: 10 };
      service.remove.mockRejectedValueOnce(new NotFoundException('no dash'));

      await expect(controller.remove(fakeReq, 'org-1', 'nope')).rejects.toThrow(
        NotFoundException,
      );
    });

    it('propagates other errors', async () => {
      const fakeReq: any = { userId: 10 };
      service.remove.mockRejectedValueOnce(new Error('boom'));

      await expect(
        controller.remove(fakeReq, 'org-1', 'dash-x'),
      ).rejects.toThrow('boom');
    });
  });

  describe('update()', () => {
    it('forwards correct args and returns DTO', async () => {
      const fakeReq: any = { userId: 123 };
      const orgId = 'org-uuid';
      const dashId = 'dash-uuid';
      const dto: UpdateDashboardDto = {
        name: 'Updated Name',
        widgets: [
          {
            id: 'widget-1',
            widgetType: 'AVERAGE_ADHERENCE_SCORE',
            name: 'Widget One',
            description: 'Desc',
            gridX: 1,
            gridY: 2,
            gridWidth: 3,
            gridHeight: 4,
            visualizationType: 'METRIC',
            filter: { filters: [] },
            isCompareToPreviousPeriodEnabled: false,
            isViewDataLabelsEnabled: false,
          },
        ],
      } as any;

      const expectedResult: ReadDashboardDto = {
        id: dashId,
        name: dto.name!,
        description: null,
        photoUrl: '',
        dashboardFilter: { filters: [] },
        isFavorite: false,
        sharingScope: DashboardSharingScope.PRIVATE,
        dateCreated: '2025-01-01T00:00:00Z',
        lastUpdated: '2025-01-02T00:00:00Z',
        createdBy: { id: 1, displayName: 'You', photoUrl: '' },
        lastModifiedBy: { id: 1, displayName: 'You', photoUrl: '' },
        widgets: [],
      };
      service.update.mockResolvedValueOnce(expectedResult);

      const result = await controller.update(fakeReq, orgId, dashId, dto);

      expect(service.update).toHaveBeenCalledWith(
        { organizationId: orgId, userId: 123 },
        dashId,
        dto,
      );
      expect(result).toBe(expectedResult);
    });

    it('propagates NotFoundException from service', async () => {
      const fakeReq: any = { userId: 5 };
      service.update.mockRejectedValueOnce(new NotFoundException('nope'));

      await expect(
        controller.update(fakeReq, 'org-1', 'dash-1', {} as any),
      ).rejects.toThrow(NotFoundException);
    });

    it('propagates other errors', async () => {
      const fakeReq: any = { userId: 5 };
      service.update.mockRejectedValueOnce(new Error('oops'));

      await expect(
        controller.update(fakeReq, 'org-1', 'dash-1', {} as any),
      ).rejects.toThrow('oops');
    });
  });
});
