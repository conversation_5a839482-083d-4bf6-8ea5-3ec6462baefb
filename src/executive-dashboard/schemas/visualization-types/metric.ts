import { MetricDataResponseDto } from '../../dto/dashboard-data.dto';

export const rowsToMetricDto = (
  rows: {
    label: string;
    value: number;
    previousValue?: number;
    changePercentage?: number;
  }[],
): MetricDataResponseDto => ({
  values: rows.map((r) => ({
    label: r.label,
    value: r.value,
    previousValue: r.previousValue,
    changePercentage: r.changePercentage,
  })),
  unitLabel: '',
});
