import { BarDataResponseDto } from '../../dto/dashboard-data.dto';

export function rowsToBarDto(
  rows: any[],
  categoryField: string,
  metricField: string,
  seriesLabel: string,
  unitLabelValues = '',
): BarDataResponseDto {
  return {
    categories: rows.map((r) => r[categoryField]),
    series: rows.map((r) => ({
      name: r[categoryField],
      data: [r[metricField].value],
      previousPeriodData: [r[metricField].previousValue],
      changePercentageData: [r[metricField].changePercentage],
    })),
    xAxisLabel: categoryField,
    yAxisLabel: seriesLabel,
    unitLabelValues,
  };
}

export function rowsWithColumnsToBarDto(
  rows: any[],
  categoryField: string,
  bucketField: string,
  metricField: string,
  yAxisLabel: string,
  unitLabelValues = '',
): BarDataResponseDto {
  const categories = [
    ...new Set(
      rows.flatMap((r) => r.columns?.map((c: any) => c[bucketField]) || []),
    ),
  ].sort();

  const series = rows.map((r) => {
    const lookup = Object.fromEntries(
      (r.columns || []).map((c: any) => [c[bucketField], c[metricField]]),
    );
    return {
      name: r[categoryField],
      data: categories.map((d) => lookup[d]?.value ?? null),
      previousPeriodData: categories.map(
        (d) => lookup[d]?.previousValue ?? null,
      ),
      changePercentageData: categories.map(
        (d) => lookup[d]?.changePercentage ?? null,
      ),
    };
  });

  return {
    categories,
    series,
    xAxisLabel: 'Date',
    yAxisLabel,
    unitLabelValues,
  };
}
