import {
  TableColumnDto,
  TableDataResponseDto,
} from '../../dto/dashboard-data.dto';

export const rowsToTableDto = (
  rows: Record<string, any>[],
  columns: TableColumnDto[],
): TableDataResponseDto => ({ columns, rows });

export function rowsWithColumnsToTableDto(
  rows: any[],
  categoryField: string,
  bucketField: string,
  metricField: string,
  unitLabelValues = '',
): TableDataResponseDto {
  const buckets = [
    ...new Set(rows.flatMap((r) => r.columns.map((c: any) => c[bucketField]))),
  ].sort();

  const columnsMeta = buckets.map((b) => ({
    key: b,
    label: b,
    type: 'number',
    units: unitLabelValues,
    sortable: true,
    sortActive: false,
  }));

  const tableRows = rows.map((r) => {
    const out: Record<string, any> = { [categoryField]: r[categoryField] };
    r.columns.forEach((c: any) => {
      out[c[bucketField]] = c[metricField].value;
    });
    return out;
  });

  return { columns: columnsMeta, rows: tableRows };
}
