import { Platform } from '../../../constants/scoring-analytics.constants';
import {
  DEFAULT_DATE_PERIOD,
  FilterField,
  VisualizationType,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../../dashboard-types';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';

export const ASSET_OVERVIEW_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.ASSET_OVERVIEW,
  name: 'Asset Overview',
  description: 'Breakdown of media performance and metadata',
  iconUrl: 'https://example.com/icons/asset_overview.svg',
  defaultVisualizationType: VisualizationType.TABLE,
  visualizationTypes: [VisualizationType.TABLE],
  defaultFilter: {
    filters: [
      {
        key: FilterField.MEDIA_CREATE_DATE,
        operator: ReportFilterOperator.After,
        value: DEFAULT_DATE_PERIOD,
      },
      {
        key: FilterField.CHANNEL,
        operator: ReportFilterOperator.In,
        value: [Platform.FACEBOOK],
      },
    ],
  },
  requiredFilters: [
    {
      key: FilterField.DATE,
      type: 'date-range',
      displayLabel: 'Date Range',
    },
    {
      key: FilterField.CHANNEL,
      type: 'single-select',
      displayLabel: 'Channel',
    },
    {
      key: FilterField.KPI,
      displayLabel: 'KPI',
      type: 'single-select',
      dependsOn: [FilterField.CHANNEL],
    },
  ],
  optionalFilters: [
    {
      key: FilterField.ASSET_SOURCE,
      type: 'multi-select',
      displayLabel: 'Asset Source',
    },
    {
      key: FilterField.WORKSPACE,
      type: 'filtered-multi-select',
      displayLabel: 'Workspace',
    },
    {
      key: FilterField.BRAND,
      type: 'multi-select',
      displayLabel: 'Brand',
    },
    {
      key: FilterField.MARKET,
      type: 'multi-select',
      displayLabel: 'Market',
    },
  ],
  workspaceSubscriptionsRequired: [
    WorkspaceSubscription.SCORING,
    WorkspaceSubscription.ANALYTICS,
  ],
};
