import {
  ChangeDirection,
  DEFAULT_DATE_PERIOD,
  FilterField,
  ToggleControl,
  VisualizationType,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../../dashboard-types';
import {
  TableColumnDto,
  TableDataResponseDto,
  WidgetDataRequestDto,
} from '../../dto/dashboard-data.dto';
import { rowsToTableDto } from '../visualization-types/table';
import { z } from 'zod/v4';
import {
  Platform,
  platformDisplayNames,
} from '../../../constants/scoring-analytics.constants';
import { UpdateScoreResponseDto } from '@vidmob/vidmob-soa-scoring-service-sdk';
import CriteriaIdentifierEnum = UpdateScoreResponseDto.CriteriaIdentifierEnum;
import { CriteriaService } from '../../../scoring/criteria/criteria.service';
import {
  AnalyticsKpiResponseDto,
  KPIService,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';
import { DashboardFilterDto } from 'src/executive-dashboard/dto/create-dashboard.dto';
import { BadRequestException } from '@nestjs/common';

export const CRITERIA_PERFORMANCE_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.CRITERIA_PERFORMANCE,
  name: 'Criteria Performance',
  description:
    'Criteria-level KPI and adherence performance by date and filters',
  iconUrl: 'https://example.com/icons/criteria_performance.svg',
  defaultVisualizationType: VisualizationType.TABLE,
  visualizationTypes: [VisualizationType.TABLE],
  defaultFilter: {
    filters: [
      {
        key: FilterField.DATE,
        operator: ReportFilterOperator.After,
        value: DEFAULT_DATE_PERIOD,
      },
      {
        key: FilterField.CHANNEL,
        operator: ReportFilterOperator.In,
        value: [
          {
            id: Platform.FACEBOOK,
            name: platformDisplayNames[Platform.FACEBOOK],
          },
        ],
      },
      {
        key: FilterField.KPI,
        operator: ReportFilterOperator.In,
        value: [],
      },
    ],
  },
  options: {
    [VisualizationType.TABLE]: {
      isKpiLiftAvailable: true,
      groupByDefault: { rows: ['criteria'], columns: [] },
    },
  },
  requiredFilters: [
    {
      key: FilterField.DATE,
      type: 'date-range',
      displayLabel: 'Impressions Date',
    },
    {
      key: FilterField.CHANNEL,
      type: 'single-select',
      displayLabel: 'Channel',
    },
    {
      key: FilterField.KPI,
      type: 'single-select',
      displayLabel: 'KPI',
      dependsOn: [FilterField.CHANNEL],
      control: {
        key: ToggleControl.KpiLift,
        label: 'Show KPI lift',
      },
    },
  ],
  optionalFilters: [
    {
      key: FilterField.WORKSPACE,
      type: 'filtered-multi-select',
      displayLabel: 'Workspace',
    },
    {
      key: FilterField.BRAND,
      type: 'multi-select',
      displayLabel: 'Brand',
    },
    {
      key: FilterField.MARKET,
      type: 'multi-select',
      displayLabel: 'Market',
    },
  ],
  workspaceSubscriptionsRequired: [
    WorkspaceSubscription.SCORING,
    WorkspaceSubscription.ANALYTICS,
  ],
};

/** metric object */
const MetricObject = z.object({
  value: z.number().optional(),
  previousValue: z.number().optional(),
  changePercentage: z.number().optional(),
});

export const CriteriaAttributesSchema = z.object({
  identifier: z.enum(CriteriaIdentifierEnum),
  platform: z.enum(Platform),
  parameters: z.record(z.string(), z.any()),
  name: z.string().optional(),
  isBestPractice: z.boolean().optional(),
});

/** criteria performance row */
export const CriteriaPerformanceDataRow = z.object({
  criteria: CriteriaAttributesSchema,
  averageAdherence: MetricObject.optional(),
  kpiValue: MetricObject.optional(),
  impressions: MetricObject.optional(),
});

export const CriteriaPerformanceDataSchema = z.object({
  widgetType: z.literal(WidgetType.CRITERIA_PERFORMANCE).optional(),
  rows: z.array(CriteriaPerformanceDataRow),
  reportPeriod: z.array(z.string()).optional(),
  previousPeriod: z.array(z.string()).optional(),
  lastRefreshed: z.string(),
  cached: z.boolean().optional(),
});

function getCriteriaPerformanceColumns(
  kpiLabel: string,
  inverseHealth: boolean,
  format: AnalyticsKpiResponseDto.FormatEnum,
): TableColumnDto[] {
  return [
    {
      key: 'criteria',
      label: 'Criteria',
      type: 'object',
      sortable: false,
      sortActive: false,
    },
    {
      key: 'averageAdherence',
      label: 'Adherence',
      type: 'number',
      units: 'percentage',
      sortable: true,
      sortActive: false,
      changeDirection: ChangeDirection.HIGHER_IS_BETTER,
    },
    {
      key: 'kpiValue',
      label: kpiLabel,
      type: 'number',
      units: format?.toLowerCase() ?? 'number',
      sortable: false,
      sortActive: false,
      changeDirection: inverseHealth
        ? ChangeDirection.LOWER_IS_BETTER
        : ChangeDirection.HIGHER_IS_BETTER,
    },
    {
      key: 'impressions',
      label: 'Impressions',
      type: 'number',
      sortable: true,
      sortActive: false,
      changeDirection: ChangeDirection.HIGHER_IS_BETTER,
    },
  ];
}

const idFrom = (filter: DashboardFilterDto | undefined, fk: FilterField) => {
  const v = filter?.filters.find((f) => f.key === fk)?.value?.[0];
  return typeof v === 'object' && v && 'id' in v ? String(v.id) : undefined;
};

export async function finalizeCriteriaPerformanceFilter(
  filter: DashboardFilterDto,
  ctx: {
    kpiService: KPIService;
    organizationId: string;
    body: WidgetDataRequestDto;
  },
) {
  const { kpiService } = ctx;

  const channelId = idFrom(filter, FilterField.CHANNEL);
  const kpiId = idFrom(filter, FilterField.KPI);

  if (!channelId) {
    throw new BadRequestException('Channel is required');
  }

  if (!kpiId) {
    const kpis = await kpiService.getPlatformKpisAsPromise(channelId);
    if (!kpis?.result?.length) {
      throw new BadRequestException('No KPIs found for channel');
    }
    const kpi = kpis.result[0];
    filter.filters.push({
      key: FilterField.KPI,
      operator: ReportFilterOperator.Equals,
      value: [{ id: kpi.id, name: kpi.name }],
    });
  }

  filter.groupBy = filter.groupBy ?? {
    rows: ['criteria'],
    columns: [],
  };

  return filter;
}

export async function enrichCriteriaPerformance(
  raw: any,
  ctx: {
    criteriaService: CriteriaService;
    kpiService: KPIService;
    organizationId: string;
    body: WidgetDataRequestDto;
  },
) {
  const { criteriaService, kpiService, organizationId, body } = ctx;
  /* ids from filters */
  const channelId = idFrom(body.filter, FilterField.CHANNEL);
  const kpiId = idFrom(body.filter, FilterField.KPI);

  /* criteria details, de-duped */
  type Key = string;
  const detailsByKey = new Map<Key, any>();

  await Promise.all(
    raw.rows.map(async (row: any) => {
      const crit = {
        ...row.criteria,
        platform: String(row.criteria.platform).toUpperCase(),
        parameters:
          typeof row.criteria.parameters === 'string'
            ? JSON.parse(row.criteria.parameters)
            : row.criteria.parameters,
      };

      const key: Key = `${crit.identifier}_${crit.platform}_${JSON.stringify(
        crit.parameters,
      )}`;

      if (!detailsByKey.has(key)) {
        const details = await criteriaService.getCriteriaDetailsAsPromise(
          organizationId,
          crit as any,
        );

        detailsByKey.set(key, {
          ...details,
          platform: crit.platform,
          isBestPractice: crit.isBestPractice,
          name: crit.name,
        });
      }
    }),
  );

  /* rebuilt rows */
  const rows = raw.rows.map((row: any) => {
    const crit = {
      ...row.criteria,
      platform: String(row.criteria.platform).toUpperCase(),
      parameters:
        typeof row.criteria.parameters === 'string'
          ? JSON.parse(row.criteria.parameters)
          : row.criteria.parameters,
    };
    const key: Key = `${crit.identifier}_${crit.platform}_${JSON.stringify(
      crit.parameters,
    )}`;
    return { ...row, criteria: detailsByKey.get(key) };
  });

  /* kpi attrs */
  const kpiAttributes =
    channelId && kpiId
      ? (await kpiService.getKpiByIdAsPromise(kpiId, channelId)).result
      : undefined;

  return { ...raw, data: rows, kpiAttributes };
}

export const toTable = (validated: any): TableDataResponseDto => {
  const rows = validated.data;
  const { name, inverseHealth, format } =
    validated.kpiAttributes as AnalyticsKpiResponseDto;
  const columns = getCriteriaPerformanceColumns(name, inverseHealth, format);
  return rowsToTableDto(rows, columns);
};

export const criteriaPerformanceTransforms = {
  [VisualizationType.TABLE]: toTable,
};
