import {
  VisualizationType,
  WidgetType,
  WidgetTypeDefinition,
} from '../../dashboard-types';

export const KEY_FINDINGS_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.KEY_FINDINGS,
  name: 'Key Findings',
  description: 'Rich-text area for notes and commentary around your dashboard',
  iconUrl: 'https://example.com/icons/key_findings.svg',
  defaultVisualizationType: VisualizationType.TEXT,
  visualizationTypes: [VisualizationType.TEXT],
  defaultFilter: {
    filters: [],
  },
  requiredFilters: [],
  optionalFilters: [],
  workspaceSubscriptionsRequired: [],
};
