import {
  ChangeDirection,
  DEFAULT_DATE_PERIOD,
  FilterField,
  VisualizationType,
  WidgetTransformCtx,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../../dashboard-types';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';
import { rowsToMetricDto } from '../visualization-types/metric';
import { z } from 'zod/v4';
import {
  rowsToBarDto,
  rowsWithColumnsToBarDto,
} from '../visualization-types/bar';
import {
  rowsToTableDto,
  rowsWithColumnsToTableDto,
} from '../visualization-types/table';

export const IMPRESSIONS_ANALYZED_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.IMPRESSIONS_ANALYZED,
  name: 'Impressions Analyzed',
  description: 'Total impressions analyzed by channel',
  iconUrl: 'https://example.com/icons/impressions_analyzed.svg',
  defaultVisualizationType: VisualizationType.DONUT,
  visualizationTypes: [
    VisualizationType.BAR,
    VisualizationType.DONUT,
    VisualizationType.LINE,
    VisualizationType.COLUMN,
    VisualizationType.METRIC,
    VisualizationType.TABLE,
  ],
  defaultFilter: {
    filters: [
      {
        key: FilterField.DATE,
        operator: ReportFilterOperator.After,
        value: DEFAULT_DATE_PERIOD, // last 6 months
      },
      {
        key: FilterField.CHANNEL,
        operator: ReportFilterOperator.In,
        value: [],
      },
    ],
  },
  options: {
    [VisualizationType.BAR]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      groupByDefault: { rows: ['channel'], columns: [] },
    },
    [VisualizationType.DONUT]: {
      isCompareToPreviousPeriodAvailable: false,
      groupByDefault: { rows: ['channel'], columns: [] },
    },
    [VisualizationType.LINE]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      isViewByPeriodAvailable: false,
      groupByDefault: { rows: ['channel'], columns: ['date'] },
    },
    [VisualizationType.COLUMN]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      isViewByPeriodAvailable: false,
      groupByDefault: { rows: ['channel'], columns: ['date'] },
    },
    [VisualizationType.METRIC]: {
      isCompareToPreviousPeriodAvailable: false,
      groupByDefault: { rows: ['channel'], columns: [] },
    },
    [VisualizationType.TABLE]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewByPeriodAvailable: false,
      isIncludeTotalAvailable: false,
      includeTotalLabel: 'View impressions total',
      groupByDefault: { rows: ['channel'], columns: ['date'] },
    },
  },
  requiredFilters: [
    {
      key: FilterField.DATE,
      type: 'date-range',
      displayLabel: 'Impressions Date',
    },
    {
      key: FilterField.CHANNEL,
      type: 'multi-select',
      displayLabel: 'Channel',
    },
  ],
  optionalFilters: [
    {
      key: FilterField.WORKSPACE,
      type: 'filtered-multi-select',
      displayLabel: 'Workspace',
    },
    {
      key: FilterField.BRAND,
      type: 'multi-select',
      displayLabel: 'Brand',
    },
    {
      key: FilterField.MARKET,
      type: 'multi-select',
      displayLabel: 'Market',
    },
  ],
  workspaceSubscriptionsRequired: [WorkspaceSubscription.ANALYTICS],
};

const MetricObj = z.object({
  value: z.number(),
  previousValue: z.number().optional(),
  changePercentage: z.number().optional(),
});

const ColumnObj = z.object({
  date: z.string(),
  impressions: MetricObj,
});

const ChannelObj = z.object({
  id: z.string(),
  name: z.string(),
});

const RowObj = z.object({
  channel: ChannelObj.optional(),
  isTotal: z.boolean().optional(),
  impressions: MetricObj,
  columns: z.array(ColumnObj).optional(),
});

export const ImpressionsAnalyzedDataSchema = z.object({
  widgetType: z.literal(WidgetType.IMPRESSIONS_ANALYZED).optional(),
  rows: z.array(RowObj),
  reportPeriod: z.tuple([z.string(), z.string()]).optional(),
  previousPeriod: z.tuple([z.string(), z.string()]).optional(),
  lastRefreshed: z.string().optional(),
  cached: z.boolean().optional(),
});

const labelRows = (
  rows: z.infer<typeof RowObj>[],
): (z.infer<typeof RowObj> & { channelName: string })[] =>
  rows.map((r) => ({
    ...r,
    channelName: r.isTotal ? 'Total' : r.channel?.name ?? '',
  }));

const toBarOrDonut = (
  payload: z.infer<typeof ImpressionsAnalyzedDataSchema>,
  _ctx: WidgetTransformCtx,
) =>
  rowsToBarDto(
    labelRows(payload.rows),
    'channelName',
    'impressions',
    'Impressions',
    'number',
  );

const toLineOrColumn = (
  payload: z.infer<typeof ImpressionsAnalyzedDataSchema>,
  _ctx: WidgetTransformCtx,
) =>
  rowsWithColumnsToBarDto(
    labelRows(payload.rows),
    'channelName',
    'date',
    'impressions',
    'Impressions',
    'number',
  );

const toTable = (payload: z.infer<typeof ImpressionsAnalyzedDataSchema>) => {
  const rows = labelRows(payload.rows);

  const hasColumns = rows.some(
    (r) => Array.isArray(r.columns) && r.columns.length > 0,
  );

  if (hasColumns) {
    const base = rowsWithColumnsToTableDto(
      rows,
      'channelName',
      'date',
      'impressions',
      'number',
    );

    return {
      columns: [
        {
          key: 'channelName',
          label: 'Channel',
          type: 'string',
          sortable: false,
          sortActive: false,
        },
        ...base.columns,
      ],
      rows: base.rows,
    };
  } else {
    // defensive case for when columns are not included
    return rowsToTableDto(
      rows.map((r) => ({
        channelName: r.channelName,
        impressions: r.impressions?.value ?? null,
      })),
      [
        {
          key: 'channelName',
          label: 'Channel',
          type: 'string',
          sortable: false,
          sortActive: false,
        },
        {
          key: 'impressions',
          label: 'Impressions',
          type: 'number',
          sortable: true,
          sortActive: false,
        },
      ],
    );
  }
};

const toMetric = (
  payload: z.infer<typeof ImpressionsAnalyzedDataSchema>,
  _ctx: WidgetTransformCtx,
) => ({
  ...rowsToMetricDto(
    labelRows(payload.rows).map((r) => ({
      label: r.channelName,
      value: r.impressions.value,
      previousValue: r.impressions.previousValue,
      changePercentage: r.impressions.changePercentage,
    })),
  ),
  changeDirection: ChangeDirection.HIGHER_IS_BETTER,
  unitLabel: 'number',
});

export const impressionsAnalyzedTransforms = {
  [VisualizationType.BAR]: toBarOrDonut,
  [VisualizationType.DONUT]: toBarOrDonut,
  [VisualizationType.LINE]: toLineOrColumn,
  [VisualizationType.COLUMN]: toLineOrColumn,
  [VisualizationType.METRIC]: toMetric,
  [VisualizationType.TABLE]: toTable,
};
