import {
  ChangeDirection,
  DEFAULT_DATE_PERIOD,
  FilterField,
  VisualizationType,
  WidgetTransformCtx,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../../dashboard-types';
import { z } from 'zod/v4';
import { TableDataResponseDto } from '../../dto/dashboard-data.dto';
import {
  rowsToBarDto,
  rowsWithColumnsToBarDto,
} from '../visualization-types/bar';
import { rowsToMetricDto } from '../visualization-types/metric';
import { rowsWithColumnsToTableDto } from '../visualization-types/table';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';

export const ADHERENCE_SCORE_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.ADHERENCE_SCORE,
  name: 'Adherence Score',
  description: 'Average adherence % by asset source',
  iconUrl: 'https://example.com/icons/adherence_score.svg',
  defaultVisualizationType: VisualizationType.METRIC,
  visualizationTypes: [
    VisualizationType.BAR,
    VisualizationType.COLUMN,
    VisualizationType.LINE,
    VisualizationType.METRIC,
    VisualizationType.TABLE,
  ],
  defaultFilter: {
    filters: [
      {
        key: FilterField.MEDIA_CREATE_DATE,
        operator: ReportFilterOperator.After,
        value: DEFAULT_DATE_PERIOD,
      },
      {
        key: FilterField.ASSET_SOURCE,
        operator: ReportFilterOperator.In,
        value: [
          {
            id: 'PRE_FLIGHT',
            name: 'Pre-flight',
          },
        ],
      },
    ],
  },
  options: {
    [VisualizationType.BAR]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      groupByDefault: { rows: ['assetSource'], columns: [] },
    },

    [VisualizationType.LINE]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      isViewByPeriodAvailable: false,
      groupByDefault: { rows: ['assetSource'], columns: ['mediaCreateDate'] },
    },

    [VisualizationType.COLUMN]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      isViewByPeriodAvailable: false,
      groupByDefault: { rows: ['assetSource'], columns: ['mediaCreateDate'] },
    },

    [VisualizationType.METRIC]: {
      isCompareToPreviousPeriodAvailable: false,
      isIncludeTotalAvailable: false,
      includeTotalLabel: 'View average metric',
      groupByDefault: { rows: ['assetSource'], columns: [] },
    },

    [VisualizationType.TABLE]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewByPeriodAvailable: false,
      isIncludeTotalAvailable: false,
      includeTotalLabel: 'View average',
      groupByDefault: { rows: ['assetSource'], columns: ['mediaCreateDate'] },
    },
  },
  requiredFilters: [
    {
      key: FilterField.MEDIA_CREATE_DATE,
      type: 'date-range',
      displayLabel: 'Media Create Date',
    },
    {
      key: FilterField.ASSET_SOURCE,
      type: 'multi-select',
      displayLabel: 'Asset Source',
      staticOptions: ['PRE_FLIGHT', 'IN_FLIGHT'],
    },
  ],
  optionalFilters: [
    {
      key: FilterField.WORKSPACE,
      type: 'filtered-multi-select',
      displayLabel: 'Workspace',
    },
    {
      key: FilterField.CHANNEL,
      type: 'multi-select',
      displayLabel: 'Channel',
    },
    {
      key: FilterField.BRAND,
      type: 'multi-select',
      displayLabel: 'Brand',
    },
    {
      key: FilterField.MARKET,
      type: 'multi-select',
      displayLabel: 'Market',
    },
  ],
  workspaceSubscriptionsRequired: [
    WorkspaceSubscription.SCORING,
    WorkspaceSubscription.ANALYTICS,
  ],
};

type AdherenceScorePayload = z.infer<typeof AdherenceScoreDataSchema>;

const MetricObj = z.object({
  value: z.number(),
  previousValue: z.number().optional(),
  changePercentage: z.number().optional(),
});

const AdherenceScoreColumn = z.object({
  mediaCreateDate: z.string(),
  averageAdherence: MetricObj,
});

const AdherenceScoreRow = z.object({
  assetSource: z.string().optional(),
  isTotal: z.boolean().optional(),
  mediaCreateDate: z.string().optional(),
  averageAdherence: MetricObj,
  columns: z.array(AdherenceScoreColumn).optional(),
});

export const AdherenceScoreDataSchema = z.object({
  widgetType: z.literal(WidgetType.ADHERENCE_SCORE).optional(),
  rows: z.array(AdherenceScoreRow),
  reportPeriod: z.tuple([z.string(), z.string()]).optional(),
  previousPeriod: z.tuple([z.string(), z.string()]).optional(),
  lastRefreshed: z.string().optional(),
  cached: z.boolean().optional(),
});

const toBar = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  { isIncludeTotalEnabled }: WidgetTransformCtx,
) => {
  const rows = payload.rows
    .filter((r) => (isIncludeTotalEnabled ? true : !r.isTotal))
    .map((r) => (r.isTotal ? { ...r, assetSource: 'Total' } : r));

  return rowsToBarDto(
    rows,
    'assetSource',
    'averageAdherence',
    'Avg Adherence %',
    'percentage',
  );
};

const toLineOrColumn = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  { isIncludeTotalEnabled }: WidgetTransformCtx,
) =>
  rowsWithColumnsToBarDto(
    payload.rows
      .filter((r) => isIncludeTotalEnabled || !r.isTotal)
      .map((r) => (r.isTotal ? { ...r, assetSource: 'Total' } : r)),
    'assetSource',
    'mediaCreateDate',
    'averageAdherence',
    'Avg Adherence %',
    'percentage',
  );

const toTable = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  { isIncludeTotalEnabled }: WidgetTransformCtx,
): TableDataResponseDto => {
  const base = rowsWithColumnsToTableDto(
    payload.rows
      .filter((r) => isIncludeTotalEnabled || !r.isTotal)
      .map((r) => (r.isTotal ? { ...r, assetSource: 'Total' } : r)),
    'assetSource',
    'mediaCreateDate',
    'averageAdherence',
    'percentage',
  );

  /* prepend the dimension column */
  const dimensionCol = {
    key: 'assetSource',
    label: 'Asset source',
    type: 'string',
    sortable: false,
    sortActive: false,
  };

  return {
    columns: [dimensionCol, ...base.columns],
    rows: base.rows,
  };
};

const toMetric = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  { isIncludeTotalEnabled }: WidgetTransformCtx,
) => ({
  ...rowsToMetricDto(
    payload.rows
      .filter((r) => (isIncludeTotalEnabled ? true : !r.isTotal))
      .map((r) => ({
        label: r.isTotal ? 'Total' : r.assetSource ?? '',
        value: r.averageAdherence.value,
        previousValue: r.averageAdherence.previousValue,
        changePercentage: r.averageAdherence.changePercentage,
      })),
  ),
  changeDirection: ChangeDirection.HIGHER_IS_BETTER,
  unitLabel: 'percentage',
});

export const adherenceScoreTransforms = {
  [VisualizationType.BAR]: toBar,
  [VisualizationType.COLUMN]: toLineOrColumn,
  [VisualizationType.LINE]: toLineOrColumn,
  [VisualizationType.METRIC]: toMetric,
  [VisualizationType.TABLE]: toTable,
};
