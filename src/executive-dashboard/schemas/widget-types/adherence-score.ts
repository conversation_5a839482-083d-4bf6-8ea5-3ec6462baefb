import {
  ChangeDirection,
  DEFAULT_DATE_PERIOD,
  FilterField,
  ViewByPeriods,
  VisualizationType,
  WidgetTransformCtx,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../../dashboard-types';
import { z } from 'zod/v4';
import { TableDataResponseDto } from '../../dto/dashboard-data.dto';
import {
  rowsToBarDto,
  rowsWithColumnsToBarDto,
} from '../visualization-types/bar';
import { rowsToMetricDto } from '../visualization-types/metric';
import { rowsWithColumnsToTableDto } from '../visualization-types/table';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';
import {
  getAvailableViewByPeriods,
  getDurationInDays,
} from '../../constants/dashboard-constants';

export const ADHERENCE_SCORE_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.ADHERENCE_SCORE,
  name: 'Adherence Score',
  description: 'Average adherence % by asset source',
  iconUrl: 'https://example.com/icons/adherence_score.svg',
  defaultVisualizationType: VisualizationType.METRIC,
  visualizationTypes: [
    VisualizationType.BAR,
    VisualizationType.COLUMN,
    VisualizationType.LINE,
    VisualizationType.METRIC,
    VisualizationType.TABLE,
  ],
  defaultFilter: {
    filters: [
      {
        key: FilterField.MEDIA_CREATE_DATE,
        operator: ReportFilterOperator.After,
        value: DEFAULT_DATE_PERIOD,
      },
      {
        key: FilterField.ASSET_SOURCE,
        operator: ReportFilterOperator.In,
        value: [
          {
            id: 'PRE_FLIGHT',
            name: 'Pre-flight',
          },
        ],
      },
    ],
  },
  viewByPeriodOptions: getAvailableViewByPeriods(
    getDurationInDays(DEFAULT_DATE_PERIOD) ?? 180,
  ),
  options: {
    [VisualizationType.BAR]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      groupByDefault: { rows: ['assetSource'], columns: [] },
    },

    [VisualizationType.LINE]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      isViewByPeriodAvailable: false,
      groupByDefault: { rows: ['assetSource'], columns: ['mediaCreateDate'] },
    },

    [VisualizationType.COLUMN]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewDataLabelsAvailable: false,
      isViewByPeriodAvailable: false,
      groupByDefault: { rows: ['assetSource'], columns: ['mediaCreateDate'] },
    },

    [VisualizationType.METRIC]: {
      isCompareToPreviousPeriodAvailable: false,
      isIncludeTotalAvailable: false,
      includeTotalLabel: 'View average metric',
      groupByDefault: { rows: ['assetSource'], columns: [] },
    },

    [VisualizationType.TABLE]: {
      isCompareToPreviousPeriodAvailable: false,
      isViewByPeriodAvailable: false,
      isIncludeTotalAvailable: false,
      includeTotalLabel: 'View average',
      groupByDefault: { rows: ['assetSource'], columns: ['mediaCreateDate'] },
    },
  },
  requiredFilters: [
    {
      key: FilterField.MEDIA_CREATE_DATE,
      type: 'date-range',
      displayLabel: 'Media Create Date',
    },
    {
      key: FilterField.ASSET_SOURCE,
      type: 'multi-select',
      displayLabel: 'Asset Source',
      staticOptions: ['PRE_FLIGHT', 'IN_FLIGHT'],
    },
  ],
  optionalFilters: [
    {
      key: FilterField.WORKSPACE,
      type: 'filtered-multi-select',
      displayLabel: 'Workspace',
    },
    {
      key: FilterField.CHANNEL,
      type: 'multi-select',
      displayLabel: 'Channel',
    },
    {
      key: FilterField.BRAND,
      type: 'multi-select',
      displayLabel: 'Brand',
    },
    {
      key: FilterField.MARKET,
      type: 'multi-select',
      displayLabel: 'Market',
    },
  ],
  workspaceSubscriptionsRequired: [
    WorkspaceSubscription.SCORING,
    WorkspaceSubscription.ANALYTICS,
  ],
};

type AdherenceScorePayload = z.infer<typeof AdherenceScoreDataSchema>;

const MetricObj = z.object({
  value: z.number(),
  previousValue: z.number().optional(),
  changePercentage: z.number().optional(),
});

const AdherenceScoreColumn = z.object({
  mediaCreateDate: z.string(),
  averageAdherence: MetricObj,
});

const AdherenceScoreRow = z.object({
  assetSource: z.string().optional(),
  isTotal: z.boolean().optional(),
  mediaCreateDate: z.string().optional(),
  averageAdherence: MetricObj,
  columns: z.array(AdherenceScoreColumn).optional(),
});

export const AdherenceScoreDataSchema = z.object({
  widgetType: z.literal(WidgetType.ADHERENCE_SCORE).optional(),
  rows: z.array(AdherenceScoreRow),
  reportPeriod: z.tuple([z.string(), z.string()]).optional(),
  previousPeriod: z.tuple([z.string(), z.string()]).optional(),
  lastRefreshed: z.string().optional(),
  cached: z.boolean().optional(),
});

const toBar = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  { isIncludeTotalEnabled }: WidgetTransformCtx,
) => {
  const rows = payload.rows
    .filter((r) => (isIncludeTotalEnabled ? true : !r.isTotal))
    .map((r) => (r.isTotal ? { ...r, assetSource: 'Total' } : r));

  return rowsToBarDto(
    rows,
    'assetSource',
    'averageAdherence',
    'Avg Adherence %',
    'percentage',
  );
};

const toLineOrColumn = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  {
    isIncludeTotalEnabled,
    viewByPeriod = ViewByPeriods.MONTH,
  }: WidgetTransformCtx,
) =>
  rowsWithColumnsToBarDto(
    fillMissingBuckets(
      payload.rows
        .filter((r) => isIncludeTotalEnabled || !r.isTotal)
        .map((r) => (r.isTotal ? { ...r, assetSource: 'Total' } : r)),
      'mediaCreateDate',
      'averageAdherence',
      payload.reportPeriod,
      viewByPeriod as ViewByPeriods,
    ),
    'assetSource',
    'mediaCreateDate',
    'averageAdherence',
    'Avg Adherence %',
    'percentage',
  );

const toTable = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  {
    isIncludeTotalEnabled,
    viewByPeriod = ViewByPeriods.MONTH,
  }: WidgetTransformCtx,
): TableDataResponseDto => {
  const filledRows = fillMissingBuckets(
    payload.rows
      .filter((r) => isIncludeTotalEnabled || !r.isTotal)
      .map((r) => (r.isTotal ? { ...r, assetSource: 'Total' } : r)),
    'mediaCreateDate',
    'averageAdherence',
    payload.reportPeriod,
    viewByPeriod as ViewByPeriods,
  );

  const base = rowsWithColumnsToTableDto(
    filledRows,
    'assetSource',
    'mediaCreateDate',
    'averageAdherence',
    'percentage',
  );

  return {
    ...base,
    columns: [
      {
        key: 'assetSource',
        label: 'Asset source',
        type: 'string',
        sortable: false,
        sortActive: false,
      },
      ...base.columns,
    ],
  };
};

const toMetric = (
  payload: z.infer<typeof AdherenceScoreDataSchema>,
  { isIncludeTotalEnabled }: WidgetTransformCtx,
) => ({
  ...rowsToMetricDto(
    payload.rows
      .filter((r) => (isIncludeTotalEnabled ? true : !r.isTotal))
      .map((r) => ({
        label: r.isTotal ? 'Total' : r.assetSource ?? '',
        value: r.averageAdherence.value,
        previousValue: r.averageAdherence.previousValue,
        changePercentage: r.averageAdherence.changePercentage,
      })),
  ),
  changeDirection: ChangeDirection.HIGHER_IS_BETTER,
  unitLabel: 'percentage',
});

export const adherenceScoreTransforms = {
  [VisualizationType.BAR]: toBar,
  [VisualizationType.COLUMN]: toLineOrColumn,
  [VisualizationType.LINE]: toLineOrColumn,
  [VisualizationType.METRIC]: toMetric,
  [VisualizationType.TABLE]: toTable,
};

export const fillMissingBuckets = (
  rows: any[],
  bucketField: string, // e.g. 'mediaCreateDate'
  metricField: string, // e.g. 'averageAdherence'
  reportPeriod: [string, string] | undefined,
  viewBy: ViewByPeriods = ViewByPeriods.MONTH,
) => {
  if (!reportPeriod) return rows;

  const [start, end] = reportPeriod.map((d) => new Date(d + 'T00:00:00Z'));

  /* ─── build the full bucket list ─────────────────────────── */
  const buckets: string[] = [];
  const current = new Date(start);

  const advance = {
    [ViewByPeriods.DAY]: (d: Date) => d.setUTCDate(d.getUTCDate() + 1),
    [ViewByPeriods.WEEK]: (d: Date) => d.setUTCDate(d.getUTCDate() + 7),
    [ViewByPeriods.MONTH]: (d: Date) => d.setUTCMonth(d.getUTCMonth() + 1),
    [ViewByPeriods.QUARTER]: (d: Date) => d.setUTCMonth(d.getUTCMonth() + 3),
    [ViewByPeriods.YEAR]: (d: Date) => d.setUTCFullYear(d.getUTCFullYear() + 1),
  }[viewBy];

  while (current <= end) {
    buckets.push(current.toISOString().slice(0, 10)); // 'YYYY-MM-DD'
    advance(current);
  }

  /* ─── pad every row so it contains every bucket ───────────── */
  return rows.map((r) => {
    const existing = new Map(r.columns.map((c: any) => [c[bucketField], c]));

    const paddedColumns = buckets.map(
      (b) =>
        existing.get(b) ?? { [bucketField]: b, [metricField]: { value: null } },
    );

    return { ...r, columns: paddedColumns };
  });
};
