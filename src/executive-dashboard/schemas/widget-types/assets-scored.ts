import {
  DEFAULT_DATE_PERIOD,
  FilterField,
  VisualizationType,
  WidgetType,
  WidgetTypeDefinition,
  WorkspaceSubscription,
} from '../../dashboard-types';
import { z } from 'zod/v4';
import { rowsToBarDto } from '../visualization-types/bar';
import { rowsToMetricDto } from '../visualization-types/metric';
import { ReportFilterOperator } from '../../../reports/model/report-filters.dto';
import { BatchType } from '../../../scoring/reports/constants/constants';

export const ASSETS_SCORED_WIDGET: WidgetTypeDefinition = {
  widgetType: WidgetType.ASSETS_SCORED,
  name: 'Assets Scored',
  description: 'Total number of assets scored',
  iconUrl: 'https://example.com/icons/assets_scored.svg',
  defaultVisualizationType: VisualizationType.BAR,
  visualizationTypes: [
    VisualizationType.METRIC,
    VisualizationType.BAR,
    VisualizationType.COLUMN,
    VisualizationType.DONUT,
    VisualizationType.TABLE,
    VisualizationType.LINE,
  ],
  defaultFilter: {
    filters: [
      {
        key: FilterField.MEDIA_CREATE_DATE,
        operator: ReportFilterOperator.After,
        value: DEFAULT_DATE_PERIOD,
      },
      {
        key: FilterField.ASSET_SOURCE,
        operator: ReportFilterOperator.In,
        value: [BatchType.InFlight, BatchType.PreFlight],
      },
    ],
  },
  requiredFilters: [
    {
      key: FilterField.MEDIA_CREATE_DATE,
      type: 'date-range',
      displayLabel: 'Media Create Date',
    },
    {
      key: FilterField.ASSET_SOURCE,
      type: 'multi-select',
      displayLabel: 'Asset Source',
    },
  ],
  optionalFilters: [
    {
      key: FilterField.WORKSPACE,
      type: 'filtered-multi-select',
      displayLabel: 'Workspace',
    },
    {
      key: FilterField.CHANNEL,
      type: 'multi-select',
      displayLabel: 'Channel',
    },
    {
      key: FilterField.BRAND,
      type: 'multi-select',
      displayLabel: 'Brand',
    },
    {
      key: FilterField.MARKET,
      type: 'multi-select',
      displayLabel: 'Market',
    },
  ],
  workspaceSubscriptionsRequired: [WorkspaceSubscription.SCORING],
};

export const AssetsScoredDataSchema = z.object({
  widgetType: z.literal(WidgetType.ASSETS_SCORED).optional(),
  data: z.array(
    z.object({
      assetSource: z.string(),
      assets: z.object({
        value: z.number(),
        previousValue: z.number().optional(),
        changePercentage: z.number().optional(),
      }),
    }),
  ),
  lastRefreshed: z.string().optional(),
  cached: z.boolean().optional(),
});

export const assetsScoredTransforms = {
  [VisualizationType.BAR]: (validated: any) =>
    rowsToBarDto(validated.data, 'assetSource', 'assets', 'Assets'),

  [VisualizationType.METRIC]: (validated: any) =>
    rowsToMetricDto(
      validated.data.map((r: any) => ({
        label: r.assetSource,
        value: r.assets.value,
        previousValue: r.assets.previousValue,
        changePercentage: r.assets.changePercentage,
      })),
    ),
};
