import { Test, TestingModule } from '@nestjs/testing';
import { WidgetController } from './widget.controller';
import { WidgetService } from './widget.service';
import { ReadWidgetDto } from '../dto/read-widget.dto';
import { CreateWidgetDto } from '../dto/create-widget.dto';
import { UpdateWidgetDto } from '../dto/update-widget.dto';

/* ─────────── explicit jest.Mock instances ─────────── */
const createWidgetMock = jest.fn() as jest.Mock<
  Promise<ReadWidgetDto>,
  [string, CreateWidgetDto, number]
>;
const updateWidgetMock = jest.fn() as jest.Mock<
  Promise<ReadWidgetDto>,
  [string, UpdateWidgetDto, number]
>;
const deleteWidgetMock = jest.fn() as jest.Mock<
  Promise<void>,
  [string, number]
>;

const serviceMock = {
  createWidget: createWidgetMock,
  updateWidget: updateWidgetMock,
  deleteWidget: deleteWidgetMock,
} as unknown as WidgetService;

describe('WidgetController – widget endpoints', () => {
  let controller: WidgetController;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WidgetController],
      providers: [{ provide: WidgetService, useValue: serviceMock }],
    }).compile();

    controller = module.get(WidgetController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  /* ------------------- createWidget ------------------- */
  it('delegates to service.createWidget()', async () => {
    const dashboardId = 'dash-1';
    const orgId = '11111111-1111-1111-1111-111111111111';
    const req = { userId: 7 } as any;

    const dto: CreateWidgetDto = {
      widgetType: 'BAR',
      name: 'My Widget',
      visualizationType: 'bar',
      parameters: {},
      filter: { channels: ['FB'] },
    } as any;

    const expected: ReadWidgetDto = { id: 'widget-1' } as any;
    createWidgetMock.mockResolvedValue(expected);

    const result = await controller.createWidget(dashboardId, orgId, dto, req);

    expect(result).toBe(expected);
    expect(createWidgetMock).toHaveBeenCalledWith(dashboardId, dto, 7, orgId);
  });

  /* ------------------- updateWidget ------------------- */
  it('delegates to service.updateWidget()', async () => {
    const widgetId = 'widget-1';
    const orgId = '11111111-1111-1111-1111-111111111111';
    const req = { userId: 99 } as any;

    const dto: UpdateWidgetDto = { name: 'Updated' } as any;
    const expected: ReadWidgetDto = { id: widgetId, name: 'Updated' } as any;
    updateWidgetMock.mockResolvedValue(expected);

    const result = await controller.updateWidget(widgetId, orgId, dto, req);

    expect(result).toBe(expected);
    expect(updateWidgetMock).toHaveBeenCalledWith(widgetId, dto, 99, orgId);
  });

  /* ------------------- deleteWidget ------------------- */
  it('delegates to service.deleteWidget()', async () => {
    const widgetId = 'widget-1';
    const orgId = '11111111-1111-1111-1111-111111111111';
    const req = { userId: 123 } as any;

    deleteWidgetMock.mockResolvedValue(undefined);

    await controller.removeWidget(widgetId, orgId, req);

    expect(deleteWidgetMock).toHaveBeenCalledWith(widgetId, 123, orgId);
  });
});
