import {
  Body,
  Controller,
  Delete,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Get,
  ValidationPipe,
  Request,
  Query,
} from '@nestjs/common';
import { ApiOkResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { CreateWidgetDto } from '../dto/create-widget.dto';
import { ReadWidgetDto } from '../dto/read-widget.dto';
import { UpdateWidgetDto } from '../dto/update-widget.dto';
import { WidgetService } from './widget.service';
import { FilterField, WidgetType } from '../dashboard-types';
import { GetPagination, PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { DashboardFilterDto } from '../dto/create-dashboard.dto';
import { readOrganizationDashboard } from '../../analytics/analytics.permissions';
import { Permissions } from '../../auth/decorators/permission.decorator';
import {
  WidgetDataRequestDto,
  WidgetDataResponseDto,
} from '../dto/dashboard-data.dto';

@Permissions(readOrganizationDashboard)
@ApiTags('Executive Dashboard - Widget')
@ApiSecurity('Bearer Token')
@Controller('executive-dashboard/organization/:organizationId/')
export class WidgetController {
  constructor(private readonly widgetService: WidgetService) {}

  @Post('dashboard/:dashboardId/widget')
  async createWidget(
    @Param('dashboardId') dashboardId: string,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Body(new ValidationPipe()) dto: CreateWidgetDto,
    @Request() req: any,
  ): Promise<ReadWidgetDto> {
    const userId = req['userId'] as number;
    return this.widgetService.createWidget(
      dashboardId,
      dto,
      userId,
      organizationId,
    );
  }

  @Patch('widget/:widgetId')
  async updateWidget(
    @Param('widgetId') widgetId: string,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Body(new ValidationPipe()) dto: UpdateWidgetDto,
    @Request() req: any,
  ): Promise<ReadWidgetDto> {
    const userId = req['userId'] as number;
    return this.widgetService.updateWidget(
      widgetId,
      dto,
      userId,
      organizationId,
    );
  }

  @Delete('widget/:widgetId')
  async removeWidget(
    @Param('widgetId', new ParseUUIDPipe()) widgetId: string,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Request() req: any,
  ): Promise<void> {
    const userId = req['userId'] as number;
    return this.widgetService.deleteWidget(widgetId, userId, organizationId);
  }

  @Get('widget/type/:typeId')
  async readWidgetType(
    @Request() req: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('typeId') typeId: WidgetType,
    @GetPagination() pagination: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    return this.widgetService.getWidgetType(
      organizationId,
      typeId,
      authorization,
      userId,
      pagination,
    );
  }

  @Post('widget/type/:typeId')
  async readWidgetTypePost(
    @Request() req: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('typeId') typeId: WidgetType,
    @Body(new ValidationPipe()) dashboardFilter: DashboardFilterDto,
    @GetPagination() pagination: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    return this.widgetService.getWidgetType(
      organizationId,
      typeId,
      authorization,
      userId,
      pagination,
      '',
      dashboardFilter,
    );
  }

  @Post('widget/type/:widgetType/filter/:fieldName')
  async listFilterValues(
    @Request() req: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Param('widgetType') widgetType: WidgetType,
    @Param('fieldName') fieldName: FilterField,
    @Body(new ValidationPipe()) dashboardFilter: DashboardFilterDto,
    @GetPagination() pagination: PaginationOptions,
    @Query('search') search?: string,
  ) {
    const userId = req.userId as number;
    const auth = req.headers.authorization as string;
    return this.widgetService.listFilterValues(
      organizationId,
      widgetType,
      fieldName,
      dashboardFilter,
      userId,
      auth,
      pagination,
      search,
    );
  }

  @Get('widget/type')
  async listWidgetTypes(
    @Request() req: any,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @GetPagination() pagination: PaginationOptions,
  ) {
    const { userId } = req;
    const authorization = req.headers.authorization;

    return this.widgetService.listWidgetTypes(
      organizationId,
      authorization,
      userId,
      pagination,
    );
  }

  // Data preview (unsaved widget)
  @Post('widget/data')
  previewWidgetData(
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Body() body: WidgetDataRequestDto,
    @Request() req: any,
  ) {
    const userId = req['userId'] as number;
    return this.widgetService.previewWidgetData(organizationId, body, userId);
  }

  @Post('widget/:widgetId/data')
  @ApiOkResponse({ type: WidgetDataRequestDto })
  widgetData(
    @Param('widgetId') widgetId: string,
    @Param('organizationId', new ParseUUIDPipe()) organizationId: string,
    @Body() body: WidgetDataRequestDto,
    @Request() req: any,
  ): Promise<WidgetDataResponseDto> {
    const userId = req['userId'] as number;
    return this.widgetService.savedWidgetData(
      widgetId,
      organizationId,
      body,
      userId,
    );
  }
}
