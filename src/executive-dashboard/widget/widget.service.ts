import {
  BadRequestException,
  ForbiddenException,
  HttpException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { format, parseISO } from 'date-fns';
import { ReadWidgetDto } from '../dto/read-widget.dto';
import { UpdateWidgetDto } from '../dto/update-widget.dto';
import { CreateWidgetDto } from '../dto/create-widget.dto';
import { ReportFilter } from '../../entities/report-filter.entity';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { DashboardWidget } from '../../entities/dashboard-widget.entity';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { AnalyticsReportsService } from '../../analytics/saved-report/analytics-reports.service';
import { Dashboard } from '../../entities/dashboard.entity';
import { Person } from '../../entities/person.entity';
import { DashboardFilterDto } from '../dto/create-dashboard.dto';
import { DashboardFilterDto as SdkFilter } from '@vidmob/vidmob-dashboard-service-sdk';

import { randomUUID } from 'crypto';
import {
  MAX_WIDGETS_PER_DASHBOARD,
  WIDGET_TYPE_DEFINITIONS,
  getWidgetTypeDefinition,
  CURRENT_DASHBOARD_FILTER_VERSION,
  widgetDataRegistry,
  dashboardSubscriptionTypes,
} from '../constants/dashboard-constants';
import {
  FilterField,
  FilterOptions,
  VisualizationType,
  WidgetType,
  WidgetTypeDefinition,
  WidgetTypeDefinitionWithValues,
  WorkspaceSubscription,
} from '../dashboard-types';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import { BrandService } from '../../account-management/organization/brand/services/brand.service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import {
  assetSourceDisplayNames,
  Platform,
  platformDisplayNames,
} from '../../constants/scoring-analytics.constants';
import { AdAccountService } from '../../account-management/organization/ad-account/services/ad-account.service';
import { KPIService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { DashboardFilterValidator } from '../utils/dashboard-filter-validator';
import { WidgetQueryService } from '@vidmob/vidmob-dashboard-service-sdk';
import {
  WidgetDataRequestDto,
  WidgetDataResponseDto,
} from '../dto/dashboard-data.dto';
import { WidgetDataMapper } from '../utils/widget-data-mapper';
import { CriteriaService } from '../../scoring/criteria/criteria.service';
import { BatchType } from '../../scoring/reports/constants/constants';
import { mergeDashboardFiltersStrict } from '../utils/filter-merger';
import { mapFilter } from '../dashboard/mapper/dashboard.profile';
import { ReportFilterValue } from '../../reports/model/report-filters.dto';
import { WorkspaceScopeUtil } from '../workspace-scope-util';
import { UserDetailsDto } from '../../analytics/dto/user-details.dto';
import {
  FilterType,
  GetAnalyticsReportFilterOptionsRequestDto,
} from '../../analytics/saved-report/dto/get-analytics-filter-values.dto';

@Injectable()
export class WidgetService {
  constructor(
    @InjectRepository(Dashboard) private dashRepo: Repository<Dashboard>,
    @InjectRepository(DashboardWidget)
    private widgetRepo: Repository<DashboardWidget>,
    private adAccountService: AdAccountService,
    private brandService: BrandService,
    private workspaceService: WorkspaceService,
    private kpiService: KPIService,
    private widgetQueryService: WidgetQueryService,
    private criteriaService: CriteriaService,
    private analyticsReportService: AnalyticsReportsService,

    @InjectRepository(ReportFilter) private filtRepo: Repository<ReportFilter>,
    @InjectDataSource()
    private readonly datasource: DataSource,
  ) {}

  private async withTransaction(
    task: (qr: QueryRunner) => Promise<void>,
    fallbackMsg: string,
  ) {
    const qr = this.datasource.createQueryRunner();
    await qr.connect();
    await qr.startTransaction();

    try {
      await task(qr);
      await qr.commitTransaction();
    } catch (err) {
      await qr.rollbackTransaction();

      if (err instanceof HttpException) throw err;
      throw new InternalServerErrorException(fallbackMsg);
    } finally {
      await qr.release();
    }
  }

  private async assertNoOverlapAndLimit(
    qr: QueryRunner,
    dashboardId: string,
    x?: number,
    y?: number,
    w?: number,
    h?: number,
    ignoreId?: string,
  ) {
    /* ── max-16 check ── */
    const widgetCount = await qr.manager.count(DashboardWidget, {
      where: { dashboardId, isDeleted: false },
    });
    if (widgetCount >= MAX_WIDGETS_PER_DASHBOARD && !ignoreId) {
      throw new BadRequestException('dashboard widget limit (16) exceeded');
    }

    // exit if any coordinate is undefined
    if (
      x === undefined ||
      y === undefined ||
      w === undefined ||
      h === undefined
    ) {
      return;
    }

    const overlaps = await qr.manager
      .createQueryBuilder(DashboardWidget, 'w')
      .where('w.dashboard_id = :dashboardId', { dashboardId })
      .andWhere('w.is_deleted = false')
      .andWhere(ignoreId ? 'w.id <> :ignoreId' : '1=1', { ignoreId })

      .andWhere(
        `w.grid_x < :right
       AND :left < w.grid_x + w.grid_width
       AND w.grid_y < :bottom
       AND :top < w.grid_y + w.grid_height`,
        {
          left: x,
          right: x + w,
          top: y,
          bottom: y + h,
        },
      )
      .getCount();

    if (overlaps) {
      throw new BadRequestException('widget grid overlaps another widget');
    }
  }

  async createWidget(
    dashboardId: string,
    dto: CreateWidgetDto,
    personId: number,
    organizationId: string,
  ): Promise<ReadWidgetDto> {
    if (dto.filter) {
      DashboardFilterValidator.parse(dto.filter);
    }
    const dash = await this.dashRepo.findOne({
      where: { id: dashboardId, isDeleted: false },
      select: ['id', 'organizationId', 'createdBy'],
    });
    if (!dash) throw new NotFoundException('dashboard not found');
    if (dash.organizationId !== organizationId || dash.createdBy !== personId) {
      throw new ForbiddenException('not allowed to modify this dashboard');
    }

    let result!: ReadWidgetDto;
    await this.withTransaction(async (qr: QueryRunner) => {
      result = await this.createWidgetWithRunner(qr, dash, dto, personId);
    }, 'failed to create widget');
    return result;
  }

  async updateWidget(
    widgetId: string,
    dto: UpdateWidgetDto,
    personId: number,
    organizationId: string,
  ): Promise<ReadWidgetDto> {
    let result!: ReadWidgetDto;
    await this.withTransaction(async (qr: QueryRunner) => {
      result = await this.updateWidgetWithRunner(
        qr,
        widgetId,
        dto,
        personId,
        organizationId,
      );
    }, 'failed to update widget');
    return result;
  }

  public async createWidgetWithRunner(
    qr: QueryRunner,
    dash: { id: string; organizationId: string },
    dto: CreateWidgetDto,
    personId: number,
  ): Promise<ReadWidgetDto> {
    // collision + limit check
    await this.assertNoOverlapAndLimit(
      qr,
      dash.id,
      dto.gridX,
      dto.gridY,
      dto.gridWidth,
      dto.gridHeight,
    );

    // optional filter
    let filterId: string | undefined;
    if (dto.filter) {
      const validated = DashboardFilterValidator.parse(dto.filter);
      const saved = await qr.manager.save(
        this.filtRepo.create({
          id: randomUUID(),
          owner: { id: personId } as Person,
          lastUpdatedByUser: { id: personId } as Person,
          filtersVersion: CURRENT_DASHBOARD_FILTER_VERSION,
          filters: JSON.stringify(validated.filters),
          sortBy: validated.sortBy
            ? JSON.stringify(validated.sortBy)
            : undefined,
          groupBy: validated.groupBy
            ? JSON.stringify(validated.groupBy)
            : undefined,
        }),
      );
      filterId = saved.id;
    }

    // widget insert
    const { filter, ...widgetData } = dto;
    const widget = this.widgetRepo.create({
      id: randomUUID(),
      dashboardId: dash.id,
      organizationId: dash.organizationId,
      createdById: personId,
      lastModifiedById: personId,
      filterId,
      ...widgetData,
    });
    await qr.manager.save(widget);

    // re-fetch with relations
    const full = await qr.manager.findOne(DashboardWidget, {
      where: { id: widget.id },
      relations: ['createdBy', 'lastModifiedBy'],
    });

    return this.toDto(full!, dto.filter);
  }

  public async updateWidgetWithRunner(
    qr: QueryRunner,
    widgetId: string,
    dto: UpdateWidgetDto,
    personId: number,
    organizationId: string,
  ): Promise<ReadWidgetDto> {
    // fetch + permission guard
    const widget = await qr.manager.findOne(DashboardWidget, {
      where: { id: widgetId, isDeleted: false },
      relations: ['dashboard'],
    });
    if (!widget) throw new NotFoundException('widget not found');
    if (
      widget.dashboard.organizationId !== organizationId ||
      widget.createdById !== personId
    ) {
      throw new ForbiddenException('not allowed to modify this widget');
    }

    // apply scalar fields
    const { filter, ...fields } = dto;
    Object.assign(widget, fields);

    await this.upsertWidgetFilter(qr, widget, filter, personId);

    // re-check overlap if layout changed
    if (
      dto.gridX !== undefined ||
      dto.gridY !== undefined ||
      dto.gridWidth !== undefined ||
      dto.gridHeight !== undefined
    ) {
      await this.assertNoOverlapAndLimit(
        qr,
        widget.dashboardId,
        widget.gridX,
        widget.gridY,
        widget.gridWidth,
        widget.gridHeight,
        widget.id,
      );
    }

    widget.lastModifiedById = personId;
    const saved = await qr.manager.save(widget);
    return this.toDto(saved, dto.filter);
  }

  private async upsertWidgetFilter(
    qr: QueryRunner,
    widget: DashboardWidget,
    filterDto: DashboardFilterDto | undefined,
    personId: number,
  ): Promise<void> {
    if (!filterDto?.filters?.length) return;

    const { filters, sortBy, groupBy } =
      DashboardFilterValidator.parse(filterDto);

    const payload: Partial<ReportFilter> = {
      filters: JSON.stringify(filters),
      sortBy: sortBy ? JSON.stringify(sortBy) : undefined,
      groupBy: groupBy ? JSON.stringify(groupBy) : undefined,
    };

    if (widget.filterId) {
      await qr.manager.update(ReportFilter, widget.filterId, payload);
    } else {
      const newFilter = this.filtRepo.create({
        id: randomUUID(),
        owner: { id: personId } as Person,
        lastUpdatedByUser: { id: personId } as Person,
        filtersVersion: CURRENT_DASHBOARD_FILTER_VERSION,
        ...payload,
        deleted: false,
      });
      const saved = await qr.manager.save(newFilter);
      widget.filterId = saved.id;
    }
  }

  async deleteWidget(
    widgetId: string,
    personId: number,
    organizationId: string,
  ): Promise<void> {
    const widget = await this.widgetRepo.findOne({
      where: { id: widgetId, isDeleted: false },
      relations: ['dashboard'],
    });
    if (!widget) throw new NotFoundException('widget not found');
    if (
      widget.dashboard.organizationId !== organizationId ||
      widget.createdById !== personId
    ) {
      throw new ForbiddenException('not allowed to delete this widget');
    }

    widget.isDeleted = true;
    widget.deleteDate = new Date();
    widget.lastModifiedById = personId;
    await this.widgetRepo.save(widget);
  }

  /**
   * Returns a widget-type definition plus first-page filter values.
   *
   * @param organizationId  org id from route
   * @param typeId          widgetType string
   * @param userId          current user id (for workspace ACLs)
   * @param authorization   bearer token (propagated to downstream svc)
   * @param pagination      { offset, perPage } – applied to every list call
   * @param search          optional search term for filter values
   * @param filters
   */
  async getWidgetType(
    organizationId: string,
    typeId: WidgetType,
    authorization: string,
    userId: number,
    pagination: PaginationOptions,
    search?: string,
    filters?: DashboardFilterDto,
  ): Promise<WidgetTypeDefinitionWithValues> {
    const def = getWidgetTypeDefinition(typeId, filters);

    const filterValues: Partial<Record<FilterField, FilterOptions>> = {};
    for (const req of def.requiredFilters) {
      filterValues[req.key] = await this.buildSingleFilterValue(
        def,
        req.key,
        organizationId,
        userId,
        authorization,
        pagination,
        search,
      );
    }

    // copy defaults and if a default filter’s value array is empty, drop in a live option
    const resolvedDefaults = (def.defaultFilter?.filters ?? []).map((f) => {
      if (Array.isArray(f.value) && f.value.length === 0) {
        const first = filterValues[f.key as FilterField]?.values?.[0];
        if (first) {
          return { ...f, value: [first] as ReportFilterValue };
        }
      }
      return f;
    });

    return {
      ...def,
      defaultFilter: {
        ...(def.defaultFilter ?? {}),
        filters: resolvedDefaults,
      },
      filterValues,
    };
  }

  public async fetchWidgetData({
    widgetId,
    organizationId,
    widgetType,
    visualizationType,
    body,
  }: {
    widgetId: string;
    organizationId: string;
    widgetType: WidgetType;
    visualizationType: VisualizationType;
    body: WidgetDataRequestDto;
  }): Promise<WidgetDataResponseDto> {
    /* ---------------- validation ---------------- */
    if (!body.filter) {
      throw new BadRequestException(
        'filters are required for widget data request',
      );
    }
    DashboardFilterValidator.parse(body.filter);

    /* ---------------- finalize filter ------------ */
    const finalizeFilter = widgetDataRegistry[widgetType]?.finalizeFilter;
    let filterBody = body.filter;
    if (finalizeFilter) {
      filterBody = await finalizeFilter(filterBody, {
        kpiService: this.kpiService,
        organizationId,
        body,
      });
    }

    /* ---------------- widget definition ---------- */
    const widgetDefinition = getWidgetTypeDefinition(widgetType, filterBody);

    const {
      groupByDefault: groupBy,
      isCompareToPreviousPeriodAvailable: defaultCompare = false,
      isIncludeTotalAvailable: defaultInclude = false,
      isKpiLiftAvailable: defaultKpiLift = false,
    } = widgetDefinition.options?.[visualizationType] ?? {};

    const compareToPrev =
      body?.isCompareToPreviousPeriodEnabled ?? defaultCompare;
    const includeTotal = body?.isIncludeTotalEnabled ?? defaultInclude;
    const kpiLift = body?.isKpiLiftEnabled ?? defaultKpiLift;
    const availableViewBy = widgetDefinition.viewByPeriodOptions ?? [];
    const viewByPeriod = body?.viewByPeriod ?? availableViewBy[0] ?? undefined;

    /* ---------------- build request --------------- */
    const reqBody = {
      filter: {
        ...filterBody,
        ...(groupBy && { groupBy }),
      } as SdkFilter,
      isCompareToPreviousPeriodEnabled: compareToPrev,
      isIncludeTotalEnabled: includeTotal,
      isKpiLiftEnabled: kpiLift,
      viewByPeriod,
      refresh: false,
    };

    let sdkResp;
    try {
      sdkResp = await this.widgetQueryService.queryAsPromise(
        organizationId,
        widgetType,
        reqBody,
      );
    } catch (err) {
      throw new InternalServerErrorException(
        'Failed to fetch widget data from dashboard service',
      );
    }

    const mapper = new WidgetDataMapper(widgetType, visualizationType);
    const data = await mapper.map(sdkResp.result, {
      criteriaService: this.criteriaService,
      kpiService: this.kpiService,
      organizationId,
      body: reqBody,
      isCompareToPreviousPeriodEnabled: compareToPrev,
      isIncludeTotalEnabled: includeTotal,
      viewByPeriod,
    });

    return {
      widgetId,
      widgetType,
      visualizationType,
      data,
      reportPeriodLabels: this.formatReportPeriod(sdkResp.result.reportPeriod),
      lastRefreshed: sdkResp.result.lastRefreshed,
    };
  }

  private formatReportPeriod(reportPeriod?: string[]): string[] | undefined {
    if (!reportPeriod || reportPeriod.length === 0) return undefined;
    return reportPeriod.map((period) => {
      try {
        const date = parseISO(period);
        return format(date, 'MMM d yyyy');
      } catch (error) {
        // If parsing fails, return the original string
        return period;
      }
    });
  }

  async previewWidgetData(
    orgId: string,
    body: WidgetDataRequestDto,
    userId: number,
  ) {
    const { widgetType, visualizationType } = this.resolveTypes(body);

    const scopeUtil = new WorkspaceScopeUtil(this.workspaceService);
    body.filter = await this.buildFilter(
      [
        getWidgetTypeDefinition(widgetType).defaultFilter,
        body.dashboardFilter,
        body.filter,
      ],
      orgId,
      userId, // viewer acts as “owner” for preview
      scopeUtil,
    );

    return this.fetchWidgetData({
      widgetId: 'preview',
      organizationId: orgId,
      widgetType,
      visualizationType,
      body,
    });
  }

  async savedWidgetData(
    widgetId: string,
    organizationId: string,
    body: WidgetDataRequestDto,
    viewerUserId: number,
  ): Promise<WidgetDataResponseDto> {
    const widget = await this.widgetRepo.findOne({
      where: { id: widgetId, isDeleted: false },
      relations: ['filter', 'dashboard'],
    });
    if (!widget) throw new NotFoundException(`Widget ${widgetId} not found`);

    const scopeUtil = new WorkspaceScopeUtil(this.workspaceService);

    const widgetType = body.widgetType ?? widget.widgetType;
    const visualizationType =
      body.visualizationType ??
      widget.visualizationType ??
      getWidgetTypeDefinition(widgetType).defaultVisualizationType;

    body.filter = await this.buildFilter(
      [
        mapFilter(widget.dashboard.dashboardFilter),
        mapFilter(widget.filter),
        body.filter,
      ],
      organizationId,
      widget.dashboard.createdBy,
      scopeUtil,
    );

    await this.verifyAccess(
      scopeUtil,
      organizationId,
      viewerUserId,
      body.filter,
    );

    return this.fetchWidgetData({
      widgetId,
      organizationId,
      widgetType,
      visualizationType,
      body,
    });
  }

  private async buildFilter(
    sources: (DashboardFilterDto | undefined)[],
    orgId: string,
    ownerId: number,
    scope: WorkspaceScopeUtil,
  ): Promise<DashboardFilterDto> {
    const merged = mergeDashboardFiltersStrict(sources.filter(Boolean));
    const scoped = await scope.applyWorkspaceScope(orgId, ownerId, merged);
    return DashboardFilterValidator.parse(scoped);
  }

  private resolveTypes(body: WidgetDataRequestDto) {
    const widgetType = body.widgetType;
    if (!widgetType) {
      throw new BadRequestException(
        'widgetType and visualizationType are required for preview',
      );
    }
    const def = getWidgetTypeDefinition(widgetType);
    return {
      widgetType,
      visualizationType: body.visualizationType ?? def.defaultVisualizationType,
    };
  }

  private async verifyAccess(
    scopeUtil: WorkspaceScopeUtil,
    orgId: string,
    viewerUserId: number,
    filter: DashboardFilterDto,
  ) {
    const requiredWsIds = scopeUtil.extractWorkspaceIdsFromFilter(filter);
    const hasAccess = await scopeUtil.viewerHasWorkspaceAccess(
      orgId,
      viewerUserId,
      requiredWsIds,
    );
    if (!hasAccess) {
      throw new ForbiddenException(
        'WORKSPACE_ACCESS: You don’t have sufficient permissions to view the data in this widget.',
      );
    }
  }

  private buildPaginatedOptions<T>(
    items: T[],
    pagination: PaginationOptions,
    labelKey: keyof T,
  ): FilterOptions {
    const offset = pagination.offset ?? 0;
    const perPage = pagination.perPage ?? 10;
    const total = items.length;
    const nextOffset = total > offset + perPage ? offset + perPage : 0;

    return {
      values: items.map((item: any) => ({
        id: String(item.id),
        name: item[labelKey],
      })),
      pagination: {
        offset,
        perPage,
        nextOffset,
        totalSize: total,
      },
    };
  }

  private buildPaginatedOptionsForWorkspace<T>(
    items: T[],
    pagination: PaginationOptions,
    labelKey: keyof T,
  ): FilterOptions {
    const offset = pagination.offset ?? 0;
    const perPage = pagination.perPage ?? 10;
    const total = items.length;
    const nextOffset = total > offset + perPage ? offset + perPage : 0;

    return {
      values: items.map((item: any) => ({
        id: String(item.id),
        name: item[labelKey],
        isSubscribed: item.isSubscribed || false,
        subscribedFeatures: item.subscribedFeatures || [],
        subscriptionsMissing: item.workspaceSubscriptionsMissing || [],
      })),
      pagination: {
        offset,
        perPage,
        nextOffset,
        totalSize: total,
      },
    };
  }

  async listFilterValues(
    organizationId: string,
    widgetType: WidgetType,
    fieldName: FilterField,
    dashboardFilter: DashboardFilterDto,
    userId: number,
    authorization: string,
    pagination: PaginationOptions,
    search?: string,
  ): Promise<FilterOptions> {
    const def = getWidgetTypeDefinition(widgetType);

    // validate that this widget type actually supports that field
    const allowed = [
      ...def.requiredFilters.map((f) => f.key),
      ...def.optionalFilters.map((f) => f.key),
    ];
    if (!allowed.includes(fieldName)) {
      throw new BadRequestException(
        `filter ${fieldName} is not valid for widget type ${widgetType}`,
      );
    }
    return this.buildSingleFilterValue(
      def,
      fieldName,
      organizationId,
      userId,
      authorization,
      pagination,
      search,
      dashboardFilter,
    );
  }

  public async getKpiOptions(
    dashboardFilter: DashboardFilterDto | undefined,
    widgetTypeDefinition: WidgetTypeDefinition,
    organizationId: string,
    userId: number,
    authorization: string,
  ): Promise<FilterOptions> {
    let platform = dashboardFilter?.filters.find(
      (f) => f.key === FilterField.CHANNEL,
    )?.value as (string | { id: string; name: string })[];

    if (!platform) {
      platform = widgetTypeDefinition.defaultFilter?.filters.find(
        (f) => f.key === FilterField.CHANNEL,
      )?.value as (string | { id: string; name: string })[];
    }
    if (!platform) throw new BadRequestException('KPI requires a platform');

    const userDetails: UserDetailsDto = {
      organizationId,
      userId,
      authorization,
    };

    const filterOptionsDto: GetAnalyticsReportFilterOptionsRequestDto = {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      platform: this.getPlatform(platform).toLowerCase(),
      filterType: FilterType.KPI,
    };

    const kpis = await this.analyticsReportService.getKPIs(
      userDetails,
      filterOptionsDto,
    );

    const items = kpis as Array<{
      id: string;
      name: string;
      isEnabled: boolean;
    }>;

    return {
      values: items.map(({ id, name, isEnabled }) => ({
        id: String(id),
        name,
        spendEnabled: isEnabled,
      })),
    };
  }

  private getPlatform(
    platformName:
      | string
      | { id: string; name: string }
      | (string | { id: string; name: string })[],
  ): Platform {
    if (Array.isArray(platformName)) {
      return this.getPlatform(platformName[0]);
    }
    if (typeof platformName === 'string') {
      return platformName.toLowerCase() as Platform;
    }
    return platformName.id.toLowerCase() as Platform;
  }

  private async buildSingleFilterValue(
    widgetTypeDefinition: WidgetTypeDefinition,
    field: FilterField,
    organizationId: string,
    userId: number,
    authorization: string,
    pagination: PaginationOptions,
    search?: string,
    dashboardFilter?: DashboardFilterDto,
  ): Promise<FilterOptions> {
    const searchTerm = search?.trim().toLowerCase();

    switch (field) {
      case FilterField.ASSET_SOURCE: {
        let values = [
          {
            id: BatchType.PreFlight,
            name: assetSourceDisplayNames[BatchType.PreFlight],
          },
          {
            id: BatchType.InFlight,
            name: assetSourceDisplayNames[BatchType.InFlight],
          },
        ];
        if (searchTerm) {
          values = values.filter((v) =>
            v.name.toLowerCase().includes(searchTerm),
          );
        }
        return { values };
      }

      case FilterField.CHANNEL: {
        let values = Object.values(Platform).map((p) => ({
          id: p,
          name: platformDisplayNames[p],
        }));
        if (searchTerm) {
          values = values.filter((v) =>
            v.name.toLowerCase().includes(searchTerm),
          );
        }
        return { values };
      }

      case FilterField.MARKET: {
        const wsPage =
          await this.workspaceService.getWorkspacesByOrganizationId(
            organizationId,
            userId,
            authorization,
            pagination,
            { search: searchTerm },
          );
        const workspaceIds = wsPage.items.map((w) => w.id);

        // fetch markets for those workspaces
        const mResp =
          await this.adAccountService.getPlatformAdAccountMarketsFromWorkspaces(
            workspaceIds,
            pagination,
          );

        // map to FilterOptions using existing utility
        const items = mResp.result.map((m) => ({
          id: m.isoCode,
          name: m.name,
        }));
        return this.buildPaginatedOptions(items, pagination, 'name');
      }

      case FilterField.KPI:
        return this.getKpiOptions(
          dashboardFilter,
          widgetTypeDefinition,
          organizationId,
          userId,
          authorization,
        );

      case FilterField.WORKSPACE: {
        let wsPage;
        try {
          wsPage = await this.workspaceService.getWorkspacesByOrganizationId(
            organizationId,
            userId,
            authorization,
            pagination,
            { search: searchTerm },
          );
        } catch (err) {
          console.info('Failed to fetch workspaces', err);
          throw err;
        }

        const enriched = await Promise.all(
          wsPage.items.map(async (w) => {
            try {
              const feats = await this.workspaceService.getWorkspaceFeatures(
                w.id,
              );
              const subscribedFeatures = Object.entries(feats)
                .filter(
                  ([feature, enabled]) =>
                    enabled &&
                    dashboardSubscriptionTypes.includes(
                      feature as WorkspaceSubscription,
                    ),
                )
                .map(([feature]) => feature);

              const required =
                widgetTypeDefinition.workspaceSubscriptionsRequired ?? [];

              const isSubscribed = required.every((r) =>
                subscribedFeatures.includes(r),
              );

              const workspaceSubscriptionsMissing = required.filter(
                (r) => !subscribedFeatures.includes(r),
              );

              return {
                id: String(w.id),
                name: w.name,
                isSubscribed,
                subscribedFeatures,
                workspaceSubscriptionsMissing,
              };
            } catch (err) {
              console.info(`Failed to enrich workspace ${w.id}`, err);
              return {
                id: String(w.id),
                name: w.name,
                isSubscribed: false,
                subscribedFeatures: [],
                workspaceSubscriptionsMissing: [],
              };
            }
          }),
        );

        return this.buildPaginatedOptionsForWorkspace(
          enriched,
          pagination,
          'name',
        );
      }

      case FilterField.BRAND: {
        const bPage = await this.brandService.getBrands(
          organizationId,
          pagination,
          searchTerm,
        );
        const items = bPage.items.map((b) => ({
          id: String(b.id),
          name: b.name,
        }));
        return this.buildPaginatedOptions(items, pagination, 'name');
      }

      case FilterField.DATE:
      case FilterField.MEDIA_CREATE_DATE:
        // date-range filters have no discrete values
        return { values: [] };

      default:
        throw new NotFoundException(`unsupported filter field: ${field}`);
    }
  }

  async listWidgetTypes(
    organizationId: string,
    authorization: string,
    userId: number,
    pagination: PaginationOptions,
    search?: string,
  ): Promise<WidgetTypeDefinitionWithValues[]> {
    return Promise.all(
      WIDGET_TYPE_DEFINITIONS.map((d) =>
        this.getWidgetType(
          organizationId,
          d.widgetType,
          authorization,
          userId,
          pagination,
          search,
        ),
      ),
    );
  }

  private toDto(
    w: DashboardWidget,
    widgetFilter: DashboardFilterDto | undefined,
  ): ReadWidgetDto {
    return {
      id: w.id,
      widgetType: w.widgetType,
      name: w.name,
      description: w.description,
      visualizationType: w.visualizationType,
      parameters: w.parameters,
      filter: widgetFilter,
      isCompareToPreviousPeriodEnabled: w.isCompareToPreviousPeriodEnabled,
      isViewDataLabelsEnabled: w.isViewDataLabelsEnabled,
      gridX: w.gridX,
      gridY: w.gridY,
      gridWidth: w.gridWidth,
      gridHeight: w.gridHeight,
      dateCreated: this.ensureDate(w.dateCreated).toISOString(),
      lastUpdated: this.ensureDate(w.lastUpdated).toISOString(),
      isKpiLiftEnabled: w.isKpiLiftEnabled,
      viewByPeriod: w.viewByPeriod,
      isIncludeTotalEnabled: w.isIncludeTotalEnabled,
      createdBy: w.createdBy, // Person instance
      lastModifiedBy: w.lastModifiedBy, // Person instance
    };
  }
  private ensureDate(value: string | Date): Date {
    return value instanceof Date ? value : new Date(value);
  }
}
