import { Test, TestingModule } from '@nestjs/testing';
import { WidgetService } from '../widget/widget.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ForbiddenException, NotFoundException } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { Dashboard } from '../../entities/dashboard.entity';
import { DashboardWidget } from '../../entities/dashboard-widget.entity';
import { ReportFilter } from '../../entities/report-filter.entity';
import { BrandService } from '../../account-management/organization/brand/services/brand.service';
import { WorkspaceService } from '../../account-management/organization/workspace/services/workspace.service';
import { AdAccountService } from '../../account-management/organization/ad-account/services/ad-account.service';
import { FilterField, VisualizationType, WidgetType } from '../dashboard-types';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  Platform,
  platformDisplayNames,
} from '../../constants/scoring-analytics.constants';
import { KPIService } from '@vidmob/vidmob-soa-analytics-service-sdk';
import { WidgetQueryService } from '@vidmob/vidmob-dashboard-service-sdk';
import { CriteriaService } from '../../scoring/criteria/criteria.service';
import { WidgetDataRequestDto } from '../dto/dashboard-data.dto';
import { WorkspaceScopeUtil } from '../workspace-scope-util';
import { AnalyticsReportsService } from '../../analytics/saved-report/analytics-reports.service';

const makeWorkspacePage = (ids: number[]) => ({
  items: ids.map((id) => ({ id, name: `WS-${id}` })),
  totalCount: ids.length,
});

/* deterministic IDs */
jest.mock('crypto', () => ({ randomUUID: () => 'uuid-1' }));

jest.mock('crypto', () => {
  const actual = jest.requireActual('crypto');
  return {
    ...actual,
    randomUUID: jest.fn(() => 'uuid-1'),
  };
});

const mockRepo = () => ({
  findOne: jest.fn(),
  create: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  count: jest.fn(),
});

/* mock downstream services */
const brandSvcMock = { getBrands: jest.fn() };
const workspaceSvcMock = {
  getWorkspacesByIds: jest.fn(),
  getWorkspaceById: jest.fn(),
  getPlatformAdAccountMarketsFromWorkspaces: jest.fn(),
  getWorkspacesByOrganizationIdForUser: jest.fn(),
};
const adAccountSvcMock = {
  getPlatformAdAccountMarketsFromWorkspaces: jest.fn(),
};
const kpiServiceMock = {
  getPlatformKpisAsPromise: jest.fn(),
  getCustomConversionKpisForAdAccountsAsPromise: jest.fn(),
  getKpiByIdAsPromise: jest.fn(),
};

const analyticsReportSvcMock = {
  getKPIs: jest.fn(),
};

const criteriaServiceMock = {
  getCriteriaDetailsAsPromise: jest.fn(),
};

describe('WidgetService', () => {
  let service: WidgetService;

  const dashRepo = mockRepo();
  const widgetRepo = mockRepo();
  const filtRepo = mockRepo();

  beforeEach(async () => {
    jest.clearAllMocks();

    const fakeQueryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        save: jest.fn((obj) =>
          (obj as any).filtersVersion !== undefined
            ? filtRepo.save(obj)
            : widgetRepo.save(obj),
        ),
        update: filtRepo.update,
        findOne: widgetRepo.findOne,
        count: async () => 0,
        createQueryBuilder: () => ({
          where: () => ({
            andWhere: () => ({
              andWhere: () => ({ getCount: async () => 0 }),
            }),
          }),
        }),
      },
    };

    const mockDataSource = {
      createQueryRunner: () => fakeQueryRunner,
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WidgetService,
        { provide: getRepositoryToken(Dashboard), useValue: dashRepo },
        { provide: getRepositoryToken(DashboardWidget), useValue: widgetRepo },
        { provide: getRepositoryToken(ReportFilter), useValue: filtRepo },
        { provide: DataSource, useValue: mockDataSource },
        { provide: BrandService, useValue: brandSvcMock },
        { provide: WorkspaceService, useValue: workspaceSvcMock },
        { provide: AdAccountService, useValue: adAccountSvcMock },
        { provide: KPIService, useValue: kpiServiceMock },
        { provide: AnalyticsReportsService, useValue: analyticsReportSvcMock },
        { provide: CriteriaService, useValue: criteriaServiceMock },
        {
          provide: WidgetQueryService,
          useValue: {
            queryAsPromise: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(WidgetService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('creates a widget + filter', async () => {
    dashRepo.findOne.mockResolvedValue({
      id: 'dash-1',
      organizationId: '99',
      createdBy: 7,
      isDeleted: false,
    });

    filtRepo.create.mockImplementation((x) => x);
    filtRepo.save.mockImplementation(async (x) => x);

    const now = new Date();
    widgetRepo.create.mockImplementation((x) => ({
      ...x,
      id: 'uuid-1',
      dateCreated: now,
      lastUpdated: now,
    }));
    widgetRepo.save.mockResolvedValue({});
    widgetRepo.findOne.mockResolvedValue({
      id: 'uuid-1',
      widgetType: 'BAR',
      name: 'My Widget',
      description: null,
      visualizationType: 'bar',
      parameters: {},
      isCompareToPreviousPeriodEnabled: false,
      isViewDataLabelsEnabled: false,
      gridX: 0,
      gridY: 0,
      gridWidth: 1,
      gridHeight: 1,
      dateCreated: now,
      lastUpdated: now,
      createdBy: { id: 7 },
      lastModifiedBy: { id: 7 },
    } as unknown as DashboardWidget);

    const dto: any = {
      widgetType: 'BAR',
      name: 'My Widget',
      visualizationType: 'bar',
      parameters: {},
      filter: {
        filters: [
          {
            key: 'date',
            operator: 'between',
            value: ['2025-06-01', '2025-06-01'],
          },
          {
            key: 'channel',
            operator: 'in',
            value: [{ id: 'FACEBOOK', name: 'Facebook' }],
          },
          {
            key: 'kpi',
            operator: 'in',
            value: [{ id: 'CTR', name: 'CTR' }],
          },
        ],
      },
    };

    const result = await service.createWidget('dash-1', dto, 7, '99');
    expect(filtRepo.save).toHaveBeenCalledTimes(1);
    expect(widgetRepo.save).toHaveBeenCalledTimes(1);
    expect(result).toMatchObject({
      id: 'uuid-1',
      filter: dto.filter, // filter echoed back
    });
  });

  /* ───────────── createWidget: dashboard missing ───────────── */
  it('throws NotFoundException when dashboard not found', async () => {
    dashRepo.findOne.mockResolvedValue(undefined);

    await expect(
      service.createWidget('missing', {} as any, 1, 'any-org'),
    ).rejects.toBeInstanceOf(NotFoundException);
  });

  /* ───────────── listFilterValues: CHANNEL static list ───────────── */
  it('returns CHANNEL filter values (static list)', async () => {
    const emptyFilter = { filters: [] } as any;
    const pagination: PaginationOptions = { offset: 0, perPage: 10 };

    const result = await service.listFilterValues(
      'org-1',
      WidgetType.ASSETS_SCORED,
      FilterField.CHANNEL,
      emptyFilter,
      7,
      'Bearer token',
      pagination,
      '',
    );

    const expectedSubset = [
      { id: Platform.FACEBOOK, name: platformDisplayNames[Platform.FACEBOOK] },
      { id: Platform.TWITTER, name: platformDisplayNames[Platform.TWITTER] },
      { id: Platform.LINKEDIN, name: platformDisplayNames[Platform.LINKEDIN] },
    ];

    expect(result.values).toEqual(expect.arrayContaining(expectedSubset));
  });

  it('injects implicit scope and validates viewer access', async () => {
    const scope = new WorkspaceScopeUtil(workspaceSvcMock as any);

    workspaceSvcMock.getWorkspacesByOrganizationIdForUser.mockResolvedValue(
      makeWorkspacePage([1, 2]),
    ); // owner list

    const scoped = await scope.applyWorkspaceScope('org', 7, {
      filters: [],
    } as any);
    expect(scoped.filters.at(-1)?.key).toBe(FilterField.WORKSPACE);

    // viewer owns both IDs → true
    expect(await scope.viewerHasWorkspaceAccess('org', 7, ['1', '2'])).toBe(
      true,
    );

    // viewer missing WS-2 → false
    workspaceSvcMock.getWorkspacesByOrganizationIdForUser.mockResolvedValue(
      makeWorkspacePage([1]),
    ); // viewer list
    expect(await scope.viewerHasWorkspaceAccess('org', 8, ['1', '2'])).toBe(
      false,
    );
  });

  it('throws ForbiddenException when viewer lacks workspace access', async () => {
    const body = {
      widgetType: WidgetType.ASSETS_SCORED,
      visualizationType: VisualizationType.TABLE,
      filter: { filters: [] },
    } as unknown as WidgetDataRequestDto;

    widgetRepo.findOne.mockResolvedValue({
      id: 'w-id',
      isDeleted: false,
      widgetType: WidgetType.ASSETS_SCORED,
      visualizationType: VisualizationType.TABLE,
      filter: null,
      dashboard: { dashboardFilter: null, createdBy: 7 },
    });

    workspaceSvcMock.getWorkspacesByOrganizationIdForUser
      .mockResolvedValueOnce(makeWorkspacePage([1, 2])) // owner
      .mockResolvedValueOnce(makeWorkspacePage([1])); // viewer

    await expect(
      service.savedWidgetData('w-id', 'org', body, 8),
    ).rejects.toBeInstanceOf(ForbiddenException);
  });

  it('viewer access is always true when no workspace IDs are required', async () => {
    const scope = new WorkspaceScopeUtil(workspaceSvcMock as any);

    const ok = await scope.viewerHasWorkspaceAccess('org', 42, []);
    expect(ok).toBe(true);
  });

  /* ───────────── getWidgetType: default view-by options ───────────── */
  it('returns widget definition with default viewByPeriodOptions', async () => {
    const pagination: PaginationOptions = { offset: 0, perPage: 10 };

    const def = await service.getWidgetType(
      'org-1',
      WidgetType.ADHERENCE_SCORE,
      'Bearer token',
      7,
      pagination,
    );

    expect(def.viewByPeriodOptions).toEqual(
      expect.arrayContaining(['MONTH', 'QUARTER']),
    );
  });

  /* ───────────── getWidgetType: custom date range (7 days) ────────── */
  it('derives day-level viewByPeriodOptions from Between filter', async () => {
    const pagination: PaginationOptions = { offset: 0, perPage: 10 };

    const filterDto = {
      filters: [
        {
          key: FilterField.MEDIA_CREATE_DATE,
          operator: 'between',
          value: ['2025-06-01', '2025-06-07'], // 7-day span
        },
      ],
    } as any;

    const def = await service.getWidgetType(
      'org-1',
      WidgetType.ADHERENCE_SCORE,
      'Bearer token',
      7,
      pagination,
      '',
      filterDto, // ⬅️ custom filter
    );

    // Only DAY should be included for a 7-day range
    expect(def.viewByPeriodOptions).toEqual(['DAY']);
  });

  describe('WidgetService.updateWidget – filter upsert branches', () => {
    const now = new Date();

    const baseWidget = {
      id: 'widget-1',
      dashboard: { organizationId: 'org-1', createdBy: 7 },
      createdById: 7,
      widgetType: 'BAR',
      name: 'Test Widget',
      description: null,
      visualizationType: 'bar',
      parameters: {},
      gridX: 0,
      gridY: 0,
      gridWidth: 1,
      gridHeight: 1,
      isCompareToPreviousPeriodEnabled: false,
      isViewDataLabelsEnabled: false,
      viewByPeriod: undefined,
      isIncludeTotalEnabled: false,
      dateCreated: now,
      lastUpdated: now,
      createdBy: { id: 7 },
      lastModifiedBy: { id: 7 },
    } as unknown as DashboardWidget;

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('skips upsert when dto.filter is **absent**', async () => {
      // stub findOne → widget without any filterId
      widgetRepo.findOne.mockResolvedValue({ ...baseWidget, filterId: null });
      // stub save → return the same widget object
      widgetRepo.save.mockResolvedValue({ ...baseWidget, filterId: null });

      // call updateWidget without a filter key
      const result = await service.updateWidget(
        'widget-1',
        {} as any,
        7,
        'org-1',
      );

      expect(filtRepo.save).not.toHaveBeenCalled();
      expect(filtRepo.update).not.toHaveBeenCalled();
      expect(widgetRepo.save).toHaveBeenCalledTimes(1);
      // toDto should echo back no filter
      expect(result.filter).toBeUndefined();
    });

    it('2) skips upsert when dto.filter.filters is an empty array', async () => {
      widgetRepo.findOne.mockResolvedValue({ ...baseWidget, filterId: null });
      widgetRepo.save.mockResolvedValue({ ...baseWidget, filterId: null });

      // call updateWidget with filter.filters = []
      const dto = { filter: { filters: [] } };
      const result = await service.updateWidget(
        'widget-1',
        dto as any,
        7,
        'org-1',
      );

      expect(filtRepo.save).not.toHaveBeenCalled();
      expect(filtRepo.update).not.toHaveBeenCalled();
      expect(widgetRepo.save).toHaveBeenCalledTimes(1);
      expect(result.filter).toEqual(dto.filter);
    });

    it('creates a new ReportFilter when widget.filterId is null', async () => {
      widgetRepo.findOne.mockResolvedValue({ ...baseWidget, filterId: null });

      filtRepo.save.mockImplementation(async (entity) => entity);

      widgetRepo.save.mockImplementation(async (widget) => widget);

      const dto = {
        filter: {
          filters: [
            {
              key: 'date',
              operator: 'between',
              value: ['2025-01-01', '2025-01-02'],
            },
          ],
        },
      };

      const result = await service.updateWidget(
        'widget-1',
        dto as any,
        7,
        'org-1',
      );

      expect(filtRepo.save).toHaveBeenCalledTimes(1);

      expect(filtRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({
          id: 'uuid-1',
          filters: JSON.stringify(dto.filter.filters),
        }),
      );

      expect(widgetRepo.save).toHaveBeenCalledWith(
        expect.objectContaining({ filterId: 'uuid-1' }),
      );

      expect(result.filter).toEqual(dto.filter);
    });
  });
});
