import { ReadDashboardDto } from '../dto/read-dashboard.dto';
import { DashboardSharingScope } from '../constants/constants';
import { ReportFilterOperator } from '../../reports/model/report-filters.dto';
import { FilterField, VisualizationType, WidgetType } from '../dashboard-types';

export const mockDashboards: ReadDashboardDto[] = [
  {
    id: '66666666-6666-6666-6666-666666666666',
    name: 'Enterprise Adherence Overview',
    description:
      'Instance created from the Enterprise Adherence Overview preset',
    dashboardFilter: {
      filters: [
        {
          key: FilterField.MEDIA_CREATE_DATE,
          operator: ReportFilterOperator.Between,
          value: ['2025-04-01T00:00:00Z', '2025-04-30T23:59:59Z'],
        },
        {
          key: 'entityType',
          operator: ReportFilterOperator.Equals,
          value: 'AD_ASSET',
        },
      ],
    },
    sharingScope: DashboardSharingScope.PRIVATE,
    dateCreated: '2025-05-07T00:00:00Z',
    lastUpdated: '2025-05-07T11:30:00Z',
    createdBy: {
      id: 10002,
      displayName: '<PERSON>e',
      photoUrl:
        'https://vidmob-storage-dev.s3.amazonaws.com/V9ECR8MH7A/avatar/avatar3345.gif',
    },
    lastModifiedBy: {
      id: 10002,
      displayName: 'John Doe',
      photoUrl:
        'https://vidmob-storage-dev.s3.amazonaws.com/V9ECR8MH7A/avatar/avatar3345.gif',
    },
    widgets: [],
    isFavorite: false,
  },
];

export const mockDashboard: ReadDashboardDto = {
  id: '66666666-6666-6666-6666-666666666666',
  name: 'Enterprise Adherence Overview',
  description: 'Instance created from the Enterprise Adherence Overview preset',
  dashboardFilter: {
    filters: [
      {
        key: FilterField.MEDIA_CREATE_DATE,
        operator: ReportFilterOperator.Between,
        value: ['2025-04-01T00:00:00Z', '2025-04-30T23:59:59Z'],
      },
    ],
  },
  isFavorite: false,
  sharingScope: DashboardSharingScope.PRIVATE,
  dateCreated: '2025-05-07T00:00:00Z',
  lastUpdated: '2025-05-07T11:30:00Z',
  createdBy: {
    id: 10002,
    displayName: 'John Doe',
    photoUrl: 'https://example.com/photos/johndoe.jpg',
  },
  lastModifiedBy: {
    id: 10002,
    displayName: 'John Doe',
    photoUrl: 'https://example.com/photos/johndoe.jpg',
  },
  widgets: [
    {
      id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
      widgetType: WidgetType.KEY_FINDINGS,
      name: 'Key Findings',
      description: 'Rich-text notes for dashboard context',
      gridX: 0,
      gridY: 0,
      gridWidth: 12,
      gridHeight: 4,
      visualizationType: VisualizationType.TEXT,
      filter: {
        filters: [],
      },
      isCompareToPreviousPeriodEnabled: false,
      isIncludeTotalEnabled: false,
      isKpiLiftEnabled: false,
      isViewDataLabelsEnabled: false,
      dateCreated: '2025-05-07T00:00:00Z',
      lastUpdated: '2025-05-07T00:00:00Z',
      createdBy: {
        id: 21075,
        displayName: 'John Doe',
        photoUrl: 'https://example.com/photos/johndoe.jpg',
      },
      lastModifiedBy: {
        id: 21075,
        displayName: 'John Doe',
        photoUrl: 'https://example.com/photos/johndoe.jpg',
      },
    },
  ],
};
