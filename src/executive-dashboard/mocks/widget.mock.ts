import { ReadWidgetDto } from '../dto/read-widget.dto';
import { ReportFilterOperator } from '../../reports/model/report-filters.dto';
import { VisualizationType, WidgetType } from '../dashboard-types';

export const mockWidget: ReadWidgetDto = {
  id: '99999999-9999-9999-9999-999999999999',
  widgetType: WidgetType.CHANNEL_ADHERENCE_TRENDS,
  name: 'Average Adherence Score (Last 30 Days)',
  description: 'Displays the overall average adherence score',
  visualizationType: VisualizationType.DONUT,
  parameters: {},
  filter: {
    filters: [
      {
        key: 'date',
        operator: ReportFilterOperator.Between,
        value: ['2025-04-01T00:00:00Z', '2025-04-30T23:59:59Z'],
      },
    ],
  },
  isCompareToPreviousPeriodEnabled: true,
  isViewDataLabelsEnabled: false,
  gridX: 0,
  gridY: 0,
  gridWidth: 4,
  gridHeight: 2,
  dateCreated: '2025-05-07T00:00:00Z',
  lastUpdated: '2025-05-07T11:30:00Z',
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  createdBy: {
    id: 1,
    displayName: 'System User',
  },
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  lastModifiedBy: {
    id: 1,
    displayName: 'System User',
  },
};

export const mockWidgets: ReadWidgetDto[] = [mockWidget];
