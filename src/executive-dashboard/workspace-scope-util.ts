import { WorkspaceService } from '../account-management/organization/workspace/services/workspace.service';
import { DashboardFilterDto } from './dto/create-dashboard.dto';
import { FilterField } from './dashboard-types';
import {
  ReportFilterDtoV2,
  ReportFilterOperator,
} from '../reports/model/report-filters.dto';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';

/**
 * Centralised utility for resolving and validating **workspace scope** for widgets.
 */
export class WorkspaceScopeUtil {
  private static readonly MAX_PAGE = 10_000;
  constructor(private readonly workspaceService: WorkspaceService) {}

  /**
   * True if the merged filter already constrains workspaces explicitly.
   */
  hasExplicitWorkspaceFilter(filter?: DashboardFilterDto): boolean {
    return !!filter?.filters?.some(this.isNonEmptyWorkspaceFilter);
  }

  private isNonEmptyWorkspaceFilter(filter: ReportFilterDtoV2): boolean {
    return (
      filter.key === FilterField.WORKSPACE &&
      Array.isArray(filter.value) &&
      filter.value.length > 0
    );
  }

  async applyWorkspaceScope(
    organizationId: string,
    ownerUserId: number,
    filter: DashboardFilterDto,
  ): Promise<DashboardFilterDto> {
    if (this.hasExplicitWorkspaceFilter(filter)) {
      return filter;
    }
    return this.applyUserWorkspaceScope(organizationId, ownerUserId, filter);
  }

  private async applyUserWorkspaceScope(
    organizationId: string,
    ownerUserId: number,
    filter: DashboardFilterDto,
  ): Promise<DashboardFilterDto> {
    const workspaceOptions = await this.fetchAccessibleWorkspaceOptions(
      organizationId,
      ownerUserId,
    );

    return {
      ...filter,
      filters: [
        ...filter.filters,
        {
          key: FilterField.WORKSPACE,
          operator: ReportFilterOperator.In,
          value: workspaceOptions,
        } as ReportFilterDtoV2,
      ],
    };
  }

  extractWorkspaceIdsFromFilter(filter?: DashboardFilterDto): string[] {
    if (!filter?.filters?.length) return [];

    return filter.filters
      .filter((f) => f.key === FilterField.WORKSPACE)
      .flatMap((f) => f.value as { id: string; name: string }[])
      .map((v) => v.id);
  }

  /**
   * Returns true if the viewer has access to **every** workspace id in
   * `requiredIds`. If `requiredIds` is empty → true.
   */
  async viewerHasWorkspaceAccess(
    organizationId: string,
    viewerUserId: number,
    requiredIds: string[],
  ): Promise<boolean> {
    if (!requiredIds.length) return true;

    const viewerOptions = await this.fetchAccessibleWorkspaceOptions(
      organizationId,
      viewerUserId,
    );

    const viewerSet = new Set(viewerOptions.map((w) => w.id));
    return requiredIds.every((id) => viewerSet.has(id));
  }

  /**
   * Retrieves all workspaces the user can access in the organization and maps
   * them to `{ id, name }` objects for filter compatibility.
   */
  private async fetchAccessibleWorkspaceOptions(
    organizationId: string,
    userId: number,
  ): Promise<{ id: string; name: string }[]> {
    const page: any =
      await this.workspaceService.getWorkspaceByOrganizationIdAndUserAndSearch(
        organizationId,
        userId,
        {
          offset: 0,
          perPage: WorkspaceScopeUtil.MAX_PAGE,
        } as PaginationOptions,
        {},
      );

    const items = page.items ?? page.result ?? [];
    return items.map((w: any) => ({
      id: String(w.id),
      name: w.name ?? String(w.id),
    }));
  }
}
