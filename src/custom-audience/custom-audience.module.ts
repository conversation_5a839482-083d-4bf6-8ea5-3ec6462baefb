import { Module } from '@nestjs/common';
import { CustomAudienceController } from './custom-audience.controller';
import { CustomAudienceService } from './custom-audience.service';
import { LegacyAnalyticsService } from '../legacy-services/legacy-analytics.service';
import { ConfigService } from '@nestjs/config';

@Module({
  controllers: [CustomAudienceController],
  providers: [CustomAudienceService, LegacyAnalyticsService, ConfigService],
})
export class CustomAudienceModule {}
