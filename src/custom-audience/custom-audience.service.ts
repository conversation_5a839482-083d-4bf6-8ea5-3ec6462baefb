import { Injectable } from '@nestjs/common';
import { PaginationOptions } from '@vidmob/vidmob-nestjs-common';
import {
  AccountType,
  CustomAudienceTargetingStatus,
  Platform,
} from '../constants/platform.constants';
import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import {
  Adset,
  CustomAudience,
  PlatformAccountGroup,
} from './ad-account-types';
import {
  CustomAudienceService as CustomAudienceServiceSdk,
  ReadCustomAudienceDto,
} from '@vidmob/vidmob-soa-analytics-service-sdk';
import { PaginatedResultArray } from '@vidmob/vidmob-nestjs-common/dist/models/pagination/pagination-result-array.dto';
import { LegacyAnalyticsService } from '../legacy-services/legacy-analytics.service';

@Injectable()
export class CustomAudienceService {
  constructor(
    private readonly customAudienceServiceSdk: CustomAudienceServiceSdk,
    private readonly configService: ConfigService,
    private readonly legacyAnalyticsService: LegacyAnalyticsService,
  ) {}

  private async getValidatedAccountsForAccountType(
    tokenWithBearerPrefix: string,
    accountId: string,
    platform: Platform,
  ): Promise<string[]> {
    const canAccessPlatformAccount = await this.canAccessPlatformAccount(
      tokenWithBearerPrefix,
      platform,
      accountId,
    );
    if (!canAccessPlatformAccount) {
      throw new Error(
        'The platform account (' + accountId + ") can't be accessed.",
      );
    }
    return [accountId];
  }

  private async getValidatedAccountsForGroupType(
    tokenWithBearerPrefix: string,
    accountId: string,
  ): Promise<string[]> {
    const platformAccountGroup: PlatformAccountGroup =
      await this.legacyAnalyticsService.getPlatformAccountGroup(
        tokenWithBearerPrefix,
        accountId,
      );
    const supportPlatforms = Object.values(Platform);
    const platformAccountGroupPlatformUpperCase =
      platformAccountGroup?.platform?.toUpperCase();
    const isPlatformAccountGroupValid =
      supportPlatforms.filter(
        (platform) => platform === platformAccountGroupPlatformUpperCase,
      ).length > 0;
    if (!isPlatformAccountGroupValid) {
      throw new Error(
        `Unsupport platform (${platformAccountGroupPlatformUpperCase}) for account group ${accountId}. Supported platforms: ${supportPlatforms.join(
          ',',
        )}`,
      );
    }

    return platformAccountGroup?.platformAdAccounts?.map(
      (platformAccount) => platformAccount.platformAccountId,
    );
  }

  async getAccountIdsForCustomAudiencesRequest(
    tokenWithBearerPrefix: string,
    accountInfo: {
      accountType: AccountType;
      accountId: string;
      platform: Platform;
    },
  ): Promise<string[]> {
    const { accountId, accountType, platform } = accountInfo;
    let accountIds: string[] = [];
    if (accountType.toUpperCase() == AccountType.ACCOUNT) {
      accountIds = await this.getValidatedAccountsForAccountType(
        tokenWithBearerPrefix,
        accountId,
        platform,
      );
    } else if (accountType.toUpperCase() == AccountType.ACCOUNT_GROUP) {
      accountIds = await this.getValidatedAccountsForGroupType(
        tokenWithBearerPrefix,
        accountId,
      );
    }

    if (accountIds?.length <= 0) {
      throw new Error(`Invalid request for accountType ${accountType}.`);
    }
    console.log(`---accountIds ${accountIds}`);

    return accountIds;
  }

  async findAllCustomAudiencesForAccounts(
    platformName: string,
    accountIds: string[],
    paginationOptions: PaginationOptions,
    customAudienceName?: string,
  ): Promise<PaginatedResultArray<ReadCustomAudienceDto>> {
    const response =
      await this.customAudienceServiceSdk.findCustomAudiencesForAccountsAsPromise(
        platformName,
        accountIds.join(','),
        customAudienceName,
        paginationOptions?.offset,
        paginationOptions?.perPage,
      );
    return new PaginatedResultArray(
      response.result,
      response.pagination?.totalSize,
    );
  }

  async canAccessPlatformAccount(
    tokenWithBearerPrefix: string,
    platform: Platform,
    accountId: string,
  ) {
    try {
      await this.legacyAnalyticsService.getPlatformAccount(
        tokenWithBearerPrefix,
        platform,
        accountId,
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  async getCustomAudience(
    platformName: Platform,
    accountId: string,
    customAudienceId: string,
  ) {
    return await this.customAudienceServiceSdk.findCustomAudienceAsPromise(
      customAudienceId,
      platformName,
      accountId,
    );
  }

  async getAdditionalCustomAudiences(
    platform: Platform,
    accountId: string,
    customAudienceId: string,
    targetingStatus: CustomAudienceTargetingStatus,
    tokenWithBearerPrefix: string,
    startDate: string,
    endDate: string,
  ) {
    const adsets: Adset[] = await this.getAdsets(
      platform,
      accountId,
      customAudienceId,
      targetingStatus,
      tokenWithBearerPrefix,
      startDate,
      endDate,
    );
    const additionalCustomAudiences: CustomAudience[] = [];
    adsets.forEach((adset) => {
      let customAudiences: CustomAudience[] = [];
      if (targetingStatus === CustomAudienceTargetingStatus.INCLUDED) {
        customAudiences = adset.targeting.customAudiences;
      } else if (targetingStatus === CustomAudienceTargetingStatus.EXCLUDED) {
        customAudiences = adset.targeting.excludedCustomAudiences;
      }
      if (customAudiences) {
        additionalCustomAudiences.push(...customAudiences);
      }
    });
    return additionalCustomAudiences;
  }

  async getAdsets(
    platform: Platform,
    accountId: string,
    customAudienceId: string,
    targetingStatus: CustomAudienceTargetingStatus,
    tokenWithBearerPrefix: string,
    startDate: string,
    endDate: string,
  ) {
    const headers = {
      Authorization: tokenWithBearerPrefix,
      'Content-Type': 'application/json',
    };
    const adsetGetUrl = this.buildAdsetGetUrl(platform, accountId);
    let parameters: any = {
      mediaTypes: 'VIDEO,IMAGE',
      startDate: startDate,
      endDate: endDate,
    };
    if (targetingStatus === CustomAudienceTargetingStatus.INCLUDED) {
      parameters = {
        ...parameters,
        customAudienceIds: [customAudienceId],
      };
    }
    if (targetingStatus === CustomAudienceTargetingStatus.EXCLUDED) {
      parameters = {
        ...parameters,
        excludedCustomAudienceIds: [customAudienceId],
      };
    }
    const queryString = Object.entries(parameters)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');

    const adsetGetUrlWithParameters = `${adsetGetUrl}?${queryString}`;
    console.log(`adsetGetUrlWithParameters ${adsetGetUrlWithParameters}`);

    try {
      const response = await axios.get(adsetGetUrlWithParameters, { headers });
      return response.data.result as Adset[];
    } catch (e) {
      throw new Error(
        `Error when getting adsets for account (${accountId}). ` + e,
      );
    }
  }

  private buildAdsetGetUrl(platform: Platform, accountId: string): string {
    const platformIntegrationUrl = this.configService.get<string>(
      'platformIntegrationUrl',
    );
    if (!platformIntegrationUrl) {
      throw new Error(`Platform Integration URL is not defined.`);
    }

    const PLATFORM_INTEGRATION_ADSET_GET_URL =
      '/platform/:platform/account/:accountId/adset';
    return platformIntegrationUrl
      .concat(PLATFORM_INTEGRATION_ADSET_GET_URL)
      .replace(':platform', platform)
      .replace(':accountId', accountId);
  }
}
