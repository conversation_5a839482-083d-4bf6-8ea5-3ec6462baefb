/**
 * Data Transfer Object (DTO) representing the custom audience entity
 */
import { AutoMap } from '@automapper/classes';

export class ReadCustomAudienceDto {
  /**
   * An unique id of a custom audience for an account
   * @example 0f6eea16c0327a5bb44b162b6cea1c7e
   */
  @AutoMap()
  id: string;

  /**
   * Platform name
   * @example FACEBOOK
   */
  @AutoMap()
  platform: string;

  /**
   * Platform Account Id which a custom audience is from
   * @example ****************
   */
  @AutoMap()
  account: string;

  /**
   * Custom Audience Id from a platform
   * @example *****************
   */
  @AutoMap()
  customAudienceId: string;

  /**
   * Custom Audience Name from a platform
   * @example "LAL (CA, 1%) - SG FB Page Eng"
   */
  @AutoMap()
  name: string;
}
