export interface PlatformAccountGroup {
  id: number;
  platform: string;
  name: string;
  enabled: boolean;
  platformAdAccounts: PlatformAdAccount[];
}

export interface PlatformAdAccount {
  id: number;
  platform: string;
  platformUserId: string;
  platformAccountId: string;
  platformAccountName: string;
  canAccess: boolean;
}

export interface Adset {
  platform: string;
  account: string;
  accountName: string;
  resource: string;
  id: string;
  name: string;
  targeting: {
    customAudiences: CustomAudience[];
    excludedCustomAudiences: CustomAudience[];
  };
}

export interface CustomAudience {
  id: string;
  customAudienceId: string;
  name: string;
}
