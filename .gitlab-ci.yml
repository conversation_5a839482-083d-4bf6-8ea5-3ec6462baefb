## Please refer to https://vidmob.atlassian.net/wiki/spaces/ATLAS/pages/3724509193/Setting+up+CI+CD for default CI/CD variable overrides
include:
  - project: 'vidmob/repos/recently-migrated/vidmob-operations'
    ref: $OPS_REPO_VERSION
    file: '/gitlab/ci/pipelines/gitflow/soa.nest.docker.ecs.pipeline.yml'

variables:
  JIRA_PROJECT_ID: "VID"
  JIRA_SUB_PROJECT_ID: "PT-BFF"
  ENABLE_PLAYWRIGHT_API_TESTS: "true"
  PLAYWRIGHT_TEST_TAGS_API_BVT: "@api_bvt"
  PLAYWRIGHT_TEST_TAGS_API_SMOKE: "@api_smoke"
  PUBLISH_SDK: "false"
  NODE_VERSION: "20.9.0"

build:
  extends: .build
  script:
    - |
      if [ "$CI_MERGE_REQUEST_IID" != "" ]; then
          export SLACK_CHANNELS="$SLACK_CHANNELS_MRS"
      fi
    - $SLACK_LIBRARY_SCRIPT "$SLACK_CHANNELS" "pipeline" "started"
    - !reference [.build, script]
  variables:
    KUBERNETES_CPU_REQUEST: "4"
    KUBERNETES_MEMORY_REQUEST: "16Gi"
  artifacts:
    paths:
      - package.json
      - package-lock.json
      - dist
      - node_modules
  needs: []

deploy:dev:
  variables:
    AWS_CLI_IAM_ROLE_ARN: "arn:aws:iam::812471470063:role/gitlabUser"
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-acs-bff,vidmob-acs-bff,ecs/vidmob-acs-bff-dev.json'

deploy:stage:
  variables:
    AWS_CLI_IAM_ROLE_ARN: "arn:aws:iam::840000716985:role/gitlabUser"
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-acs-bff,vidmob-acs-bff,ecs/vidmob-acs-bff-stage.json'

deploy:prod:
  variables:
    AWS_CLI_IAM_ROLE_ARN: "arn:aws:iam::560638139269:role/gitlabUser"
    # Comma seperated list of ecs service objects defined as space seperated bash array, defined in the same order as "<Cluster1,Service1,Task1,Task_File1,ASG1,ASG_Size1><space><Cluster2,Service2,Task2,Task_File2,ASG2,ASG_Size2>"
    ECS_DEPLOY_LIST: 'vidmob-nestjs-cluster,vidmob-acs-bff,vidmob-acs-bff,ecs/vidmob-acs-bff-prod.json'
