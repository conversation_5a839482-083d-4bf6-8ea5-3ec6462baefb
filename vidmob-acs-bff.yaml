openapi: 3.0.0
paths:
  /:
    get:
      operationId: App<PERSON><PERSON>roller_getHello
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: &ref_0
        - Bearer Token: []
      summary: >-
        Decorator Public will become this endpoint public, for instance used for
        health check or login
      tags: &ref_1
        - Test
  /helloPartner:
    get:
      operationId: AppController_getHelloPartner
      parameters:
        - name: partnerId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
      summary: >-
        This endpoint will check permission for a Workspace (commonly known as
        partner), which is passed in request but got by  domainContextHandler.

        After getting the permission, the attributes from legacy will be
        represented by: type (action),

        resource (@Permissions ({ domain: PermissionDomain.WORKSPACE, ...}) )
        and subresource feature/data to be accessed.
      tags: *ref_1
  /helloProject:
    get:
      operationId: AppController_getHelloProject
      parameters:
        - name: projectId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_0
      summary: >-
        This endpoint will check permission for a Workspace (commonly known as
        partner), which is passed in request but got by  domainContextHandler.

        After getting the permission, the attributes from legacy will be
        represented by: type (action),

        resource (@Permissions ({ domain: PermissionDomain.WORKSPACE, ...}) )
        and subresource feature/data to be accessed.
      tags: *ref_1
  /user:
    get:
      operationId: AppController_getUser
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponseDto'
      security: *ref_0
      summary: >-
        Without  decorator all requests to this endpoint will be checked for
        permission based on Auth Bearer Token
      tags: *ref_1
  /api-key-management/organization/{organizationId}:
    get:
      description: Fetches an array of API keys, so org admins can view them.
      operationId: ApiKeyManagementController_getAllApiKeysForOrg
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Returns an array of API keys for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadApiKeyResponseDto'
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Organization not found
        '500':
          description: Internal server error retrieving API keys
      security: &ref_2
        - Bearer Token: []
      summary: Retrieve API keys for a given organization
      tags: &ref_3
        - API Key Management
    post:
      operationId: ApiKeyManagementController_createOrganizationApiKey
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateApiKeyRequestDto'
      responses:
        '201':
          description: API key successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateApiKeyResponseDto'
      security: *ref_2
      summary: Create an API key for an organization user
      tags: *ref_3
  /api-key-management/organization/{organizationId}/{apiKeyId}:
    patch:
      operationId: ApiKeyManagementController_updateOrganizationApiKey
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: apiKeyId
          required: true
          in: path
          description: The id of the API Key
          schema:
            type: string
      responses:
        '200':
          description: API key successfully updated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadApiKeyResponseDto'
      security: *ref_2
      summary: Update an API key for an organization
      tags: *ref_3
    delete:
      operationId: ApiKeyManagementController_deleteOrganizationApiKey
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: apiKeyId
          required: true
          in: path
          description: The id of the API Key
          schema:
            type: string
      responses:
        '200':
          description: API key successfully deleted
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteApiKeyResponseDto'
      security: *ref_2
      summary: Delete a specific API key for an organization
      tags: *ref_3
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          description: The Health Check is successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  info:
                    type: object
                    example: &ref_4
                      database: &ref_5
                        status: up
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example: {}
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example: *ref_4
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
        '503':
          description: The Health Check is not successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  info:
                    type: object
                    example: *ref_4
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  error:
                    type: object
                    example:
                      redis: &ref_6
                        status: down
                        message: Could not connect
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
                    nullable: true
                  details:
                    type: object
                    example:
                      database: *ref_5
                      redis: *ref_6
                    additionalProperties:
                      type: object
                      required:
                        - status
                      properties:
                        status:
                          type: string
                      additionalProperties: true
      tags:
        - Health
  /criteria/workspace/{workspaceId}/current-best-practices:
    get:
      operationId: CriteriaController_getCurrentWorkspaceBestPractices
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: channels
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_7
        - Bearer Token: []
      tags: &ref_8
        - Criteria
  /criteria/workspace/{workspaceId}/outdated-best-practices:
    get:
      operationId: CriteriaController_getOutdatedWorkspaceBestPractices
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: channels
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
  /criteria/{workspaceId}/create-best-practices:
    post:
      operationId: CriteriaController_createWorkspaceBestPractices
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: userId
          required: true
          in: query
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_7
      tags: *ref_8
  /criteria/workspace/{workspaceId}/delete-outdated-best-practices:
    post:
      operationId: CriteriaController_deleteOutdatedWorkspaceBestPractices
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
  /criteria/{criteriaId}/partner/{partnerId}:
    delete:
      operationId: CriteriaController_deleteCriteria
      parameters:
        - name: partnerId
          required: true
          in: path
          description: The id of the partner
          schema:
            type: number
        - name: criteriaId
          required: true
          in: path
          schema:
            type: number
        - name: id
          required: true
          in: path
          description: The id of the criteria
          schema: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      summary: Delete a criteria from a criteria set.
      tags: *ref_8
  /criteria/workspace/{workspaceId}:
    post:
      operationId: CriteriaController_createCriteria
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The ID of the workspace
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      summary: >-
        Create a new criteria as part of a criteria set or in the partner's
        default criteria set if not specified in

        the body of the request.
      tags: *ref_8
  /criteria/template/partner/{partnerId}:
    get:
      operationId: CriteriaController_getPartnerCriteriaTemplates
      parameters:
        - name: partnerId
          required: true
          in: path
          schema:
            type: number
        - name: excludeDeprecated
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_7
      tags: *ref_8
  /criteria/organization/{organizationId}:
    post:
      operationId: CriteriaController_getCriteria
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The unique identifier of the organization for which the criteria
            belongs to.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: string
      responses:
        '201':
          description: ''
      security: *ref_7
      summary: Gets the criteria related to the given parameters.
      tags: *ref_8
  /criteria/{partnerId}/{criteriaId}:
    patch:
      operationId: CriteriaController_updateCriteria
      parameters:
        - name: criteriaId
          required: true
          in: path
          description: The id of the criterion
          schema:
            type: number
        - name: partnerId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      summary: Updates a criterion. For use in the criteria management initiative.
      tags: *ref_8
  /criteria/{id}:
    put:
      operationId: CriteriaController_deleteCriterion
      parameters:
        - name: id
          required: true
          in: path
          description: The id of the criterion
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      summary: Soft deletes a criterion. For use in the criteria management initiative.
      tags: *ref_8
  /criteria:
    get:
      operationId: CriteriaController_getCriteriaAcrossWorkspacesV2
      parameters:
        - name: workspaceIds
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_7
      tags: *ref_8
  /criteria/download-csv:
    post:
      operationId: CriteriaController_downloadWorkspaceCriteriaCsv
      parameters: []
      responses:
        '201':
          description: ''
      security: *ref_7
      tags: *ref_8
  /criteria/options:
    get:
      operationId: CriteriaController_getCriteriaOptions
      parameters:
        - name: workspaceId
          required: true
          in: query
          description: System assigned Id of the workspace
          schema:
            type: number
        - name: includeGlobalOptions
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
  /criteria/organization/{organizationId}/details:
    post:
      operationId: CriteriaController_getCriteriaDetails
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
  /criteria/organization/{organizationId}/custom-icons:
    get:
      operationId: CriteriaController_getCustomIconsForOrganization
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
    post:
      operationId: CriteriaController_uploadCustomIcon
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_7
      tags: *ref_8
  /criteria/custom-icons/attach:
    post:
      operationId: CriteriaController_attachIconsToCriteria
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
  /criteria/custom-icons/detach:
    post:
      operationId: CriteriaController_detachIconsFromCriteria
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_7
      tags: *ref_8
  /compliance/criteria-set:
    post:
      operationId: CriteriaSetController_createCriteriaSet
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_9
        - Bearer Token: []
      summary: Creates a criteria sets.
      tags: &ref_10
        - Compliance - Criteria Set
  /compliance/criteria-set/organization/{organizationId}:
    post:
      operationId: CriteriaSetController_getCriteriaSets
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The unique identifier of the organization for which  criteria set
            exists in.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_9
      summary: Gets the criteria sets related to the given parameters
      tags: *ref_10
  /compliance/criteria-set/{id}:
    put:
      operationId: CriteriaSetController_updateCriteriaSet
      parameters:
        - name: id
          required: true
          in: path
          description: The id of the criteria set
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_9
      summary: Updates a criteria set and its associations.
      tags: *ref_10
    delete:
      operationId: CriteriaSetController_deleteCriteriaSet
      parameters:
        - name: id
          required: true
          in: path
          description: The id of the criteria set
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_9
      summary: Deletes a criteria set.
      tags: *ref_10
  /criteria-group/organization/{organizationId}/workspace/{workspaceId}:
    get:
      operationId: CriteriaGroupController_getCriteriaGroups
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
        - name: includeCriteriaDetails
          required: false
          in: query
          description: >-
            Will exclude the criteria that are part of the group if false or not
            provided
          schema: {}
        - name: searchText
          required: false
          in: query
          description: Search text for filtering criteria groups
          schema: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_11
        - Bearer Token: []
      tags: &ref_12
        - Criteria Group
    post:
      operationId: CriteriaGroupController_createCriteriaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_11
      tags: *ref_12
  /criteria-group/organization/{organizationId}/workspace/{workspaceId}/criteria-group/{criteriaGroupId}:
    get:
      operationId: CriteriaGroupController_getSingleCriteriaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: criteriaGroupId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_11
      tags: *ref_12
    patch:
      operationId: CriteriaGroupController_updateCriteriaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: criteriaGroupId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: false
          in: query
          description: Workspace id for validating user access to update criteria group
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_11
      tags: *ref_12
    delete:
      operationId: CriteriaGroupController_deleteCriteriaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: criteriaGroupId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: false
          in: query
          description: Workspace id for validating user access to delete criteria group
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_11
      tags: *ref_12
  /criteria-group/organization/{organizationId}/workspace/{workspaceId}/criteria-group/criteria:
    post:
      operationId: CriteriaGroupController_manageCriteriaInCriteriaGroups
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: false
          in: query
          description: Workspace id for validating user access to delete criteria group
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_11
      tags: *ref_12
  /brand/partner/{partnerId}:
    get:
      operationId: BrandController_getBrandIdentifiers
      parameters:
        - name: partnerId
          required: true
          in: path
          description: The id of the partner to get all brand identifiers for
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: &ref_13
        - Bearer Token: []
      summary: Get all brand identifiers for a partner.
      tags: &ref_14
        - Brand
    post:
      operationId: BrandController_updateBrandIdentifiers
      parameters:
        - name: partnerId
          required: true
          in: path
          description: The id of the partner to update the brand identifiers for
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_13
      summary: Update brand identifiers for a partner.
      tags: *ref_14
  /scorecard/workspace/{workspaceId}/aggregate:
    post:
      operationId: ScoreController_getAggregateScores
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_15
        - Bearer Token: []
      summary: Calculates the aggregate scores for a scorecard or an ad account.
      tags: &ref_16
        - Scores
  /scorecard/workspace/{workspaceId}/criteria/aggregate:
    post:
      operationId: ScoreController_getAggregateCriteriaScoresForPreFlight
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      summary: >-
        Calculates the aggregate criteria scores for a pre-flight scorecard.

        In-flight scorecards have transitioned to in-flight reports. See reports
        controller.
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/channel/aggregate:
    post:
      operationId: ScoreController_getAggregateChannelScoresForPreFlight
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      summary: '@deprecated. Use getAggregateGroupScoresForPreFlight() instead.'
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/group/aggregate:
    post:
      operationId: ScoreController_getAggregateGroupScoresForPreFlight
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/score:
    post:
      operationId: ScoreController_getMediaScoreDetails
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      summary: Obtain score details for a scorecard or an ad account.
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/pre-flight/media-list:
    post:
      operationId: ScoreController_getPreflightMediaAndScores
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
      security: *ref_15
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/media:
    post:
      operationId: ScoreController_getIndividualMediaScoresV3
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReadIndividualMediaScoresRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      summary: |-
        Gets scores for individual media item.
        Collates score and criteria details for the media item.
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/media-object/{mediaId}:
    get:
      operationId: ScoreController_getSingleMediaObject
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      summary: Gets media object details for a given media id. Authorizes on workspace.
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/media/aggregate:
    post:
      operationId: ScoreController_getIndividualMediaChannelOrCriteriaGroupAggregates
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
      security: *ref_15
      summary: >-
        Gets channels/criteria groups and the media's aggregate scores for
        criteria within each.
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/media/criteria:
    post:
      operationId: ScoreController_getIndividualMediaCriteriaAndScore
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: searchTerm
          required: true
          in: query
          description: Search term to filter criteria by name
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_15
      summary: Gets criteria and the media's scores for each.
      tags: *ref_16
  /scorecard/workspace/{workspaceId}/media/csv:
    post:
      operationId: ScoreController_getIndividualMediaScoresCSV
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReadIndividualMediaScoresRequestDto'
      responses:
        '201':
          description: ''
      security: *ref_15
      summary: >-
        Gets scores for individual media item. Returns CSV of V2 get media and
        scores api.

        Collates score and criteria details for the media item.
      tags: *ref_16
  /score/norms/scopes:
    get:
      operationId: ScoringNormsController_getScoringNormsScopes
      parameters: []
      responses:
        '200':
          description: ''
      security: &ref_17
        - Bearer Token: []
      summary: return a scoring norms scopes.
      tags: &ref_18
        - Scoring Norms
  /score/norms/objectives:
    get:
      operationId: ScoringNormsController_getScoringNormsObjectives
      parameters: []
      responses:
        '200':
          description: ''
      security: *ref_17
      tags: *ref_18
  /compliance/score-override:
    put:
      operationId: ScoreOverrideController_updateScoreOverride
      parameters:
        - name: overrideRequests
          required: true
          in: path
          description: A list of resolutions to score override requests
          schema: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: &ref_19
        - Bearer Token: []
      summary: Updates and resolves one or more score override requests.
      tags: &ref_20
        - Score Override
    get:
      operationId: ScoreOverrideController_getScoreOverride
      parameters:
        - name: status
          required: true
          in: query
          description: Optionally filter by request status.
          schema:
            enum:
              - OPEN
              - CLOSED
              - RESOLVED
            type: string
        - name: canUpdate
          required: false
          in: query
          description: >-
            Optionally filter by whether the user can update the request. If
            true, only return requests that the user can update.

            If false, return all requests the user can read.
          schema:
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoreOverrideGetResponseDto'
      security: *ref_19
      summary: >-
        Gets a list of score override requests that the caller has permission to
        see.
      tags: *ref_20
  /compliance/score-override/organization/{organizationId}:
    get:
      operationId: ScoreOverrideController_getScoreOverrideV2
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: status
          required: true
          in: query
          description: Optionally filter by request status.
          schema:
            enum:
              - OPEN
              - CLOSED
              - RESOLVED
            type: string
        - name: canUpdate
          required: false
          in: query
          description: >-
            Optionally filter by whether the user can update the request. If
            true, only return requests that the user can update.

            If false, return all requests the user can read.
          schema:
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScoreOverrideGetResponseV2Dto'
      security: *ref_19
      tags: *ref_20
  /reports/workspace/{workspaceId}/report-types:
    get:
      operationId: ReportsController_getAccessibleReportTypes
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the current workspace
          schema:
            type: number
      responses:
        '200':
          description: >-
            Returns the accessible scoring report types based on workspace and
            user
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/AccessibleReportTypesDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_21
        - Bearer Token: []
      summary: >-
        Retrieves the accessible report types based on the given workspace ID
        and the user ID.
      tags: &ref_22
        - Reports
  /reports/workspace/{workspaceId}/options:
    get:
      operationId: ReportsController_getReportOptions
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the current workspace
          schema:
            type: number
      responses:
        '200':
          description: The data options available for scoring report customization
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ScoringReportOptionsDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_21
      tags: *ref_22
  /reports/markets:
    get:
      operationId: ReportsController_getMarketsForReport
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_21
      tags: *ref_22
  /reports/diversity/workspace/{workspaceId}:
    post:
      operationId: ReportsController_generateDiversityReportV2
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      responses:
        '200':
          description: Diversity report successfully created
        '201':
          description: ''
      security: *ref_21
      tags: *ref_22
  /reports/adoption/workspace/{workspaceId}:
    post:
      operationId: ReportsController_generateAdoptionReportV2
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      responses:
        '200':
          description: Adoption report successfully created
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadReportDto'
        '201':
          description: ''
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_21
      tags: *ref_22
  /reports/adherence/workspace/{workspaceId}:
    post:
      operationId: ReportsController_generateAdherenceReportV2
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      responses:
        '200':
          description: Adherence report successfully created
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadReportDto'
        '201':
          description: ''
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_21
      summary: |-
        Create and return an adherence report based on the input settings.
        This version supports analytics advanced filters.
      tags: *ref_22
  /reports/adherence-norms/workspace/{workspaceId}:
    post:
      operationId: ReportsController_generateAdherenceNormsReportV3
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      responses:
        '200':
          description: Adherence norms report successfully created
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadReportDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdherenceReportV3ResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_21
      summary: |-
        Create and return an adherence norms report based on the input settings.
        This version supports analytics advanced filters.
      tags: *ref_22
  /reports/impression-adherence/workspace/{workspaceId}:
    post:
      operationId: ReportsController_generateImpressionAdherenceReportV3
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      responses:
        '200':
          description: Impression Adherence report successfully created
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadReportDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdherenceReportV3ResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_21
      tags: *ref_22
  /reports/adoption/workspace/{workspaceId}/csv:
    post:
      operationId: ReportsController_generateAdoptionReportCsv
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
      security: *ref_21
      summary: Create and return an adoption report CSV based on the input settings.
      tags: *ref_22
  /reports/adherence/workspace/{workspaceId}/csv:
    post:
      operationId: ReportsController_generateAdherenceReportCsvV2
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
      security: *ref_21
      summary: |-
        Create and return an adherence report CSV based on the input settings.
        This version supports analytics advanced filters.
      tags: *ref_22
  /reports/impression-adherence/workspace/{workspaceId}/csv:
    post:
      operationId: ReportsController_generateImpressionAdherenceReportCsv
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
      security: *ref_21
      summary: >-
        Create and return an impression-adherence report CSV based on the input
        settings.
      tags: *ref_22
  /reports/adherence/workspace/{workspaceId}/metadata/csv:
    post:
      operationId: ReportsController_generateAdherenceReportMetadataCsv
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
      security: *ref_21
      summary: Return metadata on the media included in an adherence report row
      tags: *ref_22
  /reports/noauth/adherence/media/{mediaId}/download:
    get:
      operationId: ReportsController_downloadAdherenceMetadataMediaById
      parameters:
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
        - name: token
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_21
      summary: |-
        Redirects to download url for media files in the adherence report row
        Is public since link is shared externally in metadata csv
      tags: *ref_22
  /reports/organization/{organizationId}/adAccount/earliest-impression-date:
    post:
      operationId: ReportsController_getEarliestImpressionDate
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
      responses:
        '200':
          description: Earliest impression date for ad accounts
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/type'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_21
      summary: >-
        Returns the earliest impression date for ad accounts in the given
        request, so the FE can present lifetime data
      tags: *ref_22
  /reports/organization/{organizationId}/in-flight/summary:
    post:
      operationId: ReportsController_getInFlightReportGroupAggregate
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_21
      tags: *ref_22
  /reports/organization/{organizationId}/in-flight/criteria:
    post:
      operationId: ReportsController_getInFlightReportCriteriaAggregate
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_21
      tags: *ref_22
  /reports/organization/{organizationId}/in-flight/media-list:
    post:
      operationId: ReportsController_getInflightMediaAndScores
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
        - name: sortOrder
          required: true
          in: query
          description: The order to sort the media by
          schema:
            enum:
              - ASC
              - DESC
            type: string
        - name: sortBy
          required: true
          in: query
          description: The field to sort the media by
          schema:
            example: passedPercent
            type: string
        - name: sortByChannel
          required: true
          in: query
          description: >-
            What channel's passedPercentByChannel to sort by. Only in use if
            sortBy = passedPercentByChannel
          schema:
            example: ALL_PLATFORMS
            type: string
        - name: sortByGroupId
          required: true
          in: query
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
      security: *ref_21
      tags: *ref_22
  /reports/organization/{organizationId}/in-flight/csv:
    post:
      operationId: ReportsController_downloadInflightReportCSV
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
        - name: sortOrder
          required: true
          in: query
          description: The order to sort the media by
          schema:
            enum:
              - ASC
              - DESC
            type: string
        - name: sortBy
          required: true
          in: query
          description: The field to sort the media by
          schema:
            example: passedPercent
            type: string
        - name: sortByChannel
          required: true
          in: query
          description: >-
            What channel's passedPercentByChannel to sort by. Only in use if
            sortBy = passedPercentByChannel
          schema:
            example: ALL_PLATFORMS
            type: string
      responses:
        '201':
          description: ''
      security: *ref_21
      tags: *ref_22
  /scorecard:
    get:
      operationId: ScorecardController_getScorecardsGet
      parameters:
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_23
        - Bearer Token: []
      summary: >-
        This endpoint gets a list of scorecards for a given workspace. It
        supports a series of filters through query params.
      tags: &ref_24
        - Scorecard
    post:
      operationId: ScorecardController_createScoreCard
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: This endpoint allows the user to create a scorecard.
      tags: *ref_24
  /scorecard/{scorecardId}/score:
    post:
      operationId: ScorecardController_scoreBatch
      parameters:
        - name: scorecardId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: Convenience endpoint to rescore batches (with media if provided)
      tags: *ref_24
  /scorecard/scorecard-options:
    get:
      operationId: ScorecardController_getScorecardOptions
      parameters:
        - name: workspaceId
          required: true
          in: query
          schema:
            type: number
        - name: platforms
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: >-
        This endpoint gets a list of scorecard filter options for a given
        workspace.
      tags: *ref_24
  /scorecard/{scorecardId}:
    get:
      operationId: ScorecardController_getScorecard
      parameters:
        - name: scorecardId
          required: true
          in: path
          description: System assigned id of the scorecard
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: This endpoint allows the user to get details of a scorecard.
      tags: *ref_24
    patch:
      operationId: ScorecardController_updateScorecard
      parameters:
        - name: scorecardId
          required: true
          in: path
          description: System assigned id of the scorecard
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: >-
        This endpoint allows the user to update scorecard details like name,
        markets, platforms etc.
      tags: *ref_24
  /scorecard/{scorecardId}/csv:
    post:
      operationId: ScorecardController_downloadScorecardCsv
      parameters:
        - name: scorecardId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Stream'
      security: *ref_23
      summary: This endpoint creates a CSV file with scorecard details.
      tags: *ref_24
  /scorecard/in-flight:
    post:
      operationId: ScorecardController_createInFlightScorecard
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      tags: *ref_24
  /scorecard/{workspaceId}:
    post:
      operationId: ScorecardController_getScorecardsPost
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: >-
        This endpoint gets a list of scorecards for a given workspace. It
        supports a series of filters through query params.
      tags: *ref_24
  /scorecard/{workspaceId}/project-detail:
    post:
      operationId: ScorecardController_getScorecardsWithProjectDetails
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
      security: *ref_23
      summary: >-
        This endpoint gets a list of scorecards with project details for a given
        workspace. It supports a series of filters through body params
      tags: *ref_24
  /scorecard/{projectId}/outputVideo:
    get:
      operationId: ScorecardController_getOutputVideoScorecardsCount
      parameters:
        - name: projectId
          required: true
          in: path
          description: ID of the project
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: Paginated list of scorecards per output video
      security: *ref_23
      summary: >-
        This endpoint returns a paginated list of scorecards for each output
        video in a project.
      tags: *ref_24
  /scorecard/platform-media/{mediaId}:
    get:
      operationId: ScorecardController_getPlatformMediaId
      parameters:
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
      security: *ref_23
      summary: This endpoint return platformMediaId based on mediaId.
      tags: *ref_24
  /scorecard/workspace/{workspaceId}/scorecard/{scorecardId}/media/{mediaId}:
    get:
      operationId: ScorecardController_getMediaForScorecard
      parameters:
        - name: scorecardId
          required: true
          in: path
          schema:
            type: number
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: Endpoint to fetch media for a scorecard
      tags: *ref_24
  /scorecard/{scorecardId}/earliest-impression-date:
    get:
      operationId: ScorecardController_getEarliestAdAccountImpressionDate
      parameters:
        - name: scorecardId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_23
      summary: Endpoint to get earliest media impression date for ad-account scorecards
      tags: *ref_24
  /custom-criteria/rules:
    get:
      operationId: CustomCriteriaController_getCriteriaRulesV2
      parameters:
        - name: stages
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_25
        - Bearer Token: []
      tags: &ref_26
        - Custom Criteria
  /custom-criteria/elements:
    get:
      operationId: CustomCriteriaController_getElementListV3
      parameters:
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_25
      tags: *ref_26
  /custom-criteria/workspace/{workspaceId}:
    post:
      operationId: CustomCriteriaController_createCustomRule
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: >-
            The unique identifier of the workspace for which the custom criteria
            will belong to.
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_25
      summary: >-
        Create a new criteria as part of a criteria set or in the partner's
        default criteria set if not specified in

        the body of the request.
      tags: *ref_26
  /project/{projectId}/draft-media-variant/{mediaId}:
    get:
      operationId: DraftMediaVariantController_findDraftMediaVariantUrl
      parameters:
        - name: mediaId
          required: true
          in: path
          description: The id of the Media
          schema:
            type: number
        - name: projectId
          required: true
          in: path
          description: The id of the Project
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadDraftMediaVariantDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - Bearer Token: []
      summary: Find the draft media variant url, duration and status by media id.
      tags:
        - Studio - Project
  /project/{projectId}/media-variant/{mediaId}:
    get:
      operationId: MediaVariantController_findMediaVariantUrl
      parameters:
        - name: mediaId
          required: true
          in: path
          description: The id of the Media
          schema:
            type: number
        - name: projectId
          required: true
          in: path
          description: The id of the Project
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadMediaVariantDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - Bearer Token: []
      tags:
        - Studio - Project
  /studio/project/{projectId}/downloadCsv:
    get:
      operationId: ProjectCsvDownloadController_downloadProjectCsv
      parameters:
        - name: projectId
          required: true
          in: path
          schema:
            type: string
        - name: iterationMedia
          required: true
          in: query
          schema:
            type: string
        - name: type
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
      security:
        - Bearer Token: []
      summary: >-
        Downloads a CSV for a project based on the project ID and media IDs
        provided.
      tags:
        - Studio - Project
  /studio/project/{id}:
    post:
      operationId: ProjectController_updateProjectDetails
      parameters:
        - name: id
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProjectDetailsDto'
      responses:
        '200':
          description: ''
      security:
        - Bearer Token: []
      summary: |-
        Updates project details. Currently supports updating the delivery date.
        Future parameters can be added to UpdateProjectDetailsDto.
        Requires the updateProjectDetails permission (partner.project.update).
      tags:
        - Studio - Project
  /account-management/workspace/{workspaceId}/market:
    post:
      operationId: MarketController_createMarketWorkspace
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: isoCode
          required: true
          in: path
          description: The id of Market
          schema: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWorkspaceMarketMapDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/CreateWorkspaceMarketDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      summary: Create a new assignment to a workspace.
      tags: &ref_27
        - Market
    delete:
      operationId: MarketController_removeMarketFromWorkspace
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: isoCode
          required: true
          in: path
          description: The id of the market
          schema: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteMarketAssignResponseDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/DeleteMarketAssignResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      summary: Remove the assignment of a market to a workspace.
      tags: *ref_27
    get:
      operationId: MarketController_listMarkets
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReadMarketDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      summary: List All Markets assigned to a workspace.
      tags: *ref_27
  /account-management/organization/{organizationId}/brand:
    get:
      operationId: BrandController_getBrands
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: search
          required: false
          in: query
          description: Search term for brand name
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReadBrandDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_28
        - Bearer Token: []
      tags: &ref_29
        - Brand
    post:
      operationId: BrandController_createBrand
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: description
          required: true
          in: path
          description: The description of Brand
          schema: {}
        - name: name
          required: true
          in: path
          description: The name of Brand
          schema: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBrandDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '409':
          description: Brand name already exists.
      security: *ref_28
      summary: Create a new brand to a organization.
      tags: *ref_29
  /account-management/organization/{organizationId}/brand/{brandId}:
    put:
      operationId: BrandController_updateBrand
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: brandId
          required: true
          in: path
          description: The id of the brand
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBrandDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadBrandDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Brand name already exists.
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_28
      summary: Update a brand by id
      tags: *ref_29
    delete:
      operationId: BrandController_deleteBrand
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: brandId
          required: true
          in: path
          description: The id of the brand
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadBrandDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_28
      summary: Soft Delete a brand by id
      tags: *ref_29
  /account-management/organization/{organizationId}/workspace/all:
    get:
      operationId: WorkspaceController_getAllWorkspaces
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization ID represents the unique identifier of the
            organization to which all workspaces belong.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_30
        - Bearer Token: []
      summary: >-
        Lightweight endpoint to load all workspaces within an organization.

        The purpose of this endpoint is to provide a fast query to retrieve all
        workspaces, without much querying.
      tags: &ref_31
        - Workspace
  /account-management/organization/{organizationId}/workspace:
    get:
      operationId: WorkspaceController_getWorkspacesByOrganizationIdAndSearch
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization ID represents the unique identifier of the
            organization to which all workspaces belong.
          schema:
            type: string
        - name: search
          required: true
          in: query
          description: The search param to filter workspaces by name.
          schema:
            example: '?search=my cool Workspace'
            type: string
        - name: market
          required: true
          in: query
          description: The market (Country) isoCode to filter workspaces.
          schema:
            example: '?market=bra,usa,fra'
            type: string
        - name: brand
          required: true
          in: query
          description: The brand name to filter workspaces.
          schema:
            example: '?brand=VidMob cool brand'
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_30
      summary: Get all workspaces by organization and market.
      tags: *ref_31
    post:
      operationId: WorkspaceController_createWorkspace
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The unique identifier of the organization for which the new
            workspace will be created (provided as a URL parameter).
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWorkspaceDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_30
      summary: Create a new workspace
      tags: *ref_31
  /account-management/organization/{organizationId}/workspace/{workspaceId}:
    get:
      operationId: WorkspaceController_showWorkspaceById
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: >-
            The workspace ID represents the unique id of the workspace to be
            retrieved.
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_30
      summary: Show a workspace by id
      tags: *ref_31
    patch:
      operationId: WorkspaceController_updateWorkspace
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization ID represents the unique identifier of the
            organization to which all workspaces belong.
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWorkspaceDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_30
      summary: Update a workspace by id
      tags: *ref_31
  /account-management/organization/{organizationId}/workspace/{workspaceId}/ad-account-health:
    get:
      operationId: WorkspaceController_getAdAccountsListForHealthDashboard
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization ID represents the unique identifier of the
            organization.
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          description: The workspace ID represents the unique identifier of the partner.
          schema:
            type: number
        - name: search
          required: true
          in: query
          description: The search param to filter ad accounts by name, id or channel.
          schema:
            example: '?search=my cool Workspace'
            type: string
        - name: sortBy
          required: true
          in: query
          description: The sort by string to determine the field to sort results.
          schema:
            example: '?sortBy=channel - platform_account_name (default)'
            type: string
        - name: sortOrder
          required: true
          in: query
          description: The sort by order.
          schema:
            example: '?sortOrder=ASC - ASC(default) or DESC.'
            type: string
        - name: brands
          required: false
          in: query
          description: Brands string
          schema:
            example: none
            type: string
        - name: importStatus
          required: false
          in: query
          description: ImportStatus string
          schema:
            example: Suspended,Not Importing
            type: string
        - name: markets
          required: false
          in: query
          description: Markets string
          schema:
            example: none
            type: string
        - name: connectionStatus
          required: false
          in: query
          description: Connection Status string
          schema:
            example: Disconnected,Connected
            type: string
        - name: workspaces
          required: false
          in: query
          description: Workspaces string
          schema:
            example: none
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_30
      summary: Get Ad Accounts list for health dashboard.
      tags: *ref_31
  /account-management/organization/{organizationId}/workspace/{workspaceId}/ad-accounts/brands:
    post:
      operationId: WorkspaceController_createPlatformAdAccountsAndBrandsMaps
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAdAccountBrandMapDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadAdAccountMapDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadAdAccountMapDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_30
      summary: POST a bulk map between ad account and brands
      tags: *ref_31
  /account-management/organization/{organizationId}/workspace/{workspaceId}/ad-accounts/markets:
    post:
      operationId: WorkspaceController_createPlatformAdAccountsAndMarketsMaps
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAdAccountMarketMapDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_30
      summary: POST a bulk map between ad account and markets
      tags: *ref_31
  /account-management/organization/{organizationId}/workspace/{workspaceId}/ad-account/{adAccountId}/brands:
    get:
      operationId: WorkspaceController_getPlatformAdAccountBrands
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: adAccountId
          required: true
          in: path
          description: The ad account id
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadAdAccountBrandsDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_30
      summary: GET a list of brands associated to ad account
      tags: *ref_31
  /account-management/organization/{organizationId}/workspace/{workspaceId}/ad-account/{adAccountId}/markets:
    get:
      operationId: WorkspaceController_getPlatformAdAccountMarkets
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: adAccountId
          required: true
          in: path
          description: The ad account id
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_30
      summary: GET a list of markets associated to ad account
      tags: *ref_31
  /account-management/workspace/{workspaceId}/brand:
    post:
      operationId: WorkspaceBrandController_createBrand
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: brandId
          required: true
          in: path
          description: The id of the brand
          schema: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LocalWorkspaceBrandMapDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadWorkspaceBrandMapDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_32
        - Bearer Token: []
      summary: Assign a brand to a workspace.
      tags: &ref_33
        - Workspace Brand
    get:
      operationId: WorkspaceBrandController_getWorkspaceBrands
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReadBrandDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_32
      summary: This endpoint returns brands assigned to a workspace.
      tags: *ref_33
  /account-management/workspace/{workspaceId}/brand/{brandId}:
    delete:
      operationId: WorkspaceBrandController_removeBrandFromWorkspace
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The id of the workspace
          schema:
            type: number
        - name: brandId
          required: true
          in: path
          description: The id of the brand
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/WorkspaceBrandMapDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_32
      summary: Remove a Brand from a workspace.
      tags: *ref_33
  /account-management/organization/{organizationId}/brand/{brandId}/brand-identifier:
    post:
      operationId: BrandIdentifierController_createBrandIdentifier
      parameters:
        - name: brandId
          required: true
          in: path
          description: The id of the brand to create a brand identifier for.
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          description: The id of the organization to create a brand identifier for.
          schema:
            type: string
        - name: identifier
          required: true
          in: path
          description: The Brand Identifier description
          schema: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBrandIdentifierDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/CreateBrandIdentifierResultDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_34
        - Bearer Token: []
      summary: Create a new Brand Identitifer.
      tags: &ref_35
        - Brand Identifier
  /account-management/organization/{organizationId}/brand/{brandId}/brand-identifier/{id}:
    delete:
      operationId: BrandIdentifierController_softDeleteBrandIdentifierById
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization to update a brand identifier for.
          schema:
            type: string
        - name: brandId
          required: true
          in: path
          description: The id of the brand Identifier
          schema:
            type: string
        - name: id
          required: true
          in: path
          description: The id of the brand Identifier to be soft deleted.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/DeleteBrandIdentifierDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_34
      summary: Soft Delete a Brand Identifier by id
      tags: *ref_35
    put:
      operationId: BrandIdentifierController_updateBrandIdentifier
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization to update a brand identifier for.
          schema:
            type: string
        - name: brandId
          required: true
          in: path
          description: The id of the brand Identifier
          schema:
            type: string
        - name: id
          required: true
          in: path
          schema:
            type: string
        - name: identifier
          required: true
          in: path
          description: Description of the brand identifier to update.
          schema: {}
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBrandIdentifierDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/UpdateBrandIdentifierDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_34
      summary: Update a Brand Identifier by id
      tags: *ref_35
  /account-management/ad-account/filters:
    get:
      operationId: AdAccountController_getAvailableFiltersForAdAccounts
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_36
        - Bearer Token: []
      tags: &ref_37
        - Ad Account
  /account-management/ad-account/organization/{organizationId}/platform/{platform}/platform-permission:
    post:
      operationId: AdAccountController_updatePlatformPermissions
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: platform
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateBulkPlatformPermissionsDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      tags: *ref_37
  /account-management/ad-account/{adAccountId}/platform-permission:
    post:
      operationId: AdAccountController_updateAccountPlatformPermissions
      parameters:
        - name: adAccountId
          required: true
          in: path
          description: The id of the Ad Account
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateAccountPlatformPermissionDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_36
      summary: Update the platform permissions of an ad account.
      tags: *ref_37
  /account-management/ad-account/{adAccountId}/organizations:
    get:
      operationId: AdAccountController_findOrganizationsByAdAccountId
      parameters:
        - name: adAccountId
          required: true
          in: path
          description: The id of the Ad Account
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      summary: Find all the organizations that have the ad account mapped to them.
      tags: *ref_37
  /account-management/ad-account/import/platform/{platform}/organization-users:
    get:
      operationId: AdAccountController_getOrganizationUsersPerPlatform
      parameters:
        - name: platform
          required: true
          in: path
          description: platform name
          schema:
            type: string
        - name: importV3EnabledOnly
          required: true
          in: query
          description: import v3 enabled only
          schema:
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      summary: Get list of all organizations with users for a platform.
      tags: *ref_37
  /account-management/ad-account/import/platform/{platform}/account/{adAccountId}:
    get:
      deprecated: true
      operationId: AdAccountController_getPlatformAdAccountWithImportInfo
      parameters:
        - name: platform
          required: true
          in: path
          description: platform name
          schema:
            type: string
        - name: adAccountId
          required: true
          in: query
          description: ad account id
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      summary: Get ad account information along with import info
      tags: *ref_37
  /account-management/ad-account/import/organization/{organizationId}/platform/{platform}/account/{adAccountId}:
    get:
      operationId: AdAccountController_getOrganizationPlatformAdAccountWithImportInfo
      parameters:
        - name: organizationId
          required: true
          in: path
          description: organization id
          schema:
            type: string
        - name: platform
          required: true
          in: path
          description: platform name
          schema:
            type: string
        - name: adAccountId
          required: true
          in: query
          description: ad account id
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      summary: Get organization ad account information along with import info
      tags: *ref_37
  /account-management/ad-account/import/account/{adAccountId}/failure:
    post:
      deprecated: true
      operationId: AdAccountController_savePlatformAdAccountSequentialFailure
      parameters:
        - name: adAccountId
          required: true
          in: path
          description: ad account id
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      summary: Save platform ad account sequential failure record
      tags: *ref_37
    delete:
      deprecated: true
      operationId: AdAccountController_deleteSequentialFailuresByPlatformAdAccountId
      parameters:
        - name: adAccountId
          required: true
          in: path
          description: ad account id
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_36
      summary: Delete all sequential failures for a platform ad account
      tags: *ref_37
  /account-management/workspace/{workspaceId}/ad-account:
    get:
      operationId: WorkspaceAdAccountController_findAllAdAccountsForAWorkspace
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: >-
            The workspace ID represents the unique identifier of the workspace
            to which all ad accounts belong.
          schema:
            type: number
        - name: search
          required: true
          in: query
          description: The search param to filter ad accounts by name, id or channel.
          schema:
            example: '?search=name'
            type: string
        - name: sortBy
          required: true
          in: query
          description: The sort by string to determine the field to sort results.
          schema:
            example: '?sortBy=platform, dateCreated, platformAccountName (default)'
            type: string
        - name: sortOrder
          required: true
          in: query
          description: The sort by order.
          schema:
            example: '?sortOrder=ASC - ASC(default) or DESC.'
            type: string
        - name: brands
          required: false
          in: query
          description: Brands string
          schema:
            example: none
            type: string
        - name: importStatus
          required: false
          in: query
          description: ImportStatus string
          schema:
            example: Suspended,Not Importing
            type: string
        - name: markets
          required: false
          in: query
          description: Markets string
          schema:
            example: none
            type: string
        - name: connectionStatus
          required: false
          in: query
          description: Connection Status string
          schema:
            example: Disconnected,Connected
            type: string
        - name: workspaces
          required: false
          in: query
          description: Workspaces string
          schema:
            example: none
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: >-
                            #/components/schemas/ReadPlatformAdAccountWithRebuildInfoDto
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_38
        - Bearer Token: []
      tags: &ref_39
        - Ad Account
    post:
      operationId: WorkspaceAdAccountController_configureWorkspaceAdAccounts
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: System assigned workspace id
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/String'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_38
      summary: Maps Platform Ad Accounts to Workspace.
      tags: *ref_39
  /account-management/media/{mediaId}/organizations:
    get:
      operationId: MediaController_findOrganizationsByMediaId
      parameters:
        - name: mediaId
          required: true
          in: path
          description: The id of the Media
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security:
        - Bearer Token: []
      summary: >-
        Find all the organizations that are mapped to ad accounts related to
        media.
      tags:
        - Media
  /account-management/organization:
    get:
      operationId: OrganizationController_findAllByUser
      parameters:
        - name: search
          required: false
          in: query
          description: Search string on the organization name or id
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
        - name: userId
          required: true
          in: path
          description: ID of the User
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: >-
                            #/components/schemas/OrganizationWithWorkspaceCountDto
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_40
        - Bearer Token: []
      summary: >-
        Notice for GET findAllByUser():


        We will not use permissions for this particular endpoint because the
        organizations list is a top-level structure.

        However, when accessing the organization service, the user's userId
        should be associated

        with the 'organizationPersonRole' table with roles ORG_ADMIN or
        ORG_STANDARD.

        If not, the service will return an error message indicating that the
        user does not have

        permission to access the data.


        Note: Other endpoints in this controller might have different permission
        requirements.
      tags: &ref_41
        - Organization
  /account-management/organization/{organizationId}/whitelist-features:
    get:
      operationId: OrganizationController_getOrganizationWhitelistFeatures
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: features
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_40
      tags: *ref_41
  /account-management/organization/{organizationId}/ad-account:
    get:
      operationId: OrganizationAdAccountController_findAdAccountsByOrganizationId
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: workspaceId
          required: true
          in: query
          schema:
            type: number
        - name: search
          required: false
          in: query
          description: >-
            Search string to filter ad accounts from organizations and
            workspaces
          schema:
            example: account_id
            type: string
        - name: sortBy
          required: false
          in: query
          description: Sort by string
          schema:
            example: account_id
            type: string
        - name: sortOrder
          required: false
          in: query
          description: Sort Order string
          schema:
            example: ASC
            type: string
        - name: brands
          required: false
          in: query
          description: Brands string
          schema:
            example: none
            type: string
        - name: importStatus
          required: false
          in: query
          description: ImportStatus string
          schema:
            example: Suspended,Not Importing
            type: string
        - name: markets
          required: false
          in: query
          description: Markets string
          schema:
            example: none
            type: string
        - name: connectionStatus
          required: false
          in: query
          description: Connection Status string
          schema:
            example: Disconnected,Connected
            type: string
        - name: workspaces
          required: false
          in: query
          description: Workspaces string
          schema:
            example: none
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: >-
                            #/components/schemas/ReadPlatformAdAccountWithRebuildInfoDto
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_42
        - Bearer Token: []
      summary: Find all the ad accounts in an organization.
      tags: &ref_43
        - Ad Account
  /account-management/organization/{organizationId}/ad-account/{adAccountId}/workspaces:
    get:
      operationId: OrganizationAdAccountController_getWorkspacesByOrganizationAndAdAccount
      parameters:
        - name: adAccountId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
        - name: AdAccountId
          required: true
          in: path
          description: The id of an ad account
          schema: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_42
      summary: Get all workspaces by organization and an ad account.
      tags: *ref_43
  /account-management/organization/{organizationId}/ad-account/{platform}/ad-account-permissions:
    get:
      operationId: >-
        OrganizationAdAccountController_getOrganizationPlatformAdAccountsWithPermissions
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: platform
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: >-
                            #/components/schemas/ReadAccountPlatformPermissionsDto
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_42
      summary: Get all accounts with permissions by user, organization and platform.
      tags: *ref_43
  /account-management/organization/{organizationId}/ad-account/{platform}/user/{userId}/ad-account-permissions:
    get:
      operationId: >-
        OrganizationAdAccountController_getUserOrganizationPlatformAdAccountsWithPermissions
      parameters:
        - name: userId
          required: true
          in: path
          description: The user id on whose behalf the request is being made
          schema:
            type: number
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: platform
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: >-
                            #/components/schemas/ReadAccountPlatformPermissionsDto
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_42
      summary: >-
        Get all accounts with permissions on behalf user, organization and
        platform for import purposes.
      tags: *ref_43
  /account-management/organization/{organizationId}/workspaces/ad-account:
    post:
      operationId: OrganizationAdAccountController_fetchConnectedAdAccounts
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WorkspacesAdAccountRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_42
      tags: *ref_43
  /account-management/organization/{organizationId}/ad-account/markets:
    post:
      operationId: OrganizationAdAccountController_fetchMarketsForAdAccounts
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReadAdAccountMarketsRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_42
      tags: *ref_43
  /account-management/organization/{organizationId}/ad-accounts/industry-group:
    post:
      operationId: OrganizationAdAccountController_mapIndustryGroupToAdAccounts
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIndustryAdAccountsRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_42
      tags: *ref_43
  /account-management/organization/{organizationId}/ad-accounts/industry-group/{industryGroupId}/industry:
    post:
      operationId: OrganizationAdAccountController_mapIndustryToAdAccounts
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: industryGroupId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIndustryAdAccountsRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_42
      tags: *ref_43
  /account-management/organization/{organizationId}/ad-accounts/industry/{industryId}/sub-industry:
    post:
      operationId: OrganizationAdAccountController_mapSubIndustryToAdAccounts
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: industryId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateIndustryAdAccountsRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_42
      tags: *ref_43
  /account-management/organization/{organizationId}/platform/{platform}/account/{adAccountId}:
    post:
      operationId: OrganizationAdAccountController_updatePlatformAdAccount
      parameters:
        - name: organizationId
          required: true
          in: path
          description: organization id
          schema:
            type: string
        - name: platform
          required: true
          in: path
          description: platform name
          schema:
            type: string
        - name: adAccountId
          required: true
          in: path
          description: ad account id
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_42
      summary: Update ad account upon import completion
      tags: *ref_43
  /account-management/workspace/all:
    get:
      operationId: WorkspaceAllController_findUserWorkspacesForAllOrgs
      parameters:
        - name: organizationId
          required: false
          in: query
          description: System assigned id of the VidMob organization
          schema:
            type: string
        - name: feature
          required: false
          in: query
          description: >-
            Workspace feature identifiers to filter by. Example
            "BRAND-GOVERNANCE,CREATIVE-INTELLIGENCE"
          schema:
            type: string
      responses:
        '200':
          description: Returns all workspaces for user.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/AllUserWorkspaceDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - Bearer Token: []
      summary: Returns all workspaces for a user across all orgs
      tags:
        - All Workspace
  /account-management/organization/{organizationId}/industry-groups:
    get:
      operationId: IndustryController_getIndustryGroups
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Returns all industry groups.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/IndustryResponseDTO'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_44
        - Bearer Token: []
      tags: &ref_45
        - Industry
  /account-management/organization/{organizationId}/industry-groups/{industryGroupId}/industries:
    get:
      operationId: IndustryController_getIndustries
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: industryGroupId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: Returns all industries based on industry group id.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/IndustryResponseDTO'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_44
      tags: *ref_45
  /account-management/organization/{organizationId}/industries/{industryId}/sub_industries:
    get:
      operationId: IndustryController_getSubIndustries
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: industryId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: Returns all sub-industries based on industry id.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/IndustryResponseDTO'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_44
      tags: *ref_45
  /account-management/feature/organization/{organizationId}/whitelist:
    post:
      operationId: FeaturesController_handleOrganizationFeatureWhitelist
      parameters:
        - name: organizationId
          required: true
          in: path
          description: organization id
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrganizationFeatureWhitelistDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security:
        - Bearer Token: []
      summary: Enable or disable a feature whitelist for an organization.
      tags:
        - Organization
  /account-management/organization/{organizationId}/user/all:
    get:
      operationId: OrganizationUserController_getAllUsers
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_46
        - Bearer Token: []
      summary: >-
        Lightweight endpoint to load all users within an organization.

        The purpose of this endpoint is to provide a fast query to retrieve all
        users, without much querying.
      tags: &ref_47
        - Organization User
  /account-management/organization/{organizationId}/user:
    get:
      operationId: OrganizationUserController_findAll
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: search
          required: true
          in: query
          description: Search string on display name or username of user
          schema:
            type: string
        - name: filterBy
          required: true
          in: query
          description: >-
            filter the results whether for vidmob employees or no vidmob
            employees. Example: VIDMOB_ONLY or NO_VIDMOB
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/OrganizationUserResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_46
      summary: Return all users for the organization.
      tags: *ref_47
  /account-management/organization/{organizationId}/user/download-csv:
    get:
      operationId: OrganizationUserController_downloadCsv
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: filterBy
          required: true
          in: query
          description: >-
            filter the results whether for vidmob employees or no vidmob
            employees. Example: VIDMOB_ONLY or NO_VIDMOB
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_46
      summary: Return a .csv file for all users for the organization.
      tags: *ref_47
  /account-management/organization/{organizationId}/user/{id}:
    put:
      operationId: OrganizationUserController_update
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: id
          required: true
          in: path
          description: User ID to update on the organization
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOrganizationUserDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/OrganizationUserResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_46
      summary: Update roles of a user on an organization.
      tags: *ref_47
    delete:
      operationId: OrganizationUserController_remove
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: id
          required: true
          in: path
          description: User ID to update on the organization
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/OrganizationUserResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_46
      summary: Remove a user from an organization.
      tags: *ref_47
  /account-management/noauth/organization/{organizationId}/invite/{inviteCode}:
    get:
      operationId: OrganizationInviteController_getOrganizationInviteByInviteCode
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization to invite users
          schema:
            type: string
        - name: inviteCode
          required: true
          in: path
          description: The invitation code
          schema:
            type: string
        - name: validationCode
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_48
        - Bearer Token: []
      summary: >-
        Public facing endpoint to retrieve an organization invite.

        This endpoint needs to be public because a user might not be logged or
        not even have an account yet.
      tags: &ref_49
        - Organization Invite
  /account-management/organization/{organizationId}/invite:
    post:
      operationId: OrganizationInviteController_bulkInviteMultipleUsersToMultipleWorkspaces
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBulkUserWorkspaceDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_48
      summary: >-
        It bulk maps multiple users to multiple workspaces with a predefined
        role.
      tags: *ref_49
    get:
      operationId: OrganizationInviteController_getPendingInvites
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: search
          required: false
          in: query
          description: Search string to filter organization invites by email
          schema:
            type: string
        - name: sortBy
          required: false
          in: query
          description: Sort by string
          schema:
            example: lastUpdated
            type: string
        - name: sortOrder
          required: false
          in: query
          description: Sort Order string
          schema:
            example: ASC
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_48
      summary: >-
        It returns a paginated response with all the organization pending
        invitations.
      tags: *ref_49
  /account-management/organization/{organizationId}/invite/download-csv:
    get:
      operationId: OrganizationInviteController_downloadInvitesCsv
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_48
      summary: Return a .csv file for all pending invites for the organization.
      tags: *ref_49
  /account-management/organization/{organizationId}/invite/{organizationInviteId}:
    delete:
      operationId: OrganizationInviteController_cancelOrganizationInvite
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: organizationInviteId
          required: true
          in: path
          description: Id of the Organization invite that will be revoked
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_48
      summary: It sets the organization invite status as revoked.
      tags: *ref_49
    patch:
      operationId: OrganizationInviteController_resendOrganizationInvite
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: organizationInviteId
          required: true
          in: path
          description: Id of the Organization invite that will be revoked
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_48
      summary: It resents the organization invite.
      tags: *ref_49
  /account-management/organization/{organizationId}/invite/{organizationInviteId}/accept:
    patch:
      operationId: OrganizationInviteController_acceptOrganizationInvite
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Id of the Organization
          schema:
            type: string
        - name: organizationInviteId
          required: true
          in: path
          description: Id of the Organization invite that will be revoked
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_48
      summary: >-
        It will handle the accept organization invite.

        Since this request will come from an user which is not part of
        organization, there is no permissions we can check
      tags: *ref_49
  /account-management/workspace/{workspaceId}/user:
    get:
      operationId: WorkspaceUserController_findAll
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: Id of the workspace
          schema:
            type: number
        - name: filterBy
          required: true
          in: query
          description: >-
            filter the results whether for vidmob employees or no vidmob
            employees. Example: VIDMOB_ONLY or NO_VIDMOB
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/Number'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security:
        - Bearer Token: []
      summary: Return all users for the workspace.
      tags:
        - workspace-user
  /account-management/noauth/sso/login:
    get:
      operationId: SsoController_redirectToState
      parameters:
        - name: state
          required: true
          in: query
          description: >-
            a string which contain either the url to redirect the user or a json
            base64 encoded organization invite
          schema:
            type: string
        - name: code
          required: true
          in: query
          description: The cognito authentication code
          schema:
            type: string
      responses:
        '200':
          description: ''
      summary: >-
        This endpoint will be called by Cognito Hosted Login via callback,

        and will redirect to a web client via state parameter, passing the
        cognito

        authorization code via code query string.
      tags: &ref_50
        - SSO
  /account-management/noauth/sso/option:
    get:
      operationId: SsoController_getUserSsoOptions
      parameters:
        - name: email
          required: true
          in: query
          description: user email
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: >-
        Returns the SSO options for the organization domain related to user
        email domain
      tags: *ref_50
  /account-management/noauth/sso/session:
    post:
      operationId: SsoController_createSsoSession
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: >-
        Endpoint responsible to exchange cognito auth code for sessionId,
        accessToken and expiresIn
      tags: *ref_50
  /account-management/sso/organization/{organizationId}/configuration:
    post:
      operationId: SsoController_createSsoConfiguration
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: >-
        It creates a sso configuration for a particular organization_domain

        In order to create an SSO configuration we need to make sure there is an
        organization domain set to be used,

        also an SSO configuration mustn't not exists for that particular
        organization domain.


        To create the SSO configuration:
         1 - Create the Cognito Identity Provider on Cognito;
         2 - Update the Cognito User pool and Client Hosted UX to enable the just created Cognito IdP;
         3 - Create a SSO configuration within the database.

        If anything goes wrong on the create sso configuration, rollbacks the
        changes made on Cognito.
      tags: *ref_50
    get:
      operationId: SsoController_listSsoConfiguration
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: >-
        Return a paginated response containing all sso configurations an
        organization has
      tags: *ref_50
  /account-management/sso/organization/{organizationId}/configuration/{configurationId}:
    get:
      operationId: SsoController_getSsoConfiguration
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: configurationId
          required: true
          in: path
          description: The id of the ssoConfiguration
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_50
    patch:
      operationId: SsoController_updateSsoConfiguration
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: configurationId
          required: true
          in: path
          description: The id of the ssoConfiguration
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_50
    delete:
      operationId: SsoController_deleteSsoConfiguration
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: configurationId
          required: true
          in: path
          description: The id of the ssoConfiguration
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: >-
        Endpoint responsible to delete an sso configuration and remove the
        cognito IdP
      tags: *ref_50
  /leaderboard/organization/{organizationId}:
    post:
      operationId: LeaderboardController_getLeaderboardDataForOrganization
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization for workspaces in request to fetch creative
            performance for
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLeaderboardRequestDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/LeaderboardItemDto'
        '201':
          description: ''
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_51
        - Bearer Token: []
      summary: >-
        Get ranked performance creatives in workspaces compared to previous week
        leaderboard for specific KPI

        will return a maximum of 1000 results, with 20 or less creatives from
        each ad account
      tags: &ref_52
        - Leaderboard
  /leaderboard/organization/{organizationId}/status:
    post:
      operationId: >-
        LeaderboardController_getLeaderboardAccountConnectionStatusForOrganization
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization for workspaces in request to fetch creative
            performance for
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLeaderboardRequestDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/LeaderboardAccountInfoDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LeaderboardAccountInfoDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_51
      summary: >-
        Return information on the state of the leaderboard based on the user's
        workspace access to ad accounts
      tags: *ref_52
  /criteria-group-report/organization/{organizationId}/details:
    post:
      operationId: CriteriaPerformanceReportController_getCriteriaGroupDetailsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization for workspaces in request to fetch criteria
            performance for
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupedCriteriaDetailsRequestDto'
      responses:
        '201':
          description: ''
      summary: Get details for criteria, grouped by criteria attributes
      tags: &ref_53
        - CriteriaPerformanceReport
  /criteria-group-report/organization/{organizationId}:
    post:
      operationId: CriteriaPerformanceReportController_getCriteriaGroupReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: >-
            The organization for workspaces in request to fetch criteria
            performance for
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CriteriaPerformanceRequestDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/CriteriaPerformanceDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      summary: >-
        Get performance for criteria, grouped by criteria attributes, in
        workspaces, ad accounts, and for KPIs
      tags: *ref_53
  /analytics/platform-media/search:
    post:
      operationId: ElementPresenceController_searchElementPresence
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: &ref_54
        - Element Presence
  /analytics/element-presence/media:
    post:
      operationId: ElementPresenceController_searchMediaByElementTag
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_54
  /analytics/organization/{organizationId}/element-flag:
    post:
      operationId: ElementFlagController_createFlag
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateElementFlagDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: Creates a new element flag for the specified media.
      tags: &ref_55
        - Element Flag
  /analytics/organization/{organizationId}/element-flag/media/{mediaId}:
    get:
      operationId: ElementFlagController_getFlagsByMedia
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ReadElementFlagResponseDto'
      summary: >-
        Retrieves all element flags for a given mediaId within a specified
        organization.
      tags: *ref_55
  /custom-audience:
    get:
      operationId: CustomAudienceController_findCustomAudiencesForAccount
      parameters:
        - name: platform
          required: true
          in: query
          description: Platform name
          schema:
            enum:
              - facebook
              - snapchat
              - tiktok
              - pinterest
              - dv360
              - linkedin
              - twitter
              - reddit
              - adwords
              - amazonadvertising
              - amazonadvertisingdsp
              - instagrampage
              - facebookpage
            type: string
        - name: accountId
          required: true
          in: query
          description: Platform Account Id or Platform Account Group Id
          schema:
            type: string
        - name: name
          required: false
          in: query
          description: Custom audience name keyword
          schema:
            type: string
        - name: accountType
          required: true
          in: query
          description: >-
            Account type to know if custom audiences are get for an account or
            an account group
          schema:
            enum:
              - ACCOUNT
              - ACCOUNT_GROUP
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReadCustomAudienceDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_56
        - Bearer Token: []
      summary: This endpoint returns all custom audiences for the account
      tags: &ref_57
        - Custom Audience
  /custom-audience/{customAudienceId}:
    get:
      operationId: CustomAudienceController_findCustomAudience
      parameters:
        - name: customAudienceId
          required: true
          in: path
          description: Custom Audience Id
          schema:
            type: string
        - name: platform
          required: true
          in: query
          description: Platform name
          schema:
            enum:
              - facebook
              - snapchat
              - tiktok
              - pinterest
              - dv360
              - linkedin
              - twitter
              - reddit
              - adwords
              - amazonadvertising
              - amazonadvertisingdsp
              - instagrampage
              - facebookpage
            type: string
        - name: accountId
          required: true
          in: query
          description: Platform Account Id
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadCustomAudienceDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_56
      summary: This endpoint returns a custom audience for the account
      tags: *ref_57
  /custom-audience/{customAudienceId}/additional-audience:
    get:
      operationId: CustomAudienceController_findAdditionalCustomAudiences
      parameters:
        - name: customAudienceId
          required: true
          in: path
          description: Custom Audience Id
          schema:
            type: string
        - name: platform
          required: true
          in: query
          description: Platform name
          schema:
            enum:
              - facebook
              - snapchat
              - tiktok
              - pinterest
              - dv360
              - linkedin
              - twitter
              - reddit
              - adwords
              - amazonadvertising
              - amazonadvertisingdsp
              - instagrampage
              - facebookpage
            type: string
        - name: accountId
          required: true
          in: query
          description: Platform Account Id
          schema:
            type: string
        - name: targetingStatus
          required: true
          in: query
          description: Custom Audience targeting status
          schema:
            enum:
              - INCLUDED
              - EXCLUDED
            type: string
        - name: startDate
          required: true
          in: query
          description: >-
            Start date to get additional custom audiences for a single custom
            audience, format yyyy-mm-dd
          schema:
            type: string
        - name: endDate
          required: true
          in: query
          description: >-
            End date to get additional custom audiences for a single custom
            audience, format yyyy-mm-dd
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReadCustomAudienceDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_56
      summary: >-
        This endpoint returns additional custom audiences of a custom audience
        for an account
      tags: *ref_57
  /normative-performance:
    post:
      operationId: NormativePerformanceController_getNormativePerformance
      parameters:
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/NormativePerformanceRequestDto'
      responses:
        '200':
          description: Return a list of normative performance for given kpis
        '201':
          description: ''
      security: &ref_58
        - Bearer Token: []
      tags: &ref_59
        - Normative Performance
  /normative-performance/scopes:
    get:
      operationId: NormativePerformanceController_getAnalyticsNormativeScopes
      parameters: []
      responses:
        '200':
          description: Return the date range options for analytics normative performance
      security: *ref_58
      tags: *ref_59
  /analytics/organization/{organizationId}/platformMediaGroup:
    post:
      operationId: PlatformMediaGroupController_createPlatformMediaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlatformMediaGroupCreateDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_60
        - Bearer Token: []
      tags: &ref_61
        - Platform Media Group
  /analytics/organization/{organizationId}/platformMediaGroup/all:
    post:
      operationId: PlatformMediaGroupController_getPlatformMediaGroupsByAdAccountIds
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlatformMediaGroupRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_60
      tags: *ref_61
  /analytics/organization/{organizationId}/platformMediaGroup/{platformMediaGroupId}:
    post:
      operationId: PlatformMediaGroupController_getPlatformMediaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: platformMediaGroupId
          required: true
          in: path
          description: The id of the platform media group
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlatformMediaGroupIndividualRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_60
      tags: *ref_61
    delete:
      operationId: PlatformMediaGroupController_deletePlatformMediaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: platformMediaGroupId
          required: true
          in: path
          description: The id of the platform media group
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_60
      tags: *ref_61
  /analytics/organization/{organizationId}/platformMediaGroup/{platformMediaGroupId}/update:
    post:
      operationId: PlatformMediaGroupController_updatePlatformMediaGroup
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization
          schema:
            type: string
        - name: platformMediaGroupId
          required: true
          in: path
          description: The id of the platform media group
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlatformMediaGroupUpdateDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_60
      tags: *ref_61
  /dimensions/organization/{organizationId}:
    post:
      operationId: DimensionsController_getDimensions
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Organization ID to query dimensions for
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_62
        - Bearer Token: []
      summary: Endpoint to query for advideo or ad dimensions and metrics data
      tags: &ref_63
        - Dimensions
  /dimensions/organization/{organizationId}/media-decay:
    post:
      operationId: DimensionsController_getMediaDecay
      parameters:
        - name: organizationId
          required: true
          in: path
          description: Organization ID to query dimensions for
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_62
      tags: *ref_63
  /custom-element-set/organization/{organizationId}:
    post:
      operationId: CustomElementSetController_createCustomElementSet
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization to which the custom element set belongs
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: |-
        Save a custom element set to mysql and snowflake media tag tables
        See soa-analytics-service-sdk for the request and response types
      tags: &ref_64
        - Custom Element Set
  /custom-element-set/organization/{organizationId}/customElementSet/{customElementSetId}:
    post:
      operationId: CustomElementSetController_updateCustomElementSet
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization to which the custom element set belongs
          schema:
            type: string
        - name: customElementSetId
          required: true
          in: path
          description: The id of element set to be fetched
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: |-
        Update single custom element set for organization and platform
        See soa-analytics-service-sdk for the request and response types
      tags: *ref_64
    get:
      operationId: CustomElementSetController_getCustomElementSetById
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization to which the custom element set belongs
          schema:
            type: string
        - name: customElementSetId
          required: true
          in: path
          description: The id of element set to be fetched
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: |-
        Get single custom element set by id
        See soa-analytics-service-sdk for the request and response types
      tags: *ref_64
  /custom-element-set/organization/{organizationId}/platform/{platform}/customElementSet/{customElementSetId}:
    delete:
      operationId: CustomElementSetController_deleteCustomElementSet
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization to which the custom element set belongs
          schema:
            type: string
        - name: platform
          required: true
          in: path
          description: The platform for which the custom element set is created
          schema:
            type: string
        - name: customElementSetId
          required: true
          in: path
          description: The id of element set to be fetched
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: |-
        Delete single custom element set for organization and platform
        See soa-analytics-service-sdk for the request and response types
      tags: *ref_64
  /custom-element-set/organization/{organizationId}/platform/{platform}:
    get:
      operationId: CustomElementSetController_getAllCustomElementSets
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization to which the custom element set belongs
          schema:
            type: string
        - name: platform
          required: true
          in: path
          description: The platform for which the custom element set was created
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      summary: |-
        Get all custom element sets for ad accounts
        See soa-analytics-service-sdk for the request and response types
      tags: *ref_64
  /notifications/mailchimp/webhook:
    post:
      operationId: NotificationsController_proxyMailchimpWebhook
      parameters:
        - name: X-Mandrill-Signature
          required: true
          in: header
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security:
        - Bearer Token: []
      summary: >-
        Proxy Mailchimp webhook to Notification-Service, as-is.


        Note: it has no auth at all.

        Mailchimp payload validity is checked by the Notification-Service by
        X-Mandrill-Signature.


        Note: no versioning.

        Endpoint formats are up to Mailchimp, not BFF.
      tags:
        - Notifications
  /fe-constants/platform-logo:
    get:
      description: Fetches an array of platform logo URLs for use in frontend applications.
      operationId: FeConstantsController_getPlatformLogo
      parameters: []
      responses:
        '200':
          description: Returns an array of platform logo URLs
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PlatformLogoResponseDto'
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Not Found
        '500':
          description: Internal server error retrieving platform logos
      security:
        - Bearer Token: []
      summary: Retrieve platform logos
      tags:
        - Frontend Constants
  /metadata/industries:
    get:
      operationId: PlatformMetadataController_getIndustries
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IndustryWithSubIndustriesDto'
      security: &ref_65
        - Bearer Token: []
      summary: >-
        return all parent industries available in Vidmob and their sub
        industries.
      tags: &ref_66
        - Platform Metadata
  /metadata/regions:
    get:
      operationId: PlatformMetadataController_getRegions
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RegionWithCountriesDto'
      security: *ref_65
      summary: return all regions and their markets.
      tags: *ref_66
  /insights/create:
    post:
      operationId: InsightsController_createInsights
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security:
        - Bearer Token: []
      tags:
        - Insights v1 (Replaced by /insight)
  /organization/{organizationId}/insight/create:
    post:
      operationId: InsightController_createCopilotInsights
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: &ref_67
        - Bearer Token: []
      tags: &ref_68
        - Insight
  /organization/{organizationId}/insight:
    post:
      operationId: InsightController_createInsight
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_67
      summary: |-
        Create insight endpoint.
        This V2 endpoint will create the insight directly on MySQL database.
      tags: *ref_68
  /organization/{organizationId}/insight/all:
    post:
      operationId: InsightController_getListOfInsights
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_67
      summary: |-
        List insights endpoint.
        This endpoint will list insights from MySQL database.
      tags: *ref_68
  /organization/{organizationId}/insight/workspace/{workspaceId}/all/with-folder-permission:
    post:
      operationId: InsightController_getListOfInsightsWithFolderPermission
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_67
      summary: |-
        List insights endpoint.
        This endpoint will list insights from MySQL database.
      tags: *ref_68
  /organization/{organizationId}/insight/{insightId}/detail:
    get:
      operationId: InsightController_getIndividualInsight
      parameters:
        - name: insightId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: activeWorkspaceId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_67
      summary: >-
        Obtains a single insight. This endpoint will get a single insight from
        MySQL database.
      tags: *ref_68
  /organization/{organizationId}/insight/{insightId}:
    patch:
      operationId: InsightController_updateInsight
      parameters:
        - name: insightId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInsightRequestDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_67
      tags: *ref_68
    delete:
      operationId: InsightController_deleteInsight
      parameters:
        - name: insightId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_67
      tags: *ref_68
  /organization/{organizationId}/insight/{insightId}/favorite:
    post:
      operationId: InsightController_favoriteInsight
      parameters:
        - name: insightId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FavoriteInsightRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_67
      tags: *ref_68
  /organization/{organizationId}/insight/generate-title:
    post:
      operationId: InsightController_generateInsightTitle
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateInsightTitleRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateInsightTitleResponseDto'
      security: *ref_67
      tags: *ref_68
  /insight/create:
    post:
      operationId: InsightV2Controller_createInsightsV2
      parameters: []
      responses:
        '201':
          description: ''
      security:
        - Bearer Token: []
      tags:
        - Insight v2 (Replaced by /copilot-insight)
  /organization/{organizationId}/project-insight:
    post:
      operationId: ProjectInsightController_createProjectInsight
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProjectInsightRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_69
        - Bearer Token: []
      summary: >-
        Creates a mapping between a project and an insight, verifying the user's
        permissions

        to modify the project and access the insight.


        This method first checks if the user has modify permissions on the
        provided projects.

        Then, it verifies if the user has the necessary permissions to access
        the insight.

        If both checks pass, the project insight mapping is created.
      tags: &ref_70
        - Project Insight
  /organization/{organizationId}/project-insight/bulk:
    post:
      operationId: ProjectInsightController_createBulkProjectInsights
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_69
      tags: *ref_70
  /organization/{organizationId}/project-insight/projectId/{projectId}:
    get:
      operationId: ProjectInsightController_getProjectInsightsByProject
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: projectId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_69
      summary: >-
        Retrieves insights for a specific project within an organization.


        This endpoint fetches project insights based on the provided project ID
        and organization ID. It also ensures that the user making the request
        has the necessary permissions to view project insights.

        The response is paginated, with options for controlling the offset and
        number of results per page.
      tags: *ref_70
  /organization/{organizationId}/project-insight/projectId/{projectId}/insightId/{insightId}:
    get:
      operationId: ProjectInsightController_getProjectInsightByProject
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: projectId
          required: true
          in: path
          schema:
            type: number
        - name: insightId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_69
      summary: >-
        Checks if the user has the necessary permissions to read the project
        insight details.

        This function is invoked when a GET request is made to the endpoint

        `/projectId/:projectId/insightId/:insightId`, where `:projectId` and
        `:insightId`

        are placeholders for the project and insight IDs respectively. The user
        must have the

        `readProjectInsightProjectLevel` permission to access this endpoint.
      tags: *ref_70
  /organization/{organizationId}/project-insight/insight/{insightId}/projectId/{projectId}:
    delete:
      operationId: ProjectInsightController_deleteProjectInsight
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: insightId
          required: true
          in: path
          schema:
            type: string
        - name: projectId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_69
      summary: >-
        Deletes a single mapping between a project and an insight, verifying the
        user's permissions

        to modify the project and access the insight.


        This method checks if the user has modify permissions on the provided
        project.

        Then, it verifies if the user has the necessary permissions to access
        the insight.

        If both checks pass, the project insight mapping is deleted.
      tags: *ref_70
  /organization/{organizationId}/copilot-insight:
    post:
      operationId: CopilotInsightsController_createCopilotInsightsLinkedToMongo
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCopilotInsightRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CreateCopilotInsightResponseDto'
      security:
        - Bearer Token: []
      tags:
        - Copilot Insight
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders:
    post:
      operationId: InsightFolderController_createInsightFolder
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInsightFolderRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_71
        - Bearer Token: []
      tags: &ref_72
        - Insight Folders
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/{folderId}:
    patch:
      operationId: InsightFolderController_updateInsightFolder
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: folderId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_71
      tags: *ref_72
    delete:
      operationId: InsightFolderController_deleteInsightFolder
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: folderId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/{folderId}/favorite:
    post:
      operationId: InsightFolderController_favoriteInsightFolder
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: folderId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FavoriteInsightFolderRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: string
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/list:
    get:
      operationId: InsightFolderController_findFoldersForParent
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: parentFolderId
          required: true
          in: query
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/list-filtered:
    post:
      operationId: InsightFolderController_findFoldersFiltered
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListFilteredInsightFolderRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/hierarchy:
    get:
      operationId: InsightFolderController_findFolderHierarchy
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/insights:
    get:
      operationId: InsightFolderController_findInsightsForParentFolder
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: parentFolderId
          required: true
          in: query
          schema:
            type: string
        - name: sortOrder
          required: true
          in: query
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/move-insight:
    post:
      operationId: InsightFolderController_moveInsights
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MoveInsightRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_71
      tags: *ref_72
  /organization/{organizationId}/workspace/{workspaceId}/insight-folders/move-folder:
    post:
      operationId: InsightFolderController_moveFolder
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MoveFolderRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_71
      tags: *ref_72
  /insight-permission/organization/{organizationId}/workspace/{workspaceId}:
    get:
      operationId: InsightPermissionController_getSharedInsightPermissions
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: insightId
          required: true
          in: query
          schema:
            type: string
        - name: folderId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security:
        - Bearer Token: []
      summary: Fetches the user's access permissions for a specific insight or folder.
      tags:
        - Insight Permission
  /service-status:
    get:
      operationId: ServiceStatusController_getStatus
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags:
        - Service Status (Public)
  /report/create/workspace/{workspaceId}:
    get:
      operationId: ReportsController_createV3
      parameters:
        - name: workspaceId
          required: true
          in: path
          description: The workspace to get the defaults for
          schema:
            type: number
        - name: type
          required: true
          in: query
          description: The type of report to create
          schema:
            type: string
      responses:
        '200':
          description: New default report metadata was successfully created
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReportCreationResponseDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_73
        - Bearer Token: []
      tags: &ref_74
        - Report
  /report/workspace/{workspaceId}:
    get:
      operationId: ReportsController_getReportsV2
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: types
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
        - name: sortOrder
          required: true
          in: query
          schema:
            type: string
        - name: sortBy
          required: true
          in: query
          schema:
            type: string
        - name: searchTerm
          required: true
          in: query
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_73
      tags: *ref_74
    post:
      operationId: ReportsController_getReportsV3WithFilters
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: sortOrder
          required: true
          in: query
          schema:
            type: string
        - name: sortBy
          required: true
          in: query
          schema:
            type: string
        - name: searchTerm
          required: true
          in: query
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetScoringReportBodyDto'
      responses:
        '201':
          description: ''
      security: *ref_73
      tags: *ref_74
  /report/{reportId}/metadata:
    get:
      operationId: ReportsController_getReportMetadataV2
      parameters:
        - name: reportId
          required: true
          in: path
          description: The id of the report to fetch
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportMetadataDto'
      security: *ref_73
      tags: *ref_74
    patch:
      operationId: ReportsController_updateReportMetadata
      parameters:
        - name: reportId
          required: true
          in: path
          description: The id of the report to update
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportMetadataDto'
      security: *ref_73
      summary: Update the metadata of a report.
      tags: *ref_74
  /report/{reportId}/organization/{organizationId}/metadata:
    get:
      operationId: ReportsController_getReportMetadataV4
      parameters:
        - name: reportId
          required: true
          in: path
          description: The id of the report to fetch
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: includeInflightFiltersDisplayNames
          required: false
          in: query
          description: >-
            a flag to include the display names of the filters in the response
            if inflight report
          schema: {}
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_73
      tags: *ref_74
    patch:
      operationId: ReportsController_updateReportMetadataV2
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          description: The id of the report to update
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportMetadataDto'
      security: *ref_73
      summary: Update the metadata of a report.
      tags: *ref_74
  /report/metadata:
    post:
      operationId: ReportsController_saveReportMetadata
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportCreationRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportMetadataDto'
      security: *ref_73
      summary: Save the metadata of a report.
      tags: *ref_74
  /report/organization/{organizationId}/metadata:
    post:
      operationId: ReportsController_saveReportMetadataV2
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the organization workspaces and accounts belong to
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportMetadataDto'
      security: *ref_73
      summary: Save the metadata of a report.
      tags: *ref_74
  /report/{reportId}:
    delete:
      operationId: ReportsController_deleteReport
      parameters:
        - name: reportId
          required: true
          in: path
          description: The id of the report to delete
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_73
      summary: Delete a report.
      tags: *ref_74
  /analytics-report/organization/{organizationId}:
    get:
      operationId: AnalyticsReportsController_getAllAnalyticsReports
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization we get user saved reports for
          schema:
            type: string
        - name: types
          required: false
          in: query
          description: An array of report types to filter by.
          schema:
            type: array
            items:
              type: string
        - name: sortOrder
          required: false
          in: query
          description: The order of the sorting ASC / DESC
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
        - name: searchTerm
          required: false
          in: query
          description: Filtering results by search term in report name or description
          schema: {}
        - name: sortBy
          required: false
          in: query
          description: The field to sort the results by
          schema: {}
      responses:
        '200':
          description: Analytics report and metadata
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/AnalyticsReportDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_75
        - Bearer Token: []
      tags: &ref_76
        - Report
    post:
      operationId: AnalyticsReportsController_createAnalyticsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization user's report is created for
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsReportDto'
      security: *ref_75
      tags: *ref_76
  /analytics-report/organization/{organizationId}/report/{reportId}:
    post:
      operationId: AnalyticsReportsController_duplicateAnalyticsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization user's report is created for
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          description: The report to duplicate
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsReportDto'
      security: *ref_75
      tags: *ref_76
    patch:
      operationId: AnalyticsReportsController_updateAnalyticsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization user's report was created for
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          description: The id of user's report to update
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsReportDto'
      security: *ref_75
      tags: *ref_76
    get:
      operationId: AnalyticsReportsController_getSingleAnalyticsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization user's report was created for
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          description: The id of report to fetch for user
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsReportDto'
      security: *ref_75
      tags: *ref_76
    delete:
      operationId: AnalyticsReportsController_deleteAnalyticsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization user report was created for
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          description: The id of report to delete
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_75
      tags: *ref_76
  /analytics-report/organization/{organizationId}/report/{reportId}/rename:
    patch:
      operationId: AnalyticsReportsController_renameAnalyticsReport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of organization user's report is created for
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          description: The report to duplicate
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenameAnalyticsReportDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsReportDto'
      security: *ref_75
      tags: *ref_76
  /analytics-report/organization/{organizationId}/filters:
    post:
      operationId: AnalyticsReportsController_getAnalyticsReportFilterOptionsV2
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization user report was created for
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAnalyticsReportFilterOptionsRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_75
      summary: |-
        Get filter values for analytics data.
        Implements pagination and string search for identifier filters
      tags: *ref_76
  /analytics-report/organization/{organizationId}/filtered-ad-accounts:
    post:
      operationId: AnalyticsReportsController_getFilteredAdAccountsForBrandsOrMarkets
      parameters:
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          description: The organization id for the report
          schema: {}
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_75
      tags: *ref_76
  /analytics-report/organization/{organizationId}/scopeFilter:
    post:
      operationId: AnalyticsReportsController_filterAdAccountsByScope
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: Scope filters successfully retrieved
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_75
      tags: *ref_76
  /analytics-report/organization/{organizationId}/reports/users:
    get:
      operationId: AnalyticsReportsController_getUsersWithReportsByWorkspacesIds
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: workspaceIds
          required: true
          in: query
          schema:
            type: string
        - name: reportTypes
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_75
      tags: *ref_76
  /analytics-copilot/next:
    post:
      operationId: AnalyticsCopilotController_getLLMResponseV2
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsCopilotNextResponse'
      security: &ref_77
        - Bearer Token: []
      tags: &ref_78
        - Copilot
  /analytics-copilot/v2/next:
    post:
      operationId: AnalyticsCopilotController_getLLMResponseV2
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AnalyticsCopilotNextPayload'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsCopilotNextResponse'
      tags:
        - Copilot
  /analytics-copilot/conversation-list:
    get:
      operationId: AnalyticsCopilotController_getConversationList
      parameters:
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AnalyticsCopilotConversationListItem'
      security: *ref_77
      tags: *ref_78
  /analytics-copilot/conversation/{chatId}/rename:
    patch:
      operationId: AnalyticsCopilotController_renameConversation
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: boolean
      security: *ref_77
      tags: *ref_78
  /analytics-copilot/conversation/{chatId}:
    delete:
      operationId: AnalyticsCopilotController_deleteConversation
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: boolean
      security: *ref_77
      tags: *ref_78
  /analytics-copilot/conversation-history/{chatId}:
    get:
      operationId: AnalyticsCopilotController_getConversationHistory
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AnalyticsCopilotConversationHistory'
      security: *ref_77
      tags: *ref_78
  /analytics-copilot/update-message-feedback/{chatId}/{messageId}:
    post:
      operationId: AnalyticsCopilotController_updateMessageFeedback
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: messageId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMessageFeedbackDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: boolean
      security: *ref_77
      tags: *ref_78
  /analytics-copilot/conversation-starters:
    get:
      operationId: AnalyticsCopilotController_getConversationStarters
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
      security: *ref_77
      tags: *ref_78
  /brand-copilot/v2/next:
    post:
      operationId: BrandCopilotController_getLLMResponse
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BrandCopilotNextRequest'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandCopilotNextResponse'
      tags:
        - Copilot
  /brand-copilot/conversations:
    get:
      operationId: BrandCopilotController_getConversationList
      parameters:
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AnalyticsCopilotConversationListItem'
      security: &ref_79
        - Bearer Token: []
      tags: &ref_80
        - Copilot
  /brand-copilot/conversations/{chatId}:
    get:
      operationId: BrandCopilotController_getConversationHistory
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_79
      tags: *ref_80
    delete:
      operationId: BrandCopilotController_deleteConversation
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: boolean
      security: *ref_79
      tags: *ref_80
  /brand-copilot/conversations/{chatId}/messages/{messageId}/feedback:
    post:
      operationId: BrandCopilotController_updateMessageFeedback
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: messageId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMessageFeedbackDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: boolean
      security: *ref_79
      tags: *ref_80
  /brand-copilot/conversation-starters:
    get:
      operationId: BrandCopilotController_getConversationStarters
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
      security: *ref_79
      tags: *ref_80
  /brand-copilot/conversations/{chatId}/rename:
    patch:
      operationId: BrandCopilotController_renameConversation
      parameters:
        - name: chatId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: boolean
      security: *ref_79
      tags: *ref_80
  /brand-copilot/create-presentation:
    post:
      operationId: BrandCopilotController_createPresentation
      parameters: []
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_79
      tags: *ref_80
  /brand-copilot/next:
    post:
      operationId: BrandCopilotController_getLLMResponse
      parameters: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BrandCopilotNextRequest'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandCopilotNextResponse'
      security: *ref_79
      tags: *ref_80
  /brand-copilot/models:
    get:
      operationId: BrandCopilotController_getAvailableModels
      parameters: []
      responses:
        '200':
          description: ''
      security: *ref_79
      tags: *ref_80
  /media-annotations/media/{mediaId}/tags:
    post:
      operationId: MediaAnnotationController_getTags
      parameters:
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MediaAnnotationTagDto'
      security:
        - Bearer Token: []
      tags:
        - MediaAnnotation
  /analytics-config/default-durations:
    get:
      operationId: AnalyticsConfigController_getDefaultDurationBuckets
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      tags:
        - Analytics Config
  /data-exports/organization/{organizationId}/report/filter:
    post:
      description: >-
        Fetches an array of report metadata, so users can see what reports are
        available and download any they need.
      operationId: DataExportsController_getAllDataExportsForOrgWithFilter
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFilteredReportRequestDto'
      responses:
        '200':
          description: >-
            Returns an array of reports available for export for a given
            organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadDataExportReportOrganizationDto'
        '201':
          description: ''
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Organization not found
        '500':
          description: Internal server error retrieving data exports
      security: &ref_81
        - Bearer Token: []
      summary: Retrieve list of reports that can be exported for a given organization
      tags: &ref_82
        - Data Exports
  /data-exports/organization/{organizationId}/scheduled-exports:
    post:
      description: >-
        Fetches an array of scheduled export records so users can see
        information about and modify them.
      operationId: DataExportsController_getAllScheduledExports
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllScheduledExportsRequestDto'
      responses:
        '200':
          description: >-
            Returns an array of reports available for export for a given
            organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadDataExportReportOrganizationDto'
        '201':
          description: ''
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Organization not found
        '500':
          description: Internal server error retrieving data exports
      security: *ref_81
      summary: Retrieve list of scheduled exports for a given organization
      tags: *ref_82
  /data-exports/organization/{organizationId}:
    get:
      description: >-
        Fetches an array of report metadata, so users can see what reports are
        available and download any they need.
      operationId: DataExportsController_getAllDataExportsForOrg
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: >-
            Returns an array of reports available for export for a given
            organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FrontEndBasicReadReportDto'
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Organization not found
        '500':
          description: Internal server error retrieving data exports
      security: *ref_81
      summary: Retrieve list of reports that can be exported for a given organization
      tags: *ref_82
    post:
      operationId: DataExportsController_createDataExport
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDataExportOrganizationRequestDto'
      responses:
        '201':
          description: Data Export successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDataExportBFFResponseDto'
      security: *ref_81
      summary: Create a new data export
      tags: *ref_82
  /data-exports/organization/{organizationId}/report/{reportId}:
    get:
      description: >-
        Fetches a single export report, allowing you to retrieve a download link
        for the report.
      operationId: DataExportsController_getDataExportById
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Returns a singe export report by id for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadOneDataExportReportOrganizationDto'
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Report not found
        '500':
          description: Internal server error retrieving data exports
      security: *ref_81
      summary: Retrieve an export report by id for a given organization
      tags: *ref_82
    delete:
      operationId: DataExportsController_deleteDataExport
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: reportId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: Deleted report successfully
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Report not found
        '500':
          description: Internal server error retrieving data exports
      security: *ref_81
      summary: Deletes an export report by id for a given organization
      tags: *ref_82
  /data-exports/filters/organization/{organizationId}:
    get:
      operationId: DataExportsController_getFilters
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
        - name: filterOptions
          required: true
          in: query
          schema:
            $ref: '#/components/schemas/GetDataExportFilterQueryParamDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FrontendFilterValues'
        '201':
          description: Filter was made and returned to use
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/'
      security: *ref_81
      tags: *ref_82
  /oauth2/token:
    post:
      operationId: OAuth2Controller_createAccessToken
      parameters: []
      responses:
        '200':
          description: Creates an access token
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/AccessTokenResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      summary: It generates a new access token based on the code and grant type
      tags: &ref_83
        - OAuth2
  /oauth2/revoke:
    post:
      operationId: OAuth2Controller_revokeToken
      parameters: []
      responses:
        '200':
          description: Revoke token message
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/RevokeTokenResponseDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      summary: >-
        It revokes the token for the user and application.

        If this token is a cognito token send a revoke token command to cognito.

        If this token is a refresh token, revoke all tokens this user has on
        this application.
      tags: *ref_83
  /creative-lifecycle/report:
    post:
      operationId: CreativeLifecycleController_getReportData
      parameters:
        - name: useDB
          required: true
          in: query
          schema:
            type: boolean
        - name: isCoke
          required: true
          in: query
          schema:
            type: boolean
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreativeLifecycleDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_84
        - Bearer Token: []
      tags: &ref_85
        - Creative Lifecycle
  /creative-lifecycle/kpis:
    post:
      operationId: CreativeLifecycleController_getKpiData
      parameters:
        - name: useDB
          required: true
          in: query
          schema:
            type: boolean
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCreativeLifecycleDto'
      responses:
        '201':
          description: ''
      security: *ref_84
      tags: *ref_85
  /creative-manager/organization/{organizationId}/creatives:
    post:
      operationId: CreativeManagerController_getCreativesPerformance
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization we get creatives for
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: &ref_86
        - Creative Manager
  /creative-manager/organization/{organizationId}/ads:
    post:
      operationId: CreativeManagerController_getAdsPerformance
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization we get creatives for
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_86
  /creative-manager/organization/{organizationId}/creatives/csv:
    post:
      operationId: CreativeManagerController_downloadCreativesPerformanceCSV
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: ''
      tags: *ref_86
  /creative-manager/organization/{organizationId}/ads/csv:
    post:
      operationId: CreativeManagerController_downloadAdsPerformanceCSV
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '201':
          description: ''
      tags: *ref_86
  /connectors/organization/{organizationId}:
    get:
      description: >-
        Fetches an array of connectors, so users can see what connectors are set
        up already
      operationId: ConnectorsController_getDataExportsConnectors
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: search
          required: false
          in: query
          schema:
            type: string
        - name: sortOrder
          required: false
          in: query
          schema:
            enum:
              - ASC
              - DESC
            type: string
        - name: sortBy
          required: false
          in: query
          schema:
            type: string
      responses:
        '200':
          description: Returns an array of connectors for a given organization
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectorsDTO'
        '400':
          description: Bad Request
        '403':
          description: Forbidden
        '404':
          description: Organization not found
        '500':
          description: Internal server error retrieving data exports connectors
      security: &ref_87
        - Bearer Token: []
      summary: Retrieve list of connectors for a given organization
      tags: &ref_88
        - Connectors
    post:
      operationId: ConnectorsController_createConnector
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The id of the Organization
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateConnectorRequestDto'
      responses:
        '201':
          description: Connector successfully created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateConnectorResponseDto'
      security: *ref_87
      summary: Create a new connector
      tags: *ref_88
  /organization/{organizationId}/currency:
    get:
      operationId: CurrencyController_getCurrencies
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CurrencyDto'
      tags:
        - Currency
  /workspace/{workspaceId}/real-eyes:
    get:
      operationId: RealEyesController_workspaceHasRealEyesAccess
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_89
        - Bearer Token: []
      tags: &ref_90
        - real-eyes
  /workspace/{workspaceId}/real-eyes/{mediaId}:
    get:
      operationId: RealEyesController_getRealEyesScores
      parameters:
        - name: workspaceId
          required: true
          in: path
          schema:
            type: number
        - name: mediaId
          required: true
          in: path
          description: Target analytics media to fetch the real eyes scores
          schema:
            type: number
        - name: attention
          required: true
          in: query
          schema:
            default: true
            type: boolean
        - name: confusion
          required: true
          in: query
          schema:
            default: true
            type: boolean
        - name: contempt
          required: true
          in: query
          schema:
            default: true
            type: boolean
        - name: disgust
          required: true
          in: query
          schema:
            default: true
            type: boolean
        - name: distraction
          required: true
          in: query
          schema:
            default: true
            type: boolean
        - name: happiness
          required: true
          in: query
          schema:
            default: true
            type: boolean
        - name: surprise
          required: true
          in: query
          schema:
            default: true
            type: boolean
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_89
      tags: *ref_90
  /analytics/organization/{organizationId}/media/{mediaId}:
    get:
      operationId: AnalyticsMediaController_getMediaVariant
      parameters:
        - name: mediaId
          required: true
          in: path
          schema:
            type: number
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: &ref_91
        - Bearer Token: []
      tags: &ref_92
        - Analytics Media
  /analytics/organization/{organizationId}/platform-media:
    post:
      operationId: AnalyticsMediaController_getPlatformMedia
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PlatformMediaRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_91
      tags: *ref_92
  /organization/{organizationId}/audience:
    get:
      operationId: AudienceController_getAudiences
      parameters:
        - name: platform
          required: true
          in: query
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags:
        - audience
  /insight-filter/organization/{organizationId}/brand:
    get:
      operationId: InsightFilterController_getInsightBrands
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: &ref_93
        - Insight Filters
  /insight-filter/organization/{organizationId}/category:
    get:
      operationId: InsightFilterController_getInsightCategories
      parameters: []
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_93
  /insight-filter/organization/{organizationId}/campaign-objective:
    get:
      operationId: InsightFilterController_getCampaignObjectives
      parameters:
        - name: platforms
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_93
  /insight-filter/organization/{organizationId}/market:
    get:
      operationId: InsightFilterController_getOrganizationMarkets
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_93
  /insight-filter/organization/{organizationId}/kpi:
    get:
      operationId: InsightFilterController_getKpiFilters
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: platforms
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_93
  /insight-filter/organization/{organizationId}/status:
    get:
      operationId: InsightFilterController_getInsightStatuses
      parameters: []
      responses:
        '200':
          description: ''
      tags: *ref_93
  /insight-filter/organization/{organizationId}/type:
    get:
      operationId: InsightFilterController_getInsightTypes
      parameters:
        - name: includeCopilotTypes
          required: true
          in: query
          schema:
            type: boolean
      responses:
        '200':
          description: ''
      tags: *ref_93
  /insight-filter/organization/{organizationId}/user:
    get:
      operationId: InsightFilterController_getInsightUsers
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      tags: *ref_93
  /media-impact/organization/{organizationId}/creative:
    post:
      operationId: MediaImpactController_getMediaImpactCreativePerformanceForKpi
      parameters:
        - name: organizationId
          required: true
          in: path
          description: The organization we get creatives for
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security:
        - Bearer Token: []
      tags:
        - Media Impact
  /executive-dashboard/organization/{organizationId}/dashboard:
    post:
      operationId: DashboardController_create
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDashboardDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadDashboardDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadDashboardDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: &ref_94
        - Bearer Token: []
      tags: &ref_95
        - Executive Dashboard - Dashboards
  /executive-dashboard/organization/{organizationId}/dashboard/list:
    post:
      operationId: DashboardController_listDashboards
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: sortBy
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
        - name: sortOrder
          required: true
          in: query
          schema:
            type: array
            items:
              type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ListDashboardsDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/PaginatedSuccessResponse'
                  - properties:
                      result:
                        type: array
                        items:
                          $ref: '#/components/schemas/ReadDashboardDto'
        '201':
          description: ''
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_94
      tags: *ref_95
  /executive-dashboard/organization/{organizationId}/dashboard/{dashboardId}:
    get:
      operationId: DashboardController_getDashboard
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: dashboardId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadDashboardDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_94
      tags: *ref_95
    patch:
      operationId: DashboardController_update
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: dashboardId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDashboardDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - properties:
                      result:
                        $ref: '#/components/schemas/ReadDashboardDto'
        '400':
          description: Parameter or Request payload error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Person/User is not authorized to call this endpoint
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
      security: *ref_94
      tags: *ref_95
    delete:
      operationId: DashboardController_remove
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: dashboardId
          required: true
          in: path
          schema:
            type: string
      responses:
        '204':
          description: ''
      security: *ref_94
      tags: *ref_95
  /executive-dashboard/organization/{organizationId}/dashboard/{dashboardId}/favorite:
    put:
      operationId: DashboardController_updateFavorite
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: dashboardId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDashboardFavoriteDto'
      responses:
        '200':
          description: Favorite flag updated
      security: *ref_94
      tags: *ref_95
  /executive-dashboard/organization/{organizationId}/dashboard/{dashboardId}/widget:
    post:
      operationId: WidgetController_createWidget
      parameters:
        - name: dashboardId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWidgetDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadWidgetDto'
      security: &ref_96
        - Bearer Token: []
      tags: &ref_97
        - Executive Dashboard - Widget
  /executive-dashboard/organization/{organizationId}/widget/{widgetId}:
    patch:
      operationId: WidgetController_updateWidget
      parameters:
        - name: widgetId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWidgetDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadWidgetDto'
      security: *ref_96
      tags: *ref_97
    delete:
      operationId: WidgetController_removeWidget
      parameters:
        - name: widgetId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      responses:
        '200':
          description: ''
      security: *ref_96
      tags: *ref_97
  /executive-dashboard/organization/{organizationId}/widget/type/{typeId}:
    get:
      operationId: WidgetController_readWidgetType
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: typeId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_96
      tags: *ref_97
  /executive-dashboard/organization/{organizationId}/widget/type/{widgetType}/filter/{fieldName}:
    post:
      operationId: WidgetController_listFilterValues
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: widgetType
          required: true
          in: path
          schema:
            type: string
        - name: fieldName
          required: true
          in: path
          schema:
            type: string
        - name: search
          required: true
          in: query
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DashboardFilterDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                type: object
      security: *ref_96
      tags: *ref_97
  /executive-dashboard/organization/{organizationId}/widget/type:
    get:
      operationId: WidgetController_listWidgetTypes
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
        - name: offset
          required: false
          in: query
          description: The offset of the starting value in the returned list.
          schema:
            default: 0
            type: number
        - name: perPage
          required: false
          in: query
          description: >-
            The number of items requested per page. The list may included fewer
            items if we are at the end of the list.
          schema:
            default: 10
            type: number
            minimum: 1
        - name: queryId
          required: false
          in: query
          description: If available, pass the queryId here to retrieve cached results.
          schema:
            type: string
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
      security: *ref_96
      tags: *ref_97
  /executive-dashboard/organization/{organizationId}/widget/data:
    post:
      operationId: WidgetController_previewWidgetData
      parameters:
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WidgetDataRequestDto'
      responses:
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WidgetDataResponseDto'
      security: *ref_96
      tags: *ref_97
  /executive-dashboard/organization/{organizationId}/widget/{widgetId}/data:
    post:
      operationId: WidgetController_widgetData
      parameters:
        - name: widgetId
          required: true
          in: path
          schema:
            type: string
        - name: organizationId
          required: true
          in: path
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WidgetDataRequestDto'
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WidgetDataRequestDto'
        '201':
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WidgetDataResponseDto'
      security: *ref_96
      tags: *ref_97
info:
  title: ACS BFF API
  description: API for ACS
  version: '1.0'
  contact: {}
tags: []
servers:
  - url: https://bff-dev.vidmob.com
    description: Dev
  - url: https://bff-stage.vidmob.com
    description: Stage
  - url: https://bff.vidmob.com
    description: Prod
  - url: http://localhost:3000
    description: Local
components:
  securitySchemes:
    Bearer Token:
      scheme: bearer
      bearerFormat: Bearer Token
      type: http
      name: Authorization
      description: Enter your Bearer Token or API Key (without "Bearer " prefix)
      in: header
  schemas:
    UserResponseDto:
      type: object
      properties:
        id:
          type: number
        username:
          type: string
        authorities:
          type: array
          items:
            type: string
      required:
        - id
        - username
        - authorities
    ReadApiKeyResponseDto:
      type: object
      properties:
        status:
          type: string
          description: Status of the response
          example: success
        result:
          type: object
          description: Result of the response
      required:
        - status
        - result
    ApiKeyScopeDto:
      type: object
      properties:
        scope:
          enum:
            - scoring
            - studio
            - analytics
            - platform
            - aperture
          type: string
          description: Scope of the API Key
        permission:
          enum:
            - read
            - read_write
          type: string
          description: Permission within the API Key scope
      required:
        - scope
        - permission
    CreateApiKeyRequestDto:
      type: object
      properties:
        name:
          type: string
          example: Scoring read/write
          description: Name of the API key
        expirationDate:
          type: string
          example: '1716468511962'
          description: Expiration date of the API key
        scopes:
          type: array
          items:
            $ref: '#/components/schemas/ApiKeyScopeDto'
      required:
        - name
        - scopes
    CreateApiKeyResponseDto:
      type: object
      properties:
        status:
          type: string
          description: Status of the response
          example: success
        result:
          type: object
          description: Result of the response
      required:
        - status
        - result
    DeleteApiKeyResponseDto:
      type: object
      properties:
        status:
          type: string
          description: Status of the response
          example: success
        result:
          type: object
          description: Result of the response
      required:
        - status
        - result
    ReadIndividualMediaScoresRequestDto:
      type: object
      properties:
        scorecardId:
          type: number
          nullable: true
        mediaId:
          type: number
        platforms:
          type: array
          items:
            type: object
        rootScorecardId:
          type: number
      required:
        - mediaId
        - platforms
    ScoreOverrideGetResponseDto:
      type: object
      properties:
        personId:
          type: number
          description: >-
            The unique identifier for the person who requested the score
            override.
        overrideRequests:
          description: The array of score override requests.
          type: array
          items:
            type: object
      required:
        - personId
        - overrideRequests
    ScoreOverrideGetResponseV2Dto:
      type: object
      properties:
        personId:
          type: number
          description: >-
            The unique identifier for the person who requested the score
            override.
        overrideRequests:
          type: array
          items:
            type: object
      required:
        - personId
        - overrideRequests
    AccessibleReportTypesDto:
      type: object
      properties:
        accessibleReportTypes:
          type: array
          items:
            type: string
            enum:
              - ADHERENCE
              - IMPRESSION_ADHERENCE
              - ADOPTION
              - IN_FLIGHT
              - DIVERSITY
      required:
        - accessibleReportTypes
    ErrorData:
      type: object
      properties:
        item:
          type: string
          description: Object related to the Error
          example: organization
        value:
          type: string
          description: >-
            Value of the object related to the Error. Typically the Id of the
            Resource in the request.
          example: f629b141-cce6-4ff1-90d4-fb8ee7e08c8b
      required:
        - item
        - value
    APIError:
      type: object
      properties:
        identifier:
          type: string
          description: >-
            System assigned identifier for the Error. This is not unique. eg
            vidmob.[system].[type]
          example: vidmob.system.notFoundError
        type:
          type: string
          description: Type of the Error.
          examples:
            - NOT_FOUND
            - ACCESS_VIOLATION
            - GENERAL_ERROR
            - INVALID_ITEM
        system:
          type: string
          description: Name of the service where the error occurred. eg project, bid
          example: organization
        message:
          type: string
          description: >-
            A human-readable explanation specific to this occurrence of the
            problem. Clients should be able to understand what the error is
            about by looking at this value. Please do not include internal
            technical details here; Include them in the system logs instead.
        data:
          description: >-
            Error Data Object. Optional additional data that needs to be
            surfaced to supplement more information to the error.
          allOf:
            - $ref: '#/components/schemas/ErrorData'
      required:
        - identifier
        - type
        - system
        - message
    ErrorResponse:
      type: object
      properties:
        status:
          type: object
          description: >-
            Represents the status of the response. For errors, this prop should
            have the value error
          example: ERROR
        traceId:
          type: string
          description: >-
            A trace id that the API support team can use to debug a particular
            HTTP call.
          example: 7c0d9608-b43c-4c6d-922b-c9bfa364e199
        error:
          description: >-
            Error Object. In case of multiple errors, return an Array of Error
            Objects corresponding to each error.
          allOf:
            - $ref: '#/components/schemas/APIError'
      required:
        - status
        - error
    ScoringReportOptionsDto:
      type: object
      properties:
        markets:
          type: array
          items:
            type: object
        brands:
          type: array
          items:
            type: object
        workspaces:
          type: array
          items:
            type: object
        criteriaSets:
          type: array
          items:
            type: object
        creativeType:
          type: array
          items:
            type: object
        channels:
          type: array
          items:
            type: string
      required:
        - markets
        - brands
        - workspaces
        - criteriaSets
        - creativeType
        - channels
    ReadReportDto:
      type: object
      properties: {}
    AdherenceColumnIdentifiers:
      type: object
      properties:
        workspaceId:
          type: number
          description: The workspace id for the column
          example: 1212
        brandId:
          type: string
          description: The brand id for the column
          example: q002-qr2uu28y94y024i-42i-u
        marketId:
          type: string
          description: The market iso code for the column
          example: fra
    AdherenceColumn:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier for the column
          example: Known Brand-Main Workspace
        identifiers:
          description: The actual identifiers for the column entities
          example: '{"workspaceId": 1212,"brandId": "q002-qr2uu28y94y024i-42i-u"}'
          allOf:
            - $ref: '#/components/schemas/AdherenceColumnIdentifiers'
        displayName:
          type: string
          description: The display name for the column
          example: Main Workspace
        parentId:
          type: string
          nullable: true
          description: The id of parent column this is nested under
          example: Known Brand
        adherencePercent:
          type: number
          description: The percentage of media adherence for the column
          example: 98.5882
        impressions:
          type: number
          description: The number of impressions for the column
          example: 1000
      required:
        - id
        - identifiers
        - displayName
        - parentId
    AdherenceRowColumn:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier for the column
          example: Main Workspace
        adherencePercent:
          type: number
          description: The percentage of media adherence for the row and column
          example: 98.5882
        impressions:
          type: number
          description: The number of impressions for the row and column
          example: 1000
      required:
        - id
    AdherenceRow:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier for the adherence row
          example: LINKEDIN:HUMAN_PRESENCE_ANYTIME:{}:OPTIONAL:BEST_PRACTICE
        displayName:
          type: string
          description: The display name for the adherence row
          example: LinkedIn Human Presence Anytime
        parentId:
          type: string
          nullable: true
          description: The unique identifier for the parent of the adherence row
          example: 98UH42-HY9H2HRBRU-H2H2JH4J
        color:
          type: string
          nullable: true
          description: The color associated with the adherence parent row
          example: '#FF5733'
        criteriaIdentifier:
          type: string
          description: The unique identifier for the criteria
          example: HUMAN_PRESENCE_ANYTIME
        criteriaParameters:
          type: string
          nullable: true
          description: The name of the criteria
          example: '{"minPercent":50}'
        criteriaPlatform:
          type: string
          description: The platform associated with the criteria
          example: LINKEDIN
        isOptional:
          type: boolean
          description: If the criteria is optional
          example: true
        isBestPractice:
          type: boolean
          description: If the criteria is a best practice
          example: false
        criteriaRuleId:
          type: string
          nullable: true
          description: >-
            The unique identifier for the criteria rule used to create the
            criteria
          example: 39924g7-994208y-804ry0y2
        customIconUrls:
          description: The URL for the custom icon associated with the criteria
          example: https://example.com/icon.png
          type: array
          items:
            type: string
        columns:
          description: The adherence percentage for the criteria for every column
          example:
            - id: Main Workspace
              adherencePercent: 89.1938
              impressions: 1000
          type: array
          items:
            $ref: '#/components/schemas/AdherenceRowColumn'
      required:
        - id
        - displayName
        - parentId
        - color
        - columns
    AdherenceReportV3ResponseDto:
      type: object
      properties:
        columns:
          type: array
          items:
            $ref: '#/components/schemas/AdherenceColumn'
        rows:
          type: array
          items:
            $ref: '#/components/schemas/AdherenceRow'
      required:
        - columns
        - rows
    Stream:
      type: object
      properties: {}
    ConstraintDTO:
      type: object
      properties:
        dimension:
          type: string
          enum:
            - time
            - visualThreshold
            - fileSize
            - duration
            - aspectRatio
            - color
            - sceneChanges
        allowedOperators:
          type: array
          items:
            type: string
            enum:
              - is
              - in
              - any
              - between
              - after
              - before
              - anytime
        valueType:
          type: string
          enum:
            - string
            - multiString
            - singleSelect
            - multiSelect
            - number
        selectedOperator:
          type: string
          enum:
            - is
            - in
            - any
            - between
            - after
            - before
            - anytime
        allowedValues:
          type: object
        value:
          type: object
      required:
        - dimension
        - allowedOperators
        - valueType
    CriteriaRuleDTO:
      type: object
      properties:
        has:
          type: boolean
        category:
          type: string
        subCategory:
          type: string
        name:
          type: string
        constraints:
          type: array
          items:
            $ref: '#/components/schemas/ConstraintDTO'
      required:
        - has
        - category
        - name
        - constraints
    ReadDraftMediaVariantDto:
      type: object
      properties:
        status:
          type: object
          description: Represents the status of the draft media variant processing.
        duration:
          type: number
          description: The duration value is in seconds.
        mediaVariantUrl:
          type: string
          description: |-
            URL to access the draft media variant. This might be a temporary URL
            signed with specific access credentials.
      required:
        - status
    ReadMediaVariantDto:
      type: object
      properties:
        status:
          type: object
          description: Represents the status of the media variant processing.
        duration:
          type: number
          description: The duration value is in seconds.
        mediaVariantUrl:
          type: string
          description: |-
            URL to access the media variant. This might be a temporary URL
            signed with specific access credentials.
      required:
        - status
    UpdateProjectDetailsDto:
      type: object
      properties:
        deliveryDate:
          type: string
        workspaceId:
          type: number
      required:
        - deliveryDate
        - workspaceId
    CreateWorkspaceMarketDto:
      type: object
      properties:
        isoCode:
          type: string
        workspaceId:
          type: number
      required:
        - isoCode
        - workspaceId
    CreateWorkspaceMarketMapDto:
      type: object
      properties:
        isoCode:
          type: string
      required:
        - isoCode
    DeleteMarketAssignResponseDto:
      type: object
      properties:
        workspaceId:
          type: number
          description: Id of the Workspace (Partner) assigned to the Market
          example: 123
        isoCode:
          type: string
          description: ISO code of the Market (Country) assigned to the Workspace
          example: USA
      required:
        - workspaceId
        - isoCode
    ReadMarketDto:
      type: object
      properties:
        isoCode:
          type: string
        name:
          type: string
      required:
        - isoCode
        - name
    BrandUserDto:
      type: object
      properties:
        id:
          type: number
          description: Id of the User
          example: 123
        username:
          type: string
          description: username of the User
          example: <EMAIL>
      required:
        - id
        - username
    ReadBrandDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
      required:
        - id
        - name
    CreateBrandDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
      required:
        - name
    UpdateBrandDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the brand
          example: My Brand
        description:
          type: string
          description: The description of the brand
      required:
        - name
    CreateWorkspaceDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the workspace
          example: My Workspace
        isPrimary:
          type: boolean
          description: Is this workspace the primary workspace for the organization?
          example: true
        brands:
          description: The list of brand ids to be associated with the workspace
          type: array
          items:
            type: string
        markets:
          description: The list of market ids to be associated with the workspace
          type: array
          items:
            type: string
        logoUrl:
          type: string
          description: The Logo Url of the workspace
          example: >-
            https://www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png
      required:
        - name
        - isPrimary
        - logoUrl
    UpdateWorkspaceDto:
      type: object
      properties:
        name:
          type: string
          description: The name of the workspace
          example: My Workspace
        isPrimary:
          type: boolean
          description: Is this workspace the primary workspace for the organization?
          example: true
        brands:
          description: The list of brand ids to be associated with the workspace
          type: array
          items:
            type: string
        markets:
          description: The list of market ids to be associated with the workspace
          type: array
          items:
            type: string
    ReadAdAccountMapDto:
      type: object
      properties:
        message:
          type: string
          description: Message generated for a successfull ad account mapping
      required:
        - message
    CreateAdAccountBrandMapDto:
      type: object
      properties:
        selected_brands:
          description: List of brands ids to map
          example:
            - xxx-xxx-xxx
            - yyy-yyy
          type: array
          items:
            type: string
        unselected_brands:
          description: List of brands ids to unmap
          example:
            - xxx-xxx-xxx
            - yyy-yyy
          type: array
          items:
            type: string
        accounts:
          description: List of ad account ids
          example:
            - '*****************'
            - t2_t5vae3ih
          type: array
          items:
            type: string
      required:
        - selected_brands
        - unselected_brands
        - accounts
    CreateAdAccountMarketMapDto:
      type: object
      properties:
        selected_markets:
          description: List of country iso code to map
          example:
            - usa
            - alb
          type: array
          items:
            type: string
        unselected_markets:
          description: List of country iso code to unmap
          example:
            - usa
            - alb
          type: array
          items:
            type: string
        accounts:
          description: List of ad account ids
          example:
            - '*****************'
            - t2_t5vae3ih
          type: array
          items:
            type: string
      required:
        - selected_markets
        - unselected_markets
        - accounts
    Market:
      type: object
      properties:
        isoCode:
          type: string
        name:
          type: string
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/Workspace'
      required:
        - isoCode
        - name
        - workspaces
    Workspace:
      type: object
      properties:
        id:
          type: number
        name:
          type: string
        isPrimary:
          type: boolean
        organizationId:
          type: string
        dateCreated:
          format: date-time
          type: string
        markets:
          type: array
          items:
            $ref: '#/components/schemas/Market'
        brands:
          type: array
          items:
            $ref: '#/components/schemas/Brand'
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
      required:
        - id
        - name
        - isPrimary
        - organizationId
        - dateCreated
        - markets
        - brands
        - users
    User:
      type: object
      properties:
        id:
          type: number
        username:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        workspace:
          type: array
          items:
            $ref: '#/components/schemas/Workspace'
      required:
        - id
        - username
        - firstName
        - lastName
        - workspace
    BrandIdentifier:
      type: object
      properties:
        id:
          type: string
        brandId:
          type: string
        brands:
          $ref: '#/components/schemas/Brand'
        identifier:
          type: string
        deleted:
          type: boolean
        dateCreated:
          format: date-time
          type: string
        lastUpdated:
          format: date-time
          type: string
      required:
        - id
        - brandId
        - brands
        - identifier
        - deleted
        - dateCreated
        - lastUpdated
    Brand:
      type: object
      properties:
        id:
          type: string
          description: System assigned Id of the Brand
        name:
          type: string
          description: Name of the Organization
        description:
          type: string
          description: Description of the Organization
        deleted:
          type: boolean
          description: Indicates if the Brand is deleted or not
        updatedByPersonId:
          type: number
          description: ID of the user who last updated the Brand
        createdByPersonId:
          type: number
          description: ID of the user who created the Brand
        organizationId:
          type: string
          description: ID of the Organization to which the Brand belongs
        dateCreated:
          format: date-time
          type: string
          description: Creation date of the Brand. This is in the format YYYY-MM-DD.
        lastUpdated:
          format: date-time
          type: string
          description: Last updated date of the Brand
        workspaceCount:
          type: number
          description: The number of workspaces associated with the brand
        updatedByPerson:
          description: Brand associations
          allOf:
            - $ref: '#/components/schemas/User'
        createdByPerson:
          $ref: '#/components/schemas/User'
        brandIdentifiers:
          type: array
          items:
            $ref: '#/components/schemas/BrandIdentifier'
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/Workspace'
      required:
        - id
        - name
        - description
        - deleted
        - updatedByPersonId
        - createdByPersonId
        - organizationId
        - dateCreated
        - lastUpdated
        - workspaceCount
        - updatedByPerson
        - createdByPerson
        - brandIdentifiers
        - workspaces
    ReadAdAccountBrandsDto:
      type: object
      properties:
        brands:
          type: array
          items:
            $ref: '#/components/schemas/Brand'
      required:
        - brands
    ReadWorkspaceBrandMapDto:
      type: object
      properties:
        workspaceId:
          type: number
          description: id of the Workspace (Partner)
          example: '123445'
        brandId:
          type: string
          description: id of the Brand
          example: 123e4567-e89b-12d3-a456-************
      required:
        - workspaceId
        - brandId
    LocalWorkspaceBrandMapDto:
      type: object
      properties:
        brandId:
          type: string
      required:
        - brandId
    WorkspaceBrandMapDto:
      type: object
      properties:
        workspaceId:
          type: number
        brandId:
          type: string
      required:
        - workspaceId
        - brandId
    CreateBrandIdentifierResultDto:
      type: object
      properties:
        id:
          type: string
          description: System assigned Id of the Brand Identifier
          example: 123e4567-e89b-12d3-a456-************
        brandId:
          type: string
          description: System assigned Id of the Brand
          example: 123e4567-e89b-12d3-a456-************
        identifier:
          type: string
          description: Identifier of the Brand
          example: ABC123
        dateCreated:
          format: date-time
          type: string
          description: Creation date of the Brand Identifier
        lastUpdated:
          format: date-time
          type: string
          description: Updated date of the Brand Identifier
      required:
        - id
        - brandId
        - identifier
        - dateCreated
        - lastUpdated
    CreateBrandIdentifierDto:
      type: object
      properties:
        identifier:
          type: string
      required:
        - identifier
    DeleteBrandIdentifierDto:
      type: object
      properties:
        id:
          type: string
          description: System assigned Id of the Brand Identifier
          example: 123e4567-e89b-12d3-a456-************
      required:
        - id
    UpdateBrandIdentifierDto:
      type: object
      properties:
        identifier:
          type: string
          description: The Identifier of the brand Identifier
          example: My Brand Identifier Vidmob
      required:
        - identifier
    AccountPermissionDto:
      type: object
      properties:
        adAccountId:
          type: string
        permission:
          type: string
      required:
        - adAccountId
        - permission
    UpdateBulkPlatformPermissionsDto:
      type: object
      properties:
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/AccountPermissionDto'
      required:
        - permissions
    UpdateAccountPlatformPermissionDto:
      type: object
      properties:
        platform:
          type: string
        permission:
          type: string
        organizationId:
          type: string
      required:
        - platform
        - permission
        - organizationId
    ReadPlatformAdAccountWithRebuildInfoDto:
      type: object
      properties:
        id:
          type: number
          description: Platform Ad Account Id
          example: 1234
        platform:
          type: string
          description: Platform for the Ad Account
          example: tiktok
        platformAccountName:
          type: string
          description: Platform Ad Account Name
          example: VidMob
        platformAccountId:
          type: string
          description: Platform Ad Account Id
          example: t2_t5vae3ih
        dateCreated:
          format: date-time
          type: string
          description: Date when the Platform Ad Account was created
          example: '2021-01-01T00:00:00.000Z'
        processingCompleted:
          type: number
          description: Date when the Platform Ad Account was processed
          example: '2021-01-01T00:00:00.000Z'
        processingCompletedDate:
          format: date-time
          type: string
          description: >-
            Tinyint (1 for true, 0 for false) when the Platform Ad Account was
            processed
          example: 1
        lastUpdated:
          format: date-time
          type: string
          description: Date when the Platform Ad Account was last updated
          example: '2021-01-01T00:00:00.000Z'
        canAccess:
          type: number
          description: >-
            Tinyint (1 for true, 0 for false) when the Platform Ad Account was
            last updated
          example: 1
        lastSuccessfulProcessingDate:
          format: date-time
          type: string
          description: Date when the Platform Ad Account was last successfully processed
          example: '2021-01-01T00:00:00.000Z'
        connected:
          type: boolean
          description: >-
            Tinyint (1 for true, 0 for false) when the Platform Ad Account was
            last successfully processed
          example: 1
      required:
        - id
        - platform
        - platformAccountName
        - platformAccountId
        - dateCreated
        - processingCompleted
        - processingCompletedDate
        - lastUpdated
        - canAccess
        - lastSuccessfulProcessingDate
        - connected
    String:
      type: object
      properties: {}
    OrganizationWithWorkspaceCountDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        associatedWorkspaces:
          type: number
        redirectWorkspaceId:
          type: number
      required:
        - id
        - name
    ReadAccountPlatformPermissionsDto:
      type: object
      properties:
        platform:
          type: string
          description: The platform of the ad account.
        adAccountName:
          type: string
          description: The name of the ad account.
        adAccountId:
          type: string
          description: The id of the ad account.
        dateCreated:
          format: date-time
          type: string
          description: The date created.
        permission:
          type: string
          description: permission
        canAccess:
          type: boolean
          description: can access flag
        processingComplete:
          type: boolean
          description: processing complete flag
        connectedInOtherOrganizations:
          type: boolean
          description: connected in other organizations flag
        importPriority:
          type: number
          description: Import priority
      required:
        - platform
        - adAccountName
        - adAccountId
        - dateCreated
        - permission
        - canAccess
        - processingComplete
        - connectedInOtherOrganizations
    WorkspacesAdAccountRequestDto:
      type: object
      properties:
        workspaces:
          type: array
          items:
            type: number
        platform:
          type: string
      required:
        - workspaces
        - platform
    ReadAdAccountMarketsRequestDto:
      type: object
      properties:
        workspaceIds:
          type: array
          items:
            type: number
      required:
        - workspaceIds
    CreateIndustryAdAccountsRequestDto:
      type: object
      properties:
        accountIds:
          description: List of ad account ids
          example:
            - '*****************'
            - t2_t5vae3ih
          type: array
          items:
            type: string
        selectedIndustryGroupId:
          type: number
          description: Selected IndustryGroup Id
        unselectedIndustryGroupIds:
          description: Unselected IndustryGroup Ids
          type: array
          items:
            type: number
        selectedIndustryId:
          type: number
          description: Selected Industry Id
        unselectedIndustryIds:
          description: Unselected Industry Ids
          type: array
          items:
            type: number
        selectedSubIndustryId:
          type: number
          description: Selected Sub-industry Id
        unselectedSubIndustryIds:
          description: Unselected Sub-industry Ids
          type: array
          items:
            type: number
      required:
        - accountIds
    AllUserWorkspaceDto:
      type: object
      properties:
        id:
          type: number
          description: The id of the workspace
          example: '12345'
        industry:
          type: string
          description: The industry of the workspace
          example: Alcohol
        isEnterprise:
          type: boolean
          description: Is workspace an enterprise workspace
          example: true
        publicAccountTypeName:
          type: string
          description: Workspace account name
          example: ABI
        accountTypeIdentifier:
          type: string
          description: Workspace account type identifier
          example: ENTERPRISE
        isPersonal:
          type: boolean
          description: Is workspace a personal workspace
          example: false
        name:
          type: string
          description: Workspace name
          example: ABI - Global
        logoUrl:
          type: string
          description: Url to workspace logo
          example: >-
            www.google.com/images/branding/googlelogo/1x/googlelogo_color_272x92dp.png
        isFindMyTeamEnabled:
          type: boolean
          description: Is workspace find my team enabled
          example: false
        organizationId:
          type: string
          description: Workspace organization id
          example: 123e4567-e89b-12d3-a456-************
      required:
        - id
        - industry
        - isEnterprise
        - publicAccountTypeName
        - accountTypeIdentifier
        - isPersonal
        - name
        - logoUrl
        - organizationId
    IndustryResponseDTO:
      type: object
      properties:
        id:
          type: number
          description: The unique ID of the industry
          example: 23
        name:
          type: string
          description: The name of the industry
          example: Apparel
        identifier:
          type: string
          description: The unique identifier for the industry
          example: RETAIL:APPAREL
      required:
        - id
        - name
        - identifier
    CreateOrganizationFeatureWhitelistDto:
      type: object
      properties:
        featureWhitelist:
          type: string
          description: Feature Whitelist identifier
        featureEnabled:
          type: boolean
          description: Feature Enabled
      required:
        - featureWhitelist
        - featureEnabled
    OrganizationUserRoleResponseDto:
      type: object
      properties:
        id:
          type: number
          description: Id of the role
          example: 123
        name:
          type: string
          description: Name of the role
        type:
          type: string
          description: Type of the role
          example: organization_entity
        identifier:
          type: string
          description: Identifier of the role
          example: ORG_ADMIN
        description:
          type: string
          description: Description of the role
      required:
        - id
        - name
        - type
        - identifier
        - description
    OrganizationUserResponseDto:
      type: object
      properties:
        id:
          type: number
          description: Id of the User
          example: 123
        username:
          type: string
          description: Username
          example: <EMAIL>
        firstName:
          type: string
          description: First name of user
          example: FirstName
        lastName:
          type: string
          description: Last name of user
          example: LastName
        displayName:
          type: string
          description: Display name of user
        email:
          type: string
          description: Email of user
          example: <EMAIL>
        jobTitle:
          type: string
          description: Job title of user
          example: Pilot
        photo:
          type: string
          description: Photo of user
          example: >-
            https://d2mcrmdbaugu8j.cloudfront.net/ABC123DEFHIJ/user/avatar/user-avatar.png
        roles:
          description: Roles of user on organization
          type: array
          items:
            $ref: '#/components/schemas/OrganizationUserRoleResponseDto'
        lastLoginDate:
          type: string
          description: Last login date
          example: '2024-04-04T18:50:10.000Z'
      required:
        - id
        - username
        - firstName
        - lastName
        - displayName
        - email
        - jobTitle
        - photo
        - roles
        - lastLoginDate
    UpdateOrganizationUserRoleDto:
      type: object
      properties:
        id:
          type: number
          description: Id of the role
          example: 123
      required:
        - id
    UpdateOrganizationUserDto:
      type: object
      properties:
        roles:
          description: Roles assigned to the user
          type: array
          items:
            $ref: '#/components/schemas/UpdateOrganizationUserRoleDto'
      required:
        - roles
    CreateBulkUserWorkspaceDto:
      type: object
      properties:
        users:
          type: array
          items:
            type: string
        workspaces:
          type: array
          items:
            type: number
        roleId:
          type: number
      required:
        - users
        - workspaces
        - roleId
    Number:
      type: object
      properties: {}
    IdNameDto:
      type: object
      properties:
        id:
          type: string
          description: object's id
          example: '23847238947238947'
        name:
          type: string
          description: object's name
          example: J&J
      required:
        - id
        - name
    LeaderboardItemDto:
      type: object
      properties:
        rank:
          type: number
          description: creative's rank by kpi value for given kpi and date range
          example: 1
        previousRank:
          type: number
          description: >-
            previous creative's rank by kpi value for given kpi

            previous timeframe is determined by time difference of request's
            start and endDate

            value is 0 if creative did not exist in previous timeframe's
            leaderboard (no data or not in top "count" of account's creatives)
          example: 3
        platformMediaId:
          type: string
          description: creative's platform ad account's media id
          example: 38ei489348932904
        kpiValue:
          type: number
          description: creative's KPI value for given KPI and date range
          example: 0.*********
        impressions:
          type: number
          description: creative's number of impressions for given date range
          example: 1000
        platformAdAccount:
          description: creative's platform ad account
          allOf:
            - $ref: '#/components/schemas/IdNameDto'
        campaigns:
          description: campaigns creative was featured in during given date range
          type: array
          items:
            $ref: '#/components/schemas/IdNameDto'
        isCreativeInMultiAssetAd:
          type: boolean
          description: Whether the creative is part of a multi-asset ad
      required:
        - rank
        - previousRank
        - platformMediaId
        - kpiValue
        - impressions
        - platformAdAccount
        - campaigns
        - isCreativeInMultiAssetAd
    GetLeaderboardRequestDto:
      type: object
      properties:
        startDate:
          type: string
          description: The start date via UTC timestamp; will always be Saturday midnight
          example: '2022-08-01'
        endDate:
          type: string
          description: >-
            The end date via UTC timestamp; will always be Saturday midnight a
            week

            after the start date
          example: '2022-08-08'
        workspaces:
          description: |-
            Array of workspaces to filter by. Optional.
            If not included, return all workspaces user has access to.
          example:
            - 8923
            - 553
            - 3232
          type: array
          items:
            type: number
        adAccountIds:
          description: Ad Account Ids to filter by. Optional.
          type: array
          items:
            type: string
        kpiId:
          type: string
          description: Kpi id to filter by.
          example: '103'
        platform:
          description: Platform name to filter by.
          example: FACEBOOK
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
          type: string
        limit:
          type: number
          description: limit for total number of creatives to return for leaderboard
          example: 1000
        limitPerAdAccount:
          type: number
          description: >-
            limit for number of top performing creatives to return for each ad
            account
          example: 20
        minimumDaysLive:
          type: number
          description: >-
            minimum number of days creative must be live to qualify for the
            leaderboard
          example: 7
        minimumImpressionsPerCreative:
          type: number
          description: >-
            creative's total impressions must be greater than or equal to this
            value for the given start and end dates to qualify for the
            leaderboard
          example: 1000
        advancedFilters:
          type: object
        currency:
          type: object
          description: Currency code to convert costs to
          default: USD
      required:
        - startDate
        - endDate
        - workspaces
        - kpiId
        - platform
        - limit
        - limitPerAdAccount
        - minimumDaysLive
        - minimumImpressionsPerCreative
        - currency
    LeaderboardAccountInfoDto:
      type: object
      properties:
        hasConnectedAdAccountsForWorkspaces:
          type: boolean
          description: The start date via UTC timestamp; will always be Saturday midnight
          example: '2022-08-01'
      required:
        - hasConnectedAdAccountsForWorkspaces
    DateRangeDto:
      type: object
      properties:
        startDate:
          type: string
        endDate:
          type: string
      required:
        - startDate
        - endDate
    GroupedCriteriaDetailsRequestDto:
      type: object
      properties:
        id:
          type: string
        criteriaIds:
          type: array
          items:
            type: number
        platform:
          type: object
        adAccountIds:
          type: array
          items:
            type: string
        dateRange:
          $ref: '#/components/schemas/DateRangeDto'
        mediaTypes:
          type: array
          items:
            type: object
        workspaceIds:
          type: array
          items:
            type: number
        advancedFilters:
          type: object
        shouldIncludeStandardCriteria:
          type: boolean
      required:
        - id
        - criteriaIds
        - platform
        - adAccountIds
        - dateRange
        - workspaceIds
    CriteriaPerformanceDto:
      type: object
      properties:
        id:
          type: string
          description: >-
            unique identifier for the criteria group, generated from the groupBy
            attributes
          example: BRAND_NAME_IN_AUDIO_NEAR_END:{maxFromEndAudioBrandMention:5}
        identifier:
          type: string
          description: unique criteria identifier
          example: BRAND_NAME_IN_AUDIO_NEAR_END
        parameters:
          type: object
          description: criteria parameters
          example: '{maxFromEndAudioBrandMention:5}'
        mediaCount:
          type: object
          description: ad account media count values for the criteria group
          example:
            met: 749484
            failed: 9484
            percentageMet: 98.********
        impressions:
          type: object
          description: impressions count values for the criteria group
          example:
            met: 749484
            failed: 9484
            total: 758968
        performanceByKpiId:
          type: object
          description: kpi values for the criteria group
          example:
            '103':
              met: 1.************
              failed: 1.************
              percentLift: 2.***************
              isStatisticallySignificant: false
            '**************:1':
              met: 14.*********
              failed: 17.*********
              percentLift: -16.***************
              isStatisticallySignificant: true
      required:
        - id
        - identifier
        - parameters
        - mediaCount
        - impressions
        - performanceByKpiId
    CriteriaPerformanceRequestDto:
      type: object
      properties:
        workspaceIds:
          description: workspace ids to query leaderboard data for
          example:
            - 100
            - 200
            - 300
          type: array
          items:
            type: number
        startDate:
          type: string
          description: start date to query leaderboard data from
          example: '2023-08-22'
        endDate:
          type: string
          description: last date to query leaderboard data to
          example: '2023-08-15'
        adAccountIds:
          description: platform ad account ids to query creative performance for
          example:
            - '***************'
            - 8340nhhohr30h098
            - '****************'
          type: array
          items:
            type: string
        kpiIds:
          description: id of platform KPI to query performance for
          example:
            - '103'
            - '**************:1'
          type: array
          items:
            type: string
        platform:
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
          type: string
          description: platform (aka channel) to filter by
          example: FACEBOOK
        statisticalConfidence:
          type: number
          description: >-
            the percentage statistical confidence level to use for determining
            if a criteria performance is statistically significant. Defaults to
            95
          example: 95
        groupBy:
          description: criteria attributes to roll up performance data by
          example:
            - identifier
            - parameters
          type: array
          items:
            type: object
        filters:
          type: object
          description: filters to apply to the criteria performance data
          example:
            criteriaGroupIds:
              - BRAND_NAME_OR_LOGO_PERSISTS:{minPercent:25}
              - JNJ_VISUAL_CTA_PRESENCE:{durationBeforeEnd:3}
            creativeMediaType:
              - video
              - image
        advancedFilters:
          type: object
          description: advanced filters to apply to the criteria performance data
          example:
            adsetIdentifiers:
              - adset1
              - adset2
            campaignObjectives:
              - BRAND_AWARENESS
              - CONVERSIONS
        isSpendKpiEnabled:
          type: boolean
          default: false
      required:
        - workspaceIds
        - startDate
        - endDate
        - adAccountIds
        - kpiIds
        - platform
    CreateElementFlagDto:
      type: object
      properties:
        mediaId:
          type: number
        tagValue:
          type: string
        tagType:
          type: string
        flagReason:
          type: object
        flagSource:
          type: object
        timestampsFlagged:
          type: array
          items:
            type: number
        additionalNotes:
          type: string
      required:
        - mediaId
        - tagValue
        - tagType
    ReadElementFlagResponseDto:
      type: object
      properties:
        id:
          type: string
        mediaId:
          type: number
        organizationId:
          type: string
        userId:
          type: number
        status:
          type: string
        tagValue:
          type: string
        tagType:
          type: string
        flagSource:
          type: string
        flagReason:
          type: string
        timestampsFlagged:
          type: array
          items:
            type: number
        additionalNotes:
          type: string
        dateCreated:
          format: date-time
          type: string
        lastUpdated:
          format: date-time
          type: string
        platformAccountId:
          type: string
          description: >-
            If you want to include the account ID derived from the nested
            `platformMedia`,

            but not the full platformMedia object, put it here:
        platform:
          type: string
          description: >-
            If you want the platform string (e.g. 'FACEBOOKPAGE') in the
            response,

            include this too.
      required:
        - id
        - mediaId
        - organizationId
        - userId
        - status
        - tagValue
        - tagType
        - flagSource
        - flagReason
        - dateCreated
        - lastUpdated
    ReadCustomAudienceDto:
      type: object
      properties:
        id:
          type: string
          description: An unique id of a custom audience for an account
          example: 0f6eea16c0327a5bb44b162b6cea1c7e
        platform:
          type: string
          description: Platform name
          example: FACEBOOK
        account:
          type: string
          description: Platform Account Id which a custom audience is from
          example: '****************'
        customAudienceId:
          type: string
          description: Custom Audience Id from a platform
          example: '*****************'
        name:
          type: string
          description: Custom Audience Name from a platform
          example: LAL (CA, 1%) - SG FB Page Eng
      required:
        - id
        - platform
        - account
        - customAudienceId
        - name
    NormativePerformanceRequestDto:
      type: object
      properties:
        platform:
          type: string
          description: Platform to fetch the normative performance for
          example: facebook
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
        startDate:
          type: string
          description: |-
            Start date to filter the normative performance data
            Should be start of a month
          example: '2020-01-01'
        endDate:
          type: string
          description: |-
            End date to filter the normative performance data
            Should be end of a month
          example: '2021-12-31'
        kpiIds:
          description: Kpi ids to fetch normative performance for
          type: array
          items:
            type: string
        industryIds:
          description: Industry ids to filter the normative performance data
          example:
            - 1
            - 21
            - 33
          type: array
          items:
            type: number
        markets:
          description: Market codes to filter the normative performance data
          example: '["usa", "deu", "gbr]'
          type: array
          items:
            type: string
        objectiveGroupIds:
          description: >-
            Objective group ids to filter the normative performance data. Must
            be one of [1, 2, 3]
          example:
            - 1
            - 2
            - 3
          type: array
          items:
            type: number
        currency:
          type: object
          description: Currency to convert the normative performance data to
      required:
        - platform
        - startDate
        - endDate
        - kpiIds
        - industryIds
    PlatformMediaGroupCreateDto:
      type: object
      properties:
        name:
          type: string
        platform:
          type: string
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
        startDate:
          type: string
        endDate:
          type: string
        privacyLevel:
          type: object
        platformMediaIds:
          type: array
          items:
            type: string
        adAccountIds:
          type: array
          items:
            type: string
        workspaceIds:
          type: array
          items:
            type: number
      required:
        - name
        - platform
        - privacyLevel
        - platformMediaIds
        - adAccountIds
        - workspaceIds
    PlatformMediaGroupRequestDto:
      type: object
      properties:
        platform:
          type: string
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
        adAccountIds:
          type: array
          items:
            type: string
        workspaceIds:
          type: array
          items:
            type: number
        sortBy:
          type: object
          default: lastUpdated
        sortDirection:
          type: object
          default: DESC
      required:
        - platform
        - adAccountIds
        - workspaceIds
        - sortBy
        - sortDirection
    PlatformMediaGroupIndividualRequestDto:
      type: object
      properties:
        workspaceIds:
          type: array
          items:
            type: number
      required:
        - workspaceIds
    PlatformMediaGroupUpdateDto:
      type: object
      properties:
        adAccountIds:
          type: array
          items:
            type: string
        name:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        privacyLevel:
          type: object
        platformMediaIds:
          type: array
          items:
            type: string
        workspaceIds:
          type: array
          items:
            type: number
      required:
        - adAccountIds
        - platformMediaIds
        - workspaceIds
    PlatformLogoDto:
      type: object
      properties:
        platform_identifier:
          type: string
          example: SNAPCHAT
          description: The unique identifier of the platform
        display_name:
          type: string
          example: Snapchat
          description: The display name of the platform
        logo_url:
          type: string
          example: https://example.com/logo-snapchat.svg
          description: URL of the platform logo
      required:
        - platform_identifier
        - display_name
        - logo_url
    PlatformLogoResponseDto:
      type: object
      properties:
        platformLogos:
          description: Array of platform logos
          type: array
          items:
            $ref: '#/components/schemas/PlatformLogoDto'
      required:
        - platformLogos
    Industry:
      type: object
      properties:
        id:
          type: number
        name:
          type: string
        parentId:
          type: number
          nullable: true
        rootId:
          type: number
          nullable: true
      required:
        - id
        - name
        - parentId
        - rootId
    IndustryWithSubIndustriesDto:
      type: object
      properties:
        id:
          type: number
        name:
          type: string
        subIndustries:
          type: array
          items:
            $ref: '#/components/schemas/Industry'
      required:
        - id
        - name
        - subIndustries
    MarketDto:
      type: object
      properties:
        isoCode:
          type: string
          description: The ISO code of the market
          example: BRA
        name:
          type: string
          description: The name of the market
          example: Brazil
      required:
        - isoCode
        - name
    RegionWithCountriesDto:
      type: object
      properties:
        region:
          type: string
        regionId:
          type: number
        countries:
          type: array
          items:
            $ref: '#/components/schemas/MarketDto'
      required:
        - region
        - regionId
        - countries
    UpdateInsightRequestDto:
      type: object
      properties:
        finding:
          type: string
        publish:
          type: boolean
        title:
          type: string
        recommendation:
          type: string
    FavoriteInsightRequestDto:
      type: object
      properties:
        favorite:
          type: boolean
      required:
        - favorite
    GenerateInsightTitleRequestDto:
      type: object
      properties:
        finding:
          type: string
        recommendation:
          type: string
    GenerateInsightTitleResponseDto:
      type: object
      properties:
        title:
          type: string
      required:
        - title
    CreateProjectInsightRequestDto:
      type: object
      properties:
        insightId:
          type: string
        projectIds:
          type: array
          items:
            type: number
      required:
        - insightId
        - projectIds
    CopilotInsightRequestDto:
      type: object
      properties:
        id:
          type: string
        activeWorkspaceId:
          type: number
        adAccountIds:
          type: array
          items:
            type: string
        adAccountIdsUserHasAccess:
          type: array
          items:
            type: string
        audienceIds:
          type: array
          items:
            type: string
        brandIds:
          type: array
          items:
            type: string
        brandFilters:
          type: object
        campaigns:
          type: array
          items:
            type: object
        categoryIds:
          type: array
          items:
            type: string
        copilotChatId:
          type: string
        endDate:
          type: number
        finding:
          type: string
        formats:
          type: array
          items:
            type: string
        industryId:
          type: number
        kpiIds:
          type: array
          items:
            type: number
        marketIds:
          type: array
          items:
            type: string
        mediaTypes:
          type: array
          items:
            type: string
        normativeMetadata:
          type: object
        copilotBrandMetadata:
          type: object
        objectiveIds:
          type: array
          items:
            type: number
        organizationId:
          type: string
        placementIds:
          type: array
          items:
            type: number
        platform:
          type: string
          enum:
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - dv360
            - facebook
            - facebookpage
            - instagrampage
            - linkedin
            - pinterest
            - reddit
            - snapchat
            - tiktok
            - twitter
        recommendation:
          type: string
        reportId:
          type: string
        reportType:
          type: string
          enum:
            - ELEMENT_IMPACT
            - MEDIA_IMPACT
            - ELEMENT_PRESENCE
            - CREATIVE_LEADERBOARD
            - CRITERIA_PERFORMANCE
            - CREATIVE_MANAGER
        source:
          type: string
          enum:
            - AUTOMATED
            - MANUAL
        startDate:
          type: number
        status:
          type: object
        title:
          type: string
        type:
          type: string
          enum:
            - BRAND
            - INDUSTRY
            - PLATFORM
            - COPILOT_BRAND
            - COPILOT_INDUSTRY
        workspaceIds:
          type: array
          items:
            type: number
      required:
        - id
        - organizationId
        - platform
        - source
        - status
        - title
        - type
    CreateCopilotInsightRequestDto:
      type: object
      properties:
        insightRequest:
          type: array
          items:
            $ref: '#/components/schemas/CopilotInsightRequestDto'
        organizationId:
          type: string
        workspaceIds:
          type: array
          items:
            type: number
        chatId:
          type: string
        messageId:
          type: string
      required:
        - insightRequest
        - organizationId
        - workspaceIds
        - chatId
        - messageId
    CreateCopilotInsightResponseDto:
      type: object
      properties:
        id:
          type: string
        insightLibraryId:
          type: string
      required:
        - id
        - insightLibraryId
    CreateInsightFolderRequestDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        parentFolderId:
          type: string
      required:
        - name
    FavoriteInsightFolderRequestDto:
      type: object
      properties:
        favorite:
          type: boolean
      required:
        - favorite
    ListFilteredInsightFolderRequestDto:
      type: object
      properties:
        favorited:
          type: boolean
          description: return only folders that have been favorited
          example: true
    MoveInsightRequestDto:
      type: object
      properties:
        parentFolderId:
          type: string
          nullable: true
        insightIds:
          type: array
          items:
            type: string
    MoveFolderRequestDto:
      type: object
      properties:
        parentFolderId:
          type: string
          nullable: true
        insightFolderIds:
          type: array
          items:
            type: string
    Sort:
      type: object
      properties: {}
    AggregationColumnDto:
      type: object
      properties:
        group:
          type: string
        name:
          type: string
        type:
          type: object
        matchers:
          type: object
      required:
        - group
        - name
        - type
        - matchers
    DataGridStateDto:
      type: object
      properties:
        rows:
          type: object
        columns:
          type: object
      required:
        - rows
        - columns
    ReportCreationResponseDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        reportType:
          enum:
            - ADHERENCE
            - IMPRESSION_ADHERENCE
            - ADOPTION
            - IN_FLIGHT
            - DIVERSITY
          type: string
        filtersVersion:
          type: number
        filters:
          type: object
        sortBy:
          $ref: '#/components/schemas/Sort'
        groupBy:
          type: object
        aggregationColumns:
          type: array
          items:
            $ref: '#/components/schemas/AggregationColumnDto'
        dataGridState:
          $ref: '#/components/schemas/DataGridStateDto'
      required:
        - name
        - description
        - reportType
        - filtersVersion
        - filters
        - sortBy
        - groupBy
        - aggregationColumns
    ReportMetadataOwnerDto:
      type: object
      properties:
        id:
          type: number
          description: Id of the User
          example: 123
        firstName:
          type: string
          description: First name of user
          example: FirstName
        lastName:
          type: string
          description: Last name of user
          example: LastName
        displayName:
          type: string
          description: Display name of user
        photo:
          type: string
          description: Photo of user
          example: >-
            https://d2mcrmdbaugu8j.cloudfront.net/ABC123DEFHIJ/user/avatar/user-avatar.png
      required:
        - id
        - firstName
        - lastName
        - displayName
        - photo
    AdAccountDto:
      type: object
      properties:
        platform_account_id:
          type: string
        name:
          type: string
      required:
        - platform_account_id
        - name
    ReportMetadataListItemDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        owner:
          $ref: '#/components/schemas/ReportMetadataOwnerDto'
        shared:
          type: boolean
        reportType:
          type: string
          enum:
            - ADHERENCE
            - IMPRESSION_ADHERENCE
            - ADOPTION
            - IN_FLIGHT
            - DIVERSITY
        dateCreated:
          format: date-time
          type: string
        lastUpdated:
          format: date-time
          type: string
        isLegacy:
          type: boolean
        adAccounts:
          type: array
          items:
            $ref: '#/components/schemas/AdAccountDto'
        channels:
          type: array
          items:
            type: string
      required:
        - id
        - name
        - description
        - owner
        - shared
        - reportType
        - dateCreated
        - lastUpdated
        - isLegacy
        - adAccounts
        - channels
    DateRange:
      type: object
      properties:
        startDate:
          type: string
          description: The start date of the date range
          example: '2021-01-01'
        endDate:
          type: string
          description: The end date of the date range
          example: '2021-12-31'
      required:
        - startDate
        - endDate
    GetScoringReportBodyDto:
      type: object
      properties:
        types:
          type: array
          items:
            type: string
            enum:
              - ADHERENCE
              - IMPRESSION_ADHERENCE
              - ADOPTION
              - IN_FLIGHT
              - DIVERSITY
        dateCreated:
          $ref: '#/components/schemas/DateRange'
        lastUpdated:
          $ref: '#/components/schemas/DateRange'
        createdBy:
          type: array
          items:
            type: number
        channels:
          type: array
          items:
            type: string
            enum:
              - adwords
              - amazonadvertising
              - amazonadvertisingdsp
              - dv360
              - facebook
              - facebookpage
              - instagrampage
              - linkedin
              - pinterest
              - reddit
              - snapchat
              - tiktok
              - twitter
        adAccounts:
          type: array
          items:
            type: string
    ReportFilterDto:
      type: object
      properties:
        fieldName:
          type: string
        operator:
          type: string
          enum:
            - equals
            - between
            - in
            - norms
            - less than
            - greater than
            - after
            - before
        value:
          type: object
      required:
        - fieldName
        - operator
        - value
    ReportMetadataDto:
      type: object
      properties:
        filters:
          description: The report's filters
          type: array
          items:
            $ref: '#/components/schemas/ReportFilterDto'
        analyticsFilters:
          type: object
        id:
          type: string
          description: The report id (uuid)
          example: e35377f7-9a37-4527-9f92-7f76b1d7d81a
        name:
          type: string
          description: The report name
          example: My report
        description:
          type: string
          description: The report description
          example: My personal report
        owner:
          description: The report owner
          example: >-
            { id: 12345, username: "<EMAIL>", firstName: "John",
            lastName: "Doe"}
          allOf:
            - $ref: '#/components/schemas/ReportMetadataOwnerDto'
        shared:
          type: boolean
          description: whether the report is shared or not
          example: true
        reportType:
          description: The report's type
          example: ADOPTION
          enum:
            - ADHERENCE
            - IMPRESSION_ADHERENCE
            - ADOPTION
            - IN_FLIGHT
            - DIVERSITY
          type: string
        dateCreated:
          format: date-time
          type: string
          description: The report's creation date
        lastUpdated:
          format: date-time
          type: string
          description: The report's update date
        criteriaLastCreatedDate:
          format: date-time
          type: string
          description: The date of the most recently added criteria included in the report
        filtersVersion:
          type: number
          description: The filters version
        sortBy:
          description: The report's sortBy
          allOf:
            - $ref: '#/components/schemas/Sort'
        groupBy:
          type: object
          description: The report's groupBy
        aggregationColumns:
          description: The report's aggregationColumns
          type: array
          items:
            $ref: '#/components/schemas/AggregationColumnDto'
        dataGridState:
          description: Whether the report is expanded or collapsed on the FE
          allOf:
            - $ref: '#/components/schemas/DataGridStateDto'
      required:
        - filters
        - id
        - name
        - description
        - owner
        - shared
        - reportType
        - dateCreated
        - lastUpdated
        - filtersVersion
        - sortBy
        - groupBy
        - aggregationColumns
    ReportFilterInDto:
      type: object
      properties:
        operator:
          type: string
          enum:
            - in
        value:
          oneOf:
            - type: array
              items:
                type: string
            - type: array
              items:
                type: number
        fieldName:
          type: string
      required:
        - operator
        - value
        - fieldName
    ReportFilterBetweenDto:
      type: object
      properties:
        operator:
          type: string
          enum:
            - between
        value:
          oneOf:
            - type: array
              items:
                type: string
            - type: array
              items:
                type: number
        fieldName:
          type: string
      required:
        - operator
        - value
        - fieldName
    ReportFilterEqualsDto:
      type: object
      properties:
        operator:
          type: string
          enum:
            - equals
        value:
          oneOf:
            - type: string
            - type: number
            - type: boolean
        fieldName:
          type: string
      required:
        - operator
        - value
        - fieldName
    CreateReportGroupByDto:
      type: object
      properties:
        columns:
          type: array
          items:
            type: string
        rows:
          type: array
          items:
            type: string
      required:
        - columns
        - rows
    ReportCreationRequestDto:
      type: object
      properties:
        filters:
          type: array
          description: The report's filters
          items:
            oneOf:
              - $ref: '#/components/schemas/ReportFilterEqualsDto'
              - $ref: '#/components/schemas/ReportFilterInDto'
              - $ref: '#/components/schemas/ReportFilterBetweenDto'
          example:
            - fieldName: market
              operator: in
              value:
                - United States
            - fieldName: workspace
              operator: in
              value:
                - 123
                - 456
                - 789
                - 12
            - fieldName: dateRange
              operator: between
              value:
                - '2020-01-01'
                - '2020-01-31'
            - fieldName: batchType
              operator: equals
              value: IN_FLIGHT
            - fieldName: channel
              operator: in
              value:
                - FACEBOOK
                - PINTEREST
        analyticsFilters:
          type: object
        workspaceId:
          type: number
          description: The workspace id
          example: 1234
        name:
          type: string
          description: Name of the report
          example: New report
        description:
          type: string
          description: Description of the report
          example: My personal new report
        reportType:
          type: string
          description: The type of the report
          example: ADOPTION
          enum:
            - ADHERENCE
            - IMPRESSION_ADHERENCE
            - ADOPTION
            - IN_FLIGHT
            - DIVERSITY
        filtersVersion:
          type: number
          description: The filters' version
          example: 1
        sortBy:
          description: The report's sortBy
          allOf:
            - $ref: '#/components/schemas/Sort'
        groupBy:
          description: The report's groupBy
          allOf:
            - $ref: '#/components/schemas/CreateReportGroupByDto'
      required:
        - filters
        - workspaceId
        - name
        - description
        - reportType
        - filtersVersion
        - sortBy
        - groupBy
    ImpressionsRange:
      type: object
      properties:
        minimum:
          type: number
        maximum:
          type: number
    StatSettings:
      type: object
      properties:
        direction:
          type: string
          enum:
            - vertical
            - horizontal
        statConfidence:
          type: number
          enum:
            - 95
            - 90
            - 85
        average:
          type: string
          enum:
            - element
            - kpi
        baseComparison:
          type: string
          enum:
            - impressions
            - formattedValue
            - statLiftAgainst
            - adVideoCount
        minimumTagConfidenceLevel:
          type: number
          enum:
            - 65
            - 75
            - 85
            - 95
        isDefaultKPITimeRangeActive:
          type: boolean
      required:
        - direction
        - statConfidence
        - average
        - baseComparison
        - minimumTagConfidenceLevel
    NormsConfigurationDateRange:
      type: object
      properties:
        scope:
          type: string
        scopeKey:
          type: string
        startDate:
          type: string
        endDate:
          type: string
      required:
        - scope
        - scopeKey
        - startDate
        - endDate
    NormsConfigurationIndustries:
      type: object
      properties:
        industryIds:
          type: array
          items:
            type: number
        subIndustryIds:
          type: array
          items:
            type: number
      required:
        - industryIds
        - subIndustryIds
    NormsConfigurationMarkets:
      type: object
      properties:
        regionIds:
          type: array
          items:
            type: number
        countries:
          type: array
          items:
            type: string
      required:
        - regionIds
        - countries
    NormsConfiguration:
      type: object
      properties:
        dateRange:
          $ref: '#/components/schemas/NormsConfigurationDateRange'
        industries:
          $ref: '#/components/schemas/NormsConfigurationIndustries'
        markets:
          $ref: '#/components/schemas/NormsConfigurationMarkets'
        objectiveGroupIds:
          type: array
          items:
            type: number
      required:
        - dateRange
        - industries
    CurrencyDto:
      type: object
      properties:
        id:
          type: string
          description: Currency ISO code
          example: USD
        name:
          type: string
          description: Currency origin and name
        symbol:
          type: string
          description: >-
            Currency symbol (if available -- some currencies do not have a
            symbol)
          example: $
      required:
        - id
        - name
    AnalyticsReportFilterDto:
      type: object
      properties:
        organizationId:
          type: string
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/Workspace'
        adAccounts:
          type: array
          items:
            type: object
        platform:
          type: string
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
        dateRange:
          $ref: '#/components/schemas/DateRange'
        kpiId:
          type: string
        viewBy:
          type: string
          enum:
            - ad_group_type_detailed
            - ad_type_placement
            - audience
            - campaign
            - creative
            - creative_type
            - device_placement
            - duration
            - element_presence
            - high_level_objectives
            - detailed_objectives
            - normal_ad_type
            - placement
            - platform
            - platform_placement
            - ratio_format
            - vidmob_vs_non_vidmob
            - media_type
            - market
            - brand
            - kpi
            - none
        mediaTypes:
          type: array
          items:
            type: string
            enum:
              - video
              - image
        adTypes:
          type: array
          items:
            type: string
        columnSelections:
          type: object
        campaignIds:
          type: array
          items:
            type: string
        campaignSearchStrings:
          type: array
          items:
            type: string
        adsetIds:
          type: array
          items:
            type: string
        adsetSearchStrings:
          type: array
          items:
            type: string
        adIds:
          type: array
          items:
            type: string
        adSearchString:
          type: array
          items:
            type: string
        creativeImpressionsRange:
          $ref: '#/components/schemas/ImpressionsRange'
        adImpressionsRange:
          $ref: '#/components/schemas/ImpressionsRange'
        objectives:
          type: array
          items:
            type: string
        placements:
          type: array
          items:
            type: string
        criteriaGroupIds:
          type: array
          items:
            type: string
        selectedRows:
          type: array
          items:
            required: false
            type: array
            items:
              type: string
        isDefaultKPITimeRangeFilterActive:
          type: boolean
        createdByVidmob:
          type: boolean
        isShowAppAdsOnlyActive:
          type: boolean
        isShowSparkAdsOnlyActive:
          type: boolean
        statSettings:
          $ref: '#/components/schemas/StatSettings'
        normsConfiguration:
          $ref: '#/components/schemas/NormsConfiguration'
        currency:
          $ref: '#/components/schemas/CurrencyDto'
      required:
        - organizationId
        - workspaces
        - adAccounts
        - platform
        - dateRange
        - mediaTypes
        - selectedRows
    UserAccessStatusDto:
      type: object
      properties:
        accountsAccessStatus:
          type: object
        workspacesAccessStatus:
          type: object
      required:
        - accountsAccessStatus
        - workspacesAccessStatus
    ReadReportUserDto:
      type: object
      properties:
        id:
          type: number
          description: Id of the User
          example: 123
        firstName:
          type: string
          description: First name of user
          example: FirstName
        lastName:
          type: string
          description: Last name of user
          example: LastName
        displayName:
          type: string
          description: Display name of user
        photo:
          type: string
          description: Photo of user
          example: >-
            https://d2mcrmdbaugu8j.cloudfront.net/ABC123DEFHIJ/user/avatar/user-avatar.png
      required:
        - id
        - firstName
        - lastName
        - displayName
        - photo
    AnalyticsReportGroupByDto:
      type: object
      properties:
        columns:
          type: string
          enum:
            - ad_group_type_detailed
            - ad_type_placement
            - audience
            - campaign
            - creative
            - creative_type
            - device_placement
            - duration
            - element_presence
            - high_level_objectives
            - detailed_objectives
            - normal_ad_type
            - placement
            - platform
            - platform_placement
            - ratio_format
            - vidmob_vs_non_vidmob
            - media_type
            - market
            - brand
            - kpi
            - none
        rows:
          type: string
          enum:
            - kpi
            - element
            - criteria
            - none
            - ungrouped
            - criteriaGroup
      required:
        - columns
        - rows
    AnalyticsReportDto:
      type: object
      properties:
        filtersVersion:
          type: number
        filters:
          $ref: '#/components/schemas/AnalyticsReportFilterDto'
        userAccessStatus:
          $ref: '#/components/schemas/UserAccessStatusDto'
        id:
          type: string
        createdBy:
          $ref: '#/components/schemas/ReadReportUserDto'
        dateCreated:
          format: date-time
          type: string
        lastUpdated:
          format: date-time
          type: string
        name:
          type: string
        description:
          type: string
        reportType:
          enum:
            - ELEMENT_IMPACT
            - MEDIA_IMPACT
            - ELEMENT_PRESENCE
            - CREATIVE_LEADERBOARD
            - CRITERIA_PERFORMANCE
            - CREATIVE_MANAGER
          type: string
        sortBy:
          $ref: '#/components/schemas/Sort'
        groupBy:
          $ref: '#/components/schemas/AnalyticsReportGroupByDto'
      required:
        - filters
        - userAccessStatus
        - id
        - createdBy
        - dateCreated
        - lastUpdated
        - name
        - reportType
    GetAnalyticsReportBodyDto:
      type: object
      properties:
        types:
          type: array
          items:
            type: string
            enum:
              - ELEMENT_IMPACT
              - MEDIA_IMPACT
              - ELEMENT_PRESENCE
              - CREATIVE_LEADERBOARD
              - CRITERIA_PERFORMANCE
              - CREATIVE_MANAGER
        searchTerm:
          type: string
        dateCreated:
          $ref: '#/components/schemas/DateRange'
        lastUpdated:
          $ref: '#/components/schemas/DateRange'
        createdBy:
          type: array
          items:
            type: number
        channels:
          type: array
          items:
            type: string
            enum:
              - adwords
              - amazonadvertising
              - amazonadvertisingdsp
              - dv360
              - facebook
              - facebookpage
              - instagrampage
              - linkedin
              - pinterest
              - reddit
              - snapchat
              - tiktok
              - twitter
        workspaces:
          type: array
          items:
            type: number
        adAccounts:
          type: array
          items:
            type: string
      required:
        - types
    RenameAnalyticsReportDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
    GetAnalyticsReportFilterOptionsRequestDto:
      type: object
      properties:
        platform:
          description: The platform to get filter options for
          enum:
            - facebook
            - snapchat
            - tiktok
            - pinterest
            - dv360
            - linkedin
            - twitter
            - reddit
            - adwords
            - amazonadvertising
            - amazonadvertisingdsp
            - instagrampage
            - facebookpage
          type: string
        filterType:
          description: Type of filter to fetch filter options for
          enum:
            - campaignIdentifier
            - campaignObjective
            - adSetIdentifier
            - adIdentifier
            - adPlacement
            - adType
            - kpi
            - criteria
          type: string
        workspaceIds:
          description: The workspaces to get filter options for
          type: array
          items:
            type: number
        adAccountIds:
          description: The platform ad account ids to get filter options for
          example:
            - '****************'
            - s9sq99e80io39i82
          type: array
          items:
            type: string
        dateRange:
          description: The date range to get filter options for
          example: '{ startDate: "2021-01-01", endDate: "2021-01-31" }'
          allOf:
            - $ref: '#/components/schemas/DateRange'
        mediaTypes:
          description: The media types to get filter options for
          example:
            - video
            - image
          type: array
          items:
            type: string
            enum:
              - video
              - image
        searchString:
          type: string
          description: String to search for in filter options. Case-insensitive search.
          example: _campaign_
        shouldIncludeStandardCriteria:
          type: boolean
          description: >-
            whether to include standard criteria in the results. Only applies to
            criteria filter. Defaults to false
          example: true
        shouldIncludeAllSpendKpis:
          type: boolean
          description: Whether to include spend kpis for all platforms. Defaults to false
      required:
        - platform
        - filterType
    FilterItem:
      type: object
      properties:
        id:
          type: string
          description: The id of the filter object
          example: 720
        name:
          type: string
          description: The name of the filter object
          example: Awareness
        values:
          description: >-
            The nested values for this filter object. eg. campaign objectives
            have nested values
          example:
            - id: 854
              name: REACH
            - id: 855
              name: RF_REACH
          type: array
          items:
            $ref: '#/components/schemas/FilterItem'
      required:
        - id
        - name
    AnalyticsCopilotNextPayload:
      type: object
      properties:
        userInput:
          type: string
        userId:
          type: string
        chatId:
          type: string
      required:
        - userInput
        - userId
    AnalyticsCopilotInsightBigQueryResponseItem:
      type: object
      properties:
        element:
          type: string
        kpi_lift:
          type: string
        element_avg_kpi:
          type: number
        element_hierarchy:
          type: string
        category_lift:
          type: string
        tag_type:
          type: string
        element_ad_video_count:
          type: number
        element_impressions:
          type: number
        element_percent_lift:
          type: number
        element_kpi_value:
          type: number
      required:
        - element
        - kpi_lift
        - element_avg_kpi
    AnalyticsCopilotInsightFunctionResponse:
      type: object
      properties:
        platform:
          type: string
        industry_name:
          type: string
        kpi_name:
          type: string
        start_date:
          type: string
        overall_avg:
          type: number
        end_date:
          type: string
        level_ad_video_count:
          type: number
        level_impressions:
          type: number
        account_counts:
          type: number
        level_kpi_value:
          type: number
        inverse_health:
          type: boolean
        kpi_format:
          type: string
        advertisingData:
          type: array
          items:
            $ref: '#/components/schemas/AnalyticsCopilotInsightBigQueryResponseItem'
      required:
        - platform
        - industry_name
        - kpi_name
        - start_date
        - overall_avg
        - end_date
        - advertisingData
    AnalyticsCopilotNextResponse:
      type: object
      properties:
        id:
          type: string
        answer:
          type: object
        log:
          type: string
        messageId:
          type: string
        insight:
          $ref: '#/components/schemas/AnalyticsCopilotInsightFunctionResponse'
      required:
        - id
        - answer
    AnalyticsCopilotConversationListItem:
      type: object
      properties:
        id:
          type: string
        title:
          type: string
        timestamp:
          type: string
      required:
        - id
        - title
        - timestamp
    AnalyticsCopilotGptFunction:
      type: object
      properties:
        name:
          type: string
        arguments:
          type: string
      required:
        - name
        - arguments
    AnalyticsCopilotConversationHistory:
      type: object
      properties:
        role:
          enum:
            - system
            - user
            - assistant
            - function
          type: string
        name:
          type: string
        content:
          type: object
        additional_comments:
          type: string
        reason_type:
          enum:
            - INACCURATE_RESPONSE
            - UNCLEAR_RESPONSE
            - IRRELEVANT_RESPONSE
            - OTHER_RESPONSE
          type: string
        function_call:
          $ref: '#/components/schemas/AnalyticsCopilotGptFunction'
        feedback:
          enum:
            - GOOD
            - BAD
          type: string
        id:
          type: string
        insight:
          $ref: '#/components/schemas/AnalyticsCopilotInsightFunctionResponse'
      required:
        - role
    UpdateMessageFeedbackDto:
      type: object
      properties:
        feedback:
          enum:
            - GOOD
            - BAD
          type: string
        additionalComments:
          type: string
        reasonType:
          enum:
            - INACCURATE_RESPONSE
            - UNCLEAR_RESPONSE
            - IRRELEVANT_RESPONSE
            - OTHER_RESPONSE
          type: string
      required:
        - feedback
    BrandCopilotNextRequest:
      type: object
      properties: {}
    BrandCopilotNextResponse:
      type: object
      properties: {}
    MediaAnnotationBoundingBoxDto:
      type: object
      properties:
        Height:
          type: number
        Width:
          type: number
        Left:
          type: number
        Top:
          type: number
        height:
          type: number
        width:
          type: number
        left:
          type: number
        top:
          type: number
      required:
        - Height
        - Width
        - Left
        - Top
    MediaAnnotationTagDto:
      type: object
      properties:
        value:
          type: string
        type:
          type: string
        confidence:
          type: number
        startTime:
          type: number
        duration:
          type: number
        boundingBox:
          type: array
          items:
            $ref: '#/components/schemas/MediaAnnotationBoundingBoxDto'
        parents:
          type: array
          items:
            $ref: '#/components/schemas/MediaAnnotationTagDto'
      required:
        - value
        - type
    FrontendFilterOption:
      type: object
      properties:
        search:
          type: string
        reportIds:
          type: array
          items:
            type: string
        reportNames:
          type: array
          items:
            type: string
        reportTypes:
          type: array
          items:
            type: string
            enum:
              - Creative Scoring
              - Creative Analytics
              - Creative Elements
        reportStates:
          type: array
          items:
            type: string
            enum:
              - Active
              - Inactive
        reportStatuses:
          type: array
          items:
            type: string
            enum:
              - Completed
              - Deleted
              - Failed
              - Processing
              - Archived
        createdOnDates:
          type: array
          items:
            type: string
        createdByIds:
          type: array
          items:
            type: number
        startDate:
          type: string
        endDate:
          type: string
    CreateFilteredReportRequestDto:
      type: object
      properties:
        filter:
          $ref: '#/components/schemas/FrontendFilterOption'
        sort:
          type: array
          items:
            type: object
      required:
        - filter
    IdAndName:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
      required:
        - id
        - name
    ReadDataExportReportOrganizationDto:
      type: object
      properties:
        adAccounts:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        brands:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        channels:
          type: array
          items:
            type: string
        markets:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        downloadUrl:
          type: string
        reportId:
          type: string
        reportName:
          type: string
        reportType:
          type: object
        status:
          type: object
        createdBy:
          $ref: '#/components/schemas/IdAndName'
        dateCreated:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        recordCount:
          type: number
        hasData:
          type: boolean
        expiration:
          type: string
        failureReason:
          type: string
      required:
        - channels
        - workspaces
        - reportId
        - reportName
        - reportType
        - status
        - createdBy
        - dateCreated
        - startDate
        - endDate
    PersonDto:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        url:
          type: string
      required:
        - id
        - name
        - url
    GetAllScheduledExportsFilterOption:
      type: object
      properties:
        search:
          type: string
        reportIds:
          type: array
          items:
            type: string
        exportNames:
          type: array
          items:
            type: string
        exportTypes:
          type: array
          items:
            type: string
        lastSuccessfulDates:
          type: array
          items:
            type: string
        nextExportDates:
          type: array
          items:
            type: string
        exportFrequencies:
          type: array
          items:
            type: string
        locations:
          type: array
          items:
            type: string
        startDates:
          type: array
          items:
            type: string
        endDates:
          type: array
          items:
            type: string
        runsCompleted:
          type: array
          items:
            type: number
        runsRemaining:
          type: array
          items:
            type: number
        runsIndefinitely:
          type: array
          items:
            type: boolean
        createdOnDates:
          type: array
          items:
            type: string
        createdBys:
          type: array
          items:
            $ref: '#/components/schemas/PersonDto'
      required:
        - exportTypes
    SortByOption:
      type: object
      properties:
        field:
          type: string
        sortOrder:
          type: string
          enum:
            - ASC
            - DESC
      required:
        - field
        - sortOrder
    GetAllScheduledExportsRequestDto:
      type: object
      properties:
        filter:
          $ref: '#/components/schemas/GetAllScheduledExportsFilterOption'
        sort:
          type: array
          items:
            $ref: '#/components/schemas/SortByOption'
      required:
        - filter
    FrontEndBasicReadReportDto:
      type: object
      properties:
        reportId:
          type: string
        reportName:
          type: string
        reportType:
          type: string
          enum:
            - Creative Scoring
            - Creative Analytics
            - Creative Elements
        status:
          type: string
          enum:
            - Completed
            - Deleted
            - Failed
            - Processing
            - Archived
        createdBy:
          $ref: '#/components/schemas/IdAndName'
        dateCreated:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        recordCount:
          type: number
        hasData:
          type: boolean
        expiration:
          type: string
        failureReason:
          type: string
      required:
        - reportId
        - reportName
        - reportType
        - status
        - createdBy
        - dateCreated
        - startDate
        - endDate
    ReadOneDataExportReportOrganizationDto:
      type: object
      properties:
        adAccounts:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        brands:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        channels:
          type: array
          items:
            type: string
        markets:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/IdAndName'
        downloadUrl:
          type: string
        reportId:
          type: string
        reportName:
          type: string
        reportType:
          type: object
        status:
          type: object
        createdBy:
          $ref: '#/components/schemas/IdAndName'
        dateCreated:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        recordCount:
          type: number
        hasData:
          type: boolean
        expiration:
          type: string
        failureReason:
          type: string
      required:
        - channels
        - workspaces
        - reportId
        - reportName
        - reportType
        - status
        - createdBy
        - dateCreated
        - startDate
        - endDate
    CreateDataExportOrganizationRequestDto:
      type: object
      properties:
        reportType:
          type: object
        reportName:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        workspaces:
          type: array
          items:
            type: number
        channels:
          type: array
          items:
            type: string
        brands:
          type: array
          items:
            type: string
        markets:
          type: array
          items:
            type: string
        adAccounts:
          type: array
          items:
            type: string
      required:
        - reportType
        - reportName
        - startDate
        - endDate
        - workspaces
        - channels
    CreateDataExportBFFResponseDto:
      type: object
      properties:
        report_id:
          type: string
          description: Unique identifier for the report
          example: 1251asd-asfd-1355-asd2-13536fdsfg
        status:
          type: string
          description: Report status
          example: QUEUED
        message:
          type: string
          description: Success or error message about the request
          example: Export data requested successfully
        date_created:
          type: string
          description: Date the export was created
          example: '2024-05-29 22:46:46'
      required:
        - report_id
        - status
        - message
        - date_created
    GetDataExportFilterQueryParamDto:
      type: object
      properties:
        startDate:
          type: string
        endDate:
          type: string
    FrontendFilterValues:
      type: object
      properties:
        exportType:
          type: array
          items:
            type: string
            enum:
              - Creative Scoring
              - Creative Analytics
              - Creative Elements
        createdOn:
          type: array
          items:
            type: string
        createdBy:
          type: array
          items:
            type: object
        status:
          type: array
          items:
            type: string
            enum:
              - Completed
              - Deleted
              - Failed
              - Processing
              - Archived
        channel:
          type: array
          items:
            type: string
        workspace:
          type: array
          items:
            type: object
        adAccount:
          type: array
          items:
            type: object
        market:
          type: array
          items:
            type: object
        brand:
          type: array
          items:
            type: object
      required:
        - exportType
        - createdOn
        - createdBy
        - status
    AccessTokenResponseDto:
      type: object
      properties:
        tokenType:
          type: string
        accessToken:
          type: string
        refreshToken:
          type: string
        expiresIn:
          type: number
      required:
        - tokenType
        - accessToken
        - refreshToken
        - expiresIn
    RevokeTokenResponseDto:
      type: object
      properties:
        message:
          type: string
      required:
        - message
    GetCreativeLifecycleDto:
      type: object
      properties:
        organizationId:
          type: string
        startDate:
          type: string
        endDate:
          type: string
        groupBy:
          type: string
      required:
        - organizationId
        - startDate
        - endDate
        - groupBy
    Person:
      type: object
      properties:
        id:
          type: string
          example: '12345'
          description: The unique identifier of the entity
        name:
          type: string
          example: Example Name
          description: The name of the entity
        photoUrl:
          type: string
      required:
        - id
        - name
        - photoUrl
    ConnectorsDTO:
      type: object
      properties:
        connectorId:
          type: string
        connectorName:
          type: string
        path:
          type: string
        s3BucketName:
          type: string
        createdOn:
          type: string
        createdBy:
          $ref: '#/components/schemas/Person'
      required:
        - connectorId
        - connectorName
        - path
        - s3BucketName
        - createdOn
        - createdBy
    CreateConnectorRequestDto:
      type: object
      properties:
        connectorName:
          type: string
          description: The name of the connector
          example: Amazon S3 Connector
        path:
          type: string
          description: The path to the connector resource
          example: /path/to/resource
        s3BucketName:
          type: string
          description: The name of the S3 bucket where data is stored
          example: my-s3-bucket
        userRoleARN:
          type: string
          description: The AWS role ARN for user authentication
          example: arn:aws:iam::123456789012:role/S3AccessRole
      required:
        - connectorName
        - path
        - s3BucketName
        - userRoleARN
    CreateConnectorResponseDto:
      type: object
      properties:
        message:
          type: string
          description: Success or error message about the request
          example: Connector successfully created
      required:
        - message
    PlatformMediaRequestDto:
      type: object
      properties:
        platformMediaIds:
          type: array
          items:
            type: string
        workspaceIds:
          type: array
          items:
            type: number
        adAccountIds:
          type: array
          items:
            type: string
        platform:
          type: object
        startDate:
          type: string
        endDate:
          type: string
      required:
        - platformMediaIds
        - workspaceIds
        - adAccountIds
        - platform
        - startDate
        - endDate
    ReportFilterDtoV2:
      type: object
      properties:
        key:
          type: string
        operator:
          type: string
          enum:
            - equals
            - between
            - in
            - norms
            - less than
            - greater than
            - after
            - before
        value:
          type: object
      required:
        - key
        - operator
        - value
    DashboardFilterDto:
      type: object
      properties:
        filters:
          description: The filter definitions to apply to the dashboard
          type: array
          items:
            $ref: '#/components/schemas/ReportFilterDtoV2'
        sortBy:
          description: The sort configuration for the dashboard
          allOf:
            - $ref: '#/components/schemas/ReportFilterDtoV2'
        groupBy:
          description: The group by configuration for the dashboard
          allOf:
            - $ref: '#/components/schemas/CreateReportGroupByDto'
      required:
        - filters
    UserDto:
      type: object
      properties:
        id:
          type: number
          example: 21075
        displayName:
          type: string
          example: John Doe
        photoUrl:
          type: string
          example: photo
      required:
        - id
        - displayName
        - photoUrl
    WidgetDto:
      type: object
      properties:
        id:
          type: string
          example: 77777777-7777-7777-7777-777777777777
        widgetType:
          type: string
          example: AVERAGE_ADHERENCE_SCORE
        name:
          type: string
          example: Average Adherence Score
        description:
          type: string
          example: Displays the overall average adherence score
        parameters:
          type: object
          description: Widget type-specific parameters (JSON)
        gridX:
          type: number
          example: 0
        gridY:
          type: number
          example: 0
        gridWidth:
          type: number
          example: 4
        gridHeight:
          type: number
          example: 2
        visualizationType:
          type: string
          example: METRIC
        filter:
          description: Widget specific filter configuration
          allOf:
            - $ref: '#/components/schemas/DashboardFilterDto'
        isCompareToPreviousPeriodEnabled:
          type: boolean
          example: false
        isViewDataLabelsEnabled:
          type: boolean
          example: false
        dateCreated:
          type: string
          example: '2025-05-07T00:00:00Z'
        lastUpdated:
          type: string
          example: '2025-05-07T00:00:00Z'
        createdBy:
          $ref: '#/components/schemas/UserDto'
        lastModifiedBy:
          $ref: '#/components/schemas/UserDto'
      required:
        - widgetType
        - name
        - description
        - gridX
        - gridY
        - gridWidth
        - gridHeight
        - visualizationType
        - isCompareToPreviousPeriodEnabled
        - isViewDataLabelsEnabled
    ReadDashboardDto:
      type: object
      properties:
        id:
          type: string
          example: 66666666-6666-6666-6666-666666666666
          description: Unique identifier for the dashboard
        name:
          type: string
          example: Enterprise Adherence Overview
          description: Human-readable name of the dashboard
        description:
          type: string
          nullable: true
          example: Instance created from the Enterprise Adherence Overview preset
          description: Optional long description of the dashboard
        photoUrl:
          type: string
          example: https://example.com/dashboard/12345
          description: Optional URL for the dashboard
          nullable: true
        dashboardFilter:
          description: Global filter configuration applied to this dashboard
          nullable: true
          allOf:
            - $ref: '#/components/schemas/DashboardFilterDto'
        isFavorite:
          type: boolean
          example: true
          description: Flag indicating whether the dashboard is marked as a favorite
        sharingScope:
          enum:
            - PRIVATE
            - ORGANIZATION
            - WORKSPACE
          type: string
          example: PRIVATE
          description: Sharing scope of the dashboard (PRIVATE, ORGANIZATION, WORKSPACE)
        dateCreated:
          type: string
          example: '2025-05-07T00:00:00Z'
          description: ISO timestamp when the dashboard was created
        lastUpdated:
          type: string
          example: '2025-05-07T00:00:00Z'
          description: ISO timestamp when the dashboard was last updated
        createdBy:
          description: User who originally created the dashboard
          allOf:
            - $ref: '#/components/schemas/UserDto'
        lastModifiedBy:
          description: User who last modified the dashboard
          allOf:
            - $ref: '#/components/schemas/UserDto'
        widgets:
          description: List of widgets belonging to this dashboard (empty array if none)
          type: array
          items:
            $ref: '#/components/schemas/WidgetDto'
      required:
        - id
        - name
        - isFavorite
        - sharingScope
        - dateCreated
        - lastUpdated
        - createdBy
        - lastModifiedBy
    CreateDashboardDto:
      type: object
      properties:
        sourceDashboardId:
          type: string
          description: If cloning an existing dashboard, the ID of the source to copy from
          format: uuid
        name:
          type: string
          description: Name of the new dashboard
        description:
          type: string
          description: Description for the dashboard
        dashboardFilter:
          description: The global filter definition to apply to the dashboard
          allOf:
            - $ref: '#/components/schemas/DashboardFilterDto'
        isFavorite:
          type: boolean
          description: If the widget is favorited by the user
        sharingScope:
          default: PRIVATE
          enum: &ref_98
            - PRIVATE
            - ORGANIZATION
            - WORKSPACE
          type: string
          description: The scope within which the dashboard is shared
        widgets:
          type: array
          items:
            required: false
            description: The widgets to add to the dashboard
            type: array
            items:
              $ref: '#/components/schemas/WidgetDto'
      required:
        - widgets
    ListDashboardsFilterDto:
      type: object
      properties:
        searchTerm:
          type: string
          description: filters the result by name OR description (ILIKE '%searchTerm%')
        dateCreated:
          description: filter by creation date range
          allOf:
            - $ref: '#/components/schemas/DateRangeDto'
        lastUpdated:
          description: filter by last-updated date range
          allOf:
            - $ref: '#/components/schemas/DateRangeDto'
    ListDashboardsDto:
      type: object
      properties:
        filter:
          $ref: '#/components/schemas/ListDashboardsFilterDto'
    UpdateDashboardDto:
      type: object
      properties:
        sourceDashboardId:
          type: string
          description: If cloning an existing dashboard, the ID of the source to copy from
          format: uuid
        name:
          type: string
          description: Name of the new dashboard
        description:
          type: string
          description: Description for the dashboard
        dashboardFilter:
          description: The global filter definition to apply to the dashboard
          allOf:
            - $ref: '#/components/schemas/DashboardFilterDto'
        isFavorite:
          type: boolean
          description: If the widget is favorited by the user
        sharingScope:
          default: PRIVATE
          enum: *ref_98
          type: string
          description: The scope within which the dashboard is shared
        widgets:
          type: array
          items:
            required: false
            description: The widgets to add to the dashboard
            type: array
            items:
              $ref: '#/components/schemas/WidgetDto'
      required:
        - widgets
    UpdateDashboardFavoriteDto:
      type: object
      properties:
        isFavorite:
          type: boolean
          description: True to favorite, false to unfavorite
      required:
        - isFavorite
    CreateWidgetDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        widgetType:
          enum: &ref_99
            - ASSETS_SCORED
            - IMPRESSION_ANALYZED
            - CHANNEL_ADHERENCE_TRENDS
            - ASSET_OVERVIEW
            - KEY_FINDINGS
            - ASSET_TRENDS
            - CRITERIA_PERFORMANCE
            - ADHERENCE_SCORE
          type: string
        visualizationType:
          enum: &ref_100
            - METRIC
            - BAR
            - COLUMN
            - DONUT
            - TABLE
            - LINE
            - TEXT
          type: string
        parameters:
          type: object
        filter:
          $ref: '#/components/schemas/DashboardFilterDto'
        isCompareToPreviousPeriodEnabled:
          type: boolean
        isViewDataLabelsEnabled:
          type: boolean
        gridX:
          type: number
        gridY:
          type: number
        gridWidth:
          type: number
        gridHeight:
          type: number
        viewByPeriod:
          enum:
            - DAY
            - WEEK
            - MONTH
            - QUARTER
            - YEAR
          type: string
        isIncludeTotalEnabled:
          type: boolean
    ReadWidgetDto:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        widgetType:
          enum: *ref_99
          type: string
        visualizationType:
          enum: *ref_100
          type: string
        parameters:
          type: object
        filter:
          $ref: '#/components/schemas/DashboardFilterDto'
        isCompareToPreviousPeriodEnabled:
          type: boolean
        isViewDataLabelsEnabled:
          type: boolean
        gridX:
          type: number
        gridY:
          type: number
        gridWidth:
          type: number
        gridHeight:
          type: number
        viewByPeriod:
          enum:
            - DAY
            - WEEK
            - MONTH
            - QUARTER
            - YEAR
          type: string
        isIncludeTotalEnabled:
          type: boolean
        id:
          type: string
        dateCreated:
          type: string
        lastUpdated:
          type: string
        createdBy:
          $ref: '#/components/schemas/Person'
        lastModifiedBy:
          $ref: '#/components/schemas/Person'
      required:
        - id
        - dateCreated
        - lastUpdated
        - createdBy
        - lastModifiedBy
    UpdateWidgetDto:
      type: object
      properties: {}
    WidgetDataRequestDto:
      type: object
      properties:
        widgetType:
          enum:
            - ASSETS_SCORED
            - IMPRESSION_ANALYZED
            - CHANNEL_ADHERENCE_TRENDS
            - ASSET_OVERVIEW
            - KEY_FINDINGS
            - ASSET_TRENDS
            - CRITERIA_PERFORMANCE
            - ADHERENCE_SCORE
          type: string
        visualizationType:
          enum:
            - METRIC
            - BAR
            - COLUMN
            - DONUT
            - TABLE
            - LINE
            - TEXT
          type: string
        parameters:
          type: object
        filter:
          $ref: '#/components/schemas/DashboardFilterDto'
        dashboardFilter:
          $ref: '#/components/schemas/DashboardFilterDto'
        isCompareToPreviousPeriodEnabled:
          type: boolean
        isIncludeTotalEnabled:
          type: boolean
        isViewDataLabelsEnabled:
          type: boolean
        isKpiLiftEnabled:
          type: boolean
    WidgetDataResponseDto:
      type: object
      properties:
        widgetId:
          type: string
        widgetType:
          enum:
            - ASSETS_SCORED
            - IMPRESSION_ANALYZED
            - CHANNEL_ADHERENCE_TRENDS
            - ASSET_OVERVIEW
            - KEY_FINDINGS
            - ASSET_TRENDS
            - CRITERIA_PERFORMANCE
            - ADHERENCE_SCORE
          type: string
        visualizationType:
          enum:
            - METRIC
            - BAR
            - COLUMN
            - DONUT
            - TABLE
            - LINE
            - TEXT
          type: string
        data:
          type: object
        reportPeriodLabels:
          type: array
          items:
            type: string
        previousPeriodLabels:
          type: array
          items:
            type: string
        lastRefreshed:
          type: string
          format: date-time
      required:
        - widgetId
        - widgetType
        - visualizationType
        - data
        - lastRefreshed
