NODE_ENV: sdk
AWS_REGION: us-east-1

CORS_ORIGIN: ['*']

openai:
  secret: dev/openai

google-generative-ai:
  secret: dev/google-generative-ai # not yet created in VidMobMain

vertexai:
  projectId: vidmob-vertex-ai-development
  location: us-central1
  audience: //iam.googleapis.com/projects/************/locations/global/workloadIdentityPools/aws-ec2-idp/providers/vidmobmain-dev
  subject_token_type: urn:ietf:params:aws:token-type:aws4_request
  service_account_impersonation_url: https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken

llm:
  defaultModel: OPEN_AI_4_1

bigquery:
  NORMS_READ_ONLY:
    secret: bigquery-read-only

database:
  default:
    value:
      host: mysql
      port: 3306
      database: vidmob
      username: root
      password: root
      ssl: false
