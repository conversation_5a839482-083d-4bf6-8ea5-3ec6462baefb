NODE_ENV: local
AWS_REGION: us-east-1

# Required to allow local development with the frontend
CORS_ORIGIN: ['*']

authorizationService:
  #basePath: 'http://127.0.0.1:3001'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-authorization-service'
organizationService:
  #basePath: 'http://127.0.0.1:3003'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-organization-service'
mediaAnnotationService:
  #basePath: 'http://127.0.0.1:3007'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'media-annotation-service'
dashboardService:
  # basePath: 'http://127.0.0.1:3008'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-dashboard-service'
analyticsService:
  # basePath: 'http://127.0.0.1:3002'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-analytics-service'
scoringService:
  # basePath: 'http://127.0.0.1:3004'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-scoring-service'
mediaConversionService:
  # basePath: 'http://127.0.0.1:3005'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-media-conversion-service'
notificationService:
  # basePath: 'http://127.0.0.1:3006'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-soa-notification-service'
studioService:
  # basePath: 'http://127.0.0.1:3007'  # Local
  basePath: 'https://soa-internal-dev.vidmob.com'
  defaultHeaders:
    vidmob-nestjs-service: 'vidmob-studio-service'

legacyAnalyticsServiceUrl: 'https://api-analytics-dev.vidmob.com'
legacyApiGwUrl: 'https://api-analytics-gw-dev.vidmob.com'
platformIntegrationUrl: 'https://api-public-dev.vidmob.com/integrations'
serviceUrl: 'https://api-public-dev.vidmob.com'
baseVidMobApiUrl: 'https://api-public-dev.vidmob.com/VidMob'

google-credentials:
  secret: google-slides-secret

openai:
  secret: dev/openai

google-generative-ai:
  secret: dev/google-generative-ai # not yet created in VidMobMain

vertexai:
  projectId: vidmob-vertex-ai-development
  location: us-central1
  audience: //iam.googleapis.com/projects/************/locations/global/workloadIdentityPools/aws-ec2-idp/providers/vidmobmain-dev
  subject_token_type: urn:ietf:params:aws:token-type:aws4_request
  service_account_impersonation_url: https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken

llm:
  defaultModel: OPEN_AI_4_1

MONGODB_URI: 'mongodb+srv://analytics-copilot-dev.odxug.mongodb.net/?authSource=%24external&authMechanism=MONGODB-X509&retryWrites=true&w=majority&appName=analytics-copilot-dev'
MONGODB_DB_NAME: 'analyticsCopilot'

## actual dev secret config
## to hit big query dev, uncomment the secret config and comment out dummy config
## set your AWS_PROFILE to VidmobMain-dev "VidmobSecretManager" access key to access the secret
# bigquery:
#   NORMS_READ_ONLY:
#     secret: bigquery-read-only
## dummy config
## use this dummy config when you are NOT testing big query (normative-performance)
bigquery:
  NORMS_READ_ONLY:
    value:
      type: service_account
      project_id: vidmob-big-query-dev
      private_key_id: <>
      private_key: <>
      client_email: <EMAIL>
      client_id: 109499656920345841349
      auth_uri: https://accounts.google.com/o/oauth2/auth
      token_uri: https://oauth2.googleapis.com/token
      auth_provider_x509_cert_url: https://www.googleapis.com/oauth2/v1/certs
      client_x509_cert_url: https://www.googleapis.com/robot/v1/metadata/x509/soa-query-bigquery%40vidmob-big-query-dev.iam.gserviceaccount.com
      universe_domain: googleapis.com

database:
  default:
    value:
      host: 127.0.0.1
      port: 13326
      database: vidmob
      ssl: false

pinecone:
  indexName: 'prod-tags-v0'
  embedApiUrl: 'https://api.pinecone.io/embed'
  vectorSearchIndexedOrganizations:
    - '6288ace3-af0d-4359-86a4-b3abb9726b7a' # WPP/Coke
    - '********-9d0c-4d38-a7dc-112f6404ce40' # Kellanova
    - '6e20fb02-f8b2-45d6-ae01-6dae4db84bd3' # Home Depot
    - '1472082c-dc0c-47b6-b9f7-fffab7809c98' # L'oreal
    - '0ef41cde-0090-11ee-b5b9-12d9155fe44f' # Amazon
    - '********-f257-45be-9ebb-0cb2f5e30996' # Ulta
    - '015fcd0e-3c9b-4d49-ace2-70b947bbf4f1' # Mars Petcare
    - 'ce480809-bafc-4acf-a7b9-693d08abacff' # Kenvue
    - '3b2196ac-ec10-4048-bc2c-8e0cc81d68ec' # Shiseido
    - 'a813410b-61e2-4e85-863b-0075055016d3' # Novartis
    - '040ddd80-9fe9-4037-986c-d6aa9f79239e' # Whoop
    - '8e32986c-915c-44a8-939e-eeaddaf8f06a' # Publicis/Hershey's
  defaultOrganizationId: '6288ace3-af0d-4359-86a4-b3abb9726b7a' # WPP/Coke
#  secret: pinecone-secret

# This will not be used everytime, just so if the url cannot be determined through state parameter
acs:
  sso:
    defaultUrl: https://acs-dev.vidmob.com/finalizeSsoLogin
