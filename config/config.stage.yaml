NODE_ENV: stage
AWS_REGION: us-east-1
CORS_ORIGIN:
  [
    'https://acs-stage.vidmob.com',
    'https://acs-stage-test.vidmob.com',
    'https://cs-stage.vidmob.com',
    'https://cs-stage-test.vidmob.com',
    'https://localhost:8443',
    'http://localhost:8063',
    'https://localhost:8063',
    'http://localhost:8064',
    'http://localhost:3000',
    'https://api-docs.vidmob.com',
  ]
CORS_ALLOW_HEADERS:
  [
    'Content-Type',
    'Authorization',
    'x-datadog-trace-id',
    'x-datadog-parent-id',
    'x-datadog-origin',
    'x-datadog-sampling-priority',
    'traceparent',
  ]

legacyAnalyticsServiceUrl: 'https://api-analytics-stage.vidmob.com'
legacyApiGwUrl: 'https://api-analytics-gw-stage.vidmob.com'
platformIntegrationUrl: 'https://api-public-stage.vidmob.com/integrations'
serviceUrl: 'https://api-public-stage.vidmob.com'
baseVidMobApiUrl: 'https://api-public-stage.vidmob.com/VidMob'

google-credentials:
  secret: google-slides-secret

openai:
  secret: stage/openai

google-generative-ai:
  secret: stage/google-generative-ai # not yet created in VidMobMain

vertexai:
  projectId: vidmob-vertex-ai-production
  location: us-central1
  audience: //iam.googleapis.com/projects/************/locations/global/workloadIdentityPools/aws-idp-prod/providers/vidmobmain-stage
  subject_token_type: urn:ietf:params:aws:token-type:aws4_request
  service_account_impersonation_url: https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/<EMAIL>:generateAccessToken

llm:
  defaultModel: OPEN_AI_4_1

MONGODB_URI: 'mongodb+srv://analytics-copilot-stage.g5pkw.mongodb.net/?authSource=%24external&authMechanism=MONGODB-X509&retryWrites=true&w=majority&appName=analytics-copilot-stage'
MONGODB_DB_NAME: 'analyticsCopilot'

bigquery:
  NORMS_READ_ONLY:
    secret: bigquery-read-only

database:
  default:
    secret: stage/mysql/soa-rw

pinecone:
  indexName: 'prod-tags-v0'
  embedApiUrl: 'https://api.pinecone.io/embed'
  vectorSearchIndexedOrganizations:
    - '6288ace3-af0d-4359-86a4-b3abb9726b7a' # WPP/Coke
    - '********-9d0c-4d38-a7dc-112f6404ce40' # Kellanova
    - '6e20fb02-f8b2-45d6-ae01-6dae4db84bd3' # Home Depot
    - '1472082c-dc0c-47b6-b9f7-fffab7809c98' # L’oreal
    - '0ef41cde-0090-11ee-b5b9-12d9155fe44f' # Amazon
    - '********-f257-45be-9ebb-0cb2f5e30996' # Ulta
    - '015fcd0e-3c9b-4d49-ace2-70b947bbf4f1' # Mars Petcare
    - 'ce480809-bafc-4acf-a7b9-693d08abacff' # Kenvue
    - '3b2196ac-ec10-4048-bc2c-8e0cc81d68ec' # Shiseido
    - 'a813410b-61e2-4e85-863b-0075055016d3' # Novartis
    - '040ddd80-9fe9-4037-986c-d6aa9f79239e' # Whoop
    - '8e32986c-915c-44a8-939e-eeaddaf8f06a' # Publicis/Hershey's
  defaultOrganizationId: '1472082c-dc0c-47b6-b9f7-fffab7809c98' # L’oreal
#  secret: pinecone-secret

# This will not be used everytime, just so if the url cannot be determined through state parameter
acs:
  sso:
    defaultUrl: https://acs-stage.vidmob.com/finalizeSsoLogin
