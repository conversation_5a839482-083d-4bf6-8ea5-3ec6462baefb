// eslint-disable-next-line @typescript-eslint/no-var-requires
const mysql = require('mysql2/promise');

const BATCH_SIZE = 100;

async function fetchSingleBatchOfReports(connection, offset) {
  try {
    const sql = `
    SELECT 
        report.id, 
        report_filter.filters 
    FROM report
    INNER JOIN report_filter on report.filter_id = report_filter.id
    WHERE
        report.report_type IN ('ELEMENT_IMPACT','MEDIA_IMPACT','ELEMENT_PRESENCE', 'CREATIVE_LEADERBOARD','CRITERIA_PERFORMANCE') AND
        JSON_VALID(report_filter.filters)
    LIMIT ${BATCH_SIZE}
    OFFSET ${offset}
    `;

    const [rows] = await connection.execute(sql);
    const parsedRows = rows.reduce(
      (acc, row) => {
        acc.reportAdAccountMappings.push(
          JSON.parse(row.filters).adAccountIds?.map((adAccountId) => [
            row.id,
            adAccountId,
          ]) ?? [],
        );
        acc.reportWorkspaceMappings.push(
          JSON.parse(row.filters).workspaceIds?.map((workspaceId) => [
            row.id,
            workspaceId,
          ]) ?? [],
        );

        return acc;
      },
      {
        reportAdAccountMappings: [],
        reportWorkspaceMappings: [],
      },
    );

    return {
      reportAdAccountMappings: parsedRows.reportAdAccountMappings.flat(),
      reportWorkspaceMappings: parsedRows.reportWorkspaceMappings.flat(),
    };
  } catch (error) {
    console.error('Error fetching reports:', error);
  }
}

async function insertSingleBatchReportAdAccounts(connection, valuesToInsert) {
  if (!valuesToInsert.length) {
    return;
  }

  try {
    const sql = `INSERT IGNORE INTO report_platform_account_map (report_id, platform_account_id) VALUES ?`;

    await connection.query(sql, [valuesToInsert]);
    console.log(`Done inserting ${valuesToInsert.length} report ad accounts`);
  } catch (error) {
    console.error('Error inserting report ad accounts:', error);
  }
}

async function insertSingleBatchReportWorkspaces(connection, valuesToInsert) {
  if (!valuesToInsert.length) {
    return;
  }

  try {
    const sql = `
      INSERT IGNORE INTO report_workspace_map (report_id, workspace_id)
      VALUES ?
    `;
    await connection.query(sql, [valuesToInsert]);
    console.log(`Done inserting ${valuesToInsert.length} report workspaces`);
  } catch (error) {
    console.error('Error inserting report ad accounts:', error);
  }
}

async function main() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    port: 13326,
    database: 'vidmob',
  });

  let isDoneFetching = false;
  let offset = 0;

  while (!isDoneFetching) {
    console.log(
      `Fetching batch of reports with offset ${offset} and batch size ${BATCH_SIZE}`,
    );
    const { reportAdAccountMappings, reportWorkspaceMappings } =
      await fetchSingleBatchOfReports(connection, offset);
    if (!reportAdAccountMappings.length && !reportWorkspaceMappings.length) {
      isDoneFetching = true;
      console.log('\nMIGRATION COMPLETE!');
    }
    offset += BATCH_SIZE;

    await Promise.all([
      insertSingleBatchReportAdAccounts(connection, reportAdAccountMappings),
      insertSingleBatchReportWorkspaces(connection, reportWorkspaceMappings),
    ]);
  }

  await connection.close();
}

main();
