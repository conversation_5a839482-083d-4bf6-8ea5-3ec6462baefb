apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{APP_NAME}}
  namespace: vidmob-soa
  labels:
    app: {{APP_NAME}}
spec:
  replicas: 1
  revisionHistoryLimit: 2
  selector:
    matchLabels:
      app: {{APP_NAME}}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: {{APP_NAME}}
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
                - arm64
      containers:
      - name: {{APP_NAME}}
        image: {{CONTAINER_IMAGE}}
        args: [/bin/sh, -c, 'while true; do echo $(date); sleep 1; done']
        env:
          - name: ENVIRONMENT
            value: "{{ENVIRONMENT}}"
        ports:
        - name: http
          containerPort: 3000
        resources:
          limits:
            cpu: 500m
          requests:
            cpu: 200m
        imagePullPolicy: Always
      nodeSelector:
        kubernetes.io/os: linux
---
apiVersion: v1
kind: Service
metadata:
  name: {{APP_NAME}}
  namespace: vidmob-soa
  labels:
    app: {{APP_NAME}}
spec:
  selector:
    app: {{APP_NAME}}
  ports:
    - port: 3000
      targetPort: 3000
      protocol: TCP
  type: LoadBalancer
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{APP_NAME}}
  namespace: vidmob-soa
  labels:
    app: {{APP_NAME}}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{APP_NAME}}
  minReplicas: 1
  maxReplicas: 5
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80
