#!/usr/bin/env bash

APP_NAME=$(grep '"name":' package.json | sed 's/^.*\"name\": \"\([a-zA-Z0-9_\.\-]*\)\".*$/\1/')
APP_VERSION=$(grep '"version":' package.json | sed 's/^.*\"version\": \"\([a-zA-Z0-9_\.\-]*\)\".*$/\1/')

if [[ "$CI_COMMIT_SHORT_SHA" != "" ]]; then
    BUILD_RESOURCE="$APP_NAME:$APP_VERSION-$CI_COMMIT_SHORT_SHA"
else
    BUILD_RESOURCE="$APP_NAME:$APP_VERSION"
fi

echo "Building Application for '$BUILD_RESOURCE'"

docker build --tag=vidmob/$BUILD_RESOURCE --build-arg "APP_VERSION=$APP_VERSION" . || exit $?

if [[ "$CI_PIPELINE_ID" != "" ]]; then
    docker tag vidmob/$BUILD_RESOURCE 064908284565.dkr.ecr.us-east-1.amazonaws.com/vidmob/$BUILD_RESOURCE || exit $?

    # https://docs.aws.amazon.com/AmazonECR/latest/userguide/registry_auth.html
    if [ "$(aws --version | cut -d'/' -f2 | cut -d' ' -f1 | cut -d'.' -f1)" == "2" ]; then
        echo "AWS ECR Login using AWS CLI v2"
        aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 064908284565.dkr.ecr.us-east-1.amazonaws.com || exit $?
    else
        echo "AWS ECR Login using AWS CLI v1"
        $(aws ecr get-login --region us-east-1 --no-include-email) || exit $?
    fi

    docker push 064908284565.dkr.ecr.us-east-1.amazonaws.com/vidmob/$BUILD_RESOURCE || exit $?
fi

echo "Successfully packaged - '$BUILD_RESOURCE'"
