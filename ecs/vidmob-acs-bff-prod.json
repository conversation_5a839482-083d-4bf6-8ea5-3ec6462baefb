{"containerDefinitions": [{"name": "datadog-agent", "image": "public.ecr.aws/datadog/agent:latest", "cpu": 0, "portMappings": [], "essential": true, "environment": [{"name": "ECS_FARGATE", "value": "true"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DD_API_KEY", "valueFrom": "arn:aws:secretsmanager:us-east-1:560638139269:secret:datadog-api-key-8PvW-BaDCRW"}], "startTimeout": 30, "stopTimeout": 120, "privileged": false, "readonlyRootFilesystem": false, "interactive": false, "pseudoTerminal": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/vidmob-acs-bff/datadog-agent", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}}, {"name": "fluent-bit", "image": "906394416424.dkr.ecr.us-east-1.amazonaws.com/aws-for-fluent-bit:stable", "cpu": 0, "portMappings": [], "essential": true, "environment": [], "mountPoints": [], "volumesFrom": [], "startTimeout": 30, "stopTimeout": 120, "user": "0", "privileged": false, "readonlyRootFilesystem": true, "interactive": false, "pseudoTerminal": false, "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/aws/ecs/vidmob-acs-bff/fluent-bit", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "firelensConfiguration": {"type": "fluentbit", "options": {"config-file-type": "file", "config-file-value": "/fluent-bit/configs/parse-json.conf", "enable-ecs-log-metadata": "true"}}}, {"name": "vidmob-acs-bff", "image": "451690304815.dkr.ecr.us-east-1.amazonaws.com/vidmob/vidmob-acs-bff:${version}", "cpu": 3072, "memory": 7168, "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "DD_ENV", "value": "prod"}, {"name": "DD_SERVICE_NAME", "value": "vidmob-acs-bff"}, {"name": "DD_VERSION", "value": "${version}"}, {"name": "DD_RUNTIME_METRICS_ENABLED", "value": "true"}, {"name": "ENVIRONMENT", "value": "prod"}, {"name": "NODE_OPTIONS", "value": "--max-old-space-size=7168"}], "secrets": [{"name": "MONGODB_X509_CERT_BASE64", "valueFrom": "arn:aws:secretsmanager:us-east-1:560638139269:secret:mongodbCert-f18MCZ"}], "mountPoints": [], "volumesFrom": [], "startTimeout": 30, "stopTimeout": 120, "privileged": false, "readonlyRootFilesystem": false, "interactive": false, "pseudoTerminal": false, "dockerLabels": {"com.datadoghq.tags.env": "prod", "com.datadoghq.tags.service": "vidmob-acs-bff", "com.datadoghq.tags.version": "${version}"}, "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Host": "http-intake.logs.datadoghq.com", "Name": "datadog", "TLS": "on", "dd_message_key": "msg", "dd_service": "vidmob-acs-bff", "dd_source": "<PERSON><PERSON><PERSON>", "dd_tags": "project:fluentbit", "provider": "ecs"}, "secretOptions": [{"name": "apikey", "valueFrom": "arn:aws:secretsmanager:us-east-1:560638139269:secret:datadog-api-key-8PvW-BaDCRW"}]}}], "family": "vidmob-acs-bff", "taskRoleArn": "arn:aws:iam::560638139269:role/vidmob-acs-bff-task-role-us-east-1", "executionRoleArn": "arn:aws:iam::560638139269:role/vidmob-acs-bff-exec-role-us-east-1", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "requiresCompatibilities": ["FARGATE"], "cpu": "4096", "memory": "8192"}